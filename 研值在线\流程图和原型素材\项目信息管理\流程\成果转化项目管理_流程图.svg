<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果转化项目管理业务流程</text>

  <!-- 阶段一：用户入口与数据检索 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户入口与数据检索</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(新增、查询、导入)</text>
  </g>

  <!-- 节点2: 系统加载检索区 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统加载检索区</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(筛选条件设定)</text>
  </g>

  <!-- 节点3: 项目列表加载 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目列表加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(匹配项目展示)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：项目操作与管理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：项目操作与管理</text>

  <!-- 节点4: 新增项目 -->
  <g transform="translate(50, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">基础信息录入</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">资金明细填写</text>
  </g>

  <!-- 节点5: 项目列表操作 -->
  <g transform="translate(300, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目列表操作</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">查看、编辑、删除</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">批量操作</text>
  </g>

  <!-- 节点6: 详情查看 -->
  <g transform="translate(550, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">全量信息展示</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">历史变更记录</text>
  </g>

  <!-- 节点7: 批量导入 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">模板下载上传</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">数据校验入库</text>
  </g>

  <!-- 从项目列表到各操作的连接线 -->
  <path d="M 750 200 C 700 250, 200 280, 140 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 200 C 750 250, 450 280, 390 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 200 C 800 250, 700 280, 640 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 820 200 C 850 250, 900 280, 890 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据处理与校验 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与校验</text>

  <!-- 节点8: 数据校验入库 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验入库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(自动校验与存储)</text>
  </g>

  <!-- 节点9: 历史日志记录 -->
  <g transform="translate(500, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">历史日志记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(操作追踪归档)</text>
  </g>

  <!-- 节点10: 异常处理 -->
  <g transform="translate(800, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">异常处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(异常报告导出)</text>
  </g>

  <!-- 从操作节点到处理节点的连接线 -->
  <path d="M 140 400 C 140 450, 250 480, 300 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 390 400 C 390 450, 550 480, 600 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 400 C 890 450, 850 480, 900 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据集成与应用 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据集成与应用</text>
  
  <!-- 节点11: 数据归集统计 -->
  <g transform="translate(300, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归集统计</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">行业统计、资金效能分析</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">政策评估、模块互联</text>
  </g>

  <!-- 节点12: 业务模块互通 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">业务模块互通</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">成果管理、统计分析</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">标准数据支撑</text>
  </g>

  <!-- 从处理节点到最终应用的连接线 -->
  <path d="M 300 590 C 300 650, 400 680, 425 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 590 C 600 650, 700 680, 775 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据归集到业务互通的连接线 -->
  <path d="M 550 760 Q 600 760 650 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从最终应用回到检索区 -->
  <path d="M 775 720 C 1100 650, 1100 200, 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 1050, 400)">定期数据更新</text>

</svg>