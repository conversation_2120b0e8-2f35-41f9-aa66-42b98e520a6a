<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员人才称号管理业务流程</text>

  <!-- 阶段一：数据初始化与加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与加载</text>
  
  <!-- 节点1: 系统初始化 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统初始化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载全部人才称号信息</text>
  </g>

  <!-- 阶段二：用户交互与筛选 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与筛选</text>

  <!-- 节点2: 条件筛选 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选条件，实时刷新</text>
  </g>

  <!-- 节点3: 称号列表展示 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">称号列表展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">符合条件的称号信息</text>
  </g>

  <!-- 节点4: 详情查看 -->
  <g transform="translate(1000, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">完整称号信息与历史</text>
  </g>

  <!-- 阶段三：称号操作与管理 -->
  <text x="700" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：称号操作与管理</text>

  <!-- 节点5: 新增称号 -->
  <g transform="translate(100, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增称号</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写称号信息并入库</text>
  </g>

  <!-- 节点6: 编辑称号 -->
  <g transform="translate(350, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑称号</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调整称号属性并保存</text>
  </g>

  <!-- 节点7: 删除称号 -->
  <g transform="translate(600, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除称号</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">逻辑删除无效称号</text>
  </g>

  <!-- 节点8: 数据导出 -->
  <g transform="translate(850, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">导出检索结果数据</text>
  </g>

  <!-- 阶段四：数据管理与监控 -->
  <text x="700" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据管理与监控</text>

  <!-- 节点9: 数据校验 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">系统校验与质量保障</text>
  </g>

  <!-- 节点10: 操作日志 -->
  <g transform="translate(800, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录所有操作行为</text>
  </g>

  <!-- 连接线：系统初始化 -> 条件筛选 -->
  <path d="M 650 200 C 500 240, 400 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：条件筛选 -> 称号列表展示 -->
  <path d="M 400 355 C 450 355, 550 355, 600 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：称号列表展示 -> 详情查看 -->
  <path d="M 800 355 C 850 355, 950 355, 1000 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：称号列表展示 -> 各种操作 -->
  <path d="M 650 390 C 400 450, 300 480, 200 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 680 390 C 500 450, 450 480, 450 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 390 C 700 450, 700 480, 700 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 390 C 850 450, 900 480, 950 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：操作 -> 数据校验 -->
  <path d="M 200 590 C 250 650, 350 680, 450 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 590 C 450 650, 450 680, 500 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 590 C 650 650, 600 680, 550 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：所有操作 -> 操作日志 -->
  <path d="M 200 590 C 400 650, 600 680, 800 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 590 C 600 650, 700 680, 850 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 590 C 750 650, 800 680, 850 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 590 C 950 650, 900 680, 900 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1100 390 C 1100 600, 950 650, 900 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：操作日志 -> 系统初始化 -->
  <path d="M 800 720 C 100 650, 50 400, 50 165 C 50 165, 400 165, 600 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="400" font-size="11" fill="#666" transform="rotate(-90, 50, 400)">操作追溯</text>

  <!-- 反馈循环：数据导出 -> 条件筛选 -->
  <path d="M 950 520 C 1200 450, 1250 355, 1250 355 C 1250 355, 600 300, 300 300" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="350" font-size="11" fill="#666">重新筛选</text>

</svg>