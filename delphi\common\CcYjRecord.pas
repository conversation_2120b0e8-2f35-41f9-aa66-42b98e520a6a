unit CcYjRecord;

interface
uses
  Classes;

type
  TCcYjRecord = class
  private
    FYjrecordid: integer;
    FLrsj: string;
    FDdh: string;
    FDds: integer;
    FKsbm: string;
    FYjxm: string;
    FYjz: double;
    FSjz: double;
    FYjType: string;
    FLrr: string;
    FZrr1: string;
    FZrr2: string;
    FBz: string;
    FDdid: integer;
    FYsid: integer;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Yjrecordid: integer read FYjrecordid write FYjrecordid;
    property Lrsj: string read FLrsj write FLrsj;
    property Ddh: string read FDdh write FDdh;
    property Dds: integer read FDds write FDds;
    property Ksbm: string read FKsbm write FKsbm;
    property Yjxm: string read FYjxm write FYjxm;
    property Yjz: double read FYjz write FYjz;
    property Sjz: double read FSjz write FSjz;
    property YjType: string read FYjType write FYjType;
    property Lrr: string read FLrr write FLrr;
    property Zrr1: string read FZrr1 write FZrr1;
    property Zrr2: string read FZrr2 write FZrr2;
    property Bz: string read FBz write FBz;
    property Ddid: integer read FDdid write FDdid;
    property Ysid: integer read FYsid write FYsid;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.

