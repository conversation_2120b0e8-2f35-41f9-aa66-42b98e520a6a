<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研项目展示系统流程图</text>

  <!-- 阶段一：页面初始化和数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化和数据加载</text>
  
  <!-- 节点1: 用户进入页面 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">科研项目展示页面</text>
  </g>

  <!-- 节点2: 数据聚合服务 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据聚合服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载枚举和统计数据</text>
  </g>

  <!-- 节点3: 默认界面生成 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">默认界面生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">概览卡片、列表、图表</text>
  </g>

  <!-- 节点4: 项目类型枚举 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目类型枚举</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">技术领域枚举</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 3 -> 4 -->
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询和数据同步 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询和数据同步</text>

  <!-- 节点5: 用户筛选操作 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户筛选操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调整条件、输入关键字</text>
  </g>

  <!-- 节点6: 查询参数重组 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询参数重组</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">请求数据服务</text>
  </g>

  <!-- 节点7: 实时数据刷新 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时数据刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">多区块数据同步</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 5 (反馈循环) -->
  <path d="M 750 200 C 750 250, 350 250, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="525" y="240" text-anchor="middle" font-size="12" fill="#555">用户交互触发</text>

  <!-- 阶段三：详情查看和交互 -->
  <text x="700" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看和交互</text>
  
  <!-- 节点8: 查看详情操作 -->
  <g transform="translate(150, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看详情操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击列表项目</text>
  </g>

  <!-- 节点9: 项目信息检索 -->
  <g transform="translate(450, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目信息检索</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">写入会话缓存</text>
  </g>

  <!-- 节点10: 详情抽屉渲染 -->
  <g transform="translate(750, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情抽屉渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录浏览日志</text>
  </g>

  <!-- 节点11: 操作执行 -->
  <g transform="translate(1050, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作执行</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">跳转或下载PDF</text>
  </g>

  <!-- 连接线 8 -> 9 -->
  <path d="M 350 545 Q 400 545 450 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 650 545 Q 700 545 750 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 950 545 Q 1000 545 1050 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 7 -> 8 (用户点击) -->
  <path d="M 850 390 C 850 440, 300 440, 250 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="575" y="430" text-anchor="middle" font-size="12" fill="#555">用户点击详情</text>

  <!-- 阶段四：会话管理和日志记录 -->
  <text x="700" y="660" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：会话管理和日志记录</text>
  
  <!-- 节点12: 状态保留和缓存管理 -->
  <g transform="translate(300, 700)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">状态保留和缓存管理</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">筛选状态保留</tspan>
        <tspan dx="60">会话缓存销毁</tspan>
        <tspan dx="60">操作日志记录</tspan>
      </text>
  </g>

  <!-- 节点13: 审计追踪 -->
  <g transform="translate(800, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="35" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计追踪</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">操作统计更新</text>
  </g>

  <!-- 连接线 11 -> 13 -->
  <path d="M 1100 580 C 1100 630, 950 650, 900 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 10 -> 12 (关闭抽屉) -->
  <path d="M 800 580 C 800 630, 600 650, 550 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="675" y="620" text-anchor="middle" font-size="12" fill="#555">关闭抽屉/退出</text>

</svg>