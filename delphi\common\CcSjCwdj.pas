unit CcSjCwdj;

interface

uses
  Classes;

type
  TCcSjCwdj = class
  private

    FSjcwdjid: Integer;
    FDdid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FWczt: string;

    FSccj: string;
    FGjCj: Double;
    FGjXs: Double;
    FGj: Double;
    FBcl: Double;
    FScs: Double;
    FGjzj: Double;
    FCwfy: Double;
    FZcb: Double;
    FLrl: Double;
    FDjlr: Double;
    FPjlr: Double;
    FBz: string;
    FChje: Double;
    FDds: Integer;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjcwdjid: Integer read FSjcwdjid write FSjcwdjid;
    property Ddid: Integer read FDdid write FDdid;
    property Ddh: string read FDdh write FDdh;
    property Wczt: string read FWczt write FWczt;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Sccj: string read FSccj write FSccj;
    property GjCj: Double read FGjCj write FGjCj;
    property GjXs: Double read FGjXs write FGjXs;
    property Gj: Double read FGj write FGj;
    property Bcl: Double read FBcl write FBcl;
    property Scs: Double read FScs write FScs;
    property Gjzj: Double read FGjzj write FGjzj;
    property Cwfy: Double read FCwfy write FCwfy;
    property Zcb: Double read FZcb write FZcb;
    property Lrl: Double read FLrl write FLrl;
    property Djlr: Double read FDjlr write FDjlr;
    property Pjlr: Double read FPjlr write FPjlr;
    property Bz: string read FBz write FBz;
    property Chje: Double read FChje write FChje;
    property Dds: Integer read FDds write FDds;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
