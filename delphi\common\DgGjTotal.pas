unit DgGjTotal;

interface

uses
  Classes;

type
  TDgGjTotal = class
  private
    FDdh: string;
    FKh: string;
    FKsbm: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FCheckState: string;
    FDds: integer;
    FScs: integer;
    FODP_Date: string;
  public
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Ksbm: string read FKsbm write FKsbm;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Dds: integer read FDds write FDds;
    property Scs: integer read FScs write FScs;
    property CheckState: string read FCheckState write FCheckState;
    property ODP_Date: string read FODP_Date write FODP_Date;
  end;

implementation

end.
