<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策分类管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
    <style>
        .tree-indent-1 {
            padding-left: 1.5rem;
        }
        .tree-indent-2 {
            padding-left: 3rem;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">政策分类管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-6 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 flex flex-col space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="categoryName" class="block text-sm font-medium text-gray-700 mb-1">分类名称</label>
                            <input type="text" id="categoryName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入分类名称">
                        </div>
                        <div>
                            <label for="parentCategory" class="block text-sm font-medium text-gray-700 mb-1">父级分类</label>
                            <select id="parentCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="economic">经济政策</option>
                                <option value="social">社会政策</option>
                                <option value="environmental">环境政策</option>
                                <option value="education">教育政策</option>
                            </select>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <select id="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="enabled">启用</option>
                                <option value="disabled">停用</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                        <div>
                            <label for="createDate" class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                            <div class="flex space-x-2">
                                <input type="date" id="startDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500">至</span>
                                <input type="date" id="endDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>

                <!-- 分类列表区 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-800">分类列表</h2>
                            <button onclick="openAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                新增分类
                            </button>
                        </div>
                    </div>
                    <div class="flex-1 overflow-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类编码</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属父级</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">层级深度</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 一级分类 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EC001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <button class="mr-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                            <span class="text-sm font-medium text-gray-900">经济政策</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">启用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="openAddSubModal('EC001')" class="text-blue-600 hover:text-blue-900">新增子类</button>
                                        <button onclick="openEditModal('EC001')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-orange-600 hover:text-orange-900">停用</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- 二级分类 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EC001001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center tree-indent-1">
                                            <button class="mr-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </button>
                                            <span class="text-sm text-gray-900">财政政策</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">经济政策</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">启用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14 14:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="openAddSubModal('EC001001')" class="text-blue-600 hover:text-blue-900">新增子类</button>
                                        <button onclick="openEditModal('EC001001')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-orange-600 hover:text-orange-900">停用</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <!-- 三级分类 -->
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EC001001001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center tree-indent-2">
                                            <span class="text-sm text-gray-900">税收政策</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">财政政策</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">启用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-13 09:15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="openAddSubModal('EC001001001')" class="text-blue-600 hover:text-blue-900">新增子类</button>
                                        <button onclick="openEditModal('EC001001001')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-orange-600 hover:text-orange-900">停用</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">EC001002</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center tree-indent-1">
                                            <span class="text-sm text-gray-900">货币政策</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">经济政策</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">停用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 16:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="openAddSubModal('EC001002')" class="text-blue-600 hover:text-blue-900">新增子类</button>
                                        <button onclick="openEditModal('EC001002')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-green-600 hover:text-green-900">启用</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">SC001</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <button class="mr-2 text-gray-400 hover:text-gray-600">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                            </button>
                                            <span class="text-sm font-medium text-gray-900">社会政策</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">启用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10 11:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button onclick="openAddSubModal('SC001')" class="text-blue-600 hover:text-blue-900">新增子类</button>
                                        <button onclick="openEditModal('SC001')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-orange-600 hover:text-orange-900">停用</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="lg:w-80 space-y-6">
                <!-- 统计与预警区 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">统计与预警</h3>
                    
                    <!-- 分类统计 -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">各层级分类数量</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">一级分类</span>
                                <span class="text-sm font-medium text-blue-600">8</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">二级分类</span>
                                <span class="text-sm font-medium text-blue-600">24</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">三级分类</span>
                                <span class="text-sm font-medium text-blue-600">67</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">四级分类</span>
                                <span class="text-sm font-medium text-blue-600">156</span>
                            </div>
                        </div>
                    </div>

                    <!-- 变更趋势 -->
                    <div class="mb-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">近30天分类变更</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">新增分类</span>
                                <span class="text-sm font-medium text-green-600">12</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">修改分类</span>
                                <span class="text-sm font-medium text-yellow-600">8</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">停用分类</span>
                                <span class="text-sm font-medium text-red-600">3</span>
                            </div>
                        </div>
                    </div>

                    <!-- 预警信息 -->
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">预警信息</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-yellow-800">已停用但被引用</span>
                                </div>
                                <p class="text-xs text-yellow-700 mt-1">5个分类已停用但仍被政策引用</p>
                                <button class="text-xs text-yellow-800 underline mt-1">查看详情</button>
                            </div>
                            <div class="p-3 bg-red-50 border border-red-200 rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-red-800">编码重复风险</span>
                                </div>
                                <p class="text-xs text-red-700 mt-1">检测到2个潜在编码冲突</p>
                                <button class="text-xs text-red-800 underline mt-1">立即处理</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <button onclick="toggleImportPanel()" class="w-full flex justify-between items-center text-left">
                            <h3 class="text-lg font-medium text-gray-800">批量导入</h3>
                            <svg id="import-toggle-icon" class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="import-content" class="p-4 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">下载标准模板</label>
                            <button class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                下载Excel模板
                            </button>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">文件上传</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-md p-4 text-center hover:border-blue-400 transition-colors">
                                <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p class="text-sm text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                                <p class="text-xs text-gray-500">支持 .xlsx, .xls 格式</p>
                                <input type="file" id="file-upload" class="hidden" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div id="upload-progress" class="hidden">
                            <label class="block text-sm font-medium text-gray-700 mb-2">上传进度</label>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">正在上传... <span>0%</span></p>
                        </div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">编辑分类</h3>
                <button onclick="closeEditModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="px-6 py-4 space-y-4">
                <div>
                    <label for="editParentCategory" class="block text-sm font-medium text-gray-700 mb-1">父级分类</label>
                    <select id="editParentCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">无（顶级分类）</option>
                        <option value="economic">经济政策</option>
                        <option value="social">社会政策</option>
                        <option value="technology">科技政策</option>
                    </select>
                </div>
                <div>
                    <label for="editCategoryName" class="block text-sm font-medium text-gray-700 mb-1">分类名称</label>
                    <input type="text" id="editCategoryName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入分类名称">
                </div>
                <div>
                    <label for="editCategoryCode" class="block text-sm font-medium text-gray-700 mb-1">分类编码</label>
                    <input type="text" id="editCategoryCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="系统自动生成或手动输入">
                    <p class="text-xs text-gray-500 mt-1">编码将自动校验唯一性</p>
                </div>
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" id="editCategoryStatus" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">启用状态</span>
                    </label>
                </div>
                <div>
                    <label for="editCategoryDesc" class="block text-sm font-medium text-gray-700 mb-1">分类描述</label>
                    <textarea id="editCategoryDesc" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入分类描述（可选）"></textarea>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button onclick="closeEditModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    取消
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    保存
                </button>
            </div>
        </div>
    </div>

    <script>
        // 打开编辑弹窗
        function openEditModal(categoryId) {
            document.getElementById('editModal').classList.remove('hidden');
            document.getElementById('editModal').classList.add('flex');
        }

        // 打开新增弹窗
        function openAddModal() {
            document.getElementById('editModal').classList.remove('hidden');
            document.getElementById('editModal').classList.add('flex');
        }

        // 打开新增子类弹窗
        function openAddSubModal(parentId) {
            document.getElementById('editModal').classList.remove('hidden');
            document.getElementById('editModal').classList.add('flex');
        }

        // 关闭编辑弹窗
        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
            document.getElementById('editModal').classList.remove('flex');
        }

        // 切换导入面板
        function toggleImportPanel() {
            const content = document.getElementById('import-content');
            const icon = document.getElementById('import-toggle-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(-90deg)';
            }
        }

        // 点击弹窗外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('upload-progress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('upload-progress').classList.add('hidden');
                            alert('文件上传成功！');
                        }, 500);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>