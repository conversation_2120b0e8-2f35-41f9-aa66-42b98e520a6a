unit ErrorInfoNoticeP2Frm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, RzButton, ExtCtrls, RzPanel, StdCtrls, RzLabel, pngimage,
  PngFunctions, HtImage, AdvGlowButton, frxClass;

type
  TErrorInfoNoticeP2Form = class(TForm)
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    RzPanel3: TRzPanel;
    Image1: TImage;
    L_Info1: TRzLabel;
    L_Info3: TRzLabel;
    Btn_Save: TAdvGlowButton;
    L_Info4: TRzLabel;
    L_Info5: TRzLabel;
    procedure FormShow(Sender: TObject);
    procedure Btn_SaveClick(Sender: TObject);
    procedure HTImage2Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure Init(infoTitle, infoErrorDes, infoDetail, infoDetail1,
      infoDetail2: string);
  end;

var
  ErrorInfoNoticeP2Form: TErrorInfoNoticeP2Form;
  FInfoTitle, FInfoErrorDes, FInfoDetail, FInfoDetail1, FInfoDetail2: string;

implementation

{$R *.dfm}

procedure TErrorInfoNoticeP2Form.Init(infoTitle, infoErrorDes, infoDetail,
  infoDetail1, infoDetail2: string);
begin
  FInfoTitle := infoTitle;
  FInfoErrorDes := infoErrorDes;
  FInfoDetail := infoDetail;
  FInfoDetail1 := infoDetail1;
  FInfoDetail2 := infoDetail2;
end;

procedure TErrorInfoNoticeP2Form.Btn_SaveClick(Sender: TObject);
begin
  self.Close;
end;

procedure TErrorInfoNoticeP2Form.FormShow(Sender: TObject);
begin
  L_Info1.Caption := FInfoTitle;
  L_Info3.Caption := FInfoDetail;
  L_Info4.Caption := FInfoDetail1;
  L_Info5.Caption := FInfoDetail2;
end;

procedure TErrorInfoNoticeP2Form.HTImage2Click(Sender: TObject);
begin
  self.Close;
end;

end.
