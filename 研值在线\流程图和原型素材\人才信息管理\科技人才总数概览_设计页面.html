<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技人才总数概览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        neon: {
                            blue: '#38bdf8',
                            purple: '#818cf8',
                            green: '#22c55e',
                            pink: '#ec4899',
                            yellow: '#eab308'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-slate-900 text-gray-100 min-h-screen overflow-x-hidden">
    <!-- 页面头部 -->
    <header class="bg-slate-800/70 backdrop-blur-sm border-b border-slate-700 sticky top-0 z-30">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
                <svg class="w-8 h-8 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"></path>
                </svg>
                <h1 class="text-xl font-bold">宁波市科技人才总数概览</h1>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <input type="text" placeholder="全局搜索..." class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-neon-blue/50 w-48 transition-all duration-300">
                    <svg class="absolute right-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-neon-purple rounded-full flex items-center justify-center">
                        <span class="text-xs font-bold">管</span>
                    </div>
                    <span class="text-sm hidden md:inline">管理员</span>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区域 -->
    <main class="container mx-auto px-4 py-6">
        <!-- 数据概览卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- 研发人员卡片 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5 hover:border-neon-blue/50 transition-all duration-300 cursor-pointer group" onclick="openModal('talentListModal')">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-gray-400 text-sm">研发人员总数</h3>
                        <p class="text-3xl font-bold mt-1 group-hover:text-neon-blue transition-colors duration-300">28,764</p>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-neon-blue/10 flex items-center justify-center text-neon-blue">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-neon-green flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        8.2%
                    </span>
                    <span class="text-gray-500 ml-2">较去年同期</span>
                </div>
                <div class="mt-4 h-10">
                    <canvas id="researcherChart"></canvas>
                </div>
            </div>

            <!-- 科研管理人员卡片 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5 hover:border-neon-purple/50 transition-all duration-300 cursor-pointer group" onclick="openModal('talentListModal')">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-gray-400 text-sm">科研管理人员</h3>
                        <p class="text-3xl font-bold mt-1 group-hover:text-neon-purple transition-colors duration-300">4,215</p>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-neon-purple/10 flex items-center justify-center text-neon-purple">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-neon-green flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        3.5%
                    </span>
                    <span class="text-gray-500 ml-2">较去年同期</span>
                </div>
                <div class="mt-4 h-10">
                    <canvas id="managerChart"></canvas>
                </div>
            </div>

            <!-- 专家卡片 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5 hover:border-neon-yellow/50 transition-all duration-300 cursor-pointer group" onclick="openModal('talentListModal')">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-gray-400 text-sm">专家人才</h3>
                        <p class="text-3xl font-bold mt-1 group-hover:text-neon-yellow transition-colors duration-300">1,842</p>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-neon-yellow/10 flex items-center justify-center text-neon-yellow">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11v6m0 0l-2-2m2 2l2-2"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-neon-green flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        12.3%
                    </span>
                    <span class="text-gray-500 ml-2">较去年同期</span>
                </div>
                <div class="mt-4 h-10">
                    <canvas id="expertChart"></canvas>
                </div>
            </div>

            <!-- 科技特派员卡片 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5 hover:border-neon-pink/50 transition-all duration-300 cursor-pointer group" onclick="openModal('talentListModal')">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h3 class="text-gray-400 text-sm">科技特派员</h3>
                        <p class="text-3xl font-bold mt-1 group-hover:text-neon-pink transition-colors duration-300">638</p>
                    </div>
                    <div class="w-10 h-10 rounded-full bg-neon-pink/10 flex items-center justify-center text-neon-pink">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-1.414-.586m0 0L11 14h4a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2v4l.586-.586z"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex items-center text-sm">
                    <span class="text-neon-red flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                        1.2%
                    </span>
                    <span class="text-gray-500 ml-2">较去年同期</span>
                </div>
                <div class="mt-4 h-10">
                    <canvas id="commissionerChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 类型切换与筛选区 -->
        <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5 mb-6">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex items-center space-x-2">
                    <span class="text-gray-400 text-sm">人才类型:</span>
                    <button class="px-4 py-2 bg-neon-blue text-slate-900 rounded-lg text-sm font-medium hover:bg-neon-blue/80 transition-colors duration-300">全部人才</button>
                    <button class="px-4 py-2 bg-slate-700/50 rounded-lg text-sm hover:bg-slate-700 transition-colors duration-300">研发人员</button>
                    <button class="px-4 py-2 bg-slate-700/50 rounded-lg text-sm hover:bg-slate-700 transition-colors duration-300">科研管理</button>
                    <button class="px-4 py-2 bg-slate-700/50 rounded-lg text-sm hover:bg-slate-700 transition-colors duration-300">专家</button>
                    <button class="px-4 py-2 bg-slate-700/50 rounded-lg text-sm hover:bg-slate-700 transition-colors duration-300">特派员</button>
                </div>
                
                <div class="flex flex-wrap items-center gap-3">
                    <div class="relative">
                        <select class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-neon-blue/50">
                            <option>全部地域</option>
                            <option>海曙区</option>
                            <option>江北区</option>
                            <option>鄞州区</option>
                            <option>镇海区</option>
                            <option>北仑区</option>
                            <option>奉化区</option>
                            <option>余姚市</option>
                            <option>慈溪市</option>
                            <option>宁海县</option>
                            <option>象山县</option>
                        </select>
                        <div class="absolute right-3 top-2.5 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-neon-blue/50">
                            <option>全部行业</option>
                            <option>信息技术</option>
                            <option>生物医药</option>
                            <option>高端装备</option>
                            <option>新材料</option>
                            <option>新能源</option>
                            <option>节能环保</option>
                            <option>海洋经济</option>
                            <option>其他</option>
                        </select>
                        <div class="absolute right-3 top-2.5 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-neon-blue/50">
                            <option>全部层次</option>
                            <option>顶尖人才</option>
                            <option>领军人才</option>
                            <option>青年拔尖</option>
                            <option>骨干人才</option>
                            <option>基础人才</option>
                        </select>
                        <div class="absolute right-3 top-2.5 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <button class="px-4 py-2 bg-neon-blue text-slate-900 rounded-lg text-sm font-medium hover:bg-neon-blue/80 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 详细展示区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- 区域分布热力图 -->
            <div class="lg:col-span-2 bg-slate-800/50 border border-slate-700 rounded-lg p-5">
                <div class="flex justify-between items-center mb-5">
                    <h2 class="text-lg font-semibold">人才区域分布热力图</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-neon-blue/20 text-neon-blue rounded text-xs hover:bg-neon-blue/30 transition-colors">全市</button>
                        <button class="px-3 py-1 bg-slate-700/50 rounded text-xs hover:bg-slate-700 transition-colors">区县</button>
                    </div>
                </div>
                <div class="relative h-[400px] bg-slate-900 rounded-lg overflow-hidden">
                    <!-- 简化地图背景 -->
                    <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjQwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMzAwIDUwQzE1MCA1MCA1MCAxNTAgNTAgMzAwQzUwIDQ1MCAxNTAgNTUwIDMwMCA1NTBDNDUwIDU1MCA1NTA0NTAgNDUwIDU1MCAzMDBDMjUwIDMwMCAyMDAgMzAwIDE1MCAzMDAiIGZpbGw9IiMxZTFmMmYiLz48L3N2Zz4=')] bg-contain bg-center bg-no-repeat opacity-30"></div>
                    
                    <!-- 热力图数据点 -->
                    <div class="absolute top-[30%] left-[20%] w-8 h-8 bg-neon-blue rounded-full animate-ping opacity-75"></div>
                    <div class="absolute top-[30%] left-[20%] w-6 h-6 bg-neon-blue rounded-full flex items-center justify-center cursor-pointer" onclick="updateTalentList('haishu')">
                        <span class="text-xs font-bold">海曙</span>
                    </div>
                    
                    <div class="absolute top-[40%] left-[40%] w-12 h-12 bg-neon-purple rounded-full animate-ping opacity-75"></div>
                    <div class="absolute top-[40%] left-[40%] w-10 h-10 bg-neon-purple rounded-full flex items-center justify-center cursor-pointer" onclick="updateTalentList('yinzhou')">
                        <span class="text-xs font-bold">鄞州</span>
                    </div>
                    
                    <div class="absolute top-[50%] left-[60%] w-10 h-10 bg-neon-green rounded-full animate-ping opacity-75"></div>
                    <div class="absolute top-[50%] left-[60%] w-8 h-8 bg-neon-green rounded-full flex items-center justify-center cursor-pointer" onclick="updateTalentList('beilun')">
                        <span class="text-xs font-bold">北仑</span>
                    </div>
                    
                    <div class="absolute top-[45%] left-[15%] w-6 h-6 bg-neon-yellow rounded-full animate-ping opacity-75"></div>
                    <div class="absolute top-[45%] left-[15%] w-4 h-4 bg-neon-yellow rounded-full cursor-pointer" onclick="updateTalentList('jiangbei')"></div>
                    
                    <div class="absolute top-[55%] left-[30%] w-6 h-6 bg-neon-pink rounded-full animate-ping opacity-75"></div>
                    <div class="absolute top-[55%] left-[30%] w-4 h-4 bg-neon-pink rounded-full cursor-pointer" onclick="updateTalentList('zhenhai')"></div>
                    
                    <!-- 图例 -->
                    <div class="absolute bottom-4 right-4 bg-slate-800/80 backdrop-blur-sm p-3 rounded-lg border border-slate-700">
                        <h4 class="text-xs font-medium text-gray-300 mb-2">人才密度</h4>
                        <div class="space-y-2 text-xs">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-neon-blue rounded-full mr-2"></div>
                                <span>5,000+</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-neon-purple rounded-full mr-2"></div>
                                <span>3,000-5,000</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-neon-green rounded-full mr-2"></div>
                                <span>1,000-3,000</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-neon-yellow rounded-full mr-2"></div>
                                <span>500-1,000</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-neon-pink rounded-full mr-2"></div>
                                <span><500</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行业分布柱状图 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5">
                <div class="flex justify-between items-center mb-5">
                    <h2 class="text-lg font-semibold">人才行业分布</h2>
                    <button class="text-gray-400 hover:text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v6m0 0l-4 4m4-4l4 4"></path>
                        </svg>
                    </button>
                </div>
                <div class="h-[400px]">
                    <canvas id="industryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 人才结构分析区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- 年龄分布 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5">
                <h2 class="text-lg font-semibold mb-5">人才年龄分布</h2>
                <div class="h-[300px]">
                    <canvas id="ageChart"></canvas>
                </div>
            </div>

            <!-- 引进方式 -->
            <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5">
                <h2 class="text-lg font-semibold mb-5">人才引进方式</h2>
                <div class="h-[300px]">
                    <canvas id="introductionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 人才清册区 -->
        <div class="bg-slate-800/50 border border-slate-700 rounded-lg p-5">
            <div class="flex justify-between items-center mb-5">
                <h2 class="text-lg font-semibold">人才清册列表</h2>
                <div class="flex space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="搜索人才..." class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-neon-blue/50 w-64">
                        <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <button class="px-4 py-2 bg-neon-blue text-slate-900 rounded-lg text-sm font-medium hover:bg-neon-blue/80 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        新增人才
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full">
                    <thead>
                        <tr class="border-b border-slate-700">
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">姓名</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">类型</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">归属单位</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">行业领域</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">引进方式</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">年龄</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">参与项目</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="talentTableBody" class="divide-y divide-slate-700/50">
                        <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="openModal('talentDetailModal')">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-neon-blue/20 flex items-center justify-center text-neon-blue mr-3">
                                        <span class="font-medium">张</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium">张明</div>
                                        <div class="text-xs text-gray-400">高级工程师</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">研发人员</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm">宁波大学</div>
                                <div class="text-xs text-gray-400">海曙区</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">信息技术</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">自主培养</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">38</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">5</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-neon-blue hover:text-neon-blue/80 mr-3">详情</button>
                                <button class="text-gray-400 hover:text-white">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="openModal('talentDetailModal')">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-neon-purple/20 flex items-center justify-center text-neon-purple mr-3">
                                        <span class="font-medium">李</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium">李华</div>
                                        <div class="text-xs text-gray-400">研究员</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">专家</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm">中科院宁波材料所</div>
                                <div class="text-xs text-gray-400">鄞州区</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">新材料</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">海外引进</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">45</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">8</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-neon-blue hover:text-neon-blue/80 mr-3">详情</button>
                                <button class="text-gray-400 hover:text-white">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="openModal('talentDetailModal')">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-neon-green/20 flex items-center justify-center text-neon-green mr-3">
                                        <span class="font-medium">王</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium">王芳</div>
                                        <div class="text-xs text-gray-400">教授</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">科研管理</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm">宁波诺丁汉大学</div>
                                <div class="text-xs text-gray-400">鄞州区</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">生物医药</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">项目引进</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">42</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">3</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-neon-blue hover:text-neon-blue/80 mr-3">详情</button>
                                <button class="text-gray-400 hover:text-white">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="openModal('talentDetailModal')">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-neon-yellow/20 flex items-center justify-center text-neon-yellow mr-3">
                                        <span class="font-medium">赵</span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium">赵强</div>
                                        <div class="text-xs text-gray-400">工程师</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">科技特派员</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm">宁波激智科技</div>
                                <div class="text-xs text-gray-400">北仑区</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">高端装备</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">企业引进</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">35</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="text-sm">2</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-neon-blue hover:text-neon-blue/80 mr-3">详情</button>
                                <button class="text-gray-400 hover:text-white">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="flex justify-between items-center mt-5">
                <div class="text-sm text-gray-400">
                    显示 1-4 条，共 34,459 条
                </div>
                <div class="flex space-x-1">
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 text-gray-400 hover:bg-slate-700/50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-neon-blue bg-neon-blue/10 text-neon-blue">1</button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 hover:bg-slate-700/50">2</button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 hover:bg-slate-700/50">3</button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 hover:bg-slate-700/50">4</button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 hover:bg-slate-700/50">5</button>
                    <button class="w-8 h-8 flex items-center justify-center rounded border border-slate-700 hover:bg-slate-700/50">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- 人才列表弹窗 -->
    <div id="talentListModal" class="modal-overlay fixed inset-0 bg-black/70 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
        <div class="bg-slate-800 border border-slate-700 rounded-lg w-full max-w-5xl max-h-[90vh] flex flex-col">
            <div class="p-5 border-b border-slate-700 flex justify-between items-center">
                <h3 class="text-xl font-bold">研发人员清册</h3>
                <button onclick="closeModal('talentListModal')" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="p-5 border-b border-slate-700">
                <div class="flex flex-wrap gap-3">
                    <div class="relative">
                        <input type="text" placeholder="搜索研发人员..." class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-neon-blue/50 w-64">
                        <svg class="absolute left-3 top-2.5 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-neon-blue/50">
                            <option>全部单位</option>
                            <option>高校</option>
                            <option>科研院所</option>
                            <option>企业</option>
                            <option>医疗机构</option>
                        </select>
                        <div class="absolute right-3 top-2.5 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <div class="relative">
                        <select class="bg-slate-700/50 border border-slate-600 rounded-lg px-4 py-2 pr-8 text-sm appearance-none focus:outline-none focus:ring-2 focus:ring-neon-blue/50">
                            <option>全部职称</option>
                            <option>正高级</option>
                            <option>副高级</option>
                            <option>中级</option>
                            <option>初级</option>
                        </select>
                        <div class="absolute right-3 top-2.5 pointer-events-none">
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    
                    <button class="px-4 py-2 bg-neon-blue text-slate-900 rounded-lg text-sm font-medium hover:bg-neon-blue/80 transition-colors duration-300 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                </div>
            </div>
            
            <div class="flex-1 overflow-y-auto p-5">
                <div class="overflow-x-auto">
                    <table class="min-w-full">
                        <thead>
                            <tr class="border-b border-slate-700">
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">姓名</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">职称</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">归属单位</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">研究方向</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">学历</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">年龄</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">联系方式</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-slate-700/50">
                            <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="closeModal('talentListModal'); openModal('talentDetailModal')">
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 rounded-full bg-neon-blue/20 flex items-center justify-center text-neon-blue mr-3">
                                            <span class="font-medium">张</span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium">张明</div>
                                            <div class="text-xs text-gray-400">高级工程师</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm">高级工程师</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <div class="text-sm">宁波大学</div>
                                    <div class="text-xs text-gray-400">海曙区</div>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm">人工智能</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm">博士</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm">38</span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap">
                                    <span class="text-sm"><EMAIL></span>
                                </td>
                                <td class="px-4 py-4 whitespace-nowrap text-sm">
                                    <button class="text-neon-blue hover:text-neon-blue/80">详情</button>
                                </td>
                            </tr>
                            <!-- 更多表格行... -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="p-5 border-t border-slate-700 flex justify-between items-center">
                <div class="text-sm text-gray-400">
                    显示 1-1 条，共 28,764 条
                </div>
                <button class="px-4 py-2 bg-gray-700 rounded-lg text-sm hover:bg-gray-600 transition-colors">
                    导出数据
                </button>
            </div>
        </div>
    </div>

    <!-- 人才详情弹窗 -->
    <div id="talentDetailModal" class="modal-overlay fixed inset-0 bg-black/70 backdrop-blur-sm z-50 hidden flex items-center justify-center p-4">
        <div class="bg-slate-800 border border-slate-700 rounded-lg w-full max-w-5xl max-h-[90vh] flex flex-col">
            <div class="p-5 border-b border-slate-700 flex justify-between items-center">
                <h3 class="text-xl font-bold">人才详情</h3>
                <button onclick="closeModal('talentDetailModal')" class="text-gray-400 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="flex-1 overflow-y-auto p-5">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 左侧基本信息 -->
                    <div class="md:col-span-1">
                        <div class="bg-slate-700/30 rounded-lg p-5 mb-6">
                            <div class="flex flex-col items-center mb-4">
                                <div class="w-24 h-24 rounded-full bg-neon-blue/20 flex items-center justify-center text-neon-blue text-4xl font-bold mb-3">
                                    张
                                </div>
                                <h4 class="text-xl font-bold">张明</h4>
                                <p class="text-gray-400">高级工程师</p>
                            </div>
                            
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-400">人才类型:</span>
                                    <span>研发人员</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">归属单位:</span>
                                    <span>宁波大学</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">所在区域:</span>
                                    <span>海曙区</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">行业领域:</span>
                                    <span>信息技术</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">引进方式:</span>
                                    <span>自主培养</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">人才层次:</span>
                                    <span>领军人才</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">年龄:</span>
                                    <span>38岁</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-400">联系方式:</span>
                                    <span class="text-neon-blue"><EMAIL></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-slate-700/30 rounded-lg p-5">
                            <h4 class="text-lg font-semibold mb-4">人才标签</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-neon-blue/20 text-neon-blue text-xs rounded-full">人工智能</span>
                                <span class="px-3 py-1 bg-neon-purple/20 text-neon-purple text-xs rounded-full">机器学习</span>
                                <span class="px-3 py-1 bg-neon-green/20 text-neon-green text-xs rounded-full">自然语言处理</span>
                                <span class="px-3 py-1 bg-neon-yellow/20 text-neon-yellow text-xs rounded-full">深度学习</span>
                                <span class="px-3 py-1 bg-neon-pink/20 text-neon-pink text-xs rounded-full">计算机视觉</span>
                                <span class="px-3 py-1 bg-slate-600 text-gray-300 text-xs rounded-full">高校</span>
                                <span class="px-3 py-1 bg-slate-600 text-gray-300 text-xs rounded-full">博士</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧详细信息 -->
                    <div class="md:col-span-2 space-y-6">
                        <!-- 教育背景 -->
                        <div class="bg-slate-700/30 rounded-lg p-5">
                            <h4 class="text-lg font-semibold mb-4">教育背景</h4>
                            <div class="space-y-4">
                                <div class="flex">
                                    <div class="mr-4 text-gray-400">2005-2009</div>
                                    <div>
                                        <div class="font-medium">浙江大学</div>
                                        <div class="text-sm text-gray-400">计算机科学与技术 本科</div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="mr-4 text-gray-400">2009-2012</div>
                                    <div>
                                        <div class="font-medium">浙江大学</div>
                                        <div class="text-sm text-gray-400">计算机应用技术 硕士</div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="mr-4 text-gray-400">2012-2016</div>
                                    <div>
                                        <div class="font-medium">上海交通大学</div>
                                        <div class="text-sm text-gray-400">计算机科学与技术 博士</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 工作经历 -->
                        <div class="bg-slate-700/30 rounded-lg p-5">
                            <h4 class="text-lg font-semibold mb-4">工作经历</h4>
                            <div class="space-y-4">
                                <div class="flex">
                                    <div class="mr-4 text-gray-400">2016-2018</div>
                                    <div>
                                        <div class="font-medium">阿里巴巴达摩院</div>
                                        <div class="text-sm text-gray-400">资深算法工程师</div>
                                    </div>
                                </div>
                                <div class="flex">
                                    <div class="mr-4 text-gray-400">2018-至今</div>
                                    <div>
                                        <div class="font-medium">宁波大学信息科学与工程学院</div>
                                        <div class="text-sm text-gray-400">副教授、硕士生导师</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 参与项目 -->
                        <div class="bg-slate-700/30 rounded-lg p-5">
                            <h4 class="text-lg font-semibold mb-4">参与项目</h4>
                            <div class="space-y-4">
                                <div class="border-l-2 border-neon-blue pl-4">
                                    <div class="font-medium">基于深度学习的自然语言处理关键技术研究</div>
                                    <div class="text-sm text-gray-400">国家自然科学基金项目，2020-2023，项目负责人</div>
                                </div>
                                <div class="border-l-2 border-neon-purple pl-4">
                                    <div class="font-medium">智能医疗影像分析系统研发</div>
                                    <div class="text-sm text-gray-400">浙江省重点研发计划项目，2021-2024，核心成员</div>
                                </div>
                                <div class="border-l-2 border-neon-green pl-4">
                                    <div class="font-medium">宁波市智慧城市大脑人工智能平台建设</div>
                                    <div class="text-sm text-gray-400">宁波市重大科技专项，2019-2022，技术负责人</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 获奖情况 -->
                        <div class="bg-slate-700/30 rounded-lg p-5">
                            <h4 class="text-lg font-semibold mb-4">获奖情况</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="font-medium">浙江省科技进步奖二等奖</div>
                                        <div class="text-sm text-gray-400">基于深度学习的智能分析系统研发与应用</div>
                                    </div>
                                    <div class="text-gray-400">2022年</div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <div>
                                        <div class="font-medium">宁波市青年科技奖</div>
                                        <div class="text-sm text-gray-400">人工智能领域创新成果</div>
                                    </div>
                                    <div class="text-gray-400">2021年</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="p-5 border-t border-slate-700 flex justify-end space-x-3">
                <button onclick="closeModal('talentDetailModal')" class="px-4 py-2 bg-gray-700 rounded-lg text-sm hover:bg-gray-600 transition-colors">
                    关闭
                </button>
                <button class="px-4 py-2 bg-neon-blue text-slate-900 rounded-lg text-sm font-medium hover:bg-neon-blue/80 transition-colors">
                    编辑信息
                </button>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // 更新人才列表数据
        function updateTalentList(region) {
            let regionName = '';
            switch(region) {
                case 'haishu': regionName = '海曙区'; break;
                case 'yinzhou': regionName = '鄞州区'; break;
                case 'beilun': regionName = '北仑区'; break;
                case 'jiangbei': regionName = '江北区'; break;
                case 'zhenhai': regionName = '镇海区'; break;
                default: regionName = '宁波市';
            }
            
            const tableBody = document.getElementById('talentTableBody');
            if (tableBody) {
                tableBody.innerHTML = `
                    <tr class="hover:bg-slate-700/30 transition-colors cursor-pointer" onclick="openModal('talentDetailModal')">
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-8 h-8 rounded-full bg-neon-blue/20 flex items-center justify-center text-neon-blue mr-3">
                                    <span class="font-medium">张</span>
                                </div>
                                <div>
                                    <div class="text-sm font-medium">张明</div>
                                    <div class="text-xs text-gray-400">高级工程师</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm">研发人员</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <div class="text-sm">宁波大学</div>
                            <div class="text-xs text-gray-400">${regionName}</div>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm">信息技术</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm">自主培养</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm">38</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap">
                            <span class="text-sm">5</span>
                        </td>
                        <td class="px-4 py-4 whitespace-nowrap text-sm">
                            <button class="text-neon-blue hover:text-neon-blue/80 mr-3">详情</button>
                            <button class="text-gray-400 hover:text-white">编辑</button>
                        </td>
                    </tr>
                `;
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化小卡片趋势图
            const createMiniChart = (id, color, data) => {
                const ctx = document.getElementById(id).getContext('2d');
                return new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', '', '', '', '', '', ''],
                        datasets: [{
                            data: data,
                            borderColor: color,
                            borderWidth: 2,
                            pointRadius: 0,
                            tension: 0.4,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        },
                        elements: { line: { tension: 0.4 } }
                    }
                });
            };

            // 初始化各卡片趋势图
            createMiniChart('researcherChart', '#38bdf8', [3, 5, 4, 6, 5, 7, 6, 8, 7, 9, 8, 10]);
            createMiniChart('managerChart', '#818cf8', [2, 3, 2, 4, 3, 5, 4, 5, 4, 6, 5, 6]);
            createMiniChart('expertChart', '#eab308', [1, 2, 3, 2, 4, 3, 5, 4, 6, 5, 7, 6]);
            createMiniChart('commissionerChart', '#ec4899', [5, 4, 5, 4, 3, 4, 3, 2, 3, 2, 1, 2]);

            // 行业分布图表
            const industryCtx = document.getElementById('industryChart').getContext('2d');
            new Chart(industryCtx, {
                type: 'bar',
                data: {
                    labels: ['信息技术', '生物医药', '高端装备', '新材料', '新能源', '节能环保', '海洋经济'],
                    datasets: [{
                        label: '人才数量',
                        data: [12560, 8750, 6430, 4320, 2150, 1870, 1380],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(129, 140, 248, 0.7)',
                            'rgba(34, 197, 94, 0.7)',
                            'rgba(234, 179, 8, 0.7)',
                            'rgba(236, 72, 153, 0.7)',
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(99, 102, 241, 0.7)'
                        ],
                        borderWidth: 0,
                        borderRadius: 4
                    }]
                },
                options: {
                    indexAxis: 'y',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { 
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `人才数量: ${context.raw.toLocaleString()}`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: { 
                            grid: { color: 'rgba(255, 255, 255, 0.1)' },
                            ticks: { color: '#9CA3AF' }
                        },
                        y: { 
                            grid: { display: false },
                            ticks: { color: '#9CA3AF' }
                        }
                    }
                }
            });

            // 年龄分布图表
            const ageCtx = document.getElementById('ageChart').getContext('2d');
            new Chart(ageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['25岁以下', '25-35岁', '35-45岁', '45-55岁', '55岁以上'],
                    datasets: [{
                        data: [3200, 12500, 10800, 5600, 2359],
                        backgroundColor: [
                            'rgba(56, 189, 248, 0.7)',
                            'rgba(129, 140, 248, 0.7)',
                            'rgba(34, 197, 94, 0.7)',
                            'rgba(234, 179, 8, 0.7)',
                            'rgba(236, 72, 153, 0.7)'
                        ],