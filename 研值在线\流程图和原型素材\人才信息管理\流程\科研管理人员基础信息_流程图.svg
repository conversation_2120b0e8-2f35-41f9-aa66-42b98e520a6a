<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研管理人员基础信息管理流程</text>

  <!-- 阶段一：数据归集与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与标准化</text>
  
  <!-- 节点1: 多渠道数据归集 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多渠道数据归集</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(市科技局、高校、科研机构)</text>
  </g>

  <!-- 节点2: 数据标准化处理 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据标准化处理</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(字段补全、关联建立)</text>
  </g>

  <!-- 节点3: 精准关联匹配 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">精准关联匹配</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(项目、企业、平台信息)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：查询检索与展示 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询检索与展示</text>

  <!-- 节点4: 用户进入页面 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(基础信息管理模块)</text>
  </g>

  <!-- 节点5: 多条件筛选 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多条件筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(即时检索展示)</text>
  </g>

  <!-- 节点6: 信息列表展示 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息列表展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(符合条件的管理人员)</text>
  </g>

  <!-- 连接线 数据处理 -> 用户进入 -->
  <path d="M 560 200 C 560 240, 300 270, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情操作与管理 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情操作与管理</text>

  <!-- 节点7: 详情钻取 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(完整信息与关联资源)</text>
  </g>

  <!-- 节点8: 移除操作 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(二次确认、原因填写)</text>
  </g>

  <!-- 节点9: 批量操作 -->
  <g transform="translate(650, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(批量移除、批量导出)</text>
  </g>

  <!-- 节点10: 日志归档 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(操作记录、列表更新)</text>
  </g>

  <!-- 连接线 列表展示 -> 详情钻取 -->
  <path d="M 850 380 C 750 420, 350 450, 250 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 列表展示 -> 移除操作 -->
  <path d="M 880 380 C 800 420, 600 450, 500 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 列表展示 -> 批量操作 -->
  <path d="M 900 380 C 900 420, 800 450, 750 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 移除操作 -> 日志归档 -->
  <path d="M 600 525 Q 750 525 900 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 批量操作 -> 日志归档 -->
  <path d="M 850 525 Q 875 525 900 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统保障与合规 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统保障与合规</text>
  
  <!-- 节点11: 全程审计追溯 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
      <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">全程审计追溯</text>
      <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-80">数据完整性</tspan>
        <tspan dx="40">可溯源</tspan>
        <tspan dx="40">合规性</tspan>
      </text>
  </g>

  <!-- 节点12: 数据安全保障 -->
  <g transform="translate(700, 670)" filter="url(#soft-shadow)">
      <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据安全保障</text>
      <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-80">权限控制</tspan>
        <tspan dx="40">备份恢复</tspan>
        <tspan dx="40">风险防控</tspan>
      </text>
  </g>

  <!-- 连接线 日志归档 -> 全程审计 -->
  <path d="M 950 560 C 850 600, 550 640, 450 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 日志归档 -> 数据安全 -->
  <path d="M 1000 560 C 1000 600, 950 640, 850 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从数据安全保障回到数据归集 -->
  <path d="M 850 670 C 1200 600, 1200 100, 970 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 1150 400)">安全保障循环</text>

  <!-- 反馈循环：从详情钻取回到筛选条件 -->
  <path d="M 150 525 C 50 450, 50 280, 200 310" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 100 400)">深度查询循环</text>

</svg>