<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发团队展示模块业务流程</text>

  <!-- 阶段一：页面初始化与权限验证 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与权限验证</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">研发团队展示模块</text>
  </g>

  <!-- 节点2: 权限验证与数据加载 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">团队成员主数据与聚合指标</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点3: 筛选条件设定 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">团队、职务、学历等条件</text>
  </g>

  <!-- 节点4: 数据服务查询 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据服务查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">人事数据服务过滤</text>
  </g>

  <!-- 连接线 权限验证 -> 筛选条件 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选条件 -> 数据服务 -->
  <path d="M 500 455 C 650 455, 750 455, 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="445" text-anchor="middle" font-size="12" fill="#555">发送过滤参数</text>

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>
  
  <!-- 节点5: 详情查看 -->
  <g transform="translate(200, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">教育背景、项目记录</text>
  </g>

  <!-- 节点6: 侧栏交互 -->
  <g transform="translate(500, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">侧栏交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标签切换、报告导出</text>
  </g>

  <!-- 节点7: 统计分析 -->
  <g transform="translate(800, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计分析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合可视化图表</text>
  </g>

  <!-- 阶段四：资源管理与优化 -->
  <text x="700" y="730" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：资源管理与优化</text>
  
  <!-- 节点8: 资源释放与记录保存 -->
  <g transform="translate(500, 780)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">资源释放与记录保存</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">缓存资源释放</tspan>
        <tspan dx="40">筛选偏好保存</tspan>
        <tspan dx="40">个性化推荐优化</tspan>
      </text>
  </g>

  <!-- 连接线 数据服务 -> 详情查看 -->
  <path d="M 950 490 C 950 530, 350 560, 300 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情查看 -> 侧栏交互 -->
  <path d="M 400 635 Q 450 635 500 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 侧栏交互 -> 统计分析 -->
  <path d="M 700 635 Q 750 635 800 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线到资源管理 -->
  <path d="M 300 670 C 300 720, 600 750, 600 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 670 Q 600 725 600 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 670 C 900 720, 800 750, 800 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <path d="M 1000 455 C 1150 455, 1150 200, 800 200" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="1100" y="320" text-anchor="middle" font-size="11" fill="#666">数据刷新反馈</text>

  <!-- 操作日志反馈线 (虚线) -->
  <path d="M 600 670 C 600 700, 1050 700, 1050 490" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="850" y="690" text-anchor="middle" font-size="11" fill="#666">操作日志记录</text>

</svg>