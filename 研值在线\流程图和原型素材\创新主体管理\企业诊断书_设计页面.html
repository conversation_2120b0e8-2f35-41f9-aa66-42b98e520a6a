<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业诊断书</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">企业诊断书</h1>
            <p class="text-gray-600">全周期记录企业诊断服务，提供可视化成效评估与追踪依据</p>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">当年诊断次数</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">24</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span class="text-sm ml-1">12%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">累计诊断次数</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">156</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span class="text-sm ml-1">8%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">服务机构数量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">12</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span class="text-sm ml-1">5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">服务专家数量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">38</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span class="text-sm ml-1">15%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">平均诊断间隔</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">45天</p>
                    </div>
                    <div class="flex items-center text-red-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                        <span class="text-sm ml-1">3%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">培训参与率</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">82%</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span class="text-sm ml-1">7%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">诊断年份</label>
                    <div class="flex space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">起始年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">服务机构</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部机构</option>
                        <option value="1">宁波市企业服务中心</option>
                        <option value="2">宁波市科技创新研究院</option>
                        <option value="3">宁波市中小企业发展促进会</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">服务专家</label>
                    <input type="text" placeholder="输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">诊断类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="1">战略诊断</option>
                        <option value="2">管理诊断</option>
                        <option value="3">技术诊断</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入诊断主题关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex space-x-3 mt-4">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置
                </button>
            </div>
        </div>

        <!-- 诊断记录列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        诊断记录列表
                    </h2>
                    <div class="flex space-x-2">
                        <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12"></path>
                            </svg>
                            导出
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">诊断时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务机构</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务专家</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">诊断主题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主要意见</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">培训情况</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2024-03-15</div>
                                <div class="text-sm text-gray-500">09:30-11:30</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市企业服务中心</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">王教授</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">数字化转型战略规划</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500 line-clamp-2">建议企业加快数字化基础设施建设，优先实施ERP系统，并加强员工数字化技能培训...</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                    <button class="text-yellow-600 hover:text-yellow-900">下钻</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2024-02-28</div>
                                <div class="text-sm text-gray-500">14:00-16:30</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市科技创新研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">李博士</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">智能制造技术升级</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500 line-clamp-2">评估了企业现有生产线的自动化水平，建议分阶段引入工业机器人，并申请宁波市智能制造专项...</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">未参与</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                    <button class="text-yellow-600 hover:text-yellow-900">下钻</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2024-01-10</div>
                                <div class="text-sm text-gray-500">10:00-12:00</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市中小企业发展促进会</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">张顾问</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">人力资源管理优化</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-500 line-clamp-2">分析了企业人才流失率高的原因，建议完善绩效考核体系，建立员工职业发展通道，并优化薪酬...</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                    <button class="text-yellow-600 hover:text-yellow-900">下钻</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">24</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                统计分析
            </h2>
            <div class="flex space-x-2 mb-4">
                <button id="barChartBtn" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">年度分布</button>
                <button id="areaChartBtn" class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">机构趋势</button>
                <button id="radarChartBtn" class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">专家分析</button>
            </div>
            <div id="mainChart" class="w-full h-[400px]"></div>
        </div>
    </div>

    <!-- 诊断详情侧栏 -->
    <div id="detailSidebar" class="fixed inset-y-0 right-0 w-full max-w-xl bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">诊断详情</h3>
                <button onclick="closeDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">基本信息</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">诊断时间</p>
                                <p class="text-sm font-medium text-gray-900">2024-03-15 09:30-11:30</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">服务机构</p>
                                <p class="text-sm font-medium text-gray-900">宁波市企业服务中心</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">服务专家</p>
                                <p class="text-sm font-medium text-gray-900">王教授</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">诊断类型</p>
                                <p class="text-sm font-medium text-gray-900">战略诊断</p>
                            </div>
                        </div>
                    </div>

                    <!-- 标签页 -->
                    <div>
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button id="tab1" class="border-b-2 border-blue-500 text-blue-600 px-1 py-2 text-sm font-medium">诊断意见</button>
                                <button id="tab2" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">问题清单</button>
                                <button id="tab3" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">改进建议</button>
                                <button id="tab4" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">跟进记录</button>
                            </nav>
                        </div>
                        <div class="mt-4">
                            <div id="content1" class="space-y-4">
                                <p class="text-sm text-gray-700">企业数字化转型战略规划诊断意见：</p>
                                <p class="text-sm text-gray-700">1. 企业当前数字化水平处于初级阶段，建议优先实施ERP系统整合各部门数据，预计可提升运营效率30%...</p>
                                <p class="text-sm text-gray-700">2. 员工数字化技能培训不足，建议制定分层次培训计划，并申请宁波市数字化人才培训补贴...</p>
                            </div>
                            <div id="content2" class="hidden space-y-4">
                                <p class="text-sm text-gray-700">1. 各部门信息系统孤立，数据无法共享</p>
                                <p class="text-sm text-gray-700">2. 缺乏数字化战略规划部门</p>
                                <p class="text-sm text-gray-700">3. 员工数字化技能认证率不足20%</p>
                            </div>
                            <div id="content3" class="hidden space-y-4">
                                <p class="text-sm text-gray-700">1. 成立数字化战略委员会，由总经理直接领导</p>
                                <p class="text-sm text-gray-700">2. 分三期实施ERP系统，首期投入预算150万元</p>
                                <p class="text-sm text-gray-700">3. 申请宁波市数字化改造专项补贴</p>
                            </div>
                            <div id="content4" class="hidden space-y-4">
                                <p class="text-sm text-gray-700">2024-04-01 已成立数字化战略委员会</p>
                                <p class="text-sm text-gray-700">2024-04-15 ERP系统招标完成</p>
                                <p class="text-sm text-gray-700">2024-04-20 数字化培训计划启动</p>
                            </div>
                        </div>
                    </div>

                    <!-- 附件 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">诊断附件</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 border rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">数字化转型诊断报告.pdf</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border rounded-md">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">问题清单与改进建议.xlsx</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50">
                <button onclick="closeDetail()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    关闭
                </button>
                <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    打印诊断书
                </button>
            </div>
        </div>
    </div>

    <script>
        // 初始化图表
        function initCharts() {
            const chartDom = document.getElementById('mainChart');
            const myChart = echarts.init(chartDom);
            
            // 默认显示柱状图
            showBarChart(myChart);
            
            // 按钮切换事件
            document.getElementById('barChartBtn').addEventListener('click', () => showBarChart(myChart));
            document.getElementById('areaChartBtn').addEventListener('click', () => showAreaChart(myChart));
            document.getElementById('radarChartBtn').addEventListener('click', () => showRadarChart(myChart));
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 显示柱状图
        function showBarChart(chart) {
            const option = {
                title: {
                    text: '年度诊断次数分布',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['诊断次数'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['2020', '2021', '2022', '2023', '2024'],
                    axisLabel: {
                        interval: 0
                    }
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '诊断次数',
                        type: 'bar',
                        data: [18, 24, 32, 42, 24],
                        itemStyle: {
                            color: '#3b82f6'
                        },
                        label: {
                            show: true,
                            position: 'top'
                        }
                    }
                ]
            };
            chart.setOption(option, true);
            updateActiveButton('barChartBtn');
        }
        
        // 显示面积图
        function showAreaChart(chart) {
            const option = {
                title: {
                    text: '多机构诊断覆盖趋势',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        label: {
                            backgroundColor: '#6a7985'
                        }
                    }
                },
                legend: {
                    data: ['企业服务中心', '科技创新研究院', '中小企业促进会'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: [
                    {
                        type: 'category',
                        boundaryGap: false,
                        data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
                    }
                ],
                yAxis: [
                    {
                        type: 'value'
                    }
                ],
                series: [
                    {
                        name: '企业服务中心',
                        type: 'line',
                        stack: '总量',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [3, 4, 3, 4, 3, 4, 3],
                        itemStyle: {
                            color: '#3b82f6'
                        }
                    },
                    {
                        name: '科技创新研究院',
                        type: 'line',
                        stack: '总量',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [2, 2, 3, 2, 1, 2, 2],
                        itemStyle: {
                            color: '#10b981'
                        }
                    },
                    {
                        name: '中小企业促进会',
                        type: 'line',
                        stack: '总量',
                        areaStyle: {},
                        emphasis: {
                            focus: 'series'
                        },
                        data: [1, 1, 2, 1, 2, 1, 1],
                        itemStyle: {
                            color: '#f59e0b'
                        }
                    }
                ]
            };
            chart.setOption(option, true);
            updateActiveButton('areaChartBtn');
        }
        
        // 显示雷达图
        function showRadarChart(chart) {
            const option = {
                title: {
                    text: '服务专家诊断意见类别占比',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    data: ['王教授', '李博士', '张顾问'],
                    top: 30
                },
                radar: {
                    indicator: [
                        { name: '战略规划', max: 100 },
                        { name: '技术创新', max: 100 },
                        { name: '管理优化', max: 100 },
                        { name: '市场拓展', max: 100 },
                        { name: '人才培养', max: 100 }
                    ]
                },
                series: [
                    {
                        name: '专家诊断意见分布',
                        type: 'radar',
                        data: [
                            {
                                value: [85, 60, 40, 75, 90],
                                name: '王教授',
                                itemStyle: {
                                    color: '#3b82f6'
                                }
                            },
                            {
                                value: [40, 95, 30, 60, 50],
                                name: '李博士',
                                itemStyle: {
                                    color: '#10b981'
                                }
                            },
                            {
                                value: [30, 20, 95, 40, 80],
                                name: '张顾问',
                                itemStyle: {
                                    color: '#f59e0b'
                                }
                            }
                        ]
                    }
                ]
            };
            chart.setOption(option, true);
            updateActiveButton('radarChartBtn');
        }
        
        // 更新活动按钮样式
        function updateActiveButton(activeId) {
            document.getElementById('barChartBtn').className = 'px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50';
            document.getElementById('areaChartBtn').className = 'px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50';
            document.getElementById('radarChartBtn').className = 'px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50';
            
            document.getElementById(activeId).className = 'px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700';
        }
        
        // 详情侧栏
        function viewDetail() {
            document.getElementById('detailSidebar').classList.remove('translate-x-full');
        }
        
        function closeDetail() {
            document.getElementById('detailSidebar').classList.add('translate-x-full');
        }
        
        // 标签页切换
        document.getElementById('tab1').addEventListener('click', function() {
            document.getElementById('content1').classList.remove('hidden');
            document.getElementById('content2').classList.add('hidden');
            document.getElementById('content3').classList.add('hidden');
            document.getElementById('content4').classList.add('hidden');
            this.classList.add('border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            document.getElementById('tab2').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab3').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab4').classList.remove('border-blue-500', 'text-blue-600');
        });
        
        document.getElementById('tab2').addEventListener('click', function() {
            document.getElementById('content1').classList.add('hidden');
            document.getElementById('content2').classList.remove('hidden');
            document.getElementById('content3').classList.add('hidden');
            document.getElementById('content4').classList.add('hidden');
            this.classList.add('border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            document.getElementById('tab1').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab3').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab4').classList.remove('border-blue-500', 'text-blue-600');
        });
        
        document.getElementById('tab3').addEventListener('click', function() {
            document.getElementById('content1').classList.add('hidden');
            document.getElementById('content2').classList.add('hidden');
            document.getElementById('content3').classList.remove('hidden');
            document.getElementById('content4').classList.add('hidden');
            this.classList.add('border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            document.getElementById('tab1').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab2').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab4').classList.remove('border-blue-500', 'text-blue-600');
        });
        
        document.getElementById('tab4').addEventListener('click', function() {
            document.getElementById('content1').classList.add('hidden');
            document.getElementById('content2').classList.add('hidden');
            document.getElementById('content3').classList.add('hidden');
            document.getElementById('content4').classList.remove('hidden');
            this.classList.add('border-blue-500', 'text-blue-600');
            this.classList.remove('border-transparent', 'text-gray-500');
            document.getElementById('tab1').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab2').classList.remove('border-blue-500', 'text-blue-600');
            document.getElementById('tab3').classList.remove('border-blue-500', 'text-blue-600');
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
            
            // 点击模态框外部关闭
            document.getElementById('detailSidebar').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetail();
                }
            });
        });
    </script>
</body>
</html>