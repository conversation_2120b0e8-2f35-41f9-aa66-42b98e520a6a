object CC_MessageTempletFrame: TCC_MessageTempletFrame
  Left = 0
  Top = 0
  Width = 845
  Height = 595
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel2: TRzPanel
    Left = 0
    Top = 160
    Width = 845
    Height = 435
    Align = alClient
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    ExplicitWidth = 691
    object AdvStringGrid1: TAdvStringGrid
      Left = 0
      Top = 0
      Width = 845
      Height = 435
      Cursor = crDefault
      Align = alClient
      BevelInner = bvNone
      BevelOuter = bvNone
      ColCount = 7
      Ctl3D = False
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      ScrollBars = ssBoth
      TabOrder = 0
      OnMouseMove = AdvStringGrid1MouseMove
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      OnGetColumnFilter = AdvStringGrid1GetColumnFilter
      OnFilterSelect = AdvStringGrid1FilterSelect
      OnAnchorClick = AdvStringGrid1AnchorClick
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        #32534#21495
        #21517#31216)
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #24494#36719#38597#40657
      FilterDropDown.Font.Style = []
      FilterDropDown.Height = 300
      FilterDropDownAuto = True
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 80
      FixedRowHeight = 30
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -13
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      Flat = True
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      ShowSelection = False
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ExplicitWidth = 691
      ColWidths = (
        80
        270
        122
        64
        64
        64
        64)
      RowHeights = (
        30
        22
        22
        22
        22
        22
        22
        22
        22
        22)
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 0
    Width = 845
    Height = 160
    Align = alTop
    BorderOuter = fsFlat
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    ExplicitWidth = 691
    object RzPanel4: TRzPanel
      Left = 1
      Top = 1
      Width = 843
      Height = 40
      Align = alTop
      BorderOuter = fsFlat
      Color = clWhite
      TabOrder = 0
      ExplicitWidth = 689
      object Btn_Add: TAdvGlowButton
        Left = 416
        Top = 6
        Width = 82
        Height = 28
        Caption = #26032'  '#22686
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 0
        OnClick = Btn_AddClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16111818
        Appearance.ColorCheckedTo = 16367008
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 16102556
        Appearance.ColorMirrorCheckedTo = 16768988
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
      object Btn_Modify: TAdvGlowButton
        Left = 509
        Top = 6
        Width = 82
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -16
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 1
        OnClick = Btn_ModifyClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16111818
        Appearance.ColorCheckedTo = 16367008
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 16102556
        Appearance.ColorMirrorCheckedTo = 16768988
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
    end
    object RzPanel5: TRzPanel
      Left = 1
      Top = 82
      Width = 843
      Height = 77
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      ExplicitWidth = 689
      object RzLabel1: TRzLabel
        Left = 16
        Top = 27
        Width = 60
        Height = 17
        Caption = #27169#26495#20869#23481#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
      end
      object RzLabel4: TRzLabel
        Left = 16
        Top = 87
        Width = 76
        Height = 26
        Caption = #20179#24211#37197#32622
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel5: TRzLabel
        Left = 138
        Top = 93
        Width = 60
        Height = 19
        Caption = #24120#29992#25968#25454
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object Edit_MessageTemplet: TRzEdit
        Left = 84
        Top = 23
        Width = 493
        Height = 25
        Text = ''
        TabOrder = 0
        OnChange = Edit_MessageTempletChange
      end
    end
    object RzPanel1: TRzPanel
      Left = 1
      Top = 41
      Width = 843
      Height = 41
      Align = alTop
      BorderOuter = fsNone
      Color = 16049103
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 2
      ExplicitWidth = 689
      object SsToolbar: TRzToolbar
        Left = 0
        Top = 0
        Width = 843
        Height = 41
        Align = alClient
        AutoStyle = False
        RowHeight = 40
        ButtonLayout = blGlyphTop
        ButtonWidth = 60
        ButtonHeight = 40
        ShowButtonCaptions = True
        TextOptions = ttoShowTextLabels
        WrapControls = False
        BorderInner = fsNone
        BorderOuter = fsNone
        BorderSides = [sdTop]
        BorderWidth = 0
        Color = 16049103
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        GradientColorStop = 14215660
        ParentFont = False
        TabOrder = 0
        Transparent = True
        VisualStyle = vsGradient
        ExplicitWidth = 689
        ToolbarControls = (
          B_Tab1
          RzSpacer9
          B_Tab2
          RzSpacer15
          B_Tab3
          RzSpacer1
          B_Tab4
          RzSpacer2
          B_Tab5
          RzSpacer3
          B_Tab6
          RzSpacer4
          B_Tab7
          RzSpacer5
          B_Tab8
          RzSpacer6
          B_Tab9)
        object RzSpacer9: TRzSpacer
          Left = 84
          Top = 10
          Width = 5
        end
        object RzSpacer15: TRzSpacer
          Left = 169
          Top = 10
          Width = 5
        end
        object RzSpacer1: TRzSpacer
          Left = 254
          Top = 10
          Width = 5
        end
        object RzSpacer2: TRzSpacer
          Left = 339
          Top = 10
          Width = 5
        end
        object RzSpacer3: TRzSpacer
          Left = 424
          Top = 10
          Width = 5
        end
        object RzSpacer4: TRzSpacer
          Left = 509
          Top = 10
          Width = 5
        end
        object RzSpacer5: TRzSpacer
          Left = 594
          Top = 10
          Width = 5
        end
        object RzSpacer6: TRzSpacer
          Left = 679
          Top = 10
          Width = 5
        end
        object B_Tab1: TAdvGlowButton
          Left = 4
          Top = 6
          Width = 80
          Height = 33
          Caption = #24635' '#25253' '#20215
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 0
          OnClick = B_Tab1Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab2: TAdvGlowButton
          Left = 89
          Top = 6
          Width = 80
          Height = 33
          Caption = #20027'  '#26009
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 1
          OnClick = B_Tab2Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab3: TAdvGlowButton
          Left = 174
          Top = 6
          Width = 80
          Height = 33
          Caption = #36741'  '#26009
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 2
          OnClick = B_Tab3Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab4: TAdvGlowButton
          Left = 259
          Top = 6
          Width = 80
          Height = 33
          Caption = #21360'  '#32483
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 3
          OnClick = B_Tab4Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab5: TAdvGlowButton
          Left = 344
          Top = 6
          Width = 80
          Height = 33
          Caption = #24037'  '#20215
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 4
          OnClick = B_Tab5Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab6: TAdvGlowButton
          Left = 429
          Top = 6
          Width = 80
          Height = 33
          Caption = #20986'  '#36135
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 5
          OnClick = B_Tab6Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab7: TAdvGlowButton
          Left = 514
          Top = 6
          Width = 80
          Height = 33
          Caption = #36130'  '#21153
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 7
          OnClick = B_Tab7Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab8: TAdvGlowButton
          Left = 599
          Top = 6
          Width = 80
          Height = 33
          Caption = #20986'  '#32435
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 6
          OnClick = B_Tab8Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
        object B_Tab9: TAdvGlowButton
          Left = 684
          Top = 6
          Width = 80
          Height = 33
          Caption = #29983'  '#20135
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 8
          OnClick = B_Tab9Click
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorChecked = 16111818
          Appearance.ColorCheckedTo = 16367008
          Appearance.ColorDisabled = clBtnFace
          Appearance.ColorDisabledTo = clBtnFace
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = clGradientActiveCaption
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 16102556
          Appearance.ColorMirrorCheckedTo = 16768988
          Appearance.ColorMirrorDisabled = clBtnFace
          Appearance.ColorMirrorDisabledTo = clBtnFace
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.SystemFont = False
        end
      end
    end
  end
end
