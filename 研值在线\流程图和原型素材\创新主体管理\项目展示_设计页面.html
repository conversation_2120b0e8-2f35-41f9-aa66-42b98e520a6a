<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">项目展示</h1>
                    <p class="mt-2 text-sm text-gray-600">集中呈现研发项目与高新技术产业投资项目的全生命周期信息，支持多维度筛选与详情查看</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出数据
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 统计概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <!-- 研发项目总数 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">研发项目总数</p>
                        <p class="text-2xl font-semibold text-gray-900 mt-1">254</p>
                    </div>
                    <div class="relative w-10 h-10">
                        <svg class="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="4"></circle>
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#3B82F6" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="30"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-blue-600">70%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 投资项目总数 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">投资项目总数</p>
                        <p class="text-2xl font-semibold text-gray-900 mt-1">187</p>
                    </div>
                    <div class="relative w-10 h-10">
                        <svg class="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="4"></circle>
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#10B981" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="45"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-green-600">55%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 在研项目数量 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">在研项目数量</p>
                        <p class="text-2xl font-semibold text-gray-900 mt-1">196</p>
                    </div>
                    <div class="relative w-10 h-10">
                        <svg class="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="4"></circle>
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#F59E0B" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="60"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-yellow-600">40%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 已验收项目数量 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">已验收项目</p>
                        <p class="text-2xl font-semibold text-gray-900 mt-1">128</p>
                    </div>
                    <div class="relative w-10 h-10">
                        <svg class="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="4"></circle>
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#8B5CF6" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="10"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-purple-600">90%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 项目总投入 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-start justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">项目总投入</p>
                        <p class="text-2xl font-semibold text-gray-900 mt-1">¥38.5亿</p>
                    </div>
                    <div class="relative w-10 h-10">
                        <svg class="w-10 h-10 transform -rotate-90" viewBox="0 0 36 36">
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#E5E7EB" stroke-width="4"></circle>
                            <circle cx="18" cy="18" r="16" fill="none" stroke="#EF4444" stroke-width="4" stroke-dasharray="100" stroke-dashoffset="25"></circle>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-xs font-bold text-red-600">75%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">条件筛选</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" value="research" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">研发项目</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" value="investment" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">投资项目</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="biomedical">生物医药</option>
                        <option value="electronic">电子信息</option>
                        <option value="new-materials">新材料</option>
                        <option value="intelligent-manufacturing">智能制造</option>
                        <option value="new-energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            立项
                        </label>
                        <label class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            在研
                        </label>
                        <label class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            验收
                        </label>
                        <label class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            终止
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="mt-4 flex items-center justify-between">
                <div class="relative w-1/3">
                    <input type="text" placeholder="输入项目名称、负责人或关键词搜索" class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        重置筛选
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        搜索
                    </button>
                </div>
            </div>
        </div>

        <!-- 主要内容区：项目列表与图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 项目列表区 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-900">项目列表</h3>
                            <div class="text-sm text-gray-500">
                                共找到 <span class="font-medium text-gray-900">441</span> 个项目
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前阶段</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">立项金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市新一代信息技术创新平台建设</div>
                                        <div class="text-xs text-gray-500">项目编号: NBXX-2023-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">电子信息</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥5,800万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥2,900万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                        <button onclick="openModal('detailDrawer')" class="hover:underline">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波生物医药创新药物研发</div>
                                        <div class="text-xs text-gray-500">项目编号: NBYY-2023-012</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李华</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">验收</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥3,200万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥3,200万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                        <button onclick="openModal('detailDrawer')" class="hover:underline">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波新材料产业基地建设</div>
                                        <div class="text-xs text-gray-500">项目编号: NXXC-2022-045</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">投资项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王刚</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">立项</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥12,500万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥3,750万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                        <button onclick="openModal('detailDrawer')" class="hover:underline">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波智能制造数字化工厂改造</div>
                                        <div class="text-xs text-gray-500">项目编号: NBZZ-2023-028</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">投资项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵强</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥8,600万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥5,160万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                        <button onclick="openModal('detailDrawer')" class="hover:underline">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波新能源汽车电池研发</div>
                                        <div class="text-xs text-gray-500">项目编号: NXXN-2022-019</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈静</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">终止</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥4,300万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥1,720万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                        <button onclick="openModal('detailDrawer')" class="hover:underline">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 441 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关联图表区 -->
            <div class="space-y-6">
                <!-- 图表折叠面板控制 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-semibold text-gray-900">项目分析图表</h3>
                            <button id="toggleChartsBtn" class="text-gray-500 hover:text-gray-700">
                                <svg id="toggleChartsIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div id="chartsContainer" class="p-6 space-y-8">
                        <!-- 资金投入结构图 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">资金投入结构</h4>
                            <div class="h-64">
                                <canvas id="fundStructureChart"></canvas>
                            </div>
                        </div>

                        <!-- 阶段进度对比图 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">阶段进度对比</h4>
                            <div class="h-64">
                                <canvas id="phaseProgressChart"></canvas>
                            </div>
                        </div>

                        <!-- 投入产出相关性分析 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">投入产出相关性</h4>
                            <div class="h-64">
                                <canvas id="inputOutputChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目产出概览 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">项目产出概览</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">专利数量</p>
                                    <p class="text-xs text-gray-500">累计授权</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-900">864</p>
                                <p class="text-xs text-green-600">同比增长 12.5%</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-md flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">论文发表</p>
                                    <p class="text-xs text-gray-500">SCI/核心期刊</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-900">327</p>
                                <p class="text-xs text-green-600">同比增长 8.3%</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-yellow-100 rounded-md flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">新产品</p>
                                    <p class="text-xs text-gray-500">已上市</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-900">143</p>
                                <p class="text-xs text-green-600">同比增长 15.7%</p>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-purple-100 rounded-md flex items-center justify-center mr-3">
                                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">经济效益</p>
                                    <p class="text-xs text-gray-500">新增产值</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-gray-900">¥127亿</p>
                                <p class="text-xs text-green-600">同比增长 23.1%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情抽屉 -->
    <div id="detailDrawer" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="absolute top-0 right-0 h-full w-full lg:w-3/5 max-w-4xl bg-white shadow-xl transform transition-transform duration-300 translate-x-full" id="drawerContent">
            <div class="flex flex-col h-full">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-900">项目详情</h3>
                    <button onclick="closeModal('detailDrawer')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto p-6">
                    <!-- 项目基本信息 -->
                    <div class="mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <p class="text-sm font-medium text-gray-500">项目名称</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">宁波市新一代信息技术创新平台建设</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">项目编号</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">NBXX-2023-001</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">项目类型</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">研发项目</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">行业领域</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">电子信息</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">承担单位</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">宁波市科技发展有限公司</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">合作单位</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">宁波大学、浙江大学宁波理工学院</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">负责人</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">张明</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">联系电话</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">0574-88887777</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">立项日期</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">2023-03-15</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">计划完成日期</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">2025-03-14</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">当前阶段</p>
                                <p class="text-base font-semibold text-gray-900 mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">项目进度</p>
                                <div class="mt-1">
                                    <div class="flex justify-between text-xs mb-1">
                                        <span>45%</span>
                                        <span>预计完成时间: 2025-03-14</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 项目目标与内容 -->
                    <div class="mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">项目目标与内容</h4>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500">项目目标</p>
                                <p class="text-base text-gray-700 mt-1">
                                    建设宁波市新一代信息技术创新平台，整合产学研资源，突破关键核心技术，培育高新技术企业，推动宁波信息技术产业升级和数字化转型。
                                </p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">主要研究内容</p>
                                <ul class="text-base text-gray-700 mt-1 space-y-2 list-disc list-inside">
                                    <li>新一代人工智能算法与应用研究</li>
                                    <li>5G通信技术研发与产业化</li>
                                    <li>工业互联网平台建设与应用</li>
                                    <li>大数据分析与安全技术研究</li>
                                    <li>物联网关键技术与智能设备开发</li>
                                </ul>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-500">预期成果</p>
                                <ul class="text-base text-gray-700 mt-1 space-y-2 list-disc list-inside">
                                    <li>申请发明专利20项以上，实用新型专利30项以上</li>
                                    <li>发表高水平学术论文30篇以上</li>
                                    <li>开发核心技术产品10项以上</li>
                                    <li>培育高新技术企业5家以上</li>
                                    <li>实现经济效益5亿元以上</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 经费信息 -->
                    <div class="mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">经费信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-gray-500">立项总金额</p>
                                <p class="text-2xl font-bold text-gray-900 mt-1">¥5,800万</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-gray-500">已拨付金额</p>
                                <p class="text-2xl font-bold text-gray-900 mt-1">¥2,900万</p>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-sm font-medium text-gray-500">已使用金额</p>
                                <p class="text-2xl font-bold text-gray-900 mt-1">¥1,850万</p>
                            </div>
                        </div>

                        <h5 class="text-md font-medium text-gray-900 mb-3">经费拨付记录</h5>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付批次</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付金额</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付日期</th>
                                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付状态</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">第一批次</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">¥1,740万</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-04-10</td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已拨付</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">第二批次</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">¥1,160万</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2023-10-15</td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已拨付</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">第三批次</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">¥1,450万</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2024-04-20</td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待拨付</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">第四批次</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">¥1,450万</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2024-10-25</td>
                                        <td class="px-4 py-3 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未到时间</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 项目产出 -->
                    <div class="mb-8">
                        <h4 class="text-lg font-medium text-gray-900 mb-4 pb-2 border-b border-gray-200">项目产出</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h5 class="text-md font-medium text-gray-900 mb-3">专利成果</h5>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                            <span class="text-xs font-bold text-blue-600">发</span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">一种基于人工智能的智能决策系统及方法</p>
                                            <p class="text-xs text-gray-500">发明专利 | 申请号: 202310245678.9 | 2023-06-15</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                            <span class="text-xs font-bold text-green-600">实</span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">一种物联网设备的低功耗通信装置</p>
                                            <p class="text-xs text-gray-500">实用新型专利 | 专利号: ZL202320345678.1 | 2023-09-20</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                            <span class="text-xs font-bold text-purple-600">外</span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">一种智能传感器的外观设计</p>
                                            <p class="text-xs text-gray-500">外观设计专利 | 专利号: ZL202330123456.7 | 2023-10-05</p>
                                        </div>
                                    </div>
                                </div>
                                <button class="mt-3 text-sm text-blue-600 hover:text-blue-900">查看全部专利 (12项)</button>
                            </div>
                            <div>
                                <h5 class="text-md font-medium text-gray-900 mb-3">论文成果</h5>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-red-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                            <span class="text-xs font-bold text-red-600">SCI</span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Research on artificial intelligence algorithm for smart city management</p>
                                            <p class="text-xs text-gray-500">IEEE Transactions on Intelligent Transportation Systems | IF: 9.521 | 2023-08-10</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                            <span class="text-xs font-bold text-yellow-600">EI</span>
                                        </div>
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">5G通信技术在工业互联网中的应用研究</p>
                                            <p class="text-xs text-gray-500">中国通信学报 | 2023-09-25</p>
                                        </div>
                                    </div>
                                </div>
                                <button class="mt-3 text-sm text-blue-600 hover:text-blue-900">查看全部论文 (8篇)</button>
                            </div>
                        </div>
                        <div class="mt-6">
                            <h5 class="text-md font-medium text-gray-900 mb-3">技术成果与产品</h5>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-indigo-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-xs font-bold text-indigo-600">产</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">智能城市管理平台V1.0</p>
                                        <p class="text-xs text-gray-500">软件著作权 | 登记号: 2023SR0456789 | 2023-07-15</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-teal-100 rounded-md flex items-center justify-center mr-3 mt-0.5">
                                        <span class="text-xs font-bold text-teal-600">新</span>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">工业物联网智能网关</p>
                                        <p class="text-xs text-gray-500">新产品 | 型号: NB-IoT-2023 | 2023-11-05</p>
                                    </div>
                                </div>
                            </div>
                            <button class="mt-3 text-sm text-blue-600 hover:text-blue-900">查看全部成果 (15项)</button>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        查看投资关联
                    </button>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            导出PDF
                        </button>
                        <button onclick="closeModal('detailDrawer')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            返回列表
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                
                // 如果是抽屉式弹窗，添加动画效果
                if (modalId === 'detailDrawer') {
                    setTimeout(() => {
                        document.getElementById('drawerContent').classList.remove('translate-x-full');
                    }, 10);
                }
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                // 如果是抽屉式弹窗，添加关闭动画
                if (modalId === 'detailDrawer') {
                    document.getElementById('drawerContent').classList.add('translate-x-full');
                    setTimeout(() => {
                        modal.classList.add('hidden');
                        document.body.style.overflow = 'auto';
                    }, 300);
                } else {
                    modal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 图表面板折叠/展开功能
            const toggleChartsBtn = document.getElementById('toggleChartsBtn');
            const toggleChartsIcon = document.getElementById('toggleChartsIcon');
            const chartsContainer = document.getElementById('chartsContainer');
            
            toggleChartsBtn.addEventListener('click', function() {
                if (chartsContainer.classList.contains('hidden')) {
                    chartsContainer.classList.remove('hidden');
                    toggleChartsIcon.style.transform = 'rotate(0deg)';
                } else {
                    chartsContainer.classList.add('hidden');
                    toggleChartsIcon.style.transform = 'rotate(180deg)';
                }
            });

            // ========= 初始化所有图表 =========
            // 资金投入结构图
            const fundStructureCtx = document.getElementById('fundStructureChart');
            if (fundStructureCtx) {
                new Chart(fundStructureCtx.getContext('2d'), {
                    type: 'pie',
                    data: {
                        labels: ['财政拨款', '企业自筹', '银行贷款', '其他'],
                        datasets: [{
                            data: [35, 45, 15, 5],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right'
                            }
                        }
                    }
                });
            }

            // 阶段进度对比图
            const phaseProgressCtx = document.getElementById('phaseProgressChart');
            if (phaseProgressCtx) {
                new Chart(phaseProgressCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['立项', '研发', '中试', '产业化'],
                        datasets: [{
                            label: '计划进度',
                            data: [100, 50, 0, 0],
                            backgroundColor: 'rgba(209, 213, 219, 0.8)',
                            borderColor: 'rgba(209, 213, 219, 1)',
                            borderWidth: 1
                        }, {
                            label: '实际进度',
                            data: [100, 45, 0, 0],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 100,
                                title: {
                                    display: true,
                                    text: '完成百分比 (%)'
                                }
                            }
                        }
                    }
                });
            }

            // 投入产出相关性分析
            const inputOutputCtx = document.getElementById('inputOutputChart');
            if (inputOutputCtx) {
                new Chart(inputOutputCtx.getContext('2d'), {
                    type: 'scatter',
                    data: {
                        datasets: [{
                            label: '研发项目',
                            data: [
                                { x: 1200, y: 2500 },
                                { x: 1800, y: 3800 },
                                { x: 2500, y: 5200 },
                                { x: 3200, y: 6800 },
                                { x: 3800, y: 8500 },
                                { x: 4500, y: 9800 },
                                { x: 5200, y: 12500 },
                                { x: 5800, y: 13200 }
                            ],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)'
                        }, {
                            label: '投资项目',
                            data: [
                                { x: 3500, y: 4200 },
                                { x: 4200, y: 5800 },
                                { x: 5800, y: 7500 },
                                { x: 6500, y: 8800 },
                                { x: 7200, y: 10500 },
                                { x: 8500, y: 12800 },
                                { x: 9800, y: 15200 },
                                { x: 12500, y: 18500 }
                            ],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: '项目投入 (万元)'
                                }
                            },
                            y: {
                                title: {
                                    display: true,
                                    text: '项目产出 (万元)'
                                }
                            }
                        }
                    }
                });
            }

            // ========= 为所有弹窗绑定“点击外部关闭”事件 =========
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // ========= 表单提交处理（用于原型） =========
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>