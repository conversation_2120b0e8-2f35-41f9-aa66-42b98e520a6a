<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">研发信息管理</h1>
            <p class="text-gray-600">整合展示机构在研发经费与人员投入情况，支持数据补录、历史追踪与报表导出</p>
        </div>

        <!-- 年度切换区 -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex space-x-2 overflow-x-auto pb-2">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md">2023</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">2022</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">2021</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">2020</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50">2019</button>
                </div>
                <button class="px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    新增年度
                </button>
            </div>
        </div>

        <!-- 研发费用卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- 研发费用合计 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">研发费用合计</h3>
                        <p class="text-sm text-gray-500">年度总投入</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm hover:underline">详情</a>
                </div>
                <div class="mt-4">
                    <p class="text-2xl font-bold text-gray-900">¥12,568,900</p>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-500 mb-1">
                            <span>同比变化</span>
                            <span class="text-green-600">+8.5%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业内部研发费用 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">企业内部研发费用</h3>
                        <p class="text-sm text-gray-500">自主投入</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm hover:underline">详情</a>
                </div>
                <div class="mt-4">
                    <p class="text-2xl font-bold text-gray-900">¥8,245,300</p>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-500 mb-1">
                            <span>占比</span>
                            <span>65.6%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 65.6%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 委托外部研发费用 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">委托外部研发费用</h3>
                        <p class="text-sm text-gray-500">外包合作</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm hover:underline">详情</a>
                </div>
                <div class="mt-4">
                    <p class="text-2xl font-bold text-gray-900">¥3,126,800</p>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-500 mb-1">
                            <span>占比</span>
                            <span>24.9%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 24.9%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 政府资金占比 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">政府资金占比</h3>
                        <p class="text-sm text-gray-500">政策支持</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm hover:underline">详情</a>
                </div>
                <div class="mt-4">
                    <p class="text-2xl font-bold text-gray-900">¥1,196,800</p>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-500 mb-1">
                            <span>占比</span>
                            <span>9.5%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 9.5%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 费用结构趋势区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">研发费用结构趋势</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        PNG
                    </button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        Excel
                    </button>
                </div>
            </div>
            <div class="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <svg class="w-24 h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="mt-4 text-sm text-gray-500">
                注：图表展示2019-2023年研发费用结构变化趋势，蓝色为企业内部研发，黄色为委托外部研发，紫色为政府资金支持
            </div>
        </div>

        <!-- 研发人员统计区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">研发人员统计</h2>
                    <div class="text-sm">
                        <span class="text-gray-700">职工总数：</span>
                        <span class="font-bold text-gray-900">286</span>
                        <span class="text-green-600 ml-2">(+12)</span>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学历/职称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同比变化</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openPersonDetail('bachelor')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">本科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">54.5%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">+5.2%</td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openPersonDetail('master')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">78</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">27.3%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+12.7%</td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openPersonDetail('phd')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">11.2%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+6.7%</td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openPersonDetail('postdoc')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">博士后</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7.0%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">-2.4%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 人员梯度趋势区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">研发人员梯度趋势</h2>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">筛选:</span>
                    <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50">硕士</button>
                    <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50">博士</button>
                    <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">博士后</button>
                    <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">高级职称</button>
                </div>
            </div>
            <div class="h-64 bg-gray-100 rounded-md flex items-center justify-center">
                <svg class="w-24 h-24 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18v4H3V4z"></path>
                </svg>
            </div>
            <div class="mt-4 text-sm text-gray-500">
                注：图表展示2019-2023年研发人员梯度变化趋势，蓝色为硕士，绿色为博士，橙色为博士后，红色为高级职称
            </div>
        </div>

        <!-- 数据质量与预警区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">数据质量与预警</h2>
            <div class="space-y-3">
                <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-red-800">检测到研发费用异常波动</p>
                            <p class="text-xs text-red-700 mt-1">2023年委托外部研发费用同比增加42%，远超行业平均水平</p>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button class="text-sm text-red-600 hover:text-red-800">去修复 →</button>
                    </div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">检测到人员断层风险</p>
                            <p class="text-xs text-yellow-700 mt-1">35-45岁中坚研发人员占比不足20%，存在人才断层风险</p>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button class="text-sm text-yellow-600 hover:text-yellow-800">查看详情 →</button>
                    </div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800">3个数据字段缺失</p>
                            <p class="text-xs text-blue-700 mt-1">科研资产净值、国际合作项目数、专利转化率数据未填写</p>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-end">
                        <button class="text-sm text-blue-600 hover:text-blue-800">去补录 →</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 通用操作区 -->
        <div class="fixed bottom-6 right-6 space-x-3">
            <button class="p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </button>
            <button class="p-3 bg-white text-gray-700 rounded-full shadow-lg hover:bg-gray-100">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
            </button>
            <button class="p-3 bg-white text-gray-700 rounded-full shadow-lg hover:bg-gray-100">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 人员明细弹窗 -->
    <div id="personDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">研发人员明细</h3>
                    <button onclick="closePersonDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2" id="detailTitle">硕士学历研发人员</h4>
                        <p class="text-sm text-gray-500">共78人，平均年龄32.5岁，平均在职时间3.2年</p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入职时间</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目数</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张伟</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能装备研究院</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019-05-12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王芳</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料研发中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-08-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李强</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">数字技术实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术专家</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018-03-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-between items-center">
                        <div class="text-sm text-gray-700">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">78</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closePersonDetail()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出Excel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 人员明细弹窗
        function openPersonDetail(type) {
            const modal = document.getElementById('personDetailModal');
            const title = document.getElementById('detailTitle');
            
            switch(type) {
                case 'bachelor':
                    title.textContent = '本科学历研发人员';
                    break;
                case 'master':
                    title.textContent = '硕士学历研发人员';
                    break;
                case 'phd':
                    title.textContent = '博士学历研发人员';
                    break;
                case 'postdoc':
                    title.textContent = '博士后研究人员';
                    break;
            }
            
            modal.classList.remove('hidden');
        }
        
        function closePersonDetail() {
            document.getElementById('personDetailModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('personDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePersonDetail();
            }
        });
    </script>
</body>
</html>