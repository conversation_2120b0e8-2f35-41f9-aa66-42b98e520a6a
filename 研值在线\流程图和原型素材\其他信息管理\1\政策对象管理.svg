<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策对象管理流程</text>

  <!-- 阶段一：分类录入与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：分类录入与校验</text>
  
  <!-- 节点1: 运维录入 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">运维人员录入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(新增/修改对象分类)</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(编码唯一性、必填字段)</text>
  </g>

  <!-- 节点3: 生成分类记录 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成分类记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(状态：未发布)</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 350 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核发布 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核发布</text>

  <!-- 节点4: 推送管理员 -->
  <g transform="translate(250, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送管理员</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(按配置推送)</text>
  </g>

  <!-- 节点5: 数据管理员审核 -->
  <g transform="translate(550, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(核对无误)</text>
  </g>

  <!-- 节点6: 发布与缓存 -->
  <g transform="translate(850, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">发布与缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(状态：已发布)</text>
  </g>

  <!-- 连接线 3 -> 4 -> 5 -> 6 -->
  <path d="M 850 200 C 850 250, 350 260, 350 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 345 Q 500 345 550 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 345 Q 800 345 850 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：批量导入处理 -->
  <text x="350" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：批量导入处理</text>

  <!-- 节点7: 批量导入 -->
  <g transform="translate(50, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(文件上传)</text>
  </g>

  <!-- 节点8: 文件解析 -->
  <g transform="translate(280, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件解析</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(逐行校验)</text>
  </g>

  <!-- 节点9: 校验通过 -->
  <g transform="translate(510, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">校验通过</text>
    <text x="90" y="45" text-anchor="middle" font-size="11" fill="#555">(进入未发布队列)</text>
  </g>

  <!-- 节点10: 校验失败 -->
  <g transform="translate(510, 570)" filter="url(#soft-shadow)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">校验失败</text>
    <text x="90" y="45" text-anchor="middle" font-size="11" fill="#555">(生成错误报告)</text>
  </g>

  <!-- 连接线 7 -> 8 -->
  <path d="M 230 545 Q 255 545 280 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9, 10 -->
  <path d="M 460 530 C 485 520, 485 490, 510 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 460 560 C 485 570, 485 590, 510 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 标签 -->
  <text x="475" y="505" text-anchor="middle" font-size="11" fill="#555">通过</text>
  <text x="475" y="585" text-anchor="middle" font-size="11" fill="#555">失败</text>

  <!-- 阶段四：依赖检查与停用 -->
  <text x="1050" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：依赖检查与停用</text>

  <!-- 节点11: 停用删除请求 -->
  <g transform="translate(850, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFF3E0" stroke="#FFCC80" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">停用删除</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(用户操作)</text>
  </g>

  <!-- 节点12: 依赖检查 -->
  <g transform="translate(1080, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFF3E0" stroke="#FFCC80" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">依赖检查</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(引用关系检测)</text>
  </g>

  <!-- 节点13: 阻止操作 -->
  <g transform="translate(1080, 620)" filter="url(#soft-shadow)">
    <rect width="180" height="60" rx="8" ry="8" fill="#FFF3E0" stroke="#FFCC80" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">阻止操作</text>
    <text x="90" y="45" text-anchor="middle" font-size="11" fill="#555">(提示处理依赖)</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -->
  <path d="M 1030 545 Q 1055 545 1080 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1170 580 Q 1170 600 1170 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="1190" y="600" text-anchor="start" font-size="11" fill="#555">存在引用</text>

  <!-- 阶段五：归档与审计 -->
  <text x="700" y="750" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：归档与审计</text>

  <!-- 节点14: 定时任务 -->
  <g transform="translate(400, 780)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时任务</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(每日扫描)</text>
  </g>

  <!-- 节点15: 分类归档 -->
  <g transform="translate(630, 780)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分类归档</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(停用与历史版本)</text>
  </g>

  <!-- 节点16: 审计库 -->
  <g transform="translate(860, 780)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(操作日志记录)</text>
  </g>

  <!-- 连接线 14 -> 15 -> 16 -->
  <path d="M 580 815 Q 605 815 630 815" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 815 Q 835 815 860 815" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <path d="M 510 630 C 400 680, 200 700, 140 580" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="300" y="650" text-anchor="middle" font-size="11" fill="#666">下载修正后重新导入</text>

</svg>