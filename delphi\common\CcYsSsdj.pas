unit CcYsSsdj;

interface

uses
  Classes;

type
  TCcYsSsdj = class
  private
    FYsssdjid: Integer;
    FYsid: Integer;

    FSspmid_a: string;
    FSsggid_a: string;
    FSsSjia_a: Double;
    FSsSbi_a: Double;
    FSsSgfei_a: Double;
    FSsSgshao_a: Double;
    FSsRsfei_a: Double;
    FSsRsshao_a: Double;
    FSsBsf_a: Double;
    FSsBssh_a: Double;
    FSsScb_a: Double;

    FSspmid_b: string;
    FSsggid_b: string;
    FSsSjia_b: Double;
    FSsSbi_b: Double;
    FSsSgfei_b: Double;
    FSsSgshao_b: Double;
    FSsRsfei_b: Double;
    FSsRsshao_b: Double;
    FSsBsf_b: Double;
    FSsBssh_b: Double;
    FSsScb_b: Double;

    FSspmid_c: string;
    FSsggid_c: string;
    FSsSjia_c: Double;
    FSsSbi_c: Double;
    FSsSgfei_c: Double;
    FSsSgshao_c: Double;
    FSsRsfei_c: Double;
    FSsRsshao_c: Double;
    FSsBsf_c: Double;
    FSsBssh_c: Double;
    FSsScb_c: Double;

    FSspmid_d: string;
    FSsggid_d: string;
    FSsSjia_d: Double;
    FSsSbi_d: Double;
    FSsSgfei_d: Double;
    FSsSgshao_d: Double;
    FSsRsfei_d: Double;
    FSsRsshao_d: Double;
    FSsBsf_d: Double;
    FSsBssh_d: Double;
    FSsScb_d: Double;

    FSspmid_z: string;
    FSsggid_z: string;
    FSsZScb: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysssdjid: Integer read FYsssdjid write FYsssdjid;
    property Ysid: Integer read FYsid write FYsid;

    property Sspmid_a: string read FSspmid_a write FSspmid_a;
    property Ssggid_a: string read FSsggid_a write FSsggid_a;
    property SsSjia_a: Double read FSsSjia_a write FSsSjia_a;
    property SsSbi_a: Double read FSsSbi_a write FSsSbi_a;
    property SsSgfei_a: Double read FSsSgfei_a write FSsSgfei_a;
    property SsSgshao_a: Double read FSsSgshao_a write FSsSgshao_a;
    property SsRsfei_a: Double read FSsRsfei_a write FSsRsfei_a;
    property SsRsshao_a: Double read FSsRsshao_a write FSsRsshao_a;
    property SsBsf_a: Double read FSsBsf_a write FSsBsf_a;
    property SsBssh_a: Double read FSsBssh_a write FSsBssh_a;
    property SsScb_a: Double read FSsScb_a write FSsScb_a;

    property Sspmid_b: string read FSspmid_b write FSspmid_b;
    property Ssggid_b: string read FSsggid_b write FSsggid_b;
    property SsSjia_b: Double read FSsSjia_b write FSsSjia_b;
    property SsSbi_b: Double read FSsSbi_b write FSsSbi_b;
    property SsSgfei_b: Double read FSsSgfei_b write FSsSgfei_b;
    property SsSgshao_b: Double read FSsSgshao_b write FSsSgshao_b;
    property SsRsfei_b: Double read FSsRsfei_b write FSsRsfei_b;
    property SsRsshao_b: Double read FSsRsshao_b write FSsRsshao_b;
    property SsBsf_b: Double read FSsBsf_b write FSsBsf_b;
    property SsBssh_b: Double read FSsBssh_b write FSsBssh_b;
    property SsScb_b: Double read FSsScb_b write FSsScb_b;

    property Sspmid_c: string read FSspmid_c write FSspmid_c;
    property Ssggid_c: string read FSsggid_c write FSsggid_c;
    property SsSjia_c: Double read FSsSjia_c write FSsSjia_c;
    property SsSbi_c: Double read FSsSbi_c write FSsSbi_c;
    property SsSgfei_c: Double read FSsSgfei_c write FSsSgfei_c;
    property SsSgshao_c: Double read FSsSgshao_c write FSsSgshao_c;
    property SsRsfei_c: Double read FSsRsfei_c write FSsRsfei_c;
    property SsRsshao_c: Double read FSsRsshao_c write FSsRsshao_c;
    property SsBsf_c: Double read FSsBsf_c write FSsBsf_c;
    property SsBssh_c: Double read FSsBssh_c write FSsBssh_c;
    property SsScb_c: Double read FSsScb_c write FSsScb_c;

    property Sspmid_d: string read FSspmid_d write FSspmid_d;
    property Ssggid_d: string read FSsggid_d write FSsggid_d;
    property SsSjia_d: Double read FSsSjia_d write FSsSjia_d;
    property SsSbi_d: Double read FSsSbi_d write FSsSbi_d;
    property SsSgfei_d: Double read FSsSgfei_d write FSsSgfei_d;
    property SsSgshao_d: Double read FSsSgshao_d write FSsSgshao_d;
    property SsRsfei_d: Double read FSsRsfei_d write FSsRsfei_d;
    property SsRsshao_d: Double read FSsRsshao_d write FSsRsshao_d;
    property SsBsf_d: Double read FSsBsf_d write FSsBsf_d;
    property SsBssh_d: Double read FSsBssh_d write FSsBssh_d;
    property SsScb_d: Double read FSsScb_d write FSsScb_d;

    property Sspmid_z: string read FSspmid_z write FSspmid_z;
    property Ssggid_z: string read FSsggid_z write FSsggid_z;
    property SsZScb: Double read FSsZScb write FSsZScb;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
