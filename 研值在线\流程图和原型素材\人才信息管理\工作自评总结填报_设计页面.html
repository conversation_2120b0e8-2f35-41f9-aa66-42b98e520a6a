<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工作自评总结填报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#6366f1'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">工作自评总结填报</h1>
            <p class="text-gray-600 mt-2">科技特派员年度考核与服务绩效自我管理平台，支持自评表单填报、材料上传、总结导出等功能</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧筛选和统计区 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 筛选条件 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        筛选条件
                    </h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">年度</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="2024">2024年度</option>
                                <option value="2023">2023年度</option>
                                <option value="2022">2022年度</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">批次</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">全部批次</option>
                                <option value="1" selected>第一批 (1月-3月)</option>
                                <option value="2">第二批 (4月-6月)</option>
                                <option value="3">第三批 (7月-9月)</option>
                                <option value="4">第四批 (10月-12月)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">特派员类型</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded" checked>
                                    <span class="ml-2 text-sm text-gray-700">个人特派员</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded" checked>
                                    <span class="ml-2 text-sm text-gray-700">法人特派员</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded" checked>
                                    <span class="ml-2 text-sm text-gray-700">团队特派员</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">全部状态</option>
                                <option value="not_started">未填报</option>
                                <option value="in_progress">填报中</option>
                                <option value="submitted">已提交</option>
                                <option value="returned">退回补全</option>
                                <option value="archived">已归档</option>
                            </select>
                        </div>
                        <div class="pt-2">
                            <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                查询
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        填报统计
                    </h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">总任务数</span>
                            <span class="text-lg font-semibold text-gray-900">128</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">已完成</span>
                            <span class="text-lg font-semibold text-green-600">96</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">未完成</span>
                            <span class="text-lg font-semibold text-red-600">32</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">完成率</span>
                            <span class="text-lg font-semibold text-blue-600">75%</span>
                        </div>
                        <div class="mt-4">
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                            <div class="flex justify-between text-xs text-gray-500 mt-1">
                                <span>0%</span>
                                <span>50%</span>
                                <span>100%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧自评任务列表区 -->
            <div class="lg:col-span-3 space-y-6">
                <!-- 操作工具栏 -->
                <div class="bg-white rounded-lg shadow-md p-4 flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">自评任务列表</h2>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <input type="text" placeholder="搜索姓名/单位名" class="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center text-sm">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            批量导出
                        </button>
                    </div>
                </div>

                <!-- 自评任务表格 -->
                <div class="bg-white rounded-lg shadow-md flex flex-col flex-1 overflow-hidden">
                    <div class="overflow-x-auto flex-1">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名/单位名</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">特派员类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属批次</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">截止时间</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">张三</div>
                                        <div class="text-xs text-gray-500">宁波市农业科学研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个人特派员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">填报中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openModal('selfEvaluationModal')" class="text-blue-600 hover:text-blue-900">填报</button>
                                            <button onclick="openModal('viewSummaryModal')" class="text-indigo-600 hover:text-indigo-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">导出</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">李四</div>
                                        <div class="text-xs text-gray-500">宁波市工业技术研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个人特派员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已提交</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openModal('viewSummaryModal')" class="text-indigo-600 hover:text-indigo-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">导出</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波科技发展有限公司</div>
                                        <div class="text-xs text-gray-500">高新技术企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">法人特派员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">退回补全</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openModal('selfEvaluationModal')" class="text-blue-600 hover:text-blue-900">补全</button>
                                            <button onclick="openModal('viewSummaryModal')" class="text-indigo-600 hover:text-indigo-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">导出</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波大学材料科学团队</div>
                                        <div class="text-xs text-gray-500">高校科研团队</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">团队特派员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未填报</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openModal('selfEvaluationModal')" class="text-blue-600 hover:text-blue-900">填报</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">王五</div>
                                        <div class="text-xs text-gray-500">宁波市林业科学研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">个人特派员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openModal('viewSummaryModal')" class="text-indigo-600 hover:text-indigo-900">查看</button>
                                            <button class="text-green-600 hover:text-green-900">导出</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">128</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自评总结填报模态框 -->
    <div id="selfEvaluationModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">工作自评总结填报</h3>
                    <div class="flex items-center space-x-3">
                        <button id="saveDraftBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 text-sm">
                            保存草稿
                        </button>
                        <button onclick="closeModal('selfEvaluationModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">姓名/单位名称 <span class="text-red-500">*</span></label>
                                <input type="text" value="张三" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">特派员类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                                    <option value="individual" selected>个人特派员</option>
                                    <option value="legal">法人特派员</option>
                                    <option value="team">团队特派员</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                                <input type="text" value="宁波市农业科学研究院" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">年度工作目标 <span class="text-red-500">*</span></h4>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入年度工作目标">1. 完成宁波市鄞州区水稻种植技术推广，覆盖面积500亩；
2. 开展农业技术培训5场，培训农户200人次；
3. 引进新品种3个，建立示范基地1个；
4. 解决农业生产技术难题10个。</textarea>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">年度工作完成情况 <span class="text-red-500">*</span></h4>
                        <textarea rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述年度工作完成情况">本年度主要完成以下工作：

1. 水稻种植技术推广：已完成鄞州区650亩水稻种植技术推广，超出计划30%，平均亩产提高15%。
2. 技术培训：共开展农业技术培训8场，培训农户320人次，发放技术资料500余份。
3. 品种引进与示范：引进水稻新品种4个，建立示范基地2个，面积达100亩。
4. 技术难题解决：共解决农业生产技术难题12个，其中3个为重大技术难题。
5. 新增工作：协助建立了区级农业技术服务平台，开展线上技术咨询服务。</textarea>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">自我评价与创新举措</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">主要成绩与亮点</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述主要成绩与亮点">1. 水稻种植技术推广面积超额完成，农民反响良好；
2. 创新性地采用"理论+实操"的培训模式，提高了培训效果；
3. 引进的新品种表现优异，有望在全市范围内推广。</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">创新举措与方法</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述创新举措与方法">1. 开发了基于微信小程序的农业技术服务平台，实现线上线下相结合的服务模式；
2. 建立了"科技特派员+合作社+农户"的联动机制，提高技术推广效率；
3. 采用物联网技术对示范基地进行精准管理，降低生产成本10%。</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">服务绩效与社会效益</h4>
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">服务对象数量</label>
                                    <input type="number" value="120" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <p class="text-xs text-gray-500 mt-1">如：服务企业/农户数量</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">产生经济效益（万元）</label>
                                    <input type="number" value="350" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <p class="text-xs text-gray-500 mt-1">直接或间接产生的经济效益</p>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">社会效益描述</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述产生的社会效益">通过技术推广和培训，提高了当地农民的种植技术水平，增加了农民收入。示范基地的建立为周边地区提供了可复制的经验模式，带动了区域农业发展。引进的新品种具有抗病性强、品质好的特点，有利于提升农产品市场竞争力。</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">问题反思与改进措施</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">存在的主要问题</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述工作中存在的主要问题">1. 基层农技人员不足，技术推广覆盖面有限；
2. 部分农户接受新技术的意愿不强，需要加强引导；
3. 农业生产资料价格上涨，影响了部分农户的投入积极性。</textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">改进措施与建议</label>
                                <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请提出改进措施与建议">1. 建议加强基层农技人员培训，建立技术服务团队；
2. 开展更多的现场观摩和示范活动，提高农户接受新技术的意愿；
3. 争取政府补贴政策，降低农户采用新技术的成本。</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">下年度工作计划</h4>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请制定下年度工作计划">1. 扩大水稻种植技术推广面积至800亩，覆盖鄞州区所有乡镇；
2. 开展农业技术培训10场，培训农户400人次；
3. 引进新品种5个，建立示范基地3个；
4. 完善农业技术服务平台功能，提高线上服务能力；
5. 开展农产品品牌建设指导，提升产品附加值。</textarea>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-md font-medium text-gray-900 mb-2">佐证材料上传 <span class="text-red-500">*</span></h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">工作照片</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                    </svg>
                                    <div class="mt-2">
                                        <label for="photo-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>点击上传或拖拽文件</span>
                                            <input id="photo-upload" name="photo-upload" type="file" class="sr-only" multiple accept="image/*">
                                        </label>
                                        <p class="text-xs text-gray-500 mt-1">支持JPG、PNG格式，最多上传5张</p>
                                    </div>
                                </div>
                                <div class="mt-4 grid grid-cols-5 gap-2">
                                    <div class="relative group">
                                        <img src="https://source.unsplash.com/100x100?agriculture,farm" alt="工作照片" class="w-full h-20 object-cover rounded-md">
                                        <button class="absolute top-1 right-1 bg-white bg-opacity-70 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                            <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="relative group">
                                        <img src="https://source.unsplash.com/100x100?training,farmer" alt="工作照片" class="w-full h-20 object-cover rounded-md">
                                        <button class="absolute top-1 right-1 bg-white bg-opacity-70 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                            <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">工作报告/总结</label>
                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                    <ul class="divide-y divide-gray-200">
                                        <li class="p-3 flex items-center">
                                            <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate">2024年度科技特派员工作报告.docx</p>
                                                <p class="text-xs text-gray-500">2.4 MB · 上传于 2024-03-20</p>
                                            </div>
                                            <button class="text-gray-400 hover:text-red-500">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                                <div class="mt-2">
                                    <label for="report-upload" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        上传文件
                                        <input id="report-upload" name="report-upload" type="file" class="sr-only" accept=".doc,.docx,.pdf,.txt">
                                    </label>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">其他证明材料</label>
                                <div class="border border-gray-200 rounded-md overflow-hidden">
                                    <ul class="divide-y divide-gray-200">
                                        <li class="p-3 flex items-center">
                                            <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate">培训活动签到表.xlsx</p>
                                                <p class="text-xs text-gray-500">1.2 MB · 上传于 2024-03-20</p>
                                            </div>
                                            <button class="text-gray-400 hover:text-red-500">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </li>
                                        <li class="p-3 flex items-center">
                                            <svg class="w-5 h-5 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900 truncate">示范基地建设验收报告.pdf</p>
                                                <p class="text-xs text-gray-500">3.7 MB · 上传于 2024-03-21</p>
                                            </div>
                                            <button class="text-gray-400 hover:text-red-500">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                                <div class="mt-2">
                                    <label for="other-upload" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        上传更多文件
                                        <input id="other-upload" name="other-upload" type="file" class="sr-only" multiple>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeModal('selfEvaluationModal')" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button onclick="submitSelfEvaluation()" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        提交自评
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看自评总结模态框 -->
    <div id="viewSummaryModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">工作自评总结查看</h3>
                    <div class="flex items-center space-x-3">
                        <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出PDF
                        </button>
                        <button onclick="closeModal('viewSummaryModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="mb-8">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-bold text-gray-900">2024年度科技特派员工作自评总结</h3>
                            <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-semibold rounded-full">已提交</span>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                            <div>
                                <p class="text-sm text-gray-500">姓名/单位名称</p>
                                <p class="text-base font-medium text-gray-900">张三</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">特派员类型</p>
                                <p class="text-base font-medium text-gray-900">个人特派员</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">所属单位</p>
                                <p class="text-base font-medium text-gray-900">宁波市农业科学研究院</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">填报日期</p>
                                <p class="text-base font-medium text-gray-900">2024-03-25</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">所属批次</p>
                                <p class="text-base font-medium text-gray-900">第一批</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">提交状态</p>
                                <p class="text-base font-medium text-gray-900">已提交</p>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">一、年度工作目标</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                <li>完成宁波市鄞州区水稻种植技术推广，覆盖面积500亩；</li>
                                <li>开展农业技术培训5场，培训农户200人次；</li>
                                <li>引进新品种3个，建立示范基地1个；</li>
                                <li>解决农业生产技术难题10个。</li>
                            </ul>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">二、年度工作完成情况</h4>
                            <p class="text-gray-700 leading-relaxed mb-4">本年度主要完成以下工作：</p>
                            <ol class="list-decimal list-inside text-gray-700 space-y-2 pl-4">
                                <li>水稻种植技术推广：已完成鄞州区650亩水稻种植技术推广，超出计划30%，平均亩产提高15%。</li>
                                <li>技术培训：共开展农业技术培训8场，培训农户320人次，发放技术资料500余份。</li>
                                <li>品种引进与示范：引进水稻新品种4个，建立示范基地2个，面积达100亩。</li>
                                <li>技术难题解决：共解决农业生产技术难题12个，其中3个为重大技术难题。</li>
                                <li>新增工作：协助建立了区级农业技术服务平台，开展线上技术咨询服务。</li>
                            </ol>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">三、自我评价与创新举措</h4>
                            
                            <div class="mb-4">
                                <h5 class="text-md font-medium text-gray-900 mb-2">主要成绩与亮点</h5>
                                <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                    <li>水稻种植技术推广面积超额完成，农民反响良好；</li>
                                    <li>创新性地采用"理论+实操"的培训模式，提高了培训效果；</li>
                                    <li>引进的新品种表现优异，有望在全市范围内推广。</li>
                                </ul>
                            </div>
                            
                            <div>
                                <h5 class="text-md font-medium text-gray-900 mb-2">创新举措与方法</h5>
                                <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                    <li>开发了基于微信小程序的农业技术服务平台，实现线上线下相结合的服务模式；</li>
                                    <li>建立了"科技特派员+合作社+农户"的联动机制，提高技术推广效率；</li>
                                    <li>采用物联网技术对示范基地进行精准管理，降低生产成本10%。</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">四、服务绩效与社会效益</h4>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-700">服务对象数量</p>
                                    <p class="text-lg font-semibold text-gray-900">120户农户</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700">产生经济效益</p>
                                    <p class="text-lg font-semibold text-gray-900">350万元</p>
                                </div>
                            </div>
                            
                            <div>
                                <h5 class="text-md font-medium text-gray-900 mb-2">社会效益描述</h5>
                                <p class="text-gray-700 leading-relaxed">通过技术推广和培训，提高了当地农民的种植技术水平，增加了农民收入。示范基地的建立为周边地区提供了可复制的经验模式，带动了区域农业发展。引进的新品种具有抗病性强、品质好的特点，有利于提升农产品市场竞争力。</p>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">五、问题反思与改进措施</h4>
                            
                            <div class="mb-4">
                                <h5 class="text-md font-medium text-gray-900 mb-2">存在的主要问题</h5>
                                <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                    <li>基层农技人员不足，技术推广覆盖面有限；</li>
                                    <li>部分农户接受新技术的意愿不强，需要加强引导；</li>
                                    <li>农业生产资料价格上涨，影响了部分农户的投入积极性。</li>
                                </ul>
                            </div>
                            
                            <div>
                                <h5 class="text-md font-medium text-gray-900 mb-2">改进措施与建议</h5>
                                <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                    <li>建议加强基层农技人员培训，建立技术服务团队；</li>
                                    <li>开展更多的现场观摩和示范活动，提高农户接受新技术的意愿；</li>
                                    <li>争取政府补贴政策，降低农户采用新技术的成本。</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6 pb-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">六、下年度工作计划</h4>
                            <ul class="list-disc list-inside text-gray-700 space-y-1 pl-4">
                                <li>扩大水稻种植技术推广面积至800亩，覆盖鄞州区所有乡镇；</li>
                                <li>开展农业技术培训10场，培训农户400人次；</li>
                                <li>引进新品种5个，建立示范基地3个；</li>
                                <li>完善农业技术服务平台功能，提高线上服务能力；</li>
                                <li>开展农产品品牌建设指导，提升产品附加值。</li>
                            </ul>
                        </div>
                        
                        <div class="border-t border-gray-200 pt-6">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">七、佐证材料清单</h4>
                            
                            <div class="mb-6">
                                <h5 class="text-md font-medium text-gray-900 mb-3">工作照片</h5>
                                <div class="grid grid-cols-3 gap-3">
                                    <img src="https://source.unsplash.com/200x150?agriculture,farm" alt="工作照片" class="w-full h-32 object-cover rounded-md">
                                    <img src="https://source.unsplash.com/200x150?training,farmer" alt="工作照片" class="w-full h-32 object-cover rounded-md">
                                </div>
                            </div>
                            
                            <div class="space-y-4">
                                <div>
                                    <h5 class="text-md font-medium text-gray-900 mb-3">工作报告/总结</h5>
                                    <div class="border border-gray-200 rounded-md overflow-hidden">
                                        <ul class="divide-y divide-gray-200">
                                            <li class="p-3 flex items-center">
                                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">2024年度科技特派员工作报告.docx</p>
                                                    <p class="text-xs text-gray-500">2.4 MB · 上传于 2024-03-20</p>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-900">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                                    </svg>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div>
                                    <h5 class="text-md font-medium text-gray-900 mb-3">其他证明材料</h5>
                                    <div class="border border-gray-200 rounded-md overflow-hidden">
                                        <ul class="divide-y divide-gray-200">
                                            <li class="p-3 flex items-center">
                                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">培训活动签到表.xlsx</p>
                                                    <p class="text-xs text-gray-500">1.2 MB · 上传于 2024-03-20</p>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-900">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                                    </svg>
                                                </button>
                                            </li>
                                            <li class="p-3 flex items-center">
                                                <svg class="w-5 h-5 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 truncate">示范基地建设验收报告.pdf</p>
                                                    <p class="text-xs text-gray-500">3.7 MB · 上传于 2024-03-21</p>
                                                </div>
                                                <button class="text-blue-600 hover:text-blue-900">
                                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                                    </svg>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeModal('viewSummaryModal')" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 提交自评总结
        function submitSelfEvaluation() {
            if (confirm('确认提交自评总结？提交后将无法修改。')) {
                alert('自评总结提交成功！');
                closeModal('selfEvaluationModal');
                // 这里可以添加页面刷新或状态更新逻辑
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 保存草稿按钮事件
            document.getElementById('saveDraftBtn').addEventListener('click', function() {
                alert('草稿已保存！');
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 文件上传处理
            const fileInputs = document.querySelectorAll('input[type="file"]');
            fileInputs.forEach(input => {
                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        // 这里可以添加文件上传逻辑
                        console.log('文件已选择:', e.target.files);
                        // 简单模拟上传成功
                        alert('文件上传成功！');
                    }
                });
            });
        });
    </script>
</body>
</html>