'use client'

import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Badge } from "@/components/ui/badge"
import { Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { 
  LineChart, 
  BarChart, 
  PieChart, 
  Circle,
  AreaChart,
  CircleOff,
  GitBranch,
  Grid,
  LayoutGrid,
  Sun,
  Gauge,
  Table2,
  Activity,
  Map,
  Network,
  BoxSelect,
  CircleDot,
  Droplets,
  CheckSquare
} from "lucide-react"

const chartComponents = {
  basic: [
    { id: 'line', name: '折线图', description: '展示趋势变化', tag: '基础', icon: LineChart, count: 12 },
    { id: 'bar', name: '柱状图', description: '对比数据差异', tag: '基础', icon: BarChart, count: 8 },
    { id: 'pie', name: '饼图', description: '占比分析', tag: '基础', icon: <PERSON><PERSON><PERSON>, count: 6 },
    { id: 'scatter', name: '散点图', description: '数据分布', tag: '基础', icon: Circle, count: 4 },
    { id: 'area', name: '面积图', description: '趋势占比', tag: '基础', icon: AreaChart, count: 5 },
  ],
  advanced: [
    { id: 'radar', name: '雷达图', description: '多维度分析', tag: '高级', icon: CircleOff, count: 7 },
    { id: 'funnel', name: '漏斗图', description: '转化分析', tag: '高级', icon: GitBranch, count: 5 },
    { id: 'heatmap', name: '热力图', description: '密度分布', tag: '高级', icon: Grid, count: 3 },
    { id: 'treemap', name: '矩形树图', description: '层级占比', tag: '高级', icon: LayoutGrid, count: 4 },
    { id: 'sunburst', name: '旭日图', description: '层级关系', tag: '高级', icon: Sun, count: 2 },
  ],
  special: [
    { id: 'gauge', name: '仪表盘', description: '进度展示', tag: '特殊', icon: Gauge, count: 3 },
    { id: 'wordcloud', name: '词云图', description: '文本分析', tag: '特殊', icon: Activity, count: 4 },
    { id: 'sankey', name: '桑基图', description: '流向分析', tag: '特殊', icon: GitBranch, count: 2 },
    { id: 'graph', name: '关系图', description: '网络关系', tag: '特殊', icon: Network, count: 3 },
    { id: 'boxplot', name: '箱线图', description: '数据分布', tag: '特殊', icon: BoxSelect, count: 2 },
  ],
  table: [
    { id: 'basic-table', name: '基础表格', description: '数据明细', tag: '表格', icon: Table2, count: 5 },
    { id: 'pivot-table', name: '透视表', description: '多维分析', tag: '表格', icon: Table2, count: 3 },
    { id: 'tree-table', name: '树形表格', description: '层级数据', tag: '表格', icon: Table2, count: 4 },
  ],
  indicator: [
    { id: 'number', name: '数字指标', description: '单值展示', tag: '指标', icon: Activity, count: 6 },
    { id: 'ring-progress', name: '环形进度', description: '进度展示', tag: '指标', icon: CircleDot, count: 4 },
    { id: 'liquid', name: '水波图', description: '进度展示', tag: '指标', icon: Droplets, count: 3 },
    { id: 'status', name: '状态卡片', description: '状态展示', tag: '指标', icon: CheckSquare, count: 5 },
  ],
  map: [
    { id: 'china-map', name: '中国地图', description: '地理分布', tag: '地图', icon: Map, count: 2 },
    { id: 'world-map', name: '世界地图', description: '地理分布', tag: '地图', icon: Map, count: 3 },
    { id: 'province-map', name: '省份地图', description: '地理分布', tag: '地图', icon: Map, count: 4 },
    { id: 'geo-3d', name: '3D地图', description: '立体展示', tag: '地图', icon: Map, count: 1 },
  ]
}

export function VisualizationConfig() {
  return (
    <div className="space-y-4">
      <div className="p-4 pt-0">
        <div className="relative">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="搜索组件..." className="pl-8" />
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-250px)]">
        <Accordion type="multiple" className="w-full">
          {Object.entries(chartComponents).map(([key, charts]) => (
            <AccordionItem key={key} value={key} className="border-none">
              <AccordionTrigger className="px-4 hover:no-underline">
                <div className="flex items-center gap-2">
                  <span className="text-blue-600 font-bold text-lg">{getCategoryName(key)}</span>
                  <Badge variant="secondary" className="ml-2 font-normal">
                    {charts.length}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="grid grid-cols-2 gap-3 p-3">
                  {charts.map((chart) => (
                    <ChartCard key={chart.id} {...chart} />
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </ScrollArea>
    </div>
  )
}

// 获取分类名称
function getCategoryName(key: string) {
  switch (key) {
    case 'basic':
      return '基础图表'
    case 'advanced':
      return '高级图表'
    case 'special':
      return '特殊图表'
    case 'table':
      return '表格'
    case 'indicator':
      return '指标'
    case 'map':
      return '地图'
    default:
      return '未知分类'
  }
}

// 图表卡片组件
function ChartCard({ name, description, tag, icon: Icon, count }: { 
  name: string
  description: string
  tag: string
  icon: any
  count: number 
}) {
  if (!Icon) {
    console.warn(`Icon not found for ${name}`)
    Icon = Activity
  }

  return (
    <div
      className="rounded-lg border border-gray-100 bg-white p-3 hover:border-blue-100 hover:bg-blue-50 hover:shadow-sm cursor-move transition-all group"
      draggable
    >
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-blue-50 text-blue-500 group-hover:bg-blue-100">
              <Icon className="h-4 w-4" />
            </div>
            <span className="font-medium text-gray-700">{name}</span>
          </div>
          <Badge variant="secondary" className="text-xs bg-gray-50 text-gray-500">
            {count}
          </Badge>
        </div>
        <p className="text-xs text-gray-500">{description}</p>
      </div>
    </div>
  )
} 