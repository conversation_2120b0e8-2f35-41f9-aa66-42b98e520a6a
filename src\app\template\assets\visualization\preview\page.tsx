'use client'

import { useRouter } from 'next/navigation'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { VisualizationLayout } from "@/components/assets/visualization-layout"

export default function PreviewPage() {
  const router = useRouter()

  return (
    <div className="h-screen w-screen bg-gray-100 flex flex-col">
      <div className="flex items-center justify-between p-4 bg-white shadow-md">
        <Button 
          variant="outline" 
          onClick={() => router.back()} 
          className="text-blue-500 hover:bg-blue-50"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回编辑
        </Button>
        <h1 className="text-xl font-medium text-gray-800">预览模式</h1>
        <div className="w-16"></div>
      </div>
      <div className="flex-1 overflow-auto flex justify-center items-start p-8">
        <div className="max-w-[1400px] w-full bg-white shadow-lg rounded-lg overflow-hidden">
          <VisualizationLayout isPreview={true} />
        </div>
      </div>
    </div>
  )
} 