<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>对外投资详情 - 企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="min-h-screen p-6">
        <!-- 导航栏 -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center text-blue-600 hover:text-blue-800 mr-4">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-1"></i>
                        返回企业概览
                    </a>
                    <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                        <i data-lucide="target" class="mr-3 h-6 w-6 text-blue-600"></i>
                        对外投资详情
                    </h1>
                </div>
                <div class="flex gap-2">
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="filter" class="w-4 h-4"></i>
                        筛选
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        导出数据
                    </button>
                </div>
            </div>
            <p class="text-gray-600 mt-2">宁波创新科技股份有限公司 - 投资组合分析</p>
        </div>

        <!-- 投资概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">投资企业数</h3>
                    <i data-lucide="building-2" class="w-5 h-5 text-blue-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">12</p>
                <p class="text-sm text-green-600 mt-1">较去年 +3家</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">投资总额</h3>
                    <i data-lucide="trending-up" class="w-5 h-5 text-green-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">3.2亿</p>
                <p class="text-sm text-blue-600 mt-1">人民币</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">控股企业</h3>
                    <i data-lucide="crown" class="w-5 h-5 text-yellow-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">5</p>
                <p class="text-sm text-yellow-600 mt-1">持股>50%</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">地区分布</h3>
                    <i data-lucide="map-pin" class="w-5 h-5 text-purple-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">6</p>
                <p class="text-sm text-purple-600 mt-1">个省市</p>
            </div>
        </div>

        <!-- 投资分析图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 投资趋势分析 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="bar-chart-3" class="mr-2 h-5 w-5 text-blue-600"></i>
                    投资趋势分析
                </h2>
                <div class="h-64">
                    <canvas id="investmentTrendChart"></canvas>
                </div>
            </div>

            <!-- 行业分布 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="pie-chart" class="mr-2 h-5 w-5 text-blue-600"></i>
                    行业分布
                </h2>
                <div class="h-64">
                    <canvas id="industryChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 地区分布地图 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6 mb-6">
            <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                <i data-lucide="map" class="mr-2 h-5 w-5 text-blue-600"></i>
                投资地区分布
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="bg-gray-100 rounded-lg p-6 h-80 flex items-center justify-center">
                        <img src="https://images.unsplash.com/photo-1569336415962-a4bd9f69cd83?w=600&h=400&fit=crop" 
                             alt="中国地图" class="max-w-full max-h-full object-contain rounded">
                    </div>
                </div>
                
                <div class="space-y-3">
                    <h3 class="font-medium text-gray-700 mb-3">地区投资统计</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm text-gray-700">浙江省</span>
                            <span class="font-semibold text-blue-600">6家</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm text-gray-700">上海市</span>
                            <span class="font-semibold text-green-600">2家</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-yellow-50 rounded">
                            <span class="text-sm text-gray-700">江苏省</span>
                            <span class="font-semibold text-yellow-600">2家</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                            <span class="text-sm text-gray-700">广东省</span>
                            <span class="font-semibold text-purple-600">1家</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-red-50 rounded">
                            <span class="text-sm text-gray-700">北京市</span>
                            <span class="font-semibold text-red-600">1家</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 投资企业详细列表 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-blue-800 flex items-center">
                        <i data-lucide="list" class="mr-2 h-5 w-5 text-blue-600"></i>
                        投资企业列表
                    </h2>
                    <div class="flex gap-2">
                        <input type="text" placeholder="搜索企业..." class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部行业</option>
                            <option>人工智能</option>
                            <option>大数据</option>
                            <option>云计算</option>
                            <option>物联网</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部地区</option>
                            <option>浙江省</option>
                            <option>上海市</option>
                            <option>江苏省</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">企业名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在地区</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">持股比例</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <i data-lucide="brain" class="w-5 h-5 text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">杭州智能科技有限公司</div>
                                        <div class="text-sm text-gray-500">91330100XXX</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">杭州市</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8,000万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">80.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 80%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">人工智能</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常经营</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">业务分析</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                            <i data-lucide="database" class="w-5 h-5 text-green-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">上海数据科技有限公司</div>
                                        <div class="text-sm text-gray-500">91310000XXX</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">上海市</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5,000万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">25.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 25%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">大数据</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2021-09-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常经营</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">业务分析</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                            <i data-lucide="cloud" class="w-5 h-5 text-purple-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">苏州云计算有限公司</div>
                                        <div class="text-sm text-gray-500">91320500XXX</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">苏州市</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3,500万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">60.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">云计算</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常经营</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">业务分析</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                                            <i data-lucide="wifi" class="w-5 h-5 text-yellow-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">深圳物联网技术公司</div>
                                        <div class="text-sm text-gray-500">91440300XXX</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">深圳市</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4,200万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">35.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 35%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">物联网</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022-11-05</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常经营</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">业务分析</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">显示第 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">12</span> 条记录</p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            </button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">1</button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 投资趋势图表
        const trendCtx = document.getElementById('investmentTrendChart').getContext('2d');
        new Chart(trendCtx, {
            type: 'bar',
            data: {
                labels: ['2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: '投资金额（万元）',
                    data: [5000, 8000, 12000, 15000, 18000, 8000],
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1
                }, {
                    label: '投资企业数',
                    data: [2, 3, 4, 3, 2, 1],
                    type: 'line',
                    borderColor: 'rgba(16, 185, 129, 1)',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // 行业分布图表
        const industryCtx = document.getElementById('industryChart').getContext('2d');
        new Chart(industryCtx, {
            type: 'doughnut',
            data: {
                labels: ['人工智能', '大数据', '云计算', '物联网', '区块链', '其他'],
                datasets: [{
                    data: [25, 20, 18, 15, 12, 10],
                    backgroundColor: [
                        '#3B82F6',
                        '#10B981',
                        '#8B5CF6',
                        '#F59E0B',
                        '#EF4444',
                        '#6B7280'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 