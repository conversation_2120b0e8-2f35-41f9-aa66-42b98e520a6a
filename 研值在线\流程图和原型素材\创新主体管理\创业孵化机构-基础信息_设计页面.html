<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业孵化机构基础信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">创业孵化机构基础信息</h1>
            <p class="mt-2 text-gray-600">多类型创新孵化载体统一管理平台</p>
        </div>

        <!-- 概览卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">载体总数量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">156</p>
                    </div>
                    <button class="text-xs text-blue-600 hover:text-blue-800">查看列表</button>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                    <span>同比 +12.5%</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">国家级资质</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">28</p>
                    </div>
                    <button class="text-xs text-blue-600 hover:text-blue-800">查看列表</button>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                    <span>同比 +8.3%</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">省市级资质</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">76</p>
                    </div>
                    <button class="text-xs text-blue-600 hover:text-blue-800">查看列表</button>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                    <span>同比 +15.2%</span>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">在孵企业总数</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">2,345</p>
                    </div>
                    <button class="text-xs text-blue-600 hover:text-blue-800">查看列表</button>
                </div>
                <div class="mt-4 flex items-center text-sm text-green-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                    </svg>
                    <span>同比 +18.7%</span>
                </div>
            </div>
        </div>

        <!-- 载体详情区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">宁波市科技创新孵化中心</h2>
                        <span class="inline-block mt-1 px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full">国家级</span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                        </svg>
                        官网访问
                    </button>
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        地图导航
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-500">载体类别</p>
                            <p class="text-gray-900 font-medium mt-1">科技企业孵化器</p>
                        </div>
                        <div>
                            <p class="text-gray-500">运营单位</p>
                            <p class="text-gray-900 font-medium mt-1">宁波市科技发展集团</p>
                        </div>
                        <div>
                            <p class="text-gray-500">成立时间</p>
                            <p class="text-gray-900 font-medium mt-1">2015年6月</p>
                        </div>
                        <div>
                            <p class="text-gray-500">建筑面积</p>
                            <p class="text-gray-900 font-medium mt-1">28,500㎡</p>
                        </div>
                        <div>
                            <p class="text-gray-500">可用工位数</p>
                            <p class="text-gray-900 font-medium mt-1">1,200个</p>
                        </div>
                        <div>
                            <p class="text-gray-500">所在园区</p>
                            <p class="text-gray-900 font-medium mt-1">宁波高新区科技园</p>
                        </div>
                        <div>
                            <p class="text-gray-500">联系人</p>
                            <p class="text-gray-900 font-medium mt-1">张主任</p>
                        </div>
                        <div>
                            <p class="text-gray-500">联系电话</p>
                            <p class="text-gray-900 font-medium mt-1">0574-88888888</p>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">服务与资源</h3>
                    <div class="flex flex-wrap gap-3">
                        <div class="group relative">
                            <div class="px-3 py-2 bg-blue-50 text-blue-800 rounded-full flex items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                                <span class="text-sm">导师库 (86人)</span>
                            </div>
                            <div class="absolute z-10 hidden group-hover:block w-64 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                                <p class="text-sm text-gray-700">涵盖技术、管理、金融等领域专家</p>
                                <a href="#" class="mt-2 text-xs text-blue-600 hover:text-blue-800 block">查看详情 & 预约</a>
                            </div>
                        </div>
                        
                        <div class="group relative">
                            <div class="px-3 py-2 bg-green-50 text-green-800 rounded-full flex items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span class="text-sm">投资机构 (32家)</span>
                            </div>
                            <div class="absolute z-10 hidden group-hover:block w-64 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                                <p class="text-sm text-gray-700">与多家风投、天使投资机构建立合作关系</p>
                                <a href="#" class="mt-2 text-xs text-blue-600 hover:text-blue-800 block">查看详情 & 预约</a>
                            </div>
                        </div>
                        
                        <div class="group relative">
                            <div class="px-3 py-2 bg-purple-50 text-purple-800 rounded-full flex items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                </svg>
                                <span class="text-sm">专业实验室 (12间)</span>
                            </div>
                            <div class="absolute z-10 hidden group-hover:block w-64 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                                <p class="text-sm text-gray-700">生物医药、电子信息等专业实验室</p>
                                <a href="#" class="mt-2 text-xs text-blue-600 hover:text-blue-800 block">查看详情 & 预约</a>
                            </div>
                        </div>
                        
                        <div class="group relative">
                            <div class="px-3 py-2 bg-yellow-50 text-yellow-800 rounded-full flex items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                                </svg>
                                <span class="text-sm">公共会议室 (8间)</span>
                            </div>
                            <div class="absolute z-10 hidden group-hover:block w-64 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                                <p class="text-sm text-gray-700">不同规模的会议室，配备多媒体设备</p>
                                <a href="#" class="mt-2 text-xs text-blue-600 hover:text-blue-800 block">查看详情 & 预约</a>
                            </div>
                        </div>
                        
                        <div class="group relative">
                            <div class="px-3 py-2 bg-red-50 text-red-800 rounded-full flex items-center cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                                </svg>
                                <span class="text-sm">路演大厅 (2个)</span>
                            </div>
                            <div class="absolute z-10 hidden group-hover:block w-64 p-3 bg-white shadow-lg rounded-md border border-gray-200">
                                <p class="text-sm text-gray-700">专业路演场地，可容纳100-300人</p>
                                <a href="#" class="mt-2 text-xs text-blue-600 hover:text-blue-800 block">查看详情 & 预约</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold text-gray-900 mb-4">运营成效</h3>
            <div class="space-y-4">
                <div class="p-4 bg-gray-50 rounded-lg cursor-pointer" onclick="openModal('incubatingModal')">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-500">近三年在孵企业数量变化</p>
                            <div class="flex items-center mt-1">
                                <div class="w-16 h-10">
                                    <canvas id="incubatingChart"></canvas>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-gray-700">
                                        <span class="font-medium">2021年: 156家</span> → 
                                        <span class="font-medium">2022年: 198家</span> (+26.9%) → 
                                        <span class="font-medium">2023年: 245家</span> (+23.7%)
                                    </p>
                                </div>
                            </div>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
                
                <div class="p-4 bg-gray-50 rounded-lg cursor-pointer" onclick="openModal('graduatedModal')">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-500">近三年毕业企业数量变化</p>
                            <div class="flex items-center mt-1">
                                <div class="w-16 h-10">
                                    <canvas id="graduatedChart"></canvas>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm text-gray-700">
                                        <span class="font-medium">2021年: 32家</span> → 
                                        <span class="font-medium">2022年: 45家</span> (+40.6%) → 
                                        <span class="font-medium">2023年: 58家</span> (+28.9%)
                                    </p>
                                </div>
                            </div>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据网格区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 md:mb-0">载体清单</h3>
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3 w-full md:w-auto">
                        <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>全部类型</option>
                            <option>科技企业孵化器</option>
                            <option>众创空间</option>
                            <option>星创天地</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>全部地区</option>
                            <option>海曙区</option>
                            <option>江北区</option>
                            <option>鄞州区</option>
                            <option>高新区</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>全部资质</option>
                            <option>国家级</option>
                            <option>省级</option>
                            <option>市级</option>
                        </select>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出Excel
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">载体名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">载体类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资质等级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在地区</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">运营单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建筑面积(㎡)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在孵企业数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成立时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市科技创新孵化中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技企业孵化器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技发展集团</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28,500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">245</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2015-06</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="openDetailModal()">查看</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波鄞州创客空间</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">众创空间</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄞州区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄞州科技投资公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">186</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2016-03</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="openDetailModal()">查看</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波江北星创天地</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">星创天地</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">市级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">江北区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">江北农业科技公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8,500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">92</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2017-09</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="openDetailModal()">查看</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波海曙创业园</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技企业孵化器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">海曙区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">海曙区科技局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18,200</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2014-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="openDetailModal()">查看</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波镇海新材料孵化器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技企业孵化器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">镇海区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">镇海新材料研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22,300</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">178</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2016-11</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="openDetailModal()">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 156 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 在孵企业数量弹窗 -->
    <div id="incubatingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">在孵企业数量统计</h3>
                <button onclick="closeModal('incubatingModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="h-80 mb-4">
                <canvas id="incubatingDetailChart"></canvas>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年份</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在孵企业数</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同比增长</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">156家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">198家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+26.9%</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">245家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+23.7%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 毕业企业数量弹窗 -->
    <div id="graduatedModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">毕业企业数量统计</h3>
                <button onclick="closeModal('graduatedModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="h-80 mb-4">
                <canvas id="graduatedDetailChart"></canvas>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年份</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">毕业企业数</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同比增长</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">32家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+40.6%</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">58家</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+28.9%</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 载体详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">载体详情</h3>
                <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <p class="text-gray-500">载体名称</p>
                            <p class="text-gray-900 font-medium mt-1">宁波市科技创新孵化中心</p>
                        </div>
                        <div>
                            <p class="text-gray-500">载体类型</p>
                            <p class="text-gray-900 font-medium mt-1">科技企业孵化器</p>
                        </div>
                        <div>
                            <p class="text-gray-500">资质等级</p>
                            <p class="text-gray-900 font-medium mt-1">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">国家级</span>
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-500">运营单位</p>
                            <p class="text-gray-900 font-medium mt-1">宁波市科技发展集团</p>
                        </div>
                        <div>
                            <p class="text-gray-500">成立时间</p>
                            <p class="text-gray-900 font-medium mt-1">2015年6月</p>
                        </div>
                        <div>
                            <p class="text-gray-500">建筑面积</p>
                            <p class="text-gray-900 font-medium mt-1">28,500㎡</p>
                        </div>
                        <div>
                            <p class="text-gray-500">可用工位数</p>
                            <p class="text-gray-900 font-medium mt-1">1,200个</p>
                        </div>
                        <div>
                            <p class="text-gray-500">所在园区</p>
                            <p class="text-gray-900 font-medium mt-1">宁波高新区科技园</p>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4">运营成效</h4>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-500">在孵企业数量</p>
                            <p class="text-lg font-bold text-gray-900 mt-1">245家</p>
                            <div class="mt-2 h-20">
                                <canvas id="detailIncubatingChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">毕业企业数量</p>
                            <p class="text-lg font-bold text-gray-900 mt-1">58家</p>
                            <div class="mt-2 h-20">
                                <canvas id="detailGraduatedChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4">服务与资源</h4>
                <div class="flex flex-wrap gap-3">
                    <span class="px-3 py-2 bg-blue-50 text-blue-800 rounded-full text-sm">导师库 (86人)</span>
                    <span class="px-3 py-2 bg-green-50 text-green-800 rounded-full text-sm">投资机构 (32家)</span>
                    <span class="px-3 py-2 bg-purple-50 text-purple-800 rounded-full text-sm">专业实验室 (12间)</span>
                    <span class="px-3 py-2 bg-yellow-50 text-yellow-800 rounded-full text-sm">公共会议室 (8间)</span>
                    <span class="px-3 py-2 bg-red-50 text-red-800 rounded-full text-sm">路演大厅 (2个)</span>
                </div>
            </div>
            <div class="mt-6 flex justify-end">
                <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 mr-3">关闭</button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">编辑信息</button>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function openDetailModal() {
            openModal('detailModal');
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化小型在孵企业图表
            const incubatingCtx = document.getElementById('incubatingChart').getContext('2d');
            new Chart(incubatingCtx, {
                type: 'line',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        data: [156, 198, 245],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#3B82F6',
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            min: 150
                        }
                    }
                }
            });

            // 初始化小型毕业企业图表
            const graduatedCtx = document.getElementById('graduatedChart').getContext('2d');
            new Chart(graduatedCtx, {
                type: 'line',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        data: [32, 45, 58],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#10B981',
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            display: false
                        },
                        y: {
                            display: false,
                            min: 30
                        }
                    }
                }
            });

            // 初始化详情页在孵企业图表
            const detailIncubatingCtx = document.getElementById('detailIncubatingChart').getContext('2d');
            new Chart(detailIncubatingCtx, {
                type: 'line',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        data: [156, 198, 245],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#3B82F6',
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            min: 150,
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 初始化详情页毕业企业图表
            const detailGraduatedCtx = document.getElementById('detailGraduatedChart').getContext('2d');
            new Chart(detailGraduatedCtx, {
                type: 'line',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        data: [32, 45, 58],
                        borderColor: '#10B981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#10B981',
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            min: 30,
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 初始化详细在孵企业图表
            const incubatingDetailCtx = document.getElementById('incubatingDetailChart').getContext('2d');
            new Chart(incubatingDetailCtx, {
                type: 'bar',
                data: {
                    labels: ['2021年', '2022年', '2023年'],
                    datasets: [{
                        label: '在孵企业数量',
                        data: [156, 198, 245],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(59, 130, 246, 0.7)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(59, 130, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 150
                        }
                    }
                }
            });

            // 初始化详细毕业企业图表
            const graduatedDetailCtx = document.getElementById('graduatedDetailChart').getContext('2d');
            new Chart(graduatedDetailCtx, {
                type: 'bar',
                data: {
                    labels: ['2021年', '2022年', '2023年'],
                    datasets: [{
                        label: '毕业企业数量',
                        data: [32, 45, 58],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(16, 185, 129, 0.7)'
                        ],
                        borderColor: [
                            'rgba(16, 185, 129, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 30
                        }
                    }
                }
            });

            // 绑定弹窗外部点击关闭事件
            document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(this.id);
                    }
                });
            });

            // 绑定ESC键关闭事件
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                        if (!modal.classList.contains('hidden')) {
                            closeModal(modal.id);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>