unit ErrorInfoNoticeFrm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, RzButton, ExtCtrls, RzPanel, StdCtrls, RzLabel, pngimage,
  PngFunctions, HtImage, AdvGlowButton;

type
  TErrorInfoNoticeForm = class(TForm)
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    RzPanel3: TRzPanel;
    Image1: TImage;
    L_Info1: TRzLabel;
    L_Info3: TRzLabel;
    Btn_Save: TAdvGlowButton;
    L_Info4: TRzLabel;
    procedure FormShow(Sender: TObject);
    procedure Btn_SaveClick(Sender: TObject);
    procedure HTImage2Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure Init(infoTitle, infoErrorDes, infoDetail, infoDetail1: string);
  end;

var
  ErrorInfoNoticeForm: TErrorInfoNoticeForm;
  FInfoTitle, FInfoErrorDes, FInfoDetail, FInfoDetail1: string;

implementation

{$R *.dfm}

procedure TErrorInfoNoticeForm.Init(infoTitle, infoErrorDes, infoDetail,
  infoDetail1: string);
begin
  FInfoTitle := infoTitle;
  FInfoErrorDes := infoErrorDes;
  FInfoDetail := infoDetail;
  FInfoDetail1 := infoDetail1;
end;

procedure TErrorInfoNoticeForm.Btn_SaveClick(Sender: TObject);
begin
  self.Close;
end;

procedure TErrorInfoNoticeForm.FormShow(Sender: TObject);
begin
  L_Info1.Caption := FInfoTitle;
  L_Info3.Caption := FInfoDetail;
  L_Info4.Caption := FInfoDetail1;
end;

procedure TErrorInfoNoticeForm.HTImage2Click(Sender: TObject);
begin
  self.Close;
end;

end.
