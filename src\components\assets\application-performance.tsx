'use client'

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false })

export function ApplicationPerformance() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['响应时间', '错误率'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00']
    },
    yAxis: [
      {
        type: 'value',
        name: '响应时间(ms)',
        position: 'left'
      },
      {
        type: 'value',
        name: '错误率(%)',
        position: 'right',
        max: 1
      }
    ],
    series: [
      {
        name: '响应时间',
        type: 'line',
        data: [120, 110, 100, 150, 180, 170, 140, 130],
        itemStyle: { color: '#2563EB' }
      },
      {
        name: '错误率',
        type: 'line',
        yAxisIndex: 1,
        data: [0.2, 0.1, 0.1, 0.3, 0.4, 0.3, 0.2, 0.2],
        itemStyle: { color: '#F59E0B' }
      }
    ]
  }

  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="border-b border-[#E5E9EF]">
        <CardTitle className="text-lg font-medium">应用性能监控</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px]">
          {mounted && (
            <ReactECharts option={option} style={{ height: '100%' }} />
          )}
        </div>
      </CardContent>
    </Card>
  )
} 