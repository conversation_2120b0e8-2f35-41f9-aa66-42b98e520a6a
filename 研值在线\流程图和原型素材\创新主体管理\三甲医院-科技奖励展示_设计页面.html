<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-科技奖励展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">科技奖励展示</h1>
            <p class="mt-2 text-gray-600">聚合国家级、省级、市级科技奖项及专利奖，形成统一荣誉档案</p>
        </div>

        <!-- 荣誉概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">国家级奖项</p>
                        <p class="text-3xl font-bold text-gray-900">28</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="nationalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">省级奖项</p>
                        <p class="text-3xl font-bold text-gray-900">56</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="provincialChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">市级奖项</p>
                        <p class="text-3xl font-bold text-gray-900">89</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="municipalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">专利奖项</p>
                        <p class="text-3xl font-bold text-gray-900">42</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="patentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖项级别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">国家级</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">省级</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">市级</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="medical">医学</option>
                        <option value="biology">生物技术</option>
                        <option value="pharmacy">药学</option>
                        <option value="materials">新材料</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">获奖年度</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" min="2010" max="2024" class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="起始">
                        <span class="text-gray-500">至</span>
                        <input type="number" min="2010" max="2024" class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="结束">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">授奖单位</label>
                    <input type="text" placeholder="输入关键字" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="patentOnly" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                    <label for="patentOnly" class="ml-2 text-sm text-gray-700">仅显示专利奖</label>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 奖项列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900">奖项列表</h2>
                            <div class="flex space-x-3">
                                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    导出 Excel
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年度</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授奖单位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家科技进步奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医学</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新型肿瘤靶向治疗技术研究</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家科学技术奖励办公室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('1')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省科学技术奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">基因编辑技术在遗传病治疗中的应用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省科技厅</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('2')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市科技进步奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">药学</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新型抗肿瘤药物研发</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('3')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">中国专利金奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一种新型医用生物材料及其制备方法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家知识产权局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('4')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省专利金奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医学</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一种新型心血管支架及其制备方法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省知识产权局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('5')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 215 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据分析图表区 -->
            <div class="w-full lg:w-1/3 space-y-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">年度获奖趋势</h3>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">技术领域分布</h3>
                    <div class="h-64">
                        <canvas id="fieldDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 证书预览抽屉 -->
    <div id="certificateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 w-5/6 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">证书预览</h3>
                <button onclick="closeCertificateModal()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                        <img src="https://source.unsplash.com/800x600/?certificate" alt="证书预览" class="max-h-96 rounded-lg">
                    </div>
                </div>
                <div>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">证书信息</h4>
                        <div class="space-y-4 text-sm">
                            <div>
                                <p class="text-gray-500">奖项名称</p>
                                <p class="font-medium text-gray-900">国家科技进步奖</p>
                            </div>
                            <div>
                                <p class="text-gray-500">获奖项目</p>
                                <p class="font-medium text-gray-900">新型肿瘤靶向治疗技术研究</p>
                            </div>
                            <div>
                                <p class="text-gray-500">授奖单位</p>
                                <p class="font-medium text-gray-900">国家科学技术奖励办公室</p>
                            </div>
                            <div>
                                <p class="text-gray-500">获奖年度</p>
                                <p class="font-medium text-gray-900">2023年</p>
                            </div>
                            <div>
                                <p class="text-gray-500">获奖人员</p>
                                <p class="font-medium text-gray-900">张教授、李研究员、王博士等</p>
                            </div>
                            <div>
                                <p class="text-gray-500">证书编号</p>
                                <p class="font-medium text-gray-900">GJ-2023-001</p>
                            </div>
                        </div>
                        <div class="mt-6 space-y-3">
                            <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                下载 PDF
                            </button>
                            <button class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                                </svg>
                                打印
                            </button>
                            <button onclick="closeCertificateModal()" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                                返回列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openCertificateModal(id) {
            document.getElementById('certificateModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有图表
            const initSmallChart = (id, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2019', '2020', '2021', '2022', '2023'],
                        datasets: [{
                            data: [5, 8, 12, 15, 20],
                            borderColor: color,
                            backgroundColor: 'rgba(255, 255, 255, 0)',
                            borderWidth: 2,
                            tension: 0.4,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        }
                    }
                });
            };

            initSmallChart('nationalChart', '#3B82F6');
            initSmallChart('provincialChart', '#10B981');
            initSmallChart('municipalChart', '#F59E0B');
            initSmallChart('patentChart', '#8B5CF6');

            // 年度趋势图
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '国家级',
                            data: [5, 8, 12, 15, 20],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '省级',
                            data: [10, 12, 15, 18, 25],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '市级',
                            data: [15, 18, 20, 25, 30],
                            borderColor: '#F59E0B',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 技术领域分布图
            const fieldCtx = document.getElementById('fieldDistributionChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'radar',
                data: {
                    labels: ['医学', '生物技术', '药学', '新材料', '医疗器械'],
                    datasets: [
                        {
                            label: '奖项分布',
                            data: [65, 59, 90, 81, 56],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: '#3B82F6',
                            borderWidth: 2,
                            pointBackgroundColor: '#3B82F6',
                            pointRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 点击证书预览抽屉外部关闭
            document.getElementById('certificateModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeCertificateModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('certificateModal').classList.contains('hidden')) {
                    closeCertificateModal();
                }
            });
        });
    </script>
</body>
</html>