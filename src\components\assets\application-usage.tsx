'use client'

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false })

export function ApplicationUsage() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      data: ['预算编制系统', '资金支付系统', '项目管理系统', '报表分析系统', '绩效评估系统']
    },
    series: [
      {
        name: '日活用户',
        type: 'bar',
        data: [256, 189, 145, 167, 123],
        itemStyle: {
          color: '#2563EB'
        }
      }
    ]
  }

  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="border-b border-[#E5E9EF]">
        <CardTitle className="text-lg font-medium">应用使用情况</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px]">
          {mounted && (
            <ReactECharts option={option} style={{ height: '100%' }} />
          )}
        </div>
      </CardContent>
    </Card>
  )
} 