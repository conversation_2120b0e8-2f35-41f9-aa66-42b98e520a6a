<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1600 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="800" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技奖励展示流程图</text>

  <!-- 阶段一：模块初始化 -->
  <text x="800" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：模块初始化</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(650, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入科技奖励展示模块</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">系统根据权限加载奖励主数据</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">计算聚合指标，刷新卡片概览区</text>
  </g>

  <!-- 阶段二：筛选查询 -->
  <text x="800" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询</text>

  <!-- 节点2: 筛选条件设置 -->
  <g transform="translate(650, 300)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设置与查询</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">按级别、类别、年度等条件筛选</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">返回列表并更新统计图表与卡片</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 800 210 Q 800 235 800 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看 -->
  <text x="800" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看</text>

  <!-- 节点3: 查看详情 -->
  <g transform="translate(200, 470)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看奖励详情</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">拉取项目资料、证书图像</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">媒体报道，渲染至详情侧栏</text>
  </g>

  <!-- 节点4: 文件操作 -->
  <g transform="translate(1120, 470)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件操作与审计</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">切换标签或下载证书</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">调用文件存储服务，记录审计日志</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 380 C 600 410, 400 440, 340 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 480 510 C 650 510, 950 510, 1120 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="800" y="500" text-anchor="middle" font-size="12" fill="#555">用户操作</text>

  <!-- 阶段四：统计分析 -->
  <text x="800" y="620" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：统计分析</text>

  <!-- 节点5: 统计分析交互 -->
  <g transform="translate(650, 650)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计分析交互</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">根据筛选范围实时聚合奖励数据</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">生成可视化图表，保证同步</text>
  </g>

  <!-- 阶段五：数据导出 -->
  <text x="800" y="800" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：数据导出</text>

  <!-- 节点6: 导出与合规 -->
  <g transform="translate(550, 830)" filter="url(#soft-shadow)">
    <rect width="500" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="250" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出与合规记录</text>
    <text x="250" y="50" text-anchor="middle" font-size="12" fill="#555">生成符合模板规范的PDF或Excel文件并提供下载</text>
    <text x="250" y="65" text-anchor="middle" font-size="12" fill="#555">在日志中心记录导出时间、文件类型及涉及数据范围</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 1260 550 C 1260 600, 900 620, 800 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 800 730 Q 800 780 800 830" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈连接线 5 -> 2 (统计分析反馈到筛选查询) -->
  <path d="M 650 690 C 500 690, 400 600, 400 400, 400 340, 650 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="500" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 350, 500)">实时反馈</text>

</svg>