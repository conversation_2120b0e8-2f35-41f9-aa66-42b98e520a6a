<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">仪器设备种类维护流程图</text>

  <!-- 阶段一：系统初始化与展示 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与展示</text>
  
  <!-- 节点1: 管理员登录 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员登录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">授权用户进入维护模块</text>
  </g>

  <!-- 节点2: 系统加载分类树 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统加载分类树</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">树状结构展示所有分类</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与操作 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与操作</text>

  <!-- 节点3: 选择目标节点 -->
  <g transform="translate(100, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">选择目标节点</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">在分类树中选择操作对象</text>
  </g>

  <!-- 节点4: 执行操作 -->
  <g transform="translate(400, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">执行操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">新增/编辑/删除/拖拽调整</text>
  </g>

  <!-- 节点5: 系统响应 -->
  <g transform="translate(700, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统响应</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">弹出操作窗口或实时响应</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 500 200 C 500 250, 200 250, 200 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 3 -> 4 -->
  <path d="M 300 335 Q 350 335 400 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 600 335 Q 650 335 700 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据校验与处理 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据校验与处理</text>

  <!-- 节点6: 字段填写与校验 -->
  <g transform="translate(100, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段填写与校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动生成编码并唯一性校验</text>
  </g>

  <!-- 节点7: 引用检测 -->
  <g transform="translate(400, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">引用检测</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">检测分类是否被设备引用</text>
  </g>

  <!-- 节点8: 操作处理 -->
  <g transform="translate(700, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">执行操作或中断并提示</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 370 C 700 420, 200 420, 200 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 300 505 Q 350 505 400 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -->
  <path d="M 600 505 Q 650 505 700 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据同步与审计 -->
  <text x="700" y="610" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与审计</text>

  <!-- 节点9: 数据同步 -->
  <g transform="translate(250, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据同步</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">同步分类信息至相关模块</text>
  </g>

  <!-- 节点10: 日志记录与审计 -->
  <g transform="translate(550, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志记录与审计</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录操作日志并支持导出</text>
  </g>

  <!-- 连接线 8 -> 9 -->
  <path d="M 700 540 C 700 590, 350 590, 350 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 10 -->
  <path d="M 800 540 C 800 590, 650 590, 650 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 流程循环箭头 -->
  <path d="M 1000 675 C 1100 675, 1100 165, 600 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="420" text-anchor="middle" font-size="12" fill="#666" transform="rotate(90, 1050, 420)">持续维护循环</text>

</svg>