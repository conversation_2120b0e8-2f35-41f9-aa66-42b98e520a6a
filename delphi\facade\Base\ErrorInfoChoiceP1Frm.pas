unit ErrorInfoChoiceP1Frm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, RzButton, ExtCtrls, RzPanel, StdCtrls, RzLabel, pngimage,
  PngFunctions, HtImage, AdvGlowButton, frxClass,CommonUtil;

type
  TErrorInfoChoiceP1Form = class(TForm)
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    RzPanel3: TRzPanel;
    Image1: TImage;
    L_Info1: TRzLabel;
    L_Info3: TRzLabel;
    Btn_Yes: TAdvGlowButton;
    L_Info4: TRzLabel;
    L_Info5: TRzLabel;
    Btn_No: TAdvGlowButton;
    procedure FormShow(Sender: TObject);
    procedure Btn_YesClick(Sender: TObject);
    procedure HTImage2Click(Sender: TObject);
    procedure Btn_NoClick(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure Init(infoTitle, infoErrorDes, infoDetail, infoDetail1,
      infoDetail2: string);
  end;

var
  ErrorInfoChoiceP1Form: TErrorInfoChoiceP1Form;
  FInfoTitle, FInfoErrorDes, FInfoDetail, FInfoDetail1, FInfoDetail2: string;

implementation

{$R *.dfm}

procedure TErrorInfoChoiceP1Form.Init(infoTitle, infoErrorDes, infoDetail,
  infoDetail1, infoDetail2: string);
begin
  FInfoTitle := infoTitle;
  FInfoErrorDes := infoErrorDes;
  FInfoDetail := infoDetail;
  FInfoDetail1 := infoDetail1;
  FInfoDetail2 := infoDetail2;
  SetChoiceFlag(0);
end;

procedure TErrorInfoChoiceP1Form.Btn_NoClick(Sender: TObject);
begin
  SetChoiceFlag(2);
  self.Close;
end;

procedure TErrorInfoChoiceP1Form.Btn_YesClick(Sender: TObject);
begin
  SetChoiceFlag(1);
  self.Close;
end;

procedure TErrorInfoChoiceP1Form.FormShow(Sender: TObject);
begin
  L_Info1.Caption := FInfoTitle;
  L_Info3.Caption := FInfoDetail;
  L_Info4.Caption := FInfoDetail1;
  L_Info5.Caption := FInfoDetail2;
end;

procedure TErrorInfoChoiceP1Form.HTImage2Click(Sender: TObject);
begin
  self.Close;
end;

end.
