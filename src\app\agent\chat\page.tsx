'use client'

import React from 'react'
import { CustomizableChat } from '../difyznwd/DifyChat'

export default function ChatPage() {
  // 聊天配置 - 请根据实际情况修改
  const chatConfig = {
    apiKey: 'app-xxx', // 替换为实际的API Key
    baseURL: 'http://10.26.37.118:8081/v1', // 替换为实际的Dify服务地址
    user: 'user-123'
  }

  return (
    <div className="h-screen bg-gradient-to-b from-blue-50/30 to-white/80">
      <CustomizableChat
        config={chatConfig}
        title="甬知AI智能问答助手"
        placeholder="我想海外知识产权纠纷应对指导，请问如何申请？"
        height="h-full"
        className="h-full"
        showConversationList={false}
        historyOnlyMode={false}
        welcomeMessage="您好！我是甬知AI智能问答助手，可以帮您查询和解答知识库中的专业问题。请在下方输入您的问题，我会基于知识库中的资料为您提供准确的回答。"
        suggestedQuestions={[
          "我想海外知识产权纠纷应对指导，请问如何申请？",
          "宁波市知识产权保护中心提供哪些服务？",
          "如何申请专利快速审查？",
          "知识产权维权援助的申请条件是什么？"
        ]}
        suggestedQuestionsMode="first"
        emptyStateTitle="开始新的对话"
        emptyStateDescription="我可以帮您查询和解答知识库中的专业问题。请在下方输入您的问题，我会基于知识库中的资料为您提供准确的回答。"
        theme={{
          card: 'shadow-lg rounded-xl border-border/50',
          header: 'border-b bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-xl',
          content: 'bg-gradient-to-b from-blue-50/30 to-white/80',
          primaryButton: 'shadow-sm hover:shadow-md transition-shadow bg-gradient-to-r from-blue-500 to-blue-600 text-white border-none hover:from-blue-600 hover:to-blue-700'
        }}
        showWorkflow={true}
      />
    </div>
  )
}
