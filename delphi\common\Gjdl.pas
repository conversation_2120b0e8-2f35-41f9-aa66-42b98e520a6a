unit Gjdl;

interface

uses
  Classes;

type
  TGjdl = class
  private
    FGjdlid: integer;
    FName: string;
    FGjType: integer;
    FValidFlag: integer;
  public
    property Gjdlid: integer read FGjdlid write FGjdlid;
    property Name: string read FName write FName;
    property GjType: integer read FGjType write FGjType;
    property ValidFlag: integer read FValidFlag write FValidFlag;
  end;

implementation

end.
