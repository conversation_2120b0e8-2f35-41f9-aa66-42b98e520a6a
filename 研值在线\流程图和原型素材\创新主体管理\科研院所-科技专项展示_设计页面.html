<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研院所-科技专项展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">科技专项展示</h1>
            <p class="mt-2 text-gray-600">多维度展示科研院所承担的各类科技专项项目情况</p>
        </div>

        <!-- 指标看板区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
            <!-- 自然科学基金 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">自然科学基金</p>
                        <p class="text-2xl font-bold text-blue-600 mt-1">156</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">近三年增长</p>
                        <div class="flex items-center justify-end text-green-500">
                            <span class="text-sm font-medium">12.5%</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 国家重点研发计划 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">国家重点研发计划</p>
                        <p class="text-2xl font-bold text-green-600 mt-1">87</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">近三年增长</p>
                        <div class="flex items-center justify-end text-green-500">
                            <span class="text-sm font-medium">8.3%</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 重大项目 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">重大项目</p>
                        <p class="text-2xl font-bold text-purple-600 mt-1">42</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">近三年增长</p>
                        <div class="flex items-center justify-end text-red-500">
                            <span class="text-sm font-medium">5.2%</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 发改委项目 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">发改委项目</p>
                        <p class="text-2xl font-bold text-yellow-600 mt-1">23</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">近三年增长</p>
                        <div class="flex items-center justify-end text-green-500">
                            <span class="text-sm font-medium">3.7%</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 其他科技计划 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">其他科技计划</p>
                        <p class="text-2xl font-bold text-indigo-600 mt-1">67</p>
                    </div>
                    <div class="text-right">
                        <p class="text-sm text-gray-500">近三年增长</p>
                        <div class="flex items-center justify-end text-green-500">
                            <span class="text-sm font-medium">7.1%</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计图区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 柱折混合图 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">年度立项数量与经费趋势</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出图表
                    </button>
                </div>
                <div class="h-80">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
            
            <!-- 堆积面积图 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">经费占比演变</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出图表
                    </button>
                </div>
                <div class="h-80">
                    <canvas id="fundingChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 项目类型分布区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">项目类型分布</h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <div class="h-96">
                        <canvas id="distributionChart"></canvas>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-700">自然科学基金</p>
                                <p class="text-xl font-bold text-blue-600">156</p>
                            </div>
                            <span class="text-sm text-blue-500">占比 42.3%</span>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-700">国家重点研发计划</p>
                                <p class="text-xl font-bold text-green-600">87</p>
                            </div>
                            <span class="text-sm text-green-500">占比 23.6%</span>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-700">重大项目</p>
                                <p class="text-xl font-bold text-purple-600">42</p>
                            </div>
                            <span class="text-sm text-purple-500">占比 11.4%</span>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-700">发改委项目</p>
                                <p class="text-xl font-bold text-yellow-600">23</p>
                            </div>
                            <span class="text-sm text-yellow-500">占比 6.2%</span>
                        </div>
                    </div>
                    <div class="bg-indigo-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-700">其他科技计划</p>
                                <p class="text-xl font-bold text-indigo-600">67</p>
                            </div>
                            <span class="text-sm text-indigo-500">占比 18.2%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 清册列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目清册</h3>
                    <div class="flex space-x-3">
                        <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                            <option>全部年度</option>
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                        <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                            <option>全部类型</option>
                            <option>自然科学基金</option>
                            <option>国家重点研发计划</option>
                            <option>重大项目</option>
                            <option>发改委项目</option>
                            <option>其他科技计划</option>
                        </select>
                        <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                            <option>全部状态</option>
                            <option>在研</option>
                            <option>结题</option>
                            <option>延期</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专项类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">立项年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资助经费(万元)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高端装备关键技术研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024YFB2500001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重大项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1,250.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showProjectDetail()">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">新型功能材料设计与制备</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023YFA0200002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家重点研发计划</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">850.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showProjectDetail()">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">量子计算基础理论研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">92231000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自然科学基金</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">300.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">结题</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showProjectDetail()">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智能制造示范工程</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">FG2024001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发改委项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showProjectDetail()">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">生物医药创新平台建设</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023KJPT001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">其他科技计划</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">延期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showProjectDetail()">详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 368 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="hideProjectDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- 基本信息 -->
                        <div>
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                                <div class="space-y-3 text-sm">
                                    <div>
                                        <span class="text-gray-500">项目名称：</span>
                                        <span class="font-medium text-gray-900">高端装备关键技术研发</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目编号：</span>
                                        <span class="font-medium text-gray-900">2024YFB2500001</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">专项类型：</span>
                                        <span class="font-medium text-gray-900">重大项目</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">立项年度：</span>
                                        <span class="font-medium text-gray-900">2024</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">资助经费：</span>
                                        <span class="font-medium text-gray-900">1,250.00万元</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目负责人：</span>
                                        <span class="font-medium text-gray-900">张研究员</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">项目状态：</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">执行周期：</span>
                                        <span class="font-medium text-gray-900">2024.01-2026.12</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 项目进展 -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">项目进展</h4>
                                <div class="space-y-4">
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium text-gray-900">第一阶段</span>
                                            <span class="text-gray-500">2024.01-2024.06</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 100%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">已完成：材料设计与制备</p>
                                    </div>
                                    <div>
                                        <div class="flex justify-between text-sm mb-1">
                                            <span class="font-medium text-gray-900">第二阶段</span>
                                            <span class="text-gray-500">2024.07-2025.06</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">进行中：工艺优化与验证</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 相关成果 -->
                        <div>
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">相关成果</h4>
                                <div class="space-y-3">
                                    <div class="border-l-4 border-blue-500 pl-4 py-1">
                                        <p class="text-sm font-medium text-gray-900">发表论文：新型材料在高端装备中的应用</p>
                                        <p class="text-xs text-gray-500">SCI一区，2024年3月</p>
                                    </div>
                                    <div class="border-l-4 border-green-500 pl-4 py-1">
                                        <p class="text-sm font-medium text-gray-900">申请专利：一种高性能复合材料制备方法</p>
                                        <p class="text-xs text-gray-500">发明专利，2024年5月</p>
                                    </div>
                                    <div class="border-l-4 border-purple-500 pl-4 py-1">
                                        <p class="text-sm font-medium text-gray-900">技术报告：高端装备材料性能测试报告</p>
                                        <p class="text-xs text-gray-500">内部报告，2024年6月</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 文件下载 -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">文件下载</h4>
                                <div class="space-y-2">
                                    <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        项目批复文件.pdf
                                    </a>
                                    <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        项目任务书.docx
                                    </a>
                                    <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        中期进展报告.pdf
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示项目详情弹窗
        function showProjectDetail() {
            document.getElementById('projectDetailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏项目详情弹窗
        function hideProjectDetail() {
            document.getElementById('projectDetailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('projectDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideProjectDetail();
            }
        });

        // 图表初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 年度立项数量与经费趋势图
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'bar',
                data: {
                    labels: ['2020', '2021', '2022', '2023', '2024'],
                    datasets: [
                        {
                            label: '自然科学基金',
                            data: [120, 135, 142, 150, 156],
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            yAxisID: 'y'
                        },
                        {
                            label: '国家重点研发计划',
                            data: [65, 72, 80, 85, 87],
                            backgroundColor: 'rgba(16, 185, 129, 0.7)',
                            yAxisID: 'y'
                        },
                        {
                            label: '重大项目',
                            data: [45, 44, 43, 42, 42],
                            backgroundColor: 'rgba(139, 92, 246, 0.7)',
                            yAxisID: 'y'
                        },
                        {
                            label: '总经费(万元)',
                            data: [3200, 3800, 4200, 4500, 4800],
                            borderColor: 'rgba(239, 68, 68, 1)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            type: 'line',
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '立项数量'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false
                            },
                            title: {
                                display: true,
                                text: '总经费(万元)'
                            }
                        }
                    }
                }
            });

            // 经费占比演变图
            const fundingCtx = document.getElementById('fundingChart').getContext('2d');
            new Chart(fundingCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023', '2024'],
                    datasets: [
                        {
                            label: '自然科学基金',
                            data: [35, 36, 38, 39, 40],
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true
                        },
                        {
                            label: '国家重点研发计划',
                            data: [25, 26, 27, 28, 28],
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            fill: true
                        },
                        {
                            label: '重大项目',
                            data: [20, 19, 18, 17, 16],
                            borderColor: 'rgba(139, 92, 246, 1)',
                            backgroundColor: 'rgba(139, 92, 246, 0.1)',
                            fill: true
                        },
                        {
                            label: '发改委项目',
                            data: [10, 9, 8, 7, 7],
                            borderColor: 'rgba(245, 158, 11, 1)',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            fill: true
                        },
                        {
                            label: '其他科技计划',
                            data: [10, 10, 9, 9, 9],
                            borderColor: 'rgba(99, 102, 241, 1)',
                            backgroundColor: 'rgba(99, 102, 241, 0.1)',
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            stacked: true,
                            min: 0,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });

            // 项目类型分布图
            const distributionCtx = document.getElementById('distributionChart').getContext('2d');
            new Chart(distributionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['自然科学基金', '国家重点研发计划', '重大项目', '发改委项目', '其他科技计划'],
                    datasets: [{
                        data: [156, 87, 42, 23, 67],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(99, 102, 241, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '70%'
                }
            });
        });
    </script>
</body>
</html>