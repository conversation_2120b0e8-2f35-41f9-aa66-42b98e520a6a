// DragDrop 相关类型
export type DragDropResult = {
  draggableId: string;
  type: string;
  source: {
    index: number;
    droppableId: string;
  };
  destination?: {
    index: number;
    droppableId: string;
  };
  reason: string;
}

// ECharts 配置类型
export type EChartsOption = {
  title?: {
    text?: string;
    left?: string;
  };
  tooltip?: {
    trigger?: 'item' | 'axis';
    axisPointer?: {
      type?: string;
    };
  };
  legend?: {
    orient?: string;
    right?: number | string;
    bottom?: number | string;
    top?: string;
    data?: string[];
  };
  grid?: {
    left?: string | number;
    right?: string | number;
    bottom?: string | number;
    containLabel?: boolean;
  };
  xAxis?: any;
  yAxis?: any;
  series?: any[];
} 