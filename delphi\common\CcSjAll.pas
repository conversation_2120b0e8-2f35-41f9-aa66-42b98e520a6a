unit CcSjAll;

interface

uses
  Classes;

type
  TCcSjAll = class
  private

    FDdid: Integer;
    FDds: Integer;

    FSsScb_a: double;
    FSsScb_b: double;
    FSsScb_c: double;
    FSsScb_d: double;
    FPbcb_a: double;
    FPbcb_b: double;
    FPbcb_c: double;
    FPbcb_d: double;
    FPbgysid_a: string;
    FPbgysid_b: string;
    FPbgysid_c: string;
    FPbgysid_d: string;
    FSzbcb: double;
    FLwcb: double;
    FHjlcb: double;
    FGtbcb: double;
    FMsje: double;
    FRzdcb: double;
    FmQtje_a: double;
    FmQtje_b: double;
    FmQtje_c: double;
    FSbzje: double;
    FDpzje: double;
    FXzje: double;
    FNkzje: double;
    FLlzje: double;
    FXjdzje: double;
    FNhczje: double;
    FQtje_a: double;
    FQtje_b: double;
    FQtje_c: double;
    FYhzje: double;
    FXhzje: double;
    FCyrxCb: double;
    FCysxCb: double;
    FCyyhCb: double;
    FTxmje: double;
    FCzje: double;
    FCbje: double;
    FJdje: double;
    FZxje: double;
    FBzqtje: double;
    FGj: double;
    FCwfy: double;
    FScs: Integer;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Ddid: Integer read FDdid write FDdid;
    property Dds: Integer read FDds write FDds;

    property SsScb_a: double read FSsScb_a write FSsScb_a;
    property SsScb_b: double read FSsScb_b write FSsScb_b;
    property SsScb_c: double read FSsScb_c write FSsScb_c;
    property SsScb_d: double read FSsScb_d write FSsScb_d;
    property Pbcb_a: double read FPbcb_a write FPbcb_a;
    property Pbcb_b: double read FPbcb_b write FPbcb_b;
    property Pbcb_c: double read FPbcb_c write FPbcb_c;
    property Pbcb_d: double read FPbcb_d write FPbcb_d;

    property Pbgysid_a: string read FPbgysid_a write FPbgysid_a;
    property Pbgysid_b: string read FPbgysid_b write FPbgysid_b;
    property Pbgysid_c: string read FPbgysid_c write FPbgysid_c;
    property Pbgysid_d: string read FPbgysid_d write FPbgysid_d;

    property Szbcb: double read FSzbcb write FSzbcb;
    property Lwcb: double read FLwcb write FLwcb;
    property Hjlcb: double read FHjlcb write FHjlcb;
    property Gtbcb: double read FGtbcb write FGtbcb;
    property Msje: double read FMsje write FMsje;
    property Rzdcb: double read FRzdcb write FRzdcb;

    property mQtje_a: double read FmQtje_a write FmQtje_a;
    property mQtje_b: double read FmQtje_b write FmQtje_b;
    property mQtje_c: double read FmQtje_c write FmQtje_c;

    property Sbzje: double read FSbzje write FSbzje;
    property Dpzje: double read FDpzje write FDpzje;
    property Xzje: double read FXzje write FXzje;
    property Nkzje: double read FNkzje write FNkzje;
    property Llzje: double read FLlzje write FLlzje;
    property Xjdzje: double read FXjdzje write FXjdzje;
    property Nhczje: double read FNhczje write FNhczje;

    property Qtje_a: double read FQtje_a write FQtje_a;
    property Qtje_b: double read FQtje_b write FQtje_b;
    property Qtje_c: double read FQtje_c write FQtje_c;

    property Yhzje: double read FYhzje write FYhzje;
    property Xhzje: double read FXhzje write FXhzje;
    property CyrxCb: double read FCyrxCb write FCyrxCb;
    property CysxCb: double read FCysxCb write FCysxCb;
    property CyyhCb: double read FCyyhCb write FCyyhCb;
    property Txmje: double read FTxmje write FTxmje;
    property Czje: double read FCzje write FCzje;
    property Cbje: double read FCbje write FCbje;
    property Jdje: double read FJdje write FJdje;
    property Zxje: double read FZxje write FZxje;
    property Bzqtje: double read FBzqtje write FBzqtje;

    property Gj: double read FGj write FGj;

    property Cwfy: double read FCwfy write FCwfy;
    property Scs: Integer read FScs write FScs;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

  end;

implementation

end.
