<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院医疗设备展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">医疗设备展示</h1>
            <p class="text-gray-600">医院自有、外协及共享仪器设备数据的统一呈现与分析</p>
        </div>

        <!-- 设备概况区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">设备总台套数</p>
                        <p class="text-2xl font-semibold text-gray-900">1,245</p>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between text-sm text-gray-500 mb-1">
                        <span>大型精密设备</span>
                        <span>35%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 35%"></div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">设备总原值</p>
                        <p class="text-2xl font-semibold text-gray-900">¥3.2亿</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">开放共享比例</p>
                        <p class="text-2xl font-semibold text-gray-900">68%</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">近三年新增设备</p>
                        <p class="text-2xl font-semibold text-gray-900">326</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类分布图区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-900">设备分类分布</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">台套数</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm">原值</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="categoryChart"></canvas>
            </div>
        </div>

        <!-- 用途分析图区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-900">设备用途分析</h2>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="h-64">
                    <canvas id="usageChart"></canvas>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-700">临床诊疗 - 45%</span>
                    </div>
                    <p class="text-sm text-gray-500">主要用于患者诊断、治疗和康复的医疗设备，如CT、MRI等影像设备。</p>
                    
                    <div class="flex items-center mt-4">
                        <div class="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-700">科研实验 - 25%</span>
                    </div>
                    <p class="text-sm text-gray-500">支持医学研究和临床试验的专用设备，如流式细胞仪、PCR仪等。</p>
                    
                    <div class="flex items-center mt-4">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-700">教学培训 - 20%</span>
                    </div>
                    <p class="text-sm text-gray-500">用于医学生和医护人员培训的教学模拟设备。</p>
                    
                    <div class="flex items-center mt-4">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-700">共享服务 - 10%</span>
                    </div>
                    <p class="text-sm text-gray-500">对外开放共享的高端医疗设备资源。</p>
                </div>
            </div>
        </div>

        <!-- 设备清册列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">设备清册列表</h2>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <input type="text" placeholder="搜索设备名称或编号" class="pl-8 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400 absolute left-3 top-2.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出Excel
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">型号规格</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用途</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">购置年份</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原值(万)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">管理科室</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?medical,equipment" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">3.0T磁共振成像系统</div>
                                        <div class="text-sm text-gray-500">MR-2023-001</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MAGNETOM Vida</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">影像诊断</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">临床诊疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2,800</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">使用中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">放射科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?laboratory,equipment" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">流式细胞仪</div>
                                        <div class="text-sm text-gray-500">FC-2022-015</td>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CytoFLEX S</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">检验分析</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研实验</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">320</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">使用中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">检验科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?ultrasound,machine" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">高端彩色多普勒超声</div>
                                        <div class="text-sm text-gray-500">US-2021-042</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">EPIQ 7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">影像诊断</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">临床诊疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">450</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">维修中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">超声科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?surgery,robot" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">达芬奇手术机器人</div>
                                        <div class="text-sm text-gray-500">SR-2020-003</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Xi System</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">治疗介入</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">临床诊疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3,200</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">使用中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">外科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?microscope" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">共聚焦显微镜</div>
                                        <div class="text-sm text-gray-500">CM-2023-007</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LSM 900</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研实验</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研实验</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">580</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">使用中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 1,245 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 下载与共享入口区 -->
        <div class="flex justify-center space-x-6 mb-8">
            <button class="px-6 py-3 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                下载分类清册
            </button>
            <button class="px-6 py-3 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                </svg>
                申请共享服务
            </button>
        </div>
    </div>

    <!-- 设备详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">设备详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 左侧信息 -->
                    <div class="md:col-span-2">
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">设备名称</p>
                                    <p class="font-medium text-gray-900">3.0T磁共振成像系统</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">设备编号</p>
                                    <p class="font-medium text-gray-900">MR-2023-001</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">型号规格</p>
                                    <p class="font-medium text-gray-900">MAGNETOM Vida</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">生产厂商</p>
                                    <p class="font-medium text-gray-900">西门子医疗</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">购置日期</p>
                                    <p class="font-medium text-gray-900">2023-03-15</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">原值(万元)</p>
                                    <p class="font-medium text-gray-900">2,800</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">管理科室</p>
                                    <p class="font-medium text-gray-900">放射科</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">设备状态</p>
                                    <p class="font-medium text-gray-900">使用中</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">技术参数</h4>
                            <div class="text-sm text-gray-700 space-y-2">
                                <p><span class="font-medium">磁场强度：</span>3.0 Tesla</p>
                                <p><span class="font-medium">梯度场强：</span>45 mT/m</p>
                                <p><span class="font-medium">梯度切换率：</span>200 T/m/s</p>
                                <p><span class="font-medium">通道数：</span>128</p>
                                <p><span class="font-medium">扫描孔径：</span>70 cm</p>
                                <p><span class="font-medium">临床应用：</span>全身各部位成像，包括神经系统、心血管系统、腹部、盆腔等</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧信息 -->
                    <div>
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">设备图片</h4>
                            <img src="https://source.unsplash.com/400x300/?mri,machine" alt="设备图片" class="w-full rounded-lg mb-3">
                            <div class="grid grid-cols-3 gap-2">
                                <img src="https://source.unsplash.com/100x100/?mri,scan" alt="设备图片" class="rounded-lg">
                                <img src="https://source.unsplash.com/100x100/?mri,room" alt="设备图片" class="rounded-lg">
                                <img src="https://source.unsplash.com/100x100/?mri,console" alt="设备图片" class="rounded-lg">
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">共享服务</h4>
                            <div class="text-sm space-y-3">
                                <div>
                                    <p class="text-gray-500">共享状态</p>
                                    <p class="font-medium text-gray-900">对外开放</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">预约方式</p>
                                    <p class="font-medium text-gray-900">在线预约</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">收费标准</p>
                                    <p class="font-medium text-gray-900">¥1,200/部位</p>
                                </div>
                                <button class="w-full mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                    立即预约
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化分类分布图表
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'bar',
                data: {
                    labels: ['影像诊断', '检验分析', '治疗介入', '科研实验', '教学培训'],
                    datasets: [{
                        label: '台套数',
                        data: [356, 278, 145, 123, 89],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化用途分析图表
            const usageCtx = document.getElementById('usageChart').getContext('2d');
            new Chart(usageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['临床诊疗', '科研实验', '教学培训', '共享服务'],
                    datasets: [{
                        data: [45, 25, 20, 10],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
        });
    </script>
</body>
</html>