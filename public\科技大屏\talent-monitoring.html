<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重点人才监测 - 科技创新监测平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white border-b border-gray-200 card-shadow sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-users text-purple-500 mr-3"></i>
                        重点人才监测
                    </h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-calendar-alt"></i>
                        <span>数据时间：2025年1~6月</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出人才清单
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="gradient-bg min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            
            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">人才总数</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">2,456</p>
                            <p class="text-xs text-purple-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+15.2%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-users text-purple-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">"510"人才团队</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">156</p>
                            <p class="text-xs text-blue-600 mt-1">
                                <i class="fas fa-users mr-1"></i>团队
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-friends text-blue-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">活跃人才</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">203</p>
                            <p class="text-xs text-green-600 mt-1">
                                <i class="fas fa-chart-line mr-1"></i>高活跃
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-fire text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">新增人才</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">89</p>
                            <p class="text-xs text-orange-600 mt-1">
                                <i class="fas fa-plus mr-1"></i>本年度
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-plus text-orange-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">人才筛选与分析</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg text-sm">列表视图</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">团队视图</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">网络图</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">人才类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                            <option value="">全部类型</option>
                            <option value="expert">科技副总</option>
                            <option value="professor">产业服务教授</option>
                            <option value="researcher">科研人员</option>
                            <option value="manager">管理人才</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">"510"领域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                            <option value="">全部领域</option>
                            <option value="smart">智能制造</option>
                            <option value="bio">生物医药</option>
                            <option value="material">新材料</option>
                            <option value="energy">新能源</option>
                            <option value="digital">数字经济</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">学历层次</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                            <option value="">全部学历</option>
                            <option value="phd">博士</option>
                            <option value="master">硕士</option>
                            <option value="bachelor">学士</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">工作单位</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                            <option value="">全部单位</option>
                            <option value="university">高等院校</option>
                            <option value="institute">科研院所</option>
                            <option value="enterprise">企业</option>
                            <option value="government">政府部门</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">活跃度</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                            <option value="">全部</option>
                            <option value="high">高活跃</option>
                            <option value="medium">中等活跃</option>
                            <option value="low">低活跃</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 人才分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 人才类型分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">人才类型分布</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-orange-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">科技副总</span>
                                    <p class="text-xs text-gray-600">企业技术负责人</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-orange-600">89</span>
                                <span class="text-xs text-gray-500 block">3.6%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg border border-teal-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-teal-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">产业服务教授</span>
                                    <p class="text-xs text-gray-600">高校产业服务人才</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-teal-600">234</span>
                                <span class="text-xs text-gray-500 block">9.5%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">科研项目负责人</span>
                                    <p class="text-xs text-gray-600">项目主要承担者</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-blue-600">1,456</span>
                                <span class="text-xs text-gray-500 block">59.3%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">其他科技人才</span>
                                    <p class="text-xs text-gray-600">技术骨干、专家等</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-purple-600">677</span>
                                <span class="text-xs text-gray-500 block">27.6%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- "510"领域人才团队 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"510"领域人才团队</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">智能制造团队</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">42个</span>
                                <span class="text-xs text-gray-500 block">26.9%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">生物医药团队</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">38个</span>
                                <span class="text-xs text-gray-500 block">24.4%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">新材料团队</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">35个</span>
                                <span class="text-xs text-gray-500 block">22.4%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">新能源团队</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">28个</span>
                                <span class="text-xs text-gray-500 block">17.9%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-teal-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-teal-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">数字经济团队</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">13个</span>
                                <span class="text-xs text-gray-500 block">8.3%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">团队平均规模</span>
                            <span class="font-semibold text-gray-900">8.5人/团队</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mt-2">
                            <span class="text-gray-600">跨领域团队</span>
                            <span class="font-semibold text-blue-600">23个</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活跃人才分析 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">科技计划项目活跃人才分析</h3>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 承担项目多的人才 -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-800">承担项目多</h4>
                            <span class="text-sm bg-green-100 text-green-700 px-2 py-1 rounded-full">125人</span>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-green-800">李</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">李教授</p>
                                    <p class="text-xs text-gray-600">宁波大学 · 智能制造</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-600">8个</span>
                                    <p class="text-xs text-gray-500">项目</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-green-800">王</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">王博士</p>
                                    <p class="text-xs text-gray-600">中科院宁波所 · 新材料</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-600">7个</span>
                                    <p class="text-xs text-gray-500">项目</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <div class="w-10 h-10 bg-green-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-green-800">张</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">张研究员</p>
                                    <p class="text-xs text-gray-600">宁波诺丁汉 · 生物医药</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-green-600">6个</span>
                                    <p class="text-xs text-gray-500">项目</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 成果显著的人才 -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-800">成果显著</h4>
                            <span class="text-sm bg-blue-100 text-blue-700 px-2 py-1 rounded-full">78人</span>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-blue-800">陈</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">陈院士</p>
                                    <p class="text-xs text-gray-600">宁波大学 · 智能制造</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-600">23项</span>
                                    <p class="text-xs text-gray-500">专利</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-blue-800">刘</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">刘教授</p>
                                    <p class="text-xs text-gray-600">中科院宁波所 · 新能源</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-600">18项</span>
                                    <p class="text-xs text-gray-500">论文</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                                <div class="w-10 h-10 bg-blue-200 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-xs font-bold text-blue-800">杨</span>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm font-medium text-gray-900">杨博士</p>
                                    <p class="text-xs text-gray-600">宁波海外院 · 数字经济</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-bold text-blue-600">12项</span>
                                    <p class="text-xs text-gray-500">成果转化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 活跃度趋势 -->
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-800">活跃度趋势</h4>
                            <span class="text-sm bg-purple-100 text-purple-700 px-2 py-1 rounded-full">上升</span>
                        </div>
                        <div class="h-48 bg-gradient-to-br from-gray-50 to-purple-50 rounded-lg flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=300&h=200&fit=crop&crop=entropy" 
                                 alt="人才活跃度趋势图" 
                                 class="w-full h-full object-cover rounded-lg opacity-70">
                            <div class="absolute inset-0 bg-purple-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                                <div class="text-center text-purple-800">
                                    <i class="fas fa-chart-line text-2xl mb-2"></i>
                                    <p class="font-semibold text-sm">活跃度趋势图</p>
                                    <p class="text-xs mt-1">6个月活跃度变化</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 重点人才清单 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">重点人才清单</h3>
                    <div class="flex items-center space-x-2">
                        <input type="text" placeholder="搜索人才姓名..." 
                               class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 text-sm">
                        <button class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="bg-gray-50 border-b border-gray-200">
                                <th class="px-4 py-3 text-left font-medium text-gray-700">姓名</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">类型</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">工作单位</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">"510"领域</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">学历</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">承担项目</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">活跃度</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-bold text-purple-800">李</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">李明教授</p>
                                            <p class="text-xs text-gray-500">博士生导师</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">科技副总</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波大学</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">智能制造</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">博士</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm font-medium text-gray-900">8个</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">85%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-purple-600 hover:text-purple-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">联系</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-bold text-teal-800">王</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">王华博士</p>
                                            <p class="text-xs text-gray-500">研究员</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-teal-100 text-teal-700 rounded-full text-xs">产业服务教授</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">中科院宁波材料所</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs">新材料</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">博士</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm font-medium text-gray-900">7个</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">75%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-purple-600 hover:text-purple-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">联系</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-xs font-bold text-blue-800">张</span>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">张伟研究员</p>
                                            <p class="text-xs text-gray-500">高级工程师</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">项目负责人</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波诺丁汉大学</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">生物医药</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">博士</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm font-medium text-gray-900">6个</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-purple-500 h-2 rounded-full" style="width: 68%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">68%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-purple-600 hover:text-purple-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">联系</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-500">
                        显示 1-10 条，共 2,456 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-500">上一页</button>
                        <button class="px-3 py-1 bg-purple-500 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <span class="px-2 text-gray-400">...</span>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">246</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技创新监测平台. 保留所有权利.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>数据更新频率：实时</span>
                    <span>|</span>
                    <span>监测人才总数：2,456人</span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html> 