unit GysCd;

interface

uses
  Classes;

type
  TGysCd = class
  private
    FGysCdid: integer;
    FGysCdname: string;
    FGysCdtype: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property GysCdid: integer read FGysCdid write FGysCdid;
    property GysCdname: string read FGysCdname write FGysCdname;
    property GysCdtype: string read FGysCdtype write FGysCdtype;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
