<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家基础信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">专家基础信息管理</h1>

        <!-- 筛选条件区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="expertName" class="block text-sm font-medium text-gray-700 mb-1">专家姓名</label>
                    <input type="text" id="expertName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专家姓名">
                </div>
                <div>
                    <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">性别</label>
                    <select id="gender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                <div>
                    <label for="ageRange" class="block text-sm font-medium text-gray-700 mb-1">年龄范围</label>
                    <div class="flex space-x-2">
                        <input type="number" id="minAge" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="最小">
                        <input type="number" id="maxAge" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="最大">
                    </div>
                </div>
                <div>
                    <label for="researchField" class="block text-sm font-medium text-gray-700 mb-1">研究领域</label>
                    <input type="text" id="researchField" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入研究领域">
                </div>
                <div>
                    <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">工作单位</label>
                    <input type="text" id="organization" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                </div>
                <div>
                    <label for="education" class="block text-sm font-medium text-gray-700 mb-1">学历职称</label>
                    <select id="education" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="professor">教授/研究员</option>
                        <option value="associate">副教授/副研究员</option>
                        <option value="doctor">博士</option>
                        <option value="master">硕士</option>
                    </select>
                </div>
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">专家标签</label>
                    <div class="relative">
                        <input type="text" id="tags" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="选择或输入标签">
                        <div id="tagDropdown" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 hidden">
                            <div class="p-2 space-y-1 max-h-60 overflow-y-auto">
                                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                                    <input type="checkbox" id="tag1" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="tag1" class="ml-2 text-sm text-gray-700">人工智能</label>
                                </div>
                                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                                    <input type="checkbox" id="tag2" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="tag2" class="ml-2 text-sm text-gray-700">生物医药</label>
                                </div>
                                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                                    <input type="checkbox" id="tag3" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="tag3" class="ml-2 text-sm text-gray-700">新材料</label>
                                </div>
                                <div class="flex items-center p-2 hover:bg-gray-100 rounded cursor-pointer">
                                    <input type="checkbox" id="tag4" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <label for="tag4" class="ml-2 text-sm text-gray-700">智能制造</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4 flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 专家列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">专家信息列表</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学历职称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">研究领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目数</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">论文数</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">张</span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张明远</div>
                                        <div class="text-sm text-gray-500">138****1234</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教授/博导</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能,机器学习</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">56</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openTagModal('1')" class="text-indigo-600 hover:text-indigo-900">标签</button>
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-pink-100 flex items-center justify-center">
                                        <span class="text-pink-600 font-medium">李</span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李静怡</div>
                                        <div class="text-sm text-gray-500">139****5678</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">38</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">主任医师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">临床医学,肿瘤学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openTagModal('2')" class="text-indigo-600 hover:text-indigo-900">标签</button>
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">王</span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">王建国</div>
                                        <div class="text-sm text-gray-500">137****9012</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">52</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波材料所</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料,纳米技术</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">78</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openTagModal('3')" class="text-indigo-600 hover:text-indigo-900">标签</button>
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <span class="text-green-600 font-medium">陈</span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">陈思远</div>
                                        <div class="text-sm text-gray-500">136****3456</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">41</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波智能制造研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造,工业自动化</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">24</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openTagModal('4')" class="text-indigo-600 hover:text-indigo-900">标签</button>
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">128</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签管理弹窗 -->
    <div id="tagModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">专家标签管理</h3>
                    <button onclick="closeModal('tagModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">当前标签</label>
                        <div class="flex flex-wrap gap-2">
                            <span class="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                                人工智能
                                <button class="ml-1 text-blue-600 hover:text-blue-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </span>
                            <span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                机器学习
                                <button class="ml-1 text-green-600 hover:text-green-800">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                    </svg>
                                </button>
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">添加新标签</label>
                        <div class="flex">
                            <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入新标签">
                            <button class="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                添加
                            </button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">常用标签</label>
                        <div class="flex flex-wrap gap-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-full text-xs text-gray-700 hover:bg-gray-100">生物医药</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-full text-xs text-gray-700 hover:bg-gray-100">新材料</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-full text-xs text-gray-700 hover:bg-gray-100">智能制造</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-full text-xs text-gray-700 hover:bg-gray-100">环境保护</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-full text-xs text-gray-700 hover:bg-gray-100">能源技术</button>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="closeModal('tagModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 专家详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-semibold text-gray-900">专家详细信息</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 基本信息 -->
                    <div class="lg:col-span-1 bg-gray-50 p-6 rounded-lg">
                        <div class="flex flex-col items-center mb-6">
                            <div class="h-24 w-24 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                                <span class="text-blue-600 text-3xl font-medium">张</span>
                            </div>
                            <h4 class="text-xl font-semibold text-gray-900">张明远</h4>
                            <p class="text-sm text-gray-500">宁波大学 教授/博导</p>
                        </div>
                        
                        <div class="space-y-4">
                            <div>
                                <h5 class="text-sm font-medium text-gray-500 mb-1">基本信息</h5>
                                <div class="grid grid-cols-2 gap-2 text-sm">
                                    <div>
                                        <span class="text-gray-500">性别：</span>
                                        <span class="text-gray-900">男</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">年龄：</span>
                                        <span class="text-gray-900">45</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">联系电话：</span>
                                        <span class="text-gray-900">138****1234</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">邮箱：</span>
                                        <span class="text-gray-900"><EMAIL></span>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h5 class="text-sm font-medium text-gray-500 mb-1">专业领域</h5>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">人工智能</span>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">机器学习</span>
                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">计算机视觉</span>
                                </div>
                            </div>
                            
                            <div>
                                <h5 class="text-sm font-medium text-gray-500 mb-1">教育背景</h5>
                                <div class="text-sm space-y-1">
                                    <p class="text-gray-900">2005-2008 清华大学 计算机科学与技术 博士</p>
                                    <p class="text-gray-900">2002-2005 浙江大学 计算机科学与技术 硕士</p>
                                    <p class="text-gray-900">1998-2002 浙江大学 计算机科学与技术 学士</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 学术与项目信息 -->
                    <div class="lg:col-span-2 space-y-6">
                        <div class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">学术成果</h4>
                            <div class="grid grid-cols-3 gap-4 text-center">
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-blue-600">56</div>
                                    <div class="text-sm text-gray-500">发表论文</div>
                                </div>
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-green-600">12</div>
                                    <div class="text-sm text-gray-500">科研项目</div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <div class="text-2xl font-bold text-yellow-600">5</div>
                                    <div class="text-sm text-gray-500">获奖情况</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">代表性论文</h4>
                            <div class="space-y-3">
                                <div class="border-b border-gray-100 pb-3">
                                    <p class="text-sm font-medium text-gray-900">基于深度学习的图像识别算法研究</p>
                                    <p class="text-xs text-gray-500">IEEE Transactions on Pattern Analysis and Machine Intelligence, 2022</p>
                                </div>
                                <div class="border-b border-gray-100 pb-3">
                                    <p class="text-sm font-medium text-gray-900">多模态机器学习在医疗诊断中的应用</p>
                                    <p class="text-xs text-gray-500">Nature Machine Intelligence, 2021</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">面向边缘计算的轻量级神经网络设计</p>
                                    <p class="text-xs text-gray-500">ACM Computing Surveys, 2020</p>
                                </div>
                            </div>
                            <button class="mt-4 text-sm text-blue-600 hover:text-blue-800">查看更多论文 →</button>
                        </div>
                        
                        <div class="bg-white p-6 rounded-lg border border-gray-200">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">科研项目</h4>
                            <div class="space-y-3">
                                <div class="border-b border-gray-100 pb-3">
                                    <p class="text-sm font-medium text-gray-900">面向智慧城市的计算机视觉关键技术研究</p>
                                    <p class="text-xs text-gray-500">国家自然科学基金重点项目 · 2021-2024 · 负责人</p>
                                </div>
                                <div class="border-b border-gray-100 pb-3">
                                    <p class="text-sm font-medium text-gray-900">基于深度学习的医疗影像分析系统</p>
                                    <p class="text-xs text-gray-500">浙江省重点研发计划 · 2019-2022 · 首席科学家</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">智能制造中的视觉检测技术应用</p>
                                    <p class="text-xs text-gray-500">宁波市重大科技专项 · 2018-2021 · 项目负责人</p>
                                </div>
                            </div>
                            <button class="mt-4 text-sm text-blue-600 hover:text-blue-800">查看更多项目 →</button>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openTagModal(expertId) {
            document.getElementById('tagModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openDetailModal(expertId) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('tagModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('tagModal');
            }
        });

        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('detailModal');
            }
        });

        // 标签下拉框控制
        document.getElementById('tags').addEventListener('focus', function() {
            document.getElementById('tagDropdown').classList.remove('hidden');
        });

        document.getElementById('tags').addEventListener('blur', function() {
            setTimeout(() => {
                document.getElementById('tagDropdown').classList.add('hidden');
            }, 200);
        });

        // 点击其他区域关闭标签下拉框
        document.addEventListener('click', function(e) {
            if (!e.target.closest('#tags') && !e.target.closest('#tagDropdown')) {
                document.getElementById('tagDropdown').classList.add('hidden');
            }
        });

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!document.getElementById('tagModal').classList.contains('hidden')) {
                        closeModal('tagModal');
                    }
                    if (!document.getElementById('detailModal').classList.contains('hidden')) {
                        closeModal('detailModal');
                    }
                }
            });
        });
    </script>
</body>
</html>