<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内部研发项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">内部研发项目管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">项目筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input type="text" placeholder="请输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
                    <input type="text" placeholder="请输入项目编号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目来源</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部来源</option>
                        <option value="self">自主立项</option>
                        <option value="gov">政府项目</option>
                        <option value="coop">合作项目</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目阶段</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部阶段</option>
                        <option value="prepare">立项准备</option>
                        <option value="execution">执行中</option>
                        <option value="complete">已完成</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">起止日期</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleStatus(this)">正常</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleStatus(this)">延期</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleStatus(this)">暂停</button>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="text-sm text-gray-600">
                共 <span class="font-medium text-gray-900">128</span> 个项目
            </div>
            <div class="flex space-x-3">
                <button onclick="openImportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    批量导入
                </button>
                <button onclick="openExportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    批量导出
                </button>
                <button onclick="openProjectModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增项目
                </button>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目来源</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止日期</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目阶段</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新一代人工智能芯片研发</div>
                                <div class="text-sm text-gray-500">宁波市科技局重点项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RD-2023-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">政府项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-01-15 ~ 2024-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">执行中</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">正常</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewProject('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editProject('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject('1')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新能源汽车电池管理系统研发</div>
                                <div class="text-sm text-gray-500">企业自主立项</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RD-2023-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自主立项</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-03-10 ~ 2023-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">执行中</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">延期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewProject('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editProject('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject('2')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能家居物联网平台开发</div>
                                <div class="text-sm text-gray-500">与宁波大学合作项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RD-2023-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">合作项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-02-20 ~ 2023-11-30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">已完成</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">正常</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewProject('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editProject('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject('3')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">工业机器人控制系统升级</div>
                                <div class="text-sm text-gray-500">企业自主立项</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RD-2023-004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自主立项</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-05-15 ~ 2024-06-30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">执行中</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">暂停</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewProject('4')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editProject('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject('4')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">5G通信模块研发</div>
                                <div class="text-sm text-gray-500">宁波市科技局重点项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">RD-2023-005</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">政府项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-04-01 ~ 2024-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">立项准备</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">正常</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewProject('5')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editProject('5')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteProject('5')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">128</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 左侧基本信息 -->
                    <div class="lg:col-span-2 space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">项目名称：</span>
                                    <span class="font-medium text-gray-900">新一代人工智能芯片研发</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目编号：</span>
                                    <span class="font-medium text-gray-900">RD-2023-001</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目来源：</span>
                                    <span class="font-medium text-gray-900">政府项目</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目类型：</span>
                                    <span class="font-medium text-gray-900">技术研发</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">起止日期：</span>
                                    <span class="font-medium text-gray-900">2023-01-15 ~ 2024-12-31</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目阶段：</span>
                                    <span class="font-medium text-gray-900">执行中</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目状态：</span>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">正常</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">负责人：</span>
                                    <span class="font-medium text-gray-900">张研究员</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">技术经济目标</h4>
                            <div class="text-sm text-gray-700 space-y-2">
                                <p>1. 研发具有自主知识产权的人工智能芯片，性能达到国际领先水平</p>
                                <p>2. 申请发明专利5项，实用新型专利10项</p>
                                <p>3. 形成产业化生产能力，年产值达到5000万元</p>
                                <p>4. 培养专业技术人才20名</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">关联成果</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center p-2 border-b border-gray-200">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">一种高性能AI芯片设计方法</p>
                                        <p class="text-xs text-gray-500">发明专利 | 申请中</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-900 text-sm">查看</button>
                                </div>
                                <div class="flex justify-between items-center p-2 border-b border-gray-200">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">AI芯片散热结构</p>
                                        <p class="text-xs text-gray-500">实用新型专利 | 已授权</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-900 text-sm">查看</button>
                                </div>
                                <div class="flex justify-between items-center p-2">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">AI芯片测试报告</p>
                                        <p class="text-xs text-gray-500">技术报告 | 2023-06-15</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-900 text-sm">查看</button>
                                </div>
                            </div>
                            <button class="mt-3 px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                                添加关联成果
                            </button>
                        </div>
                    </div>
                    
                    <!-- 右侧年度投入 -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">年度投入</h4>
                            <div class="h-64">
                                <canvas id="investmentChart"></canvas>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-md font-semibold text-gray-900">2023年投入明细</h4>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">添加记录</button>
                            </div>
                            <div class="space-y-3">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">人员费用</span>
                                    <span class="font-medium text-gray-900">¥1,200,000</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">设备费用</span>
                                    <span class="font-medium text-gray-900">¥850,000</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">材料费用</span>
                                    <span class="font-medium text-gray-900">¥320,000</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-500">测试费用</span>
                                    <span class="font-medium text-gray-900">¥150,000</span>
                                </div>
                                <div class="border-t border-gray-200 pt-2 flex justify-between text-sm font-semibold">
                                    <span class="text-gray-700">合计</span>
                                    <span class="text-gray-900">¥2,520,000</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button onclick="editProject('1')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        编辑项目
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入弹窗 -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批量导入项目</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            下载导入模板
                        </a>
                    </div>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                            </label>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                        </div>
                    </div>
                    <div class="hidden" id="upload-progress">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">上传进度</span>
                            <span class="text-gray-600">75%</span>
                        </div>
                        <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openProjectModal() {
            document.getElementById('projectModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal() {
            document.getElementById('projectModal').classList.add('hidden');
            document.getElementById('importModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
        
        // 状态切换
        function toggleStatus(button) {
            if (button.classList.contains('border-blue-500')) {
                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');
                button.classList.add('border-gray-300', 'text-gray-700');
            } else {
                button.classList.remove('border-gray-300', 'text-gray-700');
                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');
            }
        }
        
        // 项目操作
        function viewProject(id) {
            openProjectModal();
            console.log('查看项目:', id);
        }
        
        function editProject(id) {
            openProjectModal();
            console.log('编辑项目:', id);
        }
        
        function deleteProject(id) {
            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {
                console.log('删除项目:', id);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('projectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('upload-progress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('upload-progress').classList.add('hidden');
                            alert('文件上传成功！');
                        }, 500);
                    }
                }, 200);
            }
        });
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 年度投入图表
            const investmentCtx = document.getElementById('investmentChart').getContext('2d');
            new Chart(investmentCtx, {
                type: 'bar',
                data: {
                    labels: ['2021', '2022', '2023', '2024'],
                    datasets: [{
                        label: '年度投入 (万元)',
                        data: [120, 180, 252, 150],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>