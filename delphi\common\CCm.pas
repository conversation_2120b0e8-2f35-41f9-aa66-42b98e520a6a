unit CCm;

interface

uses
  Classes;

type
  TCCm = class
  private
    F2: string;
    F4: string;
    F6: string;
    F8: string;
    F10: string;
    F12: string;
    F14: string;
  public
    property N_2: string read F2 write F2;
    property N_4: string read F4 write F4;
    property N_6: string read F6 write F6;
    property N_8: string read F8 write F8;
    property N_10: string read F10 write F10;
    property N_12: string read F12 write F12;
    property N_14: string read F14 write F14;
  end;

implementation

end.
