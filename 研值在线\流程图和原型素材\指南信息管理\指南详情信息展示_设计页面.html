<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指南详情信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">指南管理</h1>
            <p class="text-gray-600">提供指南的新增、修改、删除、查询、导入、导出、指南详情展示、专家关联、计划类别关联等功能</p>
        </div>

        <!-- 指南列表表格 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        宁波市指南列表
                    </h2>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新增指南
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">指南名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openGuideDetail()">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术攻关指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市科技局</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">智能制造</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">征集中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openGuideDetail()">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新材料产业发展指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市经信局</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">新材料</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">征集中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openGuideDetail()">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市数字经济创新应用指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市大数据局</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">数字经济</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已截止</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 指南详情侧边弹窗 -->
    <div id="guideDetailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-full md:w-1/2 lg:w-2/5 bg-white shadow-xl overflow-y-auto">
            <!-- 标题区 -->
            <div class="sticky top-0 bg-white z-10 p-6 border-b border-gray-200 flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-bold text-gray-900 flex items-center">
                        <span>宁波市智能制造关键技术攻关指南</span>
                        <span class="ml-3 px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">征集中</span>
                    </h2>
                </div>
                <div class="flex space-x-4">
                    <button class="text-gray-500 hover:text-gray-700" title="复制链接">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </button>
                    <button onclick="closeGuideDetail()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="p-6 space-y-6">
                <!-- 基本信息区 -->
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            基本信息
                        </h3>
                        <div class="relative" x-data="{ showSearch: false }">
                            <button @click="showSearch = !showSearch" class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                搜索
                            </button>
                            <div x-show="showSearch" @click.away="showSearch = false" class="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg z-10 p-2">
                                <input type="text" placeholder="输入关键词搜索" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 gap-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-xs text-gray-500">发布单位</p>
                                <p class="text-sm font-medium text-gray-900">宁波市科技局</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800" title="复制">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-xs text-gray-500">技术领域</p>
                                <p class="text-sm font-medium text-gray-900">智能制造</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800" title="复制">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-xs text-gray-500">关联计划类别</p>
                                <p class="text-sm font-medium text-gray-900">宁波市重点研发计划</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800" title="复制">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 研究与考核区 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        研究与考核
                    </h3>
                    
                    <!-- 研究内容卡片 -->
                    <div class="bg-gray-50 rounded-lg overflow-hidden">
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer" onclick="toggleCollapse(this)">
                            <h4 class="text-sm font-medium text-gray-900">研究内容</h4>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                        <div class="p-4">
                            <p class="text-sm text-gray-700">本指南聚焦宁波市智能制造领域的关键技术攻关，重点支持工业机器人、智能装备、数字化工厂等方向的研究。研究内容包括但不限于：1. 工业机器人高精度控制技术；2. 智能装备自主决策与协同控制技术；3. 数字化工厂全流程优化技术；4. 智能制造系统安全与可靠性技术。研究成果应具有自主知识产权，并在宁波市制造业企业中得到示范应用。</p>
                            <div class="mt-3 flex justify-end">
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    复制内容
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 考核指标卡片 -->
                    <div class="bg-gray-50 rounded-lg overflow-hidden">
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center cursor-pointer" onclick="toggleCollapse(this)">
                            <h4 class="text-sm font-medium text-gray-900">考核指标</h4>
                            <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                        <div class="p-4">
                            <ul class="list-disc pl-5 space-y-2 text-sm text-gray-700">
                                <li>申请发明专利不少于3项，其中授权发明专利不少于1项</li>
                                <li>开发具有自主知识产权的工业机器人或智能装备样机1台套</li>
                                <li>在宁波市制造业企业建立示范应用生产线1条</li>
                                <li>培养智能制造领域专业技术人才不少于5人</li>
                                <li>项目执行期内实现技术成果转化收入不低于100万元</li>
                            </ul>
                            <div class="mt-3 flex justify-end">
                                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    复制指标
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 申报要求区 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        申报要求
                    </h3>
                    
                    <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-xs text-gray-500">申报条件</p>
                                <p class="text-sm font-medium text-gray-900">宁波市注册企业，具有相关研发能力</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">额度上限</p>
                                <p class="text-sm font-medium text-gray-900">100万元</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">申报开始时间</p>
                                <p class="text-sm font-medium text-gray-900">2024-03-01</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">申报截止时间</p>
                                <p class="text-sm font-medium text-gray-900">2024-03-31</p>
                            </div>
                        </div>
                        <div class="pt-3 border-t border-gray-200 flex justify-end">
                            <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                复制全部
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 专家与标引区 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        专家与标引
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- 专家列表 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">制定专家</h4>
                            <div class="flex flex-wrap gap-3">
                                <div class="relative group">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">张</span>
                                    </div>
                                    <div class="absolute z-10 left-0 bottom-full mb-2 hidden group-hover:block w-48 bg-white shadow-lg rounded-md p-2">
                                        <p class="text-xs font-medium text-gray-900">张教授</p>
                                        <p class="text-xs text-gray-500">宁波大学智能制造研究所</p>
                                    </div>
                                </div>
                                <div class="relative group">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">王</span>
                                    </div>
                                    <div class="absolute z-10 left-0 bottom-full mb-2 hidden group-hover:block w-48 bg-white shadow-lg rounded-md p-2">
                                        <p class="text-xs font-medium text-gray-900">王研究员</p>
                                        <p class="text-xs text-gray-500">中科院宁波材料所</p>
                                    </div>
                                </div>
                                <div class="relative group">
                                    <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <span class="text-blue-600 font-medium">李</span>
                                    </div>
                                    <div class="absolute z-10 left-0 bottom-full mb-2 hidden group-hover:block w-48 bg-white shadow-lg rounded-md p-2">
                                        <p class="text-xs font-medium text-gray-900">李高工</p>
                                        <p class="text-xs text-gray-500">宁波智能制造研究院</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 标引标签 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-3">指南标引</h4>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full cursor-pointer hover:bg-blue-200">智能制造</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full cursor-pointer hover:bg-blue-200">工业机器人</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full cursor-pointer hover:bg-blue-200">智能装备</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full cursor-pointer hover:bg-blue-200">数字化工厂</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full cursor-pointer hover:bg-blue-200">关键技术攻关</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 打开指南详情弹窗
        function openGuideDetail() {
            document.getElementById('guideDetailModal').classList.remove('hidden');
        }
        
        // 关闭指南详情弹窗
        function closeGuideDetail() {
            document.getElementById('guideDetailModal').classList.add('hidden');
        }
        
        // 点击弹窗外部关闭
        document.getElementById('guideDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeGuideDetail();
            }
        });
        
        // 折叠/展开内容
        function toggleCollapse(element) {
            const content = element.nextElementSibling;
            const icon = element.querySelector('svg');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            }
        }
        
        // 初始化折叠状态
        document.addEventListener('DOMContentLoaded', function() {
            const collapseContents = document.querySelectorAll('.bg-gray-50 > div:not(:first-child)');
            collapseContents.forEach(content => {
                content.style.display = 'none';
            });
        });
    </script>
</body>
</html>