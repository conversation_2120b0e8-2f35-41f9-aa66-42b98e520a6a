import { NextRequest, NextResponse } from 'next/server';

// Dify配置
const DIFY_API_KEY = "app-plE9pju9RGzpYzEXqT97242P";
const DIFY_BASE_URL = "http://111.229.163.150/v1";
// const DIFY_API_KEY = "app-hXBah6VG2H7FcwuHQ6iSiI4A";
// const DIFY_BASE_URL = "http://10.26.37.118:8081/v1";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const messageId = searchParams.get('message_id');
  const user = searchParams.get('user') || 'user-123';
  
  if (!messageId) {
    return NextResponse.json(
      { error: '缺少消息ID参数', success: false },
      { status: 400 }
    );
  }
  
  try {
    const suggestedUrl = `${DIFY_BASE_URL}/messages/${messageId}/suggested?user=${user}`;
    
    const response = await fetch(suggestedUrl, {
      headers: {
        'Authorization': `Bearer ${DIFY_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`获取建议问题失败: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('获取建议问题错误:', error);
    return NextResponse.json(
      { error: '获取建议问题失败', success: false },
      { status: 500 }
    );
  }
} 