'use client'

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye } from "lucide-react"

const applicationData = [
  {
    id: "APP-001",
    name: "预算编制系统",
    type: "预算类",
    version: "v2.5.1",
    status: "运行中",
    users: 256,
    lastDeployment: "2024-03-15",
    performance: "良好",
    availability: "99.9%"
  },
  {
    id: "APP-002",
    name: "资金支付系统",
    type: "支付类",
    version: "v3.1.0",
    status: "运行中",
    users: 189,
    lastDeployment: "2024-03-10",
    performance: "良好",
    availability: "99.8%"
  },
  // ... 继续添加8条类似的数据
]

export function ApplicationTable() {
  return (
    <Card>
      <CardHeader className="border-b border-[#E5E9EF]">
        <CardTitle className="text-lg font-medium">应用系统清单</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="bg-[#F8FAFC] hover:bg-[#F8FAFC]">
              <TableHead>应用编号</TableHead>
              <TableHead>应用名称</TableHead>
              <TableHead>类型</TableHead>
              <TableHead>版本</TableHead>
              <TableHead>状态</TableHead>
              <TableHead>活跃用户</TableHead>
              <TableHead>最后部署</TableHead>
              <TableHead>性能</TableHead>
              <TableHead>可用性</TableHead>
              <TableHead>操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {applicationData.map((item) => (
              <TableRow key={item.id}>
                <TableCell>{item.id}</TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>{item.version}</TableCell>
                <TableCell>
                  <Badge variant="secondary" className="bg-green-50 text-green-600">
                    {item.status}
                  </Badge>
                </TableCell>
                <TableCell>{item.users}</TableCell>
                <TableCell>{item.lastDeployment}</TableCell>
                <TableCell>
                  <Badge variant="secondary" className="bg-blue-50 text-blue-600">
                    {item.performance}
                  </Badge>
                </TableCell>
                <TableCell>{item.availability}</TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
} 