unit CcMaterial;

interface

uses
  Classes;

type
  TCcMaterial = class
  private

    FMaterialid: Integer;
    FDdid: Integer;
    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FChxh: Integer;
    Fcmtypenum: Integer;
    FJkc: Integer;

    FCOLORA: string;
    FCOLORB: string;
    FCOLORC: string;
    FCOLORD: string;
    FCOLORE: string;
    FCOLORF: string;
    FYS_A: string;
    FML_A: string;
    FCK_A: string;
    FMF_A: string;
    FZLYJL_A: double;
    FFLYJL_A: double;
    FTGYJL_A: double;
    FFZ_A: string;
    FTGSL_A: Integer;
    FTGZL_A1: double;
    FTGZL_A2: double;
    FCALSTATE_A: string;
    FYS_B: string;
    FML_B: string;
    FCK_B: string;
    FMF_B: string;
    FZLYJL_B: double;
    FFLYJL_B: double;
    FTGYJL_B: double;
    FFZ_B: string;
    FTGSL_B: Integer;
    FTGZL_B1: double;
    FTGZL_B2: double;
    FCALSTATE_B: string;
    FYS_C: string;
    FML_C: string;
    FCK_C: string;
    FMF_C: string;
    FZLYJL_C: double;
    FFLYJL_C: double;
    FTGYJL_C: double;
    FFZ_C: string;
    FCALSTATE_C: string;
    FYS_D: string;
    FML_D: string;
    FCK_D: string;
    FMF_D: string;
    FZLYJL_D: double;
    FFLYJL_D: double;
    FTGYJL_D: double;
    FFZ_D: string;
    FCALSTATE_D: string;
    FYS_E: string;
    FML_E: string;
    FCK_E: string;
    FMF_E: string;
    FZLYJL_E: double;
    FFLYJL_E: double;
    FTGYJL_E: double;
    FFZ_E: string;
    FCALSTATE_E: string;
    FYS_F: string;
    FML_F: string;
    FCK_F: string;
    FMF_F: string;
    FZLYJL_F: double;
    FFLYJL_F: double;
    FTGYJL_F: double;
    FFZ_F: string;
    FCALSTATE_F: string;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Materialid: Integer read FMaterialid write FMaterialid;
    property Ddid: Integer read FDdid write FDdid;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Chxh: Integer read FChxh write FChxh;
    property COLORA: string read FCOLORA write FCOLORA;
    property COLORB: string read FCOLORB write FCOLORB;
    property COLORC: string read FCOLORC write FCOLORC;
    property COLORD: string read FCOLORD write FCOLORD;
    property COLORE: string read FCOLORE write FCOLORE;
    property COLORF: string read FCOLORF write FCOLORF;
    property cmtypenum: Integer read Fcmtypenum write Fcmtypenum;
    property Jkc: Integer read FJkc write FJkc;

    property YS_A: string read FYS_A write FYS_A;
    property ML_A: string read FML_A write FML_A;
    property CK_A: string read FCK_A write FCK_A;
    property MF_A: string read FMF_A write FMF_A;
    property ZLYJL_A: double read FZLYJL_A write FZLYJL_A;
    property FLYJL_A: double read FFLYJL_A write FFLYJL_A;
    property TGYJL_A: double read FTGYJL_A write FTGYJL_A;
    property FZ_A: string read FFZ_A write FFZ_A;
    property CALSTATE_A: string read FCALSTATE_A write FCALSTATE_A;
    property TGSL_A: Integer read FTGSL_A write FTGSL_A;
    property TGZL_A1: double read FTGZL_A1 write FTGZL_A1;
    property TGZL_A2: double read FTGZL_A2 write FTGZL_A2;

    property YS_B: string read FYS_B write FYS_B;
    property ML_B: string read FML_B write FML_B;
    property CK_B: string read FCK_B write FCK_B;
    property MF_B: string read FMF_B write FMF_B;
    property ZLYJL_B: double read FZLYJL_B write FZLYJL_B;
    property FLYJL_B: double read FFLYJL_B write FFLYJL_B;
    property TGYJL_B: double read FTGYJL_B write FTGYJL_B;
    property FZ_B: string read FFZ_B write FFZ_B;
    property CALSTATE_B: string read FCALSTATE_B write FCALSTATE_B;
    property TGSL_B: Integer read FTGSL_B write FTGSL_B;
    property TGZL_B1: double read FTGZL_B1 write FTGZL_B1;
    property TGZL_B2: double read FTGZL_B2 write FTGZL_B2;

    property YS_C: string read FYS_C write FYS_C;
    property ML_C: string read FML_C write FML_C;
    property CK_C: string read FCK_C write FCK_C;
    property MF_C: string read FMF_C write FMF_C;
    property ZLYJL_C: double read FZLYJL_C write FZLYJL_C;
    property FLYJL_C: double read FFLYJL_C write FFLYJL_C;
    property TGYJL_C: double read FTGYJL_C write FTGYJL_C;
    property FZ_C: string read FFZ_C write FFZ_C;
    property CALSTATE_C: string read FCALSTATE_C write FCALSTATE_C;

    property YS_D: string read FYS_D write FYS_D;
    property ML_D: string read FML_D write FML_D;
    property CK_D: string read FCK_D write FCK_D;
    property MF_D: string read FMF_D write FMF_D;
    property ZLYJL_D: double read FZLYJL_D write FZLYJL_D;
    property FLYJL_D: double read FFLYJL_D write FFLYJL_D;
    property TGYJL_D: double read FTGYJL_D write FTGYJL_D;
    property FZ_D: string read FFZ_D write FFZ_D;
    property CALSTATE_D: string read FCALSTATE_D write FCALSTATE_D;

    property YS_E: string read FYS_E write FYS_E;
    property ML_E: string read FML_E write FML_E;
    property CK_E: string read FCK_E write FCK_E;
    property MF_E: string read FMF_E write FMF_E;
    property ZLYJL_E: double read FZLYJL_E write FZLYJL_E;
    property FLYJL_E: double read FFLYJL_E write FFLYJL_E;
    property TGYJL_E: double read FTGYJL_E write FTGYJL_E;
    property FZ_E: string read FFZ_E write FFZ_E;
    property CALSTATE_E: string read FCALSTATE_E write FCALSTATE_E;

    property YS_F: string read FYS_F write FYS_F;
    property ML_F: string read FML_F write FML_F;
    property CK_F: string read FCK_F write FCK_F;
    property MF_F: string read FMF_F write FMF_F;
    property ZLYJL_F: double read FZLYJL_F write FZLYJL_F;
    property FLYJL_F: double read FFLYJL_F write FFLYJL_F;
    property TGYJL_F: double read FTGYJL_F write FTGYJL_F;
    property FZ_F: string read FFZ_F write FFZ_F;
    property CALSTATE_F: string read FCALSTATE_F write FCALSTATE_F;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
