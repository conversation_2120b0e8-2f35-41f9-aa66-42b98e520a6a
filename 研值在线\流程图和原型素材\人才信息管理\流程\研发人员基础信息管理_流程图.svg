<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员基础信息管理流程图</text>

  <!-- 阶段一：数据同步与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与标准化</text>
  
  <!-- 节点1: 数据源同步 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多渠道数据同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(市科技局、高校、院所)</text>
  </g>

  <!-- 节点2: 数据清洗融合 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据清洗与融合</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(科研项目、专利信息)</text>
  </g>

  <!-- 节点3: 标准化处理 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动补全标准化</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(基础信息字段)</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 320 165 Q 360 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 620 165 Q 660 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与筛选 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与筛选</text>

  <!-- 节点4: 用户进入页面 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(基础信息管理)</text>
  </g>

  <!-- 节点5: 条件筛选 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设置筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(业务需求导向)</text>
  </g>

  <!-- 节点6: 实时检索展示 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时检索展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(人员信息列表)</text>
  </g>

  <!-- 连接线 标准化 -> 用户进入 -->
  <path d="M 810 200 C 810 240, 300 270, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情操作与管理 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情操作与管理</text>

  <!-- 节点7: 详情钻取 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(全量基础数据)</text>
  </g>

  <!-- 节点8: 移除操作 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(确认提示+原因)</text>
  </g>

  <!-- 节点9: 批量操作 -->
  <g transform="translate(650, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(移除/导出)</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(归档/外部应用)</text>
  </g>

  <!-- 连接线 检索展示 -> 各操作 -->
  <path d="M 850 380 C 800 420, 300 450, 240 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 880 380 C 850 420, 550 450, 490 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 380 C 900 420, 750 450, 740 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 920 380 C 950 420, 1000 450, 990 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据管理与监控 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据管理与监控</text>
  
  <!-- 节点11: 操作日志 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">全程操作日志归档</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">数据完整性</tspan>
      <tspan dx="40">可溯源性</tspan>
      <tspan dx="40">合规性保障</tspan>
    </text>
  </g>

  <!-- 连接线 各操作 -> 操作日志 -->
  <path d="M 240 560 C 240 600, 600 640, 600 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 490 560 C 490 600, 650 640, 650 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 740 560 C 740 600, 700 640, 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 990 560 C 990 600, 750 640, 750 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：操作日志 -> 数据同步 -->
  <path d="M 500 700 C 200 750, 100 800, 50 700 C 50 300, 100 100, 210 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="80" y="420" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 80, 420)">数据质量反馈</text>

  <!-- 反馈循环：数据导出 -> 条件筛选 -->
  <path d="M 1000 490 C 1100 400, 1150 350, 1100 300 C 1050 250, 700 280, 600 310" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="350" text-anchor="middle" font-size="11" fill="#666">导出需求优化</text>

</svg>