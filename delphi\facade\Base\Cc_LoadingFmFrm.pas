unit Cc_LoadingFmFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingFmForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    RzLabel2: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingFmForm: TLoadingFmForm;

implementation

{$R *.dfm}

procedure TLoadingFmForm.FormCreate(Sender: TObject);
begin
  LoadingFmForm.left := (screen.width - LoadingFmForm.width) div 2;
  LoadingFmForm.top := (screen.height - LoadingFmForm.height) div 2;
end;

procedure TLoadingFmForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
