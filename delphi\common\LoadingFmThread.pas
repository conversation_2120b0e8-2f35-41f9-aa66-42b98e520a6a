unit LoadingFmThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingFmFrm;

type
  TThreadFmModel = class(TThread)
  private
    FLoadingFmForm: TLoadingFmForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingFmForm: TLoadingFmForm read FLoadingFmForm
      write FLoadingFmForm;

  end;

var
  LoadingFmForm: TLoadingFmForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadFmModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadFmModel.CallVclMethod;
begin
  // LoadingPicForm.Show;
  // LoadingPicForm.Update;
  // LoadingPicForm.RxGIFAnimator1.Animate := true;
  // LoadingPicForm.Update;
end;

procedure TThreadFmModel.DoTerminate;
begin
  LoadingFmForm.Close;
end;

end.
