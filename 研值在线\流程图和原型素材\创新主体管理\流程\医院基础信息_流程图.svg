<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">医院基础信息系统流程图</text>

  <!-- 阶段一：数据同步与处理 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与处理</text>
  
  <!-- 节点1: 卫健委接口 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">卫健委信息公开接口</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">基础数据与荣誉证书</text>
  </g>

  <!-- 节点2: 医院内部系统 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">医院内部信息化系统</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">内部数据与扫描件</text>
  </g>

  <!-- 节点3: 数据处理 -->
  <g transform="translate(850, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">字段映射、图片压缩、去重</text>
  </g>

  <!-- 节点4: 分类聚合 -->
  <g transform="translate(590, 250)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分类聚合服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">学科、平台、人才数据聚合</text>
  </g>

  <!-- 连接线：数据源到处理 -->
  <path d="M 420 165 C 550 165, 650 165, 850 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 165 C 780 165, 820 165, 850 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线：处理到聚合 -->
  <path d="M 960 200 C 960 225, 810 225, 810 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：页面展示与交互 -->
  <text x="700" y="380" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：页面展示与交互</text>

  <!-- 节点5: 前端页面 -->
  <g transform="translate(150, 420)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">医院基础信息页面</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">概况卡片、详细介绍</text>
  </g>

  <!-- 节点6: 学科平台面板 -->
  <g transform="translate(450, 420)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">学科与平台面板</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">重点学科、科研平台</text>
  </g>

  <!-- 节点7: 人才队伍 -->
  <g transform="translate(750, 420)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才队伍卡片</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人才库、专家信息</text>
  </g>

  <!-- 节点8: 荣誉画廊 -->
  <g transform="translate(1050, 420)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">荣誉画廊组件</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">证书展示、高清弹窗</text>
  </g>

  <!-- 连接线：聚合到前端 -->
  <path d="M 590 320 C 400 350, 350 380, 260 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 320 C 650 350, 600 380, 560 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 320 C 750 350, 800 380, 860 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 320 C 950 350, 1000 380, 1160 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：用户交互与日志 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：用户交互与日志</text>
  
  <!-- 节点9: 跳转服务 -->
  <g transform="translate(200, 620)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">跳转服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">官网跳转、二维码扫描</text>
  </g>

  <!-- 节点10: 模块加载 -->
  <g transform="translate(500, 620)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模块加载服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">学科详情、人才库加载</text>
  </g>

  <!-- 节点11: 日志记录 -->
  <g transform="translate(800, 620)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志记录服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">访问记录、行为分析</text>
  </g>

  <!-- 连接线：页面到服务 -->
  <path d="M 260 490 C 280 550, 290 580, 310 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 560 490 C 580 550, 590 580, 610 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 860 490 C 880 550, 890 580, 910 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1160 490 C 1100 550, 1000 580, 910 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：数据更新 -->
  <path d="M 700 250 C 750 200, 750 150, 700 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="770" y="190" text-anchor="middle" font-size="11" fill="#666">定期同步</text>

  <!-- 反馈循环：性能优化 -->
  <path d="M 910 620 C 1100 580, 1200 500, 1200 420" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="520" text-anchor="middle" font-size="11" fill="#666">优化反馈</text>

  <!-- 反馈循环：使用分析 -->
  <path d="M 800 690 C 600 750, 400 750, 200 690" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="500" y="770" text-anchor="middle" font-size="11" fill="#666">使用分析与内容更新</text>

</svg>