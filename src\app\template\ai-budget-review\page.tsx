'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  FileText, Search, Filter, Brain, BarChart3, 
  CheckCircle, AlertTriangle, XCircle, ArrowRight,
  Edit, LayoutList, Settings, FileCode, Play, Trash,
  Building, MapPin, Calendar, DollarSign, FileImage
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

// 使用脱敏处理后的农村公益事业建设项目数据
const projects = [
  {
    id: "NCGY2024A01",
    name: "XX村老年活动中心及周边环境提升项目",
    type: "农村公益事业",
    department: "A镇",
    budget: 880000,
    fundSupport: 790000,
    submitDate: "2024-04-10",
    status: "pending",
    risk: "medium",
    village: "XX村",
    endDate: "2024-12-31",
    description: "该村持续推进美丽乡村建设，着力打造景美业兴的样板乡村，建设内容包括老年活动中心立面改造和内部装潢，周边设施改造建设包括室外厕所改造、外部道路修补提升、周边水系围栏河埠头建设、搭建休闲区及绿化提升等。",
    matchedRules: ["老年活动中心", "河埠头"],
    aiInsights: "检测到'河埠头'关键词，可能涉及小型码头或渡口建设，需进一步确认是否为简单的环境提升或新建码头设施。",
    whitelist: false
  },
  {
    id: "NCGY2024A02",
    name: "A镇生态湿地公园",
    type: "农村公益事业",
    department: "A镇",
    budget: 920000,
    fundSupport: 830000,
    submitDate: "2024-04-08",
    status: "pending",
    risk: "low",
    village: "YY村",
    endDate: "2024-12-31",
    description: "湿地公园占地面积适中，项目建设内容包括土方回填、土地平整、水体开挖、水上休闲步道建设、水体周边绿化种植。",
    matchedRules: [],
    aiInsights: "项目符合生态文明建设要求，主要为环境提升类项目，未检测到违规内容。"
  },
  {
    id: "NCGY2024B01",
    name: "ZZ村老年协会改造提升及周边停车场建设",
    type: "农村公益事业",
    department: "B镇",
    budget: 750000,
    fundSupport: 680000,
    submitDate: "2024-04-07",
    status: "reviewing",
    risk: "low",
    village: "ZZ村",
    endDate: "2024-12-31",
    description: "对村内老年协会进行改造提升及周边停车场的建设，改造提升后的老年协会和停车场面积适中，满足村民需求。",
    matchedRules: [],
    aiInsights: "项目为老年协会设施改造和停车场建设，符合农村公共服务设施建设要求，未检测到违规内容。"
  },
  {
    id: "NCGY2024B02",
    name: "AA村老年活动中心项目",
    type: "农村公益事业",
    department: "B镇",
    budget: 1100000,
    fundSupport: 950000,
    submitDate: "2024-04-05",
    status: "pending",
    risk: "medium",
    village: "AA村",
    endDate: "2024-12-31",
    description: "新建整个文化礼堂两层结构，包含多功能活动区域和公共卫生设施，以及室外公共活动场地改造。",
    matchedRules: ["文化礼堂"],
    aiInsights: "虽包含'文化礼堂'关键词，但经AI内容语义分析，该项目为村级公共文化服务设施，符合农村公共文化服务体系建设要求，非传统宗族祠堂，建议审核通过。"
  },
  {
    id: "NCGY2024B03",
    name: "BB村道路建设工程",
    type: "农村公益事业",
    department: "B镇",
    budget: 680000,
    fundSupport: 610000,
    submitDate: "2024-04-03",
    status: "pending",
    risk: "low",
    village: "BB村",
    endDate: "2024-12-31",
    description: "本项目拟对村内农田进出道路进行硬化，为方便村民农作及日常进出，计划在原有的砂石路基础上建设水泥路面。",
    matchedRules: [],
    aiInsights: "项目为农村基础设施改善，符合要求。道路类型为农村内部道路，非桥梁工程，建议审核通过。"
  },
  {
    id: "NCGY2024B04",
    name: "CC村老年活动室改造及周边配套提升",
    type: "农村公益事业",
    department: "B镇",
    budget: 520000,
    fundSupport: 470000,
    submitDate: "2024-04-01",
    status: "approved",
    risk: "low",
    village: "CC村",
    endDate: "2024-12-31",
    description: "主要建设内容包括村内平房进行改造，内部基础设施改造及周边附属配套提升等。",
    matchedRules: [],
    aiInsights: "项目为老年活动室改造，符合农村公共服务设施建设要求，未检测到违规内容。"
  },
  {
    id: "NCGY2024B05",
    name: "DD村祠堂修缮工程",
    type: "农村公益事业",
    department: "B镇",
    budget: 630000,
    fundSupport: 570000,
    submitDate: "2024-04-12",
    status: "pending",
    risk: "high",
    village: "DD村",
    endDate: "2024-12-31",
    description: "对村内宗祠进行修缮，包括屋顶维修、立柱加固、墙面修补粉刷、地面铺设及排水系统改造等。",
    matchedRules: ["祠堂", "宗祠"],
    aiInsights: "AI检测到项目内容为祠堂修缮工程，属于宗族祠堂类项目，不符合农村公益事业建设财政奖补范围，建议不予批准。",
    rejected: true
  },
  {
    id: "NCGY2024A03",
    name: "EE村古桥修复工程",
    type: "农村公益事业",
    department: "A镇",
    budget: 580000,
    fundSupport: 520000,
    submitDate: "2024-04-11",
    status: "pending",
    risk: "high",
    village: "EE村",
    endDate: "2024-12-31",
    description: "修复村内传统石拱桥一座，包括桥面修缮、栏杆重建、桥墩加固等工程，为当地历史文化景观。",
    matchedRules: ["桥", "石拱桥"],
    aiInsights: "AI检测到项目内容为桥梁修复工程，属于交通基础设施中的桥梁项目，根据规定不在农村公益事业建设财政奖补范围内，建议不予批准。",
    rejected: true
  }
];

// 更新审核规则
const auditRules = [
  {
    id: "R001",
    name: "祠堂类项目检测",
    description: "检测项目描述中是否包含宗族祠堂相关关键词",
    keywords: [
      "祠堂", "宗祠", "宗族祠堂", "家庙", "家祠", "宗庙"
    ],
    severity: "high",
    enabled: true
  },
  {
    id: "R002",
    name: "桥梁类项目检测",
    description: "检测项目描述中是否包含桥梁建设相关关键词",
    keywords: ["桥", "桥梁", "石桥", "水泥桥", "钢桥", "人行桥", "车行桥", "石拱桥"],
    severity: "high",
    enabled: true
  },
  {
    id: "R003",
    name: "预算金额异常检测",
    description: "检测项目预算是否超过同类项目平均水平的50%",
    severity: "medium",
    enabled: true
  },
  {
    id: "R004",
    name: "寺庙宗教场所检测",
    description: "检测项目是否涉及宗教场所建设",
    keywords: ["寺庙", "道观", "庙宇", "教堂", "佛堂", "神龛"],
    severity: "high",
    enabled: true
  }
];

// 白名单规则
const whitelistRules = [
  {
    id: "W001",
    name: "文化类公共设施豁免",
    description: "村级文化礼堂、文化站等公共文化设施不视为祠堂类建筑",
    fundType: "农村公共文化服务体系建设资金",
    keywords: ["文化礼堂", "文化站", "文化活动中心"],
    enabled: true
  },
  {
    id: "W002",
    name: "老年设施豁免",
    description: "老年活动中心、老年协会等养老设施不视为祠堂类建筑",
    fundType: "农村养老服务设施建设资金",
    keywords: ["老年活动中心", "老年协会", "老年之家"],
    enabled: true
  }
];

export default function AIBudgetReviewPage() {
  const [activeTab, setActiveTab] = useState('projects');
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterRisk, setFilterRisk] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  // 处理筛选
  const filteredProjects = projects.filter(project => {
    // 状态筛选
    if (filterStatus !== 'all' && project.status !== filterStatus) {
      return false;
    }
    
    // 风险等级筛选
    if (filterRisk !== 'all' && project.risk !== filterRisk) {
      return false;
    }
    
    // 搜索筛选
    if (searchQuery && !project.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
        !project.village.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    return true;
  });
  
  const project = selectedProject ? projects.find(p => p.id === selectedProject) : null;
  
  return (
    <div className="flex-1 p-6 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="h-[calc(100vh-120px)] flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-semibold text-indigo-800 flex items-center">
            <Brain className="mr-2 h-6 w-6 text-indigo-600" />
            农村公益事业建设项目AI智能审核
          </h1>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1 border-indigo-200 text-indigo-700 bg-indigo-50 hover:bg-indigo-100"
            >
              <BarChart3 size={16} className="text-indigo-600" />
              项目统计
            </Button>
            
            <Button 
              size="sm" 
              className="flex items-center gap-1 bg-indigo-600 hover:bg-indigo-700 text-white"
            >
              <FileText size={16} />
              新建项目
            </Button>
          </div>
        </div>
        
        <Card className="flex-1 shadow-lg border-indigo-100 bg-white">
          <Tabs 
            defaultValue="projects" 
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full h-full"
          >
            <CardHeader className="border-b border-indigo-100 bg-indigo-50/50 p-4">
              <div className="flex justify-between items-center">
                <TabsList className="bg-indigo-100">
                  <TabsTrigger value="projects" className="flex items-center data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                    <FileText size={16} className="mr-2" />
                    项目清单
                  </TabsTrigger>
                  <TabsTrigger value="rules" className="flex items-center data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                    <Settings size={16} className="mr-2" />
                    规则管理
                  </TabsTrigger>
                  <TabsTrigger value="whitelist" className="flex items-center data-[state=active]:bg-indigo-600 data-[state=active]:text-white">
                    <CheckCircle size={16} className="mr-2" />
                    白名单管理
                  </TabsTrigger>
                </TabsList>
                
                {activeTab === 'projects' && (
                  <div className="flex gap-2">
                    <Select 
                      defaultValue="all"
                      value={filterStatus}
                      onValueChange={setFilterStatus}
                    >
                      <SelectTrigger className="w-[140px] h-9 text-sm border-indigo-200 bg-white">
                        <SelectValue placeholder="筛选状态" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部状态</SelectItem>
                        <SelectItem value="pending">待审核</SelectItem>
                        <SelectItem value="reviewing">审核中</SelectItem>
                        <SelectItem value="approved">已通过</SelectItem>
                        <SelectItem value="rejected">已拒绝</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Select 
                      defaultValue="all"
                      value={filterRisk}
                      onValueChange={setFilterRisk}
                    >
                      <SelectTrigger className="w-[140px] h-9 text-sm border-indigo-200 bg-white">
                        <SelectValue placeholder="风险等级" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">全部风险</SelectItem>
                        <SelectItem value="low">低风险</SelectItem>
                        <SelectItem value="medium">中风险</SelectItem>
                        <SelectItem value="high">高风险</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2 h-4 w-4 text-gray-500" />
                      <Input 
                        placeholder="搜索项目或村名..." 
                        className="pl-8 h-9 text-sm border-indigo-200 focus-visible:ring-indigo-400 w-[220px] bg-white"
                        value={searchQuery}
                        onChange={e => setSearchQuery(e.target.value)}
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardHeader>
            
            <CardContent className="p-0 flex-1 flex flex-col overflow-hidden">
              <TabsContent value="projects" className="m-0 p-0 h-full flex flex-col overflow-hidden">
                {selectedProject ? (
                  <div className="flex-1 flex flex-col">
                    <div className="p-4 border-b border-indigo-100 bg-indigo-50/30 flex justify-between items-center">
                      <div className="flex items-center">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="mr-2 text-indigo-700 hover:bg-indigo-100"
                          onClick={() => setSelectedProject(null)}
                        >
                          <ArrowRight size={16} className="mr-1 transform rotate-180" /> 
                          返回列表
                        </Button>
                        <h3 className="font-medium text-indigo-800">项目详情：{project?.name}</h3>
                        <Badge 
                          className={`ml-4 ${
                            project?.risk === 'low' ? 'bg-green-100 text-green-800' : 
                            project?.risk === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-red-100 text-red-800'
                          }`}
                        >
                          {project?.risk === 'low' ? '低风险' : 
                           project?.risk === 'medium' ? '中风险' : '高风险'}
                        </Badge>
                      </div>
                      
                      {!project?.rejected ? (
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="border-indigo-200 text-indigo-700 bg-white hover:bg-indigo-50"
                          >
                            <Edit size={14} className="mr-1" />
                            编辑
                          </Button>
                          <Button 
                            size="sm" 
                            className="bg-indigo-600 hover:bg-indigo-700 text-white"
                          >
                            审核通过
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="border-red-200 text-red-700 bg-white hover:bg-red-50"
                          >
                            <XCircle size={14} className="mr-1" />
                            驳回项目
                          </Button>
                        </div>
                      )}
                    </div>
                    
                    <ScrollArea className="flex-1 p-4 bg-white">
                      <div className="max-w-4xl mx-auto">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card className="shadow-sm border-indigo-100 overflow-hidden">
                            <CardHeader className="p-4 pb-2 bg-indigo-50/50 border-b border-indigo-100">
                              <CardTitle className="text-md text-indigo-800">基本信息</CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 bg-white">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <FileText size={14} className="mr-1 text-indigo-500" />
                                    项目编号
                                  </div>
                                  <div className="text-gray-800">{project?.id}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <Calendar size={14} className="mr-1 text-indigo-500" />
                                    提交日期
                                  </div>
                                  <div className="text-gray-800">{project?.submitDate}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <Calendar size={14} className="mr-1 text-indigo-500" />
                                    完成期限
                                  </div>
                                  <div className="text-gray-800">{project?.endDate}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <FileText size={14} className="mr-1 text-indigo-500" />
                                    项目类型
                                  </div>
                                  <div className="text-gray-800">{project?.type}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <Building size={14} className="mr-1 text-indigo-500" />
                                    所属部门
                                  </div>
                                  <div className="text-gray-800">{project?.department}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <MapPin size={14} className="mr-1 text-indigo-500" />
                                    所在村
                                  </div>
                                  <div className="text-gray-800">{project?.village}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <DollarSign size={14} className="mr-1 text-indigo-500" />
                                    计划总投资
                                  </div>
                                  <div className="text-gray-800">¥{project?.budget.toLocaleString()}</div>
                                </div>
                                <div>
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <DollarSign size={14} className="mr-1 text-indigo-500" />
                                    财政补助
                                  </div>
                                  <div className="text-gray-800">¥{project?.fundSupport.toLocaleString()}</div>
                                </div>
                                <div className="col-span-2">
                                  <div className="text-sm text-gray-500 mb-1 flex items-center">
                                    <FileImage size={14} className="mr-1 text-indigo-500" />
                                    审核状态
                                  </div>
                                  <div className="text-gray-800">
                                    <Badge 
                                      className={`${
                                        project?.status === 'pending' ? 'bg-gray-100 text-gray-800' : 
                                        project?.status === 'reviewing' ? 'bg-blue-100 text-blue-800' : 
                                        project?.status === 'approved' ? 'bg-green-100 text-green-800' : 
                                        'bg-red-100 text-red-800'
                                      }`}
                                    >
                                      {project?.status === 'pending' ? '待审核' : 
                                       project?.status === 'reviewing' ? '审核中' : 
                                       project?.status === 'approved' ? '已通过' : '已拒绝'}
                                    </Badge>
                                  </div>
                                </div>
                              </div>
                              
                              <div className="mt-4">
                                <div className="text-sm text-gray-500 mb-1">项目描述</div>
                                <div className="text-gray-800 p-3 bg-gray-50 rounded-lg border border-gray-200">
                                  {project?.description}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                          
                          <Card className="shadow-sm border-indigo-100 overflow-hidden">
                            <CardHeader className="p-4 pb-2 bg-indigo-50/50 border-b border-indigo-100">
                              <CardTitle className="text-md text-indigo-800">AI智能分析</CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 bg-white">
                              <div className="space-y-4">
                                {project?.rejected ? (
                                  <div className="p-3 rounded-lg border bg-red-50 border-red-100">
                                    <div className="flex items-start">
                                      <XCircle size={16} className="mr-2 text-red-500 mt-0.5" />
                                      <div>
                                        <div className="text-sm font-medium text-red-700">
                                          项目不符合农村公益事业建设要求
                                        </div>
                                        <div className="text-xs text-gray-700 mt-1">
                                          {project.aiInsights}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ) : project?.risk === 'high' || project?.risk === 'medium' ? (
                                  <div 
                                    className={`p-3 rounded-lg border ${
                                      project?.risk === 'medium' ? 'bg-yellow-50 border-yellow-100' : 
                                      'bg-red-50 border-red-100'
                                    }`}
                                  >
                                    <div className="flex items-start">
                                      {project?.risk === 'medium' ? (
                                        <AlertTriangle size={16} className="mr-2 text-yellow-500 mt-0.5" />
                                      ) : (
                                        <XCircle size={16} className="mr-2 text-red-500 mt-0.5" />
                                      )}
                                      <div>
                                        <div className={`text-sm font-medium ${
                                          project?.risk === 'medium' ? 'text-yellow-700' : 'text-red-700'
                                        }`}>
                                          {project?.matchedRules?.length > 0 ? `检测到关键词：${project?.matchedRules?.join('、')}` : "风险提示"}
                                        </div>
                                        <div className="text-xs text-gray-700 mt-1">
                                          {project.aiInsights}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  <div className="p-3 rounded-lg border bg-green-50 border-green-100">
                                    <div className="flex items-start">
                                      <CheckCircle size={16} className="mr-2 text-green-500 mt-0.5" />
                                      <div>
                                        <div className="text-sm font-medium text-green-700">
                                          项目符合农村公益事业建设要求
                                        </div>
                                        <div className="text-xs text-gray-700 mt-1">
                                          {project.aiInsights}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                
                                <div className="p-3 bg-indigo-50 rounded-lg border border-indigo-100">
                                  <div className="flex items-start">
                                    <Brain size={16} className="mr-2 text-indigo-600 mt-0.5" />
                                    <div>
                                      <div className="text-sm font-medium text-indigo-700">
                                        AI审核说明
                                      </div>
                                      <div className="text-xs text-gray-700 mt-1">
                                        AI系统不仅通过关键词匹配进行审核，还会进行语义分析，理解项目上下文和意图。例如"文化礼堂"与传统"祠堂"有明显区别，AI能识别其公共服务属性；同时能区分小型环境整治中的"河埠头"和大型桥梁工程，提高审核准确性，减少误判。
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                
                                {!project?.rejected && (
                                  <div>
                                    <h3 className="text-sm font-medium text-gray-700 mb-2">审核意见</h3>
                                    <Textarea 
                                      placeholder="请输入审核意见..." 
                                      className="border-indigo-200 bg-white"
                                      rows={3}
                                    />
                                  </div>
                                )}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                        
                        <div className="mt-6">
                          <Card className="shadow-sm border-indigo-100 overflow-hidden">
                            <CardHeader className="p-4 pb-2 bg-indigo-50/50 border-b border-indigo-100">
                              <CardTitle className="text-md text-indigo-800">农村公益事业项目建设标准查验</CardTitle>
                            </CardHeader>
                            <CardContent className="p-4 bg-white">
                              <div className="text-sm text-gray-700 mb-4">
                                <p>根据《农村公益事业财政奖补项目管理办法》，农村公益事业建设项目应当符合以下标准：</p>
                                <ul className="list-disc pl-5 mt-2 space-y-1">
                                  <li>项目应为农村基础设施和公共服务设施，包括村内道路、饮水工程、文化体育设施、环境整治等</li>
                                  <li>不得用于修建祠堂、庙宇等非公益性建筑</li>
                                  <li>不得用于建设桥梁、隧道等大型交通基础设施</li>
                                  <li>不得用于宗族性、排他性设施建设</li>
                                  <li>项目应当惠及村集体或广大村民，不得用于个人或小团体</li>
                                </ul>
                              </div>
                              
                              <div className="grid grid-cols-2 gap-4">
                                <div className="bg-green-50 p-3 rounded-lg border border-green-100">
                                  <h4 className="text-sm font-medium text-green-800 mb-2">符合公益事业建设的项目类型</h4>
                                  <ul className="text-xs text-gray-700 space-y-1">
                                    <li className="flex items-center">
                                      <CheckCircle size={12} className="mr-2 text-green-600" />
                                      村内道路硬化、农田水利设施
                                    </li>
                                    <li className="flex items-center">
                                      <CheckCircle size={12} className="mr-2 text-green-600" />
                                      村级文化礼堂、文化活动中心
                                    </li>
                                    <li className="flex items-center">
                                      <CheckCircle size={12} className="mr-2 text-green-600" />
                                      老年活动中心、老年之家
                                    </li>
                                    <li className="flex items-center">
                                      <CheckCircle size={12} className="mr-2 text-green-600" />
                                      村庄环境整治、垃圾处理设施
                                    </li>
                                  </ul>
                                </div>
                                
                                <div className="bg-red-50 p-3 rounded-lg border border-red-100">
                                  <h4 className="text-sm font-medium text-red-800 mb-2">不符合公益事业建设的项目类型</h4>
                                  <ul className="text-xs text-gray-700 space-y-1">
                                    <li className="flex items-center">
                                      <XCircle size={12} className="mr-2 text-red-600" />
                                      宗族祠堂、庙宇等宗教场所
                                    </li>
                                    <li className="flex items-center">
                                      <XCircle size={12} className="mr-2 text-red-600" />
                                      村外道路、桥梁等交通设施
                                    </li>
                                    <li className="flex items-center">
                                      <XCircle size={12} className="mr-2 text-red-600" />
                                      个人住宅或商业设施
                                    </li>
                                    <li className="flex items-center">
                                    <XCircle size={12} className="mr-2 text-red-600" />
                                      个人住宅或商业设施
                                    </li>
                                    <li className="flex items-center">
                                      <XCircle size={12} className="mr-2 text-red-600" />
                                      纯装饰性景观工程
                                    </li>
                                  </ul>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    </ScrollArea>
                  </div>
                ) : (
                  <div className="flex-1 overflow-auto bg-white">
                    <div className="grid grid-cols-1 divide-y divide-indigo-100">
                      {filteredProjects.map(project => (
                        <div 
                          key={project.id}
                          className="p-4 hover:bg-indigo-50 transition-colors cursor-pointer"
                          onClick={() => setSelectedProject(project.id)}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center">
                                <h3 className="font-medium text-indigo-800">{project.name}</h3>
                                {project.rejected && (
                                  <Badge className="ml-2 bg-red-100 text-red-800 text-xs">建议驳回</Badge>
                                )}
                                {project.whitelist && (
                                  <Badge className="ml-2 bg-indigo-100 text-indigo-800 text-xs">白名单</Badge>
                                )}
                              </div>
                              <div className="mt-1 text-sm text-gray-600">
                                <span className="inline-flex items-center mr-3">
                                  <Building size={14} className="mr-1 text-indigo-500" />
                                  {project.department}
                                </span> 
                                <span className="inline-flex items-center mr-3">
                                  <MapPin size={14} className="mr-1 text-indigo-500" />
                                  {project.village}
                                </span> 
                                <span className="inline-flex items-center">
                                  <DollarSign size={14} className="mr-1 text-indigo-500" />
                                  ¥{project.budget.toLocaleString()}
                                </span>
                              </div>
                              <div className="mt-2 text-sm flex items-center flex-wrap">
                                <Badge 
                                  className={`mr-2 ${
                                    project.status === 'pending' ? 'bg-gray-100 text-gray-800' : 
                                    project.status === 'reviewing' ? 'bg-blue-100 text-blue-800' : 
                                    project.status === 'approved' ? 'bg-green-100 text-green-800' : 
                                    'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {project.status === 'pending' ? '待审核' : 
                                   project.status === 'reviewing' ? '审核中' : 
                                   project.status === 'approved' ? '已通过' : '已拒绝'}
                                </Badge>
                                <Badge 
                                  className={`mr-2 ${
                                    project.risk === 'low' ? 'bg-green-100 text-green-800' : 
                                    project.risk === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                    'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {project.risk === 'low' ? '低风险' : 
                                   project.risk === 'medium' ? '中风险' : '高风险'}
                                </Badge>
                                <span className="text-gray-500">{project.submitDate}</span>
                                
                                {project.matchedRules && project.matchedRules.length > 0 && (
                                  <div className="mt-1 flex flex-wrap gap-1">
                                    {project.matchedRules.map((rule, idx) => (
                                      <Badge key={idx} variant="outline" className="text-xs text-gray-700 border-gray-300 bg-gray-50">
                                        {rule}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                            <Button variant="ghost" size="sm" className="text-indigo-700 hover:bg-indigo-100">
                              查看详情
                            </Button>
                          </div>
                        </div>
                      ))}
                      
                      {filteredProjects.length === 0 && (
                        <div className="p-8 text-center text-gray-500">
                          没有找到匹配的项目
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="rules" className="m-0 p-4 h-full overflow-auto bg-white">
                <div className="flex justify-between mb-4">
                  <h3 className="text-lg font-medium text-indigo-800">审核规则管理</h3>
                  <Button size="sm" className="flex items-center gap-1 bg-indigo-600 hover:bg-indigo-700 text-white">
                    <FileCode size={14} className="mr-1" />
                    新建规则
                  </Button>
                </div>
                
                <div className="space-y-4">
                  {auditRules.map(rule => (
                    <Card key={rule.id} className="shadow-sm border-indigo-100 overflow-hidden">
                      <CardContent className="p-4 bg-white">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <h4 className="font-medium text-indigo-800">{rule.name}</h4>
                              <Badge 
                                className={`ml-2 ${
                                  rule.severity === 'low' ? 'bg-green-100 text-green-800' : 
                                  rule.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                                  'bg-red-100 text-red-800'
                                }`}
                              >
                                {rule.severity === 'low' ? '低风险' : 
                                 rule.severity === 'medium' ? '中风险' : '高风险'}
                              </Badge>
                              <div className="ml-auto flex items-center">
                                <Switch id={`rule-${rule.id}`} checked={rule.enabled} className="data-[state=checked]:bg-indigo-600" />
                                <Label htmlFor={`rule-${rule.id}`} className="ml-2">
                                  {rule.enabled ? '已启用' : '已禁用'}
                                </Label>
                              </div>
                            </div>
                            
                            <div className="mt-2 text-sm text-gray-600">
                              {rule.description}
                            </div>
                            
                            {rule.keywords && (
                              <div className="mt-3">
                                <div className="text-sm text-gray-700 mb-1">关键词列表:</div>
                                <div className="flex flex-wrap gap-1">
                                  {rule.keywords.map((keyword, idx) => (
                                    <Badge key={idx} className="bg-gray-100 text-gray-800 text-xs">
                                      {keyword}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex gap-1 ml-4">
                            <Button variant="ghost" size="sm" className="text-indigo-700 hover:bg-indigo-100">
                              <Edit size={14} />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-700 hover:bg-red-100">
                              <Trash size={14} />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
              
              <TabsContent value="whitelist" className="m-0 p-4 h-full overflow-auto bg-white">
                <div className="flex justify-between mb-4">
                  <h3 className="text-lg font-medium text-indigo-800">白名单规则管理</h3>
                  <Button size="sm" className="flex items-center gap-1 bg-indigo-600 hover:bg-indigo-700 text-white">
                    <FileCode size={14} className="mr-1" />
                    新建白名单规则
                  </Button>
                </div>
                
                <div className="space-y-4">
                  {whitelistRules.map(rule => (
                    <Card key={rule.id} className="shadow-sm border-indigo-100 overflow-hidden">
                      <CardContent className="p-4 bg-white">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <h4 className="font-medium text-indigo-800">{rule.name}</h4>
                              <div className="ml-auto flex items-center">
                                <Switch id={`whitelist-${rule.id}`} checked={rule.enabled} className="data-[state=checked]:bg-indigo-600" />
                                <Label htmlFor={`whitelist-${rule.id}`} className="ml-2">
                                  {rule.enabled ? '已启用' : '已禁用'}
                                </Label>
                              </div>
                            </div>
                            
                            <div className="mt-2 text-sm text-gray-600">
                              {rule.description}
                            </div>
                            
                            <div className="mt-2 text-sm text-gray-600">
                              <span className="font-medium">资金类型:</span> {rule.fundType}
                            </div>
                            
                            <div className="mt-3">
                              <div className="text-sm text-gray-700 mb-1">豁免关键词:</div>
                              <div className="flex flex-wrap gap-1">
                                {rule.keywords.map((keyword, idx) => (
                                  <Badge key={idx} className="bg-indigo-50 text-indigo-700 text-xs">
                                    {keyword}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-1 ml-4">
                            <Button variant="ghost" size="sm" className="text-indigo-700 hover:bg-indigo-100">
                              <Edit size={14} />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-700 hover:bg-red-100">
                              <Trash size={14} />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </TabsContent>
            </CardContent>
          </Tabs>
        </Card>
      </div>
    </div>
  )
}