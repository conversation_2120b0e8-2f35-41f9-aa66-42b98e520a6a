<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业监测大屏 - 数字化企业监测分析平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">企业监测大屏</h1>
                        <p class="text-sm text-gray-600">区域企业研发与创新能力深度分析平台</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full pulse-dot"></div>
                        <span>系统正常运行</span>
                    </div>
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 策略提示区 -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white mb-8">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-lightbulb text-2xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold mb-2">智能策略提示</h2>
                    <p class="text-blue-100 mb-4">自定义规则进行挖掘筛选，发现潜在价值企业</p>
                    <div class="bg-white/10 rounded-lg p-4">
                        <p class="text-sm leading-relaxed">
                            <strong>示例策略：</strong> 通过设置"注册2年内" + "近三年授权发明专利≥5项" + "研发费用增速≥10%" + "博士人员≥10人" + "有市级及以上高层次人才"等条件，系统可智能挖掘潜在种子企业。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心统计数据 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">监测企业总数</p>
                        <p class="text-2xl font-bold text-gray-900">12,847</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上月 +5.2%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">高新技术企业</p>
                        <p class="text-2xl font-bold text-gray-900">3,256</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上月 +8.7%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">研发投入总额</p>
                        <p class="text-2xl font-bold text-gray-900">286.5亿</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +12.3%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">预警企业数量</p>
                        <p class="text-2xl font-bold text-gray-900">127</p>
                        <p class="text-red-600 text-xs">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            需要关注
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块入口 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            <!-- 企业储备库模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-database text-blue-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">企业储备库</h3>
                            <p class="text-sm text-gray-600">分类储备重点企业名单</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">种子企业</span>
                            <span class="font-medium">412家</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">科技型中小企业</span>
                            <span class="font-medium">2,847家</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">高新技术企业</span>
                            <span class="font-medium">3,256家</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='enterprise-reserve.html'" 
                            class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 企业挖掘模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-search text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">企业挖掘</h3>
                            <p class="text-sm text-gray-600">多维条件筛选工具</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">技术领域</span>
                            <span class="font-medium">510分类</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">产业领域</span>
                            <span class="font-medium">361分类</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">可用筛选条件</span>
                            <span class="font-medium">15+维度</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='enterprise-mining.html'" 
                            class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 指标模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-purple-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">指标分析</h3>
                            <p class="text-sm text-gray-600">企业创新指标表现</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">研发费用500强</span>
                            <span class="font-medium">已更新</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">重点监测企业</span>
                            <span class="font-medium">实时追踪</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">研值排名</span>
                            <span class="font-medium">Top100</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='enterprise-metrics.html'" 
                            class="w-full bg-purple-500 text-white py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 区域分布模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-map text-indigo-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">区域分布</h3>
                            <p class="text-sm text-gray-600">企业空间分布分析</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">361产业分布</span>
                            <span class="font-medium">可视化图表</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">510技术领域</span>
                            <span class="font-medium">热力图展示</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">集中度分析</span>
                            <span class="font-medium">深度洞察</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='regional-distribution.html'" 
                            class="w-full bg-indigo-500 text-white py-2 rounded-lg hover:bg-indigo-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 亲密度模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-handshake text-pink-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">合作亲密度</h3>
                            <p class="text-sm text-gray-600">产学研合作分析</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">本地合作</span>
                            <span class="font-medium">Top10排名</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">外地合作</span>
                            <span class="font-medium">活跃度分析</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">产业链筛选</span>
                            <span class="font-medium">标志性企业</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='proximity.html'" 
                            class="w-full bg-pink-500 text-white py-2 rounded-lg hover:bg-pink-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 企业预警模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">企业预警</h3>
                            <p class="text-sm text-gray-600">风险识别与预警</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">红色预警</span>
                            <span class="font-medium text-red-600">23家</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">黄色预警</span>
                            <span class="font-medium text-yellow-600">67家</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">沉默企业</span>
                            <span class="font-medium text-gray-600">37家</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='enterprise-warning.html'" 
                            class="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 研发机构模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-university text-teal-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">研发机构</h3>
                            <p class="text-sm text-gray-600">企业研发资源分析</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">市级机构</span>
                            <span class="font-medium">156个</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">省级机构</span>
                            <span class="font-medium">89个</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">领域分布</span>
                            <span class="font-medium">全覆盖</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='rd-institution.html'" 
                            class="w-full bg-teal-500 text-white py-2 rounded-lg hover:bg-teal-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 技术需求模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-lightbulb text-orange-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">技术需求</h3>
                            <p class="text-sm text-gray-600">企业技术难题采集</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">待对接需求</span>
                            <span class="font-medium">284条</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">已对接成功</span>
                            <span class="font-medium">127条</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">技术领域</span>
                            <span class="font-medium">多分类</span>
                        </div>
                    </div>
                    <button onclick="window.location.href='technical-demand.html'" 
                            class="w-full bg-orange-500 text-white py-2 rounded-lg hover:bg-orange-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        进入模块
                    </button>
                </div>
            </div>

            <!-- 智能规则模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-12 h-12 bg-cyan-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-robot text-cyan-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-bold text-gray-900">智能规则</h3>
                            <p class="text-sm text-gray-600">AI驱动的企业发现</p>
                        </div>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">规则模板</span>
                            <span class="font-medium">15个</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">值得关注榜</span>
                            <span class="font-medium">实时更新</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">命中精度</span>
                            <span class="font-medium">92.5%</span>
                        </div>
                    </div>
                    <button class="w-full bg-cyan-500 text-white py-2 rounded-lg hover:bg-cyan-600 transition-colors">
                        <i class="fas fa-arrow-right mr-2"></i>
                        即将上线
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="mt-12 text-center text-gray-500 text-sm">
            <p>© 2024 企业监测大屏系统 - 数据驱动决策，智能洞察未来</p>
            <p class="mt-1">最后更新：2024年1月15日 14:30 | 数据来源：科技管理平台</p>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 添加数字滚动效果
            const numbers = document.querySelectorAll('[data-number]');
            numbers.forEach(number => {
                const target = parseInt(number.getAttribute('data-number'));
                let current = 0;
                const increment = target / 100;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        number.textContent = target.toLocaleString();
                        clearInterval(timer);
                    } else {
                        number.textContent = Math.floor(current).toLocaleString();
                    }
                }, 20);
            });
        });
    </script>
</body>
</html> 