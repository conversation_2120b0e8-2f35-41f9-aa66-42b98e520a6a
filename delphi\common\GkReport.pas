unit GkReport;

interface

uses
  Classes;

type
  TGkReport = class
  private
    FXh: Integer;
    FKh: string;
    FKz: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FSl: Integer;
    FXs: double;
    FJj: double;
    FJjlr: double;
    FZb: double;
  public
    property Xh: Integer read FXh write FXh;
    property Kh: string read FKh write FKh;
    property Kz: string read FKz write FKz;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Sl: Integer read FSl write FSl;
    property Xs: double read FXs write FXs;
    property Jj: double read FJj write FJj;
    property Jjlr: double read FJjlr write FJjlr;
    property Zb: double read FZb write FZb;
  end;

implementation

end.
