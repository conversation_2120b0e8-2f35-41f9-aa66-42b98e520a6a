<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>需求征集发布</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">需求征集发布</h1>
                <p class="text-gray-600 mt-1">农业农村领域技术服务需求征集与管理平台</p>
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出数据
                </button>
                <button onclick="openAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增需求
                </button>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">需求标题</label>
                    <input type="text" placeholder="请输入需求标题" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">需求类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="plant">种植技术</option>
                        <option value="breed">养殖技术</option>
                        <option value="process">农产品加工</option>
                        <option value="equip">农业机械</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">申请单位</label>
                    <input type="text" placeholder="请输入单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">审核状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="pending">待审核</option>
                        <option value="approved">已通过</option>
                        <option value="rejected">已驳回</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">发布时间</label>
                    <div class="flex space-x-1">
                        <input type="date" class="w-full px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500 text-xs">至</span>
                        <input type="date" class="w-full px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">发布状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="published">已发布</option>
                        <option value="unpublished">未发布</option>
                        <option value="revoked">已撤销</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    查询
                </button>
            </div>
        </div>

        <!-- 需求列表 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">需求征集列表</h2>
                <div class="text-sm text-gray-500">
                    共 <span class="font-medium text-gray-900">128</span> 条记录
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请单位/联系人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NJ-2024-001</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">水稻病虫害防治技术需求</div>
                                <div class="text-sm text-gray-500">宁波市鄞州区</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">种植技术</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市鄞州农业合作社</div>
                                <div class="text-sm text-gray-500">张先生 138****1234</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已通过</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openReviewModal()" class="text-indigo-600 hover:text-indigo-900">审核</button>
                                <button class="text-green-600 hover:text-green-900">发布</button>
                                <button class="text-red-600 hover:text-red-900">撤销</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NJ-2024-002</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">生猪养殖智能化管理需求</div>
                                <div class="text-sm text-gray-500">宁波市余姚市</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">养殖技术</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">余姚市畜牧养殖场</div>
                                <div class="text-sm text-gray-500">李先生 139****5678</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-18</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">未发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openReviewModal()" class="text-indigo-600 hover:text-indigo-900">审核</button>
                                <button disabled class="text-gray-400">发布</button>
                                <button disabled class="text-gray-400">撤销</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NJ-2024-003</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">杨梅深加工技术需求</div>
                                <div class="text-sm text-gray-500">宁波市慈溪市</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农产品加工</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">慈溪市杨梅合作社</div>
                                <div class="text-sm text-gray-500">王女士 135****9012</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">已驳回</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">未发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openReviewModal()" class="text-indigo-600 hover:text-indigo-900">审核</button>
                                <button disabled class="text-gray-400">发布</button>
                                <button disabled class="text-gray-400">撤销</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NJ-2024-004</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">农田智能灌溉设备需求</div>
                                <div class="text-sm text-gray-500">宁波市宁海县</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农业机械</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁海县农业技术推广站</div>
                                <div class="text-sm text-gray-500">陈先生 137****3456</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已通过</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">已撤销</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button disabled class="text-gray-400">审核</button>
                                <button class="text-green-600 hover:text-green-900">发布</button>
                                <button disabled class="text-gray-400">撤销</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-4 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增需求弹窗 -->
    <div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增技术服务需求</h3>
                    <button onclick="closeModal('addModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">需求标题 <span class="text-red-500">*</span></label>
                            <input type="text" placeholder="请输入需求标题" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">需求类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择需求类型</option>
                                <option value="plant">种植技术</option>
                                <option value="breed">养殖技术</option>
                                <option value="process">农产品加工</option>
                                <option value="equip">农业机械</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">申请单位 <span class="text-red-500">*</span></label>
                            <input type="text" placeholder="请输入申请单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系人 <span class="text-red-500">*</span></label>
                            <input type="text" placeholder="请输入联系人姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                            <input type="tel" placeholder="请输入联系电话" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所在地区 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择所在地区</option>
                                <option value="haishu">海曙区</option>
                                <option value="jiangdong">江东区</option>
                                <option value="jiangbei">江北区</option>
                                <option value="zhenhai">镇海区</option>
                                <option value="yinzhou">鄞州区</option>
                                <option value="beilun">北仑区</option>
                                <option value="yuyao">余姚市</option>
                                <option value="cixi">慈溪市</option>
                                <option value="fenghua">奉化区</option>
                                <option value="xiangshan">象山县</option>
                                <option value="ninghai">宁海县</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">需求描述 <span class="text-red-500">*</span></label>
                        <textarea rows="4" placeholder="请详细描述您的技术需求，包括现状、问题和预期目标" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .doc, .xls, .jpg 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('addModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            提交需求
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 需求详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">需求详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">需求编号</label>
                            <div class="text-sm text-gray-900">NJ-2024-001</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">需求标题</label>
                            <div class="text-sm text-gray-900">水稻病虫害防治技术需求</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">需求类型</label>
                            <div class="text-sm text-gray-900">种植技术</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">所在地区</label>
                            <div class="text-sm text-gray-900">宁波市鄞州区</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">申请单位</label>
                            <div class="text-sm text-gray-900">宁波市鄞州农业合作社</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">联系人</label>
                            <div class="text-sm text-gray-900">张先生 138****1234</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">发布时间</label>
                            <div class="text-sm text-gray-900">2024-01-15 14:30</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">审核状态</label>
                            <div class="text-sm text-gray-900">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已通过</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">发布状态</label>
                            <div class="text-sm text-gray-900">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">已发布</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">分配特派员</label>
                            <div class="text-sm text-gray-900">李教授（宁波市农业科学研究院）</div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">需求描述</label>
                        <div class="text-sm text-gray-900 bg-gray-50 p-4 rounded-md">
                            <p>我社现有水稻种植面积500亩，近年来病虫害问题日益严重，特别是稻飞虱和纹枯病频发。传统防治方法效果不佳且农药使用量大，希望引进绿色防控技术，减少农药使用量30%以上，同时保证产量不下降。</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">附件</label>
                        <div class="flex items-center space-x-2">
                            <div class="flex items-center p-2 border border-gray-200 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm text-gray-700">病虫害照片.zip</span>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">操作记录</label>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <div class="space-y-2">
                                <div class="text-sm">
                                    <span class="font-medium text-gray-900">2024-01-15 14:30</span>
                                    <span class="text-gray-500">- 需求已发布</span>
                                </div>
                                <div class="text-sm">
                                    <span class="font-medium text-gray-900">2024-01-15 10:15</span>
                                    <span class="text-gray-500">- 审核通过（审核人：王管理员）</span>
                                </div>
                                <div class="text-sm">
                                    <span class="font-medium text-gray-900">2024-01-14 16:20</span>
                                    <span class="text-gray-500">- 需求已提交</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end pt-4">
                        <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核需求弹窗 -->
    <div id="reviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">审核技术服务需求</h3>
                    <button onclick="closeModal('reviewModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">需求编号</label>
                            <div class="text-sm text-gray-900">NJ-2024-002</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">需求标题</label>
                            <div class="text-sm text-gray-900">生猪养殖智能化管理需求</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">申请单位</label>
                            <div class="text-sm text-gray-900">余姚市畜牧养殖场</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">联系人</label>
                            <div class="text-sm text-gray-900">李先生 139****5678</div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">需求描述</label>
                        <div class="text-sm text-gray-900 bg-gray-50 p-4 rounded-md">
                            <p>我场现有生猪存栏量2000头，目前采用传统人工管理模式，效率低下且成本高。希望引入智能化养殖管理系统，包括环境监测、自动喂食、疫病预警等功能，实现养殖过程数字化管理，降低人工成本30%以上。</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核意见 <span class="text-red-500">*</span></label>
                        <textarea rows="3" placeholder="请输入审核意见" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('reviewModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50">
                            驳回
                        </button>
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            通过
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openAddModal() {
            document.getElementById('addModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openReviewModal() {
            document.getElementById('reviewModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        document.querySelectorAll('#addModal, #detailModal, #reviewModal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal(this.id);
                }
            });
        });

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const fileName = e.target.files[0].name;
                const fileSize = (e.target.files[0].size / 1024 / 1024).toFixed(2);
                alert(`已选择文件: ${fileName} (${fileSize} MB)`);
            }
        });
    </script>
</body>
</html>