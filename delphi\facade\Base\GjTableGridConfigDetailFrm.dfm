object GjTableGridConfigDetailFrame: TGjTableGridConfigDetailFrame
  Left = 0
  Top = 0
  Width = 800
  Height = 595
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel2: TRzPanel
    Left = 0
    Top = 160
    Width = 800
    Height = 435
    Align = alClient
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    ExplicitTop = 180
    ExplicitHeight = 415
    object AdvStringGrid1: TAdvStringGrid
      Left = 9
      Top = 0
      Width = 791
      Height = 435
      Cursor = crDefault
      Align = alClient
      BevelInner = bvNone
      BevelOuter = bvNone
      ColCount = 12
      Ctl3D = False
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goEditing, goRowSelect]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      ScrollBars = ssBoth
      TabOrder = 0
      OnMouseMove = AdvStringGrid1MouseMove
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      OnAnchorClick = AdvStringGrid1AnchorClick
      OnCellValidate = AdvStringGrid1CellValidate
      OnGetEditorType = AdvStringGrid1GetEditorType
      OnGetEditorProp = AdvStringGrid1GetEditorProp
      OnCheckBoxChange = AdvStringGrid1CheckBoxChange
      OnEditChange = AdvStringGrid1EditChange
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        #32534#21495
        #21517#31216)
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #24494#36719#38597#40657
      FilterDropDown.Font.Style = []
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 80
      FixedRowHeight = 30
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -13
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      Flat = True
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      ShowSelection = False
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ExplicitHeight = 415
      ColWidths = (
        80
        270
        122
        64
        64
        64
        64
        64
        64
        64
        64
        64)
      RowHeights = (
        30
        22
        22
        22
        22
        22
        22
        22
        22
        22)
    end
    object RzPanel5: TRzPanel
      Left = 0
      Top = 0
      Width = 9
      Height = 435
      Align = alLeft
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 1
      ExplicitHeight = 415
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 0
    Width = 800
    Height = 160
    Align = alTop
    BorderOuter = fsNone
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    object RzPanel4: TRzPanel
      Left = 0
      Top = 0
      Width = 800
      Height = 83
      Align = alTop
      BorderOuter = fsFlat
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object RzLabel3: TRzLabel
        Left = 239
        Top = 9
        Width = 34
        Height = 19
        Caption = #31867'  '#21035
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel1: TRzLabel
        Left = 239
        Top = 48
        Width = 34
        Height = 19
        Caption = #24037'  '#24207
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel4: TRzLabel
        Left = 16
        Top = 49
        Width = 120
        Height = 26
        Caption = #19977#32423#31867#21035' '#24037#24207
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel5: TRzLabel
        Left = 143
        Top = 52
        Width = 30
        Height = 19
        Caption = #32534#21046
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel20: TRzLabel
        Left = 536
        Top = 51
        Width = 483
        Height = 19
        Caption = #26412#21378#24037#20215#20013#65292#24037#24207#30340#21512#35745#20998#20540#20026'0'#21487#36827#34892#21024#38500#65292#21512#35745#20998#20540#22823#20110'0'#19981#33021#21024#38500#12290
        Color = clSilver
        Font.Charset = GB2312_CHARSET
        Font.Color = 81119
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
        Transparent = True
      end
      object RzComboBox_Lb: TRzComboBox
        Left = 286
        Top = 6
        Width = 195
        Height = 27
        AllowEdit = False
        Color = 16051944
        DropDownCount = 20
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        TabOrder = 0
        OnChange = RzComboBox_LbChange
      end
      object RzComboBox_Gx: TRzComboBox
        Left = 286
        Top = 45
        Width = 195
        Height = 25
        AllowEdit = False
        Color = 16051944
        DropDownCount = 20
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        OnChange = RzComboBox_GxChange
      end
      object Btn_Save: TAdvGlowButton
        Left = 536
        Top = 6
        Width = 82
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -16
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 2
        OnClick = Btn_SaveClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
    end
    object RzPanel6: TRzPanel
      Left = 0
      Top = 83
      Width = 9
      Height = 77
      Align = alLeft
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 1
      ExplicitTop = 100
      ExplicitHeight = 80
    end
    object RzPanel1: TRzPanel
      Left = 9
      Top = 83
      Width = 791
      Height = 77
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 2
      ExplicitTop = 73
      ExplicitHeight = 107
      object RzLabel2: TRzLabel
        Left = 16
        Top = 50
        Width = 60
        Height = 17
        Caption = #36873'      '#29992#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
      end
      object RzLabel7: TRzLabel
        Left = 16
        Top = 14
        Width = 60
        Height = 17
        Caption = #21517'      '#31216#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
      end
      object Edit_Mc: TRzEdit
        Left = 98
        Top = 10
        Width = 250
        Height = 25
        Text = ''
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
        ParentFont = False
        TabOrder = 0
        OnChange = Edit_McChange
      end
      object ComboBox_Check: TRzComboBox
        Left = 98
        Top = 46
        Width = 250
        Height = 25
        AutoComplete = False
        DropDownCount = 20
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
        ParentFont = False
        TabOrder = 1
        OnChange = ComboBox_CheckChange
        Items.Strings = (
          #26159
          #21542)
      end
    end
  end
end
