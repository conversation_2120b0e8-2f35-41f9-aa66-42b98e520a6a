# 知识产权公共服务平台PC端智能问答业务流程

## 业务流程

```flow
用户访问公共服务平台 --> 检测访问端类型(PC/移动)
检测访问端类型(PC/移动) --> 加载智能问答浮窗
加载智能问答浮窗 --> 用户身份识别
用户身份识别 --> 登录用户{已登录用户}
用户身份识别 --> 匿名用户{匿名用户}
登录用户{已登录用户} --> 单点登录验证
单点登录验证 --> 个性化问答界面
匿名用户{匿名用户} --> 生成临时UUID
生成临时UUID --> 匿名问答界面
个性化问答界面 --> 用户输入问题
匿名问答界面 --> 用户输入问题
用户输入问题 --> 调用对话网关
调用对话网关 --> 知识库检索
知识库检索 --> 公网大模型API
公网大模型API --> 生成答案
生成答案 --> 显示答案内容
显示答案内容 --> 用户反馈评价
用户反馈评价 --> 有帮助{有帮助}
用户反馈评价 --> 无帮助{无帮助}
有帮助{有帮助} --> 写入反馈服务
无帮助{无帮助} --> 写入反馈服务
写入反馈服务 --> 继续对话{继续对话}
写入反馈服务 --> 转人工服务{转人工服务}
继续对话{继续对话} --> 用户输入问题
转人工服务{转人工服务} --> 人工热线备用通道
调用对话网关 --> 心跳探针监控
心跳探针监控 --> 接口异常检测
接口异常检测 --> 正常响应{正常}
接口异常检测 --> 连续3次异常{异常}
正常响应{正常} --> 生成答案
连续3次异常{异常} --> 隐藏浮窗
隐藏浮窗 --> 人工热线备用通道
人工热线备用通道 --> 表单提交
表单提交 --> 人工客服处理
生成答案 --> 脱敏日志记录
脱敏日志记录 --> 7天自动清理
个性化问答界面 --> 历史对话追溯
匿名问答界面 --> 转实名合并{选择转实名}
转实名合并{选择转实名} --> 合并对话记录
合并对话记录 --> 个性化问答界面
```

## 核心组件说明

1. **智能问答浮窗**: 网站右侧浮窗，支持PC/移动端适配
2. **用户身份识别**: 登录用户使用单点登录，匿名用户生成UUID
3. **对话网关**: 统一的问答接口，连接知识库和大模型
4. **知识库检索**: 基于问题内容进行相关知识检索
5. **反馈系统**: 用户对答案质量进行评价
6. **监控降级**: 心跳探针监控，异常时降级到人工服务
7. **数据安全**: 脱敏日志记录，7天自动清理

## 技术架构

- **前端**: 响应式浮窗组件，支持PC/移动端
- **后端**: 对话网关、知识库、大模型API集成
- **安全**: 单点登录、数据脱敏、日志自动清理
- **监控**: 心跳探针、异常降级机制 