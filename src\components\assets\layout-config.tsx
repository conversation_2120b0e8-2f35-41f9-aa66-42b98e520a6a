'use client'

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"

export function LayoutConfig() {
  return (
    <ScrollArea className="h-[calc(100vh-250px)]">
      <div className="p-4 space-y-6">
        {/* 网格设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">网格设置</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>显示网格</Label>
              <Switch className="data-[state=checked]:bg-blue-500" />
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>网格大小</Label>
                <span className="text-sm text-gray-500">20px</span>
              </div>
              <Slider defaultValue={[20]} max={50} min={10} step={5} className="bg-blue-50" />
            </div>
          </div>
        </div>

        {/* 布局方式 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">布局方式</h3>
          <Select defaultValue="grid">
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="选择布局方式" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="grid">网格布局</SelectItem>
              <SelectItem value="flex">弹性布局</SelectItem>
              <SelectItem value="absolute">绝对定位</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 对齐方式 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">对齐方式</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>水平对齐</Label>
              <Select defaultValue="start">
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="水平对齐" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="start">左对齐</SelectItem>
                  <SelectItem value="center">居中</SelectItem>
                  <SelectItem value="end">右对齐</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>垂直对齐</Label>
              <Select defaultValue="start">
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="垂直对齐" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="start">顶部对齐</SelectItem>
                  <SelectItem value="center">居中</SelectItem>
                  <SelectItem value="end">底部对齐</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 间距设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">间距设置</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>水平间距</Label>
              <Input type="number" className="bg-white" defaultValue={16} />
            </div>
            <div className="space-y-2">
              <Label>垂直间距</Label>
              <Input type="number" className="bg-white" defaultValue={16} />
            </div>
          </div>
        </div>

        {/* 响应式设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">响应式设置</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>启用响应式</Label>
              <Switch className="data-[state=checked]:bg-blue-500" />
            </div>
            <Select defaultValue="stretch">
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="缩放方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="stretch">拉伸</SelectItem>
                <SelectItem value="contain">等比缩放</SelectItem>
                <SelectItem value="cover">填充</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-2">
          <Button variant="outline" className="hover:bg-blue-50">重置</Button>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white">应用</Button>
        </div>
      </div>
    </ScrollArea>
  )
} 