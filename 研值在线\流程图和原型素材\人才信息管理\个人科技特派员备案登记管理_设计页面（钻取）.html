<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人科技特派员备案登记管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">个人科技特派员备案登记管理</h1>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                条件筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                    <input type="text" placeholder="请输入姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">工作单位</label>
                    <input type="text" placeholder="请输入工作单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">专业特长</label>
                    <input type="text" placeholder="请输入专业特长" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">申报批次</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部批次</option>
                        <option value="2023-1">2023年第一批</option>
                        <option value="2023-2">2023年第二批</option>
                        <option value="2024-1">2024年第一批</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="draft">草稿</option>
                        <option value="pending">待审核</option>
                        <option value="reviewing">审核中</option>
                        <option value="approved">已通过</option>
                        <option value="rejected">已驳回</option>
                        <option value="archived">已归档</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">申报时间</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex space-x-3">
                <button onclick="openBatchModal()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增批次
                </button>
                <button onclick="openRegistrationModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增备案
                </button>
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出Excel
                </button>
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                    </svg>
                    批量归档
                </button>
            </div>
        </div>

        <!-- 备案登记列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-800">备案登记列表</h2>
                    <div class="text-sm text-gray-500">
                        共 <span class="font-medium text-blue-600">156</span> 条记录
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业特长</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结对服务单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报批次</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">张三</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">男</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市农业科学研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">水稻种植技术</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市鄞州区姜山镇农技站</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024年第一批</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-01-15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已通过</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openReviewModal('1')" class="text-indigo-600 hover:text-indigo-900">审核</button>
                                <button onclick="archiveRecord('1')" class="text-yellow-600 hover:text-yellow-900">归档</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">李四</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">女</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波大学海洋学院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">水产养殖</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市象山县水产技术推广站</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024年第一批</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-01-18</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">审核中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openReviewModal('2')" class="text-indigo-600 hover:text-indigo-900">审核</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王五</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">男</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市林业技术推广总站</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">果树栽培</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市奉化区水蜜桃研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2023年第二批</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2023-07-22</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openReviewModal('3')" class="text-indigo-600 hover:text-indigo-900">审核</button>
                                <button onclick="withdrawRecord('3')" class="text-red-600 hover:text-red-900">撤销</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">赵六</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">女</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市农业机械化研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">农业机械</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市余姚市农机推广中心</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2023年第一批</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2023-03-10</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">已归档</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">查看</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">156</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 备案详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">备案登记详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 个人信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">个人信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">姓名：</span>
                                <span class="w-2/3 font-medium">张三</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">性别：</span>
                                <span class="w-2/3 font-medium">男</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">出生年月：</span>
                                <span class="w-2/3 font-medium">1985-05-15</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">工作单位：</span>
                                <span class="w-2/3 font-medium">宁波市农业科学研究院</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">职称：</span>
                                <span class="w-2/3 font-medium">高级农艺师</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">专业特长：</span>
                                <span class="w-2/3 font-medium">水稻种植技术</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">联系电话：</span>
                                <span class="w-2/3 font-medium">138****1234</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 服务单位信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">服务单位信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">单位名称：</span>
                                <span class="w-2/3 font-medium">宁波市鄞州区姜山镇农技站</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">单位地址：</span>
                                <span class="w-2/3 font-medium">宁波市鄞州区姜山镇人民路88号</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">联系人：</span>
                                <span class="w-2/3 font-medium">李站长</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">联系电话：</span>
                                <span class="w-2/3 font-medium">0574-88****88</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">服务期限：</span>
                                <span class="w-2/3 font-medium">2024-01-01 至 2024-12-31</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 科技服务内容 -->
                    <div class="bg-gray-50 p-4 rounded-lg md:col-span-2">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">科技服务内容</h4>
                        <div class="space-y-3 text-sm">
                            <div>
                                <span class="text-gray-600">服务内容：</span>
                                <p class="mt-1 font-medium">1. 指导水稻新品种选育与示范推广<br>2. 开展水稻高产栽培技术培训<br>3. 解决水稻病虫害防治技术难题</p>
                            </div>
                            <div>
                                <span class="text-gray-600">预期目标：</span>
                                <p class="mt-1 font-medium">1. 推广水稻新品种2个，面积500亩<br>2. 培训农民200人次<br>3. 帮助服务单位建立水稻病虫害防治体系</p>
                            </div>
                            <div>
                                <span class="text-gray-600">任务说明：</span>
                                <p class="mt-1 font-medium">每月至少到服务单位现场指导1次，每季度开展1次技术培训，全年完成技术报告2份。</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 申报信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">申报信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">申报批次：</span>
                                <span class="w-2/3 font-medium">2024年第一批</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">申报时间：</span>
                                <span class="w-2/3 font-medium">2024-01-15</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">申报单位：</span>
                                <span class="w-2/3 font-medium">宁波市农业科学研究院</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">归口部门：</span>
                                <span class="w-2/3 font-medium">宁波市科技局</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审核信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">审核信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">当前状态：</span>
                                <span class="w-2/3 font-medium"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已通过</span></span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">审核时间：</span>
                                <span class="w-2/3 font-medium">2024-02-10</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">审核意见：</span>
                                <span class="w-2/3 font-medium">申报材料齐全，符合科技特派员备案要求，同意备案。</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 备案登记表单弹窗 -->
    <div id="registrationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">个人科技特派员备案登记</h3>
                    <button onclick="closeModal('registrationModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 个人信息 -->
                        <div class="md:col-span-2 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">个人信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">性别 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                        <option value="">请选择</option>
                                        <option value="male">男</option>
                                        <option value="female">女</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">出生年月 <span class="text-red-500">*</span></label>
                                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">工作单位 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">职称 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">专业特长 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                                    <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 服务单位信息 -->
                        <div class="md:col-span-2 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">结对服务单位信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">单位名称 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">单位地址 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">联系人 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">服务期限 <span class="text-red-500">*</span></label>
                                    <div class="flex space-x-2">
                                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                        <span class="flex items-center text-gray-500">至</span>
                                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 科技服务内容 -->
                        <div class="md:col-span-2 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">科技服务内容</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">服务内容 <span class="text-red-500">*</span></label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">预期目标 <span class="text-red-500">*</span></label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">任务说明 <span class="text-red-500">*</span></label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 申报信息 -->
                        <div class="md:col-span-2 bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-4">申报信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报批次 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                        <option value="">请选择批次</option>
                                        <option value="2024-1">2024年第一批</option>
                                        <option value="2024-2">2024年第二批</option>
                                        <option value="2023-1">2023年第一批</option>
                                        <option value="2023-2">2023年第二批</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报单位 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">归口部门 <span class="text-red-500">*</span></label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                                    <div class="mt-1 flex items-center">
                                        <label class="cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>选择文件</span>
                                            <input type="file" class="sr-only">
                                        </label>
                                        <span class="ml-2 text-sm text-gray-500">未选择文件</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('registrationModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            保存草稿
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            提交上报
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 审核弹窗 -->
    <div id="reviewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">备案登记审核</h3>
                    <button onclick="closeModal('reviewModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- 备案基本信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">备案基本信息</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">姓名：</span>
                                <span class="w-2/3 font-medium">张三</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">工作单位：</span>
                                <span class="w-2/3 font-medium">宁波市农业科学研究院</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">专业特长：</span>
                                <span class="w-2/3 font-medium">水稻种植技术</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">服务单位：</span>
                                <span class="w-2/3 font-medium">宁波市鄞州区姜山镇农技站</span>
                            </div>
                            <div class="flex">
                                <span class="w-1/3 text-gray-600">申报批次：</span>
                                <span class="w-2/3 font-medium">2024年第一批</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审核历史 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-800 mb-3">审核历史</h4>
                        <div class="space-y-3 text-sm">
                            <div class="border-l-4 border-blue-500 pl-3">
                                <div class="font-medium">乡镇审核</div>
                                <div class="text-gray-600">2024-01-20</div>
                                <div class="text-green-600">通过</div>
                                <div class="text-gray-700">申报材料齐全，符合要求。</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核意见 <span class="text-red-500">*</span></label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">审核结果 <span class="text-red-500">*</span></label>
                        <div class="space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="reviewResult" value="approve" class="text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">通过</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="reviewResult" value="reject" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">驳回</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="reviewResult" value="return" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">退回补充</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('reviewModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            提交审核
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批次管理弹窗 -->
    <div id="batchModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批次管理</h3>
                    <button onclick="closeModal('batchModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">批次名称 <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="例如：2024年第一批" required>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">申报开始时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">申报结束时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">批次状态 <span class="text-red-500">*</span></label>
                        <div class="space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="batchStatus" value="active" class="text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">启用</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="batchStatus" value="inactive" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">停用</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">批次说明</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('batchModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存批次
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 打开弹窗函数
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function openRegistrationModal() {
            document.getElementById('registrationModal').classList.remove('hidden');
        }
        
        function openReviewModal(id) {
            document.getElementById('reviewModal').classList.remove('hidden');
        }
        
        function openBatchModal() {
            document.getElementById('batchModal').classList.remove('hidden');
        }
        
        // 关闭弹窗函数
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }
        
        // 点击弹窗外部关闭
        document.querySelectorAll('[id$="Modal"]').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
        });
        
        // 其他操作函数
        function archiveRecord(id) {
            if (confirm('确定要将此备案归档吗？归档后将无法修改。')) {
                console.log('归档备案:', id);
            }
        }
        
        function withdrawRecord(id) {
            if (confirm('确定要撤销此备案吗？撤销后需要重新提交。')) {
                console.log('撤销备案:', id);
            }
        }
    </script>
</body>
</html>