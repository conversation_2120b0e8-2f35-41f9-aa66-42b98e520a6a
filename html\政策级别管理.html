<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策级别管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">政策级别管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-4/5 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-3 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-1">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="policyName" class="block text-sm font-medium text-gray-700 mb-0.5">政策名称</label>
                            <input type="text" id="policyName" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入政策名称">
                        </div>
                        <div>
                            <label for="documentNumber" class="block text-sm font-medium text-gray-700 mb-0.5">发文文号</label>
                            <input type="text" id="documentNumber" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入发文文号">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-0.5">政策级别</label>
                            <div class="flex flex-wrap gap-1">
                                <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">国家级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">省级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">市级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">区级</button>
                            </div>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-0.5">生效状态</label>
                            <select id="status" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="effective">生效</option>
                                <option value="ineffective">失效</option>
                            </select>
                        </div>
                        <div>
                            <label for="effectiveDate" class="block text-sm font-medium text-gray-700 mb-0.5">生效日期</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-0.5">发文部门</label>
                            <select id="department" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="state-council">国务院</option>
                                <option value="ministry">部委</option>
                                <option value="provincial">省政府</option>
                                <option value="municipal">市政府</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-1.5 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 政策级别列表区 -->
                    <div class="w-full">
                        <!-- 级别列表区 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800">政策级别列表</h2>
                                <button onclick="openEditModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    批量变更级别
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发文文号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前级别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引用次数</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">关于促进科技创新发展的若干意见</div>
                                            <div class="text-sm text-gray-500">科技创新政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国发〔2024〕15号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">156</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">省级产业发展扶持政策实施细则</div>
                                            <div class="text-sm text-gray-500">产业发展政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙政发〔2024〕8号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">89</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 14:20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">市级中小企业发展专项资金管理办法</div>
                                            <div class="text-sm text-gray-500">资金管理政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">杭政办〔2024〕12号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">失效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 09:15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">区级人才引进激励政策</div>
                                            <div class="text-sm text-gray-500">人才政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">西湖政发〔2024〕3号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">区级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">67</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05 16:45</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('4')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">数字经济发展三年行动计划</div>
                                            <div class="text-sm text-gray-500">数字经济政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">未分级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-03 11:20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('5')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="w-full lg:w-1/5 lg:min-w-[280px] space-y-4">
                <!-- 统计与预警区 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">统计与预警</h3>
                    <div class="space-y-6">
                        <!-- 政策级别分布 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">政策级别分布</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">国家级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">1,245</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">省级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 70%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">2,156</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">市级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">3,892</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">区级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">1,567</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 近30天级别变更趋势 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">近30天级别变更趋势</h4>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-2xl font-bold text-blue-600">156</span>
                                <span class="text-xs text-gray-500">次变更</span>
                            </div>
                            <div class="h-16 flex items-end space-x-1">
                                <div class="w-3 bg-blue-200 rounded-t" style="height: 30%"></div>
                                <div class="w-3 bg-blue-300 rounded-t" style="height: 50%"></div>
                                <div class="w-3 bg-blue-400 rounded-t" style="height: 40%"></div>
                                <div class="w-3 bg-blue-500 rounded-t" style="height: 70%"></div>
                                <div class="w-3 bg-blue-600 rounded-t" style="height: 100%"></div>
                                <div class="w-3 bg-blue-500 rounded-t" style="height: 80%"></div>
                                <div class="w-3 bg-blue-400 rounded-t" style="height: 60%"></div>
                            </div>
                        </div>

                        <!-- 预警信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">预警信息</h4>
                            <div class="space-y-3">
                                <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <div>
                                            <p class="text-xs font-medium text-red-800">23个政策缺失级别标签</p>
                                            <a href="#" class="text-xs text-red-700 hover:text-red-900">立即处理 →</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <p class="text-xs font-medium text-yellow-800">8个政策即将到期</p>
                                            <a href="#" class="text-xs text-yellow-700 hover:text-yellow-900">查看详情 →</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量维护区（可折叠） -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <button onclick="toggleBatchMaintenance()" class="w-full flex items-center justify-between text-left">
                            <h3 class="text-lg font-medium text-gray-800">批量维护</h3>
                            <svg id="batch-toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div id="batch-content" class="p-4 space-y-4">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                下载标准模板
                            </a>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div class="hidden" id="upload-progress">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">上传进度</span>
                                <span class="text-gray-600">75%</span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 级别编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">变更政策级别</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择新级别</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="level" value="national" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">国家级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="provincial" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">省级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="municipal" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">市级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="district" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">区级</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label for="changeReason" class="block text-sm font-medium text-gray-700 mb-1">变更说明</label>
                        <textarea id="changeReason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入变更原因和说明"></textarea>
                    </div>
                    <div>
                        <label for="effectiveDate" class="block text-sm font-medium text-gray-700 mb-1">生效时间</label>
                        <input type="datetime-local" id="effectiveDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存变更
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleLevel(button) {
            if (button.classList.contains('border-blue-500')) {
                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');
                button.classList.add('border-gray-300', 'text-gray-700');
            } else {
                button.classList.remove('border-gray-300', 'text-gray-700');
                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');
            }
        }

        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 批量维护折叠功能
        function toggleBatchMaintenance() {
            const content = document.getElementById('batch-content');
            const icon = document.getElementById('batch-toggle-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            }
        }

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('upload-progress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('upload-progress').classList.add('hidden');
                            alert('文件上传成功！');
                        }, 500);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>