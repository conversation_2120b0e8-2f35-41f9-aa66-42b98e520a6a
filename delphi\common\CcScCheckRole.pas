unit CcScCheckRole;

interface

uses
  Classes;

type
  TCcScCheckRole = class
  private
    FDdid: integer;
    FJkRoleName: string;
    FCheckTime1: string;
    FCheckTime2: string;
    FCheckTime3: string;
    FCheckState1: integer;
    FCheckState2: integer;
    FCheckState3: integer;
    FCheckStep1: string;
    FCheckStep2: string;
    FCheckStep3: string;
    FState: integer;
    FMarketDate: string;

  public
    property Ddid: integer read FDdid write FDdid;
    property JkRoleName: string read FJkRoleName write FJkRoleName;
    property CheckTime1: string read FCheckTime1 write FCheckTime1;
    property CheckTime2: string read FCheckTime2 write FCheckTime2;
    property CheckTime3: string read FCheckTime3 write FCheckTime3;
    property CheckState1: integer read FCheckState1 write FCheckState1;
    property CheckState2: integer read FCheckState2 write FCheckState2;
    property CheckState3: integer read FCheckState3 write FCheckState3;
    property CheckStep1: string read FCheckStep1 write FCheckStep1;
    property CheckStep2: string read FCheckStep2 write FCheckStep2;
    property CheckStep3: string read FCheckStep3 write FCheckStep3;
    property State: integer read FState write FState;
    property MarketDate: string read FMarketDate write FMarketDate;

  end;

implementation

end.
