'use client'

import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend, Sector } from 'recharts'
import { LineChart as RechartsLineChart, Line, XAxis, YAxis, CartesianGrid, Area, AreaChart } from 'recharts'
import { <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>, Bar } from 'recharts'

interface ChartProps {
  data: any[]
  colors?: string[]
  tooltipFormat?: (value: number) => string
}

interface LineBarChartProps extends ChartProps {
  xField: string
  yField: string | string[]
}

// 自定义激活的扇形区域
const renderActiveShape = (props: any) => {
  const {
    cx, cy, innerRadius, outerRadius, startAngle, endAngle,
    fill, payload, percent, value
  } = props

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 8}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 10}
        outerRadius={outerRadius + 12}
        fill={fill}
      />
      <text x={cx} y={cy - 8} textAnchor="middle" fill="#333" fontSize={20} fontWeight="bold">
        {payload.name}
      </text>
      <text x={cx} y={cy + 8} textAnchor="middle" fill="#666">
        {`${(percent * 100).toFixed(1)}%`}
      </text>
      <text x={cx} y={cy + 25} textAnchor="middle" fill="#999" fontSize={12}>
        {`(${value})`}
      </text>
    </g>
  )
}

// 饼图组件
export function PieChart({ 
  data, 
  colors = ['#3B82F6', '#10B981', '#F59E0B', '#6366F1', '#EC4899'], 
  tooltipFormat 
}: ChartProps) {
  const [activeIndex, setActiveIndex] = useState<number>(0)

  return (
    <ResponsiveContainer width="100%" height="100%">
      <RechartsPieChart>
        <Pie
          activeIndex={activeIndex}
          activeShape={renderActiveShape}
          data={data}
          dataKey="value"
          nameKey="name"
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
          onMouseEnter={(_, index) => setActiveIndex(index)}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={colors[index % colors.length]}
              style={{
                filter: `drop-shadow(0px 2px 4px ${colors[index % colors.length]}40)`
              }}
            />
          ))}
        </Pie>
        <Tooltip 
          formatter={(value: number) => tooltipFormat ? tooltipFormat(value) : value}
          contentStyle={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '8px',
            border: 'none',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          }}
        />
        <Legend 
          verticalAlign="bottom"
          height={36}
          iconType="circle"
        />
      </RechartsPieChart>
    </ResponsiveContainer>
  )
}

// 折线图组件
export function LineChart({ data, xField, yField, tooltipFormat }: LineBarChartProps) {
  return (
    <ResponsiveContainer width="100%" height="100%">
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.2}/>
            <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
          </linearGradient>
        </defs>
        <CartesianGrid 
          strokeDasharray="3 3" 
          vertical={false}
          stroke="#E5E7EB"
        />
        <XAxis 
          dataKey={xField} 
          axisLine={false}
          tickLine={false}
          tick={{ fill: '#6B7280' }}
        />
        <YAxis 
          axisLine={false}
          tickLine={false}
          tick={{ fill: '#6B7280' }}
        />
        <Tooltip 
          formatter={(value: number) => tooltipFormat ? tooltipFormat(value) : value}
          contentStyle={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '8px',
            border: 'none',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          }}
        />
        <Area
          type="monotone"
          dataKey={yField}
          stroke="#3B82F6"
          strokeWidth={3}
          fill="url(#colorGradient)"
          dot={{ 
            fill: '#3B82F6',
            strokeWidth: 2,
            stroke: '#FFFFFF',
            r: 4
          }}
          activeDot={{
            r: 6,
            stroke: '#3B82F6',
            strokeWidth: 2,
            fill: '#FFFFFF'
          }}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

// 柱状图组件
export function BarChart({ data, xField, yField, tooltipFormat }: LineBarChartProps) {
  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#6366F1', '#EC4899']
  const gradientIds = colors.map((_, index) => `barGradient${index}`)

  return (
    <ResponsiveContainer width="100%" height="100%">
      <RechartsBarChart data={data}>
        <defs>
          {colors.map((color, index) => (
            <linearGradient
              key={gradientIds[index]}
              id={gradientIds[index]}
              x1="0"
              y1="0"
              x2="0"
              y2="1"
            >
              <stop offset="0%" stopColor={color} stopOpacity={1}/>
              <stop offset="100%" stopColor={color} stopOpacity={0.6}/>
            </linearGradient>
          ))}
        </defs>
        <CartesianGrid 
          strokeDasharray="3 3" 
          vertical={false}
          stroke="#E5E7EB"
        />
        <XAxis 
          dataKey={xField} 
          axisLine={false}
          tickLine={false}
          tick={{ fill: '#6B7280' }}
        />
        <YAxis 
          axisLine={false}
          tickLine={false}
          tick={{ fill: '#6B7280' }}
        />
        <Tooltip 
          formatter={(value: number) => tooltipFormat ? tooltipFormat(value) : value}
          cursor={{ fill: 'rgba(229, 231, 235, 0.4)' }}
          contentStyle={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '8px',
            border: 'none',
            boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
          }}
        />
        <Legend 
          verticalAlign="top"
          height={36}
          iconType="circle"
        />
        {Array.isArray(yField) ? (
          yField.map((field, index) => (
            <Bar 
              key={field} 
              dataKey={field} 
              fill={`url(#${gradientIds[index]})`}
              radius={[4, 4, 0, 0]}
              maxBarSize={50}
            />
          ))
        ) : (
          <Bar 
            dataKey={yField} 
            fill={`url(#${gradientIds[0]})`}
            radius={[4, 4, 0, 0]}
            maxBarSize={50}
          />
        )}
      </RechartsBarChart>
    </ResponsiveContainer>
  )
} 