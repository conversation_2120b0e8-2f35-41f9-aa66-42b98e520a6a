object CC_YjDetailAllForm: TCC_YjDetailAllForm
  Left = 0
  Top = 0
  ClientHeight = 562
  ClientWidth = 1084
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  PixelsPerInch = 96
  TextHeight = 13
  object TopPanel: TRzPanel
    Left = 0
    Top = 40
    Width = 1084
    Height = 165
    Align = alTop
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -11
    Font.Name = 'Tahoma'
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    object TopLeftPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 784
      Height = 165
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object Label_NoRecordInfo: TRzLabel
        Left = 676
        Top = 99
        Width = 102
        Height = 28
        Caption = #23578' '#26410' '#20986' '#36135
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -21
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Visible = False
      end
      object Btn_2: TAdvGlowButton
        Left = 88
        Top = 14
        Width = 68
        Height = 33
        Caption = '  B '#33394' '#32433
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 0
        OnClick = Btn_2Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_3: TAdvGlowButton
        Left = 162
        Top = 14
        Width = 68
        Height = 33
        Caption = '  C '#33394' '#32433
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 1
        OnClick = Btn_3Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_4: TAdvGlowButton
        Left = 236
        Top = 14
        Width = 68
        Height = 33
        Caption = '  D '#33394' '#32433
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 2
        OnClick = Btn_4Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_5: TAdvGlowButton
        Left = 310
        Top = 14
        Width = 68
        Height = 33
        Caption = '  A '#22383' '#24067
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 3
        OnClick = Btn_5Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_1: TAdvGlowButton
        Left = 14
        Top = 14
        Width = 68
        Height = 33
        Caption = '  A '#33394' '#32433' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 4
        OnClick = Btn_1Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_6: TAdvGlowButton
        Left = 384
        Top = 14
        Width = 68
        Height = 33
        Caption = '  B '#22383' '#24067
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 5
        OnClick = Btn_6Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_7: TAdvGlowButton
        Left = 458
        Top = 14
        Width = 68
        Height = 33
        Caption = '  C '#22383' '#24067
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 6
        OnClick = Btn_7Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_9: TAdvGlowButton
        Left = 14
        Top = 56
        Width = 68
        Height = 33
        Caption = '  '#26797' '#32455' '#24067
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 7
        OnClick = Btn_9Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_8: TAdvGlowButton
        Left = 532
        Top = 14
        Width = 68
        Height = 33
        Caption = '  D '#22383' '#24067
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 8
        OnClick = Btn_8Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_10: TAdvGlowButton
        Left = 88
        Top = 56
        Width = 68
        Height = 33
        Caption = '   '#32599'  '#32441' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 9
        OnClick = Btn_10Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_11: TAdvGlowButton
        Left = 162
        Top = 56
        Width = 68
        Height = 33
        Caption = '   '#27178'  '#26426
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 10
        OnClick = Btn_11Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_12: TAdvGlowButton
        Left = 236
        Top = 56
        Width = 68
        Height = 33
        Caption = ' '#28378#26465#32455#24102' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 11
        OnClick = Btn_12Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_13: TAdvGlowButton
        Left = 310
        Top = 56
        Width = 68
        Height = 33
        Caption = ' '#20854#20182#20027#26009
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 12
        OnClick = Btn_13Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_14: TAdvGlowButton
        Left = 14
        Top = 99
        Width = 68
        Height = 33
        Caption = '   '#36741'  '#26009' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 13
        OnClick = Btn_14Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_15: TAdvGlowButton
        Left = 88
        Top = 99
        Width = 68
        Height = 33
        Caption = '   '#21360'  '#32483' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 14
        OnClick = Btn_15Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_16: TAdvGlowButton
        Left = 162
        Top = 99
        Width = 68
        Height = 33
        Caption = ' '#25104#34915#26579#27927' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 15
        OnClick = Btn_16Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_17: TAdvGlowButton
        Left = 236
        Top = 99
        Width = 68
        Height = 33
        Caption = '   '#21253'  '#35013' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 16
        OnClick = Btn_17Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_18: TAdvGlowButton
        Left = 310
        Top = 99
        Width = 68
        Height = 33
        Caption = ' '#20854#20182#36741#26009
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 17
        OnClick = Btn_18Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_19: TAdvGlowButton
        Left = 384
        Top = 99
        Width = 68
        Height = 33
        Caption = ' '#32541#21046#24037#20215
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 18
        OnClick = Btn_19Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_20: TAdvGlowButton
        Left = 458
        Top = 99
        Width = 68
        Height = 33
        Caption = '   '#20986'  '#36135' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 19
        OnClick = Btn_20Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_21: TAdvGlowButton
        Left = 532
        Top = 99
        Width = 68
        Height = 33
        Caption = '   '#24635'  '#20215' '
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 3289650
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 20
        OnClick = Btn_21Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
    end
    object TopRightPanel: TRzPanel
      Left = 784
      Top = 0
      Width = 300
      Height = 165
      Align = alRight
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      object ImagePanel: TRzPanel
        Left = 131
        Top = 4
        Width = 140
        Height = 160
        BorderOuter = fsNone
        BorderColor = clSilver
        BorderWidth = 1
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        object Image1: TImage
          Left = 0
          Top = -1
          Width = 140
          Height = 140
          Align = alCustom
          Center = True
          Proportional = True
          Stretch = True
        end
        object RzLabel1: TRzLabel
          Left = 9
          Top = 145
          Width = 43
          Height = 13
          Caption = 'RzLabel1'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = 7237230
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
        end
      end
    end
  end
  object MainPanel: TRzPanel
    Left = 0
    Top = 205
    Width = 1084
    Height = 357
    Align = alClient
    BorderOuter = fsNone
    TabOrder = 1
    object MainTopPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1084
      Height = 65
      Align = alTop
      BorderOuter = fsNone
      TabOrder = 0
      object TopBluePanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1084
        Height = 20
        Align = alTop
        BorderOuter = fsNone
        BorderSides = [sdLeft, sdTop, sdRight]
        BorderColor = 14671839
        BorderWidth = 1
        Color = 16049103
        DoubleBuffered = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        GradientColorStyle = gcsCustom
        GradientColorStart = 16643306
        GradientColorStop = 16049103
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 0
        VisualStyle = vsGradient
      end
      object DdAdvStringGrid: TAdvStringGrid
        Left = 0
        Top = 20
        Width = 1084
        Height = 45
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        BorderStyle = bsNone
        ColCount = 10
        Ctl3D = True
        DefaultRowHeight = 40
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedCols = 0
        RowCount = 2
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goEditing, goRowSelect]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        ScrollBars = ssVertical
        TabOrder = 1
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #23435#20307
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ColumnHeaders.Strings = (
          '')
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.ColumnWidth = True
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -12
        FilterDropDown.Font.Name = #23435#20307
        FilterDropDown.Font.Style = []
        FilterDropDown.GlyphActive.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          08000000000000010000530B0000530B00000001000000010000104A10001063
          100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
          63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
          8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
          1414020214141414141414141414141414030902141414141414141414141414
          030D090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141403
          130D09000214141414141414141403130D0D0501000214141414141414031311
          0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
          0806090909040100021403030303030303030303030303030303141414141414
          1414141414141414141414141414141414141414141414141414}
        FilterDropDown.Height = 200
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDown.Width = 200
        FilterDropDownClear = #20840#37096
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FilterNormalCellsOnly = False
        FixedColWidth = 35
        FixedRowHeight = 40
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = [fsBold]
        FloatFormat = '%.2f'
        FloatingFooter.Column = 10
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        ShowSelection = False
        SortSettings.DefaultFormat = ssAutomatic
        SortSettings.Show = True
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        WordWrap = False
        ColWidths = (
          35
          51
          61
          63
          58
          54
          50
          51
          58
          54)
      end
    end
    object GridPanel: TRzPanel
      Left = 0
      Top = 65
      Width = 1084
      Height = 292
      Align = alClient
      BorderOuter = fsNone
      TabOrder = 1
      object WhitePanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1084
        Height = 12
        Align = alTop
        BorderOuter = fsNone
        BorderSides = [sdTop]
        BorderColor = clGradientActiveCaption
        BorderWidth = 1
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
      end
      object BjAdvStringGrid: TAdvStringGrid
        Left = 0
        Top = 12
        Width = 884
        Height = 280
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        BorderStyle = bsNone
        ColCount = 20
        Ctl3D = True
        DefaultRowHeight = 45
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedCols = 0
        FixedRows = 2
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        PopupMenu = AdvPopupMenu2
        ScrollBars = ssBoth
        TabOrder = 1
        OnKeyDown = BjAdvStringGridKeyDown
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        OnDblClickCell = BjAdvStringGridDblClickCell
        OnAnchorClick = BjAdvStringGridAnchorClick
        OnGetFloatFormat = BjAdvStringGridGetFloatFormat
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #24494#36719#38597#40657
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.ColumnWidth = True
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -12
        FilterDropDown.Font.Name = #24494#36719#38597#40657
        FilterDropDown.Font.Style = []
        FilterDropDown.GlyphActive.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          08000000000000010000530B0000530B00000001000000010000104A10001063
          100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
          63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
          8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
          1414020214141414141414141414141414030902141414141414141414141414
          030D090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141403
          130D09000214141414141414141403130D0D0501000214141414141414031311
          0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
          0806090909040100021403030303030303030303030303030303141414141414
          1414141414141414141414141414141414141414141414141414}
        FilterDropDown.Height = 200
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDown.Width = 200
        FilterDropDownClear = #20840#37096
        FilterDropDownCheck = True
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedColWidth = 35
        FixedRowHeight = 45
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = []
        FloatFormat = '%.2f'
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HoverFixedCells = hfFixedRows
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        ShowSelection = False
        SortSettings.DefaultFormat = ssAutomatic
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        WordWrap = False
        ColWidths = (
          35
          51
          61
          59
          51
          54
          50
          51
          58
          54
          64
          64
          64
          64
          64
          64
          64
          64
          64
          64)
      end
      object NextPagePanel: TRzPanel
        Left = 884
        Top = 12
        Width = 200
        Height = 280
        Align = alRight
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        object Btn_Left: TAdvGlowButton
          Left = 12
          Top = 141
          Width = 40
          Height = 40
          Hint = #24050#21040#36798#31532#19968#39029
          BorderStyle = bsNone
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          HotPicture.Data = {
            89504E470D0A1A0A0000000D494844520000001C0000001C0806000000720DDF
            94000004C64944415448C78D964B6C546514C77FDFBD77E6B6F37E1405ACC502
            2DA5A5010C0B562626C4981848346AD24413497C44515C94854BA22B1361615C
            4974E74AE20A8D21415134260D814080F02A8582B460DB69A79DB93377E6CE71
            3197B98F99A27731F7CE77BEEF3CFEE77CFF7314DF5E3570B4DD08E3C073400E
            A54084C01B01FCFFBDA5D6DBFF78B205E0344A8EA037260C1C6D17A2BE43D1D7
            DC28EEEEF061D718AB28EEF4346539E01544EDC2D1C634603C604C29CF40E0A4
            BBE65F5721637E87C232A40F615C43782160CCEFB2F80E0716DCCF70C4EA3F65
            7B3420D5F23E7C5085230D2968E552FEAF2CA5B57223E206A0BC40C4FD11774D
            9A083C9330886A8F22691E5140DED459DBAD37FD157FE1B83A50682D28957B0A
            21704294970FF7F3C5A763EC591F6B06A19A4E6A0A76F598BCD69FE0C998EECB
            A14FB702A3E315101F36CA4BDD9A6E9DFD83490E0CA7A989F0C9C43CC7A74AC4
            0DC54B7D71C6B76518CA44D8963379EFCCC35041355F5AE70A135FC4CDCF5C97
            C6D8A604EF6F4DD3D3A573BB5807147143B1E7A9188746336CCF9BCC551ACC57
            9CA60E7F6E5A11B6B2ECAB525101EFD6C574DED89C64FF608A753183DF672C0E
            4DCC315771D8B721CE075BD38C664D268B353E3BBFC06F3356B064C5AB7EC333
            A4C277A0096397C6ABFD09DE194AB3A64BE7D7FB165F5E5E64B258E3F9F5DD1C
            184EB3336F7265D1E6D8B5254EDC2DB162FBD10AEA355A55A9DAA9236F6ABCBD
            25CD9B9B93F4C6747E99B1F8FC4281897FAABCBC21CE87236976E44D6E166B1C
            BD54E0C7E9322B35E9E4B73FC2B0508112B2519DD7372678772845D6D43975DF
            E2AB2B4B9C9BABB2331FE5E36D194673512E156CBEBEBAC44F77CB2CDA0D9F31
            F1184BF923EC48BC8AA3BB7BD8DB172711D13875BFCCA7E717B8306FF3D66092
            8F46D20CA4A24C2ED7F8E2628113D3652C474214AC3CA33E235AEB62862A75D6
            AA536F08110DD676EB64A21A02CC941DACBAA06B9035757AE391D5C9BB75FFBC
            0D3AFB0E1EF6A0F404E7E6AB541C614B3A4A7F32C2BA98C174A9CEE9198BEBC5
            1A3BF2267D7183BE84C1A2DDE0F6729D6A2344DE226DE96A1A542ACCEC541CB8
            58B0A937603013617BDEA4371EE1CE4A9D3F1F58DC2BD519484719C99A0CA523
            14EC06532BAED1166A6E10E28F70EFC1C36D2DC6651DBB01D7976C6A0D184847
            D89137E98D1BDC5AAE7166B6C28CE5B03169309A33D992691ABD59ACF1A87602
            B97421F641EAF7C8A338CB11265D25839908DB73269B5311CE2F5439375765B6
            ECD09F3418CD9A0C67A314AA8F22F52ADE0F9F17A1F8C81B15E081725DB8B164
            E3088C64A38C644C36A5A25C59ACF1C7830A0F2D87817484E16C949D3D269623
            4C3CAC06D9CBE536CFA00A35C350E5598E70B96023C0B33D5DA44D8DC9629DBF
            1E58DC5AAE316B398CE69A90DB8EF0FD54A9437305C3ABA6F08024C16E2150B4
            1B7C73ADC8135D3AD3A53A3FDF2B8152D80D38F57799B5DD3A5B33517EB8536A
            A7B516011DBB2101E56DD398AF312B3F1977287D0921D441E66BC074189A681F
            A8C4D76EC4CB8D57FA8F97694031486B121C095B50D379720BD0178F97415143
            71B26D140C747EF172203EA75A91874AFF7132C5490D3882301D802480AF2234
            5585C6C8F0F8BDAA6C1A38A2A137CEA2640CE43822736DF74158B539D3615CED
            209C038EA3640CBD71F65F2AA229673DAD7B570000000049454E44AE426082}
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000036D4944415448C79595CF6F146518C73FCF3BB3B3B3DDB2DD5DB72C14
            8310B54291B6601B31D604A38927881A4FC678E36054387841305A13B5FA0790
            686AA20783170F1E38E8C11F898683B674AB010A4D3512401A76B7DB5DDB6566
            BAF37AD8A63BDB4ED9E5B9CC37F3E379DEEF679EF77985CFAF24F0E555E01822
            FD044303B24E87DDDBA873A0C751FA2B132D2F837C889040EB40122134A42D3D
            88660C2DBE09BC8E90A83F90E697374BD04E882480374D347DF7F4B10E146CA5
            D17B1422AADDDCE9A8C250CD0E63A690B454FDDE7AC722AA9E5CEBD68E81C3DB
            630C65A26B5604783011E1F907E2A4A3AA61511A76CD26F6A13C844E5378E5A1
            2DBCD59FA2E2D578E37C9EF3F37778326BF3EEC1348F774779B8CBE2F4446183
            0D850E3AD01B92470DE1855D9D9C78344926AAF8F166958AEBB32719E19D0369
            46B23617173CFE2C3AA14B54ABAC42DB4680A33BE38C3E9666F79608E7AE2DF3
            516E012530369CE1F0F61857173D4E4F16F8F69FA5D002E666CC1311C54BBB3B
            3939902211518CE58A7C3A53666FD2E2E4408A677B627C777D99F72F14F8A3E8
            52DBE4379A752A7AD5451D4BCC105EDC15E7ED8114695BF1E5D5325FCC56E830
            85F70EA639B4D566AAE0F0F174915CC1E56E2D62D6A934233AB12FC96B7D09EE
            8B1A7C3653E693E912BD5D114E0DA618C9DAFC76DBE1D44481DF6F3BB4EA3F15
            D8146B97A25B03EA3DBEA3C3C007165D9F6A4D632A615B8741A7A9F075C8065C
            A70D8E1E1F5D5BFDEAC0BA5C72293A3E7B9316C3DD3686083FDDACF2CBFC1DF6
            A52CF6A72CFA521617F20EF3D55A3D9F04265E40070A344879BE66A6E451727D
            86323623591B43093FDC586632EFD0DB6571286BD3DB15E162C9E5D6726DD3E1
            6570E4F86853F5D549BAA261BAE0325759E1E99E18CFF474B03566F0CDDFFFF1
            F3BF5576C40D9EBB3FCE7026CA5CC5E3FAD24A0359607487220AC6ECA247C5D3
            F4A72D9EDAD68152C2AFB7EAB8F6A72D86BA6DFAD31673158FBF2A5E7B88D657
            BAB2E852F634C3DD363B3B4D26F30E137987CB0B2E8F242D0E64A22CBA3EDFDF
            A86E4064367235230AC6F28AE6EBB90A9682BCE3932BD4C7C254C1E183A9224F
            646DCE5D5B0A3D0985F1594DF8C25B1C0AD2965668ED87236A35BCDBD05AFB0A
            E1D2FA8D76571BFA1EB470490167D09437EBA2F0C5EBD65A5306CE98883E8B46
            A13986C8607B3FA30522AD73C038A2CFFE0FA3DE4D1E285A19D6000000004945
            4E44AE426082}
          Transparent = True
          TabOrder = 0
          OnClick = Btn_LeftClick
          Appearance.BorderColor = 16316405
          Appearance.BorderColorHot = 16316405
          Appearance.BorderColorDown = 16316405
          Appearance.BorderColorFocused = 16316405
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Right: TAdvGlowButton
          Left = 48
          Top = 141
          Width = 40
          Height = 40
          Hint = #24050#21040#36798#26368#21518#19968#39029
          BorderStyle = bsNone
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          HotPicture.Data = {
            89504E470D0A1A0A0000000D494844520000001C0000001C0806000000720DDF
            94000004C14944415448C78D96DB6F14551CC73F677666B7BBDBEEA54060B994
            9BB52D2DD64443424C447C204A020FC4048DCF1A135212E40F90378D91F86204
            C3830F6A30468D4F3E18BCBD1824287231855AA8209452DA5DF6D2DD9D999D39
            3ECC74AEAD755ECECEFCF69CEFEFF2FD7DCF4F70664205B10B38063C8794AB11
            02A424B42281E0BBFFC95B838F67937308F113C8F7415C50413C0D9C05FAC2FF
            8E6E76C158E6E0A51E67DF6AE02537A8C30A70DC03F3A2C28F22E88010E1EF22
            021674286EEB038E2BC0BE1058D06519D81C8D5C128F58AC68DBA72065CEF33E
            BA5144238D1CE0D552FE3F9B24A7866B2302295D0C56820C78107A0F00C8658A
            19B1A921D62DE64308F694D21CDA9C65FC91C1679375EA1DC7A9AD3D1A07FAB2
            F46555DEBE5C615EB75D4722B51441277D93BA640B4838329463FFA62CB7EA26
            332D8B6FFF59C0B0E0858D59DEDC59209350986D5B9C1EAF5133ED4889459CBD
            AE2DC1C1A327C22970D2B62EA3F2446F8A6D398DFEBCC68396C5ED4607809DC5
            1443C5248385241D0913558396257D660AB7147E4EDD733D40E183E1D4E846D5
            64B66531544C325448B2B54765B665F1EBC33697CA3A83F924438514FD790DD3
            869B359366470622133E98F4D99FE0E0D889707F39AB6E4B26EB2615C366A0A0
            B1B398624B8FCA74D3E2FCACCE44CDA4AF5B65B4B78BFEBC8661072295918C05
            CE752294D15E73C2376C98AA77A8E83603798D91DE14FD398DE966879FEFB7B8
            DDB0D89055195D95E2F18286694BAE554C8C607A2392E4008A088F85F4D446B7
            2453F50E65C36630AF315C4C31D29BE246D5E497076D6E373A6CCCA88CF62619
            2EA69012C6AB066DCBF6154B10A8E18128204ECE17FBD14DEF44CD2421044FAD
            E9626B8FCA4831C9BDA6C58FF75BCCB43A8CAE4A315048F2646F8AAC26F8FE5E
            3B4C1A1744F11B33AA0A32265777174C2ABA8565435A1594320904F0C8B09969
            599836A88A605D5A0D303EAC566AEC460816DBC52CA6140E6DC9F2FA409ECDDD
            2A97E6758E9D7FC8D5B2C9DEF5698EECC8F3CCDA2EEE373BBC7BA5C2E7371BBE
            C244CAA5C67434429C425261FFA60C47870B3C96D3F8635EE7833FAB5C9A37D8
            B526C5D8709EE74B19A6EA269F4CD6F9E256838A6187A54E7A8A8E1A57045F75
            BA930A2F6FEFE6B581BC07F6CEE50A3F4CB7D85B4A737438CFEEB569EE364D4E
            5FAF72F666C395BA80E35E0F3A81A9F17670C04A19953DA534633B0A6CEBD1B8
            38D7E6C3F12ABFCDE96CCF691C1B29F06C29CD54DDE4E3891A5F4E3578D8B6FC
            C8BC4B20002A8311CA4591753E8C0D1738BCAD9B0D59952B659DF7AE3EE2DCBD
            262F6ECAF2C6608EDD6ECD4E8D57F974B24E4577DB2096C6304BD5D81DE8FE58
            D595607597C2E5799D93D71CB005D3A9C3969CCA5CDBE2D4B893C672DB5E7ED6
            885C95AACFA6F080F4D6EFF35C2BEB4C540DCE4DB7B0DCD27C35B540D394F4F5
            A87C345E0B042562878764CDEBB2337F493FEFA12B91B5E904A62D29EB768074
            92A422589F51F9BBD109533F98D2685BC815DA4222986959C42E680986257DB0
            25A8EF4F11119B0005A885B1647824F452CDD2931B3200C67FDB90350538171B
            0583E3A2370007F7C98032B9628F58D906DF29C049E08E3F1845D9268248614A
            2F49FD656C42DC017152017901295F41F035C8728CDA323A37B222F523B632F0
            0DF02A70F15FCBC251CD8AD9F3660000000049454E44AE426082}
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000036B4944415448C79596416C1B551086BFB7BBDEB5E3D88ED7360D2914
            A928A14D68EAA890022A87168913545CE080102A870A894839A785430EA8E580
            842AC4855C5124A4AA485C38C0A1A0822AD2404005B5B24AA296D44D63277662
            AFBDF67A5F0F7662C7B16B67A4D5FE5ABD37F3E69FF9DFAC60261104DE03CE02
            711A4D02A209B7FAD68C61019801BED18077818B4010291B9C085A9AE802431C
            292F2284AB011340B0BA48EC5CDCDE41671322084C28C0F09E36CADAD31D1E56
            0085BD9AE81A2B55E752EED83F6AEA9C8F8739F9A40FADB6296228BC73B0978F
            8683F8B55AA144435A6DB0B683FB9A9D190CF0E1E110D71FDA4CCDA5985BB589
            470C3E8987E9D3554A2ECCDED9C47264C73454DE9C9CAE46ADF7A00B8C9A062F
            C4BC3C17D2B9FEB048D272081B2AAF0DF470C4D4C996240B69BB239B2AA727A7
            EB1954DFF7F20E898D32A3A6CEF8135EF6FB55AEAD14F969D922E65339D1DFC3
            B1A8C19D4D8744B6DC45802673252C6D3ACCA76D06831E5E7FCACF4858673E65
            73793187AA084EECF37272C047B6EC723B53C691ED02B4A068AB402B56859BEB
            258E450DC6630643219DABC902BF240BA88AE0D4808F51D360A324B99D2D5176
            BBA4680B4BE041A1C2DD9CC32BFD5EC622064FF76AFCBE6AF3E3728167031E5E
            EDF7311635C8965CE653BB6BA26C8B630B34A5AA08C8392E0FAC0A8E2BF1A982
            905EEDEE65AB42A1C68DA9AB3BC55833AD7E68D17C6121801763069F8F4778DE
            34F875A5C8C46FAB54244C1D0D736628C07DCBE1C2C23ADFFE97AB532CEAB79F
            D25AA11255403CA23375D4642C6AF073B2C0F41F6B588EE483C120EF0F064817
            5D3EFB2BC395A53C858A6CA907AD7E6AB99D85575378799F972F8E4739D4A7F3
            C3FF16D3F369EEE51D2EBD14E3ED83BDDCB71C3EBE91E6F262AECA489BABBB25
            456F3DE36772A48FA19087ABC902E7E6527884E07C3CCC1B077A58DC2C736161
            9DEFEFE6912D68E948D111D36024ECE1DA4A914FFF5CE356A64C40573835E023
            65BB5CBA99E1CA520EBB0D2D8D58F075423653647A154E1FF0732365F3CF7A69
            BB298EC70CF6FB35BE5BCA3F9696462C9849C85623B24F57B02BB256BC9A6804
            84748535BBD2C5DCAC620D70B767424317654ABB655991B066BB7B1908AE02DC
            DA2D34F1F889D63DFE5741F225B0D14A68EDA799EC02B381E42B0DC16C8DA2B3
            88ADDF964E913A52F437C81904B38F0065745BDA17FEFACE0000000049454E44
            AE426082}
          Transparent = True
          TabOrder = 1
          OnClick = Btn_RightClick
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
      end
    end
  end
  object RzPanel4: TRzPanel
    Left = 0
    Top = 0
    Width = 1084
    Height = 40
    Align = alTop
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 2
    object RzLabel3: TRzLabel
      Left = 19
      Top = 7
      Width = 73
      Height = 24
      Caption = #39044#35686' '#35814#24773
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clGray
      Font.Height = -17
      Font.Name = #24494#36719#38597#40657
      Font.Style = [fsBold]
      ParentFont = False
      Transparent = True
    end
    object DdAdvStringGrid1: TAdvStringGrid
      Left = 620
      Top = 7
      Width = 1084
      Height = 45
      Cursor = crDefault
      Align = alCustom
      BevelInner = bvNone
      BevelOuter = bvNone
      BorderStyle = bsNone
      ColCount = 10
      Ctl3D = True
      DefaultRowHeight = 40
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedCols = 0
      RowCount = 2
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goEditing, goRowSelect]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      ScrollBars = ssVertical
      TabOrder = 0
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        '')
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.ColumnWidth = True
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #23435#20307
      FilterDropDown.Font.Style = []
      FilterDropDown.GlyphActive.Data = {
        36050000424D3605000000000000360400002800000010000000100000000100
        08000000000000010000530B0000530B00000001000000010000104A10001063
        100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
        63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
        8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
        FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
        1414020214141414141414141414141414030902141414141414141414141414
        030D090214141414141414141414141403130902141414141414141414141414
        0313090214141414141414141414141403130902141414141414141414141414
        0313090214141414141414141414141403130902141414141414141414141403
        130D09000214141414141414141403130D0D0501000214141414141414031311
        0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
        0806090909040100021403030303030303030303030303030303141414141414
        1414141414141414141414141414141414141414141414141414}
      FilterDropDown.Height = 200
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDown.Width = 200
      FilterDropDownClear = #20840#37096
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FilterNormalCellsOnly = False
      FixedColWidth = 36
      FixedRowHeight = 40
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      FloatFormat = '%.2f'
      FloatingFooter.Column = 10
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      ShowSelection = False
      SortSettings.DefaultFormat = ssAutomatic
      SortSettings.Show = True
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      WordWrap = False
      ColWidths = (
        36
        51
        61
        63
        58
        54
        50
        51
        58
        54)
    end
  end
  object AdvPopupMenu2: TAdvPopupMenu
    AutoHotkeys = maManual
    Version = '2.6.2.1'
    Left = 776
    Top = 368
    object MessageRecord: TMenuItem
      Caption = #28040#24687' '#21457#36865
      OnClick = MessageRecordClick
    end
  end
end
