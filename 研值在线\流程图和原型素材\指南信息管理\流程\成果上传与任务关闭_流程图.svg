<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果上传与任务关闭流程</text>

  <!-- 阶段一：文件上传与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：文件上传与校验</text>
  
  <!-- 节点1: 专家评审通过 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专家评审通过</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">业务处室确认</text>
  </g>

  <!-- 节点2: 上传面板 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">上传面板</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择指南文本与附件</text>
  </g>

  <!-- 节点3: 系统校验 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">格式、大小、病毒、签章</text>
  </g>

  <!-- 节点4: 待审列表 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">待审列表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">校验通过的文件</text>
  </g>

  <!-- 连接线 阶段一 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与入库 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与入库</text>

  <!-- 节点5: 提交审核 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">提交审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推送至审核人</text>
  </g>

  <!-- 节点6: 审核人核对 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核人核对</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">内容及校验报告</text>
  </g>

  <!-- 节点7: 写入指南库 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入指南库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">建立征集事项关联</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：任务关闭与归档 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：任务关闭与归档</text>

  <!-- 节点8: 锁定任务 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">锁定任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">协同空间锁定</text>
  </g>

  <!-- 节点9: 数据归档 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">版本记录、附件、日志</text>
  </g>

  <!-- 节点10: 审计库记录 -->
  <g transform="translate(650, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成归档编号</text>
  </g>

  <!-- 节点11: 任务完成 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务完成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态变更</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 350 525 Q 375 525 400 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 525 Q 625 525 650 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 525 Q 875 525 900 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：通知与统计 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：通知与统计</text>

  <!-- 节点12: 通知服务 -->
  <g transform="translate(400, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">通知服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推送完成消息</text>
  </g>

  <!-- 节点13: 统计更新 -->
  <g transform="translate(700, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新统计模块数据</text>
  </g>

  <!-- 异常处理节点 -->
  <g transform="translate(100, 310)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFEBEE" stroke="#EF9A9A" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验失败</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">退回修改</text>
  </g>

  <!-- 连接线：跨阶段连接 -->
  <path d="M 1100 200 C 1100 250, 300 260, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 380 C 900 430, 250 440, 250 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 560 C 1000 610, 600 620, 500 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 705 Q 650 705 700 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 异常处理连接线 -->
  <path d="M 800 200 C 800 240, 200 250, 190 310" stroke="#EF5350" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="450" y="240" text-anchor="middle" font-size="12" fill="#EF5350">校验失败</text>

  <!-- 审核驳回连接线 -->
  <path d="M 600 380 C 600 420, 190 430, 190 380" stroke="#EF5350" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="400" y="410" text-anchor="middle" font-size="12" fill="#EF5350">审核驳回</text>

  <!-- 重新提交循环 -->
  <path d="M 190 380 C 190 420, 1100 430, 1100 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="650" y="420" text-anchor="middle" font-size="12" fill="#666">修正后重新提交</text>

</svg>