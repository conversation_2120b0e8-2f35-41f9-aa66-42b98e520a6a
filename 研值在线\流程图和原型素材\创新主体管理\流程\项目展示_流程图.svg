<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">项目展示流程</text>

  <!-- 阶段一：页面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与数据加载</text>
  
  <!-- 节点1: 用户访问页面 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">项目展示页面</text>
  </g>

  <!-- 节点2: 枚举数据加载 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">枚举数据加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">项目类型、行业领域、状态</text>
  </g>

  <!-- 节点3: 初始数据生成 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">初始数据生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">统计概览与项目列表</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点4: 筛选条件调整 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件调整</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">用户输入关键字或条件</text>
  </g>

  <!-- 节点5: 查询参数组合 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询参数组合</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">调用数据聚合服务</text>
  </g>

  <!-- 节点6: 多区块数据刷新 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多区块数据刷新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">概览、列表、图表同步</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 420 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>

  <!-- 节点7: 查看详情按钮 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击查看详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">列表行中的按钮</text>
  </g>

  <!-- 节点8: 项目数据检索 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目数据检索</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">依据唯一标识获取</text>
  </g>

  <!-- 节点9: 会话缓存写入 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">会话缓存写入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全量项目信息</text>
  </g>

  <!-- 节点10: 详情抽屉渲染 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情抽屉渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录浏览动作</text>
  </g>

  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：操作执行与状态管理 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：操作执行与状态管理</text>

  <!-- 节点11: 投资关联视图 -->
  <g transform="translate(100, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">投资关联视图</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">投资数据聚合服务</text>
  </g>

  <!-- 节点12: 项目报告下载 -->
  <g transform="translate(350, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目报告下载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">文件生成服务</text>
  </g>

  <!-- 节点13: 筛选状态保留 -->
  <g transform="translate(600, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选状态保留</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">确保操作连续性</text>
  </g>

  <!-- 节点14: 会话缓存销毁 -->
  <g transform="translate(850, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">会话缓存销毁</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录最终操作日志</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -> 14 -->
  <path d="M 300 705 Q 325 705 350 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 705 Q 575 705 600 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 705 Q 825 705 850 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 从初始数据生成到筛选条件调整 -->
  <path d="M 860 200 C 860 240, 310 240, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从多区块数据刷新到查看详情 -->
  <path d="M 910 380 C 910 420, 200 420, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从详情抽屉到操作执行 -->
  <path d="M 950 560 C 950 600, 200 600, 200 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <!-- 从详情抽屉回到筛选状态 -->
  <path d="M 950 560 C 950 620, 700 620, 700 670" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="825" y="615" font-size="11" fill="#666">关闭抽屉返回列表</text>

  <!-- 从筛选状态回到多区块数据刷新 -->
  <path d="M 700 670 C 700 640, 910 640, 910 380" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="800" y="635" font-size="11" fill="#666">按需刷新图表</text>

  <!-- 从会话缓存销毁回到用户访问页面 -->
  <path d="M 950 740 C 950 780, 260 780, 260 200" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="600" y="795" font-size="11" fill="#666">用户重新访问页面</text>

</svg>