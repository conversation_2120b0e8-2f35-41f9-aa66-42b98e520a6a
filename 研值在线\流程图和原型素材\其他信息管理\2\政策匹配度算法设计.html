<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策匹配度算法设计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">政策匹配度算法设计</h1>
                    <p class="mt-2 text-sm text-gray-600">通过多维匹配条件计算创新主体与政策的匹配度，提供精准的政策推荐服务</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 版本管理下拉 -->
                    <div class="relative">
                        <button onclick="toggleVersionDropdown()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            版本管理
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="versionDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">模型版本管理</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                                        <div>
                                            <div class="font-medium text-gray-900">v2.1.0 (生产版本)</div>
                                            <div class="text-sm text-gray-600">创建人：张三 | 2024-01-15</div>
                                            <div class="text-xs text-green-600">优化权重算法，提升匹配精度</div>
                                        </div>
                                        <div class="flex space-x-1">
                                            <button class="text-red-600 hover:text-red-800 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">v2.0.3</div>
                                            <div class="text-sm text-gray-600">创建人：李四 | 2024-01-10</div>
                                            <div class="text-xs text-gray-500">修复行业代码匹配问题</div>
                                        </div>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                </svg>
                                            </button>
                                            <button class="text-green-600 hover:text-green-800 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 p-1">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建版本
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 选项卡导航 -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button onclick="switchTab('design')" id="tab-design" class="tab-button active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                    算法设计
                </button>
                <button onclick="switchTab('test')" id="tab-test" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                    批量测试
                </button>
            </nav>
        </div>

        <!-- 算法设计页签内容 -->
        <div id="content-design" class="tab-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 h-[calc(100vh-280px)]">
                <!-- 左侧：条件模型配置区 -->
                <div class="bg-white rounded-lg shadow-md p-6 overflow-y-auto">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">条件模型配置</h2>
                        <button onclick="saveModel()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            保存模型
                        </button>
                    </div>

                    <!-- 政策要素字典树 -->
                    <div class="space-y-4">
                        <!-- 对象类型 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    对象类型
                                </h3>
                                <span class="text-sm text-gray-500">权重: 25%</span>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">高新技术企业</span>
                                    <input type="number" value="100" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">科技型中小企业</span>
                                    <input type="number" value="90" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">创新型企业</span>
                                    <input type="number" value="85" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">一般企业</span>
                                    <input type="number" value="70" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                            </div>
                        </div>

                        <!-- 规模维度 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                    规模维度
                                </h3>
                                <span class="text-sm text-gray-500">权重: 20%</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">年营收 ≥ 2亿元</span>
                                    <input type="number" value="100" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">5000万 ≤ 年营收 < 2亿</span>
                                    <input type="number" value="85" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">年营收 < 5000万</span>
                                    <input type="number" value="70" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                            </div>
                        </div>

                        <!-- 行业代码 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                    </svg>
                                    行业代码
                                </h3>
                                <span class="text-sm text-gray-500">权重: 15%</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">软件和信息技术服务业</span>
                                    <input type="number" value="100" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">制造业</span>
                                    <input type="number" value="90" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">科学研究和技术服务业</span>
                                    <input type="number" value="95" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                            </div>
                        </div>

                        <!-- 财务指标 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    财务指标
                                </h3>
                                <span class="text-sm text-gray-500">权重: 25%</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">研发投入占比 ≥ 10%</span>
                                    <input type="number" value="100" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">5% ≤ 研发投入占比 < 10%</span>
                                    <input type="number" value="80" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">研发投入占比 < 5%</span>
                                    <input type="number" value="60" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                            </div>
                        </div>

                        <!-- 知识产权指标 -->
                        <div class="border rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                    知识产权指标
                                </h3>
                                <span class="text-sm text-gray-500">权重: 15%</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">发明专利 ≥ 10件</span>
                                    <input type="number" value="100" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">5件 ≤ 发明专利 < 10件</span>
                                    <input type="number" value="85" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm">发明专利 < 5件</span>
                                    <input type="number" value="70" class="w-16 text-xs border rounded px-2 py-1">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：数据映射区和匹配结果预览区 -->
                <div class="space-y-6">
                    <!-- 数据映射区 -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">数据映射配置</h2>
                        <div class="grid grid-cols-2 gap-4">
                            <!-- 政策字段 -->
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 mb-2">政策字段</h3>
                                <div class="space-y-2 max-h-48 overflow-y-auto border rounded p-2">
                                    <div draggable="true" class="p-2 bg-blue-50 border border-blue-200 rounded cursor-move hover:bg-blue-100">
                                        <span class="text-sm font-medium">企业类型</span>
                                    </div>
                                    <div draggable="true" class="p-2 bg-blue-50 border border-blue-200 rounded cursor-move hover:bg-blue-100">
                                        <span class="text-sm font-medium">年营业收入</span>
                                    </div>
                                    <div draggable="true" class="p-2 bg-blue-50 border border-blue-200 rounded cursor-move hover:bg-blue-100">
                                        <span class="text-sm font-medium">行业分类代码</span>
                                    </div>
                                    <div draggable="true" class="p-2 bg-blue-50 border border-blue-200 rounded cursor-move hover:bg-blue-100">
                                        <span class="text-sm font-medium">研发费用占比</span>
                                    </div>
                                    <div draggable="true" class="p-2 bg-blue-50 border border-blue-200 rounded cursor-move hover:bg-blue-100">
                                        <span class="text-sm font-medium">发明专利数量</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 创新主体字段 -->
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 mb-2">创新主体数据库字段</h3>
                                <div class="space-y-2 max-h-48 overflow-y-auto border rounded p-2 bg-gray-50" ondrop="drop(event)" ondragover="allowDrop(event)">
                                    <div class="p-2 bg-green-50 border border-green-200 rounded">
                                        <span class="text-sm font-medium">company_type → 企业类型</span>
                                        <button class="float-right text-red-500 hover:text-red-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="p-2 bg-green-50 border border-green-200 rounded">
                                        <span class="text-sm font-medium">annual_revenue → 年营业收入</span>
                                        <button class="float-right text-red-500 hover:text-red-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="p-2 border-2 border-dashed border-gray-300 rounded text-center text-gray-500">
                                        拖拽字段到此处进行映射
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span class="text-sm text-green-700">映射状态：已完成 2/5 个字段映射，类型校验通过</span>
                            </div>
                        </div>
                    </div>

                    <!-- 匹配结果预览区 -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">匹配结果预览</h2>
                        <div class="space-y-4">
                            <!-- 示例企业信息 -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h3 class="font-medium text-gray-900 mb-2">示例企业：深圳市创新科技有限公司</h3>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>企业类型：高新技术企业</div>
                                    <div>年营收：1.2亿元</div>
                                    <div>行业：软件和信息技术服务业</div>
                                    <div>研发投入占比：12%</div>
                                    <div>发明专利：15件</div>
                                    <div>员工数量：280人</div>
                                </div>
                            </div>

                            <!-- 匹配结果 -->
                            <div class="border rounded-lg overflow-hidden">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">政策名称</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">总体得分</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">匹配结果</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-4 text-sm font-medium text-gray-900">高新技术企业研发费用加计扣除</td>
                                            <td class="px-4 py-4 text-sm text-gray-900">
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div class="bg-green-600 h-2 rounded-full" style="width: 95%"></div>
                                                    </div>
                                                    <span class="font-medium text-green-600">95分</span>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    可享受
                                                </span>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <button onclick="showMatchDetail('policy1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-4 text-sm font-medium text-gray-900">科技型中小企业技术创新基金</td>
                                            <td class="px-4 py-4 text-sm text-gray-900">
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 72%"></div>
                                                    </div>
                                                    <span class="font-medium text-yellow-600">72分</span>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    部分符合
                                                </span>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <button onclick="showMatchDetail('policy2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-4 py-4 text-sm font-medium text-gray-900">制造业单项冠军企业奖励</td>
                                            <td class="px-4 py-4 text-sm text-gray-900">
                                                <div class="flex items-center">
                                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                                        <div class="bg-red-600 h-2 rounded-full" style="width: 35%"></div>
                                                    </div>
                                                    <span class="font-medium text-red-600">35分</span>
                                                </div>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    不符合
                                                </span>
                                            </td>
                                            <td class="px-4 py-4 text-sm">
                                                <button onclick="showMatchDetail('policy3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 批量测试页签内容 -->
        <div id="content-test" class="tab-content hidden">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-6">批量测试</h2>
                
                <!-- 测试配置 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择模型版本</label>
                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2">
                            <option>v2.1.0 (当前生产版本)</option>
                            <option>v2.0.3</option>
                            <option>v1.9.8</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">上传测试数据</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                            <div class="mt-2">
                                <button class="text-blue-600 hover:text-blue-500">点击上传</button>
                                <span class="text-gray-500">或拖拽文件到此处</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">支持 Excel、CSV 格式，最大 10MB</p>
                        </div>
                    </div>
                </div>

                <!-- 测试进度 -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700">测试进度</span>
                        <span class="text-sm text-gray-500">1,250 / 2,000 条记录</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 62.5%"></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>预计剩余时间：3分钟</span>
                        <span>处理速度：15条/秒</span>
                    </div>
                </div>

                <!-- 实时日志 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">实时日志</h3>
                        <div class="bg-gray-900 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
                            <div>[2024-01-15 14:30:25] 开始处理批量测试任务...</div>
                            <div>[2024-01-15 14:30:26] 加载模型版本 v2.1.0</div>
                            <div>[2024-01-15 14:30:27] 解析上传文件：test_data.xlsx</div>
                            <div>[2024-01-15 14:30:28] 共发现 2,000 条有效记录</div>
                            <div>[2024-01-15 14:30:29] 开始匹配计算...</div>
                            <div>[2024-01-15 14:30:30] 处理进度：100/2000 (5%)</div>
                            <div>[2024-01-15 14:30:35] 处理进度：250/2000 (12.5%)</div>
                            <div>[2024-01-15 14:30:40] 处理进度：400/2000 (20%)</div>
                            <div>[2024-01-15 14:30:45] 处理进度：550/2000 (27.5%)</div>
                            <div class="text-yellow-400">[2024-01-15 14:30:50] 警告：企业ID E001234 缺少行业代码字段</div>
                            <div>[2024-01-15 14:30:55] 处理进度：750/2000 (37.5%)</div>
                            <div>[2024-01-15 14:31:00] 处理进度：900/2000 (45%)</div>
                            <div>[2024-01-15 14:31:05] 处理进度：1050/2000 (52.5%)</div>
                            <div>[2024-01-15 14:31:10] 处理进度：1200/2000 (60%)</div>
                            <div class="text-blue-400">[2024-01-15 14:31:12] 正在处理第 1250 条记录...</div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">结果下载</h3>
                        <div class="space-y-3">
                            <div class="border rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium text-gray-900">批量测试结果_20240115_v2.1.0.xlsx</div>
                                        <div class="text-sm text-gray-500">生成时间：2024-01-15 14:25:30</div>
                                        <div class="text-sm text-gray-500">文件大小：2.3 MB</div>
                                    </div>
                                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        下载
                                    </button>
                                </div>
                            </div>
                            
                            <div class="border rounded-lg p-4 bg-gray-50">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="font-medium text-gray-500">当前测试任务</div>
                                        <div class="text-sm text-gray-500">预计完成时间：2024-01-15 14:33:00</div>
                                        <div class="text-sm text-gray-500">进度：62.5%</div>
                                    </div>
                                    <button disabled class="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed">
                                        处理中...
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div class="mt-6 grid grid-cols-2 gap-4">
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-green-600">1,180</div>
                                <div class="text-sm text-green-700">可享受政策</div>
                            </div>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-yellow-600">520</div>
                                <div class="text-sm text-yellow-700">部分符合</div>
                            </div>
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-red-600">300</div>
                                <div class="text-sm text-red-700">不符合</div>
                            </div>
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                                <div class="text-2xl font-bold text-blue-600">85.2%</div>
                                <div class="text-sm text-blue-700">平均匹配度</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 匹配详情弹窗 -->
    <div id="matchDetailModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">匹配详情分析</h3>
                <button onclick="closeMatchDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 各维度得分 -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">各维度得分详情</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="text-sm font-medium">对象类型匹配</span>
                            <div class="flex items-center">
                                <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">100分</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="text-sm font-medium">规模维度匹配</span>
                            <div class="flex items-center">
                                <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">85分</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="text-sm font-medium">行业代码匹配</span>
                            <div class="flex items-center">
                                <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">100分</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="text-sm font-medium">财务指标匹配</span>
                            <div class="flex items-center">
                                <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">100分</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                            <span class="text-sm font-medium">知识产权匹配</span>
                            <div class="flex items-center">
                                <div class="w-20 bg-gray-200 rounded-full h-2 mr-2">
                                    <div class="bg-green-600 h-2 rounded-full" style="width: 100%"></div>
                                </div>
                                <span class="text-sm font-medium text-green-600">100分</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 命中规则摘要 -->
                <div>
                    <h4 class="font-medium text-gray-900 mb-3">命中规则摘要</h4>
                    <div class="space-y-2">
                        <div class="flex items-start p-3 bg-green-50 border border-green-200 rounded">
                            <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <div class="text-sm font-medium text-green-800">企业类型符合</div>
                                <div class="text-xs text-green-600">高新技术企业认定有效</div>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-green-50 border border-green-200 rounded">
                            <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <div class="text-sm font-medium text-green-800">研发投入达标</div>
                                <div class="text-xs text-green-600">研发费用占比12% ≥ 要求的10%</div>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-green-50 border border-green-200 rounded">
                            <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <div class="text-sm font-medium text-green-800">知识产权充足</div>
                                <div class="text-xs text-green-600">发明专利15件 ≥ 要求的10件</div>
                            </div>
                        </div>
                        <div class="flex items-start p-3 bg-green-50 border border-green-200 rounded">
                            <svg class="w-5 h-5 text-green-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <div class="text-sm font-medium text-green-800">行业领域匹配</div>
                                <div class="text-xs text-green-600">软件和信息技术服务业为重点支持行业</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 总体评估 -->
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-medium text-blue-900 mb-2">总体评估结论</h4>
                <p class="text-sm text-blue-800">
                    该企业在所有关键维度均表现优秀，完全符合"高新技术企业研发费用加计扣除"政策的申请条件。
                    建议优先推荐此政策，预计可享受研发费用175%的税前扣除优惠。
                </p>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有选项卡的活动状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById('content-' + tabName).classList.remove('hidden');
            
            // 激活选中的选项卡
            const activeTab = document.getElementById('tab-' + tabName);
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }

        // 版本管理下拉菜单
        function toggleVersionDropdown() {
            const dropdown = document.getElementById('versionDropdown');
            dropdown.classList.toggle('hidden');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('versionDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleVersionDropdown') === -1) {
                dropdown.classList.add('hidden');
            }
        });

        // 保存模型
        function saveModel() {
            // 模拟保存过程
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>保存中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                alert('模型配置已保存成功！');
            }, 2000);
        }

        // 拖拽功能
        function allowDrop(ev) {
            ev.preventDefault();
        }

        function drop(ev) {
            ev.preventDefault();
            // 这里可以添加实际的拖拽处理逻辑
            console.log('字段映射功能');
        }

        // 显示匹配详情
        function showMatchDetail(policyId) {
            document.getElementById('matchDetailModal').classList.remove('hidden');
        }

        // 关闭匹配详情
        function closeMatchDetail() {
            document.getElementById('matchDetailModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('matchDetailModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeMatchDetail();
            }
        });
    </script>
</body>
</html>