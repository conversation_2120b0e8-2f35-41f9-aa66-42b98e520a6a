<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1600 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="800" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技成果展示流程图</text>

  <!-- 阶段一：数据集成 -->
  <text x="800" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据集成与同步</text>
  
  <!-- 节点1: 数据源 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据同步</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">国家知识产权局、学术文献数据库</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">院所成果管理系统</text>
  </g>

  <!-- 节点2: 数据处理 -->
  <g transform="translate(1120, 130)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理与存储</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">格式转换、唯一标识对齐、去重</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">写入科技成果主题库</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 480 170 C 650 170, 950 170, 1120 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="800" y="160" text-anchor="middle" font-size="12" fill="#555">每日同步</text>

  <!-- 阶段二：统计引擎 -->
  <text x="800" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计引擎与缓存</text>

  <!-- 节点3: 统计聚合 -->
  <g transform="translate(550, 310)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计引擎聚合</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">申请量、授权量、论文指标、转化收益</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">写入缓存数据库，推送变化日志</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 1260 210 C 1260 260, 800 280, 700 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示 -->
  <text x="800" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点4: 页面初始化 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面初始化</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">请求缓存接口获取指标卡</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">统计图及分布图数据</text>
  </g>

  <!-- 节点5: 用户交互 -->
  <g transform="translate(1120, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">点击成果类型或评价层级</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">请求清册接口，返回明细数据</text>
  </g>

  <!-- 连接线 3 -> 4 -->
  <path d="M 600 390 C 500 430, 400 460, 340 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 480 530 C 650 530, 950 530, 1120 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="800" y="520" text-anchor="middle" font-size="12" fill="#555">用户操作</text>

  <!-- 阶段四：文件访问 -->
  <text x="800" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：文件访问与安全</text>

  <!-- 节点6: 文件下载 -->
  <g transform="translate(550, 670)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件访问控制</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">校验文件访问权限</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">生成临时下载链接，更新访问记录</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 1260 570 C 1260 620, 800 640, 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：数据质量监控 -->
  <text x="800" y="820" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：数据质量监控</text>

  <!-- 节点7: 质量监控 -->
  <g transform="translate(550, 850)" filter="url(#soft-shadow)">
    <rect width="500" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="250" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据质量监控与治理</text>
    <text x="250" y="50" text-anchor="middle" font-size="12" fill="#555">检测成果关键字段完整性和专利状态变更</text>
    <text x="250" y="65" text-anchor="middle" font-size="12" fill="#555">发现异常自动派发工单，修正后同步至主题库与缓存</text>
  </g>

  <!-- 连接线 6 -> 7 -->
  <path d="M 700 750 Q 700 800 700 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈连接线 7 -> 2 (质量监控反馈到数据处理) -->
  <path d="M 1050 850 C 1300 850, 1450 600, 1450 300, 1450 210, 1400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1480" y="500" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1480, 500)">质量反馈</text>

</svg>