unit CcMddPz;

interface

uses
  Classes;

type
  TCcMddPz = class
  private
    FChMddFlag: integer;
    FChMddContent: string;
    FChMddState: integer;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property ChMddFlag: integer read FCh<PERSON>dd<PERSON>lag write FChMddFlag;
    property ChMddContent: string read FChMddContent write FChMddContent;
    property ChMddState: integer read FChMddState write FChMddState;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
