unit Cc_LoadingPicFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingPicForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingPicForm: TLoadingPicForm;

implementation

{$R *.dfm}

procedure TLoadingPicForm.FormCreate(Sender: TObject);
begin
  LoadingPicForm.left := (screen.width - LoadingPicForm.width) div 2;
  LoadingPicForm.top := (screen.height - LoadingPicForm.height) div 2;
end;

procedure TLoadingPicForm.FormShow(Sender: TObject);
begin
//  self.RxGIFAnimator1.Animate := true;
end;

end.
