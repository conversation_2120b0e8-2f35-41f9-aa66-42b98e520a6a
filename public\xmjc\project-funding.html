<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目经费 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .funding-card {
            background: linear-gradient(135deg, #FEF3C7, #FDE68A);
            border: 2px solid #F59E0B;
        }
        .amount-large {
            font-size: 2.5rem;
            font-weight: 900;
            background: linear-gradient(135deg, #F59E0B, #D97706);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">项目经费</h1>
                        <p class="text-sm text-gray-600">Project Funding</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>
                        导出报表
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 经费总览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="funding-card rounded-xl p-6 hover-lift">
                <div class="text-center">
                    <i class="fas fa-coins text-yellow-600 text-3xl mb-3"></i>
                    <p class="text-gray-700 text-sm mb-2">项目总经费</p>
                    <p class="amount-large">156.8</p>
                    <p class="text-gray-600 text-sm">亿元</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年 +18.9%
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-university text-blue-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">财政经费</p>
                    <p class="text-3xl font-bold text-blue-600">89.2</p>
                    <p class="text-gray-600 text-sm">亿元</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年 +22.5%
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-building text-green-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">企业自筹</p>
                    <p class="text-3xl font-bold text-green-600">67.6</p>
                    <p class="text-gray-600 text-sm">亿元</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年 +14.2%
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-chart-line text-purple-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">平均单项金额</p>
                    <p class="text-3xl font-bold text-purple-600">551</p>
                    <p class="text-gray-600 text-sm">万元</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年 +5.8%
                    </p>
                </div>
            </div>
        </div>

        <!-- 经费分布分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 经费来源结构 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                        经费来源结构
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-lg">饼图</button>
                        <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">柱状图</button>
                    </div>
                </div>
                
                <!-- 饼图区域 -->
                <div class="h-64 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-pie text-3xl mb-2"></i>
                        <p class="text-sm">经费来源分布图</p>
                        <p class="text-xs">财政 vs 企业自筹</p>
                    </div>
                </div>

                <!-- 来源明细 -->
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm font-medium">国家财政</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-blue-600">45.8亿</span>
                            <span class="text-xs text-gray-500 block">29.2%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm font-medium">省级财政</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-green-600">25.6亿</span>
                            <span class="text-xs text-gray-500 block">16.3%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <span class="text-sm font-medium">市级财政</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-yellow-600">17.8亿</span>
                            <span class="text-xs text-gray-500 block">11.4%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-sm font-medium">企业自筹</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold text-purple-600">67.6亿</span>
                            <span class="text-xs text-gray-500 block">43.1%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分层级经费统计 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-layer-group text-green-500 mr-2"></i>
                    分层级经费统计
                </h3>

                <!-- 层级经费卡片 -->
                <div class="space-y-4">
                    <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium text-red-800">国家级项目</h4>
                            <span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">346项</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-red-600">项目总经费：</span>
                                <span class="font-bold text-red-700">45.8亿</span>
                            </div>
                            <div>
                                <span class="text-red-600">平均金额：</span>
                                <span class="font-bold text-red-700">1,324万</span>
                            </div>
                            <div>
                                <span class="text-red-600">财政比例：</span>
                                <span class="font-bold text-red-700">78.5%</span>
                            </div>
                            <div>
                                <span class="text-red-600">增长率：</span>
                                <span class="font-bold text-green-700">+25.8%</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium text-yellow-800">省级项目</h4>
                            <span class="text-xs bg-yellow-100 text-yellow-600 px-2 py-1 rounded-full">892项</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-yellow-600">项目总经费：</span>
                                <span class="font-bold text-yellow-700">67.2亿</span>
                            </div>
                            <div>
                                <span class="text-yellow-600">平均金额：</span>
                                <span class="font-bold text-yellow-700">753万</span>
                            </div>
                            <div>
                                <span class="text-yellow-600">财政比例：</span>
                                <span class="font-bold text-yellow-700">64.2%</span>
                            </div>
                            <div>
                                <span class="text-yellow-600">增长率：</span>
                                <span class="font-bold text-green-700">+19.3%</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                        <div class="flex justify-between items-center mb-2">
                            <h4 class="font-medium text-green-800">市级项目</h4>
                            <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">1,609项</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-green-600">项目总经费：</span>
                                <span class="font-bold text-green-700">43.8亿</span>
                            </div>
                            <div>
                                <span class="text-green-600">平均金额：</span>
                                <span class="font-bold text-green-700">272万</span>
                            </div>
                            <div>
                                <span class="text-green-600">财政比例：</span>
                                <span class="font-bold text-green-700">42.6%</span>
                            </div>
                            <div>
                                <span class="text-green-600">增长率：</span>
                                <span class="font-bold text-green-700">+12.7%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 拨动经费TOP10项目 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                    拨动经费前10项目
                </h3>
                <div class="flex space-x-2">
                    <button class="text-sm bg-yellow-500 text-white px-3 py-1 rounded-lg">全部</button>
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">在研</button>
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">已结题</button>
                </div>
            </div>

            <!-- 项目列表 -->
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排名</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目层级</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨动金额</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成进度</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-center w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full font-bold">1</div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900">新材料产业化示范项目</div>
                                <div class="text-sm text-gray-500">高性能复合材料关键技术</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                宁波新材料科技园
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-600 rounded-full">国家级</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                2.8亿元
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 78%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">78%</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-blue-500 hover:text-blue-700">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-100 text-gray-600 rounded-full font-bold">2</div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900">智能制造关键技术研发</div>
                                <div class="text-sm text-gray-500">工业机器人控制系统</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                宁波工业技术研究院
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-600 rounded-full">省级</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                2.1亿元
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 92%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">92%</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-blue-500 hover:text-blue-700">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-100 text-gray-600 rounded-full font-bold">3</div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900">海洋工程装备创新平台</div>
                                <div class="text-sm text-gray-500">深海装备关键技术</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                宁波大学海洋学院
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-red-100 text-red-600 rounded-full">国家级</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                1.9亿元
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">45%</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-blue-500 hover:text-blue-700">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-100 text-gray-600 rounded-full font-bold">4</div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900">生物医药创新药研发</div>
                                <div class="text-sm text-gray-500">抗肿瘤药物开发</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                宁波生物医药研究院
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-600 rounded-full">省级</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                1.6亿元
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">85%</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-blue-500 hover:text-blue-700">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center justify-center w-8 h-8 bg-gray-100 text-gray-600 rounded-full font-bold">5</div>
                            </td>
                            <td class="px-4 py-4">
                                <div class="text-sm font-medium text-gray-900">人工智能算法优化</div>
                                <div class="text-sm text-gray-500">深度学习框架开发</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                                宁波AI科技有限公司
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-600 rounded-full">市级</span>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                                1.4亿元
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-full bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 67%"></div>
                                    </div>
                                    <span class="text-sm text-gray-600">67%</span>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap text-sm">
                                <button class="text-blue-500 hover:text-blue-700">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="mt-4 text-center">
                <button class="text-blue-500 hover:text-blue-700 text-sm">
                    查看完整排行榜 <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </div>

        <!-- 经费趋势分析 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-chart-line text-purple-500 mr-2"></i>
                    经费趋势分析
                </h3>
                <div class="flex space-x-2">
                    <button class="text-xs bg-purple-500 text-white px-3 py-1 rounded-lg">年度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">季度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">月度</button>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 经费增长趋势 -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-3">年度经费增长趋势</h4>
                    <div class="h-64 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-line text-3xl mb-2"></i>
                            <p class="text-sm">经费增长趋势图</p>
                            <p class="text-xs">2019-2024年度对比</p>
                        </div>
                    </div>
                </div>
                
                <!-- 经费结构变化 -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-3">经费结构变化</h4>
                    <div class="h-64 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-area text-3xl mb-2"></i>
                            <p class="text-sm">经费结构图</p>
                            <p class="text-xs">财政与自筹比例</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 数字动画
            const animateNumbers = () => {
                const targets = [
                    { element: document.querySelector('.amount-large'), target: 156.8, suffix: '' },
                ];

                targets.forEach(({ element, target, suffix }) => {
                    if (element) {
                        let current = 0;
                        const increment = target / 50;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                element.textContent = target.toFixed(1) + suffix;
                                clearInterval(timer);
                            } else {
                                element.textContent = current.toFixed(1) + suffix;
                            }
                        }, 30);
                    }
                });
            };

            // 延迟执行动画
            setTimeout(animateNumbers, 500);
        });
    </script>
</body>
</html> 