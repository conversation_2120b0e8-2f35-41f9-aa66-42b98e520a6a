<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策目标管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <h1 class="text-3xl font-bold text-gray-900">政策目标管理</h1>
                <p class="mt-2 text-sm text-gray-600">构建从目标设定、进度监测、动态修订到综合报告的闭环管理链路</p>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 h-[calc(100vh-120px)] overflow-auto">
        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">政策名称</label>
                    <input type="text" placeholder="请输入政策名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">目标状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="not_started">未开始</option>
                        <option value="in_progress">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="need_revision">需修订</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">目标负责人</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部负责人</option>
                        <option value="zhang">张三</option>
                        <option value="li">李四</option>
                        <option value="wang">王五</option>
                    </select>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    查询
                </button>
            </div>
        </div>

        <!-- 目标总览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">目标总数</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="p-3 bg-blue-100 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">达成率</p>
                        <p class="text-2xl font-bold text-green-600">78.5%</p>
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">超额率</p>
                        <p class="text-2xl font-bold text-purple-600">23.1%</p>
                    </div>
                    <div class="p-3 bg-purple-100 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">待修订</p>
                        <p class="text-2xl font-bold text-orange-600">12</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 目标列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-800">目标列表</h2>
                    <button onclick="openTargetModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        <span>新增目标</span>
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">指标类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">基准值</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标值</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前进度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">下次评估</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <button class="mr-2 text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                    <div class="text-sm font-medium text-gray-900">企业数字化转型率</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">定量指标</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">80%</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">65%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">进行中</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openTargetModal('edit', 1)" class="text-green-600 hover:text-green-900">编辑</button>
                                    <button onclick="openMonitorDrawer()" class="text-purple-600 hover:text-purple-900">监测</button>
                                    <button class="text-orange-600 hover:text-orange-900">修订</button>
                                    <button onclick="deleteTarget(1, '企业数字化转型率')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <button class="mr-2 text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                    <div class="text-sm font-medium text-gray-900">创新企业孵化数量</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">定量指标</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">50家</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">120家</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 95%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">95%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">已完成</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-02-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openTargetModal('edit', 2)" class="text-green-600 hover:text-green-900">编辑</button>
                                    <button onclick="openMonitorDrawer()" class="text-purple-600 hover:text-purple-900">监测</button>
                                    <button class="text-orange-600 hover:text-orange-900">修订</button>
                                    <button onclick="deleteTarget(2, '创新企业孵化数量')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <button class="mr-2 text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                    <div class="text-sm font-medium text-gray-900">政策满意度提升</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">定性指标</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">良好</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">优秀</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                        <div class="bg-orange-600 h-2 rounded-full" style="width: 35%"></div>
                                    </div>
                                    <span class="text-sm text-gray-900">35%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">需修订</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openTargetModal('edit', 3)" class="text-green-600 hover:text-green-900">编辑</button>
                                    <button onclick="openMonitorDrawer()" class="text-purple-600 hover:text-purple-900">监测</button>
                                    <button class="text-orange-600 hover:text-orange-900">修订</button>
                                    <button onclick="deleteTarget(3, '政策满意度提升')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 目标编辑弹窗 -->
    <div id="targetModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">目标编辑</h3>
                        <button onclick="closeTargetModal()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <form class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标模板</label>
                            <select name="template" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">选择目标模板</option>
                                <option value="economic">经济发展类</option>
                                <option value="innovation">创新驱动类</option>
                                <option value="social">社会民生类</option>
                                <option value="environment">环境保护类</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标名称 <span class="text-red-500">*</span></label>
                            <input type="text" name="target_name" placeholder="请输入目标名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">指标类型 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="indicator_type" value="quantitative" class="mr-2" required>
                                    <span class="text-sm text-gray-700">定量指标</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="indicator_type" value="qualitative" class="mr-2" required>
                                    <span class="text-sm text-gray-700">定性指标</span>
                                </label>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">基准值 <span class="text-red-500">*</span></label>
                                <input type="text" name="baseline_value" placeholder="请输入基准值" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">目标值 <span class="text-red-500">*</span></label>
                                <input type="text" name="target_value" placeholder="请输入目标值" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">负责人 <span class="text-red-500">*</span></label>
                            <select name="responsible" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">选择负责人</option>
                                <option value="zhang">张三</option>
                                <option value="li">李四</option>
                                <option value="wang">王五</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">评估周期</label>
                            <select name="evaluation_cycle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="monthly">月度</option>
                                <option value="quarterly">季度</option>
                                <option value="semi_annual">半年度</option>
                                <option value="annual">年度</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">目标描述</label>
                            <textarea name="description" rows="4" placeholder="请输入目标描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </form>
                </div>
                <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                    <button type="button" onclick="closeTargetModal()" class="px-4 py-2 text-gray-600 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                    <button type="button" onclick="saveTarget()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 监测与修订侧边抽屉 -->
    <div id="monitorDrawer" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex justify-end">
            <div class="bg-white w-full max-w-2xl h-full overflow-y-auto">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900">监测与修订</h3>
                        <button onclick="closeMonitorDrawer()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-6 space-y-6">
                    <!-- 进度图表 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-4">进度趋势</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <canvas id="progressChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- 效果雷达图 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-4">效果评估</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <canvas id="radarChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- 监测日志 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-4">监测日志</h4>
                        <div class="space-y-3">
                            <div class="bg-blue-50 border-l-4 border-blue-400 p-4">
                                <div class="flex justify-between">
                                    <p class="text-sm text-blue-700">目标进度更新：65% → 68%</p>
                                    <span class="text-xs text-blue-500">2024-01-10</span>
                                </div>
                            </div>
                            <div class="bg-green-50 border-l-4 border-green-400 p-4">
                                <div class="flex justify-between">
                                    <p class="text-sm text-green-700">阶段性目标达成</p>
                                    <span class="text-xs text-green-500">2024-01-08</span>
                                </div>
                            </div>
                            <div class="bg-orange-50 border-l-4 border-orange-400 p-4">
                                <div class="flex justify-between">
                                    <p class="text-sm text-orange-700">进度偏差预警：低于预期5%</p>
                                    <span class="text-xs text-orange-500">2024-01-05</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 修订建议 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-4">系统建议</h4>
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex">
                                <svg class="w-5 h-5 text-yellow-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                <div>
                                    <h5 class="text-sm font-medium text-yellow-800">建议调整目标值</h5>
                                    <p class="text-sm text-yellow-700 mt-1">基于当前进度和外部环境变化，建议将目标值从80%调整为75%，以确保目标的可达性。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 修订表单 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-4">目标修订</h4>
                        <form class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">新目标值</label>
                                <input type="text" value="75%" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">修订原因</label>
                                <textarea rows="3" placeholder="请输入修订原因" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                            </div>
                            <button class="w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
                                应用修订
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报告生成折叠面板 -->
    <div class="fixed right-0 top-1/2 transform -translate-y-1/2 z-40">
        <div id="reportPanel" class="bg-white rounded-l-lg shadow-lg border border-gray-200 transition-all duration-300 transform translate-x-80">
            <!-- 折叠按钮 -->
            <button onclick="toggleReportPanel()" class="absolute left-0 top-1/2 transform -translate-x-full -translate-y-1/2 bg-blue-600 text-white p-2 rounded-l-md hover:bg-blue-700 transition-colors">
                <svg id="reportPanelIcon" class="w-4 h-4 transform rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </button>
            
            <div class="w-80">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">报告生成</h3>
                </div>
                <div class="p-4 space-y-4 max-h-96 overflow-y-auto">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">报告模板</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="summary">目标总结报告</option>
                            <option value="progress">进度分析报告</option>
                            <option value="performance">绩效评估报告</option>
                            <option value="comprehensive">综合管理报告</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时间跨度</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="current_month">本月</option>
                            <option value="current_quarter">本季度</option>
                            <option value="current_year">本年度</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">输出格式</label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2" checked>
                                <span class="text-sm text-gray-700">PDF</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="mr-2">
                                <span class="text-sm text-gray-700">Excel</span>
                            </label>
                        </div>
                    </div>
                    <button onclick="generateReport()" class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                        <span id="generateBtnText">生成报告</span>
                        <div id="generateLoader" class="hidden inline-flex items-center ml-2">
                            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </button>
                    
                    <!-- 实时生成状态 -->
                    <div id="reportStatus" class="hidden bg-blue-50 border border-blue-200 rounded-md p-3">
                        <div class="flex items-center">
                            <svg class="animate-spin h-4 w-4 text-blue-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="text-sm text-blue-700">正在生成报告...</span>
                        </div>
                        <div class="mt-2 bg-blue-200 rounded-full h-2">
                            <div id="reportProgress" class="bg-blue-600 h-2 rounded-full transition-all duration-500" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <!-- 分享链接 -->
                    <div id="shareSection" class="hidden border-t border-gray-200 pt-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">分享链接</h4>
                        <div class="flex">
                            <input id="shareLink" type="text" readonly class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md bg-gray-50 text-sm" value="https://example.com/report/abc123">
                            <button onclick="copyShareLink()" class="px-3 py-2 bg-gray-600 text-white rounded-r-md hover:bg-gray-700 transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">历史报告</h4>
                        <div class="space-y-2 max-h-32 overflow-y-auto">
                            <div class="flex justify-between items-center text-sm p-2 bg-gray-50 rounded">
                                <div>
                                    <span class="text-gray-900 block">2024年Q1目标报告</span>
                                    <span class="text-gray-500 text-xs">2024-01-15 生成</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800 p-1" title="下载">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800 p-1" title="分享">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-sm p-2 bg-gray-50 rounded">
                                <div>
                                    <span class="text-gray-900 block">12月进度分析</span>
                                    <span class="text-gray-500 text-xs">2024-01-08 生成</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800 p-1" title="下载">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800 p-1" title="分享">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex justify-between items-center text-sm p-2 bg-gray-50 rounded">
                                <div>
                                    <span class="text-gray-900 block">绩效评估报告</span>
                                    <span class="text-gray-500 text-xs">2024-01-01 生成</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button class="text-blue-600 hover:text-blue-800 p-1" title="下载">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800 p-1" title="分享">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 目标编辑弹窗控制
        let currentEditMode = 'add'; // 'add' 或 'edit'
        let currentTargetId = null;
        
        function openTargetModal(mode = 'add', targetId = null) {
            currentEditMode = mode;
            currentTargetId = targetId;
            
            const modal = document.getElementById('targetModal');
            const title = modal.querySelector('h3');
            const form = modal.querySelector('form');
            
            // 设置标题
            title.textContent = mode === 'add' ? '新增目标' : '编辑目标';
            
            // 清空或填充表单
            if (mode === 'add') {
                form.reset();
            } else {
                // 编辑模式：填充现有数据
                fillTargetForm(targetId);
            }
            
            modal.classList.remove('hidden');
        }
        
        function closeTargetModal() {
            document.getElementById('targetModal').classList.add('hidden');
            currentEditMode = 'add';
            currentTargetId = null;
        }
        
        // 填充目标表单数据（编辑模式）
        function fillTargetForm(targetId) {
            // 模拟从服务器获取数据
            const mockData = {
                1: {
                    template: 'innovation',
                    name: '企业数字化转型率',
                    indicatorType: 'quantitative',
                    baselineValue: '45%',
                    targetValue: '80%',
                    responsible: 'zhang',
                    evaluationCycle: 'quarterly',
                    description: '推动企业数字化转型，提升企业竞争力和创新能力。'
                },
                2: {
                    template: 'innovation',
                    name: '创新企业孵化数量',
                    indicatorType: 'quantitative',
                    baselineValue: '120家',
                    targetValue: '200家',
                    responsible: 'li',
                    evaluationCycle: 'annual',
                    description: '通过孵化器建设，培育更多创新型企业。'
                }
            };
            
            const data = mockData[targetId];
            if (data) {
                const form = document.querySelector('#targetModal form');
                form.querySelector('select[name="template"]').value = data.template || '';
                form.querySelector('input[placeholder="请输入目标名称"]').value = data.name || '';
                form.querySelector(`input[name="indicator_type"][value="${data.indicatorType}"]`).checked = true;
                form.querySelector('input[placeholder="请输入基准值"]').value = data.baselineValue || '';
                form.querySelector('input[placeholder="请输入目标值"]').value = data.targetValue || '';
                form.querySelector('select[name="responsible"]').value = data.responsible || '';
                form.querySelector('select[name="evaluation_cycle"]').value = data.evaluationCycle || '';
                form.querySelector('textarea[placeholder="请输入目标描述"]').value = data.description || '';
            }
        }
        
        // 保存目标
        function saveTarget() {
            const form = document.querySelector('#targetModal form');
            const formData = new FormData(form);
            
            // 获取表单数据
            const targetData = {
                template: form.querySelector('select[name="template"]').value,
                name: form.querySelector('input[placeholder="请输入目标名称"]').value,
                indicatorType: form.querySelector('input[name="indicator_type"]:checked')?.value,
                baselineValue: form.querySelector('input[placeholder="请输入基准值"]').value,
                targetValue: form.querySelector('input[placeholder="请输入目标值"]').value,
                responsible: form.querySelector('select[name="responsible"]').value,
                evaluationCycle: form.querySelector('select[name="evaluation_cycle"]').value,
                description: form.querySelector('textarea[placeholder="请输入目标描述"]').value
            };
            
            // 表单验证
            if (!targetData.name) {
                alert('请输入目标名称');
                return;
            }
            if (!targetData.indicatorType) {
                alert('请选择指标类型');
                return;
            }
            if (!targetData.baselineValue) {
                alert('请输入基准值');
                return;
            }
            if (!targetData.targetValue) {
                alert('请输入目标值');
                return;
            }
            if (!targetData.responsible) {
                alert('请选择负责人');
                return;
            }
            
            // 模拟保存到服务器
            console.log('保存目标数据:', targetData);
            console.log('操作模式:', currentEditMode);
            console.log('目标ID:', currentTargetId);
            
            // 显示保存成功消息
            const message = currentEditMode === 'add' ? '目标新增成功！' : '目标更新成功！';
            alert(message);
            
            // 关闭弹窗
            closeTargetModal();
            
            // 刷新目标列表（实际项目中应该重新加载数据）
            // refreshTargetList();
        }
        
        // 删除目标
        function deleteTarget(targetId, targetName) {
            if (confirm(`确定要删除目标"${targetName}"吗？此操作不可撤销。`)) {
                console.log('删除目标ID:', targetId);
                alert('目标删除成功！');
                // 实际项目中应该调用API删除数据并刷新列表
            }
        }
        
        // 监测抽屉控制
        function openMonitorDrawer() {
            document.getElementById('monitorDrawer').classList.remove('hidden');
            initCharts();
        }
        
        function closeMonitorDrawer() {
            document.getElementById('monitorDrawer').classList.add('hidden');
        }
        
        // 初始化图表
        function initCharts() {
            // 进度趋势图
            const progressCtx = document.getElementById('progressChart').getContext('2d');
            new Chart(progressCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '目标进度',
                        data: [20, 35, 45, 55, 62, 65],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 效果雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['执行效率', '资源利用', '影响范围', '满意度', '可持续性'],
                    datasets: [{
                        label: '当前表现',
                        data: [75, 68, 82, 65, 70],
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                        pointBackgroundColor: 'rgb(34, 197, 94)'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        // 报告面板折叠控制
        let reportPanelOpen = false;
        
        function toggleReportPanel() {
            const panel = document.getElementById('reportPanel');
            const icon = document.getElementById('reportPanelIcon');
            
            if (reportPanelOpen) {
                panel.classList.add('translate-x-80');
                icon.classList.add('rotate-180');
                reportPanelOpen = false;
            } else {
                panel.classList.remove('translate-x-80');
                icon.classList.remove('rotate-180');
                reportPanelOpen = true;
            }
        }
        
        // 报告生成功能
        function generateReport() {
            const btnText = document.getElementById('generateBtnText');
            const loader = document.getElementById('generateLoader');
            const status = document.getElementById('reportStatus');
            const progress = document.getElementById('reportProgress');
            const shareSection = document.getElementById('shareSection');
            
            // 显示加载状态
            btnText.textContent = '生成中...';
            loader.classList.remove('hidden');
            status.classList.remove('hidden');
            
            // 模拟进度
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 20;
                if (progressValue > 100) progressValue = 100;
                progress.style.width = progressValue + '%';
                
                if (progressValue >= 100) {
                    clearInterval(progressInterval);
                    
                    // 完成生成
                    setTimeout(() => {
                        btnText.textContent = '生成报告';
                        loader.classList.add('hidden');
                        status.classList.add('hidden');
                        shareSection.classList.remove('hidden');
                        
                        // 重置进度条
                        progress.style.width = '0%';
                        
                        // 模拟下载
                        const link = document.createElement('a');
                        link.href = 'data:text/plain;charset=utf-8,政策目标管理报告\n生成时间：' + new Date().toLocaleString();
                        link.download = '政策目标报告_' + new Date().toISOString().slice(0, 10) + '.txt';
                        link.click();
                        
                        alert('报告生成完成！');
                    }, 500);
                }
            }, 200);
        }
        
        // 复制分享链接
        function copyShareLink() {
            const shareLink = document.getElementById('shareLink');
            shareLink.select();
            shareLink.setSelectionRange(0, 99999);
            
            try {
                document.execCommand('copy');
                alert('分享链接已复制到剪贴板！');
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制链接');
            }
        }
        
        // 点击外部关闭弹窗
        document.getElementById('targetModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTargetModal();
            }
        });
        
        document.getElementById('monitorDrawer').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMonitorDrawer();
            }
        });
    </script>
</body>
</html>