<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">知识产权智能问答系统架构流程图</text>
  <text x="700" y="65" font-size="16" text-anchor="middle" fill="#555">一套核心能力、双域部署、统一治理</text>

  <!-- 接入层 -->
  <text x="100" y="120" font-size="18" font-weight="500" fill="#1d4ed8">接入层 (Presentation Layer)</text>
  
  <!-- H5智能问答页面 -->
  <g transform="translate(50, 140)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">H5 智能问答页面</text>
    <text x="150" y="45" text-anchor="middle" font-size="12" fill="#555">微信自定义菜单 / 关键词回复</text>
    <text x="150" y="60" text-anchor="middle" font-size="12" fill="#555">公共服务平台浮窗、内部平台功能入口</text>
  </g>

  <!-- OAuth认证 -->
  <g transform="translate(400, 140)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">浙政钉 / 微信 OAuth</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">完成用户身份解析</text>
    <text x="140" y="60" text-anchor="middle" font-size="12" fill="#555">生成最小权限的访问 Token</text>
  </g>

  <!-- 边界层 -->
  <text x="100" y="280" font-size="18" font-weight="500" fill="#059669">边界层 (Access Layer)</text>
  
  <!-- WAF + CDN -->
  <g transform="translate(50, 300)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">互联网区 WAF + CDN</text>
    <text x="150" y="45" text-anchor="middle" font-size="12" fill="#555">DDoS 防护、速率限制</text>
    <text x="150" y="60" text-anchor="middle" font-size="12" fill="#555">IP 黑白名单</text>
  </g>

  <!-- DMZ反向代理 -->
  <g transform="translate(400, 300)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">DMZ 反向代理</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">Nginx/Gateway</text>
    <text x="140" y="60" text-anchor="middle" font-size="12" fill="#555">仅开放 443 端口</text>
  </g>

  <!-- 业务层 -->
  <text x="100" y="440" font-size="18" font-weight="500" fill="#7c3aed">业务层 (Application Layer)</text>
  
  <!-- 外网智能问答应用 -->
  <g transform="translate(50, 460)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能问答应用</text>
    <text x="100" y="40" text-anchor="middle" font-size="14" font-weight="500" fill="#7c3aed">(外网实例)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">部署在互联网区</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">负责公众流量</text>
  </g>

  <!-- 内网智能问答应用 -->
  <g transform="translate(280, 460)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能问答应用</text>
    <text x="100" y="40" text-anchor="middle" font-size="14" font-weight="500" fill="#7c3aed">(内网实例)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">部署在内部平台区</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">服务员工与托底计算</text>
  </g>

  <!-- 统一Chat Gateway -->
  <g transform="translate(520, 460)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统一 Chat Gateway</text>
    <text x="150" y="40" text-anchor="middle" font-size="12" fill="#555">会话管理与限流</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">内容安全四级检测</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">模型路由策略</text>
  </g>

  <!-- AI服务层 -->
  <text x="100" y="600" font-size="18" font-weight="500" fill="#dc2626">AI 服务层 (Model Layer)</text>
  
  <!-- DeepSeek-R1 -->
  <g transform="translate(50, 620)" filter="url(#soft-shadow)">
    <rect width="350" height="80" rx="8" ry="8" fill="#FEF2F2" stroke="#FCA5A5" stroke-width="1.5" />
    <text x="175" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">DeepSeek-R1 GPU 集群</text>
    <text x="175" y="40" text-anchor="middle" font-size="12" fill="#555">内部平台区 - 主力推理</text>
    <text x="175" y="55" text-anchor="middle" font-size="12" fill="#555">保证数据不出域</text>
    <text x="175" y="70" text-anchor="middle" font-size="12" fill="#555">可开放给外网实例使用</text>
  </g>

  <!-- 互联网大模型SaaS -->
  <g transform="translate(450, 620)" filter="url(#soft-shadow)">
    <rect width="350" height="80" rx="8" ry="8" fill="#FEF2F2" stroke="#FCA5A5" stroke-width="1.5" />
    <text x="175" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">互联网大模型 SaaS</text>
    <text x="175" y="40" text-anchor="middle" font-size="12" fill="#555">按量计费，应对公众高并发</text>
    <text x="175" y="55" text-anchor="middle" font-size="12" fill="#555">返回结果经 Gateway 统一过滤</text>
  </g>

  <!-- 知识层 -->
  <text x="100" y="760" font-size="18" font-weight="500" fill="#ea580c">知识层 (Knowledge Layer)</text>
  
  <!-- 智能体平台 -->
  <g transform="translate(50, 780)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能体平台 / Dify</text>
    <text x="125" y="40" text-anchor="middle" font-size="12" fill="#555">双域各一套</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">文档切分、Embedding</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">RAG 检索</text>
  </g>

  <!-- 对象存储 -->
  <g transform="translate(350, 780)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">对象存储</text>
    <text x="100" y="40" text-anchor="middle" font-size="12" fill="#555">MinIO / OSS</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">文档存储</text>
  </g>

  <!-- 向量数据库 -->
  <g transform="translate(600, 780)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">向量数据库</text>
    <text x="100" y="40" text-anchor="middle" font-size="12" fill="#555">PGVector</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">元数据统一标签</text>
  </g>

  <!-- 右侧治理层 -->
  <text x="900" y="120" font-size="18" font-weight="500" fill="#6366f1">数据治理与安全层</text>
  
  <!-- 四级安全检测 -->
  <g transform="translate(850, 140)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#EEF2FF" stroke="#C7D2FE" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">四级敏感词检测</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">请求、Prompt、检索结果、回复</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">违规内容即时阻断</text>
  </g>

  <!-- 日志审计 -->
  <g transform="translate(850, 250)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#EEF2FF" stroke="#C7D2FE" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志与审计</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">外网会话匿名化存储 7 天</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">内网日志 ClickHouse 180 天</text>
    <text x="140" y="70" text-anchor="middle" font-size="12" fill="#555">支持溯源</text>
  </g>

  <!-- 双域隔离 -->
  <g transform="translate(850, 360)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#EEF2FF" stroke="#C7D2FE" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">双域隔离</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">零信任网关最小权限通信</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">mTLS + 白名单</text>
  </g>

  <!-- 运维监控 -->
  <text x="900" y="500" font-size="18" font-weight="500" fill="#16a34a">运维与可观测性层</text>
  
  <!-- 监控告警 -->
  <g transform="translate(850, 520)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F0FDF4" stroke="#BBF7D0" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">Prometheus + Grafana</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">QPS、P95 延迟、GPU 利用率</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">异常触发告警</text>
  </g>

  <!-- CI/CD -->
  <g transform="translate(850, 630)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F0FDF4" stroke="#BBF7D0" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">CI/CD</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">GitLab CI 自动化发布</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">确保版本一致性</text>
  </g>

  <!-- 主要流程箭头 -->
  
  <!-- 接入层内部连接 -->
  <path d="M 350 180 C 375 180, 375 180, 400 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 接入层到边界层 -->
  <path d="M 200 220 C 200 250, 200 270, 200 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 540 220 C 540 250, 540 270, 540 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 边界层内部连接 -->
  <path d="M 350 340 C 375 340, 375 340, 400 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 边界层到业务层 -->
  <path d="M 200 380 C 200 410, 200 430, 200 460" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 540 380 C 540 410, 540 430, 540 460" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 业务层到Chat Gateway -->
  <path d="M 250 500 C 350 500, 450 500, 520 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 380 500 C 450 500, 450 500, 520 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- Chat Gateway到AI服务层 -->
  <path d="M 620 540 C 620 570, 225 590, 225 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 540 C 720 570, 625 590, 625 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 知识层内部连接 -->
  <path d="M 300 820 C 325 820, 325 820, 350 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 820 C 575 820, 575 820, 600 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- Chat Gateway到知识层 -->
  <path d="M 620 540 C 620 650, 175 750, 175 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 到治理层的连接 -->
  <path d="M 820 500 C 840 500, 840 180, 850 180" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 整体流程标签 -->
  <text x="700" y="930" font-size="14" text-anchor="middle" font-weight="500" fill="#333">整体流程：</text>
  <text x="700" y="950" font-size="12" text-anchor="middle" fill="#555">用户 → 接入层 → 边界层安全校验 → 智能问答应用 → Chat Gateway →</text>
  <text x="700" y="970" font-size="12" text-anchor="middle" fill="#555">① 路由模型（DeepSeek-R1 或 SaaS）② 并行拉取向量检索结果 → 四级内容安全 → 统一格式化 → 返回前端</text>

</svg> 