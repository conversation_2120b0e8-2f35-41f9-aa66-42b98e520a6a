<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策匹配度算法设计流程</text>

  <!-- 阶段一：模型配置 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：模型配置与校验</text>
  
  <!-- 节点1: 条件模型配置 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件模型配置</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">设定政策要素权重及阈值</text>
  </g>

  <!-- 节点2: 参数校验与草稿生成 -->
  <g transform="translate(550, 240)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">参数校验与草稿生成</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">校验参数合法性并生成模型草稿</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 220 700 240" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据处理 -->
  <text x="700" y="360" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据抽取与处理</text>

  <!-- 节点3: 数据映射抽取 -->
  <g transform="translate(200, 390)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据映射抽取</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">根据字段映射规则抽取数据</text>
  </g>

  <!-- 节点4: 数据清洗标准化 -->
  <g transform="translate(920, 390)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据清洗标准化</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">生成算法输入向量</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 600 310 C 500 340, 400 360, 340 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 480 425 C 650 425, 750 425, 920 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="415" text-anchor="middle" font-size="12" fill="#555">数据流转</text>

  <!-- 阶段三：算法计算 -->
  <text x="700" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：算法核心计算</text>
  
  <!-- 节点5: 规则过滤与得分计算 -->
  <g transform="translate(500, 550)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">规则过滤与得分计算</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">政策要素与创新主体指标匹配计算</text>
    <text x="200" y="70" text-anchor="middle" font-size="12" fill="#555">按权重汇总匹配度总分并标记等级</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 1060 460 C 1060 500, 800 530, 700 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：结果处理 -->
  <text x="700" y="690" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：结果展示与记录</text>

  <!-- 节点6: 结果写入与预览 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果写入与预览</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">得分明细写入结果表并展示</text>
  </g>

  <!-- 节点7: 日志记录与版本追溯 -->
  <g transform="translate(920, 720)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志记录与版本追溯</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">记录执行日志与版本号</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 550 630 C 450 660, 400 690, 340 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 7 -->
  <path d="M 850 630 C 950 660, 1000 690, 1060 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：版本管理 -->
  <text x="700" y="840" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：版本管理与发布</text>

  <!-- 节点8: 版本审核与生产发布 -->
  <g transform="translate(450, 860)" filter="url(#soft-shadow)">
    <rect width="500" height="80" rx="8" ry="8" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1.5" />
    <text x="250" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">版本审核与生产发布</text>
    <text x="250" y="55" text-anchor="middle" font-size="12" fill="#555">管理员审核模型表现并决定是否设为生产</text>
    <text x="250" y="70" text-anchor="middle" font-size="12" fill="#555">发布版本并向智能推送服务广播更新事件</text>
  </g>

  <!-- 连接线 6,7 -> 8 -->
  <path d="M 340 790 C 340 820, 550 840, 600 860" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1060 790 C 1060 820, 850 840, 800 860" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>