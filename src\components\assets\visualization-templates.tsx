'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area"
import {
  Search,
  Plus,
  Copy,
  Trash2,
  Edit2,
  Eye,
  Clock,
  User
} from "lucide-react"

// 模拟模板数据
const templates = [
  {
    id: 'tpl1',
    name: '预算执行分析大屏',
    description: '展示预算执行情况的可视化大屏',
    thumbnail: '/templates/budget.png',
    category: '预算管理',
    creator: '张三',
    createTime: '2024-03-25 10:30:00',
    updateTime: '2024-03-28 15:30:00'
  },
  {
    id: 'tpl2',
    name: '资金支付监控大屏',
    description: '实时监控资金支付情况的可视化大屏',
    thumbnail: '/templates/payment.png',
    category: '资金监控',
    creator: '李四',
    createTime: '2024-03-26 14:20:00',
    updateTime: '2024-03-28 16:45:00'
  },
  // ... 更多模板
]

interface TemplatesProps {
  isOpen: boolean
  onClose: () => void
  onSelect: (template: any) => void
}

export function VisualizationTemplates({ isOpen, onClose, onSelect }: TemplatesProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  const categories = [
    { id: 'all', name: '全部' },
    { id: 'budget', name: '预算管理' },
    { id: 'payment', name: '资金监控' },
    { id: 'project', name: '项目管理' },
    { id: 'analysis', name: '统计分析' }
  ]

  const filteredTemplates = templates.filter(template => {
    if (searchQuery) {
      return template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
             template.description.toLowerCase().includes(searchQuery.toLowerCase())
    }
    if (selectedCategory !== 'all') {
      return template.category === selectedCategory
    }
    return true
  })

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>模板管理</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* 搜索和过滤 */}
          <div className="flex items-center justify-between">
            <div className="relative w-[300px]">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索模板"
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              新建模板
            </Button>
          </div>

          {/* 分类过滤 */}
          <div className="flex items-center gap-2">
            {categories.map(category => (
              <Badge
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.name}
              </Badge>
            ))}
          </div>

          {/* 模板列表 */}
          <ScrollArea className="h-[500px]">
            <div className="grid grid-cols-2 gap-4">
              {filteredTemplates.map(template => (
                <div
                  key={template.id}
                  className="border border-[#E5E9EF] rounded-lg overflow-hidden hover:border-blue-500 cursor-pointer"
                >
                  <div className="aspect-video bg-gray-100">
                    {/* 模板缩略图 */}
                  </div>
                  <div className="p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium">{template.name}</h3>
                      <Badge variant="outline">{template.category}</Badge>
                    </div>
                    <p className="text-sm text-gray-500">{template.description}</p>
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {template.creator}
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        {template.updateTime}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onSelect(template)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        预览
                      </Button>
                      <Button variant="outline" size="sm">
                        <Copy className="h-4 w-4 mr-2" />
                        复制
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit2 className="h-4 w-4 mr-2" />
                        编辑
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog> 