<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">水产新品种管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">水产新品种管理</text>
  </g>

  <!-- 节点2: 权限验证与数据加载 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成指标概览，状态：已加载</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 500 165 Q 650 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点3: 用户设定筛选条件 -->
  <g transform="translate(150, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户设定筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">提交查询参数</text>
  </g>

  <!-- 节点4: 新品种检索服务 -->
  <g transform="translate(450, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新品种检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">后台触发检索</text>
  </g>

  <!-- 节点5: 创新主体关联服务 -->
  <g transform="translate(750, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">创新主体关联服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">同步启动关联查询</text>
  </g>

  <!-- 节点6: 数据整合与展示 -->
  <g transform="translate(1050, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据整合与展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新表格图表，状态：已完成</text>
  </g>

  <!-- 连接线 权限验证 -> 筛选条件 -->
  <path d="M 850 200 C 850 250, 300 250, 250 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 筛选条件 -> 检索服务 -->
  <path d="M 350 355 Q 400 355 450 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 检索服务 -> 关联服务 (并行) -->
  <path d="M 550 340 Q 650 340 750 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="650" y="330" text-anchor="middle" font-size="12" fill="#555">并行处理</text>
  
  <!-- 连接线 关联服务 -> 数据整合 -->
  <path d="M 950 355 Q 1000 355 1050 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点7: 新增/编辑操作 -->
  <g transform="translate(100, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增/编辑操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">数据合法性校验</text>
  </g>

  <!-- 节点8: 批量导入任务 -->
  <g transform="translate(320, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入任务</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">文件格式校验</text>
  </g>

  <!-- 节点9: 数据导出操作 -->
  <g transform="translate(540, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">生成Excel文件</text>
  </g>

  <!-- 节点10: 缓存刷新操作 -->
  <g transform="translate(760, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">清空并重载数据</text>
  </g>

  <!-- 节点11: 数据库更新 -->
  <g transform="translate(980, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据库更新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">写入品种库，重算指标</text>
  </g>

  <!-- 连接线 数据整合 -> 各种操作 -->
  <path d="M 1100 390 C 1100 450, 200 450, 190 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1100 390 C 1100 450, 410 450, 410 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1100 390 C 1100 450, 630 450, 630 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1100 390 C 1100 450, 850 450, 850 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 操作 -> 数据库更新 -->
  <path d="M 280 555 C 600 555, 900 555, 980 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 555 C 700 555, 900 555, 980 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 940 555 Q 960 555 980 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>

  <!-- 节点12: 定时同步任务 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步任务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">每日从农业农村部平台同步</text>
  </g>

  <!-- 节点13: 数据比对更新 -->
  <g transform="translate(550, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据比对更新</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">审定状态、权属信息、育种进度</text>
  </g>

  <!-- 节点14: 质量监控 -->
  <g transform="translate(900, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量监控</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">确保数据准确性及时效性</text>
  </g>

  <!-- 连接线 数据库更新 -> 定时同步 -->
  <path d="M 1070 590 C 1070 650, 325 650, 325 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 定时同步 -> 数据比对 -->
  <path d="M 450 755 Q 500 755 550 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 数据比对 -> 质量监控 -->
  <path d="M 800 755 Q 850 755 900 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：质量监控 -> 权限验证 -->
  <path d="M 1000 720 C 1200 720, 1200 100, 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1250, 400)">数据同步反馈</text>

</svg>