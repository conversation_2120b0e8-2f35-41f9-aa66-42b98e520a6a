<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员展示模块流程图</text>

  <!-- 阶段一：页面初始化与权限验证 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与权限验证</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">权限验证与主体范围确定</text>
  </g>

  <!-- 节点2: 指标概览初始化 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">指标概览初始化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">聚合统计结果加载</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据过滤 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据过滤</text>

  <!-- 节点3: 筛选条件设定 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">用户修改查询条件</text>
  </g>

  <!-- 节点4: 数据服务层处理 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据服务层处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">过滤人员、项目、成果数据</text>
  </g>

  <!-- 连接线 指标概览 -> 筛选条件 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选条件 -> 数据服务 -->
  <path d="M 500 455 C 650 455, 750 455, 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="445" text-anchor="middle" font-size="12" fill="#555">发送筛选参数</text>

  <!-- 阶段三：详情查看与交互操作 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互操作</text>
  
  <!-- 节点5: 指标卡片下钻 -->
  <g transform="translate(200, 630)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">指标卡片下钻</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">明细数据拉取与视图跳转</text>
  </g>

  <!-- 节点6: 人员画像侧栏 -->
  <g transform="translate(600, 630)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人员画像侧栏</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">教育经历、项目、知识产权</text>
  </g>

  <!-- 节点7: 用户行为记录 -->
  <g transform="translate(1000, 630)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户行为记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推荐与审计数据收集</text>
  </g>

  <!-- 连接线 数据服务 -> 指标卡片 -->
  <path d="M 950 490 C 850 550, 400 580, 300 630" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 指标卡片 -> 人员画像 -->
  <path d="M 400 665 C 500 665, 500 665, 600 665" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 人员画像 -> 行为记录 -->
  <path d="M 800 665 C 900 665, 900 665, 1000 665" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：导出分享与资源管理 -->
  <text x="700" y="780" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：导出分享与资源管理</text>
  
  <!-- 节点8: 导出分享操作 -->
  <g transform="translate(300, 820)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出分享操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">PDF/Excel生成与分享链接</text>
  </g>

  <!-- 节点9: 资源释放与统计 -->
  <g transform="translate(900, 820)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">资源释放与统计</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">缓存清理与访问统计更新</text>
  </g>

  <!-- 连接线 人员画像 -> 导出分享 -->
  <path d="M 600 700 C 550 750, 450 780, 400 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 人员画像 -> 资源释放 -->
  <path d="M 700 700 C 750 750, 850 780, 900 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <!-- 行为记录 -> 指标概览 (虚线) -->
  <path d="M 1000 630 C 1100 500, 900 400, 700 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="950" y="470" text-anchor="middle" font-size="11" fill="#666">推荐优化</text>

  <!-- 资源释放 -> 筛选条件 (虚线) -->
  <path d="M 900 820 C 800 750, 600 600, 400 490" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="650" y="650" text-anchor="middle" font-size="11" fill="#666">性能优化数据</text>

  <!-- 导出分享 -> 行为记录 (虚线) -->
  <path d="M 500 855 C 700 800, 900 750, 1100 665" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="800" y="760" text-anchor="middle" font-size="11" fill="#666">日志记录</text>

</svg>