<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法人科技特派员备案登记管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">法人科技特派员备案登记管理</h1>
            <p class="text-gray-600">建立科技服务类法人单位的系统化备案登记机制</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="bg-white rounded-lg shadow-md h-[calc(100vh-120px)] overflow-hidden">
            <!-- 选项卡导航 -->
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6 pt-4" aria-label="Tabs">
                    <button onclick="switchTab('registration')" id="tab-registration" class="tab-button active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600">
                        备案登记
                    </button>
                    <button onclick="switchTab('batch')" id="tab-batch" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        批次管理
                    </button>
                    <button onclick="switchTab('review')" id="tab-review" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        审核管理
                    </button>
                    <button onclick="switchTab('archive')" id="tab-archive" class="tab-button border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
                        历史归档
                    </button>
                </nav>
            </div>

            <!-- 选项卡内容 -->
            <div class="p-6 overflow-y-auto h-full">
                <!-- 备案登记内容 -->
                <div id="content-registration" class="tab-content">
                    <form class="space-y-8">
                        <!-- 法人单位登记信息区 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                法人单位登记信息
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        单位名称 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入单位名称">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        统一社会信用代码 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入统一社会信用代码">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        单位性质 <span class="text-red-500">*</span>
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">请选择单位性质</option>
                                        <option value="国有企业">国有企业</option>
                                        <option value="民营企业">民营企业</option>
                                        <option value="外资企业">外资企业</option>
                                        <option value="科研院所">科研院所</option>
                                        <option value="高等院校">高等院校</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        法人代表 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入法人代表姓名">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        单位地址 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入详细地址">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        授权负责人 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入授权负责人姓名">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        联系电话 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入联系电话">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        电子邮箱 <span class="text-red-500">*</span>
                                    </label>
                                    <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入电子邮箱">
                                </div>
                            </div>
                        </div>

                        <!-- 单位简介与对接园区信息区 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                单位简介与对接园区信息
                            </h3>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        单位简介 <span class="text-red-500">*</span>
                                    </label>
                                    <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请简要介绍单位基本情况、主营业务、技术优势等"></textarea>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            对接园区名称 <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入园区名称">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            园区地址
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入园区地址">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            管理单位
                                        </label>
                                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入管理单位">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            区域类型
                                        </label>
                                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                            <option value="">请选择区域类型</option>
                                            <option value="农业园区">农业园区</option>
                                            <option value="工业园区">工业园区</option>
                                            <option value="科技园区">科技园区</option>
                                            <option value="其他">其他</option>
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        服务联系窗口说明
                                    </label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请说明服务联系方式、对接窗口等信息"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 服务任务与协议信息区 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                服务任务与协议信息
                            </h3>
                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        科技服务协议主要内容 <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="agreement-content" rows="5" maxlength="2000" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请详细描述科技服务协议的主要内容、服务范围、合作方式等" oninput="updateCharCount('agreement-content', 'agreement-count', 2000)"></textarea>
                                    <div class="text-right text-sm text-gray-500 mt-1">
                                        <span id="agreement-count">0</span>/2000 字符
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        承担任务说明 <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="task-description" rows="4" maxlength="1500" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请说明法人科技特派员承担的具体任务、职责分工等" oninput="updateCharCount('task-description', 'task-count', 1500)"></textarea>
                                    <div class="text-right text-sm text-gray-500 mt-1">
                                        <span id="task-count">0</span>/1500 字符
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        预期目标 <span class="text-red-500">*</span>
                                    </label>
                                    <textarea id="expected-goals" rows="4" maxlength="1500" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请描述预期达成的目标、成果指标等" oninput="updateCharCount('expected-goals', 'goals-count', 1500)"></textarea>
                                    <div class="text-right text-sm text-gray-500 mt-1">
                                        <span id="goals-count">0</span>/1500 字符
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 主要成员信息区 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    主要成员信息
                                </h3>
                                <div class="space-x-2">
                                    <button type="button" onclick="addMember()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        添加成员
                                    </button>
                                    <button type="button" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                        导入模板
                                    </button>
                                </div>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-100">
                                        <tr>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出生年月</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务内容</th>
                                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="members-table" class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-4 py-3">
                                                <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入姓名">
                                            </td>
                                            <td class="px-4 py-3">
                                                <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                                    <option value="">选择</option>
                                                    <option value="男">男</option>
                                                    <option value="女">女</option>
                                                </select>
                                            </td>
                                            <td class="px-4 py-3">
                                                <input type="month" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                                            </td>
                                            <td class="px-4 py-3">
                                                <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入职务">
                                            </td>
                                            <td class="px-4 py-3">
                                                <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入服务内容">
                                            </td>
                                            <td class="px-4 py-3">
                                                <button type="button" onclick="removeMember(this)" class="text-red-600 hover:text-red-800">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 附件上传与批次归属信息区 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                附件上传与批次归属
                            </h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        备案批次 <span class="text-red-500">*</span>
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">请选择备案批次</option>
                                        <option value="2024-01">2024年第一批</option>
                                        <option value="2024-02">2024年第二批</option>
                                        <option value="2024-03">2024年第三批</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        备案状态
                                    </label>
                                    <div class="px-3 py-2 bg-yellow-100 text-yellow-800 rounded-md">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            待提交
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    附件上传
                                </label>
                                <div class="space-y-3">
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        <div class="text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                            <div class="mt-4">
                                                <label for="file-upload" class="cursor-pointer">
                                                    <span class="mt-2 block text-sm font-medium text-gray-900">
                                                        点击上传或拖拽文件到此区域
                                                    </span>
                                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                                                </label>
                                                <p class="mt-1 text-xs text-gray-500">
                                                    支持PDF、DOC、DOCX格式，单个文件不超过10MB
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                                            <div class="flex items-center">
                                                <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                                                </svg>
                                                <span class="text-sm text-gray-900">科技服务协议.pdf</span>
                                                <span class="ml-2 text-xs text-gray-500">(2.3MB)</span>
                                            </div>
                                            <div class="flex space-x-2">
                                                <button type="button" class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                                                <button type="button" class="text-red-600 hover:text-red-800 text-sm">删除</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <button type="button" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                保存草稿
                            </button>
                            <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                提交备案
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 批次管理内容 -->
                <div id="content-batch" class="tab-content hidden">
                    <div class="mb-6">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-semibold text-gray-900">批次管理</h2>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                新建批次
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-900">2024年第一批</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    进行中
                                </span>
                            </div>
                            <div class="space-y-2 text-sm text-gray-600">
                                <p><span class="font-medium">开始时间：</span>2024-01-15</p>
                                <p><span class="font-medium">截止时间：</span>2024-03-15</p>
                                <p><span class="font-medium">已申报：</span>25 家</p>
                                <p><span class="font-medium">已审核：</span>18 家</p>
                            </div>
                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    查看详情
                                </button>
                                <button class="flex-1 px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                    编辑
                                </button>
                            </div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-900">2024年第二批</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    筹备中
                                </span>
                            </div>
                            <div class="space-y-2 text-sm text-gray-600">
                                <p><span class="font-medium">开始时间：</span>2024-04-01</p>
                                <p><span class="font-medium">截止时间：</span>2024-06-01</p>
                                <p><span class="font-medium">已申报：</span>0 家</p>
                                <p><span class="font-medium">已审核：</span>0 家</p>
                            </div>
                            <div class="mt-4 flex space-x-2">
                                <button class="flex-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    查看详情
                                </button>
                                <button class="flex-1 px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200">
                                    编辑
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核管理内容 -->
                <div id="content-review" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">审核管理</h2>
                        <div class="flex space-x-4">
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已通过</option>
                                <option value="rejected">已驳回</option>
                            </select>
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部批次</option>
                                <option value="2024-01">2024年第一批</option>
                                <option value="2024-02">2024年第二批</option>
                            </select>
                            <input type="text" placeholder="搜索单位名称" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报批次</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审核状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">某某科技有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-15 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            待审核
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">审核</button>
                                        <button class="text-gray-600 hover:text-gray-900">查看</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">创新农业科技公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-10 09:15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            已通过
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-gray-600 hover:text-gray-900">查看</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 历史归档内容 -->
                <div id="content-archive" class="tab-content hidden">
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">历史归档</h2>
                        <div class="flex space-x-4">
                            <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部年度</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                            </select>
                            <input type="text" placeholder="搜索单位名称" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                导出数据
                            </button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-medium text-gray-900">2023年度汇总</h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    已归档
                                </span>
                            </div>
                            <div class="space-y-2 text-sm text-gray-600">
                                <p><span class="font-medium">备案单位：</span>156 家</p>
                                <p><span class="font-medium">通过审核：</span>142 家</p>
                                <p><span class="font-medium">服务园区：</span>89 个</p>
                                <p><span class="font-medium">归档时间：</span>2024-01-10</p>
                            </div>
                            <div class="mt-4">
                                <button class="w-full px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.add('hidden'));
            
            // 重置所有选项卡样式
            const tabs = document.querySelectorAll('.tab-button');
            tabs.forEach(tab => {
                tab.classList.remove('active', 'border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(`content-${tabName}`).classList.remove('hidden');
            
            // 激活选中的选项卡
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
        }

        // 字符计数功能
        function updateCharCount(textareaId, countId, maxLength) {
            const textarea = document.getElementById(textareaId);
            const counter = document.getElementById(countId);
            const currentLength = textarea.value.length;
            counter.textContent = currentLength;
            
            if (currentLength > maxLength * 0.9) {
                counter.classList.add('text-red-500');
                counter.classList.remove('text-gray-500');
            } else {
                counter.classList.add('text-gray-500');
                counter.classList.remove('text-red-500');
            }
        }

        // 添加成员功能
        function addMember() {
            const table = document.getElementById('members-table');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td class="px-4 py-3">
                    <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入姓名">
                </td>
                <td class="px-4 py-3">
                    <select class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                        <option value="">选择</option>
                        <option value="男">男</option>
                        <option value="女">女</option>
                    </select>
                </td>
                <td class="px-4 py-3">
                    <input type="month" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500">
                </td>
                <td class="px-4 py-3">
                    <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入职务">
                </td>
                <td class="px-4 py-3">
                    <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-1 focus:ring-blue-500" placeholder="请输入服务内容">
                </td>
                <td class="px-4 py-3">
                    <button type="button" onclick="removeMember(this)" class="text-red-600 hover:text-red-800">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </td>
            `;
            table.appendChild(newRow);
        }

        // 删除成员功能
        function removeMember(button) {
            const row = button.closest('tr');
            row.remove();
        }
    </script>
</body>
</html>