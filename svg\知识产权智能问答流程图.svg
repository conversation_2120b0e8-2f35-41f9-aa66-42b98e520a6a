<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1200" font-family="Arial, sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
    <!-- 层次背景渐变 -->
    <linearGradient id="layer-bg" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f8f9fa;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#e9ecef;stop-opacity:0.8" />
    </linearGradient>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="28" text-anchor="middle" font-weight="600" fill="#333">知识产权智能问答系统架构图</text>
  <text x="700" y="70" font-size="16" text-anchor="middle" fill="#666">一套核心能力、双域部署、统一治理</text>

  <!-- 用户层 -->
  <rect x="50" y="100" width="1300" height="60" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="125" font-size="14" font-weight="600" fill="#555">用户入口</text>
  
  <!-- 用户节点 -->
  <g transform="translate(200, 110)">
    <rect width="120" height="40" rx="6" ry="6" fill="#FFE8E8" stroke="#FFB3B3" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="60" y="28" text-anchor="middle" font-size="12" font-weight="600" fill="#333">微信用户</text>
  </g>
  
  <g transform="translate(400, 110)">
    <rect width="120" height="40" rx="6" ry="6" fill="#FFE8E8" stroke="#FFB3B3" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="60" y="28" text-anchor="middle" font-size="12" font-weight="600" fill="#333">浙政钉用户</text>
  </g>
  
  <g transform="translate(600, 110)">
    <rect width="120" height="40" rx="6" ry="6" fill="#FFE8E8" stroke="#FFB3B3" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="60" y="28" text-anchor="middle" font-size="12" font-weight="600" fill="#333">平台用户</text>
  </g>

  <g transform="translate(1000, 110)">
    <rect width="120" height="40" rx="6" ry="6" fill="#FFE8E8" stroke="#FFB3B3" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="60" y="28" text-anchor="middle" font-size="12" font-weight="600" fill="#333">内部员工</text>
  </g>

  <!-- 接入层 -->
  <rect x="50" y="180" width="1300" height="80" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="205" font-size="14" font-weight="600" fill="#555">接入层 (Presentation Layer)</text>
  
  <g transform="translate(200, 195)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">H5 智能问答页面</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">统一入口，携带身份信息</text>
  </g>

  <g transform="translate(500, 195)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">OAuth 认证</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">身份解析与Token生成</text>
  </g>

  <!-- 边界层 -->
  <rect x="50" y="280" width="1300" height="80" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="305" font-size="14" font-weight="600" fill="#555">边界层 (Access Layer)</text>
  
  <g transform="translate(200, 295)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E8F5E8" stroke="#A5D6A7" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">WAF + CDN</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">DDoS防护、速率限制</text>
  </g>

  <g transform="translate(500, 295)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E8F5E8" stroke="#A5D6A7" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">DMZ 反向代理</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">Nginx/Gateway 443端口</text>
  </g>

  <!-- 业务层 -->
  <rect x="50" y="380" width="1300" height="120" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="405" font-size="14" font-weight="600" fill="#555">业务层 (Application Layer)</text>
  
  <!-- 外网实例 -->
  <g transform="translate(150, 420)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="25" text-anchor="middle" font-size="13" font-weight="600" fill="#333">智能问答应用</text>
    <text x="90" y="40" text-anchor="middle" font-size="11" fill="#555">外网实例</text>
    <text x="90" y="55" text-anchor="middle" font-size="10" fill="#555">公众流量</text>
  </g>

  <!-- Chat Gateway -->
  <g transform="translate(400, 420)">
    <rect width="180" height="60" rx="8" ry="8" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="20" text-anchor="middle" font-size="13" font-weight="600" fill="#333">Chat Gateway</text>
    <text x="90" y="35" text-anchor="middle" font-size="10" fill="#555">统一逻辑服务</text>
    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#555">会话管理、内容安全、路由</text>
  </g>

  <!-- 内网实例 -->
  <g transform="translate(650, 420)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="25" text-anchor="middle" font-size="13" font-weight="600" fill="#333">智能问答应用</text>
    <text x="90" y="40" text-anchor="middle" font-size="11" fill="#555">内网实例</text>
    <text x="90" y="55" text-anchor="middle" font-size="10" fill="#555">员工与托底计算</text>
  </g>

  <!-- AI服务层 -->
  <rect x="50" y="520" width="1300" height="100" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="545" font-size="14" font-weight="600" fill="#555">AI服务层 (Model Layer)</text>
  
  <g transform="translate(200, 555)">
    <rect width="220" height="50" rx="8" ry="8" fill="#E1F5FE" stroke="#4FC3F7" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="110" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">DeepSeek-R1 GPU集群</text>
    <text x="110" y="40" text-anchor="middle" font-size="10" fill="#555">内部平台区，数据不出域</text>
  </g>

  <g transform="translate(500, 555)">
    <rect width="220" height="50" rx="8" ry="8" fill="#E1F5FE" stroke="#4FC3F7" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="110" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">互联网大模型SaaS</text>
    <text x="110" y="40" text-anchor="middle" font-size="10" fill="#555">按量计费，高并发</text>
  </g>

  <!-- 知识层 -->
  <rect x="50" y="640" width="1300" height="100" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="665" font-size="14" font-weight="600" fill="#555">知识层 (Knowledge Layer)</text>
  
  <g transform="translate(150, 675)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">智能体平台/Dify</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">双域各一套，RAG检索</text>
  </g>

  <g transform="translate(400, 675)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">对象存储</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">MinIO/OSS 文档存储</text>
  </g>

  <g transform="translate(650, 675)">
    <rect width="200" height="50" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="100" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">向量数据库</text>
    <text x="100" y="40" text-anchor="middle" font-size="10" fill="#555">PGVector 向量存储</text>
  </g>

  <!-- 数据治理与安全层 -->
  <rect x="50" y="760" width="1300" height="120" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="785" font-size="14" font-weight="600" fill="#555">数据治理与安全层 (Governance and Security)</text>
  
  <g transform="translate(100, 800)">
    <rect width="180" height="60" rx="8" ry="8" fill="#FFEBEE" stroke="#EF5350" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="600" fill="#333">四级敏感词检测</text>
    <text x="90" y="35" text-anchor="middle" font-size="10" fill="#555">全流程安全策略</text>
    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#555">违规内容即时阻断</text>
  </g>

  <g transform="translate(320, 800)">
    <rect width="180" height="60" rx="8" ry="8" fill="#FFEBEE" stroke="#EF5350" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="600" fill="#333">日志与审计</text>
    <text x="90" y="35" text-anchor="middle" font-size="10" fill="#555">外网7天/内网180天</text>
    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#555">ClickHouse存储</text>
  </g>

  <g transform="translate(540, 800)">
    <rect width="180" height="60" rx="8" ry="8" fill="#FFEBEE" stroke="#EF5350" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="90" y="20" text-anchor="middle" font-size="12" font-weight="600" fill="#333">双域隔离</text>
    <text x="90" y="35" text-anchor="middle" font-size="10" fill="#555">零信任网关</text>
    <text x="90" y="50" text-anchor="middle" font-size="10" fill="#555">mTLS + 白名单</text>
  </g>

  <!-- 运维与可观测性层 -->
  <rect x="50" y="900" width="1300" height="100" rx="8" fill="url(#layer-bg)" stroke="#ddd" stroke-width="1" />
  <text x="80" y="925" font-size="14" font-weight="600" fill="#555">运维与可观测性层 (Ops Layer)</text>
  
  <g transform="translate(200, 935)">
    <rect width="220" height="50" rx="8" ry="8" fill="#FFF8E1" stroke="#FFD54F" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="110" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">Prometheus + Grafana</text>
    <text x="110" y="40" text-anchor="middle" font-size="10" fill="#555">指标收集、异常告警</text>
  </g>

  <g transform="translate(500, 935)">
    <rect width="220" height="50" rx="8" ry="8" fill="#FFF8E1" stroke="#FFD54F" stroke-width="1.5" filter="url(#soft-shadow)" />
    <text x="110" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">CI/CD Pipeline</text>
    <text x="110" y="40" text-anchor="middle" font-size="10" fill="#555">GitLab CI 自动化发布</text>
  </g>

  <!-- 主要数据流向箭头 -->
  <!-- 用户到接入层 -->
  <path d="M 260 150 L 260 195" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 460 150 L 460 195" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 660 150 L 660 195" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1060 150 L 1060 195" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 接入层到边界层 -->
  <path d="M 300 245 L 300 295" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 245 L 600 295" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 边界层到业务层 -->
  <path d="M 240 345 L 240 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 490 345 L 490 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 业务层到AI服务层 -->
  <path d="M 490 480 L 310 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 490 480 L 610 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- AI服务层到知识层 -->
  <path d="M 310 605 L 250 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 610 605 L 750 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 侧边流程标识 -->
  <text x="1250" y="400" font-size="12" fill="#666" text-anchor="middle">用户请求</text>
  <text x="1250" y="420" font-size="12" fill="#666" text-anchor="middle">↓</text>
  <text x="1250" y="500" font-size="12" fill="#666" text-anchor="middle">AI推理</text>
  <text x="1250" y="520" font-size="12" fill="#666" text-anchor="middle">↓</text>
  <text x="1250" y="600" font-size="12" fill="#666" text-anchor="middle">知识检索</text>
  <text x="1250" y="620" font-size="12" fill="#666" text-anchor="middle">↑</text>
  <text x="1250" y="700" font-size="12" fill="#666" text-anchor="middle">结果返回</text>

  <!-- 版权信息 -->
  <text x="700" y="1180" font-size="12" text-anchor="middle" fill="#999">知识产权智能问答系统 - 双域部署架构设计图</text>

</svg> 