<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策对象管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">政策对象管理</h1>
            <p class="text-gray-600">构建覆盖主体类型、规模层级与行业领域的统一对象分类体系，为创新主体在政策享受分析与精准匹配过程中提供可信的标签依据</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">主体类型</label>
                            <div class="flex flex-wrap gap-2">
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    企业
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    高校
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    科研院所
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    个人
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规模区间</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部规模</option>
                                <option value="micro">微型（10人以下）</option>
                                <option value="small">小型（10-99人）</option>
                                <option value="medium">中型（100-499人）</option>
                                <option value="large">大型（500人以上）</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">行业类别</label>
                            <div class="relative">
                                <button id="industryButton" onclick="toggleIndustryTree()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                                    <span id="selectedIndustry">请选择行业类别</span>
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div id="industryTree" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                                    <ul class="space-y-2">
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    制造业
                                                </label>
                                            </div>
                                            <ul class="pl-6 mt-2 space-y-2 hidden">
                                                <li>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                        计算机、通信和其他电子设备制造业
                                                    </label>
                                                </li>
                                                <li>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                        医药制造业
                                                    </label>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    信息传输、软件和信息技术服务业
                                                </label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    科学研究和技术服务业
                                                </label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                            <input type="text" placeholder="输入对象编码或描述关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效状态</label>
                            <div class="flex items-center space-x-4 mt-2">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">全部</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已启用</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="inactive" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已停用</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 对象分类列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                对象分类列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openObjectModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增分类
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对象编码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主体类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规模级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属行业</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">ENT-SM-MFG-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">小型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">制造业-计算机、通信和其他电子设备制造业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已启用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-20 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="disableObject('obj1')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                            <button onclick="deleteObject('obj1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">UNI-LG-EDU-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">高校</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">大型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">教育</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已启用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15 09:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="disableObject('obj2')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                            <button onclick="deleteObject('obj2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">INS-MD-RES-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科研院所</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">中型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科学研究和技术服务业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已停用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-10 16:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="enableObject('obj3')" class="text-green-600 hover:text-green-900">启用</button>
                                            <button onclick="deleteObject('obj3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        分类统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已启用分类总数</div>
                                <div class="text-2xl font-bold text-blue-600">38</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近一月新增</div>
                                <div class="text-2xl font-bold text-green-600">12</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已停用分类</div>
                                <div class="text-2xl font-bold text-yellow-600">4</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量导入
                            </h2>
                            <button onclick="toggleImportPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="importPanelToggle">展开</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500">支持Excel批量导入对象分类信息</p>
                    </div>
                    <div id="importPanel" class="hidden">
                        <div class="p-6 space-y-4">
                            <div class="flex items-center justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                    </svg>
                                    下载导入模板
                                </a>
                                <span class="text-xs text-gray-500">支持.xlsx格式</span>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                            </div>
                            <div id="uploadProgress" class="hidden">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">上传进度</span>
                                    <span class="text-sm font-medium text-gray-700">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                        <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                            <li>第3行：对象编码已存在</li>
                                            <li>第5行：主体类型为必填项</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类编辑弹窗 -->
    <div id="objectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">对象分类编辑</h3>
                    <button onclick="closeObjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">对象编码 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入对象编码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-xs text-gray-500">建议使用主体类型-规模-行业的缩写组合</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">主体类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择主体类型</option>
                                    <option value="enterprise">企业</option>
                                    <option value="university">高校</option>
                                    <option value="institute">科研院所</option>
                                    <option value="individual">个人</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">规模级别 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择规模级别</option>
                                    <option value="micro">微型（10人以下）</option>
                                    <option value="small">小型（10-99人）</option>
                                    <option value="medium">中型（100-499人）</option>
                                    <option value="large">大型（500人以上）</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属行业 <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <button id="modalIndustryButton" onclick="toggleModalIndustrySelect()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                                        <span id="modalSelectedIndustry">请选择所属行业</span>
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="modalIndustrySelect" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                                        <div class="space-y-2">
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind1" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind1" class="ml-2 text-sm text-gray-700">制造业-计算机、通信和其他电子设备制造业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind2" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind2" class="ml-2 text-sm text-gray-700">制造业-医药制造业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind3" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind3" class="ml-2 text-sm text-gray-700">信息传输、软件和信息技术服务业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind4" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind4" class="ml-2 text-sm text-gray-700">科学研究和技术服务业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind5" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind5" class="ml-2 text-sm text-gray-700">教育</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分类描述</label>
                            <textarea rows="3" placeholder="请输入分类描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <!-- 生效状态 -->
                        <div>
                            <label class="flex items-center">
                                <span class="mr-3 text-sm font-medium text-gray-700">生效状态:</span>
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox" id="statusToggle" class="sr-only">
                                    <div class="block h-6 bg-gray-300 rounded-full w-12"></div>
                                    <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                                </div>
                                <span id="statusText" class="text-sm text-gray-700">已停用</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeObjectModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 行业树形选择器
        function toggleIndustryTree() {
            const tree = document.getElementById('industryTree');
            tree.classList.toggle('hidden');
        }
        
        // 弹窗中的行业选择器
        function toggleModalIndustrySelect() {
            const select = document.getElementById('modalIndustrySelect');
            select.classList.toggle('hidden');
        }
        
        // 对象分类编辑弹窗
        function openObjectModal() {
            document.getElementById('objectModal').classList.remove('hidden');
        }
        
        function closeObjectModal() {
            document.getElementById('objectModal').classList.add('hidden');
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 批量导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('importPanel');
            const toggle = document.getElementById('importPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 状态开关
        document.getElementById('statusToggle').addEventListener('change', function() {
            const statusText = document.getElementById('statusText');
            if (this.checked) {
                statusText.textContent = '已启用';
                document.querySelector('.dot').classList.add('transform', 'translate-x-6');
            } else {
                statusText.textContent = '已停用';
                document.querySelector('.dot').classList.remove('transform', 'translate-x-6');
            }
        });
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 其他功能函数
        function viewObject(objectId) {
            console.log('查看对象:', objectId);
        }
        
        function editObject(objectId) {
            openObjectModal();
            console.log('编辑对象:', objectId);
        }
        
        function disableObject(objectId) {
            if (confirm('确定要停用这个对象分类吗？')) {
                console.log('停用对象:', objectId);
            }
        }
        
        function enableObject(objectId) {
            if (confirm('确定要启用这个对象分类吗？')) {
                console.log('启用对象:', objectId);
            }
        }
        
        function deleteObject(objectId) {
            if (confirm('确定要删除这个对象分类吗？此操作不可恢复！')) {
                console.log('删除对象:', objectId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('objectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeObjectModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 行业树形选择器
            const industryButton = document.getElementById('industryButton');
            const industryTree = document.getElementById('industryTree');
            if (!industryButton.contains(e.target) && !industryTree.contains(e.target) && !industryTree.classList.contains('hidden')) {
                industryTree.classList.add('hidden');
            }
            
            // 弹窗中的行业选择器
            const modalIndustryButton = document.getElementById('modalIndustryButton');
            const modalIndustrySelect = document.getElementById('modalIndustrySelect');
            if (modalIndustryButton && modalIndustrySelect && !modalIndustryButton.contains(e.target) && !modalIndustrySelect.contains(e.target) && !modalIndustrySelect.classList.contains('hidden')) {
                modalIndustrySelect.classList.add('hidden');
            }
            
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 自定义样式
        document.addEventListener('DOMContentLoaded', function() {
            // 状态开关样式
            const style = document.createElement('style');
            style.textContent = `
                #statusToggle:checked + .block {
                    background-color: #2563eb;
                }
                #statusToggle:checked ~ .dot {
                    transform: translateX(100%);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>