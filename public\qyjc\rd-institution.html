<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发机构模块 - 企业监测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #14B8A6, #0F766E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" 
                            class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                        <span>返回</span>
                    </button>
                    <div class="w-8 h-8 bg-gradient-to-r from-teal-500 to-teal-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-university text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">研发机构模块</h1>
                        <p class="text-sm text-gray-600">企业研发机构资源情况分析与管理</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="bg-teal-500 text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                    <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 功能说明卡片 -->
        <div class="bg-gradient-to-r from-teal-500 to-teal-600 rounded-xl p-6 text-white mb-6">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-university text-2xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold mb-2">研发机构资源分析</h2>
                    <p class="text-teal-100">
                        全面分析辖区企业所属研发机构资源情况，包括市级/省级机构汇总数量、按领域分布统计，支持下钻查看各类企业研发机构清单，为科技资源配置提供决策支持。
                    </p>
                </div>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-filter text-teal-500 mr-2"></i>
                筛选条件
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">机构级别</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        <option>全部级别</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                        <option>区县级</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">机构类型</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        <option>全部类型</option>
                        <option>企业技术中心</option>
                        <option>工程技术研究中心</option>
                        <option>重点实验室</option>
                        <option>企业研究院</option>
                        <option>工程研究中心</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        <option>全部领域</option>
                        <option>新材料</option>
                        <option>新能源</option>
                        <option>生物医药</option>
                        <option>电子信息</option>
                        <option>智能制造</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">建设状态</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-teal-500 focus:border-teal-500">
                        <option>全部状态</option>
                        <option>已建成</option>
                        <option>在建</option>
                        <option>规划中</option>
                        <option>申报中</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    重置
                </button>
                <button class="bg-teal-500 text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition-colors">
                    应用筛选
                </button>
                <button class="border border-teal-500 text-teal-500 px-4 py-2 rounded-lg hover:bg-teal-50 transition-colors">
                    保存为模板
                </button>
            </div>
        </div>

        <!-- 概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">研发机构总数</p>
                        <p class="text-2xl font-bold text-gray-900">245</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +12个
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-university text-teal-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">省级机构</p>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                        <p class="text-blue-600 text-xs">
                            <i class="fas fa-star mr-1"></i>
                            占比 36.3%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-star text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">市级机构</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            占比 63.7%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">覆盖技术领域</p>
                        <p class="text-2xl font-bold text-gray-900">18</p>
                        <p class="text-purple-600 text-xs">
                            <i class="fas fa-cogs mr-1"></i>
                            全领域覆盖
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cogs text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 机构类型分布 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-chart-pie text-teal-500 mr-2"></i>
                        机构类型分布
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-teal-50 to-teal-100 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-teal-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">企业技术中心</span>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-teal-600">89个</p>
                            <p class="text-xs text-gray-600">36.3%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-blue-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">工程技术研究中心</span>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">67个</p>
                            <p class="text-xs text-gray-600">27.3%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-green-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">重点实验室</span>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">42个</p>
                            <p class="text-xs text-gray-600">17.1%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-purple-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">企业研究院</span>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-purple-600">28个</p>
                            <p class="text-xs text-gray-600">11.4%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-4 h-4 bg-orange-500 rounded-full"></div>
                            <span class="font-medium text-gray-900">工程研究中心</span>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-orange-600">19个</p>
                            <p class="text-xs text-gray-600">7.8%</p>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-teal-500 hover:text-teal-700 text-sm font-medium">
                        查看详细清单 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- 技术领域分布 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-sitemap text-blue-500 mr-2"></i>
                        技术领域分布
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">新材料技术</p>
                            <p class="text-xs text-gray-600">包含金属材料、无机非金属材料等</p>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">45个</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">查看清单</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">电子信息技术</p>
                            <p class="text-xs text-gray-600">包含集成电路、软件技术等</p>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">38个</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">查看清单</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">生物医药技术</p>
                            <p class="text-xs text-gray-600">包含生物技术药物、医疗器械等</p>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">32个</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">查看清单</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">先进制造技术</p>
                            <p class="text-xs text-gray-600">包含智能制造、精密制造等</p>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">29个</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">查看清单</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-gray-900">新能源技术</p>
                            <p class="text-xs text-gray-600">包含太阳能、储能技术等</p>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">26个</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">查看清单</button>
                        </div>
                    </div>
                    <div class="text-center">
                        <button class="text-gray-500 text-xs hover:text-gray-700">
                            展开更多领域 <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 优秀机构展示 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                优秀研发机构展示
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                    <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-crown text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h4 class="font-medium text-gray-900">宁波新材料技术研究院</h4>
                                <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">国家级</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-2">依托单位：宁波材料技术与工程研究所</p>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发人员</span>
                                    <span class="font-medium">235人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">年度投入</span>
                                    <span class="font-medium">8.5亿</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">专利数量</span>
                                    <span class="font-medium">126项</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">成果转化</span>
                                    <span class="font-medium">23项</span>
                                </div>
                            </div>
                            <button class="w-full mt-3 bg-yellow-500 text-white py-2 rounded-lg hover:bg-yellow-600 transition-colors text-xs">
                                查看详情
                            </button>
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                    <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-medal text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h4 class="font-medium text-gray-900">宁波智能制造技术中心</h4>
                                <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">省级</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-2">依托单位：宁波智能制造产业研究院</p>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发人员</span>
                                    <span class="font-medium">178人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">年度投入</span>
                                    <span class="font-medium">5.2亿</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">专利数量</span>
                                    <span class="font-medium">89项</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">成果转化</span>
                                    <span class="font-medium">15项</span>
                                </div>
                            </div>
                            <button class="w-full mt-3 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors text-xs">
                                查看详情
                            </button>
                        </div>
                    </div>
                </div>
                <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                    <div class="flex items-start space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i class="fas fa-star text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h4 class="font-medium text-gray-900">海天生物医药研究院</h4>
                                <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">省级</span>
                            </div>
                            <p class="text-xs text-gray-600 mb-2">依托单位：宁波海天生物医药有限公司</p>
                            <div class="grid grid-cols-2 gap-2 text-xs">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发人员</span>
                                    <span class="font-medium">142人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">年度投入</span>
                                    <span class="font-medium">3.8亿</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">专利数量</span>
                                    <span class="font-medium">67项</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">成果转化</span>
                                    <span class="font-medium">12项</span>
                                </div>
                            </div>
                            <button class="w-full mt-3 bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors text-xs">
                                查看详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-center">
                <button class="text-teal-500 hover:text-teal-700 text-sm font-medium">
                    查看全部机构 <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </div>

        <!-- 建设状态统计 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-gray-900 flex items-center">
                    <i class="fas fa-tasks text-purple-500 mr-2"></i>
                    建设状态统计
                </h3>
                <div class="flex space-x-2">
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">年度</button>
                    <button class="text-xs bg-purple-500 text-white px-3 py-1 rounded-lg">季度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">月度</button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-check text-white text-xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-green-600">187</p>
                        <p class="text-sm text-gray-600">已建成</p>
                        <p class="text-xs text-green-600 mt-1">占比 76.3%</p>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-cog text-white text-xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-blue-600">34</p>
                        <p class="text-sm text-gray-600">在建</p>
                        <p class="text-xs text-blue-600 mt-1">占比 13.9%</p>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-clipboard-list text-white text-xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-yellow-600">16</p>
                        <p class="text-sm text-gray-600">规划中</p>
                        <p class="text-xs text-yellow-600 mt-1">占比 6.5%</p>
                    </div>
                </div>
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-file-alt text-white text-xl"></i>
                        </div>
                        <p class="text-2xl font-bold text-purple-600">8</p>
                        <p class="text-sm text-gray-600">申报中</p>
                        <p class="text-xs text-purple-600 mt-1">占比 3.3%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据导出区域 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-download text-teal-500 mr-2"></i>
                数据导出与分享
            </h3>
            <div class="flex flex-wrap gap-3">
                <button class="bg-teal-500 text-white px-4 py-2 rounded-lg hover:bg-teal-600 transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    导出Excel
                </button>
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                    <i class="fas fa-file-pdf mr-2"></i>
                    生成PDF报告
                </button>
                <button class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-share-alt mr-2"></i>
                    分享链接
                </button>
                <button class="border border-blue-500 text-blue-500 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>
                    定期推送设置
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 下拉框变化事件
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log('筛选条件变化:', this.value);
                });
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 