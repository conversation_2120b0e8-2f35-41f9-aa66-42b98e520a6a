object CC_Ccgk_ScWcsjStatisticalReportAllForm: TCC_Ccgk_ScWcsjStatisticalReportAllForm
  Left = 0
  Top = 0
  ClientHeight = 761
  ClientWidth = 1304
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  OldCreateOrder = True
  Position = poScreenCenter
  PixelsPerInch = 96
  TextHeight = 12
  object MainPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 1304
    Height = 460
    Align = alTop
    BorderOuter = fsNone
    BorderColor = clWhite
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    object RzPanel1: TRzPanel
      Left = 0
      Top = 0
      Width = 1304
      Height = 460
      Align = alClient
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object RzPanel4: TRzPanel
        Left = 0
        Top = 0
        Width = 1304
        Height = 460
        Align = alClient
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #23435#20307
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
        object RzPanel6: TRzPanel
          Left = 0
          Top = 0
          Width = 1304
          Height = 89
          Align = alTop
          BorderOuter = fsNone
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #23435#20307
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          object RzLabel1: TRzLabel
            Left = 458
            Top = 55
            Width = 60
            Height = 19
            Caption = #26410#23436#25104#25968
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -15
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object Label_Jq: TRzLabel
            Left = 1014
            Top = 70
            Width = 94
            Height = 19
            Caption = #29983#20135#23436#25104' '#31579#36873
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clGray
            Font.Height = -15
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object RzLabel2: TRzLabel
            Left = 331
            Top = 50
            Width = 30
            Height = 31
            Caption = '05'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clGray
            Font.Height = -24
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object RzLabel5: TRzLabel
            Left = 382
            Top = 50
            Width = 30
            Height = 31
            Caption = '18'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlue
            Font.Height = -24
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object RzLabel3: TRzLabel
            Left = 362
            Top = 52
            Width = 19
            Height = 25
            Caption = #26376
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlue
            Font.Height = -19
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
          end
          object RzLabel6: TRzLabel
            Left = 412
            Top = 52
            Width = 19
            Height = 25
            Caption = #26085
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlue
            Font.Height = -19
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
          end
          object RzLabel7: TRzLabel
            Left = 434
            Top = 54
            Width = 16
            Height = 21
            Caption = #31561
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clGray
            Font.Height = -16
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
          end
          object RzLabel8: TRzLabel
            Left = 358
            Top = 52
            Width = 75
            Height = 28
            Caption = #21508' '#37096' '#38376
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clGray
            Font.Height = -21
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object RzLabel9: TRzLabel
            Left = 524
            Top = 55
            Width = 12
            Height = 19
            Caption = '()'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -15
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
          object RzLabel14: TRzLabel
            Left = 618
            Top = 14
            Width = 360
            Height = 19
            Caption = #32479#35745#25968#25454#21487#33021#26377#24046#24322#65292#35831#21040#36827#24230#26085#35760#20013#26597#30475#35814#32454#20449#24687#12290
            Font.Charset = DEFAULT_CHARSET
            Font.Color = 81119
            Font.Height = -15
            Font.Name = #24494#36719#38597#40657
            Font.Style = [fsBold]
            ParentFont = False
          end
        end
        object RzPanel7: TRzPanel
          Left = 50
          Top = 89
          Width = 903
          Height = 343
          Align = alLeft
          BorderOuter = fsNone
          BorderWidth = 1
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #23435#20307
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          object YkChart3: TChart
            Left = 1
            Top = 1
            Width = 901
            Height = 341
            BackWall.Brush.Gradient.Colors = <
              item
                Color = 15395562
              end
              item
                Color = clWhite
                Offset = 0.500000000000000000
              end>
            BackWall.Brush.Gradient.Direction = gdBottomTop
            BackWall.Brush.Gradient.EndColor = clWhite
            BackWall.Brush.Gradient.StartColor = 15395562
            BackWall.Pen.Visible = False
            Border.SmallSpace = 1
            BottomWall.Pen.Visible = False
            BottomWall.Size = 4
            Foot.Brush.Gradient.Colors = <
              item
                Color = clYellow
              end>
            Foot.Brush.Gradient.StartColor = clYellow
            Foot.Font.Color = clBlue
            Gradient.Colors = <
              item
                Color = clYellow
              end>
            Gradient.StartColor = clYellow
            LeftWall.Pen.Visible = False
            LeftWall.Size = 4
            Legend.Color = clInfoBk
            Legend.Frame.Color = clGray
            Legend.Frame.Visible = False
            Legend.Shadow.Color = 13421772
            Legend.Shadow.Transparency = 0
            Legend.Symbol.Pen.SmallSpace = 1
            Legend.TextStyle = ltsPlain
            Legend.Visible = False
            SubFoot.Brush.Gradient.Colors = <
              item
                Color = clYellow
              end>
            SubFoot.Brush.Gradient.StartColor = clYellow
            SubFoot.Font.Color = clBlue
            SubTitle.Brush.Gradient.Colors = <
              item
                Color = clYellow
              end>
            SubTitle.Brush.Gradient.StartColor = clYellow
            Title.Alignment = taLeftJustify
            Title.Brush.Gradient.Colors = <
              item
                Color = clYellow
              end>
            Title.Brush.Gradient.StartColor = clYellow
            Title.Color = clInfoBk
            Title.Font.Color = clBlack
            Title.Font.Height = -13
            Title.Frame.Color = clGray
            Title.Shadow.HorizSize = 0
            Title.Shadow.VertSize = 0
            Title.Text.Strings = (
              'TChart')
            Title.Transparent = False
            Title.Visible = False
            BottomAxis.Axis.Color = clGray
            BottomAxis.Axis.Width = 1
            BottomAxis.Axis.SmallSpace = 1
            BottomAxis.Grid.Color = 13421772
            BottomAxis.Grid.Visible = False
            BottomAxis.Inverted = True
            BottomAxis.LabelsFormat.Font.Color = 8618883
            BottomAxis.LabelsFormat.Font.Height = -12
            BottomAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
            BottomAxis.LabelsFormat.Font.Style = [fsBold]
            BottomAxis.LabelsFormat.TextAlignment = taCenter
            BottomAxis.MinorTicks.Visible = False
            BottomAxis.RoundFirstLabel = False
            BottomAxis.TicksInner.Color = 11119017
            BottomAxis.TicksInner.Visible = False
            BottomAxis.Title.Font.Height = -13
            ClipPoints = False
            DepthAxis.Axis.Width = 1
            DepthAxis.Grid.Color = clGray
            DepthAxis.LabelsFormat.TextAlignment = taCenter
            DepthAxis.MinorTicks.Visible = False
            DepthAxis.TicksInner.Color = 11119017
            DepthAxis.TicksInner.Visible = False
            DepthTopAxis.Axis.Width = 1
            DepthTopAxis.Grid.Color = clGray
            DepthTopAxis.LabelsFormat.TextAlignment = taCenter
            DepthTopAxis.MinorTicks.Visible = False
            DepthTopAxis.TicksInner.Color = 11119017
            DepthTopAxis.TicksInner.Visible = False
            Frame.Visible = False
            LeftAxis.Axis.Color = clSilver
            LeftAxis.Axis.Width = 1
            LeftAxis.Axis.SmallSpace = 1
            LeftAxis.AxisValuesFormat = '0'
            LeftAxis.Grid.Color = clGray
            LeftAxis.Grid.Visible = False
            LeftAxis.Increment = 100.000000000000000000
            LeftAxis.LabelsFormat.Font.Color = clGray
            LeftAxis.LabelsFormat.TextAlignment = taCenter
            LeftAxis.MinorTicks.Visible = False
            LeftAxis.TicksInner.Color = 11119017
            LeftAxis.TicksInner.Visible = False
            RightAxis.Axis.Width = 1
            RightAxis.Grid.Color = clGray
            RightAxis.LabelsFormat.TextAlignment = taCenter
            RightAxis.MinorTicks.Visible = False
            RightAxis.TicksInner.Color = 11119017
            RightAxis.TicksInner.Visible = False
            TopAxis.Axis.Width = 1
            TopAxis.Grid.Color = clGray
            TopAxis.LabelsFormat.TextAlignment = taCenter
            TopAxis.MinorTicks.Visible = False
            TopAxis.TicksInner.Color = 11119017
            TopAxis.TicksInner.Visible = False
            View3D = False
            View3DOptions.Elevation = 315
            View3DOptions.Orthogonal = False
            View3DOptions.Perspective = 0
            View3DOptions.Rotation = 360
            Zoom.Pen.Mode = pmNotXor
            Align = alClient
            Color = clWhite
            Enabled = False
            TabOrder = 0
            DefaultCanvas = 'TGDIPlusCanvas'
            ColorPaletteIndex = 0
            object RzLabel4: TRzLabel
              Left = 20
              Top = 519
              Width = 45
              Height = 20
              Caption = #24635#24037#20215
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGray
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = []
              ParentFont = False
            end
            object YkChartSeries4: TBarSeries
              BarPen.Visible = False
              Marks.Visible = False
              MultiBar = mbStacked
              XValues.Name = 'X'
              XValues.Order = loAscending
              YValues.Name = #38271#26465
              YValues.Order = loNone
              DataSources = (
                'SeriesTextSource2'
                'SeriesTextSource1')
            end
            object YkChartSeries3: TBarSeries
              BarPen.Visible = False
              Marks.Font.Height = -15
              Marks.Font.Style = [fsBold]
              Marks.Frame.Visible = False
              Marks.Visible = True
              Marks.Style = smsValue
              SeriesColor = 12622198
              ValueFormat = '0'
              MultiBar = mbStacked
              XValues.Name = 'X'
              XValues.Order = loAscending
              YValues.Name = #38271#26465
              YValues.Order = loNone
              DataSources = (
                'SeriesTextSource4'
                'SeriesTextSource3')
            end
            object GridBandTool5: TGridBandTool
              Band1.Color = 15329769
              AxisID = 2
            end
            object GridBandTool6: TGridBandTool
              Band1.Color = 15329769
              AxisID = 2
            end
            object GridBandTool9: TGridBandTool
              Band1.Color = 15329769
              AxisID = 2
            end
          end
        end
        object RzPanel2: TRzPanel
          Left = 0
          Top = 89
          Width = 50
          Height = 343
          Align = alLeft
          BorderOuter = fsNone
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #23435#20307
          Font.Style = []
          ParentFont = False
          TabOrder = 2
        end
        object RzPanel3: TRzPanel
          Left = 953
          Top = 89
          Width = 351
          Height = 343
          Align = alClient
          BorderOuter = fsNone
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #23435#20307
          Font.Style = []
          ParentFont = False
          TabOrder = 3
          object RzPanel8: TRzPanel
            Left = 0
            Top = 0
            Width = 55
            Height = 343
            Align = alLeft
            BorderOuter = fsNone
            Color = clWhite
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = #23435#20307
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            object Btn_Left: TAdvGlowButton
              Left = 4
              Top = 144
              Width = 50
              Height = 33
              BorderStyle = bsNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -12
              Font.Name = #24494#36719#38597#40657
              Font.Style = [fsBold]
              NotesFont.Charset = DEFAULT_CHARSET
              NotesFont.Color = clWindowText
              NotesFont.Height = -11
              NotesFont.Name = 'Tahoma'
              NotesFont.Style = []
              ParentFont = False
              Picture.Data = {
                89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                F40000021D49444154588563601805A360140C3060244691454808E70F26A556
                46C6FFE7CFAFEC5E4C4D07301352A0E71AC3FD835FE3C803ADF8C07FFFFE78E9
                48F35F7E7CE3C24D6A3980099FA4BD7FBC00A388FC89AB66F5869F0554193E4A
                18B3FFFDCF64442DCBF13AC0DE3F5EE003A7F4E18B662D3AD4B4101DB0601374
                090DE57FCD247DF8A2592B4D2D6760C09208CD3C3CF87EF21B1DBD68DAAAC3C0
                045501A599FEFF62D03CDDBC9DEBDB9B1B700DF822118BDC7F46C67F67E2A756
                337831FDC47080959F1FEF572E9D63178D5B75902D46A699FEFD64E0FEFA1861
                01231226C4676060F8C7CCCAA0726CFAF68B0B3BBD181890A2C0DEDF5FE025AF
                F9CE1BFA957883FD1F333BC36701150C87114533303030FDF9C9C0FCEB0B3F46
                20BDE7D0E87BA2186686CF725A00B80318FF33FC6364F8476FFB110E10FD7FAF
                58EEEE8A53F476003C0DEC59BDFAA36B0CBB13EBC9B21317CCBB70A603E6BF3F
                19B83E3EC29DE8187088C31221132BC31F56EE0F30F330B2A16B4C0CF7EBDF52
                272E987662CD86BAC7EA37B3FD7E875A14E32D4F51E5FFFF67FA77C6657A0D43
                3AE36FAC0E60606060B00F0DE5F9C0A47CECA279BB2EB203F83E5C67D03CDBD3
                7472E3BC7A0256120DB0BAFDE0EAD55F04FEDDB5D23F557E855A1691E4009823
                B8BFDDB0D23F495B47E08DBD639B367D16607F69AA75BAE12CDFFB9B0CFC2FCE
                FE6061F877869A0E20BE41F25FA19191F9FFB9F3AB7A5750D301A360148C8201
                07005642AA0127A25BB60000000049454E44AE426082}
              Transparent = True
              TabOrder = 0
              OnClick = Btn_LeftClick
              Appearance.Color = clSilver
              Appearance.ColorTo = 16316405
              Appearance.ColorChecked = 16316405
              Appearance.ColorCheckedTo = 16316405
              Appearance.ColorDisabled = 16316405
              Appearance.ColorDisabledTo = 16316405
              Appearance.ColorDown = 16316405
              Appearance.ColorDownTo = 16316405
              Appearance.ColorHot = 16316405
              Appearance.ColorHotTo = 16316405
              Appearance.ColorMirror = 16316405
              Appearance.ColorMirrorTo = 16316405
              Appearance.ColorMirrorHot = 16316405
              Appearance.ColorMirrorHotTo = 16316405
              Appearance.ColorMirrorDown = 16316405
              Appearance.ColorMirrorDownTo = 16316405
              Appearance.ColorMirrorChecked = 16316405
              Appearance.ColorMirrorCheckedTo = 16316405
              Appearance.ColorMirrorDisabled = 16316405
              Appearance.ColorMirrorDisabledTo = 16316405
              Layout = blGlyphLeftAdjusted
            end
          end
          object RzPanel9: TRzPanel
            Left = 55
            Top = 0
            Width = 296
            Height = 343
            Align = alClient
            BorderOuter = fsNone
            Color = clWhite
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = #23435#20307
            Font.Style = []
            ParentFont = False
            TabOrder = 1
            object RzLabel10: TRzLabel
              Left = 27
              Top = 286
              Width = 75
              Height = 19
              Caption = #36229#26399#26410#23436#25104
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGray
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = [fsBold]
              ParentFont = False
            end
            object RzLabel11: TRzLabel
              Left = 27
              Top = 318
              Width = 30
              Height = 19
              Caption = #8236#26410#25490
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGray
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = [fsBold]
              ParentFont = False
            end
            object RzLabel13: TRzLabel
              Left = 27
              Top = 351
              Width = 30
              Height = 19
              Caption = #27491#24120
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clGray
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = [fsBold]
              ParentFont = False
            end
            object RzCheckList1: TRzCheckList
              Left = 1
              Top = 6
              Width = 95
              Height = 259
              OnChange = RzCheckList1Change
              Align = alCustom
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clWindowText
              Font.Height = -12
              Font.Name = #24494#36719#38597#40657
              Font.Style = []
              ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
              ItemHeight = 19
              ParentFont = False
              TabOrder = 0
            end
            object Btn_Right: TAdvGlowButton
              Left = 96
              Top = 144
              Width = 50
              Height = 33
              BorderStyle = bsNone
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clBlack
              Font.Height = -12
              Font.Name = #24494#36719#38597#40657
              Font.Style = [fsBold]
              NotesFont.Charset = DEFAULT_CHARSET
              NotesFont.Color = clWindowText
              NotesFont.Height = -11
              NotesFont.Name = 'Tahoma'
              NotesFont.Style = []
              ParentFont = False
              Picture.Data = {
                89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
                F4000001E249444154588563601805A360140C3060A6B6810631E571E27AD6B1
                8652BC27EEDCB9F393AE0E300F48087A2965BEF0A5A68FEDDF5FDF9CB579FFAC
                78F8F0E12F7C7A98A8E9807F2CACC69FE4CCD83E89A833DCB0AF34FBA0607EDC
                DEDE9E876E0E400717DD3B75083982A60E60606060B8E881DF112C7056FD7F36
                938B396D8C0C7F311D85CF9948723FB94534BE0828623AC2AB53477F5BF9717B
                0606CB83070F7E41966384310C43CB76DED2CA7663FAF70B2103C34C58F80C98
                E25F05E518FEB1B021C4D168FDADE557386F1E323B71E2C477CC10F8F38DFF3B
                9724C33F2656840370188497C6032E7A77EA68B3D41EB16767B73E78F0E00F06
                C25AA80F1E19C71B7D50309F0CE3D3DD010CFFFF3130FE63F837600E5038BDE4
                8CC4EB4B79303E3C0DFC63E3FEC0F5F52903E3FFDF100122121D662294872442
                1CC06073E96589D7576C76ECD8012FA2E1B98061E67F56931D992D8CFFFFE10E
                155C3250F19F5C225A571CEABDFEB1B261244E832DA59765DEDFB0D8B265CB37
                64AD8C188651004C43525B6F5A15577D1255477180C136EC96E3F313D5003ECB
                69EE0083EDF82D6760402E88A80058FEFD3DCDFFF8CC0F0686FF1C32D7D61E97
                797FC3059FE50C0C544E030C0C0C0C06D1A5918CFFFFE9F13D3DD38A5EEE8F82
                51300A062500008E76AD342F239EFB0000000049454E44AE426082}
              Transparent = True
              TabOrder = 1
              OnClick = Btn_RightClick
              Appearance.Color = clSilver
              Appearance.ColorTo = 16316405
              Appearance.ColorChecked = 16316405
              Appearance.ColorCheckedTo = 16316405
              Appearance.ColorDisabled = 16316405
              Appearance.ColorDisabledTo = 16316405
              Appearance.ColorDown = 16316405
              Appearance.ColorDownTo = 16316405
              Appearance.ColorHot = 16316405
              Appearance.ColorHotTo = 16316405
              Appearance.ColorMirror = 16316405
              Appearance.ColorMirrorTo = 16316405
              Appearance.ColorMirrorHot = 16316405
              Appearance.ColorMirrorHotTo = 16316405
              Appearance.ColorMirrorDown = 16316405
              Appearance.ColorMirrorDownTo = 16316405
              Appearance.ColorMirrorChecked = 16316405
              Appearance.ColorMirrorCheckedTo = 16316405
              Appearance.ColorMirrorDisabled = 16316405
              Appearance.ColorMirrorDisabledTo = 16316405
              Layout = blGlyphLeftAdjusted
            end
            object Btn_Type1: TAdvGlowButton
              Left = 1
              Top = 286
              Width = 15
              Height = 16
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clNavy
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = []
              NotesFont.Charset = DEFAULT_CHARSET
              NotesFont.Color = clWindowText
              NotesFont.Height = -11
              NotesFont.Name = 'Tahoma'
              NotesFont.Style = []
              ParentFont = False
              TabOrder = 2
              Appearance.BorderColor = clRed
              Appearance.BorderColorHot = clRed
              Appearance.BorderColorCheckedHot = clRed
              Appearance.BorderColorDown = clRed
              Appearance.BorderColorChecked = clRed
              Appearance.BorderColorDisabled = clRed
              Appearance.BorderColorFocused = clRed
              Appearance.Color = clRed
              Appearance.ColorTo = clRed
              Appearance.ColorChecked = clRed
              Appearance.ColorCheckedTo = clRed
              Appearance.ColorDisabled = clRed
              Appearance.ColorDisabledTo = clRed
              Appearance.ColorDown = clRed
              Appearance.ColorDownTo = clRed
              Appearance.ColorHot = clRed
              Appearance.ColorHotTo = clRed
              Appearance.ColorMirror = clRed
              Appearance.ColorMirrorTo = clRed
              Appearance.ColorMirrorHot = clRed
              Appearance.ColorMirrorHotTo = clRed
              Appearance.ColorMirrorDown = clRed
              Appearance.ColorMirrorDownTo = clRed
              Appearance.ColorMirrorChecked = clRed
              Appearance.ColorMirrorCheckedTo = clRed
              Appearance.ColorMirrorDisabled = clRed
              Appearance.ColorMirrorDisabledTo = clRed
              Layout = blGlyphLeftAdjusted
            end
            object AdvGlowButton1: TAdvGlowButton
              Left = 1
              Top = 320
              Width = 16
              Height = 16
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clNavy
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = []
              NotesFont.Charset = DEFAULT_CHARSET
              NotesFont.Color = clWindowText
              NotesFont.Height = -11
              NotesFont.Name = 'Tahoma'
              NotesFont.Style = []
              ParentFont = False
              TabOrder = 3
              Appearance.BorderColor = 33023
              Appearance.BorderColorHot = 33023
              Appearance.BorderColorCheckedHot = 33023
              Appearance.BorderColorDown = 33023
              Appearance.BorderColorChecked = 33023
              Appearance.BorderColorDisabled = 33023
              Appearance.BorderColorFocused = 33023
              Appearance.Color = 33023
              Appearance.ColorTo = 33023
              Appearance.ColorChecked = 33023
              Appearance.ColorCheckedTo = 33023
              Appearance.ColorDisabled = 33023
              Appearance.ColorDisabledTo = 33023
              Appearance.ColorDown = 33023
              Appearance.ColorDownTo = 33023
              Appearance.ColorHot = 33023
              Appearance.ColorHotTo = 33023
              Appearance.ColorMirror = 33023
              Appearance.ColorMirrorTo = 33023
              Appearance.ColorMirrorHot = 33023
              Appearance.ColorMirrorHotTo = 33023
              Appearance.ColorMirrorDown = 33023
              Appearance.ColorMirrorDownTo = 33023
              Appearance.ColorMirrorChecked = 33023
              Appearance.ColorMirrorCheckedTo = 33023
              Appearance.ColorMirrorDisabled = 33023
              Appearance.ColorMirrorDisabledTo = 33023
              Layout = blGlyphLeftAdjusted
            end
            object AdvGlowButton3: TAdvGlowButton
              Left = 1
              Top = 353
              Width = 16
              Height = 16
              Font.Charset = DEFAULT_CHARSET
              Font.Color = clNavy
              Font.Height = -15
              Font.Name = #24494#36719#38597#40657
              Font.Style = []
              NotesFont.Charset = DEFAULT_CHARSET
              NotesFont.Color = clWindowText
              NotesFont.Height = -11
              NotesFont.Name = 'Tahoma'
              NotesFont.Style = []
              ParentFont = False
              TabOrder = 4
              Appearance.BorderColor = 12622198
              Appearance.BorderColorHot = 12622198
              Appearance.BorderColorCheckedHot = 12622198
              Appearance.BorderColorDown = 12622198
              Appearance.BorderColorChecked = 12622198
              Appearance.BorderColorDisabled = 12622198
              Appearance.BorderColorFocused = 12622198
              Appearance.Color = 12622198
              Appearance.ColorTo = 12622198
              Appearance.ColorChecked = 12622198
              Appearance.ColorCheckedTo = 12622198
              Appearance.ColorDisabled = 12622198
              Appearance.ColorDisabledTo = 12622198
              Appearance.ColorDown = 12622198
              Appearance.ColorDownTo = 12622198
              Appearance.ColorHot = 12622198
              Appearance.ColorHotTo = 12622198
              Appearance.ColorMirror = 12622198
              Appearance.ColorMirrorTo = 12622198
              Appearance.ColorMirrorHot = 12622198
              Appearance.ColorMirrorHotTo = 12622198
              Appearance.ColorMirrorDown = 12622198
              Appearance.ColorMirrorDownTo = 12622198
              Appearance.ColorMirrorChecked = 12622198
              Appearance.ColorMirrorCheckedTo = 12622198
              Appearance.ColorMirrorDisabled = 12622198
              Appearance.ColorMirrorDisabledTo = 12622198
              Layout = blGlyphLeftAdjusted
            end
          end
        end
        object RzPanel5: TRzPanel
          Left = 0
          Top = 432
          Width = 1304
          Height = 28
          Align = alBottom
          BorderOuter = fsNone
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #23435#20307
          Font.Style = []
          ParentFont = False
          TabOrder = 4
        end
      end
    end
  end
  object ScMainPanel: TRzPanel
    Left = 0
    Top = 460
    Width = 1304
    Height = 301
    Align = alClient
    BorderOuter = fsNone
    BorderColor = clGradientActiveCaption
    BorderWidth = 1
    Color = clWhite
    TabOrder = 1
    object GridPanel: TRzPanel
      Left = 1
      Top = 13
      Width = 1302
      Height = 287
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 0
      object ScAdvStringGrid: TAdvStringGrid
        Left = 0
        Top = 0
        Width = 1302
        Height = 287
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        BorderStyle = bsNone
        ColCount = 20
        Ctl3D = True
        DefaultRowHeight = 24
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedCols = 0
        FixedRows = 2
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        ScrollBars = ssBoth
        TabOrder = 0
        OnKeyDown = ScAdvStringGridKeyDown
        OnMouseDown = ScAdvStringGridMouseDown
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        OnCustomCellDraw = ScAdvStringGridCustomCellDraw
        OnGetCellBorder = ScAdvStringGridGetCellBorder
        OnGetCellBorderProp = ScAdvStringGridGetCellBorderProp
        OnDblClickCell = ScAdvStringGridDblClickCell
        OnGetFloatFormat = ScAdvStringGridGetFloatFormat
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #24494#36719#38597#40657
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.ColumnWidth = True
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -12
        FilterDropDown.Font.Name = #24494#36719#38597#40657
        FilterDropDown.Font.Style = []
        FilterDropDown.GlyphActive.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          08000000000000010000530B0000530B00000001000000010000104A10001063
          100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
          63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
          8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
          1414020214141414141414141414141414030902141414141414141414141414
          030D090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141403
          130D09000214141414141414141403130D0D0501000214141414141414031311
          0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
          0806090909040100021403030303030303030303030303030303141414141414
          1414141414141414141414141414141414141414141414141414}
        FilterDropDown.Height = 200
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDown.Width = 200
        FilterDropDownClear = #20840#37096
        FilterDropDownCheck = True
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedFooters = 1
        FixedColWidth = 35
        FixedRowHeight = 24
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = []
        FloatFormat = '%.2f'
        FloatingFooter.Visible = True
        GridImages = PngImageList1
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HoverFixedCells = hfFixedRows
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        ScrollProportional = True
        ScrollSynch = True
        ScrollWidth = 50
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        SortSettings.DefaultFormat = ssAutomatic
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        ColWidths = (
          35
          51
          61
          59
          51
          54
          50
          51
          58
          54
          64
          64
          64
          64
          64
          64
          64
          64
          64
          64)
        RowHeights = (
          24
          24
          24
          24
          24
          24
          24
          24
          24
          24)
      end
    end
    object RightWhitePanel: TRzPanel
      Left = 1
      Top = 1
      Width = 1302
      Height = 12
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdBottom]
      BorderColor = clGradientActiveCaption
      BorderWidth = 1
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 1
    end
  end
  object SeriesTextSource3: TSeriesTextSource
    Active = True
    Automatic = False
    HeaderLines = 1
    Fields = <>
    FieldSeparator = ','
    Series = YkChartSeries3
    Text.Strings = (
      '0,0'
      #23458#25143'1,10000'
      #23458#25143'2,20000'
      #23458#25143'3,30000'
      #23458#25143'4,40000'
      #23458#25143'5,50000')
    Left = 792
    Top = 24
  end
  object SeriesTextSource1: TSeriesTextSource
    Active = True
    Fields = <>
    FieldSeparator = ','
    Series = YkChartSeries4
  end
  object SeriesTextSource4: TSeriesTextSource
    Automatic = False
    HeaderLines = 1
    Fields = <>
    FieldSeparator = ','
    Series = YkChartSeries3
    Text.Strings = (
      '')
    Left = 872
    Top = 32
  end
  object TeeExcelSource1: TTeeExcelSource
  end
  object SeriesTextSource2: TSeriesTextSource
    Active = True
    Fields = <>
    FieldSeparator = ','
    Series = YkChartSeries4
  end
  object ActionList1: TActionList
    Left = 820
    Top = 12
    object actSc: TAction
      Category = 'Samples'
      Caption = 'Welcome'
      Checked = True
      ImageIndex = 44
    end
  end
  object PngImageList1: TPngImageList
    PngImages = <
      item
        Background = clWindow
        Name = 'filter'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008030000015F2A3F
          C5000000017352474200AECE1CE90000000467414D410000B18F0BFC61050000
          0015504C5445000000A2A2A2A3A0A0A2A1A1A2A1A1A2A2A2A1A0A054428CA400
          00000774524E53001656FFAB55EBCC841724000000097048597300000B120000
          0B1201D2DD7EFC000000634944415478DA8D4EDB0E8020083D88F6FFDF1B2221
          B856ABB57CC073E3420081C92B9983C9D0C48BA36A50D7784F8AF51C780E2552
          D5F46E3D00C7D709658A3AAD4D312C324C3DC365BCB67F09D47411969CE6D7A4
          D9CF96585AE432831AC47E6E39008A3D172506DF90AF0000000049454E44AE42
          6082}
      end
      item
        Background = clWindow
        Name = 'filterdown'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          610000001974455874536F6674776172650041646F626520496D616765526561
          647971C9653C000002834944415478DAA5925F6852511CC77FF77AF57A9D7F33
          1B9A10C1A897D120073E864205494ECAA4A78D609B5B4F238A5583465039FAF3
          B0070B31A88788A811AC6D3E16582CC4EE8314D1838E25D80AFC537AFD37B5DB
          EF5E6D69592F1DF871EE39F77C3FE77BBEE7103CCFC3FF344200B85CAEA38944
          C2188FC7A152A908F314968E244935C330A0542AEB6AB5BAAAD56AABD85770BC
          8EFF16DB01E3C1603050AFD74540B95C068EAB423A5D029294824AA5028D4623
          808061E4E0F57A4E23E04E07001D80DFEF0F140A0514735845C86639A02819E0
          AEA0D3E94488CF77690A419B1D0087C301C964723C140A0532990C0AB3E86413
          36363890C918D0EBF560301840ABD5C0E4E4C9298542314F51D4AF0CAC562B94
          4AA53ECCC01E894402F97C1EC71548A5BE815CAE84DEDE1D28D6C1C484E73CBA
          599448241F3A426C0100017D98813D1A8D0638AE84AED2D0D3A30393C9082323
          CE590C710101EF11007F030821F6E1FCA1D5D5D7FEB5B5CF787E038C8D1DBF86
          C247643FFDF6CB010EBE1A8B00C2F517BF770508E0BD58EE959557573C9EC3D7
          31B487D43E26961A4521817F84A7D30E309BCD50ABD52097CB897DABCDFC04D0
          343D9DBD4DA29808E0BC058B4580770B401044B747B605A83BA4D355079E5B42
          BC193D72CA7237748F85063F0875FE9F808B08B88A80B9E23CBD0DAD0B3BC309
          A7DBF2E4D9022BAEE081FD03D0F67D7679F9E50DB7FBE06CF926EDB41DB35B7E
          DFE1C5D3E76C5707F8CA84EECCD252F896CB65BB509B6376E36E22A0DF336079
          F738D67440743A102E5785C54033EBE17038E6B3D9F6CF341A8DFBA2E0B23264
          1ADE35F0E9C14716A4C42050444706522C23D6F61660A87922228A6B52226048
          7A0E57EC417114C55ED849C30F2F303B69DD97E65D0000000049454E44AE4260
          82}
      end>
    Left = 728
    Top = 232
    Bitmap = {}
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 1
    Left = 864
    Top = 16
  end
end
