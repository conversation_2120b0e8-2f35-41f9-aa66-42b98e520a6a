object CC_AdminUserFrame: TCC_AdminUserFrame
  Left = 0
  Top = 0
  Width = 679
  Height = 595
  Color = clWhite
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentBackground = False
  ParentColor = False
  ParentFont = False
  TabOrder = 0
  object MainPanel: TRzPanel
    Left = 0
    Top = 169
    Width = 679
    Height = 426
    Align = alClient
    BorderOuter = fsNone
    TabOrder = 0
    object RzPanel4: TRzPanel
      Left = 0
      Top = 0
      Width = 679
      Height = 426
      Align = alClient
      BorderOuter = fsFlat
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object UserAdvStringGrid: TAdvStringGrid
        Left = 1
        Top = 1
        Width = 677
        Height = 424
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        ColCount = 6
        Ctl3D = False
        DefaultRowHeight = 25
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedColor = clWhite
        FixedCols = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        ScrollBars = ssBoth
        TabOrder = 0
        OnMouseMove = UserAdvStringGridMouseMove
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        OnGetAlignment = UserAdvStringGridGetAlignment
        OnAnchorClick = UserAdvStringGridAnchorClick
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #23435#20307
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ColumnHeaders.Strings = (
          #32534#21495
          #21517#31216)
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -11
        FilterDropDown.Font.Name = 'Tahoma'
        FilterDropDown.Font.Style = []
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDownClear = '(All)'
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedColWidth = 80
        FixedRowHeight = 25
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = [fsBold]
        Flat = True
        FloatFormat = '%.2f'
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        ShowSelection = False
        SortSettings.DefaultFormat = ssAutomatic
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        WordWrap = False
        ColWidths = (
          80
          270
          122
          64
          64
          64)
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 679
    Height = 169
    Align = alTop
    BorderOuter = fsNone
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    object RzPanel5: TRzPanel
      Left = 0
      Top = 56
      Width = 679
      Height = 113
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object RzLabel1: TRzLabel
        Left = 19
        Top = 70
        Width = 39
        Height = 19
        Caption = #23494#30721#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RzLabel2: TRzLabel
        Left = 19
        Top = 24
        Width = 52
        Height = 19
        Caption = #29992#25143#21517#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RzLabel3: TRzLabel
        Left = 387
        Top = 40
        Width = 39
        Height = 19
        Caption = #35282#33394#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Visible = False
      end
      object Lab_Username: TRzLabel
        Left = 81
        Top = 24
        Width = 52
        Height = 19
        Caption = #29992#25143#21517#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RzEdit1: TRzEdit
        Left = 81
        Top = 70
        Width = 208
        Height = 25
        Text = ''
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        OnChange = RzEdit1Change
      end
      object RzComboBox1: TRzComboBox
        Left = 449
        Top = 37
        Width = 208
        Height = 25
        TabOrder = 1
        Visible = False
      end
    end
    object RzPanel1: TRzPanel
      Left = 0
      Top = 15
      Width = 679
      Height = 41
      Align = alTop
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      object RzLabel4: TRzLabel
        Left = 19
        Top = 7
        Width = 82
        Height = 26
        Caption = #29992#25143' '#31649#29702
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object Btn_Save: TAdvGlowButton
        Left = 505
        Top = 6
        Width = 82
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 0
        OnClick = Btn_SaveClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
      object AdvGlowButton1: TAdvGlowButton
        Left = 387
        Top = 6
        Width = 82
        Height = 28
        Caption = #26032'  '#22686
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 1
        Visible = False
        OnClick = AdvGlowButton1Click
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
    end
    object RzPanel3: TRzPanel
      Left = 0
      Top = 0
      Width = 679
      Height = 15
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdLeft, sdTop, sdRight]
      BorderColor = 14671839
      BorderWidth = 1
      Color = 16049103
      DoubleBuffered = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 2
      VisualStyle = vsGradient
    end
  end
end
