'use client'

import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Pencil, Trash2 } from "lucide-react"

interface VisualizationLayoutProps {
  isPreview?: boolean
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
}

export function VisualizationLayout({ 
  isPreview = false,
  onEdit,
  onDelete 
}: VisualizationLayoutProps) {
  return (
    <div className="p-8 space-y-6">
      <div className="grid grid-cols-4 gap-6">
        {/* 数值指标卡片 */}
        {['users', 'visits', 'business', 'response'].map((id) => (
          <Card key={id} className="p-4 group relative">
            {!isPreview && (
              <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit?.(id)}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete?.(id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            )}
            <div className="mb-2 text-sm font-medium text-gray-700">
              {id === 'users' && '当前在线用户'}
              {id === 'visits' && '今日访问量'}
              {id === 'business' && '业务办理量'}
              {id === 'response' && '系统响应时间'}
            </div>
            <div className="h-[80px] bg-gray-50 flex items-center justify-center">
              <span className="text-gray-400">数值指标区域</span>
            </div>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-3 gap-6">
        {/* 图表区域 */}
        <Card className="p-4 col-span-2 group relative">
          {!isPreview && (
            <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit?.('realtime-users')}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete?.('realtime-users')}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
          <div className="mb-2 text-sm font-medium text-gray-700">实时在线用户</div>
          <div className="h-[300px] bg-gray-50 flex items-center justify-center">
            <span className="text-gray-400">折线图区域</span>
          </div>
        </Card>

        <Card className="p-4 group relative">
          {!isPreview && (
            <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit?.('hot-search')}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete?.('hot-search')}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
          <div className="mb-2 text-sm font-medium text-gray-700">热门搜索词</div>
          <div className="h-[300px] bg-gray-50 flex items-center justify-center">
            <span className="text-gray-400">列表区域</span>
          </div>
        </Card>
      </div>

      <Card className="p-4 group relative">
        {!isPreview && (
          <div className="absolute top-2 right-2 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onEdit?.('business-trend')}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDelete?.('business-trend')}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        )}
        <div className="mb-2 text-sm font-medium text-gray-700">业务趋势</div>
        <div className="h-[300px] bg-gray-50 flex items-center justify-center">
          <span className="text-gray-400">柱状图区域</span>
        </div>
      </Card>
    </div>
  )
} 