unit CcYsMlAlldj;

interface

uses
  Classes;

type
  TCcYsMlAlldj = class
  private
    FYsmlalldjid: Integer;
    FYsid: Integer;
    FHjtgs_a: Double;
    FHjtgs_b: Double;
    FHjpbj: Double;

    FHjlpmid: string;
    FHjlggid: string;
    FHjlCzf: Double;
    FHjlZzsh: Double;
    FHjlRsf: Double;

    FHjjgf: Double;
    FHjlcb: Double;
    FHjlbz: string;
    FRzd_a: Double;
    FRzddj_a: Double;
    FRzd_b: Double;
    FRzddj_b: Double;
    FRzdcb: Double;
    FRzdbz: string;
    FGtbtgs: Double;
    FGtbpbj: Double;
    FGtbcb: Double;
    FGtbbz: string;
    FLwtgs: Double;
    FLwpbj: Double;

    FLwSb: Double;
    FLwAlj: Double;
    FLwAlbl: Double;
    FLwZzf: Double;
    FLwZzsh: Double;
    FLwRsf: Double;

    FLwcb: Double;
    FLwbz: string;
    FSzbpmid: string;
    FSzbggid: string;
    FSzbtgs: Double;
    FSzbpbj: Double;
    FSzbzl: Double;
    FRsj: Double;
    FSzbcb: Double;
    FSzbbz: string;
    FMss: Double;
    FMsdj: Double;
    FMsje: Double;
    FMsbz: string;
    FmPmbmid_a: string;
    FmQtsl_a: Double;
    FmQtdj_a: Double;
    FmQtje_a: Double;
    FmPmbmid_b: string;
    FmQtsl_b: Double;
    FmQtdj_b: Double;
    FmQtje_b: Double;
    FmPmbmid_c: string;
    FmQtsl_c: Double;
    FmQtdj_c: Double;
    FmQtje_c: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysmlalldjid: Integer read FYsmlalldjid write FYsmlalldjid;
    property Ysid: Integer read FYsid write FYsid;
    property Hjtgs_a: Double read FHjtgs_a write FHjtgs_a;
    property Hjtgs_b: Double read FHjtgs_b write FHjtgs_b;
    property Hjpbj: Double read FHjpbj write FHjpbj;
    property Hjlpmid: string read FHjlpmid write FHjlpmid;
    property Hjlggid: string read FHjlggid write FHjlggid;
    property HjlCzf: Double read FHjlCzf write FHjlCzf;
    property HjlZzsh: Double read FHjlZzsh write FHjlZzsh;
    property HjlRsf: Double read FHjlRsf write FHjlRsf;

    property Hjjgf: Double read FHjjgf write FHjjgf;
    property Hjlcb: Double read FHjlcb write FHjlcb;
    property Hjlbz: string read FHjlbz write FHjlbz;
    property Rzd_a: Double read FRzd_a write FRzd_a;
    property Rzddj_a: Double read FRzddj_a write FRzddj_a;
    property Rzd_b: Double read FRzd_b write FRzd_b;
    property Rzddj_b: Double read FRzddj_b write FRzddj_b;
    property Rzdcb: Double read FRzdcb write FRzdcb;
    property Rzdbz: string read FRzdbz write FRzdbz;
    property Gtbtgs: Double read FGtbtgs write FGtbtgs;
    property Gtbpbj: Double read FGtbpbj write FGtbpbj;
    property Gtbcb: Double read FGtbcb write FGtbcb;
    property Gtbbz: string read FGtbbz write FGtbbz;
    property Lwtgs: Double read FLwtgs write FLwtgs;
    property Lwpbj: Double read FLwpbj write FLwpbj;
    property LwSb: Double read FLwSb write FLwSb;
    property LwAlj: Double read FLwAlj write FLwAlj;
    property LwAlbl: Double read FLwAlbl write FLwAlbl;
    property LwZzf: Double read FLwZzf write FLwZzf;
    property LwZzsh: Double read FLwZzsh write FLwZzsh;
    property LwRsf: Double read FLwRsf write FLwRsf;
    property Lwcb: Double read FLwcb write FLwcb;
    property Lwbz: string read FLwbz write FLwbz;
    property Szbpmid: string read FSzbpmid write FSzbpmid;
    property Szbggid: string read FSzbggid write FSzbggid;
    property Szbtgs: Double read FSzbtgs write FSzbtgs;
    property Szbpbj: Double read FSzbpbj write FSzbpbj;
    property Szbzl: Double read FSzbzl write FSzbzl;
    property Rsj: Double read FRsj write FRsj;
    property Szbcb: Double read FSzbcb write FSzbcb;
    property Szbbz: string read FSzbbz write FSzbbz;
    property Mss: Double read FMss write FMss;
    property Msdj: Double read FMsdj write FMsdj;
    property Msje: Double read FMsje write FMsje;
    property Msbz: string read FMsbz write FMsbz;
    property mPmbmid_a: string read FmPmbmid_a write FmPmbmid_a;
    property mQtsl_a: Double read FmQtsl_a write FmQtsl_a;
    property mQtdj_a: Double read FmQtdj_a write FmQtdj_a;
    property mQtje_a: Double read FmQtje_a write FmQtje_a;
    property mPmbmid_b: string read FmPmbmid_b write FmPmbmid_b;
    property mQtsl_b: Double read FmQtsl_b write FmQtsl_b;
    property mQtdj_b: Double read FmQtdj_b write FmQtdj_b;
    property mQtje_b: Double read FmQtje_b write FmQtje_b;
    property mPmbmid_c: string read FmPmbmid_c write FmPmbmid_c;
    property mQtsl_c: Double read FmQtsl_c write FmQtsl_c;
    property mQtdj_c: Double read FmQtdj_c write FmQtdj_c;
    property mQtje_c: Double read FmQtje_c write FmQtje_c;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
