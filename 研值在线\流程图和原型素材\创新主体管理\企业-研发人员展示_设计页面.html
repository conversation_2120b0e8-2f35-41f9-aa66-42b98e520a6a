<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">研发人员展示</h1>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('研发人员总数')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">研发人员总数</p>
                        <p class="text-2xl font-bold text-gray-900">248</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <span class="text-sm">+12%</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('技术顾问总数')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">技术顾问总数</p>
                        <p class="text-2xl font-bold text-gray-900">36</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <span class="text-sm">+5%</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('硕博士及以上')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">硕博士及以上</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <span class="text-sm">+8%</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('知识产权数量')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">知识产权数量</p>
                        <p class="text-2xl font-bold text-gray-900">328</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <span class="text-sm">+15%</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">主体名称</label>
                    <input type="text" placeholder="请输入企业/机构名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">人员类别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="internal">内部研发人员</option>
                        <option value="external">技术顾问</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">学历</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="doctor">博士</option>
                        <option value="master">硕士</option>
                        <option value="bachelor">本科</option>
                        <option value="college">大专及以下</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">职称</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="senior">高级职称</option>
                        <option value="middle">中级职称</option>
                        <option value="junior">初级职称</option>
                        <option value="none">无职称</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目年份</label>
                    <div class="flex space-x-2">
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">起始年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs">
                            <input type="checkbox" class="h-3 w-3 text-blue-600 focus:ring-blue-500 mr-1">
                            专利
                        </label>
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs">
                            <input type="checkbox" class="h-3 w-3 text-blue-600 focus:ring-blue-500 mr-1">
                            论文
                        </label>
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-xs">
                            <input type="checkbox" class="h-3 w-3 text-blue-600 focus:ring-blue-500 mr-1">
                            软件著作权
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 人员列表区 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">研发人员列表</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        导出
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增人员
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人员类别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最高学历</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在职状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">知识产权</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?portrait,1" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张明</div>
                                        <div class="text-sm text-gray-500">宁波市智能科技</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">内部研发</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openProfileModal('1')" class="text-blue-600 hover:text-blue-900 mr-3">查看画像</button>
                                <button onclick="openProjectModal('1')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?portrait,2" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李华</div>
                                        <div class="text-sm text-gray-500">宁波大学</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">技术顾问</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">副教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">19</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openProfileModal('2')" class="text-blue-600 hover:text-blue-900 mr-3">查看画像</button>
                                <button onclick="openProjectModal('2')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?portrait,3" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">王强</div>
                                        <div class="text-sm text-gray-500">宁波市新材料研究院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">内部研发</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">休假中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">34</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openProfileModal('3')" class="text-blue-600 hover:text-blue-900 mr-3">查看画像</button>
                                <button onclick="openProjectModal('3')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?portrait,4" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">赵敏</div>
                                        <div class="text-sm text-gray-500">宁波市电子科技</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">内部研发</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">21</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openProfileModal('4')" class="text-blue-600 hover:text-blue-900 mr-3">查看画像</button>
                                <button onclick="openProjectModal('4')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/100x100/?portrait,5" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">陈刚</div>
                                        <div class="text-sm text-gray-500">宁波市智能制造研究院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">技术顾问</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在职</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openProfileModal('5')" class="text-blue-600 hover:text-blue-900 mr-3">查看画像</button>
                                <button onclick="openProjectModal('5')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">248</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">学历分布</h3>
                        <div class="flex space-x-2">
                            <button class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">柱状图</button>
                            <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">饼图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="educationChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">职称晋升趋势</h3>
                        <div class="flex space-x-2">
                            <button class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">折线图</button>
                            <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">面积图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="promotionChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">年度参与项目数量</h3>
                        <div class="flex space-x-2">
                            <button class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">柱状图</button>
                            <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">折线图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">知识产权累积趋势</h3>
                        <div class="flex space-x-2">
                            <button class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">折线图</button>
                            <button class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">面积图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="ipChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 人员画像侧栏 -->
    <div id="profileModal" class="fixed inset-y-0 right-0 w-full max-w-xl bg-white shadow-xl transform transition-transform duration-300 translate-x-full z-50">
        <div class="h-full overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">人员画像</h3>
                <button onclick="closeProfileModal()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <!-- 基础信息 -->
                <div class="flex items-start mb-6">
                    <div class="flex-shrink-0 h-20 w-20">
                        <img class="h-20 w-20 rounded-full" src="https://source.unsplash.com/200x200/?portrait,1" alt="">
                    </div>
                    <div class="ml-4">
                        <h4 class="text-xl font-bold text-gray-900">张明</h4>
                        <p class="text-sm text-gray-500">宁波市智能科技 | 高级工程师</p>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">博士</span>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">在职</span>
                            <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">核心研发</span>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button class="border-b-2 border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 text-sm font-medium">教育经历</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">工作经历</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">职称证书</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">参与项目</button>
                    </nav>
                </div>

                <!-- 教育经历内容 -->
                <div class="mt-6 space-y-6">
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <h5 class="text-md font-medium text-gray-900">浙江大学</h5>
                        <p class="text-sm text-gray-500">计算机科学与技术 | 博士</p>
                        <p class="text-xs text-gray-400">2010.09 - 2015.06</p>
                    </div>
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <h5 class="text-md font-medium text-gray-900">宁波大学</h5>
                        <p class="text-sm text-gray-500">计算机科学与技术 | 硕士</p>
                        <p class="text-xs text-gray-400">2007.09 - 2010.06</p>
                    </div>
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <h5 class="text-md font-medium text-gray-900">宁波大学</h5>
                        <p class="text-sm text-gray-500">计算机科学与技术 | 本科</p>
                        <p class="text-xs text-gray-400">2003.09 - 2007.06</p>
                    </div>
                </div>

                <!-- 时间轴 -->
                <div class="mt-8">
                    <h4 class="text-md font-medium text-gray-900 mb-4">工作轨迹</h4>
                    <div class="relative">
                        <div class="absolute left-4 h-full w-0.5 bg-gray-200"></div>
                        <div class="space-y-4">
                            <div class="relative pl-8">
                                <div class="absolute left-0 top-1 h-3 w-3 rounded-full bg-blue-500 border-4 border-blue-100"></div>
                                <div class="text-sm font-medium text-gray-900">宁波市智能科技</div>
                                <div class="text-xs text-gray-500">高级工程师 | 2015.07 - 至今</div>
                            </div>
                            <div class="relative pl-8">
                                <div class="absolute left-0 top-1 h-3 w-3 rounded-full bg-blue-500 border-4 border-blue-100"></div>
                                <div class="text-sm font-medium text-gray-900">宁波市软件园</div>
                                <div class="text-xs text-gray-500">研发工程师 | 2010.07 - 2015.06</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    导出报告
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    发送邮件
                </button>
            </div>
        </div>
    </div>

    <!-- 项目明细弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">参与项目明细</h3>
                    <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开始日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结束日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智能家居控制系统研发</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PROJ-2023-001</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">企业自主研发</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-06-30</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术负责人</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">工业物联网平台开发</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PROJ-2022-008</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">政府资助项目</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-05-10</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-31</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">验收中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">核心开发</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">人工智能算法优化</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">PROJ-2021-015</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">产学研合作</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-09-01</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-12-31</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">已完成</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">算法工程师</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="closeProjectModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        关闭
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        导出项目清单
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 指标详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="detailModalTitle">指标详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="h-96">
                    <canvas id="detailChart"></canvas>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="closeDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openProfileModal(id) {
            document.getElementById('profileModal').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function closeProfileModal() {
            document.getElementById('profileModal').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        function openProjectModal(id) {
            document.getElementById('projectModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeProjectModal() {
            document.getElementById('projectModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openDetailModal(title) {
            document.getElementById('detailModalTitle').textContent = title + '详情';
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('projectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProjectModal();
            }
        });

        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });

        // --- 图表初始化 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 学历分布图表
            const educationCtx = document.getElementById('educationChart').getContext('2d');
            new Chart(educationCtx, {
                type: 'bar',
                data: {
                    labels: ['博士', '硕士', '本科', '大专及以下'],
                    datasets: [{
                        label: '人数',
                        data: [85, 71, 68, 24],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.7)',
                            'rgba(16, 185, 129, 0.7)',
                            'rgba(245, 158, 11, 0.7)',
                            'rgba(239, 68, 68, 0.7)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 职称晋升趋势图表
            const promotionCtx = document.getElementById('promotionChart').getContext('2d');
            new Chart(promotionCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '高级职称',
                            data: [12, 15, 18, 22, 28],
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '中级职称',
                            data: [35, 38, 42, 45, 50],
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '初级职称',
                            data: [60, 55, 50, 45, 40],
                            borderColor: 'rgba(245, 158, 11, 1)',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 年度参与项目数量图表
            const projectCtx = document.getElementById('projectChart').getContext('2d');
            new Chart(projectCtx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '项目数量',
                        data: [45, 68, 72, 89, 112],
                        backgroundColor: 'rgba(139, 92, 246, 0.7)',
                        borderColor: 'rgba(139, 92, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 知识产权累积趋势图表
            const ipCtx = document.getElementById('ipChart').getContext('2d');
            new Chart(ipCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '知识产权数量',
                        data: [56, 78, 102, 145, 201],
                        borderColor: 'rgba(239, 68, 68, 1)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 指标详情图表
            const detailCtx = document.getElementById('detailChart').getContext('2d');
            new Chart(detailCtx, {
                type: 'line',
                data: {
                    labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    datasets: [{
                        label: '数量',
                        data: [12, 19, 3, 5, 2, 3, 7, 14, 8, 11, 15, 18],
                        borderColor: '#3B82F6',
                        tension: 0.4,
                        fill: true,
                        backgroundColor: 'rgba(59, 130, 246, 0.1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>