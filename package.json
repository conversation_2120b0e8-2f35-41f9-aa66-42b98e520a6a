{"name": "cherry-studio-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 57779", "lint": "next lint"}, "dependencies": {"@ant-design/plots": "^2.3.2", "@hello-pangea/dnd": "^17.0.0", "@prisma/client": "^6.4.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.6", "@shadcn/ui": "^0.0.4", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-resizable": "^3.0.8", "angular-expressions": "^1.4.3", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "docxtemplater": "^3.60.1", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.465.0", "mammoth": "^1.9.0", "mysql2": "^3.14.0", "nanoid": "^5.0.6", "next": "14.0.4", "next-themes": "^0.4.3", "pizzip": "^3.1.8", "qrcode.react": "^4.1.0", "react": "^18", "react-beautiful-dnd": "^13.1.1", "react-colorful": "^5.6.1", "react-datepicker": "^7.5.0", "react-day-picker": "^9.4.0", "react-dom": "^18", "react-leaflet": "^4.2.1", "react-resizable": "^3.0.5", "reactflow": "^11.11.4", "recharts": "^2.15.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/exceljs": "^1.3.2", "@types/file-saver": "^2.0.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "prisma": "^6.4.1", "tailwindcss": "^3.3.0", "typescript": "^5"}, "packageManager": "yarn@4.6.0+sha512.5383cc12567a95f1d668fbe762dfe0075c595b4bfff433be478dbbe24e05251a8e8c3eb992a986667c1d53b6c3a9c85b8398c35a960587fbd9fa3a0915406728"}