<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">学术活动展示模块流程图</text>

  <!-- 阶段一：页面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与数据加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">权限验证与页面加载</text>
  </g>

  <!-- 节点2: 活动数据加载 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">活动数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">计算汇总信息与卡片刷新</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点3: 筛选条件设定 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">类型、时间、地点等条件</text>
  </g>

  <!-- 节点4: 数据服务查询 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据服务查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">返回列表与统计图表</text>
  </g>

  <!-- 连接线 数据加载 -> 筛选条件 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选条件 -> 数据服务 -->
  <path d="M 500 455 C 650 455, 750 455, 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="445" text-anchor="middle" font-size="12" fill="#555">发送过滤参数</text>

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>
  
  <!-- 节点5: 详情查看 -->
  <g transform="translate(200, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">议程、嘉宾、报名须知</text>
  </g>

  <!-- 节点6: 报名操作 -->
  <g transform="translate(500, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">报名操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">信息填写与费用支付</text>
  </g>

  <!-- 节点7: 统计分析 -->
  <g transform="translate(800, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计分析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合与可视化图表</text>
  </g>

  <!-- 连接线 数据服务 -> 详情查看 -->
  <path d="M 950 490 C 900 550, 400 580, 300 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情查看 -> 报名操作 -->
  <path d="M 400 655 C 450 655, 450 655, 500 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据服务 -> 统计分析 -->
  <path d="M 1000 490 C 1000 550, 950 580, 900 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：操作执行与状态管理 -->
  <text x="700" y="780" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：操作执行与状态管理</text>
  
  <!-- 节点8: 导出与订阅 -->
  <g transform="translate(500, 820)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出与订阅操作</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">PDF/iCal文件生成、下载链接、日志记录</text>
  </g>

  <!-- 连接线 报名操作 -> 导出与订阅 -->
  <path d="M 600 690 C 650 750, 650 780, 700 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <!-- 统计分析 -> 筛选条件 (虚线) -->
  <path d="M 800 620 C 700 550, 500 500, 400 490" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="600" y="540" text-anchor="middle" font-size="11" fill="#666">数据刷新</text>

  <!-- 报名操作 -> 活动数据加载 (虚线) -->
  <path d="M 500 620 C 400 500, 500 400, 600 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="450" y="470" text-anchor="middle" font-size="11" fill="#666">名额更新</text>

</svg>