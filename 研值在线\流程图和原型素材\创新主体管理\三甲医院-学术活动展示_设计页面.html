<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-学术活动展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">学术活动展示</h1>
            <p class="mt-2 text-gray-600">集中呈现医院举办或参与的各类学术活动，包括论坛、研讨会、培训班等</p>
        </div>

        <!-- 数据概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">即将开始</p>
                        <p class="text-2xl font-semibold text-gray-900">24</p>
                    </div>
                </div>
                <div class="mt-4 h-20">
                    <canvas id="upcomingChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">进行中</p>
                        <p class="text-2xl font-semibold text-gray-900">12</p>
                    </div>
                </div>
                <div class="mt-4 h-20">
                    <canvas id="ongoingChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已结束</p>
                        <p class="text-2xl font-semibold text-gray-900">156</p>
                    </div>
                </div>
                <div class="mt-4 h-20">
                    <canvas id="completedChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">累计参会</p>
                        <p class="text-2xl font-semibold text-gray-900">3,245</p>
                    </div>
                </div>
                <div class="mt-4 h-20">
                    <canvas id="participantsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">活动类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            论坛
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            研讨会
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            培训班
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">中心议题</label>
                    <input type="text" placeholder="请输入关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">举办时间</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">举办地点</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部地点</option>
                        <option value="ningbo">宁波市</option>
                        <option value="hangzhou">杭州市</option>
                        <option value="shanghai">上海市</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">费用范围</label>
                    <div class="flex items-center space-x-2">
                        <input type="range" min="0" max="5000" step="100" class="w-full">
                        <span class="text-sm text-gray-500">0-5000元</span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">主办单位</label>
                    <input type="text" placeholder="请输入主办单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 活动列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">学术活动列表</h2>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中心议题</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地点</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">费用</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">2024年医学前沿论坛</div>
                                        <div class="text-sm text-gray-500">论坛</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">人工智能在医学中的应用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 至 2024-03-17</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市国际会议中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1200元</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">即将开始</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">临床研究方法培训班</div>
                                        <div class="text-sm text-gray-500">培训班</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">临床研究设计与实施</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-28 至 2024-03-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市医学继续教育学院</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">800元</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">肿瘤精准治疗研讨会</div>
                                        <div class="text-sm text-gray-500">研讨会</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">肿瘤精准治疗新进展</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-20 至 2024-01-21</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市肿瘤医院</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">免费</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">已结束</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">24</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分析图表区 -->
            <div class="w-full lg:w-1/3 space-y-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">活动类型分布</h3>
                    <div class="h-64">
                        <canvas id="typeChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">地区活动频次</h3>
                    <div class="h-64">
                        <canvas id="regionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动详情模态框 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">活动详情</h3>
                <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 基本信息 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">活动名称</p>
                                <p class="text-sm font-medium text-gray-900">2024年医学前沿论坛</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">活动类型</p>
                                <p class="text-sm font-medium text-gray-900">论坛</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">举办时间</p>
                                <p class="text-sm font-medium text-gray-900">2024-03-15 至 2024-03-17</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">举办地点</p>
                                <p class="text-sm font-medium text-gray-900">宁波市国际会议中心</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">费用标准</p>
                                <p class="text-sm font-medium text-gray-900">1200元/人</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">主办单位</p>
                                <p class="text-sm font-medium text-gray-900">宁波市医学会</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 详细议程 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">详细议程</h4>
                        <div class="space-y-4">
                            <div class="border-l-4 border-blue-500 pl-4 py-2">
                                <p class="font-medium text-gray-900">3月15日 上午</p>
                                <p class="text-sm text-gray-600">08:30-09:00 开幕式</p>
                                <p class="text-sm text-gray-600">09:00-10:30 主题报告：人工智能在医学中的应用</p>
                            </div>
                            <div class="border-l-4 border-blue-500 pl-4 py-2">
                                <p class="font-medium text-gray-900">3月15日 下午</p>
                                <p class="text-sm text-gray-600">14:00-16:00 分论坛：医学影像AI分析</p>
                                <p class="text-sm text-gray-600">16:00-17:30 分论坛：智能辅助诊断系统</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 嘉宾信息 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">嘉宾信息</h4>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-600">张</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">张教授</p>
                                    <p class="text-sm text-gray-500">宁波市第一医院 院长</p>
                                    <p class="text-sm text-gray-500">主讲题目：人工智能在临床决策中的应用</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                    <span class="text-gray-600">李</span>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">李研究员</p>
                                    <p class="text-sm text-gray-500">浙江大学医学院 研究员</p>
                                    <p class="text-sm text-gray-500">主讲题目：医学大数据分析与应用</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧信息 -->
                <div>
                    <!-- 报名信息 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">报名信息</h4>
                        <div class="space-y-3">
                            <div>
                                <p class="text-sm text-gray-500">报名截止</p>
                                <p class="text-sm font-medium text-gray-900">2024-03-10</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">目标对象</p>
                                <p class="text-sm font-medium text-gray-900">临床医生、研究人员</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">参会人数</p>
                                <p class="text-sm font-medium text-gray-900">200人</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">报名状态</p>
                                <p class="text-sm font-medium text-gray-900">开放报名</p>
                            </div>
                            <button class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                在线报名
                            </button>
                        </div>
                    </div>
                    
                    <!-- 附件下载 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">附件下载</h4>
                        <div class="space-y-2">
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                详细议程PDF
                            </a>
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                参会指南
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览图表
            const upcomingCtx = document.getElementById('upcomingChart').getContext('2d');
            new Chart(upcomingCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        data: [3, 5, 8, 6, 4, 7],
                        borderColor: '#3B82F6',
                        tension: 0.4,
                        fill: false,
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            const ongoingCtx = document.getElementById('ongoingChart').getContext('2d');
            new Chart(ongoingCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        data: [2, 3, 4, 5, 3, 4],
                        borderColor: '#10B981',
                        tension: 0.4,
                        fill: false,
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            const completedCtx = document.getElementById('completedChart').getContext('2d');
            new Chart(completedCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        data: [12, 15, 18, 16, 14, 17],
                        borderColor: '#F59E0B',
                        tension: 0.4,
                        fill: false,
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            const participantsCtx = document.getElementById('participantsChart').getContext('2d');
            new Chart(participantsCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        data: [120, 150, 180, 160, 140, 170],
                        borderColor: '#8B5CF6',
                        tension: 0.4,
                        fill: false,
                        borderWidth: 2,
                        pointRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            // 初始化活动类型分布图表
            const typeCtx = document.getElementById('typeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['论坛', '研讨会', '培训班', '成果发布会'],
                    datasets: [{
                        data: [45, 30, 20, 5],
                        backgroundColor: [
                            '#3B82F6',
                            '#10B981',
                            '#F59E0B',
                            '#8B5CF6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 初始化地区活动频次图表
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'radar',
                data: {
                    labels: ['宁波市', '杭州市', '上海市', '南京市', '苏州市'],
                    datasets: [{
                        label: '活动频次',
                        data: [65, 40, 30, 25, 15],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: '#3B82F6',
                        borderWidth: 2,
                        pointBackgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>