unit FpdUpdateType;

interface

uses
  Classes;

type
  TFpdUpdateType = class
  private
    Fbeginnum: integer;
    Fbeginindex: integer;
    Fresettype: integer;
    FScjqrq: string;
  public
    property beginnum: integer read Fbeginnum write Fbeginnum;
    property beginindex: integer read Fbeginindex write Fbeginindex;
    property resettype: integer read Fresetty<PERSON> write Fresettype;
    property Scjqrq: string read FScjqrq write FScjqrq;
  end;

implementation

end.
