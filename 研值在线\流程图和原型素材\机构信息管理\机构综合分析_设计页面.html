<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构综合分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">机构综合分析</h1>
            <p class="text-gray-600">多维度可视化分析机构类型、行业领域、地理区域及科创平台分布特征</p>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">机构类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="enterprise">企业</option>
                        <option value="university">高校</option>
                        <option value="institute">科研院所</option>
                        <option value="hospital">医院</option>
                        <option value="platform">创新载体</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业类别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部行业</option>
                        <option value="manufacturing">制造业</option>
                        <option value="it">信息技术</option>
                        <option value="medical">医药健康</option>
                        <option value="finance">金融</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">区域层级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部区域</option>
                        <option value="city">市级</option>
                        <option value="district">区级</option>
                        <option value="street">街道</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div class="flex space-x-4">
                    <div class="bg-blue-50 px-4 py-2 rounded-lg">
                        <div class="text-sm text-gray-500">机构总数</div>
                        <div class="text-xl font-bold text-blue-600">1,245</div>
                    </div>
                    <div class="bg-green-50 px-4 py-2 rounded-lg">
                        <div class="text-sm text-gray-500">覆盖行业</div>
                        <div class="text-xl font-bold text-green-600">36</div>
                    </div>
                    <div class="bg-purple-50 px-4 py-2 rounded-lg">
                        <div class="text-sm text-gray-500">覆盖区域</div>
                        <div class="text-xl font-bold text-purple-600">12</div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 机构类型分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    机构类型分布
                </h2>
                <div id="typeChart" class="w-full h-[400px]"></div>
                <div class="mt-4 overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">机构类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">企业</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">856</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">68.7%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高校</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">128</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">10.3%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">科研院所</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12.5%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">医院</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3.6%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">创新载体</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">60</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.8%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 行业分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    行业分布
                </h2>
                <div class="flex justify-end mb-4">
                    <div class="relative w-64">
                        <input type="text" placeholder="搜索行业..." class="w-full pl-8 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 text-gray-400 absolute left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div id="industryChart" class="w-full h-[400px]"></div>
            </div>
        </div>

        <!-- 区域分布 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                </svg>
                区域分布
            </h2>
            <div id="regionChart" class="w-full h-[500px]"></div>
        </div>

        <!-- 科创平台与创新载体分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    科创平台分布
                </h2>
                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-500">国家级平台</div>
                        <div class="text-2xl font-bold text-blue-600">28</div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-500">省级平台</div>
                        <div class="text-2xl font-bold text-green-600">56</div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-500">市级平台</div>
                        <div class="text-2xl font-bold text-purple-600">112</div>
                    </div>
                </div>
                <div id="platformChart" class="w-full h-[300px]"></div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    创新载体分析
                </h2>
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-500">孵化器</div>
                        <div class="text-2xl font-bold text-yellow-600">45</div>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg">
                        <div class="text-sm text-gray-500">众创空间</div>
                        <div class="text-2xl font-bold text-red-600">32</div>
                    </div>
                </div>
                <div id="carrierChart" class="w-full h-[300px]"></div>
            </div>
        </div>

        <!-- 数据质量与注释 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                数据质量与注释
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500">数据覆盖率</div>
                    <div class="text-xl font-bold text-gray-900">98.5%</div>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500">数据缺失率</div>
                    <div class="text-xl font-bold text-orange-600">1.5%</div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500">最近更新时间</div>
                    <div class="text-xl font-bold text-gray-900">2024-03-15 14:30</div>
                </div>
            </div>
            <div class="text-sm text-gray-500">
                <p class="mb-2"><span class="font-medium">统计口径：</span>数据来源于宁波市科技大脑及各区县科技局上报数据，统计截止时间为2024年3月15日。</p>
                <p><span class="font-medium">图例说明：</span>机构类型分布图中"企业"包含高新技术企业、科技型中小企业等；"创新载体"包含各类孵化器、众创空间、加速器等。</p>
            </div>
            <div class="mt-4">
                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    去数据管理
                </a>
            </div>
        </div>
    </div>

    <script>
        // 机构类型分布图
        const typeChart = echarts.init(document.getElementById('typeChart'));
        const typeOption = {
            title: {
                text: '机构类型分布',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                data: ['企业', '高校', '科研院所', '医院', '创新载体']
            },
            series: [
                {
                    name: '机构类型',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 856, name: '企业', itemStyle: { color: '#5470C6' } },
                        { value: 128, name: '高校', itemStyle: { color: '#91CC75' } },
                        { value: 156, name: '科研院所', itemStyle: { color: '#FAC858' } },
                        { value: 45, name: '医院', itemStyle: { color: '#EE6666' } },
                        { value: 60, name: '创新载体', itemStyle: { color: '#73C0DE' } }
                    ]
                }
            ]
        };
        typeChart.setOption(typeOption);

        // 行业分布图
        const industryChart = echarts.init(document.getElementById('industryChart'));
        const industryOption = {
            title: {
                text: '行业分布',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}'
                }
            },
            yAxis: {
                type: 'category',
                data: ['制造业', '信息技术', '医药健康', '金融', '教育', '能源', '农业'],
                axisLabel: {
                    interval: 0,
                    rotate: 0
                }
            },
            series: [
                {
                    name: '机构数量',
                    type: 'bar',
                    data: [
                        { value: 356, itemStyle: { color: '#5470C6' } },
                        { value: 289, itemStyle: { color: '#91CC75' } },
                        { value: 178, itemStyle: { color: '#FAC858' } },
                        { value: 156, itemStyle: { color: '#EE6666' } },
                        { value: 128, itemStyle: { color: '#73C0DE' } },
                        { value: 87, itemStyle: { color: '#3BA272' } },
                        { value: 65, itemStyle: { color: '#9A60B4' } }
                    ],
                    label: {
                        show: true,
                        position: 'right',
                        formatter: function(params) {
                            return params.value + ' (+' + Math.floor(Math.random() * 10 + 5) + '%)';
                        }
                    }
                }
            ]
        };
        industryChart.setOption(industryOption);

        // 区域分布图
        const regionChart = echarts.init(document.getElementById('regionChart'));
        const regionOption = {
            title: {
                text: '宁波市机构区域分布',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{b}<br/>机构数量: {c}'
            },
            visualMap: {
                min: 0,
                max: 300,
                text: ['高', '低'],
                realtime: false,
                calculable: true,
                inRange: {
                    color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
                }
            },
            series: [
                {
                    name: '机构数量',
                    type: 'map',
                    map: '宁波',
                    roam: true,
                    emphasis: {
                        label: {
                            show: true
                        }
                    },
                    data: [
                        { name: '海曙区', value: 156 },
                        { name: '江北区', value: 98 },
                        { name: '鄞州区', value: 245 },
                        { name: '镇海区', value: 87 },
                        { name: '北仑区', value: 132 },
                        { name: '奉化区', value: 76 },
                        { name: '余姚市', value: 112 },
                        { name: '慈溪市', value: 145 },
                        { name: '宁海县', value: 68 },
                        { name: '象山县', value: 54 }
                    ]
                }
            ]
        };
        // 加载宁波地图数据
        fetch('https://geo.datav.aliyun.com/areas_v3/bound/330200_full.json')
            .then(response => response.json())
            .then(data => {
                echarts.registerMap('宁波', data);
                regionChart.setOption(regionOption);
            });

        // 科创平台分布图
        const platformChart = echarts.init(document.getElementById('platformChart'));
        const platformOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['国家级', '省级', '市级'],
                right: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['企业', '高校', '科研院所', '医院']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '国家级',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [12, 8, 6, 2],
                    itemStyle: { color: '#5470C6' }
                },
                {
                    name: '省级',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [24, 16, 12, 4],
                    itemStyle: { color: '#91CC75' }
                },
                {
                    name: '市级',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [48, 32, 24, 8],
                    itemStyle: { color: '#FAC858' }
                }
            ]
        };
        platformChart.setOption(platformOption);

        // 创新载体分析图
        const carrierChart = echarts.init(document.getElementById('carrierChart'));
        const carrierOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['孵化器', '众创空间'],
                right: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2019', '2020', '2021', '2022', '2023']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '孵化器',
                    type: 'bar',
                    data: [12, 18, 24, 32, 45],
                    itemStyle: { color: '#EE6666' }
                },
                {
                    name: '众创空间',
                    type: 'bar',
                    data: [8, 14, 20, 26, 32],
                    itemStyle: { color: '#73C0DE' }
                }
            ]
        };
        carrierChart.setOption(carrierOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            typeChart.resize();
            industryChart.resize();
            regionChart.resize();
            platformChart.resize();
            carrierChart.resize();
        });
    </script>
</body>
</html>