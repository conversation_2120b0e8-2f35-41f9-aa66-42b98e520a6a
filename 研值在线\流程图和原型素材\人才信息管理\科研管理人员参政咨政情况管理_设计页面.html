<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员参政咨政情况管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研管理人员参政咨政情况管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-full flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-3">查询与筛选</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-0.5">姓名</label>
                            <input type="text" id="name" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="unit" class="block text-sm font-medium text-gray-700 mb-0.5">所属单位</label>
                            <input type="text" id="unit" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入所属单位">
                        </div>
                        <div>
                            <label for="politicalRole" class="block text-sm font-medium text-gray-700 mb-0.5">参政身份</label>
                            <select id="politicalRole" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="npc">人大代表</option>
                                <option value="cppcc">政协委员</option>
                                <option value="specialist">特邀专家</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="year" class="block text-sm font-medium text-gray-700 mb-0.5">年份</label>
                            <select id="year" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                                <option value="2021">2021</option>
                                <option value="2020">2020</option>
                            </select>
                        </div>
                        <div>
                            <label for="proposalTheme" class="block text-sm font-medium text-gray-700 mb-0.5">提案主题</label>
                            <input type="text" id="proposalTheme" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入提案主题关键词">
                        </div>
                        <div>
                            <label for="proposalStatus" class="block text-sm font-medium text-gray-700 mb-0.5">提案状态</label>
                            <select id="proposalStatus" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="submitted">已提交</option>
                                <option value="reviewing">审核中</option>
                                <option value="adopted">已采纳</option>
                                <option value="rejected">未采纳</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 参政咨政情况列表区 -->
                    <div class="w-full">
                        <!-- 列表区标题栏 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800">参政咨政情况列表</h2>
                                <div class="flex space-x-3">
                                    <div class="relative" id="exportDropdown">
                                        <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                            </svg>
                                            导出
                                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </button>
                                        <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                            <div class="py-1">
                                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为PDF</a>
                                            </div>
                                        </div>
                                    </div>
                                    <button onclick="openModal('addModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        新增记录
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参政身份</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提案主题</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提案状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年份</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">张明</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">政协委员</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">关于加强宁波市生物医药产业创新发展的建议</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已采纳</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">李华</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">人大代表</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">关于推进宁波高校科技成果转化的若干建议</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已采纳</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">王芳</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市高新技术开发区</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">特邀专家</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">关于优化宁波科技创新生态环境的调研报告</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">审核中</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">赵强</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">人大代表</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">关于加快宁波智能制造产业发展的建议</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">未采纳</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">陈静</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市海洋经济研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">政协委员</span>
                                        </td>
                                        <td class="px-6 py-4 text-sm text-gray-500">关于宁波海洋经济高质量发展的提案</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已采纳</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">28</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增记录弹窗 -->
    <div id="addModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增参政咨政记录</h3>
                    <button onclick="closeModal('addModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="addName" class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="addName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="addUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="addUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入所属单位">
                        </div>
                        <div>
                            <label for="addRole" class="block text-sm font-medium text-gray-700 mb-1">参政身份 <span class="text-red-500">*</span></label>
                            <select id="addRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择参政身份</option>
                                <option value="npc">人大代表</option>
                                <option value="cppcc">政协委员</option>
                                <option value="specialist">特邀专家</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="addYear" class="block text-sm font-medium text-gray-700 mb-1">年份 <span class="text-red-500">*</span></label>
                            <select id="addYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择年份</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                                <option value="2021">2021</option>
                                <option value="2020">2020</option>
                            </select>
                        </div>
                        <div>
                            <label for="addProposalTheme" class="block text-sm font-medium text-gray-700 mb-1">提案主题 <span class="text-red-500">*</span></label>
                            <input type="text" id="addProposalTheme" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入提案主题">
                        </div>
                        <div>
                            <label for="addProposalStatus" class="block text-sm font-medium text-gray-700 mb-1">提案状态 <span class="text-red-500">*</span></label>
                            <select id="addProposalStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择提案状态</option>
                                <option value="submitted">已提交</option>
                                <option value="reviewing">审核中</option>
                                <option value="adopted">已采纳</option>
                                <option value="rejected">未采纳</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="addProposalContent" class="block text-sm font-medium text-gray-700 mb-1">提案内容摘要</label>
                        <textarea id="addProposalContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入提案内容摘要"></textarea>
                    </div>
                    <div>
                        <label for="addReportTitle" class="block text-sm font-medium text-gray-700 mb-1">报告标题</label>
                        <input type="text" id="addReportTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入报告标题">
                    </div>
                    <div>
                        <label for="addResearchContent" class="block text-sm font-medium text-gray-700 mb-1">调研内容</label>
                        <textarea id="addResearchContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入调研内容"></textarea>
                    </div>
                    <div>
                        <label for="addRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="addRemark" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入备注信息"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">相关附件</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                                </label>
                                <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel格式，单个文件不超过10MB</p>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('addModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存记录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑记录弹窗 -->
    <div id="editModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑参政咨政记录</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="editName" class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="editName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张明">
                        </div>
                        <div>
                            <label for="editUnit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="editUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市科技研究院">
                        </div>
                        <div>
                            <label for="editRole" class="block text-sm font-medium text-gray-700 mb-1">参政身份 <span class="text-red-500">*</span></label>
                            <select id="editRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择参政身份</option>
                                <option value="npc">人大代表</option>
                                <option value="cppcc" selected>政协委员</option>
                                <option value="specialist">特邀专家</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="editYear" class="block text-sm font-medium text-gray-700 mb-1">年份 <span class="text-red-500">*</span></label>
                            <select id="editYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择年份</option>
                                <option value="2024" selected>2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                                <option value="2021">2021</option>
                                <option value="2020">2020</option>
                            </select>
                        </div>
                        <div>
                            <label for="editProposalTheme" class="block text-sm font-medium text-gray-700 mb-1">提案主题 <span class="text-red-500">*</span></label>
                            <input type="text" id="editProposalTheme" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="关于加强宁波市生物医药产业创新发展的建议">
                        </div>
                        <div>
                            <label for="editProposalStatus" class="block text-sm font-medium text-gray-700 mb-1">提案状态 <span class="text-red-500">*</span></label>
                            <select id="editProposalStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择提案状态</option>
                                <option value="submitted">已提交</option>
                                <option value="reviewing">审核中</option>
                                <option value="adopted" selected>已采纳</option>
                                <option value="rejected">未采纳</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="editProposalContent" class="block text-sm font-medium text-gray-700 mb-1">提案内容摘要</label>
                        <textarea id="editProposalContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">建议加大对宁波市生物医药产业的政策扶持力度，建立专项基金，吸引高端人才，推动产学研深度融合，提升产业创新能力和核心竞争力。</textarea>
                    </div>
                    <div>
                        <label for="editReportTitle" class="block text-sm font-medium text-gray-700 mb-1">报告标题</label>
                        <input type="text" id="editReportTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市生物医药产业发展现状与对策研究">
                    </div>
                    <div>
                        <label for="editResearchContent" class="block text-sm font-medium text-gray-700 mb-1">调研内容</label>
                        <textarea id="editResearchContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">对宁波市区15家生物医药企业进行实地调研，了解企业在研发投入、人才队伍、成果转化等方面的情况，分析当前产业发展面临的主要问题和挑战。</textarea>
                    </div>
                    <div>
                        <label for="editRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="editRemark" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">该提案已被市科技局采纳，相关政策正在制定中。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">相关附件</label>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">宁波市生物医药产业发展调研报告.pdf</span>
                                </div>
                                <button type="button" class="text-red-500 hover:text-red-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                <label for="edit-file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>添加更多文件</span>
                                    <input id="edit-file-upload" name="edit-file-upload" type="file" class="sr-only" multiple>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">参政咨政记录详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="bg-gray-50 rounded-lg p-5 mb-4">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">张明</h4>
                            <p class="text-sm text-gray-500">宁波市科技研究院 · 政协委员</p>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="flex">
                                <span class="text-gray-500 w-24">所属单位：</span>
                                <span class="text-gray-900">宁波市科技研究院</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">参政身份：</span>
                                <span class="text-gray-900">政协委员</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">年份：</span>
                                <span class="text-gray-900">2024</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">提案状态：</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已采纳</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">提案信息</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex flex-col">
                                <span class="text-gray-500">提案主题：</span>
                                <span class="text-gray-900">关于加强宁波市生物医药产业创新发展的建议</span>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-gray-500">提案内容摘要：</span>
                                <span class="text-gray-900">建议加大对宁波市生物医药产业的政策扶持力度，建立专项基金，吸引高端人才，推动产学研深度融合，提升产业创新能力和核心竞争力。</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">报告与调研</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex flex-col">
                                <span class="text-gray-500">报告标题：</span>
                                <span class="text-gray-900">宁波市生物医药产业发展现状与对策研究</span>
                            </div>
                            <div class="flex flex-col">
                                <span class="text-gray-500">调研内容：</span>
                                <span class="text-gray-900">对宁波市区15家生物医药企业进行实地调研，了解企业在研发投入、人才队伍、成果转化等方面的情况，分析当前产业发展面临的主要问题和挑战。调研发现，我市生物医药产业存在研发投入不足、高端人才短缺、成果转化机制不完善等问题，需要采取有效措施加以解决。</span>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">备注信息</h4>
                        <div class="text-sm text-gray-900">
                            该提案已被市科技局采纳，相关政策正在制定中。预计2024年第三季度将出台《宁波市生物医药产业创新发展三年行动计划》，并设立10亿元专项发展基金。
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-3">相关附件</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700">宁波市生物医药产业发展调研报告.pdf</span>
                                </div>
                                <button type="button" class="text-blue-500 hover:text-blue-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button type="button" onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        function confirmDelete() {
            if (confirm('确定要移除这条记录吗？此操作不可恢复。')) {
                alert('记录已成功移除。');
            }
        }

        function toggleExportDropdown() {
            const options = document.getElementById('exportOptions');
            options.classList.toggle('hidden');
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('操作成功 (原型演示)');
                    closeModal(parentModalId);
                });
            });
            
            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', function(e) {
                const exportDropdown = document.getElementById('exportDropdown');
                const exportOptions = document.getElementById('exportOptions');
                if (exportOptions && !exportOptions.classList.contains('hidden') && 
                    !exportDropdown.contains(e.target)) {
                    exportOptions.classList.add('hidden');
                }
            });
            
            // 文件上传处理
            document.querySelectorAll('input[type="file"]').forEach(fileInput => {
                fileInput.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        alert(`已选择 ${e.target.files.length} 个文件`);
                    }
                });
            });
        });
    </script>
</body>
</html>