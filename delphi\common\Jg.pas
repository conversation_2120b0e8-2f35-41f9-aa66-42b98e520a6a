unit Jg;

interface

uses
  Classes;

type
  TJg = class
  private
    FHpbh: string;
    FJgbh: string;
    FKsbm: string;
    FZwpm: string;
    FYscm: string;
    FMy: double;
  public
    property Hpbh: string read FHpbh write FHpbh;
    property Jgbh: string read FJgbh write FJgbh;
    property Ksbm: string read FKsbm write FKsbm;
    property Zwpm: string read FZwpm write FZwpm;
    property Yscm: string read FYscm write FYscm;
    property My: double read FMy write FMy;
  end;

implementation

end.
