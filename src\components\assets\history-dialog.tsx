'use client'

import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Button } from "@/components/ui/button"
import { Clock, RotateCcw, ArrowLeftRight } from "lucide-react"

interface HistoryVersion {
  id: string
  version: string
  date: string
  author: string
  description: string
}

const historyData: HistoryVersion[] = [
  {
    id: '1',
    version: 'v1.0.3',
    date: '2024-03-20 14:30',
    author: '张三',
    description: '更新布局配置，优化展示效果'
  },
  {
    id: '2',
    version: 'v1.0.2',
    date: '2024-03-19 16:45',
    author: '李四',
    description: '新增数据指标组件'
  },
  {
    id: '3',
    version: 'v1.0.1',
    date: '2024-03-18 11:20',
    author: '王五',
    description: '初始化大屏配置'
  }
]

interface HistoryDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function HistoryD<PERSON>og({ open, onOpenChange }: HistoryDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>历史版本</DialogTitle>
        </DialogHeader>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {historyData.map((version) => (
              <div
                key={version.id}
                className="relative pl-6 pb-4 border-l-2 border-gray-200 last:border-l-0"
              >
                <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-white border-2 border-blue-500" />
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-4">
                      <span className="font-medium">{version.version}</span>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {version.date}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <ArrowLeftRight className="h-4 w-4 mr-1" />
                        对比
                      </Button>
                      <Button variant="outline" size="sm">
                        <RotateCcw className="h-4 w-4 mr-1" />
                        还原
                      </Button>
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="text-gray-500">作者：</span>
                    {version.author}
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    <span className="text-gray-500">描述：</span>
                    {version.description}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  )
} 