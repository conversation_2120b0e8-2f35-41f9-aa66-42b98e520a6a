'use client';

import { useState, use<PERSON><PERSON>back, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Search, RefreshCw, Upload, Download, FileDown, 
  MapPin, Eye, Copy, RotateCcw, Calendar,
  AlertCircle, CheckCircle, Clock, Map, FileText,
  ChevronDown, X, Filter, Share2
} from "lucide-react";

// 地址匹配结果数据类型定义
interface AddressMatch {
  id: string;
  originalAddress: string;
  standardAddress: string;
  adminArea: string;
  functionalArea: string;
  confidence: number;
  status: '已完成' | '处理中' | '失败';
  taskId: string;
  createTime: string;
  coordinates?: {
    lat: number;
    lng: number;
  };
}

// 批量任务数据类型
interface BatchTask {
  id: string;
  fileName: string;
  total: number;
  processed: number;
  successful: number;
  failed: number;
  status: '处理中' | '已完成' | '失败';
  createTime: string;
  results: AddressMatch[];
}

// 模拟数据 - 更丰富的科研机构和产业园区地址数据
const mockAddressMatches: AddressMatch[] = [
  {
    id: 'addr_001',
    originalAddress: '北京中科院软件所',
    standardAddress: '北京市海淀区中关村南四街4号中科院软件园',
    adminArea: '北京市海淀区',
    functionalArea: '中关村科学城核心区',
    confidence: 96,
    status: '已完成',
    taskId: 'task_001',
    createTime: '2024-01-15 14:30:25',
    coordinates: { lat: 40.056873, lng: 116.307968 }
  },
  {
    id: 'addr_002',
    originalAddress: '上海张江人工智能岛',
    standardAddress: '上海市浦东新区张江科学城人工智能岛',
    adminArea: '上海市浦东新区',
    functionalArea: '张江科学城',
    confidence: 94,
    status: '已完成',
    taskId: 'task_002',
    createTime: '2024-01-15 14:28:10',
    coordinates: { lat: 31.235929, lng: 121.499763 }
  },
  {
    id: 'addr_003',
    originalAddress: '深圳南山科技园腾讯总部',
    standardAddress: '深圳市南山区科技园科技中一路腾讯滨海大厦',
    adminArea: '深圳市南山区',
    functionalArea: '深圳高新技术产业园区南区',
    confidence: 98,
    status: '已完成',
    taskId: 'task_003',
    createTime: '2024-01-15 14:25:45',
    coordinates: { lat: 22.540503, lng: 113.934528 }
  },
  {
    id: 'addr_004',
    originalAddress: '杭州西湖大学云栖校区',
    standardAddress: '杭州市西湖区云栖小镇石龙山街18号西湖大学',
    adminArea: '杭州市西湖区',
    functionalArea: '杭州云栖小镇',
    confidence: 92,
    status: '已完成',
    taskId: 'task_004',
    createTime: '2024-01-15 14:22:30',
    coordinates: { lat: 30.276225, lng: 120.031039 }
  },
  {
    id: 'addr_005',
    originalAddress: '成都天府新区独角兽岛',
    standardAddress: '成都市天府新区兴隆湖畔独角兽岛',
    adminArea: '成都市天府新区',
    functionalArea: '成都科学城',
    confidence: 89,
    status: '处理中',
    taskId: 'task_005',
    createTime: '2024-01-15 14:20:15',
    coordinates: { lat: 30.456123, lng: 104.062847 }
  },
  {
    id: 'addr_006',
    originalAddress: '苏州工业园区生物医药产业园',
    standardAddress: '苏州市工业园区星湖街218号苏州生物医药产业园',
    adminArea: '苏州市工业园区',
    functionalArea: '苏州工业园区',
    confidence: 95,
    status: '已完成',
    taskId: 'task_006',
    createTime: '2024-01-15 14:18:45',
    coordinates: { lat: 31.289774, lng: 120.695474 }
  },
  {
    id: 'addr_007',
    originalAddress: '广州大学城华南理工',
    standardAddress: '广州市番禺区大学城外环东路382号华南理工大学',
    adminArea: '广州市番禺区',
    functionalArea: '广州大学城',
    confidence: 93,
    status: '已完成',
    taskId: 'task_007',
    createTime: '2024-01-15 14:15:20',
    coordinates: { lat: 23.048672, lng: 113.396524 }
  },
  {
    id: 'addr_008',
    originalAddress: '西安高新区软件新城',
    standardAddress: '西安市高新区软件新城天谷八路软件新城',
    adminArea: '西安市高新区',
    functionalArea: '西安软件新城',
    confidence: 87,
    status: '已完成',
    taskId: 'task_008',
    createTime: '2024-01-15 14:12:10',
    coordinates: { lat: 34.229825, lng: 108.891654 }
  },
  {
    id: 'addr_009',
    originalAddress: '武汉光谷生物城',
    standardAddress: '武汉市东湖新技术开发区高新大道666号光谷生物城',
    adminArea: '武汉市东湖新技术开发区',
    functionalArea: '武汉东湖国家自主创新示范区',
    confidence: 91,
    status: '已完成',
    taskId: 'task_009',
    createTime: '2024-01-15 14:10:35',
    coordinates: { lat: 30.476543, lng: 114.423876 }
  },
  {
    id: 'addr_010',
    originalAddress: '合肥中科大先研院',
    standardAddress: '合肥市蜀山区望江西路860号中科大先进技术研究院',
    adminArea: '合肥市蜀山区',
    functionalArea: '合肥科学城',
    confidence: 76,
    status: '失败',
    taskId: 'task_010',
    createTime: '2024-01-15 14:08:15',
    coordinates: { lat: 31.838974, lng: 117.223621 }
  },
  {
    id: 'addr_011',
    originalAddress: '南京江北新区生命科技园',
    standardAddress: '南京市江北新区药谷大道11号生命科技园',
    adminArea: '南京市江北新区',
    functionalArea: '南京江北国家级新区',
    confidence: 88,
    status: '处理中',
    taskId: 'task_011',
    createTime: '2024-01-15 14:05:50',
    coordinates: { lat: 32.118345, lng: 118.623456 }
  },
  {
    id: 'addr_012',
    originalAddress: '长沙岳麓山大学科技城',
    standardAddress: '长沙市岳麓区麓山南路岳麓山国家大学科技城',
    adminArea: '长沙市岳麓区',
    functionalArea: '岳麓山国家大学科技城',
    confidence: 90,
    status: '已完成',
    taskId: 'task_012',
    createTime: '2024-01-15 14:03:25',
    coordinates: { lat: 28.196234, lng: 112.936789 }
  }
];

const mockBatchTasks: BatchTask[] = [
  {
    id: 'batch_001',
    fileName: '企业地址清单_2024Q1.xlsx',
    total: 156,
    processed: 156,
    successful: 148,
    failed: 8,
    status: '已完成',
    createTime: '2024-01-15 10:30:00',
    results: mockAddressMatches
  },
  {
    id: 'batch_002',
    fileName: '科研机构地址数据.csv',
    total: 89,
    processed: 45,
    successful: 42,
    failed: 3,
    status: '处理中',
    createTime: '2024-01-15 14:15:30',
    results: []
  }
];

// 查询条件面板组件
function QueryPanel({ 
  onSearch, 
  loading 
}: { 
  onSearch: (params: any) => void;
  loading: boolean;
}) {
  const [address, setAddress] = useState('');
  const [matchType, setMatchType] = useState('admin');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });

  const handleSearch = () => {
    onSearch({
      address,
      matchType,
      dateRange
    });
  };

  const handleReset = () => {
    setAddress('');
    setMatchType('admin');
    setDateRange({ start: '', end: '' });
  };

  return (
    <Card className="bg-white rounded-lg shadow-md">
      <CardHeader className="border-b border-gray-200">
        <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
          <Search className="h-5 w-5 mr-2 text-blue-600" />
          查询条件
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              地址信息
            </label>
            <Input
              placeholder="请输入待匹配的地址信息（支持模糊匹配）"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              className="w-full"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              匹配维度
            </label>
            <Select value={matchType} onValueChange={setMatchType}>
              <SelectTrigger>
                <SelectValue placeholder="选择匹配维度" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">行政区</SelectItem>
                <SelectItem value="functional">功能区</SelectItem>
                <SelectItem value="both">行政区+功能区</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              创建时间
            </label>
            <div className="flex space-x-2">
              <Input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                className="flex-1"
              />
              <Input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                className="flex-1"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={handleReset}
            className="flex items-center"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            重置
          </Button>
          <Button
            onClick={handleSearch}
            disabled={loading}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white flex items-center"
          >
            {loading ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            查询
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// 结果列表组件
function ResultsTable({ 
  results, 
  onViewDetail, 
  onCopyResult 
}: { 
  results: AddressMatch[];
  onViewDetail: (result: AddressMatch) => void;
  onCopyResult: (result: AddressMatch) => void;
}) {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case '已完成':
        return (
          <Badge className="bg-green-100 text-green-700 border border-green-200 px-3 py-1 rounded-full font-medium">
            <CheckCircle className="h-3 w-3 mr-1" />
            已完成
          </Badge>
        );
      case '处理中':
        return (
          <Badge className="bg-yellow-100 text-yellow-700 border border-yellow-200 px-3 py-1 rounded-full font-medium">
            <Clock className="h-3 w-3 mr-1" />
            处理中
          </Badge>
        );
      case '失败':
        return (
          <Badge className="bg-red-100 text-red-700 border border-red-200 px-3 py-1 rounded-full font-medium">
            <AlertCircle className="h-3 w-3 mr-1" />
            失败
          </Badge>
        );
      default:
        return (
          <Badge className="bg-gray-100 text-gray-700 border border-gray-200 px-3 py-1 rounded-full font-medium">
            {status}
          </Badge>
        );
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600';
    if (confidence >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <Card className="bg-white rounded-xl shadow-lg border-0 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100 py-4">
        <CardTitle className="text-lg font-semibold text-gray-900 flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 bg-blue-500 rounded-lg mr-3">
              <FileText className="h-5 w-5 text-white" />
            </div>
            <span>匹配结果列表</span>
            <Badge className="ml-3 bg-blue-500 text-white px-3 py-1 rounded-full font-medium">
              {results.length} 条记录
            </Badge>
          </div>
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-400 rounded-full"></div>
              <span className="text-green-700 font-medium">
                已完成: {results.filter(r => r.status === '已完成').length}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
              <span className="text-yellow-700 font-medium">
                处理中: {results.filter(r => r.status === '处理中').length}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-400 rounded-full"></div>
              <span className="text-red-700 font-medium">
                失败: {results.filter(r => r.status === '失败').length}
              </span>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[450px]">
          <Table>
            <TableHeader className="sticky top-0 z-10">
              <TableRow className="bg-gradient-to-r from-gray-50 to-blue-50 hover:from-gray-50 hover:to-blue-50 border-b border-gray-200">
                <TableHead className="text-gray-700 font-semibold py-4 px-6">
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-blue-500" />
                    <span>原始地址</span>
                  </div>
                </TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>标准化地址</span>
                  </div>
                </TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6">行政区</TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6">功能区</TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6 text-center">置信度</TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6 text-center">状态</TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6">创建时间</TableHead>
                <TableHead className="text-gray-700 font-semibold py-4 px-6 text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {results.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="py-12 text-center">
                    <div className="flex flex-col items-center space-y-3">
                      <div className="p-4 bg-gray-100 rounded-full">
                        <Search className="h-8 w-8 text-gray-400" />
                      </div>
                      <div className="text-gray-500">
                        <div className="font-medium">暂无匹配结果</div>
                        <div className="text-sm mt-1">请尝试调整查询条件或上传地址文件</div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                results.map((result, index) => (
                <TableRow 
                  key={result.id} 
                  className={`hover:bg-blue-50 transition-colors duration-200 border-b border-gray-100 ${
                    index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                  }`}
                >
                  <TableCell className="py-4 px-6">
                    <div className="max-w-64">
                      <div className="text-sm font-medium text-gray-900 truncate" title={result.originalAddress}>
                        {result.originalAddress}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        ID: {result.id}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6">
                    <div className="max-w-64">
                      <div className="text-sm text-gray-900 truncate" title={result.standardAddress}>
                        {result.standardAddress}
                      </div>
                      {result.coordinates && (
                        <div className="text-xs text-blue-600 mt-1">
                          📍 {result.coordinates.lat.toFixed(4)}, {result.coordinates.lng.toFixed(4)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6">
                    <div className="text-sm font-medium text-gray-800">
                      {result.adminArea}
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6">
                    <div className="max-w-48">
                      <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {result.functionalArea}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6 text-center">
                    <div className="flex items-center justify-center">
                      <div className={`flex items-center space-x-2 px-3 py-1.5 rounded-full text-sm font-semibold ${
                        result.confidence >= 90 ? 'bg-green-100 text-green-700' :
                        result.confidence >= 75 ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {result.confidence >= 90 ? '🎯' : result.confidence >= 75 ? '⚡' : '⚠️'}
                        <span>{result.confidence}%</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6 text-center">
                    {getStatusBadge(result.status)}
                  </TableCell>
                  <TableCell className="py-4 px-6">
                    <div className="text-sm text-gray-600">
                      <div>{result.createTime.split(' ')[0]}</div>
                      <div className="text-xs text-gray-400">{result.createTime.split(' ')[1]}</div>
                    </div>
                  </TableCell>
                  <TableCell className="py-4 px-6 text-center">
                    <div className="flex justify-center space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewDetail(result)}
                        className="h-8 w-8 p-0 hover:bg-blue-100 hover:text-blue-600 rounded-full transition-colors"
                        title="查看详情"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onCopyResult(result)}
                        className="h-8 w-8 p-0 hover:bg-green-100 hover:text-green-600 rounded-full transition-colors"
                        title="复制结果"
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}

// 批量导入组件
function BatchImportCard({ 
  onFileUpload, 
  uploadProgress, 
  batchTasks 
}: { 
  onFileUpload: (file: File) => void;
  uploadProgress: number;
  batchTasks: BatchTask[];
}) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      onFileUpload(files[0]);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onFileUpload(files[0]);
    }
  };

  const getTaskStatusBadge = (status: string) => {
    switch (status) {
      case '已完成':
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>;
      case '处理中':
        return <Badge className="bg-yellow-100 text-yellow-800">处理中</Badge>;
      case '失败':
        return <Badge className="bg-red-100 text-red-800">失败</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <Card className="bg-white rounded-lg shadow-md">
      <CardHeader className="border-b border-gray-200">
        <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
          <Upload className="h-5 w-5 mr-2 text-blue-600" />
          批量导入
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600 mb-2">
            拖拽文件到此处或 
            <label className="text-blue-600 cursor-pointer ml-1">
              点击选择文件
              <input
                type="file"
                accept=".xlsx,.xls,.csv"
                onChange={handleFileSelect}
                className="hidden"
              />
            </label>
          </p>
          <p className="text-sm text-gray-500">
            支持 Excel (.xlsx/.xls) 和 CSV 格式文件
          </p>
          <Button
            variant="outline"
            className="mt-3"
            onClick={() => {
              // 模拟下载模板
              const link = document.createElement('a');
              link.href = '/template/address-import-template.xlsx';
              link.download = '地址导入模板.xlsx';
              link.click();
            }}
          >
            <FileDown className="h-4 w-4 mr-2" />
            下载导入模板
          </Button>
        </div>

        {uploadProgress > 0 && uploadProgress < 100 && (
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">上传进度</span>
              <span className="text-sm text-gray-600">{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
          </div>
        )}

        {batchTasks.length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-900 mb-3">批量任务历史</h4>
            <ScrollArea className="h-40">
              <div className="space-y-2">
                {batchTasks.map((task) => (
                  <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium">{task.fileName}</span>
                        {getTaskStatusBadge(task.status)}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        总计: {task.total} | 成功: {task.successful} | 失败: {task.failed}
                      </div>
                      <div className="text-xs text-gray-400">{task.createTime}</div>
                    </div>
                    {task.status === '处理中' && (
                      <div className="ml-4">
                        <Progress 
                          value={(task.processed / task.total) * 100} 
                          className="w-20 h-2" 
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// 详情展示弹窗组件
function DetailModal({ 
  result, 
  isOpen, 
  onClose, 
  onCorrect 
}: { 
  result: AddressMatch | null;
  isOpen: boolean;
  onClose: () => void;
  onCorrect: (result: AddressMatch, corrections: any) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [corrections, setCorrections] = useState({
    standardAddress: '',
    adminArea: '',
    functionalArea: ''
  });

  useEffect(() => {
    if (result) {
      setCorrections({
        standardAddress: result.standardAddress,
        adminArea: result.adminArea,
        functionalArea: result.functionalArea
      });
    }
  }, [result]);

  const handleSaveCorrection = () => {
    if (result) {
      onCorrect(result, corrections);
      setIsEditing(false);
    }
  };

  if (!result) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <MapPin className="h-5 w-5 mr-2 text-blue-600" />
            地址匹配详情
          </DialogTitle>
        </DialogHeader>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 基本信息 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">基本信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">原始地址</label>
                  <div className="mt-1 p-2 bg-gray-50 rounded border">
                    {result.originalAddress}
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-700">标准化地址</label>
                  {isEditing ? (
                    <Input
                      value={corrections.standardAddress}
                      onChange={(e) => setCorrections(prev => ({ ...prev, standardAddress: e.target.value }))}
                      className="mt-1"
                    />
                  ) : (
                    <div className="mt-1 p-2 bg-gray-50 rounded border">
                      {result.standardAddress}
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">行政区</label>
                    {isEditing ? (
                      <Input
                        value={corrections.adminArea}
                        onChange={(e) => setCorrections(prev => ({ ...prev, adminArea: e.target.value }))}
                        className="mt-1"
                      />
                    ) : (
                      <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                        {result.adminArea}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700">功能区</label>
                    {isEditing ? (
                      <Input
                        value={corrections.functionalArea}
                        onChange={(e) => setCorrections(prev => ({ ...prev, functionalArea: e.target.value }))}
                        className="mt-1"
                      />
                    ) : (
                      <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                        {result.functionalArea}
                      </div>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="text-sm font-medium text-gray-700">置信度</label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      <span className={`font-medium ${
                        result.confidence >= 90 ? 'text-green-600' : 
                        result.confidence >= 70 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {result.confidence}%
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-700">处理状态</label>
                    <div className="mt-1">
                      <Badge className={
                        result.status === '已完成' ? 'bg-green-100 text-green-800' :
                        result.status === '处理中' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }>
                        {result.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                {result.coordinates && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">坐标位置</label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      经度: {result.coordinates.lng}, 纬度: {result.coordinates.lat}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="flex space-x-3">
              {isEditing ? (
                <>
                  <Button onClick={handleSaveCorrection} className="flex-1">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    保存修正
                  </Button>
                  <Button variant="outline" onClick={() => setIsEditing(false)} className="flex-1">
                    取消
                  </Button>
                </>
              ) : (
                <Button onClick={() => setIsEditing(true)} className="flex-1">
                  人工校正
                </Button>
              )}
            </div>
          </div>

          {/* 地图预览区域 */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center">
                  <Map className="h-4 w-4 mr-2" />
                  位置预览
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <MapPin className="h-12 w-12 mx-auto mb-2" />
                    <p>地图预览</p>
                    <p className="text-sm mt-1">
                      {result.coordinates ? 
                        `位置: ${result.coordinates.lat}, ${result.coordinates.lng}` : 
                        '暂无坐标信息'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">处理记录</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">地址解析完成</div>
                      <div className="text-xs text-gray-500">{result.createTime}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">匹配结果生成</div>
                      <div className="text-xs text-gray-500">系统自动匹配</div>
                    </div>
                  </div>
                  {result.status === '已完成' && (
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium">处理完成</div>
                        <div className="text-xs text-gray-500">质量检查通过</div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// 导出功能组件
function ExportSection({ 
  onExport, 
  results 
}: { 
  onExport: (format: string, scope: string) => void;
  results: AddressMatch[];
}) {
  return (
    <Card className="bg-white rounded-lg shadow-md">
      <CardHeader className="border-b border-gray-200">
        <CardTitle className="text-lg font-medium text-gray-900 flex items-center">
          <Share2 className="h-5 w-5 mr-2 text-blue-600" />
          导出与共享
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">导出当前结果</h4>
            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={() => onExport('excel', 'current')}
                className="w-full justify-start"
                disabled={results.length === 0}
              >
                <Download className="h-4 w-4 mr-2" />
                导出为 Excel 文件
              </Button>
              <Button
                variant="outline"
                onClick={() => onExport('csv', 'current')}
                className="w-full justify-start"
                disabled={results.length === 0}
              >
                <Download className="h-4 w-4 mr-2" />
                导出为 CSV 文件
              </Button>
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-3">批量导出</h4>
            <div className="space-y-2">
              <Button
                variant="outline"
                onClick={() => onExport('excel', 'all')}
                className="w-full justify-start"
              >
                <FileDown className="h-4 w-4 mr-2" />
                导出全部任务结果
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  const url = `${window.location.origin}/share/address-results?token=abc123`;
                  navigator.clipboard.writeText(url);
                  alert('共享链接已复制到剪贴板');
                }}
                className="w-full justify-start"
              >
                <Share2 className="h-4 w-4 mr-2" />
                生成共享链接
              </Button>
            </div>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center text-sm text-gray-600">
            <AlertCircle className="h-4 w-4 mr-2" />
            导出历史和共享链接会自动记录，支持后续审计追踪
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// 主页面组件
export default function AddressMatchingPage() {
  const [results, setResults] = useState<AddressMatch[]>(mockAddressMatches);
  const [batchTasks, setBatchTasks] = useState<BatchTask[]>(mockBatchTasks);
  const [loading, setLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [selectedResult, setSelectedResult] = useState<AddressMatch | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // 搜索处理
  const handleSearch = useCallback(async (params: any) => {
    setLoading(true);
    
    // 模拟API调用
    setTimeout(() => {
      let filteredResults = [...mockAddressMatches];
      
      if (params.address) {
        filteredResults = filteredResults.filter(result =>
          result.originalAddress.includes(params.address) ||
          result.standardAddress.includes(params.address)
        );
      }
      
      if (params.matchType === 'admin') {
        // 只显示有行政区信息的结果
      } else if (params.matchType === 'functional') {
        // 只显示有功能区信息的结果
      }
      
      setResults(filteredResults);
      setLoading(false);
    }, 1500);
  }, []);

  // 文件上传处理
  const handleFileUpload = useCallback(async (file: File) => {
    setUploadProgress(10);
    
    // 模拟文件上传和处理
    const newTask: BatchTask = {
      id: `batch_${Date.now()}`,
      fileName: file.name,
      total: Math.floor(Math.random() * 200) + 50,
      processed: 0,
      successful: 0,
      failed: 0,
      status: '处理中',
      createTime: new Date().toLocaleString('zh-CN'),
      results: []
    };
    
    setBatchTasks(prev => [newTask, ...prev]);
    
    // 模拟进度更新
    let progress = 10;
    const interval = setInterval(() => {
      progress += Math.random() * 20;
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        
        // 更新任务状态
        setBatchTasks(prev => prev.map(task => 
          task.id === newTask.id 
            ? {
                ...task,
                status: '已完成',
                processed: task.total,
                successful: Math.floor(task.total * 0.9),
                failed: Math.floor(task.total * 0.1)
              }
            : task
        ));
        
        setUploadProgress(0);
      } else {
        setUploadProgress(progress);
        
        // 更新处理进度
        setBatchTasks(prev => prev.map(task => 
          task.id === newTask.id 
            ? {
                ...task,
                processed: Math.floor((progress / 100) * task.total),
                successful: Math.floor((progress / 100) * task.total * 0.9),
                failed: Math.floor((progress / 100) * task.total * 0.1)
              }
            : task
        ));
      }
    }, 500);
  }, []);

  // 查看详情
  const handleViewDetail = useCallback((result: AddressMatch) => {
    setSelectedResult(result);
    setIsDetailModalOpen(true);
  }, []);

  // 复制结果
  const handleCopyResult = useCallback((result: AddressMatch) => {
    const text = `原始地址：${result.originalAddress}\n标准化地址：${result.standardAddress}\n行政区：${result.adminArea}\n功能区：${result.functionalArea}\n置信度：${result.confidence}%`;
    navigator.clipboard.writeText(text);
    alert('结果已复制到剪贴板');
  }, []);

  // 人工校正
  const handleCorrectResult = useCallback((result: AddressMatch, corrections: any) => {
    setResults(prev => prev.map(r => 
      r.id === result.id 
        ? { ...r, ...corrections }
        : r
    ));
    alert('校正结果已保存');
  }, []);

  // 导出处理
  const handleExport = useCallback((format: string, scope: string) => {
    // 模拟导出
    const filename = scope === 'current' 
      ? `地址匹配结果_${new Date().toISOString().split('T')[0]}.${format}`
      : `全部地址匹配结果_${new Date().toISOString().split('T')[0]}.${format}`;
    
    alert(`正在导出文件: ${filename}`);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="h-[calc(100vh-4rem)] overflow-y-auto p-8">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* 页面标题 */}
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-bold text-gray-900">地址匹配查询</h1>
            <div className="text-sm text-gray-500">
              科研人才与项目管理 / 地理信息分析管理 / 地址匹配查询
            </div>
          </div>

          {/* 查询条件区 */}
          <QueryPanel onSearch={handleSearch} loading={loading} />

          {/* 主要内容区域 */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
            {/* 左侧：结果列表 */}
            <div className="xl:col-span-2 space-y-6">
              <ResultsTable
                results={results}
                onViewDetail={handleViewDetail}
                onCopyResult={handleCopyResult}
              />
              
              <ExportSection
                onExport={handleExport}
                results={results}
              />
            </div>

            {/* 右侧：批量导入 */}
            <div className="space-y-6">
              <BatchImportCard
                onFileUpload={handleFileUpload}
                uploadProgress={uploadProgress}
                batchTasks={batchTasks}
              />
            </div>
          </div>
        </div>
      </div>

      {/* 详情弹窗 */}
      <DetailModal
        result={selectedResult}
        isOpen={isDetailModalOpen}
        onClose={() => setIsDetailModalOpen(false)}
        onCorrect={handleCorrectResult}
      />
    </div>
  );
} 