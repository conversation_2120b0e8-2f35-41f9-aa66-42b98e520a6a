<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">专家详情展示业务流程</text>

  <!-- 阶段一：数据归集与档案构建 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与档案构建</text>
  
  <!-- 节点1: 多源数据归集 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据归集</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">科技局、高校、院所专家数据</text>
  </g>

  <!-- 节点2: 专家档案构建 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专家档案构建</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">整合基础信息、成果、履历</text>
  </g>

  <!-- 阶段二：详情页面渲染与展示 -->
  <text x="700" y="400" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：详情页面渲染与展示</text>

  <!-- 节点3: 用户点击触发 -->
  <g transform="translate(200, 450)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户点击触发</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">从总览或清册进入详情页</text>
  </g>

  <!-- 节点4: 实时数据调取 -->
  <g transform="translate(500, 450)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时数据调取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">渲染详情页与可视化组件</text>
  </g>

  <!-- 节点5: 分区数据加载 -->
  <g transform="translate(800, 450)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分区数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">依次加载各板块内容</text>
  </g>

  <!-- 阶段三：交互浏览与数据操作 -->
  <text x="700" y="600" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：交互浏览与数据操作</text>

  <!-- 节点6: 板块切换浏览 -->
  <g transform="translate(300, 650)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">板块切换浏览</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">基础信息、成果、奖励、项目</text>
  </g>

  <!-- 节点7: 筛选排序导出 -->
  <g transform="translate(600, 650)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选排序导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按类别、时间条件操作</text>
  </g>

  <!-- 节点8: 关联数据刷新 -->
  <g transform="translate(900, 650)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联数据刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据用户操作自动更新</text>
  </g>

  <!-- 阶段四：数据导出与审计追溯 -->
  <text x="700" y="800" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与审计追溯</text>

  <!-- 节点9: 按需导出与归档 -->
  <g transform="translate(400, 850)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">按需导出与审计追溯</text>
    <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-100">个人简历</tspan>
      <tspan dx="40">项目履历</tspan>
      <tspan dx="40">成果报告</tspan>
      <tspan dx="40">操作日志</tspan>
    </text>
  </g>

  <!-- 连接线 多源数据归集 -> 专家档案构建 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 专家档案构建 -> 用户点击触发 -->
  <path d="M 650 320 C 550 370, 450 400, 350 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 用户点击触发 -> 实时数据调取 -->
  <path d="M 400 485 Q 450 485 500 485" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 实时数据调取 -> 分区数据加载 -->
  <path d="M 700 485 Q 750 485 800 485" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 分区数据加载 -> 板块切换浏览 -->
  <path d="M 850 520 C 750 570, 550 600, 400 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 板块切换浏览 -> 筛选排序导出 -->
  <path d="M 500 685 Q 550 685 600 685" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选排序导出 -> 关联数据刷新 -->
  <path d="M 800 685 Q 850 685 900 685" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 关联数据刷新 -> 按需导出与归档 -->
  <path d="M 950 720 C 900 770, 800 820, 700 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 板块切换浏览 -> 按需导出与归档 -->
  <path d="M 400 720 C 450 770, 500 820, 500 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从按需导出回到多源数据归集 -->
  <path d="M 400 890 C 200 950, 100 700, 150 400, 200 200, 500 150, 600 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="940" text-anchor="middle" font-size="11" fill="#666">数据质量反馈与优化</text>

  <!-- 内部循环：关联数据刷新回到实时数据调取 -->
  <path d="M 1000 650 C 1100 600, 1100 500, 1000 450, 900 450, 800 450, 700 485" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="1050" y="550" text-anchor="middle" font-size="11" fill="#666">实时更新循环</text>

</svg>