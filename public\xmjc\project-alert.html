<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目预警 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .alert-card {
            border-left: 4px solid;
        }
        .alert-high { border-left-color: #EF4444; background: #FEF2F2; }
        .alert-medium { border-left-color: #F59E0B; background: #FFFBEB; }
        .alert-low { border-left-color: #F97316; background: #FFF7ED; }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">项目预警</h1>
                        <p class="text-sm text-gray-600">Project Alert</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm">
                        <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        <span class="text-red-600 font-medium">47项需要关注</span>
                    </div>
                    <button class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-bell mr-2"></i>
                        设置提醒
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 预警概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift alert-card alert-high">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">信用预警</p>
                        <p class="text-2xl font-bold text-red-600">23项</p>
                        <p class="text-red-500 text-xs">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            高风险等级
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-circle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift alert-card alert-medium">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">科研诚信预警</p>
                        <p class="text-2xl font-bold text-yellow-600">15项</p>
                        <p class="text-yellow-500 text-xs">
                            <i class="fas fa-user-shield mr-1"></i>
                            中风险等级
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift alert-card alert-low">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">验收预警</p>
                        <p class="text-2xl font-bold text-orange-600">9项</p>
                        <p class="text-orange-500 text-xs">
                            <i class="fas fa-clock mr-1"></i>
                            低风险等级
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预警详细列表 -->
        <div class="space-y-8">
            <!-- 信用预警 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                        信用预警
                        <span class="ml-2 bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">23项</span>
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-sm bg-red-500 text-white px-3 py-1 rounded-lg">全部</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">新增</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">已处理</button>
                    </div>
                </div>

                <!-- 预警说明 -->
                <div class="bg-red-50 p-4 rounded-lg mb-6">
                    <p class="text-sm text-red-700">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>预警来源：</strong>发改信用平台、地方信用库数据。涉及项目完成单位或承接单位存在信用异常记录。
                    </p>
                </div>

                <!-- 预警列表 -->
                <div class="space-y-4">
                    <div class="border border-red-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">宁波新材料科技有限公司</h4>
                                <p class="text-sm text-gray-600">关联项目：新型复合材料关键技术研发</p>
                            </div>
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">信用异常</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">预警来源：</span>
                                <span class="text-gray-900">发改信用平台</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目金额：</span>
                                <span class="text-gray-900">1,280万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发现时间：</span>
                                <span class="text-gray-900">2024-01-10</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>暂停项目拨款，要求企业说明情况并提供整改方案
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600">暂停拨款</button>
                                <button class="text-xs bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600">发送通知</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>

                    <div class="border border-red-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">浙江智造机器人科技股份公司</h4>
                                <p class="text-sm text-gray-600">关联项目：工业机器人智能控制系统</p>
                            </div>
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">严重失信</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">预警来源：</span>
                                <span class="text-gray-900">地方信用库</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目金额：</span>
                                <span class="text-gray-900">2,560万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发现时间：</span>
                                <span class="text-gray-900">2024-01-08</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>立即终止项目合作，追回已拨付资金，列入黑名单
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700">终止项目</button>
                                <button class="text-xs bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600">追回资金</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <button class="text-blue-500 hover:text-blue-700 text-sm">
                        查看全部23项信用预警 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- 科研诚信预警 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-user-shield text-yellow-500 mr-2"></i>
                        科研诚信预警
                        <span class="ml-2 bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">15项</span>
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-sm bg-yellow-500 text-white px-3 py-1 rounded-lg">学术不端</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">数据造假</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">重复申报</button>
                    </div>
                </div>

                <!-- 预警说明 -->
                <div class="bg-yellow-50 p-4 rounded-lg mb-6">
                    <p class="text-sm text-yellow-700">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>监测范围：</strong>项目负责人及主要参与人员的学术诚信记录，包括论文撤稿、学术不端等行为。
                    </p>
                </div>

                <!-- 预警列表 -->
                <div class="space-y-4">
                    <div class="border border-yellow-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">李某某（项目负责人）</h4>
                                <p class="text-sm text-gray-600">关联项目：人工智能算法优化研究</p>
                            </div>
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">论文撤稿</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">涉事期刊：</span>
                                <span class="text-gray-900">IEEE Computer</span>
                            </div>
                            <div>
                                <span class="text-gray-500">撤稿原因：</span>
                                <span class="text-gray-900">数据重复使用</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发现时间：</span>
                                <span class="text-gray-900">2024-01-12</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>要求项目负责人说明情况，评估项目成果真实性
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600">要求说明</button>
                                <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">成果评估</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>

                    <div class="border border-yellow-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">王某某（主要参与人）</h4>
                                <p class="text-sm text-gray-600">关联项目：新能源材料制备技术</p>
                            </div>
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">重复申报</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">重复项目：</span>
                                <span class="text-gray-900">国家自然科学基金</span>
                            </div>
                            <div>
                                <span class="text-gray-500">重复内容：</span>
                                <span class="text-gray-900">研究目标85%相似</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发现时间：</span>
                                <span class="text-gray-900">2024-01-11</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>核实项目内容差异，要求修改研究方案或撤销申报
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600">核实内容</button>
                                <button class="text-xs bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600">要求修改</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <button class="text-blue-500 hover:text-blue-700 text-sm">
                        查看全部15项诚信预警 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- 验收预警 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-clock text-orange-500 mr-2"></i>
                        验收预警
                        <span class="ml-2 bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">9项</span>
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-sm bg-orange-500 text-white px-3 py-1 rounded-lg">延迟结题</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">中期未通过</button>
                        <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">多次退回</button>
                    </div>
                </div>

                <!-- 预警说明 -->
                <div class="bg-orange-50 p-4 rounded-lg mb-6">
                    <p class="text-sm text-orange-700">
                        <i class="fas fa-info-circle mr-2"></i>
                        <strong>监测内容：</strong>项目进度滞后、中期检查未通过、验收材料多次退回修改等情况。
                    </p>
                </div>

                <!-- 预警列表 -->
                <div class="space-y-4">
                    <div class="border border-orange-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">智能制造关键技术研发项目</h4>
                                <p class="text-sm text-gray-600">承担单位：宁波工业技术研究院</p>
                            </div>
                            <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">验收延期</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">计划结题：</span>
                                <span class="text-gray-900">2023-12-31</span>
                            </div>
                            <div>
                                <span class="text-gray-500">延期时长：</span>
                                <span class="text-gray-900">已延期15天</span>
                            </div>
                            <div>
                                <span class="text-gray-500">延期原因：</span>
                                <span class="text-gray-900">技术验证不充分</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>催促提交验收材料，必要时组织专家现场检查
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600">催促提交</button>
                                <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600">专家检查</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>

                    <div class="border border-orange-200 rounded-lg p-4 hover-lift">
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900">海洋工程装备创新平台建设</h4>
                                <p class="text-sm text-gray-600">承担单位：宁波大学海洋学院</p>
                            </div>
                            <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full">中期未通过</span>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">中期时间：</span>
                                <span class="text-gray-900">2023-12-15</span>
                            </div>
                            <div>
                                <span class="text-gray-500">主要问题：</span>
                                <span class="text-gray-900">进度滞后50%</span>
                            </div>
                            <div>
                                <span class="text-gray-500">整改期限：</span>
                                <span class="text-gray-900">2024-02-15</span>
                            </div>
                        </div>
                        <div class="mt-3 pt-3 border-t border-gray-200">
                            <p class="text-sm text-gray-700 mb-2">
                                <strong>建议处理动作：</strong>督促按期整改，考虑调整项目目标或终止项目
                            </p>
                            <div class="flex space-x-2">
                                <button class="text-xs bg-orange-500 text-white px-3 py-1 rounded hover:bg-orange-600">督促整改</button>
                                <button class="text-xs bg-yellow-500 text-white px-3 py-1 rounded hover:bg-yellow-600">调整目标</button>
                                <button class="text-xs bg-gray-500 text-white px-3 py-1 rounded hover:bg-gray-600">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <button class="text-blue-500 hover:text-blue-700 text-sm">
                        查看全部9项验收预警 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center mt-8">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 模拟实时数据更新
            setInterval(() => {
                const alertDot = document.querySelector('.animate-pulse');
                if (alertDot) {
                    alertDot.style.animation = 'none';
                    setTimeout(() => {
                        alertDot.style.animation = 'pulse 2s infinite';
                    }, 100);
                }
            }, 5000);
        });
    </script>
</body>
</html> 