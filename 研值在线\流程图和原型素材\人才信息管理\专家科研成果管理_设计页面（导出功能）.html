<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家科研成果管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">专家科研成果管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">专家姓名</label>
                    <input type="text" placeholder="请输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                    <input type="text" placeholder="请输入成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="paper">学术论文</option>
                        <option value="patent">专利</option>
                        <option value="project">科研项目</option>
                        <option value="award">科技奖励</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">所属领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="material">新材料</option>
                        <option value="energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">获奖情况</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">完成时间</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="valid">有效</option>
                        <option value="invalid">无效</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 成果列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">科研成果列表</h2>
                <div class="flex space-x-3">
                    <button onclick="openExportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出
                    </button>
                    <button onclick="openLinkModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        关联成果
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属专家</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果简介</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖情况</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">基于深度学习的医学影像分析系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">一种基于深度学习的医学影像自动分析系统，可提高诊断准确率...</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技进步一等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeResult('1')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">新型生物降解材料研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">学术论文</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02-28</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">发表在《Advanced Materials》期刊上的研究成果...</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeResult('2')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智能电网关键技术研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">宁波市重点研发计划项目，研究智能电网关键技术...</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省科技进步二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeResult('3')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">抗肿瘤新药研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">一种新型抗肿瘤药物及其制备方法，已获国家发明专利...</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeResult('4')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">工业机器人控制系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">一种高精度工业机器人控制系统，已应用于宁波市多家制造企业...</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市专利金奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('5')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeResult('5')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">128</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成果详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科研成果详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                            <p class="text-sm text-gray-900">基于深度学习的医学影像分析系统</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                            <p class="text-sm text-gray-900">专利</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属专家</label>
                            <p class="text-sm text-gray-900">王教授</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">完成时间</label>
                            <p class="text-sm text-gray-900">2023-05-15</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属领域</label>
                            <p class="text-sm text-gray-900">人工智能</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">获奖情况</label>
                            <p class="text-sm text-gray-900">宁波市科技进步一等奖</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                            <p class="text-sm text-gray-900">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">成果简介</label>
                        <p class="text-sm text-gray-900 mt-1">
                            一种基于深度学习的医学影像自动分析系统，通过卷积神经网络和迁移学习技术，能够自动识别和分析X光、CT、MRI等医学影像中的异常特征。系统在宁波市多家三甲医院的临床试验中，对肺部结节、脑卒中等疾病的识别准确率达到95%以上，显著提高了诊断效率和准确性。
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">核心技术</label>
                        <p class="text-sm text-gray-900 mt-1">
                            1. 采用改进的ResNet-50网络结构，优化了特征提取能力<br>
                            2. 引入注意力机制，提高了对微小病灶的识别能力<br>
                            3. 开发了专用的数据增强算法，解决了医学影像样本不足的问题<br>
                            4. 系统支持多模态影像融合分析，提高了诊断全面性
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">应用情况</label>
                        <p class="text-sm text-gray-900 mt-1">
                            该系统已在宁波市第一医院、宁波大学附属医院等5家医疗机构部署使用，累计完成超过10万例影像分析，平均诊断时间缩短60%，辅助医生发现了300多例早期癌症病例，取得了显著的社会效益。
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">相关证明材料</label>
                        <div class="mt-2 grid grid-cols-3 gap-4">
                            <div class="border border-gray-200 rounded-md p-3">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-900">专利证书.pdf</span>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-md p-3">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-900">获奖证书.pdf</span>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-md p-3">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="ml-2 text-sm text-gray-900">应用证明.docx</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑成果弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑科研成果</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="基于深度学习的医学影像分析系统">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="paper">学术论文</option>
                                <option value="patent" selected>专利</option>
                                <option value="project">科研项目</option>
                                <option value="award">科技奖励</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属专家</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="王教授">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">完成时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2023-05-15">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属领域</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="ai" selected>人工智能</option>
                                <option value="bio">生物医药</option>
                                <option value="material">新材料</option>
                                <option value="energy">新能源</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">获奖情况</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市科技进步一等奖">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="valid" selected>有效</option>
                                <option value="invalid">无效</option>
                                <option value="pending">待审核</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">成果简介</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">一种基于深度学习的医学影像自动分析系统，通过卷积神经网络和迁移学习技术，能够自动识别和分析X光、CT、MRI等医学影像中的异常特征。系统在宁波市多家三甲医院的临床试验中，对肺部结节、脑卒中等疾病的识别准确率达到95%以上，显著提高了诊断效率和准确性。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">上传证明材料</label>
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="pl-1">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500">支持PDF、DOCX、JPG格式，最大10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 关联成果弹窗 -->
    <div id="linkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">关联科研成果</h3>
                    <button onclick="closeModal('linkModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">选择专家</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择专家</option>
                                <option value="1">王教授</option>
                                <option value="2">李研究员</option>
                                <option value="3">张教授</option>
                                <option value="4">陈博士</option>
                                <option value="5">赵教授</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">搜索成果</label>
                            <input type="text" placeholder="输入成果名称或关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-md">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-medium text-gray-700">可关联成果列表</h4>
                            <div class="text-xs text-gray-500">已选择 <span class="font-medium">2</span> 项</div>
                        </div>
                        <div class="overflow-y-auto max-h-96">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能医疗影像分析算法</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-10</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基于AI的病理诊断系统</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">软件著作权</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" checked>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">医疗大数据分析平台</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11-20</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">远程医疗诊断系统</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-05</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('linkModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            确认关联
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出成果弹窗 -->
    <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">导出科研成果</h3>
                    <button onclick="closeModal('exportModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">导出范围</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="exportScope" value="current" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">当前筛选结果 (128条)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="exportScope" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">全部数据 (2,847条)</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">导出格式</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="excel">Excel (.xlsx)</option>
                            <option value="csv">CSV (.csv)</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">包含字段</label>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">成果名称</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">成果类型</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">所属专家</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">完成时间</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">所属领域</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">获奖情况</span>
                            </label>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('exportModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            导出
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openLinkModal() {
            document.getElementById('linkModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function openExportModal() {
            document.getElementById('exportModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function removeResult(id) {
            if (confirm('确定要移除这条科研成果吗？')) {
                alert('科研成果已移除 (原型演示)');
            }
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(event) {
            const modals = ['detailModal', 'editModal', 'linkModal', 'exportModal'];
            modals.forEach(modalId => {
                const modal = document.getElementById(modalId);
                if (modal && !modal.classList.contains('hidden') && event.target === modal) {
                    closeModal(modalId);
                }
            });
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modals = ['detailModal', 'editModal', 'linkModal', 'exportModal'];
                modals.forEach(modalId => {
                    const modal = document.getElementById(modalId);
                    if (modal && !modal.classList.contains('hidden')) {
                        closeModal(modalId);
                    }
                });
            }
        });

        // 表单提交处理
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('表单已提交 (原型演示)');
                const modalId = this.closest('[id$="Modal"]').id;
                closeModal(modalId);
            });
        });
    </script>
</body>
</html>