<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专项征集表单 - 科技表单信息系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .form-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid #E5E9EF;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-bullhorn text-purple-600"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">专项征集表单</h1>
                        <p class="text-sm text-gray-600">科创甬江2035、自然基金等专项需求信息采集</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm">
                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span class="text-gray-600">专项征集模块</span>
                    </div>
                    <button onclick="window.open('index1.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回首页
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-5xl mx-auto px-6 py-8">
        <!-- Tab导航 -->
        <div class="bg-white rounded-xl card-shadow mb-6">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button class="tab-btn active py-4 px-1 border-b-2 border-purple-500 font-medium text-sm text-purple-600" data-tab="basic">
                        <i class="fas fa-info-circle mr-2"></i>
                        基础信息
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="details">
                        <i class="fas fa-cogs mr-2"></i>
                        技术详情
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="outcomes">
                        <i class="fas fa-trophy mr-2"></i>
                        预期成果
                    </button>
                    <button class="tab-btn py-4 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700" data-tab="guide">
                        <i class="fas fa-book mr-2"></i>
                        指南配置
                    </button>
                </nav>
            </div>
        </div>

        <!-- 表单内容 -->
        <form class="space-y-6">
            <!-- 基础信息标签页 -->
            <div id="tab-basic" class="tab-content">
                <!-- 专项类别 -->
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-tag text-purple-500 mr-2"></i>
                        专项类别
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                专项类别 <span class="text-red-500">*</span>
                            </label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">科创甬江2035</span>
                                    <span class="ml-2 px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">重点</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">国家自然科学基金</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">省重点研发计划</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">市科技创新专项</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">其他专项</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                专项状态
                            </label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">征集状态</span>
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">进行中</span>
                                </div>
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm text-gray-600">征集截止</span>
                                    <span class="text-xs text-gray-500">2024-03-30</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-600">已征集数量</span>
                                    <span class="text-sm font-semibold text-purple-600">156个</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 研发情况 -->
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-flask text-blue-500 mr-2"></i>
                        研发情况
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                是否处于研发过程 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="in_research" value="yes" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">是</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="in_research" value="no" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">否</span>
                                </label>
                            </div>
                        </div>

                        <div id="project-details" class="hidden">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        项目编号
                                    </label>
                                    <input type="text" placeholder="请输入项目编号" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        研发投入（万元）
                                    </label>
                                    <div class="relative">
                                        <input type="number" placeholder="0.00" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500">
                                            <span class="text-sm">万元</span>
                                        </div>
                                    </div>
                                    <p class="mt-1 text-xs text-gray-500">系统对接提取</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                拟解决的问题 <span class="text-red-500">*</span>
                            </label>
                            <textarea rows="4" placeholder="请详细描述需要解决的技术问题..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>
                    </div>
                </div>

                <!-- 适用性评估 -->
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                        适用性评估
                    </h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                是否适合科技型中小企业牵头
                            </label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="suitable_sme" value="yes" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">是</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="suitable_sme" value="no" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">否</span>
                                </label>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                是否适合列入其他项目类型
                            </label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">关键技术攻关项目</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">应用示范项目</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">产业化项目</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术详情标签页 -->
            <div id="tab-details" class="tab-content hidden">
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-cogs text-orange-500 mr-2"></i>
                        技术详情
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                攻关背景与意义 <span class="text-red-500">*</span>
                            </label>
                            <textarea rows="4" placeholder="请描述技术攻关的背景意义..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                关键制约因素 <span class="text-red-500">*</span>
                            </label>
                            <textarea rows="4" placeholder="请描述技术发展的关键制约因素..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                建设基础 <span class="text-red-500">*</span>
                            </label>
                            <textarea rows="4" placeholder="请描述现有的技术基础和条件..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                协同要求 <span class="text-red-500">*</span>
                            </label>
                            <textarea rows="4" placeholder="请描述产学研协同的具体要求..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                核心技术参数
                            </label>
                            <div class="space-y-3" id="tech-params">
                                <div class="flex items-center space-x-3">
                                    <input type="text" placeholder="参数名称" 
                                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                    <input type="text" placeholder="目标值" 
                                           class="w-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                    <input type="text" placeholder="单位" 
                                           class="w-20 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                                    <button type="button" class="p-3 text-red-500 hover:text-red-700">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" id="add-tech-param" 
                                    class="mt-3 flex items-center px-4 py-2 text-purple-600 border border-purple-300 rounded-lg hover:bg-purple-50 transition-colors">
                                <i class="fas fa-plus mr-2"></i>
                                添加技术参数
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 预期成果标签页 -->
            <div id="tab-outcomes" class="tab-content hidden">
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                        预期成果
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                预期成果类型 <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-2 lg:grid-cols-3 gap-3">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">进口替代</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">新兴产业</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">技术突破</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">产业升级</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">标准制定</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                                    <span class="ml-2 text-sm text-gray-700">平台建设</span>
                                </label>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    社会经济效益
                                </label>
                                <textarea rows="4" placeholder="请描述预期的社会经济效益..." 
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    转化意向区域
                                </label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">宁波市内</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">浙江省内</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">长三角地区</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                                        <span class="ml-2 text-sm text-gray-700">全国范围</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                标志性成果与水平
                            </label>
                            <textarea rows="3" placeholder="请描述标志性成果及其达到的技术水平..." 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 指南配置标签页 -->
            <div id="tab-guide" class="tab-content hidden">
                <div class="form-section rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                        <i class="fas fa-book text-teal-500 mr-2"></i>
                        指南配置
                    </h3>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                是否凝练成指南 <span class="text-red-500">*</span>
                            </label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="create_guide" value="yes" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">是</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="create_guide" value="no" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">否</span>
                                </label>
                            </div>
                        </div>

                        <div id="guide-details" class="hidden">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        指南编号
                                    </label>
                                    <input type="text" placeholder="请输入指南编号" 
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        指南类型
                                    </label>
                                    <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                                        <option value="">请选择指南类型</option>
                                        <option value="技术攻关">技术攻关类</option>
                                        <option value="应用示范">应用示范类</option>
                                        <option value="产业化">产业化类</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="dispatch-details" class="hidden">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    自动派单原因
                                </label>
                                <textarea rows="3" placeholder="请说明为什么不适合凝练成指南..." 
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"></textarea>
                            </div>
                            <div class="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-info-circle text-orange-500 mr-2"></i>
                                    <span class="text-sm text-orange-800 font-medium">系统将自动派单处理</span>
                                </div>
                                <p class="text-sm text-orange-700 mt-1">该需求将自动分配给相关部门进行后续处理和跟进。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单操作按钮 -->
            <div class="flex items-center justify-between pt-6">
                <div class="flex items-center space-x-4">
                    <button type="button" class="flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存草稿
                    </button>
                    <button type="button" class="flex items-center px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-eye mr-2"></i>
                        预览表单
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        重置表单
                    </button>
                    <button type="submit" class="px-8 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                        提交专项申请
                    </button>
                </div>
            </div>
        </form>
    </main>

    <script>
        // Tab切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 更新tab按钮状态
                    tabBtns.forEach(b => {
                        b.classList.remove('active', 'border-purple-500', 'text-purple-600');
                        b.classList.add('border-transparent', 'text-gray-500');
                    });
                    this.classList.add('active', 'border-purple-500', 'text-purple-600');
                    this.classList.remove('border-transparent', 'text-gray-500');

                    // 更新tab内容显示
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    document.getElementById('tab-' + targetTab).classList.remove('hidden');
                });
            });

            // 研发过程选择联动
            const researchRadios = document.querySelectorAll('input[name="in_research"]');
            const projectDetails = document.getElementById('project-details');

            researchRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'yes') {
                        projectDetails.classList.remove('hidden');
                    } else {
                        projectDetails.classList.add('hidden');
                    }
                });
            });

            // 指南配置联动
            const guideRadios = document.querySelectorAll('input[name="create_guide"]');
            const guideDetails = document.getElementById('guide-details');
            const dispatchDetails = document.getElementById('dispatch-details');

            guideRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'yes') {
                        guideDetails.classList.remove('hidden');
                        dispatchDetails.classList.add('hidden');
                    } else {
                        guideDetails.classList.add('hidden');
                        dispatchDetails.classList.remove('hidden');
                    }
                });
            });

            // 添加技术参数
            document.getElementById('add-tech-param').addEventListener('click', function() {
                const container = document.getElementById('tech-params');
                const newParam = document.createElement('div');
                newParam.className = 'flex items-center space-x-3';
                newParam.innerHTML = `
                    <input type="text" placeholder="参数名称" 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <input type="text" placeholder="目标值" 
                           class="w-32 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <input type="text" placeholder="单位" 
                           class="w-20 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <button type="button" class="p-3 text-red-500 hover:text-red-700 remove-param">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(newParam);

                // 添加删除功能
                newParam.querySelector('.remove-param').addEventListener('click', function() {
                    newParam.remove();
                });
            });
        });
    </script>
</body>
</html> 