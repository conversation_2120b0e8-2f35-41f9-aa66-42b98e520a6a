object CC_CjgjFrame: TCC_CjgjFrame
  Left = 0
  Top = 0
  Width = 1392
  Height = 659
  Color = clWhite
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentBackground = False
  ParentColor = False
  ParentFont = False
  TabOrder = 0
  object MainPanel: TRzPanel
    Left = 0
    Top = 62
    Width = 1392
    Height = 412
    Align = alClient
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 0
    object GridPanel: TRzPanel
      Left = 0
      Top = 51
      Width = 1392
      Height = 361
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 0
      object GjAdvStringGrid: TAdvStringGrid
        Left = 0
        Top = 0
        Width = 1242
        Height = 361
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        BorderStyle = bsNone
        ColCount = 20
        Ctl3D = True
        DefaultRowHeight = 24
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedCols = 12
        FixedRows = 2
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goColMoving, goEditing]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        PopupMenu = AdvPopupMenu2
        ScrollBars = ssBoth
        TabOrder = 0
        OnColumnMoved = GjAdvStringGridColumnMoved
        OnFixedCellClick = GjAdvStringGridFixedCellClick
        OnKeyDown = GjAdvStringGridKeyDown
        OnMouseDown = GjAdvStringGridMouseDown
        OnSelectCell = GjAdvStringGridSelectCell
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        OnDblClickCell = GjAdvStringGridDblClickCell
        OnAnchorClick = GjAdvStringGridAnchorClick
        OnCellValidate = GjAdvStringGridCellValidate
        OnGetFloatFormat = GjAdvStringGridGetFloatFormat
        OnEditChange = GjAdvStringGridEditChange
        HighlightColor = clNone
        OnColumnMove = GjAdvStringGridColumnMove
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #24494#36719#38597#40657
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.ColumnWidth = True
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -12
        FilterDropDown.Font.Name = #24494#36719#38597#40657
        FilterDropDown.Font.Style = []
        FilterDropDown.GlyphActive.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          08000000000000010000530B0000530B00000001000000010000104A10001063
          100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
          63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
          8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
          1414020214141414141414141414141414030902141414141414141414141414
          030D090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141403
          130D09000214141414141414141403130D0D0501000214141414141414031311
          0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
          0806090909040100021403030303030303030303030303030303141414141414
          1414141414141414141414141414141414141414141414141414}
        FilterDropDown.Height = 200
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDown.Width = 200
        FilterDropDownClear = #20840#37096
        FilterDropDownCheck = True
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedFooters = 1
        FixedColWidth = 35
        FixedRowHeight = 24
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = []
        FloatFormat = '%.2f'
        FloatingFooter.Visible = True
        GridImages = PngImageList1
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HoverFixedCells = hfFixedRows
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        ScrollProportional = True
        ScrollSynch = True
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        SortSettings.DefaultFormat = ssAutomatic
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        ExplicitLeft = -6
        ExplicitTop = 5
        ColWidths = (
          35
          51
          61
          59
          51
          54
          50
          51
          58
          54
          64
          64
          64
          64
          64
          64
          64
          64
          64
          64)
      end
      object NextPagePanel: TRzPanel
        Left = 1242
        Top = 0
        Width = 150
        Height = 361
        Align = alRight
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        object Btn_Left: TAdvGlowButton
          Left = 0
          Top = 226
          Width = 40
          Height = 40
          Hint = #24050#21040#36798#31532#19968#39029
          BorderStyle = bsNone
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          HotPicture.Data = {
            89504E470D0A1A0A0000000D494844520000001C0000001C0806000000720DDF
            94000004C64944415448C78D964B6C546514C77FDFBD77E6B6F37E1405ACC502
            2DA5A5010C0B562626C4981848346AD24413497C44515C94854BA22B1361615C
            4974E74AE20A8D21415134260D814080F02A8582B460DB69A79DB93377E6CE71
            3197B98F99A27731F7CE77BEEF3CFEE77CFF7314DF5E3570B4DD08E3C073400E
            A54084C01B01FCFFBDA5D6DBFF78B205E0344A8EA037260C1C6D17A2BE43D1D7
            DC28EEEEF061D718AB28EEF4346539E01544EDC2D1C634603C604C29CF40E0A4
            BBE65F5721637E87C232A40F615C43782160CCEFB2F80E0716DCCF70C4EA3F65
            7B3420D5F23E7C5085230D2968E552FEAF2CA5B57223E206A0BC40C4FD11774D
            9A083C9330886A8F22691E5140DED459DBAD37FD157FE1B83A50682D28957B0A
            21704294970FF7F3C5A763EC591F6B06A19A4E6A0A76F598BCD69FE0C998EECB
            A14FB702A3E315101F36CA4BDD9A6E9DFD83490E0CA7A989F0C9C43CC7A74AC4
            0DC54B7D71C6B76518CA44D8963379EFCCC35041355F5AE70A135FC4CDCF5C97
            C6D8A604EF6F4DD3D3A573BB5807147143B1E7A9188746336CCF9BCC551ACC57
            9CA60E7F6E5A11B6B2ECAB525101EFD6C574DED89C64FF608A753183DF672C0E
            4DCC315771D8B721CE075BD38C664D268B353E3BBFC06F3356B064C5AB7EC333
            A4C277A0096397C6ABFD09DE194AB3A64BE7D7FB165F5E5E64B258E3F9F5DD1C
            184EB3336F7265D1E6D8B5254EDC2DB162FBD10AEA355A55A9DAA9236F6ABCBD
            25CD9B9B93F4C6747E99B1F8FC4281897FAABCBC21CE87236976E44D6E166B1C
            BD54E0C7E9322B35E9E4B73FC2B0508112B2519DD7372678772845D6D43975DF
            E2AB2B4B9C9BABB2331FE5E36D194673512E156CBEBEBAC44F77CB2CDA0D9F31
            F1184BF923EC48BC8AA3BB7BD8DB172711D13875BFCCA7E717B8306FF3D66092
            8F46D20CA4A24C2ED7F8E2628113D3652C474214AC3CA33E235AEB62862A75D6
            AA536F08110DD676EB64A21A02CC941DACBAA06B9035757AE391D5C9BB75FFBC
            0D3AFB0E1EF6A0F404E7E6AB541C614B3A4A7F32C2BA98C174A9CEE9198BEBC5
            1A3BF2267D7183BE84C1A2DDE0F6729D6A2344DE226DE96A1A542ACCEC541CB8
            58B0A937603013617BDEA4371EE1CE4A9D3F1F58DC2BD519484719C99A0CA523
            14EC06532BAED1166A6E10E28F70EFC1C36D2DC6651DBB01D7976C6A0D184847
            D89137E98D1BDC5AAE7166B6C28CE5B03169309A33D992691ABD59ACF1A87602
            B97421F641EAF7C8A338CB11265D25839908DB73269B5311CE2F5439375765B6
            ECD09F3418CD9A0C67A314AA8F22F52ADE0F9F17A1F8C81B15E081725DB8B164
            E3088C64A38C644C36A5A25C59ACF1C7830A0F2D87817484E16C949D3D269623
            4C3CAC06D9CBE536CFA00A35C350E5598E70B96023C0B33D5DA44D8DC9629DBF
            1E58DC5AAE316B398CE69A90DB8EF0FD54A9437305C3ABA6F08024C16E2150B4
            1B7C73ADC8135D3AD3A53A3FDF2B8152D80D38F57799B5DD3A5B33517EB8536A
            A7B516011DBB2101E56DD398AF312B3F1977287D0921D441E66BC074189A681F
            A8C4D76EC4CB8D57FA8F97694031486B121C095B50D379720BD0178F97415143
            71B26D140C747EF172203EA75A91874AFF7132C5490D3882301D802480AF2234
            5585C6C8F0F8BDAA6C1A38A2A137CEA2640CE43822736DF74158B539D3615CED
            209C038EA3640CBD71F65F2AA229673DAD7B570000000049454E44AE426082}
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000036D4944415448C79595CF6F146518C73FCF3BB3B3B3DDB2DD5DB72C14
            8310B54291B6601B31D604A38927881A4FC678E36054387841305A13B5FA0790
            686AA20783170F1E38E8C11F898683B674AB010A4D3512401A76B7DB5DDB6566
            BAF37AD8A63BDB4ED9E5B9CC37F3E379DEEF679EF77985CFAF24F0E555E01822
            FD044303B24E87DDDBA873A0C751FA2B132D2F837C889040EB40122134A42D3D
            88660C2DBE09BC8E90A83F90E697374BD04E882480374D347DF7F4B10E146CA5
            D17B1422AADDDCE9A8C250CD0E63A690B454FDDE7AC722AA9E5CEBD68E81C3DB
            630C65A26B5604783011E1F907E2A4A3AA61511A76CD26F6A13C844E5378E5A1
            2DBCD59FA2E2D578E37C9EF3F37778326BF3EEC1348F774779B8CBE2F4446183
            0D850E3AD01B92470DE1855D9D9C78344926AAF8F166958AEBB32719E19D0369
            46B23617173CFE2C3AA14B54ABAC42DB4680A33BE38C3E9666F79608E7AE2DF3
            516E012530369CE1F0F61857173D4E4F16F8F69FA5D002E666CC1311C54BBB3B
            3939902211518CE58A7C3A53666FD2E2E4408A677B627C777D99F72F14F8A3E8
            52DBE4379A752A7AD5451D4BCC105EDC15E7ED8114695BF1E5D5325FCC56E830
            85F70EA639B4D566AAE0F0F174915CC1E56E2D62D6A934233AB12FC96B7D09EE
            8B1A7C3653E693E912BD5D114E0DA618C9DAFC76DBE1D44481DF6F3BB4EA3F15
            D8146B97A25B03EA3DBEA3C3C007165D9F6A4D632A615B8741A7A9F075C8065C
            A70D8E1E1F5D5BFDEAC0BA5C72293A3E7B9316C3DD3686083FDDACF2CBFC1DF6
            A52CF6A72CFA521617F20EF3D55A3D9F04265E40070A344879BE66A6E451727D
            86323623591B43093FDC586632EFD0DB6571286BD3DB15E162C9E5D6726DD3E1
            6570E4F86853F5D549BAA261BAE0325759E1E99E18CFF474B03566F0CDDFFFF1
            F3BF5576C40D9EBB3FCE7026CA5CC5E3FAD24A0359607487220AC6ECA247C5D3
            F4A72D9EDAD68152C2AFB7EAB8F6A72D86BA6DFAD31673158FBF2A5E7B88D657
            BAB2E852F634C3DD363B3B4D26F30E137987CB0B2E8F242D0E64A22CBA3EDFDF
            A86E4064367235230AC6F28AE6EBB90A9682BCE3932BD4C7C254C1E183A9224F
            646DCE5D5B0A3D0985F1594DF8C25B1C0AD2965668ED87236A35BCDBD05AFB0A
            E1D2FA8D76571BFA1EB470490167D09437EBA2F0C5EBD65A5306CE98883E8B46
            A13986C8607B3FA30522AD73C038A2CFFE0FA3DE4D1E285A19D6000000004945
            4E44AE426082}
          Transparent = True
          TabOrder = 0
          OnClick = Btn_LeftClick
          Appearance.BorderColor = 16316405
          Appearance.BorderColorHot = 16316405
          Appearance.BorderColorDown = 16316405
          Appearance.BorderColorFocused = 16316405
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Right: TAdvGlowButton
          Left = 36
          Top = 226
          Width = 40
          Height = 40
          Hint = #24050#21040#36798#26368#21518#19968#39029
          BorderStyle = bsNone
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          HotPicture.Data = {
            89504E470D0A1A0A0000000D494844520000001C0000001C0806000000720DDF
            94000004C14944415448C78D96DB6F14551CC73F677666B7BBDBEEA54060B994
            9BB52D2DD64443424C447C204A020FC4048DCF1A135212E40F90378D91F86204
            C3830F6A30468D4F3E18BCBD1824287231855AA8209452DA5DF6D2DD9D999D39
            3ECC74AEAD755ECECEFCF69CEFEFF2FD7DCF4F70664205B10B38063C8794AB11
            02A424B42281E0BBFFC95B838F67937308F113C8F7415C50413C0D9C05FAC2FF
            8E6E76C158E6E0A51E67DF6AE02537A8C30A70DC03F3A2C28F22E88010E1EF22
            021674286EEB038E2BC0BE1058D06519D81C8D5C128F58AC68DBA72065CEF33E
            BA5144238D1CE0D552FE3F9B24A7866B2302295D0C56820C78107A0F00C8658A
            19B1A921D62DE64308F694D21CDA9C65FC91C1679375EA1DC7A9AD3D1A07FAB2
            F46555DEBE5C615EB75D4722B51441277D93BA640B4838329463FFA62CB7EA26
            332D8B6FFF59C0B0E0858D59DEDC59209350986D5B9C1EAF5133ED4889459CBD
            AE2DC1C1A327C22970D2B62EA3F2446F8A6D398DFEBCC68396C5ED4607809DC5
            1443C5248385241D0913558396257D660AB7147E4EDD733D40E183E1D4E846D5
            64B66531544C325448B2B54765B665F1EBC33697CA3A83F924438514FD790DD3
            869B359366470622133E98F4D99FE0E0D889707F39AB6E4B26EB2615C366A0A0
            B1B398624B8FCA74D3E2FCACCE44CDA4AF5B65B4B78BFEBC8661072295918C05
            CE752294D15E73C2376C98AA77A8E83603798D91DE14FD398DE966879FEFB7B8
            DDB0D89055195D95E2F18286694BAE554C8C607A2392E4008A088F85F4D446B7
            2453F50E65C36630AF315C4C31D29BE246D5E497076D6E373A6CCCA88CF62619
            2EA69012C6AB066DCBF6154B10A8E18128204ECE17FBD14DEF44CD2421044FAD
            E9626B8FCA4831C9BDA6C58FF75BCCB43A8CAE4A315048F2646F8AAC26F8FE5E
            3B4C1A1744F11B33AA0A32265777174C2ABA8565435A1594320904F0C8B09969
            599836A88A605D5A0D303EAC566AEC460816DBC52CA6140E6DC9F2FA409ECDDD
            2A97E6758E9D7FC8D5B2C9DEF5698EECC8F3CCDA2EEE373BBC7BA5C2E7371BBE
            C244CAA5C67434429C425261FFA60C47870B3C96D3F8635EE7833FAB5C9A37D8
            B526C5D8709EE74B19A6EA269F4CD6F9E256838A6187A54E7A8A8E1A57045F75
            BA930A2F6FEFE6B581BC07F6CEE50A3F4CB7D85B4A737438CFEEB569EE364D4E
            5FAF72F666C395BA80E35E0F3A81A9F17670C04A19953DA534633B0A6CEBD1B8
            38D7E6C3F12ABFCDE96CCF691C1B29F06C29CD54DDE4E3891A5F4E3578D8B6FC
            C8BC4B20002A8311CA4591753E8C0D1738BCAD9B0D59952B659DF7AE3EE2DCBD
            262F6ECAF2C6608EDD6ECD4E8D57F974B24E4577DB2096C6304BD5D81DE8FE58
            D595607597C2E5799D93D71CB005D3A9C3969CCA5CDBE2D4B893C672DB5E7ED6
            885C95AACFA6F080F4D6EFF35C2BEB4C540DCE4DB7B0DCD27C35B540D394F4F5
            A87C345E0B042562878764CDEBB2337F493FEFA12B91B5E904A62D29EB768074
            92A422589F51F9BBD109533F98D2685BC815DA4222986959C42E680986257DB0
            25A8EF4F11119B0005A885B1647824F452CDD2931B3200C67FDB90350538171B
            0583E3A2370007F7C98032B9628F58D906DF29C049E08E3F1845D9268248614A
            2F49FD656C42DC017152017901295F41F035C8728CDA323A37B222F523B632F0
            0DF02A70F15FCBC251CD8AD9F3660000000049454E44AE426082}
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D4948445200000018000000180806000000E0773D
            F80000036B4944415448C79596416C1B551086BFB7BBDEB5E3D88ED7360D2914
            A928A14D68EAA890022A87168913545CE080102A870A894839A785430EA8E580
            842AC4855C5124A4AA485C38C0A1A0822AD2404005B5B24AA296D44D63277662
            AFBDF67A5F0F7662C7B16B67A4D5FE5ABD37F3E69FF9DFAC60261104DE03CE02
            711A4D02A209B7FAD68C61019801BED18077818B4010291B9C085A9AE802431C
            292F2284AB011340B0BA48EC5CDCDE41671322084C28C0F09E36CADAD31D1E56
            0085BD9AE81A2B55E752EED83F6AEA9C8F8739F9A40FADB6296228BC73B0978F
            8683F8B55AA144435A6DB0B683FB9A9D190CF0E1E110D71FDA4CCDA5985BB589
            470C3E8987E9D3554A2ECCDED9C47264C73454DE9C9CAE46ADF7A00B8C9A062F
            C4BC3C17D2B9FEB048D272081B2AAF0DF470C4D4C996240B69BB239B2AA727A7
            EB1954DFF7F20E898D32A3A6CEF8135EF6FB55AEAD14F969D922E65339D1DFC3
            B1A8C19D4D8744B6DC45802673252C6D3ACCA76D06831E5E7FCACF4858673E65
            73793187AA084EECF37272C047B6EC723B53C691ED02B4A068AB402B56859BEB
            258E450DC6630643219DABC902BF240BA88AE0D4808F51D360A324B99D2D5176
            BBA4680B4BE041A1C2DD9CC32BFD5EC622064FF76AFCBE6AF3E3728167031E5E
            EDF7311635C8965CE653BB6BA26C8B630B34A5AA08C8392E0FAC0A8E2BF1A982
            905EEDEE65AB42A1C68DA9AB3BC55833AD7E68D17C6121801763069F8F4778DE
            34F875A5C8C46FAB54244C1D0D736628C07DCBE1C2C23ADFFE97AB532CEAB79F
            D25AA11255403CA23375D4642C6AF073B2C0F41F6B588EE483C120EF0F064817
            5D3EFB2BC395A53C858A6CA907AD7E6AB99D85575378799F972F8E4739D4A7F3
            C3FF16D3F369EEE51D2EBD14E3ED83BDDCB71C3EBE91E6F262AECA489BABBB25
            456F3DE36772A48FA19087ABC902E7E6527884E07C3CCC1B077A58DC2C736161
            9DEFEFE6912D68E948D111D36024ECE1DA4A914FFF5CE356A64C40573835E023
            65BB5CBA99E1CA520EBB0D2D8D58F075423653647A154E1FF0732365F3CF7A69
            BB298EC70CF6FB35BE5BCA3F9696462C9849C85623B24F57B02BB256BC9A6804
            84748535BBD2C5DCAC620D70B767424317654ABB655991B066BB7B1908AE02DC
            DA2D34F1F889D63DFE5741F225B0D14A68EDA799EC02B381E42B0DC16C8DA2B3
            88ADDF964E913A52F437C81904B38F0065745BDA17FEFACE0000000049454E44
            AE426082}
          Transparent = True
          TabOrder = 1
          OnClick = Btn_RightClick
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
      end
    end
    object ButtonPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1392
      Height = 51
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdLeft, sdTop, sdRight]
      BorderColor = 14671839
      BorderWidth = 1
      Color = 16049103
      DoubleBuffered = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 1
      VisualStyle = vsGradient
      object ButtonLeftPanel: TRzPanel
        Left = 1
        Top = 1
        Width = 600
        Height = 49
        Align = alLeft
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
        object LeftWhitePanel: TRzPanel
          Left = 0
          Top = 37
          Width = 600
          Height = 12
          Align = alBottom
          BorderOuter = fsNone
          BorderSides = [sdTop]
          BorderColor = clGradientActiveCaption
          BorderWidth = 1
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 0
        end
        object Btn_Import: TAdvGlowButton
          Left = 21
          Top = 5
          Width = 108
          Height = 33
          BorderStyle = bsNone
          Caption = #21516#27454' '#22797#21046
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
            610000015A49444154388D95D3BD2F835114C7F1EFB97DD5817A998CD24DA4B4
            163188C9DC184C5D6DD2887829834D424552420C221161F44FD45C0C76AB3034
            254F5BDA7B0C45A4799E2775B69BDF399F9C7B932B002CA643F4DB2DDC4A09D1
            B0058A0F15D71C807C7A9AB5D49C6B964BC6599D3821978CBBC506004B089186
            2B507CA8F06137099B1D37C478AFD51DD21DE083740F7422DB33D1FF033F4840
            6FA8BF4F02043D1B57C767205626D6149C669242B9F49B69A0F933EB0D604691
            5A869A58C43E0225D72ECFF958F914610A21C5DEFD79477A1721F8E4BF416D6C
            18913010253F3E44B8EF0DA77A80C800B4A4615B2F2C2556BC01091EA16C0083
            B4E418A7FA0A3A8B6119048C1C12EBDDF70676EF327F4ED7AC4F54B1E69948FD
            16272EE00430647D1EB1A3541523096AD133A41E44CC0850693FA2E113D5883F
            C065FB6A2C20CC7FA31702F87F670015C5E8152A39B059308AEA053D8DB52F12
            F37CBA216C7F410000000049454E44AE426082}
          Transparent = True
          TabOrder = 1
          OnClick = Btn_ImportClick
          Appearance.Color = clSilver
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 16316405
          Appearance.ColorDownTo = 16316405
          Appearance.ColorHot = 16316405
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 16316405
          Appearance.ColorMirrorDownTo = 16316405
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
      end
      object TabPanel: TRzPanel
        Left = 601
        Top = 1
        Width = 790
        Height = 49
        Align = alClient
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        Transparent = True
        object FlToolbar: TRzToolbar
          Left = 0
          Top = 0
          Width = 790
          Height = 37
          Align = alClient
          AutoStyle = False
          RowHeight = 40
          ButtonLayout = blGlyphTop
          ButtonWidth = 60
          ButtonHeight = 40
          ShowButtonCaptions = True
          TextOptions = ttoShowTextLabels
          WrapControls = False
          BorderInner = fsNone
          BorderOuter = fsNone
          BorderSides = [sdTop]
          BorderWidth = 0
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          GradientColorStop = 14215660
          ParentFont = False
          TabOrder = 0
          Transparent = True
          VisualStyle = vsGradient
          ToolbarControls = (
            B_Tab1
            B_Space1
            B_Tab2
            B_Space2
            B_Tab3
            B_Space3
            B_Tab4
            B_Space4
            B_Tab5
            B_Space5
            B_Tab6
            B_Space6
            B_Tab7
            B_Space7
            B_Tab8
            B_Space8
            B_Tab9
            B_Space9)
          object B_Space1: TRzSpacer
            Left = 79
            Top = 10
            Width = 5
          end
          object B_Space2: TRzSpacer
            Left = 159
            Top = 10
            Width = 5
          end
          object B_Space3: TRzSpacer
            Left = 239
            Top = 10
            Width = 5
          end
          object B_Space4: TRzSpacer
            Left = 319
            Top = 10
            Width = 5
          end
          object B_Space5: TRzSpacer
            Left = 399
            Top = 10
            Width = 5
          end
          object B_Space6: TRzSpacer
            Left = 479
            Top = 10
            Width = 5
          end
          object B_Space7: TRzSpacer
            Left = 559
            Top = 10
            Width = 5
          end
          object B_Space8: TRzSpacer
            Left = 639
            Top = 10
            Width = 5
          end
          object B_Space9: TRzSpacer
            Left = 719
            Top = 10
            Width = 5
          end
          object B_Tab1: TAdvGlowButton
            Left = 4
            Top = 6
            Width = 75
            Height = 33
            Action = actFl1
            Caption = '1'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ImageIndex = 44
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 0
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab2: TAdvGlowButton
            Left = 84
            Top = 6
            Width = 75
            Height = 33
            Action = actFl2
            Caption = '2'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ImageIndex = 14
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 1
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab3: TAdvGlowButton
            Left = 164
            Top = 6
            Width = 75
            Height = 33
            Action = actFl3
            Caption = '3'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ImageIndex = 0
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 2
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab4: TAdvGlowButton
            Left = 244
            Top = 6
            Width = 75
            Height = 33
            Action = actFl4
            Caption = '4'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ImageIndex = 30
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 3
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab5: TAdvGlowButton
            Left = 324
            Top = 6
            Width = 75
            Height = 33
            Action = actFl5
            Caption = '5'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ImageIndex = 38
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 5
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab6: TAdvGlowButton
            Left = 404
            Top = 6
            Width = 75
            Height = 33
            Action = actFl6
            Caption = '6'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 4
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab7: TAdvGlowButton
            Left = 484
            Top = 6
            Width = 75
            Height = 33
            Action = actFl7
            Caption = '7'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 6
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab8: TAdvGlowButton
            Left = 564
            Top = 6
            Width = 75
            Height = 33
            Action = actFl8
            Caption = '8'
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 7
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
          object B_Tab9: TAdvGlowButton
            Left = 644
            Top = 6
            Width = 75
            Height = 33
            Action = actFl8
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clBlack
            Font.Height = -13
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            NotesFont.Charset = DEFAULT_CHARSET
            NotesFont.Color = clWindowText
            NotesFont.Height = -11
            NotesFont.Name = 'Tahoma'
            NotesFont.Style = []
            ParentFont = False
            Spacing = 10
            TabOrder = 8
            OnClick = B_Tab9Click
            Appearance.BorderColorHot = 10079963
            Appearance.BorderColorDown = 4548219
            Appearance.ColorChecked = 7915518
            Appearance.ColorCheckedTo = 11918331
            Appearance.ColorDisabled = clBtnFace
            Appearance.ColorDisabledTo = clBtnFace
            Appearance.ColorDown = 12631218
            Appearance.ColorDownTo = 12631218
            Appearance.ColorHot = clWhite
            Appearance.ColorHotTo = 16316405
            Appearance.ColorMirror = clGradientActiveCaption
            Appearance.ColorMirrorHot = 16316405
            Appearance.ColorMirrorHotTo = 16316405
            Appearance.ColorMirrorDown = 12631218
            Appearance.ColorMirrorDownTo = 12631218
            Appearance.ColorMirrorChecked = 10480637
            Appearance.ColorMirrorCheckedTo = 5682430
            Appearance.ColorMirrorDisabled = clBtnFace
            Appearance.ColorMirrorDisabledTo = clBtnFace
            Appearance.GradientHot = ggVertical
            Appearance.GradientMirrorHot = ggVertical
            Appearance.GradientDown = ggVertical
            Appearance.GradientMirrorDown = ggVertical
            Appearance.GradientChecked = ggVertical
            Appearance.SystemFont = False
          end
        end
        object RightWhitePanel: TRzPanel
          Left = 0
          Top = 37
          Width = 790
          Height = 12
          Align = alBottom
          BorderOuter = fsNone
          BorderSides = [sdTop]
          BorderColor = clGradientActiveCaption
          BorderWidth = 1
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -11
          Font.Name = 'Tahoma'
          Font.Style = []
          ParentFont = False
          TabOrder = 1
        end
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 1392
    Height = 62
    Align = alTop
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    GradientColorStyle = gcsCustom
    ParentFont = False
    TabOrder = 1
    VisualStyle = vsGradient
    object BluePanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1392
      Height = 15
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdLeft, sdTop, sdRight]
      BorderColor = 14671839
      BorderWidth = 1
      Color = 16049103
      DoubleBuffered = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 0
      VisualStyle = vsGradient
    end
    object TopMainPanel: TRzPanel
      Left = 0
      Top = 15
      Width = 1392
      Height = 47
      Align = alClient
      BorderOuter = fsNone
      BorderColor = 14671839
      BorderWidth = 1
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      object RzSeparator1: TRzSeparator
        Left = 607
        Top = 42
        Width = 720
        ShowGradient = False
        Color = clWhite
        ParentColor = False
        Visible = False
      end
      object Image1: TImage
        Left = 641
        Top = 38
        Width = 16
        Height = 13
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000000D0000
          000D080600000072EBE47C000000097048597300000B1300000B1301009A9C18
          00000A4D6943435050686F746F73686F70204943432070726F66696C65000078
          DA9D53775893F7163EDFF7650F5642D8F0B1976C81002223AC08C81059A21092
          006184101240C585880A561415119C4855C482D50A489D88E2A028B867418A88
          5A8B555C38EE1FDCA7B57D7AEFEDEDFBD7FBBCE79CE7FCCE79CF0F8011122691
          E6A26A003952853C3AD81F8F4F48C4C9BD80021548E0042010E6CBC26705C500
          00F00379787E74B03FFC01AF6F00020070D52E2412C7E1FF83BA502657002091
          00E02212E70B01905200C82E54C81400C81800B053B3640A009400006C797C42
          2200AA0D00ECF4493E0500D8A993DC1700D8A21CA908008D0100992847240240
          BB00605581522C02C0C200A0AC40222E04C0AE018059B632470280BD0500768E
          58900F4060008099422CCC0020380200431E13CD03204C03A030D2BFE0A95F70
          85B8480100C0CB95CD974BD23314B895D01A77F2F0E0E221E2C26CB142611729
          106609E4229C979B231348E7034CCE0C00001AF9D1C1FE383F90E7E6E4E1E666
          E76CEFF4C5A2FE6BF06F223E21F1DFFEBC8C020400104ECFEFDA5FE5E5D60370
          C701B075BF6BA95B00DA560068DFF95D33DB09A05A0AD07AF98B7938FC401E9E
          A150C83C1D1C0A0B0BED2562A1BD30E38B3EFF33E16FE08B7EF6FC401EFEDB7A
          F000719A4099ADC0A383FD71616E76AE528EE7CB0442316EF7E723FEC7857FFD
          8E29D1E234B15C2C158AF15889B850224DC779B952914421C995E212E97F32F1
          1F96FD0993770D00AC864FC04EB607B5CB6CC07EEE01028B0E58D27600407EF3
          2D8C1A0B91001067343279F7000093BFF98F402B0100CD97A4E30000BCE8185C
          A894174CC608000044A0812AB041070CC114ACC00E9CC11DBCC0170261064440
          0C24C03C104206E4801C0AA11896411954C03AD804B5B0031AA0119AE110B4C1
          31380DE7E0125C81EB70170660189EC218BC86090441C8081361213A8811628E
          D822CE0817998E04226148349280A420E988145122C5C872A402A9426A915D48
          23F22D7214398D5C40FA90DBC820328AFC8ABC47319481B25103D4027540B9A8
          1F1A8AC6A073D174340F5D8096A26BD11AB41E3D80B6A2A7D14BE87574007D8A
          8E6380D1310E668CD9615C8C87456089581A26C71663E55835568F35631D5837
          76151BC09E61EF0824028B8013EC085E8410C26C82909047584C5843A825EC23
          B412BA085709838431C2272293A84FB4257A12F9C478623AB1905846AC26EE21
          1E219E255E270E135F9348240EC992E44E0A21259032490B496B48DB482DA453
          A43ED210699C4C26EB906DC9DEE408B280AC209791B7900F904F92FBC9C3E4B7
          143AC588E24C09A22452A494124A35653FE504A59F324299A0AA51CDA99ED408
          AA883A9F5A496DA076502F5387A91334759A25CD9B1643CBA42DA3D5D09A6967
          69F7682FE974BA09DD831E4597D097D26BE807E9E7E983F4770C0D860D83C748
          6228196B197B19A718B7192F994CA605D39799C85430D7321B9967980F986F55
          582AF62A7C1591CA12953A9556957E95E7AA545573553FD579AA0B54AB550FAB
          5E567DA64655B350E3A909D416ABD5A91D55BBA936AECE5277528F50CF515FA3
          BE5FFD82FA630DB2868546A08648A35463B7C6198D2116C63265F15842D67256
          03EB2C6B984D625BB2F9EC4C7605FB1B762F7B4C534373AA66AC6691669DE671
          CD010EC6B1E0F039D99C4ACE21CE0DCE7B2D032D3F2DB1D66AAD66AD7EAD37DA
          7ADABEDA62ED72ED16EDEBDAEF75709D409D2C9DF53A6D3AF77509BA36BA51BA
          85BADB75CFEA3ED363EB79E909F5CAF50EE9DDD147F56DF4A3F517EAEFD6EFD1
          1F373034083690196C313863F0CC9063E86B9869B8D1F084E1A811CB68BA91C4
          68A3D149A327B826EE8767E33578173E66AC6F1C62AC34DE65DC6B3C61626932
          DBA4C4A4C5E4BE29CD946B9A66BAD1B4D374CCCCC82CDCACD8ACC9EC8E39D59C
          6B9E61BED9BCDBFC8D85A5459CC54A8B368BC796DA967CCB05964D96F7AC9856
          3E567956F556D7AC49D65CEB2CEB6DD6576C501B579B0C9B3A9BCBB6A8AD9BAD
          C4769B6DDF14E2148F29D229F5536EDA31ECFCEC0AEC9AEC06ED39F661F625F6
          6DF6CF1DCC1C121DD63B743B7C727475CC766C70BCEBA4E134C3A9C4A9C3E957
          671B67A1739DF33517A64B90CB1297769717536DA78AA76E9F7ACB95E51AEEBA
          D2B5D3F5A39BBB9BDCADD96DD4DDCC3DC57DABFB4D2E9B1BC95DC33DEF41F4F0
          F758E271CCE39DA79BA7C2F390E72F5E765E595EFBBD1E4FB39C269ED6306DC8
          DBC45BE0BDCB7B603A3E3D65FACEE9033EC63E029F7A9F87BEA6BE22DF3DBE23
          7ED67E997E07FC9EFB3BFACBFD8FF8BFE179F216F14E056001C101E501BD811A
          81B3036B031F049904A50735058D05BB062F0C3E15420C090D591F72936FC017
          F21BF96333DC672C9AD115CA089D155A1BFA30CC264C1ED6118E86CF08DF107E
          6FA6F94CE9CCB60888E0476C88B81F69199917F97D14292A32AA2EEA51B45374
          7174F72CD6ACE459FB67BD8EF18FA98CB93BDB6AB6727667AC6A6C526C63EC9B
          B880B8AAB8817887F845F1971274132409ED89E4C4D8C43D89E37302E76C9A33
          9CE49A54967463AEE5DCA2B917E6E9CECB9E773C593559907C3885981297B23F
          E5832042502F184FE5A76E4D1D13F2849B854F45BEA28DA251B1B7B84A3C92E6
          9D5695F638DD3B7D43FA68864F4675C633094F522B79911992B923F34D5644D6
          DEACCFD971D92D39949C949CA3520D6996B42BD730B728B74F662B2B930DE479
          E66DCA1B9387CAF7E423F973F3DB156C854CD1A3B452AE500E164C2FA82B785B
          185B78B848BD485AD433DF66FEEAF9230B82167CBD90B050B8B0B3D8B87859F1
          E022BF45BB16238B5317772E315D52BA647869F0D27DCB68CBB296FD50E25852
          55F26A79DCF28E5283D2A5A5432B82573495A994C9CB6EAEF45AB96315619564
          55EF6A97D55B567F2A17955FAC70ACA8AEF8B046B8E6E2574E5FD57CF5796DDA
          DADE4AB7CAEDEB48EBA4EB6EACF759BFAF4ABD6A41D5D086F00DAD1BF18DE51B
          5F6D4ADE74A17A6AF58ECDB4CDCACD03356135ED5BCCB6ACDBF2A136A3F67A9D
          7F5DCB56FDADABB7BED926DAD6BFDD777BF30E831D153BDEEF94ECBCB52B7857
          6BBD457DF56ED2EE82DD8F1A621BBABFE67EDDB847774FC59E8F7BA57B07F645
          EFEB6A746F6CDCAFBFBFB2096D52368D1E483A70E59B806FDA9BED9A77B5705A
          2A0EC241E5C127DFA67C7BE350E8A1CEC3DCC3CDDF997FB7F508EB48792BD23A
          BF75AC2DA36DA03DA1BDEFE88CA39D1D5E1D47BEB7FF7EEF31E36375C7358F57
          9EA09D283DF1F9E48293E3A764A79E9D4E3F3DD499DC79F74CFC996B5D515DBD
          6743CF9E3F1774EE4CB75FF7C9F3DEE78F5DF0BC70F422F762DB25B74BAD3DAE
          3D477E70FDE148AF5B6FEB65F7CBED573CAE74F44DEB3BD1EFD37FFA6AC0D573
          D7F8D72E5D9F79BDEFC6EC1BB76E26DD1CB825BAF5F876F6ED17770AEE4CDC5D
          7A8F78AFFCBEDAFDEA07FA0FEA7FB4FEB165C06DE0F860C060CFC3590FEF0E09
          879EFE94FFD387E1D247CC47D52346238D8F9D1F1F1B0D1ABDF264CE93E1A7B2
          A713CFCA7E56FF79EB73ABE7DFFDE2FB4BCF58FCD8F00BF98BCFBFAE79A9F372
          EFABA9AF3AC723C71FBCCE793DF1A6FCADCEDB7DEFB8EFBADFC7BD1F9928FC40
          FE50F3D1FA63C7A7D04FF73EE77CFEFC2FF784F3FB25D29F33000000AA494441
          5478DA63FCBF94011DB002710810B703712510AF01E2DFC80A18B168E205E236
          20CE01E229405C05C49F09691207E217487C09207E4948D35620F642E2EF0062
          3F6427A26B02D9F20C8899D00C5A0EC451D834E9403DADCE8009EE027100105F
          41D7341588B318708369409C8DACC90A889700B1221E4DF7813806888FC13455
          40E3851000C55B074853029031178BE7B1817F409C0CD2F49F08C528806C4DA0
          60E42141CF1700765C32948A3F64310000000049454E44AE426082}
        Visible = False
      end
      object Image2: TImage
        Left = 726
        Top = 38
        Width = 16
        Height = 13
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000000D0000
          000D080600000072EBE47C000000097048597300000B1300000B1301009A9C18
          00000A4D6943435050686F746F73686F70204943432070726F66696C65000078
          DA9D53775893F7163EDFF7650F5642D8F0B1976C81002223AC08C81059A21092
          006184101240C585880A561415119C4855C482D50A489D88E2A028B867418A88
          5A8B555C38EE1FDCA7B57D7AEFEDEDFBD7FBBCE79CE7FCCE79CF0F8011122691
          E6A26A003952853C3AD81F8F4F48C4C9BD80021548E0042010E6CBC26705C500
          00F00379787E74B03FFC01AF6F00020070D52E2412C7E1FF83BA502657002091
          00E02212E70B01905200C82E54C81400C81800B053B3640A009400006C797C42
          2200AA0D00ECF4493E0500D8A993DC1700D8A21CA908008D0100992847240240
          BB00605581522C02C0C200A0AC40222E04C0AE018059B632470280BD0500768E
          58900F4060008099422CCC0020380200431E13CD03204C03A030D2BFE0A95F70
          85B8480100C0CB95CD974BD23314B895D01A77F2F0E0E221E2C26CB142611729
          106609E4229C979B231348E7034CCE0C00001AF9D1C1FE383F90E7E6E4E1E666
          E76CEFF4C5A2FE6BF06F223E21F1DFFEBC8C020400104ECFEFDA5FE5E5D60370
          C701B075BF6BA95B00DA560068DFF95D33DB09A05A0AD07AF98B7938FC401E9E
          A150C83C1D1C0A0B0BED2562A1BD30E38B3EFF33E16FE08B7EF6FC401EFEDB7A
          F000719A4099ADC0A383FD71616E76AE528EE7CB0442316EF7E723FEC7857FFD
          8E29D1E234B15C2C158AF15889B850224DC779B952914421C995E212E97F32F1
          1F96FD0993770D00AC864FC04EB607B5CB6CC07EEE01028B0E58D27600407EF3
          2D8C1A0B91001067343279F7000093BFF98F402B0100CD97A4E30000BCE8185C
          A894174CC608000044A0812AB041070CC114ACC00E9CC11DBCC0170261064440
          0C24C03C104206E4801C0AA11896411954C03AD804B5B0031AA0119AE110B4C1
          31380DE7E0125C81EB70170660189EC218BC86090441C8081361213A8811628E
          D822CE0817998E04226148349280A420E988145122C5C872A402A9426A915D48
          23F22D7214398D5C40FA90DBC820328AFC8ABC47319481B25103D4027540B9A8
          1F1A8AC6A073D174340F5D8096A26BD11AB41E3D80B6A2A7D14BE87574007D8A
          8E6380D1310E668CD9615C8C87456089581A26C71663E55835568F35631D5837
          76151BC09E61EF0824028B8013EC085E8410C26C82909047584C5843A825EC23
          B412BA085709838431C2272293A84FB4257A12F9C478623AB1905846AC26EE21
          1E219E255E270E135F9348240EC992E44E0A21259032490B496B48DB482DA453
          A43ED210699C4C26EB906DC9DEE408B280AC209791B7900F904F92FBC9C3E4B7
          143AC588E24C09A22452A494124A35653FE504A59F324299A0AA51CDA99ED408
          AA883A9F5A496DA076502F5387A91334759A25CD9B1643CBA42DA3D5D09A6967
          69F7682FE974BA09DD831E4597D097D26BE807E9E7E983F4770C0D860D83C748
          6228196B197B19A718B7192F994CA605D39799C85430D7321B9967980F986F55
          582AF62A7C1591CA12953A9556957E95E7AA545573553FD579AA0B54AB550FAB
          5E567DA64655B350E3A909D416ABD5A91D55BBA936AECE5277528F50CF515FA3
          BE5FFD82FA630DB2868546A08648A35463B7C6198D2116C63265F15842D67256
          03EB2C6B984D625BB2F9EC4C7605FB1B762F7B4C534373AA66AC6691669DE671
          CD010EC6B1E0F039D99C4ACE21CE0DCE7B2D032D3F2DB1D66AAD66AD7EAD37DA
          7ADABEDA62ED72ED16EDEBDAEF75709D409D2C9DF53A6D3AF77509BA36BA51BA
          85BADB75CFEA3ED363EB79E909F5CAF50EE9DDD147F56DF4A3F517EAEFD6EFD1
          1F373034083690196C313863F0CC9063E86B9869B8D1F084E1A811CB68BA91C4
          68A3D149A327B826EE8767E33578173E66AC6F1C62AC34DE65DC6B3C61626932
          DBA4C4A4C5E4BE29CD946B9A66BAD1B4D374CCCCC82CDCACD8ACC9EC8E39D59C
          6B9E61BED9BCDBFC8D85A5459CC54A8B368BC796DA967CCB05964D96F7AC9856
          3E567956F556D7AC49D65CEB2CEB6DD6576C501B579B0C9B3A9BCBB6A8AD9BAD
          C4769B6DDF14E2148F29D229F5536EDA31ECFCEC0AEC9AEC06ED39F661F625F6
          6DF6CF1DCC1C121DD63B743B7C727475CC766C70BCEBA4E134C3A9C4A9C3E957
          671B67A1739DF33517A64B90CB1297769717536DA78AA76E9F7ACB95E51AEEBA
          D2B5D3F5A39BBB9BDCADD96DD4DDCC3DC57DABFB4D2E9B1BC95DC33DEF41F4F0
          F758E271CCE39DA79BA7C2F390E72F5E765E595EFBBD1E4FB39C269ED6306DC8
          DBC45BE0BDCB7B603A3E3D65FACEE9033EC63E029F7A9F87BEA6BE22DF3DBE23
          7ED67E997E07FC9EFB3BFACBFD8FF8BFE179F216F14E056001C101E501BD811A
          81B3036B031F049904A50735058D05BB062F0C3E15420C090D591F72936FC017
          F21BF96333DC672C9AD115CA089D155A1BFA30CC264C1ED6118E86CF08DF107E
          6FA6F94CE9CCB60888E0476C88B81F69199917F97D14292A32AA2EEA51B45374
          7174F72CD6ACE459FB67BD8EF18FA98CB93BDB6AB6727667AC6A6C526C63EC9B
          B880B8AAB8817887F845F1971274132409ED89E4C4D8C43D89E37302E76C9A33
          9CE49A54967463AEE5DCA2B917E6E9CECB9E773C593559907C3885981297B23F
          E5832042502F184FE5A76E4D1D13F2849B854F45BEA28DA251B1B7B84A3C92E6
          9D5695F638DD3B7D43FA68864F4675C633094F522B79911992B923F34D5644D6
          DEACCFD971D92D39949C949CA3520D6996B42BD730B728B74F662B2B930DE479
          E66DCA1B9387CAF7E423F973F3DB156C854CD1A3B452AE500E164C2FA82B785B
          185B78B848BD485AD433DF66FEEAF9230B82167CBD90B050B8B0B3D8B87859F1
          E022BF45BB16238B5317772E315D52BA647869F0D27DCB68CBB296FD50E25852
          55F26A79DCF28E5283D2A5A5432B82573495A994C9CB6EAEF45AB96315619564
          55EF6A97D55B567F2A17955FAC70ACA8AEF8B046B8E6E2574E5FD57CF5796DDA
          DADE4AB7CAEDEB48EBA4EB6EACF759BFAF4ABD6A41D5D086F00DAD1BF18DE51B
          5F6D4ADE74A17A6AF58ECDB4CDCACD03356135ED5BCCB6ACDBF2A136A3F67A9D
          7F5DCB56FDADABB7BED926DAD6BFDD777BF30E831D153BDEEF94ECBCB52B7857
          6BBD457DF56ED2EE82DD8F1A621BBABFE67EDDB847774FC59E8F7BA57B07F645
          EFEB6A746F6CDCAFBFBFB2096D52368D1E483A70E59B806FDA9BED9A77B5705A
          2A0EC241E5C127DFA67C7BE350E8A1CEC3DCC3CDDF997FB7F508EB48792BD23A
          BF75AC2DA36DA03DA1BDEFE88CA39D1D5E1D47BEB7FF7EEF31E36375C7358F57
          9EA09D283DF1F9E48293E3A764A79E9D4E3F3DD499DC79F74CFC996B5D515DBD
          6743CF9E3F1774EE4CB75FF7C9F3DEE78F5DF0BC70F422F762DB25B74BAD3DAE
          3D477E70FDE148AF5B6FEB65F7CBED573CAE74F44DEB3BD1EFD37FFA6AC0D573
          D7F8D72E5D9F79BDEFC6EC1BB76E26DD1CB825BAF5F876F6ED17770AEE4CDC5D
          7A8F78AFFCBEDAFDEA07FA0FEA7FB4FEB165C06DE0F860C060CFC3590FEF0E09
          879EFE94FFD387E1D247CC47D52346238D8F9D1F1F1B0D1ABDF264CE93E1A7B2
          A713CFCA7E56FF79EB73ABE7DFFDE2FB4BCF58FCD8F00BF98BCFBFAE79A9F372
          EFABA9AF3AC723C71FBCCE793DF1A6FCADCEDB7DEFB8EFBADFC7BD1F9928FC40
          FE50F3D1FA63C7A7D04FF73EE77CFEFC2FF784F3FB25D29F33000000AA494441
          5478DA63FCBF94011DB002710810B703712510AF01E2DFC80A18B168E205E236
          20CE01E229405C05C49F09691207E217487C09207E4948D35620F642E2EF0062
          3F6427A26B02D9F20C8899D00C5A0EC451D834E9403DADCE8009EE027100105F
          41D7341588B318708369409C8DACC90A889700B1221E4DF7813806888FC13455
          40E3851000C55B074853029031178BE7B1817F409C0CD2F49F08C528806C4DA0
          60E42141CF1700765C32948A3F64310000000049454E44AE426082}
        Visible = False
      end
      object Image3: TImage
        Left = 814
        Top = 38
        Width = 16
        Height = 13
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000000D0000
          000D080600000072EBE47C000000097048597300000B1300000B1301009A9C18
          00000A4D6943435050686F746F73686F70204943432070726F66696C65000078
          DA9D53775893F7163EDFF7650F5642D8F0B1976C81002223AC08C81059A21092
          006184101240C585880A561415119C4855C482D50A489D88E2A028B867418A88
          5A8B555C38EE1FDCA7B57D7AEFEDEDFBD7FBBCE79CE7FCCE79CF0F8011122691
          E6A26A003952853C3AD81F8F4F48C4C9BD80021548E0042010E6CBC26705C500
          00F00379787E74B03FFC01AF6F00020070D52E2412C7E1FF83BA502657002091
          00E02212E70B01905200C82E54C81400C81800B053B3640A009400006C797C42
          2200AA0D00ECF4493E0500D8A993DC1700D8A21CA908008D0100992847240240
          BB00605581522C02C0C200A0AC40222E04C0AE018059B632470280BD0500768E
          58900F4060008099422CCC0020380200431E13CD03204C03A030D2BFE0A95F70
          85B8480100C0CB95CD974BD23314B895D01A77F2F0E0E221E2C26CB142611729
          106609E4229C979B231348E7034CCE0C00001AF9D1C1FE383F90E7E6E4E1E666
          E76CEFF4C5A2FE6BF06F223E21F1DFFEBC8C020400104ECFEFDA5FE5E5D60370
          C701B075BF6BA95B00DA560068DFF95D33DB09A05A0AD07AF98B7938FC401E9E
          A150C83C1D1C0A0B0BED2562A1BD30E38B3EFF33E16FE08B7EF6FC401EFEDB7A
          F000719A4099ADC0A383FD71616E76AE528EE7CB0442316EF7E723FEC7857FFD
          8E29D1E234B15C2C158AF15889B850224DC779B952914421C995E212E97F32F1
          1F96FD0993770D00AC864FC04EB607B5CB6CC07EEE01028B0E58D27600407EF3
          2D8C1A0B91001067343279F7000093BFF98F402B0100CD97A4E30000BCE8185C
          A894174CC608000044A0812AB041070CC114ACC00E9CC11DBCC0170261064440
          0C24C03C104206E4801C0AA11896411954C03AD804B5B0031AA0119AE110B4C1
          31380DE7E0125C81EB70170660189EC218BC86090441C8081361213A8811628E
          D822CE0817998E04226148349280A420E988145122C5C872A402A9426A915D48
          23F22D7214398D5C40FA90DBC820328AFC8ABC47319481B25103D4027540B9A8
          1F1A8AC6A073D174340F5D8096A26BD11AB41E3D80B6A2A7D14BE87574007D8A
          8E6380D1310E668CD9615C8C87456089581A26C71663E55835568F35631D5837
          76151BC09E61EF0824028B8013EC085E8410C26C82909047584C5843A825EC23
          B412BA085709838431C2272293A84FB4257A12F9C478623AB1905846AC26EE21
          1E219E255E270E135F9348240EC992E44E0A21259032490B496B48DB482DA453
          A43ED210699C4C26EB906DC9DEE408B280AC209791B7900F904F92FBC9C3E4B7
          143AC588E24C09A22452A494124A35653FE504A59F324299A0AA51CDA99ED408
          AA883A9F5A496DA076502F5387A91334759A25CD9B1643CBA42DA3D5D09A6967
          69F7682FE974BA09DD831E4597D097D26BE807E9E7E983F4770C0D860D83C748
          6228196B197B19A718B7192F994CA605D39799C85430D7321B9967980F986F55
          582AF62A7C1591CA12953A9556957E95E7AA545573553FD579AA0B54AB550FAB
          5E567DA64655B350E3A909D416ABD5A91D55BBA936AECE5277528F50CF515FA3
          BE5FFD82FA630DB2868546A08648A35463B7C6198D2116C63265F15842D67256
          03EB2C6B984D625BB2F9EC4C7605FB1B762F7B4C534373AA66AC6691669DE671
          CD010EC6B1E0F039D99C4ACE21CE0DCE7B2D032D3F2DB1D66AAD66AD7EAD37DA
          7ADABEDA62ED72ED16EDEBDAEF75709D409D2C9DF53A6D3AF77509BA36BA51BA
          85BADB75CFEA3ED363EB79E909F5CAF50EE9DDD147F56DF4A3F517EAEFD6EFD1
          1F373034083690196C313863F0CC9063E86B9869B8D1F084E1A811CB68BA91C4
          68A3D149A327B826EE8767E33578173E66AC6F1C62AC34DE65DC6B3C61626932
          DBA4C4A4C5E4BE29CD946B9A66BAD1B4D374CCCCC82CDCACD8ACC9EC8E39D59C
          6B9E61BED9BCDBFC8D85A5459CC54A8B368BC796DA967CCB05964D96F7AC9856
          3E567956F556D7AC49D65CEB2CEB6DD6576C501B579B0C9B3A9BCBB6A8AD9BAD
          C4769B6DDF14E2148F29D229F5536EDA31ECFCEC0AEC9AEC06ED39F661F625F6
          6DF6CF1DCC1C121DD63B743B7C727475CC766C70BCEBA4E134C3A9C4A9C3E957
          671B67A1739DF33517A64B90CB1297769717536DA78AA76E9F7ACB95E51AEEBA
          D2B5D3F5A39BBB9BDCADD96DD4DDCC3DC57DABFB4D2E9B1BC95DC33DEF41F4F0
          F758E271CCE39DA79BA7C2F390E72F5E765E595EFBBD1E4FB39C269ED6306DC8
          DBC45BE0BDCB7B603A3E3D65FACEE9033EC63E029F7A9F87BEA6BE22DF3DBE23
          7ED67E997E07FC9EFB3BFACBFD8FF8BFE179F216F14E056001C101E501BD811A
          81B3036B031F049904A50735058D05BB062F0C3E15420C090D591F72936FC017
          F21BF96333DC672C9AD115CA089D155A1BFA30CC264C1ED6118E86CF08DF107E
          6FA6F94CE9CCB60888E0476C88B81F69199917F97D14292A32AA2EEA51B45374
          7174F72CD6ACE459FB67BD8EF18FA98CB93BDB6AB6727667AC6A6C526C63EC9B
          B880B8AAB8817887F845F1971274132409ED89E4C4D8C43D89E37302E76C9A33
          9CE49A54967463AEE5DCA2B917E6E9CECB9E773C593559907C3885981297B23F
          E5832042502F184FE5A76E4D1D13F2849B854F45BEA28DA251B1B7B84A3C92E6
          9D5695F638DD3B7D43FA68864F4675C633094F522B79911992B923F34D5644D6
          DEACCFD971D92D39949C949CA3520D6996B42BD730B728B74F662B2B930DE479
          E66DCA1B9387CAF7E423F973F3DB156C854CD1A3B452AE500E164C2FA82B785B
          185B78B848BD485AD433DF66FEEAF9230B82167CBD90B050B8B0B3D8B87859F1
          E022BF45BB16238B5317772E315D52BA647869F0D27DCB68CBB296FD50E25852
          55F26A79DCF28E5283D2A5A5432B82573495A994C9CB6EAEF45AB96315619564
          55EF6A97D55B567F2A17955FAC70ACA8AEF8B046B8E6E2574E5FD57CF5796DDA
          DADE4AB7CAEDEB48EBA4EB6EACF759BFAF4ABD6A41D5D086F00DAD1BF18DE51B
          5F6D4ADE74A17A6AF58ECDB4CDCACD03356135ED5BCCB6ACDBF2A136A3F67A9D
          7F5DCB56FDADABB7BED926DAD6BFDD777BF30E831D153BDEEF94ECBCB52B7857
          6BBD457DF56ED2EE82DD8F1A621BBABFE67EDDB847774FC59E8F7BA57B07F645
          EFEB6A746F6CDCAFBFBFB2096D52368D1E483A70E59B806FDA9BED9A77B5705A
          2A0EC241E5C127DFA67C7BE350E8A1CEC3DCC3CDDF997FB7F508EB48792BD23A
          BF75AC2DA36DA03DA1BDEFE88CA39D1D5E1D47BEB7FF7EEF31E36375C7358F57
          9EA09D283DF1F9E48293E3A764A79E9D4E3F3DD499DC79F74CFC996B5D515DBD
          6743CF9E3F1774EE4CB75FF7C9F3DEE78F5DF0BC70F422F762DB25B74BAD3DAE
          3D477E70FDE148AF5B6FEB65F7CBED573CAE74F44DEB3BD1EFD37FFA6AC0D573
          D7F8D72E5D9F79BDEFC6EC1BB76E26DD1CB825BAF5F876F6ED17770AEE4CDC5D
          7A8F78AFFCBEDAFDEA07FA0FEA7FB4FEB165C06DE0F860C060CFC3590FEF0E09
          879EFE94FFD387E1D247CC47D52346238D8F9D1F1F1B0D1ABDF264CE93E1A7B2
          A713CFCA7E56FF79EB73ABE7DFFDE2FB4BCF58FCD8F00BF98BCFBFAE79A9F372
          EFABA9AF3AC723C71FBCCE793DF1A6FCADCEDB7DEFB8EFBADFC7BD1F9928FC40
          FE50F3D1FA63C7A7D04FF73EE77CFEFC2FF784F3FB25D29F33000000AA494441
          5478DA63FCBF94011DB002710810B703712510AF01E2DFC80A18B168E205E236
          20CE01E229405C05C49F09691207E217487C09207E4948D35620F642E2EF0062
          3F6427A26B02D9F20C8899D00C5A0EC451D834E9403DADCE8009EE027100105F
          41D7341588B318708369409C8DACC90A889700B1221E4DF7813806888FC13455
          40E3851000C55B074853029031178BE7B1817F409C0CD2F49F08C528806C4DA0
          60E42141CF1700765C32948A3F64310000000049454E44AE426082}
        Visible = False
      end
      object Image4: TImage
        Left = 901
        Top = 38
        Width = 16
        Height = 13
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000000D0000
          000D080600000072EBE47C000000097048597300000B1300000B1301009A9C18
          00000A4D6943435050686F746F73686F70204943432070726F66696C65000078
          DA9D53775893F7163EDFF7650F5642D8F0B1976C81002223AC08C81059A21092
          006184101240C585880A561415119C4855C482D50A489D88E2A028B867418A88
          5A8B555C38EE1FDCA7B57D7AEFEDEDFBD7FBBCE79CE7FCCE79CF0F8011122691
          E6A26A003952853C3AD81F8F4F48C4C9BD80021548E0042010E6CBC26705C500
          00F00379787E74B03FFC01AF6F00020070D52E2412C7E1FF83BA502657002091
          00E02212E70B01905200C82E54C81400C81800B053B3640A009400006C797C42
          2200AA0D00ECF4493E0500D8A993DC1700D8A21CA908008D0100992847240240
          BB00605581522C02C0C200A0AC40222E04C0AE018059B632470280BD0500768E
          58900F4060008099422CCC0020380200431E13CD03204C03A030D2BFE0A95F70
          85B8480100C0CB95CD974BD23314B895D01A77F2F0E0E221E2C26CB142611729
          106609E4229C979B231348E7034CCE0C00001AF9D1C1FE383F90E7E6E4E1E666
          E76CEFF4C5A2FE6BF06F223E21F1DFFEBC8C020400104ECFEFDA5FE5E5D60370
          C701B075BF6BA95B00DA560068DFF95D33DB09A05A0AD07AF98B7938FC401E9E
          A150C83C1D1C0A0B0BED2562A1BD30E38B3EFF33E16FE08B7EF6FC401EFEDB7A
          F000719A4099ADC0A383FD71616E76AE528EE7CB0442316EF7E723FEC7857FFD
          8E29D1E234B15C2C158AF15889B850224DC779B952914421C995E212E97F32F1
          1F96FD0993770D00AC864FC04EB607B5CB6CC07EEE01028B0E58D27600407EF3
          2D8C1A0B91001067343279F7000093BFF98F402B0100CD97A4E30000BCE8185C
          A894174CC608000044A0812AB041070CC114ACC00E9CC11DBCC0170261064440
          0C24C03C104206E4801C0AA11896411954C03AD804B5B0031AA0119AE110B4C1
          31380DE7E0125C81EB70170660189EC218BC86090441C8081361213A8811628E
          D822CE0817998E04226148349280A420E988145122C5C872A402A9426A915D48
          23F22D7214398D5C40FA90DBC820328AFC8ABC47319481B25103D4027540B9A8
          1F1A8AC6A073D174340F5D8096A26BD11AB41E3D80B6A2A7D14BE87574007D8A
          8E6380D1310E668CD9615C8C87456089581A26C71663E55835568F35631D5837
          76151BC09E61EF0824028B8013EC085E8410C26C82909047584C5843A825EC23
          B412BA085709838431C2272293A84FB4257A12F9C478623AB1905846AC26EE21
          1E219E255E270E135F9348240EC992E44E0A21259032490B496B48DB482DA453
          A43ED210699C4C26EB906DC9DEE408B280AC209791B7900F904F92FBC9C3E4B7
          143AC588E24C09A22452A494124A35653FE504A59F324299A0AA51CDA99ED408
          AA883A9F5A496DA076502F5387A91334759A25CD9B1643CBA42DA3D5D09A6967
          69F7682FE974BA09DD831E4597D097D26BE807E9E7E983F4770C0D860D83C748
          6228196B197B19A718B7192F994CA605D39799C85430D7321B9967980F986F55
          582AF62A7C1591CA12953A9556957E95E7AA545573553FD579AA0B54AB550FAB
          5E567DA64655B350E3A909D416ABD5A91D55BBA936AECE5277528F50CF515FA3
          BE5FFD82FA630DB2868546A08648A35463B7C6198D2116C63265F15842D67256
          03EB2C6B984D625BB2F9EC4C7605FB1B762F7B4C534373AA66AC6691669DE671
          CD010EC6B1E0F039D99C4ACE21CE0DCE7B2D032D3F2DB1D66AAD66AD7EAD37DA
          7ADABEDA62ED72ED16EDEBDAEF75709D409D2C9DF53A6D3AF77509BA36BA51BA
          85BADB75CFEA3ED363EB79E909F5CAF50EE9DDD147F56DF4A3F517EAEFD6EFD1
          1F373034083690196C313863F0CC9063E86B9869B8D1F084E1A811CB68BA91C4
          68A3D149A327B826EE8767E33578173E66AC6F1C62AC34DE65DC6B3C61626932
          DBA4C4A4C5E4BE29CD946B9A66BAD1B4D374CCCCC82CDCACD8ACC9EC8E39D59C
          6B9E61BED9BCDBFC8D85A5459CC54A8B368BC796DA967CCB05964D96F7AC9856
          3E567956F556D7AC49D65CEB2CEB6DD6576C501B579B0C9B3A9BCBB6A8AD9BAD
          C4769B6DDF14E2148F29D229F5536EDA31ECFCEC0AEC9AEC06ED39F661F625F6
          6DF6CF1DCC1C121DD63B743B7C727475CC766C70BCEBA4E134C3A9C4A9C3E957
          671B67A1739DF33517A64B90CB1297769717536DA78AA76E9F7ACB95E51AEEBA
          D2B5D3F5A39BBB9BDCADD96DD4DDCC3DC57DABFB4D2E9B1BC95DC33DEF41F4F0
          F758E271CCE39DA79BA7C2F390E72F5E765E595EFBBD1E4FB39C269ED6306DC8
          DBC45BE0BDCB7B603A3E3D65FACEE9033EC63E029F7A9F87BEA6BE22DF3DBE23
          7ED67E997E07FC9EFB3BFACBFD8FF8BFE179F216F14E056001C101E501BD811A
          81B3036B031F049904A50735058D05BB062F0C3E15420C090D591F72936FC017
          F21BF96333DC672C9AD115CA089D155A1BFA30CC264C1ED6118E86CF08DF107E
          6FA6F94CE9CCB60888E0476C88B81F69199917F97D14292A32AA2EEA51B45374
          7174F72CD6ACE459FB67BD8EF18FA98CB93BDB6AB6727667AC6A6C526C63EC9B
          B880B8AAB8817887F845F1971274132409ED89E4C4D8C43D89E37302E76C9A33
          9CE49A54967463AEE5DCA2B917E6E9CECB9E773C593559907C3885981297B23F
          E5832042502F184FE5A76E4D1D13F2849B854F45BEA28DA251B1B7B84A3C92E6
          9D5695F638DD3B7D43FA68864F4675C633094F522B79911992B923F34D5644D6
          DEACCFD971D92D39949C949CA3520D6996B42BD730B728B74F662B2B930DE479
          E66DCA1B9387CAF7E423F973F3DB156C854CD1A3B452AE500E164C2FA82B785B
          185B78B848BD485AD433DF66FEEAF9230B82167CBD90B050B8B0B3D8B87859F1
          E022BF45BB16238B5317772E315D52BA647869F0D27DCB68CBB296FD50E25852
          55F26A79DCF28E5283D2A5A5432B82573495A994C9CB6EAEF45AB96315619564
          55EF6A97D55B567F2A17955FAC70ACA8AEF8B046B8E6E2574E5FD57CF5796DDA
          DADE4AB7CAEDEB48EBA4EB6EACF759BFAF4ABD6A41D5D086F00DAD1BF18DE51B
          5F6D4ADE74A17A6AF58ECDB4CDCACD03356135ED5BCCB6ACDBF2A136A3F67A9D
          7F5DCB56FDADABB7BED926DAD6BFDD777BF30E831D153BDEEF94ECBCB52B7857
          6BBD457DF56ED2EE82DD8F1A621BBABFE67EDDB847774FC59E8F7BA57B07F645
          EFEB6A746F6CDCAFBFBFB2096D52368D1E483A70E59B806FDA9BED9A77B5705A
          2A0EC241E5C127DFA67C7BE350E8A1CEC3DCC3CDDF997FB7F508EB48792BD23A
          BF75AC2DA36DA03DA1BDEFE88CA39D1D5E1D47BEB7FF7EEF31E36375C7358F57
          9EA09D283DF1F9E48293E3A764A79E9D4E3F3DD499DC79F74CFC996B5D515DBD
          6743CF9E3F1774EE4CB75FF7C9F3DEE78F5DF0BC70F422F762DB25B74BAD3DAE
          3D477E70FDE148AF5B6FEB65F7CBED573CAE74F44DEB3BD1EFD37FFA6AC0D573
          D7F8D72E5D9F79BDEFC6EC1BB76E26DD1CB825BAF5F876F6ED17770AEE4CDC5D
          7A8F78AFFCBEDAFDEA07FA0FEA7FB4FEB165C06DE0F860C060CFC3590FEF0E09
          879EFE94FFD387E1D247CC47D52346238D8F9D1F1F1B0D1ABDF264CE93E1A7B2
          A713CFCA7E56FF79EB73ABE7DFFDE2FB4BCF58FCD8F00BF98BCFBFAE79A9F372
          EFABA9AF3AC723C71FBCCE793DF1A6FCADCEDB7DEFB8EFBADFC7BD1F9928FC40
          FE50F3D1FA63C7A7D04FF73EE77CFEFC2FF784F3FB25D29F33000000AA494441
          5478DA63FCBF94011DB002710810B703712510AF01E2DFC80A18B168E205E236
          20CE01E229405C05C49F09691207E217487C09207E4948D35620F642E2EF0062
          3F6427A26B02D9F20C8899D00C5A0EC451D834E9403DADCE8009EE027100105F
          41D7341588B318708369409C8DACC90A889700B1221E4DF7813806888FC13455
          40E3851000C55B074853029031178BE7B1817F409C0CD2F49F08C528806C4DA0
          60E42141CF1700765C32948A3F64310000000049454E44AE426082}
        Visible = False
      end
      object Image5: TImage
        Left = 989
        Top = 38
        Width = 16
        Height = 13
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000000D0000
          000D080600000072EBE47C000000097048597300000B1300000B1301009A9C18
          00000A4D6943435050686F746F73686F70204943432070726F66696C65000078
          DA9D53775893F7163EDFF7650F5642D8F0B1976C81002223AC08C81059A21092
          006184101240C585880A561415119C4855C482D50A489D88E2A028B867418A88
          5A8B555C38EE1FDCA7B57D7AEFEDEDFBD7FBBCE79CE7FCCE79CF0F8011122691
          E6A26A003952853C3AD81F8F4F48C4C9BD80021548E0042010E6CBC26705C500
          00F00379787E74B03FFC01AF6F00020070D52E2412C7E1FF83BA502657002091
          00E02212E70B01905200C82E54C81400C81800B053B3640A009400006C797C42
          2200AA0D00ECF4493E0500D8A993DC1700D8A21CA908008D0100992847240240
          BB00605581522C02C0C200A0AC40222E04C0AE018059B632470280BD0500768E
          58900F4060008099422CCC0020380200431E13CD03204C03A030D2BFE0A95F70
          85B8480100C0CB95CD974BD23314B895D01A77F2F0E0E221E2C26CB142611729
          106609E4229C979B231348E7034CCE0C00001AF9D1C1FE383F90E7E6E4E1E666
          E76CEFF4C5A2FE6BF06F223E21F1DFFEBC8C020400104ECFEFDA5FE5E5D60370
          C701B075BF6BA95B00DA560068DFF95D33DB09A05A0AD07AF98B7938FC401E9E
          A150C83C1D1C0A0B0BED2562A1BD30E38B3EFF33E16FE08B7EF6FC401EFEDB7A
          F000719A4099ADC0A383FD71616E76AE528EE7CB0442316EF7E723FEC7857FFD
          8E29D1E234B15C2C158AF15889B850224DC779B952914421C995E212E97F32F1
          1F96FD0993770D00AC864FC04EB607B5CB6CC07EEE01028B0E58D27600407EF3
          2D8C1A0B91001067343279F7000093BFF98F402B0100CD97A4E30000BCE8185C
          A894174CC608000044A0812AB041070CC114ACC00E9CC11DBCC0170261064440
          0C24C03C104206E4801C0AA11896411954C03AD804B5B0031AA0119AE110B4C1
          31380DE7E0125C81EB70170660189EC218BC86090441C8081361213A8811628E
          D822CE0817998E04226148349280A420E988145122C5C872A402A9426A915D48
          23F22D7214398D5C40FA90DBC820328AFC8ABC47319481B25103D4027540B9A8
          1F1A8AC6A073D174340F5D8096A26BD11AB41E3D80B6A2A7D14BE87574007D8A
          8E6380D1310E668CD9615C8C87456089581A26C71663E55835568F35631D5837
          76151BC09E61EF0824028B8013EC085E8410C26C82909047584C5843A825EC23
          B412BA085709838431C2272293A84FB4257A12F9C478623AB1905846AC26EE21
          1E219E255E270E135F9348240EC992E44E0A21259032490B496B48DB482DA453
          A43ED210699C4C26EB906DC9DEE408B280AC209791B7900F904F92FBC9C3E4B7
          143AC588E24C09A22452A494124A35653FE504A59F324299A0AA51CDA99ED408
          AA883A9F5A496DA076502F5387A91334759A25CD9B1643CBA42DA3D5D09A6967
          69F7682FE974BA09DD831E4597D097D26BE807E9E7E983F4770C0D860D83C748
          6228196B197B19A718B7192F994CA605D39799C85430D7321B9967980F986F55
          582AF62A7C1591CA12953A9556957E95E7AA545573553FD579AA0B54AB550FAB
          5E567DA64655B350E3A909D416ABD5A91D55BBA936AECE5277528F50CF515FA3
          BE5FFD82FA630DB2868546A08648A35463B7C6198D2116C63265F15842D67256
          03EB2C6B984D625BB2F9EC4C7605FB1B762F7B4C534373AA66AC6691669DE671
          CD010EC6B1E0F039D99C4ACE21CE0DCE7B2D032D3F2DB1D66AAD66AD7EAD37DA
          7ADABEDA62ED72ED16EDEBDAEF75709D409D2C9DF53A6D3AF77509BA36BA51BA
          85BADB75CFEA3ED363EB79E909F5CAF50EE9DDD147F56DF4A3F517EAEFD6EFD1
          1F373034083690196C313863F0CC9063E86B9869B8D1F084E1A811CB68BA91C4
          68A3D149A327B826EE8767E33578173E66AC6F1C62AC34DE65DC6B3C61626932
          DBA4C4A4C5E4BE29CD946B9A66BAD1B4D374CCCCC82CDCACD8ACC9EC8E39D59C
          6B9E61BED9BCDBFC8D85A5459CC54A8B368BC796DA967CCB05964D96F7AC9856
          3E567956F556D7AC49D65CEB2CEB6DD6576C501B579B0C9B3A9BCBB6A8AD9BAD
          C4769B6DDF14E2148F29D229F5536EDA31ECFCEC0AEC9AEC06ED39F661F625F6
          6DF6CF1DCC1C121DD63B743B7C727475CC766C70BCEBA4E134C3A9C4A9C3E957
          671B67A1739DF33517A64B90CB1297769717536DA78AA76E9F7ACB95E51AEEBA
          D2B5D3F5A39BBB9BDCADD96DD4DDCC3DC57DABFB4D2E9B1BC95DC33DEF41F4F0
          F758E271CCE39DA79BA7C2F390E72F5E765E595EFBBD1E4FB39C269ED6306DC8
          DBC45BE0BDCB7B603A3E3D65FACEE9033EC63E029F7A9F87BEA6BE22DF3DBE23
          7ED67E997E07FC9EFB3BFACBFD8FF8BFE179F216F14E056001C101E501BD811A
          81B3036B031F049904A50735058D05BB062F0C3E15420C090D591F72936FC017
          F21BF96333DC672C9AD115CA089D155A1BFA30CC264C1ED6118E86CF08DF107E
          6FA6F94CE9CCB60888E0476C88B81F69199917F97D14292A32AA2EEA51B45374
          7174F72CD6ACE459FB67BD8EF18FA98CB93BDB6AB6727667AC6A6C526C63EC9B
          B880B8AAB8817887F845F1971274132409ED89E4C4D8C43D89E37302E76C9A33
          9CE49A54967463AEE5DCA2B917E6E9CECB9E773C593559907C3885981297B23F
          E5832042502F184FE5A76E4D1D13F2849B854F45BEA28DA251B1B7B84A3C92E6
          9D5695F638DD3B7D43FA68864F4675C633094F522B79911992B923F34D5644D6
          DEACCFD971D92D39949C949CA3520D6996B42BD730B728B74F662B2B930DE479
          E66DCA1B9387CAF7E423F973F3DB156C854CD1A3B452AE500E164C2FA82B785B
          185B78B848BD485AD433DF66FEEAF9230B82167CBD90B050B8B0B3D8B87859F1
          E022BF45BB16238B5317772E315D52BA647869F0D27DCB68CBB296FD50E25852
          55F26A79DCF28E5283D2A5A5432B82573495A994C9CB6EAEF45AB96315619564
          55EF6A97D55B567F2A17955FAC70ACA8AEF8B046B8E6E2574E5FD57CF5796DDA
          DADE4AB7CAEDEB48EBA4EB6EACF759BFAF4ABD6A41D5D086F00DAD1BF18DE51B
          5F6D4ADE74A17A6AF58ECDB4CDCACD03356135ED5BCCB6ACDBF2A136A3F67A9D
          7F5DCB56FDADABB7BED926DAD6BFDD777BF30E831D153BDEEF94ECBCB52B7857
          6BBD457DF56ED2EE82DD8F1A621BBABFE67EDDB847774FC59E8F7BA57B07F645
          EFEB6A746F6CDCAFBFBFB2096D52368D1E483A70E59B806FDA9BED9A77B5705A
          2A0EC241E5C127DFA67C7BE350E8A1CEC3DCC3CDDF997FB7F508EB48792BD23A
          BF75AC2DA36DA03DA1BDEFE88CA39D1D5E1D47BEB7FF7EEF31E36375C7358F57
          9EA09D283DF1F9E48293E3A764A79E9D4E3F3DD499DC79F74CFC996B5D515DBD
          6743CF9E3F1774EE4CB75FF7C9F3DEE78F5DF0BC70F422F762DB25B74BAD3DAE
          3D477E70FDE148AF5B6FEB65F7CBED573CAE74F44DEB3BD1EFD37FFA6AC0D573
          D7F8D72E5D9F79BDEFC6EC1BB76E26DD1CB825BAF5F876F6ED17770AEE4CDC5D
          7A8F78AFFCBEDAFDEA07FA0FEA7FB4FEB165C06DE0F860C060CFC3590FEF0E09
          879EFE94FFD387E1D247CC47D52346238D8F9D1F1F1B0D1ABDF264CE93E1A7B2
          A713CFCA7E56FF79EB73ABE7DFFDE2FB4BCF58FCD8F00BF98BCFBFAE79A9F372
          EFABA9AF3AC723C71FBCCE793DF1A6FCADCEDB7DEFB8EFBADFC7BD1F9928FC40
          FE50F3D1FA63C7A7D04FF73EE77CFEFC2FF784F3FB25D29F33000000AA494441
          5478DA63FCBF94011DB002710810B703712510AF01E2DFC80A18B168E205E236
          20CE01E229405C05C49F09691207E217487C09207E4948D35620F642E2EF0062
          3F6427A26B02D9F20C8899D00C5A0EC451D834E9403DADCE8009EE027100105F
          41D7341588B318708369409C8DACC90A889700B1221E4DF7813806888FC13455
          40E3851000C55B074853029031178BE7B1817F409C0CD2F49F08C528806C4DA0
          60E42141CF1700765C32948A3F64310000000049454E44AE426082}
        Visible = False
      end
      object RzLabel1: TRzLabel
        Left = 22
        Top = 16
        Width = 22
        Height = 16
        Caption = #36215#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        Transparent = True
      end
      object RzLabel3: TRzLabel
        Left = 159
        Top = 16
        Width = 22
        Height = 16
        Caption = #27490#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        Transparent = True
      end
      object Btn_Save: TAdvGlowButton
        Left = 1190
        Top = 8
        Width = 80
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -16
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 0
        OnClick = Btn_SaveClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
      object Btn_Query: TAdvGlowButton
        Left = 300
        Top = 7
        Width = 65
        Height = 28
        Caption = #21047'  '#26032
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 1
        OnClick = Btn_QueryClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = clBtnFace
        Appearance.ColorDisabledTo = clBtnFace
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = 16316405
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 16316405
        Appearance.ColorMirrorHotTo = 16316405
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = clBtnFace
        Appearance.ColorMirrorDisabledTo = clBtnFace
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
      end
      object Btn_Type1: TAdvGlowButton
        Left = 605
        Top = 4
        Width = 78
        Height = 33
        Caption = 'a'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 2
        Visible = False
        OnClick = Btn_Type1Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Type2: TAdvGlowButton
        Left = 693
        Top = 4
        Width = 78
        Height = 33
        Caption = 'b'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 3
        Visible = False
        OnClick = Btn_Type2Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Type3: TAdvGlowButton
        Left = 782
        Top = 3
        Width = 78
        Height = 33
        Caption = 'c'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 4
        Visible = False
        OnClick = Btn_Type3Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Type4: TAdvGlowButton
        Left = 870
        Top = 4
        Width = 78
        Height = 33
        Caption = 'd'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 5
        Visible = False
        OnClick = Btn_Type4Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Type5: TAdvGlowButton
        Left = 959
        Top = 4
        Width = 78
        Height = 33
        Caption = 'e'
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clNavy
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        TabOrder = 6
        Visible = False
        OnClick = Btn_Type5Click
        Appearance.ColorChecked = clWhite
        Appearance.ColorCheckedTo = clWhite
        Appearance.ColorDisabled = clWhite
        Appearance.ColorDisabledTo = clWhite
        Appearance.ColorDown = clWhite
        Appearance.ColorDownTo = clWhite
        Appearance.ColorHot = clWhite
        Appearance.ColorHotTo = clWhite
        Appearance.ColorMirror = clWhite
        Appearance.ColorMirrorHot = clWhite
        Appearance.ColorMirrorHotTo = clWhite
        Appearance.ColorMirrorDown = clWhite
        Appearance.ColorMirrorDownTo = clWhite
        Appearance.ColorMirrorChecked = clWhite
        Appearance.ColorMirrorCheckedTo = clWhite
        Appearance.ColorMirrorDisabled = clWhite
        Appearance.ColorMirrorDisabledTo = clWhite
        Layout = blGlyphLeftAdjusted
      end
      object DateTimePicker_BeginTime: TAdvDateTimePicker
        Left = 50
        Top = 10
        Width = 94
        Height = 25
        Date = 41091.599976851850000000
        Format = ''
        Time = 41091.599976851850000000
        DoubleBuffered = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
        Kind = dkDate
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 7
        TabStop = True
        OnChange = DateTimePicker_BeginTimeChange
        BorderStyle = bsSingle
        Ctl3D = True
        DateTime = 41091.599976851850000000
        Version = '1.2.5.0'
        LabelFont.Charset = DEFAULT_CHARSET
        LabelFont.Color = clWindowText
        LabelFont.Height = -11
        LabelFont.Name = 'Tahoma'
        LabelFont.Style = []
      end
      object DateTimePicker_EndTime: TAdvDateTimePicker
        Left = 186
        Top = 10
        Width = 94
        Height = 25
        Date = 41091.599976851850000000
        Format = ''
        Time = 41091.599976851850000000
        DoubleBuffered = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
        Kind = dkDate
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 8
        TabStop = True
        OnChange = DateTimePicker_EndTimeChange
        BorderStyle = bsSingle
        Ctl3D = True
        DateTime = 41091.599976851850000000
        Version = '1.2.5.0'
        LabelFont.Charset = DEFAULT_CHARSET
        LabelFont.Color = clWindowText
        LabelFont.Height = -11
        LabelFont.Name = 'Tahoma'
        LabelFont.Style = []
      end
      object Btn_Refresh: TAdvGlowButton
        Left = 1010
        Top = 6
        Width = 100
        Height = 33
        BorderStyle = bsNone
        Caption = #31579#36873' '#22238#36864
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Picture.Data = {
          89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
          F4000006F3494441545885B5966B8C55D515C77F6BEF739F738779C00C333C94
          41405140D1B41F5462B4B652DBA6CFD87E30691362629A6A23681FB1B168AD5A
          1FD81062A345FB481B4CB4C45A8AADA6452C68AC2D940E08850A8CCA8CA30CDC
          B9F3B877EE3D7BAD7EB87798194405B42B5939FBECACBDD67FFDD7FF9C6CE114
          ECCBB736F0951B171197A205D9E4B43592EDF9FA4D97FCBDAB6BE7E0A9A49960
          D1C9049DB3C4F3BD758BC8A6A7E0CA4D6B5D545EF67AFF065AD333BB7CE2B46B
          9F1C80EFAE9FCEC59F3E8FE123EE8B513CF5B76F0F6FC994E23C91AFBB6768A8
          8FFDDB4FBFFBF70570F9B20CDF5ABD98F240A639E4A7AE8F43F7653D85DF13B9
          34E252245272FBB38F7CB8E22704D0D0062B9F99CB99B36753C9A76E49B8BA7B
          DFEC7F1643F13E030AA00F8BB7D29377143E340019FF72ED4F9AF8EAF20B28BC
          ED1666FCCCA78F0C77CE1A2CEF475C0A510171A82991ABBBA83175CEB6FA5678
          ED5F47D9F38F5E763CDFCBB68D03548AA701604A07DCBFF57CB2C9165CB9F991
          582BD7750F6CC2B914CE22546A816A0402CDE9731189702EBD2D93A87FC92713
          9BCA766463321315BBF6F5F2DC2FF6F3DCA36F513E890909C003AFCCA57DC6CC
          8F65DC199B7B0A5B3323F111BC4BA380A881505B3B54C0428C61A809A0442E47
          43E62C1CC91DD954D3AFCA927F38D390187EE9E97DFCFA07AFF2CE6BFAFE00D6
          742EA47572C7E01BF917EA2297C1C4216AB5CE1DAA2002AA86C8E8BB613540A8
          A266A819184CCECD27E51B7F67AEF49D4C63B4FF85F57B7968D96EB0F700B0BA
          F33C5275561A2E1F4D39936AE15A72C16A05DD3100A654993043D461E3C199A1
          4109C434A63BC8A5DA7F13CBE0375C3284954B377368576502000730383040C5
          F29F03C1CCD5DC6AEEC01CC10C706830020641C0044508410041836041401208
          69F243DD1CEC7BF9DA788442B1C0F9773E7F25B32ECABE9B81E6199E3BB7B653
          19B14BE28A6C119177D16E6AA838CCAABAB0717B6260A37B66A8B9B11833E210
          98946EC3B9F4925C8BDBB262E12686FA6C0C00402225ACDA3D9D50617E8865A7
          A9B8EA28AA89D4C6AFABF3370151C10C4CAA6C99569F2A861C63D21187985CAA
          051745D3FABA077AEEFFCCBFC74600501931BE3DEF104305DBED2266056C8830
          8EE231BA6F067746A6C12D4ED7C9D53E2337E1655D08BE1082A0781430F5C40A
          B17A54C148921FEEA35CD6A79A66A5693F37339181F176FB8B33A86F8E1ACB23
          EC55A545C677E7DC63A162CBEEBDAA8B190B52CCBA30CBD99765397371966241
          2F1A297287995C6D26C7BAAF7E1D0ED56AB928152DDAFEF4E1CE3FDDDD7D6200
          00B76C98CED4B393C9F220AF9ADA59666E94E643D9263FE3D685FB18191EFBAE
          24824FDDD8C215D74FA170442FD5B26C3293C86C4C0BC79AF06E55FF5BF18A47
          BFB6676C04C7DB7D9F3DC49EE787CBC9ACCC51DC7645501534C8F472D126CDBF
          A27E42BCC5F0E755EF70DB057B18EEB72DE2655EAC428C10AB108F8EC43CA122
          97379F999EA88113D9DAEB7AD9F99722A97A7FA106F9AB068F2254CA5C39EFB2
          DC09CF8C0C1B0F5EB5974A850320CB2DF69882A9605A5DC7EAE6E2FD070300F8
          E5377BD8FCF3A3649AFD271479421542EC97D64D7EFFABC4E3CB5F27CA460FC6
          0671EC093155578FC5E446F5F0810000FE78DF6136DC7D98BAC9FE1A33F75333
          C9BEA7786AF6C6B62186F28A99DF6556A53E363F0AA23F8C0AF2640000FCEDB1
          3EF23D1596DEDC761328F99EF081678A8342AA4E8AAAA32284601E8CCE62BF9D
          1A0080CE670A743E73729790D6F975D4B524290DE86235C3CC577560E022D970
          F8C0F0A90338155BFAC339140BF6790B78358F618020025152D6FC77D391FF1F
          80A52BE791694A5129EA5A004490B15FCE8F7CCA0FED78A2073849119E6AF18E
          4B9B894BE159119922E2C6FFEDFE99AAF7B7BDF8B3AE631B1F1903E2856BD62E
          A2697A9AF250D808EE93C785BC1265DCC77B5F1D60FBE3DD63E73E8AE20BBED0
          CA921B66139774AAC6B6059803D4E66E18764F22E3BFDFD359E00F2BFE33E1EC
          0406D2E93422427B7B3BAA8AAAD2D6D68673279894814FC3FCEB13245ACA9407
          C27284079480A128016789A79CF91B7251E39B873615D9787717E9549AD24869
          8C81BBEEBA8BD9B36793CBE550D56337A15153D551A62680555386A42FBCEC1F
          FFD2A01D5E8DB3F66031F5A16D7B6B98F3647DDCFA503634E773369994D5E313
          42321B212238E728954A1C3C781059B76E5D3A84701E301D68A9791A680272D5
          5EC900CDE301388BF440F2C573F3D15BB31A2BD3F293B4ADB7393EE3B5885449
          0991890E185A324C101B048E0225E09D9A1FF2DEEF8A42080DC0526021300D98
          0434D43A4ED57C542B5603E75462E9285FAC32E24610CD2AD6612E745428968E
          8B1FA9790CF40305A01BE80C21BC1901BDC08F015F4B9E00B2B524999A8F176B
          632D0695F878195780FC44A550ACB901C3B598121000FE077525A4433CEE124B
          0000000049454E44AE426082}
        Transparent = True
        TabOrder = 9
        OnClick = Btn_RefreshClick
        Appearance.Color = clSilver
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16316405
        Appearance.ColorCheckedTo = 16316405
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 16316405
        Appearance.ColorDownTo = 16316405
        Appearance.ColorHot = 16316405
        Appearance.ColorHotTo = 16316405
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 16316405
        Appearance.ColorMirrorHotTo = 16316405
        Appearance.ColorMirrorDown = 16316405
        Appearance.ColorMirrorDownTo = 16316405
        Appearance.ColorMirrorChecked = 16316405
        Appearance.ColorMirrorCheckedTo = 16316405
        Appearance.ColorMirrorDisabled = 16316405
        Appearance.ColorMirrorDisabledTo = 16316405
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Goback: TAdvGlowButton
        Left = 1110
        Top = 7
        Width = 40
        Height = 33
        BorderStyle = bsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Picture.Data = {
          89504E470D0A1A0A0000000D49484452000000140000001408060000008D891D
          0D000002EF4944415438CB95935F68DB5514C73FE7D7246DD32C4B6B07DD18A2
          38C1BE09CE29D44D9F5A1F44714C1C8A83C8369DF8203E0D8B1595465F648A30
          C47F555AEB8B2B73437D50749321DBCB606C34C315A93AABC41A034DB326F99D
          737C6812BAA5DDE2F7C7E577B85FCEE79E7BEFB9C275B4F7D8A5C4C6EEAE5717
          4AE5ECDB43B77E480B8AAC658C9C9A7B78A1547E77E68F7F3699FBEDB4A826E0
          8BDFCE6E8EB605EFFC9E2BEC5C289571B3C9237BEE9A6D1528F560F7E7E7832D
          9B52CFE517AE8CE60A8B4955C54CE9EB490CAF8FC7BE38FFCBDCCC37CFDC6F2D
          010F7EFFEB1DE56AF8E95FF9E2B6A54A0555C34C1BA33316211A090AD1B6E0C7
          9E64FCCBD25279EAA3C7EE2EAC09DC7774FAF8DF85C587CC947A65668A9A615A
          FB371630D6777594D6C5DB3FEE5ED7F5FAFBBBB6E59A808F7E726677A55A1D57
          D388D5AAD35AF2F2022BE61ABE918C77E47B5389F4D1FD43C79ACE70F0F00FBB
          D4C2CF4C2DD6809962BA226E54BC1C03C4A29170432AF9F8772F3C3275151060
          C7A1AF0755F58899266ADBBC8073404D53AABA4555B79BE98340DCF1465E7B34
          9A6F0B82DBCE8E3C59906B0FF59E37A7EE0D35FCCACC7A4CB5A4A6375D786DCF
          52DDEF7F69AC1778C3C5F702E020220007B2A3E9F7826B81670EEE3C1D86E1F6
          6AB532570DAB7155DDBAD2CF66D2F3D94C7A1F7018A40EC3F1018060B5AB3FF7
          CA13D3AA3A606E3338F7ADDE1EF23278C559FEC4A5774D204076343D2BC800F8
          8935FCBC2097C505414098BFEE5BAE25E580DC6A5EFFF058DCDDFBEA5BC63979
          43E00DF4BC88C4DD1D81CB08934D6DD3AAFA87C7061D8E033181A23B431733E9
          9FFE37B07F782C70FC59E01010C399069EBA9879FA6CD34BA96B7C7C3C80466F
          3594C956EF74780BD821701AF860433B932747D295AB6E7F6262E216E001E066
          77EF1691141071F7141088087F5EB1E44CD1B7F6B64B7173A7FC9C8890179102
          10BA7B4144FE057E034E44800230EDEEF3400A8803891AACC3DDBBFA3A848D9D
          C12977474416DD7D0930A008946A8C9CBB17FE03EAEBA85EC0E751CF00000000
          49454E44AE426082}
        Transparent = True
        TabOrder = 10
        OnClick = Btn_GobackClick
        Appearance.Color = clSilver
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16316405
        Appearance.ColorCheckedTo = 16316405
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 16316405
        Appearance.ColorDownTo = 16316405
        Appearance.ColorHot = 16316405
        Appearance.ColorHotTo = 16316405
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 16316405
        Appearance.ColorMirrorHotTo = 16316405
        Appearance.ColorMirrorDown = 16316405
        Appearance.ColorMirrorDownTo = 16316405
        Appearance.ColorMirrorChecked = 16316405
        Appearance.ColorMirrorCheckedTo = 16316405
        Appearance.ColorMirrorDisabled = 16316405
        Appearance.ColorMirrorDisabledTo = 16316405
        Layout = blGlyphLeftAdjusted
      end
      object Btn_Gointo: TAdvGlowButton
        Left = 1140
        Top = 7
        Width = 40
        Height = 33
        BorderStyle = bsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Picture.Data = {
          89504E470D0A1A0A0000000D49484452000000140000001408060000008D891D
          0D000002EA4944415438CB9D9351685B6514C77FE73B6996A6E948A0B3C307E9
          648316A64F327CE8A62838ACC8D44955B485081D7DB4A20FB3D0B1691EF6E4DE
          94CA3618655251C187A103293A1C88D417B1A6B04D479028B54B63D3A4499AFB
          1D1F7A13D76AC7D60397EF72EEB93FFEDFF99F23DC65BC71F9B7D1CEF88E3DF9
          5BE593679FDB57D9AACEDD2DF08F42E9EB1BF9C29B3BE391B989EFF2035BD509
          F710472FFC382DCE0D76C66374A73A3EA9AD0563EF3FB5277FCFC081C96FDD43
          0FDEBFB750AABDBA502C4F38A7A82ADDA98E622AD17EFC5A7E6972FA9587FD1D
          81239FCD2663D1E88B85E5CA91B5C0F7D71B3E59AD37704E5B8FAAA37D4794EE
          54E2FB689B0E9D7EA2E77A6433E8D8A73FEC2E94CA27727F2E0EFF5DAEC6D5B9
          7F01CEE1343C9D12044A100454AAF547EF4B759C068E6E001E99BCFCC2DCAFB9
          B3A54A2DD9FA491D1A0203E7501F9ECD9C77A8D3C65F4BFE63006DC29E3CF3C5
          607EA130BDB25A6DF73EC09BC7CC30B3DBDE3DFEBFB9AA192F5D1AE9FFBCD5C3
          47DEBBD8B5D608AED5EA6BC9A655B2FE69C539FD4A55AFAAEA75755A44F8C839
          ED0D6FB0ACAACF5F191B98690A8B0094576B2F03C9D6DD0D103E04C6E7DE1D2E
          34D3FB272EC4D5698F53C53BB7A81A79FAEA5BCFCEDEDEB6B087767083E1C299
          6C263DB6D9B020080E78EF63CE0739AF91C3B3E383F39B6B22A1A0AE75598050
          15E4C4FFCE9271C8E3E72DB0C33F9F1ACE6DBD7AC6A29820220872339B492F6F
          B15733821CCC66D2B93BAE5EDF3BE747810F4C0C6045905DD94CBACA36C20198
          D845847CA836018CB2CD6839D13B7EEE90205F9A591CA42AC233D94C7A665B0A
          01E633AF5F011E43641EB19861977AC7CF1DDBB6C266F49F3C1F2DD4790D1831
          3800CC3878FB785FDB4F1B0CB7F5A9181A1AF21B805353533DC0E3C003669612
          91241031B364A941D7EFABB6F756CD12FB3ADDECEE982C87202F2245A0616645
          11590272C03711A008FC62668BE1B6C48104E076B649AC2F42079DEB23656688
          48D9CCAA800756804AC85830B3E23F8EDF3F4EFA08BDAA0000000049454E44AE
          426082}
        Transparent = True
        TabOrder = 11
        OnClick = Btn_GointoClick
        Appearance.Color = clSilver
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16316405
        Appearance.ColorCheckedTo = 16316405
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 16316405
        Appearance.ColorDownTo = 16316405
        Appearance.ColorHot = 16316405
        Appearance.ColorHotTo = 16316405
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 16316405
        Appearance.ColorMirrorHotTo = 16316405
        Appearance.ColorMirrorDown = 16316405
        Appearance.ColorMirrorDownTo = 16316405
        Appearance.ColorMirrorChecked = 16316405
        Appearance.ColorMirrorCheckedTo = 16316405
        Appearance.ColorMirrorDisabled = 16316405
        Appearance.ColorMirrorDisabledTo = 16316405
        Layout = blGlyphLeftAdjusted
      end
    end
  end
  object ButtomPanel: TRzPanel
    Left = 0
    Top = 474
    Width = 1392
    Height = 185
    Align = alBottom
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 2
    object ButtomMainPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1392
      Height = 185
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object ButtomLeftPanel: TRzPanel
        Left = 0
        Top = 0
        Width = 10
        Height = 185
        Align = alLeft
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
      end
      object ButtomRightPanel: TRzPanel
        Left = 1262
        Top = 0
        Width = 130
        Height = 185
        Align = alRight
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        Transparent = True
        object Btn_MultiSelectOk: TAdvGlowButton
          Left = 21
          Top = 82
          Width = 72
          Height = 28
          Caption = #30830'  '#23450
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 0
          OnClick = Btn_MultiSelectOkClick
          Appearance.BorderColor = 12631218
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 7915518
          Appearance.ColorCheckedTo = 11918331
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = 12631218
          Appearance.ColorHotTo = 12631218
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 12631218
          Appearance.ColorMirrorHotTo = 12631218
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 10480637
          Appearance.ColorMirrorCheckedTo = 5682430
          Appearance.ColorMirrorDisabled = 11974326
          Appearance.ColorMirrorDisabledTo = 15921906
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.GradientChecked = ggVertical
          Appearance.SystemFont = False
          Appearance.TextColorDown = clWhite
          Appearance.TextColorHot = clWhite
        end
        object Btn_MultiSelect: TAdvGlowButton
          Left = 21
          Top = 34
          Width = 72
          Height = 28
          Caption = #22810#36873#22270#29255
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlue
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 1
          OnClick = Btn_MultiSelectClick
          Appearance.BorderColor = 12631218
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 7915518
          Appearance.ColorCheckedTo = 11918331
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = 12631218
          Appearance.ColorHotTo = 12631218
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 12631218
          Appearance.ColorMirrorHotTo = 12631218
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 10480637
          Appearance.ColorMirrorCheckedTo = 5682430
          Appearance.ColorMirrorDisabled = 11974326
          Appearance.ColorMirrorDisabledTo = 15921906
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.GradientChecked = ggVertical
          Appearance.SystemFont = False
          Appearance.TextColorDown = clWhite
          Appearance.TextColorHot = clWhite
        end
      end
      object ButtomClientPanel: TRzPanel
        Left = 10
        Top = 0
        Width = 1252
        Height = 185
        Align = alClient
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        Transparent = True
        object AdvSmoothImageListBox1: TAdvSmoothImageListBox
          Left = 0
          Top = 0
          Width = 1252
          Height = 185
          ScrollType = stNormal
          AnimationFactor = 1
          ZoomAnimationFactor = 1.500000000000000000
          SelectedItemIndex = 0
          Items = <>
          TopLayerItems = <
            item
              Top = 250
              Left = 250
              HTMLText.Location = cpCenterCenter
              HTMLText.Font.Charset = DEFAULT_CHARSET
              HTMLText.Font.Color = clWindowText
              HTMLText.Font.Height = -21
              HTMLText.Font.Name = 'Tahoma'
              HTMLText.Font.Style = []
              Fill.Color = clWhite
              Fill.ColorTo = clWhite
              Fill.ColorMirror = clNone
              Fill.ColorMirrorTo = clNone
              Fill.GradientType = gtVertical
              Fill.GradientMirrorType = gtSolid
              Fill.PictureLeft = 10
              Fill.PictureTop = 10
              Fill.PictureSize = psCustom
              Fill.PictureWidth = 75
              Fill.PictureHeight = 75
              Fill.Opacity = 114
              Fill.OpacityTo = 194
              Fill.BorderColor = clBlack
              Fill.Rounding = 20
              Fill.RoundingType = rtNone
              Fill.ShadowOffset = 0
              Fill.Glow = gmNone
              Width = 0
              Height = 0
              Tag = 0
            end>
          ItemAppearance.AutoSize = True
          ItemAppearance.TextVisible = True
          ItemAppearance.TextTop = 5
          ItemAppearance.TextHeight = 15
          ItemAppearance.ItemWidth = 137
          ItemAppearance.ItemHeight = 152
          ItemAppearance.Fill.Color = 16773091
          ItemAppearance.Fill.ColorTo = 16768452
          ItemAppearance.Fill.ColorMirror = 16765357
          ItemAppearance.Fill.ColorMirrorTo = 16767936
          ItemAppearance.Fill.GradientType = gtVertical
          ItemAppearance.Fill.GradientMirrorType = gtVertical
          ItemAppearance.Fill.BorderColor = 16765357
          ItemAppearance.Fill.Rounding = 0
          ItemAppearance.Fill.ShadowOffset = 0
          ItemAppearance.Fill.Glow = gmNone
          ItemAppearance.SelectedFill.Color = 11196927
          ItemAppearance.SelectedFill.ColorTo = 7257087
          ItemAppearance.SelectedFill.ColorMirror = 4370174
          ItemAppearance.SelectedFill.ColorMirrorTo = 8053246
          ItemAppearance.SelectedFill.GradientType = gtVertical
          ItemAppearance.SelectedFill.GradientMirrorType = gtVertical
          ItemAppearance.SelectedFill.BorderColor = 16765357
          ItemAppearance.SelectedFill.BorderWidth = 10
          ItemAppearance.SelectedFill.Rounding = 0
          ItemAppearance.SelectedFill.ShadowOffset = 0
          ItemAppearance.SelectedFill.Glow = gmNone
          ItemAppearance.DisabledFill.Color = 15921906
          ItemAppearance.DisabledFill.ColorTo = 11974326
          ItemAppearance.DisabledFill.ColorMirror = 11974326
          ItemAppearance.DisabledFill.ColorMirrorTo = 15921906
          ItemAppearance.DisabledFill.GradientType = gtVertical
          ItemAppearance.DisabledFill.GradientMirrorType = gtVertical
          ItemAppearance.DisabledFill.BorderColor = 16765357
          ItemAppearance.DisabledFill.Rounding = 0
          ItemAppearance.DisabledFill.ShadowOffset = 0
          ItemAppearance.DisabledFill.Glow = gmNone
          ItemAppearance.HoverFill.Color = 15465983
          ItemAppearance.HoverFill.ColorTo = 11332863
          ItemAppearance.HoverFill.ColorMirror = 5888767
          ItemAppearance.HoverFill.ColorMirrorTo = 10807807
          ItemAppearance.HoverFill.GradientType = gtVertical
          ItemAppearance.HoverFill.GradientMirrorType = gtVertical
          ItemAppearance.HoverFill.BorderColor = 10079963
          ItemAppearance.HoverFill.Rounding = 0
          ItemAppearance.HoverFill.ShadowOffset = 0
          ItemAppearance.HoverFill.Glow = gmNone
          ItemAppearance.HoverSize = 20
          ItemAppearance.Splitter.Fill.Color = 11196927
          ItemAppearance.Splitter.Fill.ColorTo = 7257087
          ItemAppearance.Splitter.Fill.ColorMirror = clNone
          ItemAppearance.Splitter.Fill.ColorMirrorTo = clNone
          ItemAppearance.Splitter.Fill.GradientType = gtHorizontal
          ItemAppearance.Splitter.Fill.GradientMirrorType = gtSolid
          ItemAppearance.Splitter.Fill.BorderColor = 16765357
          ItemAppearance.Splitter.Fill.Rounding = 0
          ItemAppearance.Splitter.Fill.ShadowOffset = 0
          ItemAppearance.Splitter.Fill.Glow = gmNone
          ItemAppearance.Splitter.TextLocation = cpBottomCenter
          ItemAppearance.Splitter.TextFont.Charset = DEFAULT_CHARSET
          ItemAppearance.Splitter.TextFont.Color = clWindowText
          ItemAppearance.Splitter.TextFont.Height = -11
          ItemAppearance.Splitter.TextFont.Name = 'Tahoma'
          ItemAppearance.Splitter.TextFont.Style = []
          ItemAppearance.Splitter.ExpanderColor = 16773091
          ItemAppearance.Splitter.ExpanderDownColor = 7257087
          ItemAppearance.Splitter.ExpanderHoverColor = 11196927
          Header.Caption = 'Header'
          Header.Font.Charset = DEFAULT_CHARSET
          Header.Font.Color = 7485192
          Header.Font.Height = -13
          Header.Font.Name = 'Tahoma'
          Header.Font.Style = []
          Header.Fill.Color = 16773091
          Header.Fill.ColorTo = 16765615
          Header.Fill.ColorMirror = clNone
          Header.Fill.ColorMirrorTo = clNone
          Header.Fill.GradientType = gtVertical
          Header.Fill.GradientMirrorType = gtSolid
          Header.Fill.BorderColor = 16765615
          Header.Fill.Rounding = 0
          Header.Fill.ShadowOffset = 0
          Header.Fill.Glow = gmNone
          Header.Visible = False
          Header.Navigator.Visible = False
          Header.Navigator.Color = 16773091
          Header.Navigator.HintNext = 'Next Item'
          Header.Navigator.HintPrevious = 'Previous Item'
          Header.Navigator.HintNextPage = 'Next Page'
          Header.Navigator.HintPreviousPage = 'Previous Page'
          Header.Navigator.DisabledColor = clGray
          Header.Navigator.HoverColor = 11196927
          Header.Navigator.DownColor = 7257087
          Header.Navigator.BorderColor = clBlack
          Footer.Caption = 'Footer'
          Footer.Font.Charset = DEFAULT_CHARSET
          Footer.Font.Color = 7485192
          Footer.Font.Height = -13
          Footer.Font.Name = 'Tahoma'
          Footer.Font.Style = []
          Footer.Fill.Color = 16773091
          Footer.Fill.ColorTo = 16765615
          Footer.Fill.ColorMirror = clNone
          Footer.Fill.ColorMirrorTo = clNone
          Footer.Fill.GradientType = gtVertical
          Footer.Fill.GradientMirrorType = gtSolid
          Footer.Fill.BorderColor = 16765615
          Footer.Fill.Rounding = 0
          Footer.Fill.ShadowOffset = 0
          Footer.Fill.Glow = gmNone
          Footer.Visible = False
          Footer.Navigator.Visible = True
          Footer.Navigator.Color = 16773091
          Footer.Navigator.HintNext = 'Next Item'
          Footer.Navigator.HintPrevious = 'Previous Item'
          Footer.Navigator.HintNextPage = 'Next Page'
          Footer.Navigator.HintPreviousPage = 'Previous Page'
          Footer.Navigator.DisabledColor = clGray
          Footer.Navigator.HoverColor = 11196927
          Footer.Navigator.DownColor = 7257087
          Footer.Navigator.BorderColor = clBlack
          Fill.Color = clNone
          Fill.ColorTo = 14464664
          Fill.ColorMirror = clNone
          Fill.ColorMirrorTo = clNone
          Fill.GradientType = gtVertical
          Fill.GradientMirrorType = gtSolid
          Fill.HatchStyle = HatchStyleBackwardDiagonal
          Fill.HatchStyleMirror = HatchStyleBackwardDiagonal
          Fill.BackGroundPicturePosition = ppStretched
          Fill.OpacityTo = 138
          Fill.BorderColor = clBlack
          Fill.BorderOpacity = 151
          Fill.BorderWidth = 0
          Fill.Rounding = 0
          Fill.ShadowOffset = 0
          Fill.Glow = gmNone
          DefaultHTMLText.Location = cpTopLeft
          DefaultHTMLText.Font.Charset = DEFAULT_CHARSET
          DefaultHTMLText.Font.Color = clWindowText
          DefaultHTMLText.Font.Height = -11
          DefaultHTMLText.Font.Name = 'Tahoma'
          DefaultHTMLText.Font.Style = []
          Rows = 1
          ZoomOnDblClick = False
          ZoomMode = zmAspectRatio
          ShowScrollBar = False
          OnItemSelect = AdvSmoothImageListBox1ItemSelect
          OnItemDblClick = AdvSmoothImageListBox1ItemDblClick
          Align = alClient
          TabOrder = 0
          ParentShowHint = False
          ShowHint = True
          TMSStyle = 4
        end
      end
    end
  end
  object AdvPopupMenu2: TAdvPopupMenu
    AutoHotkeys = maManual
    OnPopup = AdvPopupMenu2Popup
    Version = '2.6.2.1'
    Left = 664
    Top = 240
    object CopyRecord: TMenuItem
      Caption = #36873' '#39033'  '#22797#21046
      OnClick = CopyRecordClick
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object CopyGj: TMenuItem
      Caption = #20840' '#37096'  '#22797#21046' '
      OnClick = CopyGjClick
    end
  end
  object ActionList1: TActionList
    Left = 1180
    Top = 92
    object actFl1: TAction
      Category = 'Samples'
      Caption = 'Welcome'
      Checked = True
      ImageIndex = 44
      OnExecute = actFl1Execute
    end
    object actFl2: TAction
      Tag = 1
      Category = 'Samples'
      Caption = 'Custom Framing'
      Hint = 'Click here to see a demonstration of Custom Framing.'
      ImageIndex = 14
      OnExecute = actFl2Execute
    end
    object actFl3: TAction
      Tag = 2
      Category = 'Samples'
      Caption = 'Tabs'
      Hint = 'Click here to see the power and flexibility of our tab controls.'
      ImageIndex = 0
      OnExecute = actFl3Execute
    end
    object actFl4: TAction
      Tag = 3
      Category = 'Samples'
      Caption = 'Edits'
      Hint = 'Click here to see several examples of custom edit controls.'
      ImageIndex = 30
      OnExecute = actFl4Execute
    end
    object actFl5: TAction
      Tag = 4
      Category = 'Samples'
      Caption = 'Combo Boxes'
      Hint = 'This form shows several examples of custom combo boxes.'
      ImageIndex = 38
      OnExecute = actFl5Execute
    end
    object actFl6: TAction
      Category = 'Samples'
      Caption = 'actZd'
      OnExecute = actFl6Execute
    end
    object actFl7: TAction
      Category = 'Samples'
      Caption = 'actFl7'
      OnExecute = actFl7Execute
    end
    object actFl8: TAction
      Category = 'Samples'
      Caption = 'actFl8'
      OnExecute = actFl8Execute
    end
    object actFl9: TAction
      Category = 'Samples'
      Caption = 'actFl9'
      OnExecute = actFl9Execute
    end
  end
  object PngImageList1: TPngImageList
    PngImages = <
      item
        Background = clWindow
        Name = 'filter'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          61000000854944415478DA6364A010300E230316CE9FBFFAFCF9F321C4683234
          345C139F98188A62C0F4E9D3C56E5EBFFE921803D43535C53333335F617861C1
          DCB961172E5E5C894FB3BEA161586262E26A9C6180CF2BC84EC769003EAF203B
          1DA701B8BC82EE74BC06A07B059BD3091A80EC156C4E276800CC2BFF9998FE63
          733A5106100306DE0000FDD14611086D579B0000000049454E44AE426082}
      end
      item
        Background = clWindow
        Name = 'filterdown'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          610000001974455874536F6674776172650041646F626520496D616765526561
          647971C9653C000002834944415478DAA5925F6852511CC77FF77AF57A9D7F33
          1B9A10C1A897D120073E864205494ECAA4A78D609B5B4F238A5583465039FAF3
          B0070B31A88788A811AC6D3E16582CC4EE8314D1838E25D80AFC537AFD37B5DB
          EF5E6D69592F1DF871EE39F77C3FE77BBEE7103CCFC3FF344200B85CAEA38944
          C2188FC7A152A908F314968E244935C330A0542AEB6AB5BAAAD56AABD85770BC
          8EFF16DB01E3C1603050AFD74540B95C068EAB423A5D029294824AA5028D4623
          808061E4E0F57A4E23E04E07001D80DFEF0F140A0514735845C86639A02819E0
          AEA0D3E94488CF77690A419B1D0087C301C964723C140A0532990C0AB3E86413
          36363890C918D0EBF560301840ABD5C0E4E4C9298542314F51D4AF0CAC562B94
          4AA53ECCC01E894402F97C1EC71548A5BE815CAE84DEDE1D28D6C1C484E73CBA
          599448241F3A426C0100017D98813D1A8D0638AE84AED2D0D3A30393C9082323
          CE590C710101EF11007F030821F6E1FCA1D5D5D7FEB5B5CF787E038C8D1DBF86
          C247643FFDF6CB010EBE1A8B00C2F517BF770508E0BD58EE959557573C9EC3D7
          31B487D43E26961A4521817F84A7D30E309BCD50ABD52097CB897DABCDFC04D0
          343D9DBD4DA29808E0BC058B4580770B401044B747B605A83BA4D355079E5B42
          BC193D72CA7237748F85063F0875FE9F808B08B88A80B9E23CBD0DAD0B3BC309
          A7DBF2E4D9022BAEE081FD03D0F67D7679F9E50DB7FBE06CF926EDB41DB35B7E
          DFE1C5D3E76C5707F8CA84EECCD252F896CB65BB509B6376E36E22A0DF336079
          F738D67440743A102E5785C54033EBE17038E6B3D9F6CF341A8DFBA2E0B23264
          1ADE35F0E9C14716A4C42050444706522C23D6F61660A87922228A6B52226048
          7A0E57EC417114C55ED849C30F2F303B69DD97E65D0000000049454E44AE4260
          82}
      end>
    Left = 592
    Top = 240
    Bitmap = {}
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 1
    OnTimer = Timer1Timer
    Left = 768
    Top = 176
  end
  object Timer5: TTimer
    Enabled = False
    Interval = 1
    OnTimer = Timer5Timer
    Left = 560
    Top = 64
  end
end
