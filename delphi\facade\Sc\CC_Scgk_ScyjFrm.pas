unit CC_Scgk_ScyjFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  Vcl.Grids, AdvObj, BaseGrid, AdvGrid, DBAdvGrid,  DMUtil,
  Vcl.ImgList, RzButton, Vcl.StdCtrls, Vcl.Mask, RzEdit,
  RzLabel, Txm, RzCmboBx, VclTee.TeeGDIPlus, VclTee.TeEngine, VclTee.TeeTools,
  VclTee.TeePageNumTool, VclTee.Series, VclTee.TeeDoubleHorizBar,
  VclTee.TeeProcs, VclTee.Chart, VclTee.TeeURL, VclTee.TeeSeriesTextEd,
  VclTee.TeeExcelSource, RzBckgnd, Vcl.Imaging.pngimage, AdvGlowButton, CcXjll,
  BankModelConfig, RzCommon, CommonUtil, CC_ScConFrm, RzTabs, CcSc,
  CC_Ccgk_ScStatisticalReportFrm;

type
  TCC_Scgk_ScyjFrame = class(TFrame)
    MainPanel: TRzPanel;
    TopPanel: TRzPanel;
    TopHeadPanel: TRzPanel;
    TopButtonPanel: TRzPanel;
    TopBluePanel: TRzPanel;
    RightPanel: TRzPanel;
    RzPageControl1: TRzPageControl;
    TabSheet1: TRzTabSheet;
    TabSheet2: TRzTabSheet;
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    Btn_Type2: TAdvGlowButton;
    Btn_Type1: TAdvGlowButton;
    RzLabel14: TRzLabel;
    procedure Btn_Type1Click(Sender: TObject);
    procedure Btn_Type2Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_ScConFrame: TCC_ScConFrame;
    FCC_Ccgk_ScStatisticalReportFrame: TCC_Ccgk_ScStatisticalReportFrame;
    procedure Init();
    procedure TabShowByType(showtype: Integer);
  end;

var
  UpdateFlag: Integer;
  UpdateID: string;
  DMUtilSelect: TDataModule1;
  G_Sztype: Integer;

implementation

uses IndexFrm;

{$R *.dfm}

procedure TCC_Scgk_ScyjFrame.Btn_Type1Click(Sender: TObject);
begin
  TabShowByType(1);

  if (FCC_ScConFrame <> nil) then
  begin
    FCC_ScConFrame.Free;
    FCC_ScConFrame := nil;
  end;

  if (FCC_Ccgk_ScStatisticalReportFrame <> nil) then
  begin
    FCC_Ccgk_ScStatisticalReportFrame.Free;
    FCC_Ccgk_ScStatisticalReportFrame := nil;
  end;

  if (FCC_ScConFrame = nil) then
  begin
    FCC_ScConFrame := TCC_ScConFrame.Create(Application);
    FCC_ScConFrame.Parent := self.RzPanel1;
    FCC_ScConFrame.Align := alClient;
    FCC_ScConFrame.Init();
  end;

  self.Btn_Type1.Enabled := true;
  self.Btn_Type2.Enabled := true;
end;

procedure TCC_Scgk_ScyjFrame.Btn_Type2Click(Sender: TObject);
begin
  TabShowByType(2);

  if (FCC_ScConFrame <> nil) then
  begin
    FCC_ScConFrame.Free;
    FCC_ScConFrame := nil;
  end;

  if (FCC_Ccgk_ScStatisticalReportFrame <> nil) then
  begin
    FCC_Ccgk_ScStatisticalReportFrame.Free;
    FCC_Ccgk_ScStatisticalReportFrame := nil;
  end;

  if (FCC_Ccgk_ScStatisticalReportFrame = nil) then
  begin
    FCC_Ccgk_ScStatisticalReportFrame :=
      TCC_Ccgk_ScStatisticalReportFrame.Create(Application);
    FCC_Ccgk_ScStatisticalReportFrame.Parent := self.RzPanel2;
    FCC_Ccgk_ScStatisticalReportFrame.Align := alClient;
    FCC_Ccgk_ScStatisticalReportFrame.Init();
  end;

  self.Btn_Type1.Enabled := true;
  self.Btn_Type2.Enabled := true;

end;

procedure TCC_Scgk_ScyjFrame.Init();
begin
  TabShowByType(1);

  if (FCC_ScConFrame <> nil) then
  begin
    FCC_ScConFrame.Free;
    FCC_ScConFrame := nil;
  end;

  if (FCC_Ccgk_ScStatisticalReportFrame <> nil) then
  begin
    FCC_Ccgk_ScStatisticalReportFrame.Free;
    FCC_Ccgk_ScStatisticalReportFrame := nil;
  end;

  if (FCC_ScConFrame = nil) then
  begin
    FCC_ScConFrame := TCC_ScConFrame.Create(Application);
    FCC_ScConFrame.Parent := self.RzPanel1;
    FCC_ScConFrame.Align := alClient;
    FCC_ScConFrame.Init();
  end;

  self.Btn_Type1.Enabled := true;
  self.Btn_Type2.Enabled := true;

end;

procedure TCC_Scgk_ScyjFrame.TabShowByType(showtype: Integer);
begin
  self.Btn_Type1.Font.Style := [fsBold];
  self.Btn_Type2.Font.Style := [fsBold];

  self.Btn_Type1.Font.Color := clNavy;
  self.Btn_Type1.Appearance.BorderColor := clgray;
  self.Btn_Type1.Appearance.ColorHot := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type1.Appearance.ColorHotTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type1.Appearance.Color := clwhite;
  self.Btn_Type1.Appearance.ColorTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirror := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type2.Font.Color := clNavy;
  self.Btn_Type2.Appearance.BorderColor := clgray;
  self.Btn_Type2.Appearance.ColorHot := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type2.Appearance.ColorHotTo := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type2.Appearance.Color := clwhite;
  self.Btn_Type2.Appearance.ColorTo := clwhite;
  self.Btn_Type2.Appearance.ColorMirror := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type1.Refresh;
  self.Btn_Type2.Refresh;

  self.Btn_Type1.Enabled := false;
  self.Btn_Type2.Enabled := false;

  if showtype = 1 then
  begin
      self.Btn_Type1.Font.Color := clwhite;
    self.Btn_Type1.Font.Style := [fsBold];
    self.Btn_Type1.Appearance.BorderColor := TabButtonColor;
    self.Btn_Type1.Appearance.ColorHot := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorHot := TabButtonColor;
    self.Btn_Type1.Appearance.ColorHotTo := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorHotTo := TabButtonColor;
    self.Btn_Type1.Appearance.Color := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirror := TabButtonColor;
    self.Btn_Type1.Appearance.ColorTo := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorTo := TabButtonColor;
  end;

  if showtype = 2 then
  begin
      self.Btn_Type2.Font.Color := clwhite;
    self.Btn_Type2.Font.Style := [fsBold];
    self.Btn_Type2.Appearance.BorderColor := TabButtonColor;
    self.Btn_Type2.Appearance.ColorHot := TabButtonColor;
    self.Btn_Type2.Appearance.ColorMirrorHot := TabButtonColor;
    self.Btn_Type2.Appearance.ColorHotTo := TabButtonColor;
    self.Btn_Type2.Appearance.ColorMirrorHotTo := TabButtonColor;
    self.Btn_Type2.Appearance.Color := TabButtonColor;
    self.Btn_Type2.Appearance.ColorMirror := TabButtonColor;
    self.Btn_Type2.Appearance.ColorTo := TabButtonColor;
    self.Btn_Type2.Appearance.ColorMirrorTo := TabButtonColor;
  end;

  if (showtype = 1) then
  begin
    self.RzPageControl1.ActivePageIndex := 0;
  end;

  if (showtype = 2) then
  begin
    self.RzPageControl1.ActivePageIndex := 1;
  end;

end;

end.
