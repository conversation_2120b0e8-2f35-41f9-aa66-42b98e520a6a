<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业指标分析 - 企业检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'blue-50': '#EFF6FF',
                        'blue-100': '#DBEAFE',
                        'blue-500': '#3B82F6',
                        'blue-600': '#2563EB'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F1F5F9 0%, #EFF6FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
        }
        .metric-item {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .metric-item:hover {
            border-left-color: #3B82F6;
            transform: translateX(2px);
        }
        .trend-up {
            color: #10B981;
        }
        .trend-down {
            color: #EF4444;
        }
        .chart-container {
            background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-blue-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">企业指标分析</h1>
                        <p class="text-sm text-gray-600">企业创新指标表现，支持导出及趋势分析</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">时间切换：</span>
                        <div class="flex bg-gray-100 rounded-lg p-1">
                            <button class="px-3 py-1 text-sm rounded-md bg-white text-gray-900 shadow-sm">年度</button>
                            <button class="px-3 py-1 text-sm rounded-md text-gray-600 hover:text-gray-900">季度</button>
                        </div>
                    </div>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- 核心指标概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                    </div>
                    <div class="flex items-center space-x-1 text-sm">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-up">+15.6%</span>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900">158.6亿</p>
                <p class="text-sm text-gray-600">总研发投入</p>
                <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 75%"></div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-percentage text-green-600 text-xl"></i>
                    </div>
                    <div class="flex items-center space-x-1 text-sm">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-up">+2.1%</span>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900">3.42%</p>
                <p class="text-sm text-gray-600">平均研发强度</p>
                <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 68%"></div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-purple-600 text-xl"></i>
                    </div>
                    <div class="flex items-center space-x-1 text-sm">
                        <i class="fas fa-arrow-up trend-up"></i>
                        <span class="trend-up">+8.3%</span>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900">1,247</p>
                <p class="text-sm text-gray-600">研发活跃企业</p>
                <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full" style="width: 82%"></div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-lightbulb text-orange-600 text-xl"></i>
                    </div>
                    <div class="flex items-center space-x-1 text-sm">
                        <i class="fas fa-arrow-down trend-down"></i>
                        <span class="trend-down">-3.2%</span>
                    </div>
                </div>
                <p class="text-2xl font-bold text-gray-900">12,356</p>
                <p class="text-sm text-gray-600">专利申请总量</p>
                <div class="mt-3 w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 65%"></div>
                </div>
            </div>
        </div>

        <!-- Tab导航 -->
        <div class="bg-white rounded-xl card-shadow mb-6">
            <div class="border-b border-gray-100">
                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                    <button class="tab-btn border-b-2 border-purple-500 py-4 px-1 text-sm font-medium text-purple-600" data-tab="rd500">
                        研发费用500强
                    </button>
                    <button class="tab-btn border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="monitor">
                        重点监测企业
                    </button>
                    <button class="tab-btn border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="research-value">
                        "研值"排名
                    </button>
                    <button class="tab-btn border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700" data-tab="trends">
                        趋势分析
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧图表区域 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 研发费用500强 -->
                <div id="tab-rd500" class="tab-content">
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">研发费用500强企业分布</h3>
                            <div class="flex items-center space-x-4">
                                <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                    <option>全部行业</option>
                                    <option>智能制造</option>
                                    <option>生物医药</option>
                                    <option>新材料</option>
                                </select>
                                <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm">
                                    <i class="fas fa-download mr-2"></i>
                                    导出清单
                                </button>
                            </div>
                        </div>
                        
                        <!-- 图表容器 -->
                        <div class="chart-container rounded-lg p-6 mb-6">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop&crop=entropy" 
                                 alt="研发费用分布图表" 
                                 class="w-full h-64 object-cover rounded-lg opacity-80">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-gray-700">
                                    <i class="fas fa-chart-area text-4xl mb-2 text-blue-600"></i>
                                    <p class="text-lg font-semibold">研发费用500强企业分布图</p>
                                    <p class="text-sm mt-1">按行业、区域维度展示</p>
                                </div>
                            </div>
                        </div>

                        <!-- 统计数据 -->
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <p class="text-2xl font-bold text-blue-600">186</p>
                                <p class="text-sm text-gray-600">宁波企业入榜</p>
                            </div>
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <p class="text-2xl font-bold text-green-600">23.8%</p>
                                <p class="text-sm text-gray-600">占全省比重</p>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <p class="text-2xl font-bold text-purple-600">+12</p>
                                <p class="text-sm text-gray-600">较去年新增</p>
                            </div>
                        </div>
                    </div>

                    <!-- 行业分布 -->
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-6">行业分布情况</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                    <span class="text-sm text-gray-700">智能制造</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-500 h-2 rounded-full" style="width: 35%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-16 text-right">65家</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-green-500 rounded"></div>
                                    <span class="text-sm text-gray-700">生物医药</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 28%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-16 text-right">52家</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-purple-500 rounded"></div>
                                    <span class="text-sm text-gray-700">新材料</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-purple-500 h-2 rounded-full" style="width: 22%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-16 text-right">41家</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-4 h-4 bg-orange-500 rounded"></div>
                                    <span class="text-sm text-gray-700">数字经济</span>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <div class="w-32 bg-gray-200 rounded-full h-2">
                                        <div class="bg-orange-500 h-2 rounded-full" style="width: 15%"></div>
                                    </div>
                                    <span class="text-sm font-medium text-gray-900 w-16 text-right">28家</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 重点监测企业 -->
                <div id="tab-monitor" class="tab-content hidden">
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">重点监测企业情况</h3>
                            <div class="flex items-center space-x-2">
                                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">总额</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">占比</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">增速</button>
                            </div>
                        </div>

                        <!-- 预警企业列表 -->
                        <div class="space-y-6">
                            <!-- 研发投入下降企业 -->
                            <div class="border border-red-200 rounded-lg p-4 bg-red-50">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-semibold text-red-900 flex items-center">
                                        <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                        研发投入下降超25%企业
                                    </h4>
                                    <span class="text-sm text-red-600 font-medium">23家</span>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div class="metric-item bg-white p-3 rounded border-l-4 border-red-500">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm text-gray-700">宁波XX制造有限公司</span>
                                            <span class="text-sm font-medium text-red-600">-28.5%</span>
                                        </div>
                                    </div>
                                    <div class="metric-item bg-white p-3 rounded border-l-4 border-red-500">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm text-gray-700">东海XX科技股份公司</span>
                                            <span class="text-sm font-medium text-red-600">-31.2%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 增长前20企业 -->
                            <div class="border border-green-200 rounded-lg p-4 bg-green-50">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-semibold text-green-900 flex items-center">
                                        <i class="fas fa-arrow-up text-green-600 mr-2"></i>
                                        研发投入增长前20企业
                                    </h4>
                                    <button class="text-sm text-green-600 hover:text-green-800">查看完整榜单</button>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <div class="metric-item bg-white p-3 rounded border-l-4 border-green-500">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm text-gray-700">甬江XX新材料集团</span>
                                            <span class="text-sm font-medium text-green-600">+156.3%</span>
                                        </div>
                                    </div>
                                    <div class="metric-item bg-white p-3 rounded border-l-4 border-green-500">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm text-gray-700">海曙XX生物科技公司</span>
                                            <span class="text-sm font-medium text-green-600">+89.7%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 高新技术产业产值 -->
                            <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-semibold text-blue-900 flex items-center">
                                        <i class="fas fa-industry text-blue-600 mr-2"></i>
                                        高新技术产业产值排名
                                    </h4>
                                    <div class="flex space-x-2">
                                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">前50</button>
                                        <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">后20</button>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-white rounded">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
                                            <span class="text-sm text-gray-700">宁波XX智能制造集团</span>
                                        </div>
                                        <span class="text-sm font-medium text-blue-600">285.6亿</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-white rounded">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
                                            <span class="text-sm text-gray-700">镇海XX新材料股份</span>
                                        </div>
                                        <span class="text-sm font-medium text-blue-600">198.3亿</span>
                                    </div>
                                    <div class="flex items-center justify-between p-2 bg-white rounded">
                                        <div class="flex items-center space-x-3">
                                            <span class="w-6 h-6 bg-orange-400 text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
                                            <span class="text-sm text-gray-700">北仑XX海洋科技</span>
                                        </div>
                                        <span class="text-sm font-medium text-blue-600">156.8亿</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- "研值"排名 -->
                <div id="tab-research-value" class="tab-content hidden">
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">"研值"排名前100企业</h3>
                            <button class="flex items-center px-4 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors text-sm">
                                <i class="fas fa-trophy mr-2"></i>
                                查看完整榜单
                            </button>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div class="bg-gradient-to-r from-yellow-50 to-orange-50 p-4 rounded-lg border border-yellow-200">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <div class="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                                        <span class="text-sm font-semibold text-gray-900">甬江新材料集团</span>
                                    </div>
                                    <div class="space-y-1 text-xs">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研值得分</span>
                                            <span class="font-medium text-yellow-600">98.6</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研发投入</span>
                                            <span class="font-medium">2.4亿</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">创新产出</span>
                                            <span class="font-medium">342项专利</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-gray-50 to-blue-50 p-4 rounded-lg border border-gray-200">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <div class="w-8 h-8 bg-gray-400 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                                        <span class="text-sm font-semibold text-gray-900">宁波智能制造</span>
                                    </div>
                                    <div class="space-y-1 text-xs">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研值得分</span>
                                            <span class="font-medium text-gray-600">96.3</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研发投入</span>
                                            <span class="font-medium">1.8亿</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">创新产出</span>
                                            <span class="font-medium">289项专利</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-orange-50 to-red-50 p-4 rounded-lg border border-orange-200">
                                    <div class="flex items-center space-x-3 mb-3">
                                        <div class="w-8 h-8 bg-orange-400 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                                        <span class="text-sm font-semibold text-gray-900">东海生物医药</span>
                                    </div>
                                    <div class="space-y-1 text-xs">
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研值得分</span>
                                            <span class="font-medium text-orange-600">94.8</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">研发投入</span>
                                            <span class="font-medium">1.2亿</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-gray-600">创新产出</span>
                                            <span class="font-medium">156项专利</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 研值评分说明 -->
                            <div class="bg-purple-50 rounded-lg p-4 border border-purple-200">
                                <h4 class="font-semibold text-purple-900 mb-2 flex items-center">
                                    <i class="fas fa-info-circle text-purple-600 mr-2"></i>
                                    "研值"评分说明
                                </h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                    <div>
                                        <span class="font-medium text-purple-800">研发投入（40%）</span>
                                        <p class="text-purple-700 mt-1">研发费用、研发强度、增长率</p>
                                    </div>
                                    <div>
                                        <span class="font-medium text-purple-800">创新产出（35%）</span>
                                        <p class="text-purple-700 mt-1">专利、论文、标准、奖项</p>
                                    </div>
                                    <div>
                                        <span class="font-medium text-purple-800">创新效率（25%）</span>
                                        <p class="text-purple-700 mt-1">产出效率、转化效率</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 趋势分析 -->
                <div id="tab-trends" class="tab-content hidden">
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-900">趋势分析</h3>
                            <div class="flex items-center space-x-2">
                                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">过去5年</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">过去3年</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">过去1年</button>
                            </div>
                        </div>
                        
                        <!-- 趋势图表 -->
                        <div class="chart-container rounded-lg p-6 mb-6 relative">
                            <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=300&fit=crop&crop=entropy" 
                                 alt="趋势分析图表" 
                                 class="w-full h-48 object-cover rounded-lg opacity-70">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-gray-700">
                                    <i class="fas fa-chart-line text-3xl mb-2 text-purple-600"></i>
                                    <p class="text-lg font-semibold">研发投入趋势分析图</p>
                                    <p class="text-sm mt-1">2019-2024年变化趋势</p>
                                </div>
                            </div>
                        </div>

                        <!-- 趋势数据 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-4">关键指标变化</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                        <span class="text-sm text-gray-700">研发投入总额</span>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm font-medium">158.6亿</span>
                                            <span class="text-sm text-green-600">+15.6%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                        <span class="text-sm text-gray-700">平均研发强度</span>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm font-medium">3.42%</span>
                                            <span class="text-sm text-green-600">+0.23%</span>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                        <span class="text-sm text-gray-700">研发活跃企业</span>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm font-medium">1,247家</span>
                                            <span class="text-sm text-green-600">+8.3%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900 mb-4">发展预测</h4>
                                <div class="space-y-3">
                                    <div class="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-trending-up text-yellow-600"></i>
                                            <span class="text-sm font-medium text-yellow-900">增长预期</span>
                                        </div>
                                        <p class="text-sm text-yellow-800">预计2025年研发投入将达到185亿元</p>
                                    </div>
                                    <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <i class="fas fa-target text-blue-600"></i>
                                            <span class="text-sm font-medium text-blue-900">政策目标</span>
                                        </div>
                                        <p class="text-sm text-blue-800">力争研发强度达到3.8%，新增高企200家</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧操作面板 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 快速操作 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button class="w-full flex items-center justify-center py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-download mr-2"></i>
                            导出分析报告
                        </button>
                        <button class="w-full flex items-center justify-center py-3 bg-green-50 text-green-600 border border-green-200 rounded-lg hover:bg-green-100 transition-colors">
                            <i class="fas fa-chart-bar mr-2"></i>
                            自定义图表
                        </button>
                        <button class="w-full flex items-center justify-center py-3 bg-purple-50 text-purple-600 border border-purple-200 rounded-lg hover:bg-purple-100 transition-colors">
                            <i class="fas fa-filter mr-2"></i>
                            高级筛选
                        </button>
                        <button class="w-full flex items-center justify-center py-3 bg-orange-50 text-orange-600 border border-orange-200 rounded-lg hover:bg-orange-100 transition-colors">
                            <i class="fas fa-bell mr-2"></i>
                            设置预警
                        </button>
                    </div>
                </div>

                <!-- 数据概览 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">数据概览</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">数据更新时间</span>
                            <span class="text-sm font-medium">刚刚</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">数据覆盖企业</span>
                            <span class="text-sm font-medium">2,468家</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">数据完整度</span>
                            <span class="text-sm font-medium text-green-600">98.6%</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-600">数据来源</span>
                            <span class="text-sm font-medium">科技大脑</span>
                        </div>
                    </div>
                </div>

                <!-- AI洞察 -->
                <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-xl card-shadow p-6 border border-blue-100">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-blue-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-blue-900">AI洞察</h3>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-blue-900">重点发现</span>
                                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">新</span>
                            </div>
                            <p class="text-xs text-blue-800 leading-relaxed">
                                智能制造领域研发投入增长最为显著，同比增长23.5%，建议加大政策扶持力度。
                            </p>
                        </div>
                        
                        <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-blue-900">风险提示</span>
                                <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">关注</span>
                            </div>
                            <p class="text-xs text-blue-800 leading-relaxed">
                                23家企业研发投入下降超25%，需要重点关注并提供针对性支持。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Tab切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 更新tab按钮状态
                    tabBtns.forEach(b => {
                        b.classList.remove('border-purple-500', 'text-purple-600');
                        b.classList.add('border-transparent', 'text-gray-500');
                    });
                    this.classList.add('border-purple-500', 'text-purple-600');
                    this.classList.remove('border-transparent', 'text-gray-500');

                    // 更新tab内容显示
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    const targetContent = document.getElementById('tab-' + targetTab);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }
                });
            });

            // 初始化显示第一个tab
            if (tabBtns.length > 0 && tabContents.length > 0) {
                tabBtns[0].click();
            }
        });

        // 图表交互
        function toggleTimeRange(range) {
            console.log('切换时间范围:', range);
            // 这里可以添加重新加载数据的逻辑
        }

        // 导出功能
        function exportData(type) {
            console.log('导出数据:', type);
            // 模拟导出效果
            alert('正在导出数据，请稍候...');
        }
    </script>
</body>
</html> 