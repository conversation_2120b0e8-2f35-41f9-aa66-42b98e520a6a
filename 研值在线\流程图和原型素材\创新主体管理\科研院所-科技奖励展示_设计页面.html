<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技奖励展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科技奖励展示</h1>

        <!-- 卡片概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="filterByLevel('national')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">国家级奖励</p>
                        <p class="text-2xl font-bold text-gray-900">28</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm">12%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="filterByLevel('provincial')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">省级奖励</p>
                        <p class="text-2xl font-bold text-gray-900">56</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm">8%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="filterByLevel('municipal')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">市级奖励</p>
                        <p class="text-2xl font-bold text-gray-900">124</p>
                    </div>
                    <div class="text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        <span class="text-sm">5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="filterByType('patent')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">专利奖</p>
                        <p class="text-2xl font-bold text-gray-900">42</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm">15%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">近三年增长率</p>
                        <p class="text-2xl font-bold text-gray-900">18%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">团队覆盖率</p>
                        <p class="text-2xl font-bold text-gray-900">76%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖项级别</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50" onclick="toggleLevel(this)">国家级</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleLevel(this)">省级</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleLevel(this)">市级</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleLevel(this)">专利奖</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖项类别</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleType(this)">自然科学奖</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleType(this)">技术发明奖</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleType(this)">科技进步奖</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50" onclick="toggleType(this)">专利金奖</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">授奖年份</label>
                    <div class="flex space-x-2">
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>全部年份</option>
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                        </select>
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>至</option>
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">学科领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部领域</option>
                        <option>信息科学</option>
                        <option>材料科学</option>
                        <option>生物医药</option>
                        <option>工程技术</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目负责人</label>
                    <input type="text" placeholder="请输入负责人姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    查询
                </button>
            </div>
        </div>

        <!-- 奖项列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">奖项列表</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增奖励
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授奖年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项类别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主要完成人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家自然科学奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新型纳米材料在能源存储中的应用研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自然科学奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授等5人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">材料科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载证书</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省科技进步奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能电网关键技术研究与应用</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技进步奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授等8人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程技术</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载证书</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市专利金奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种高效节能的污水处理装置</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利金奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王研究员等3人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">环境科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载证书</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">87</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-lg font-medium text-gray-800 mb-6">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-4">按级别分布</h3>
                    <div class="h-64">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-4">年度获奖趋势</h3>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-4">学科领域分布</h3>
                    <div class="h-64">
                        <canvas id="fieldChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-4">奖励类别占比</h3>
                    <div class="h-64">
                        <canvas id="typeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 奖项详情侧栏 -->
    <div id="detailSidebar" class="fixed inset-y-0 right-0 w-full lg:w-1/3 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="h-full overflow-y-auto">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">奖项详情</h3>
                <button onclick="hideDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="flex items-center justify-center mb-6">
                    <div class="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-2">基本信息</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">奖项名称：</span>
                                <span class="font-medium text-gray-900">国家自然科学奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">获奖项目：</span>
                                <span class="font-medium text-gray-900">新型纳米材料在能源存储中的应用研究</span>
                            </div>
                            <div>
                                <span class="text-gray-500">授奖年度：</span>
                                <span class="font-medium text-gray-900">2023</span>
                            </div>
                            <div>
                                <span class="text-gray-500">奖项级别：</span>
                                <span class="font-medium text-gray-900">国家级</span>
                            </div>
                            <div>
                                <span class="text-gray-500">奖项类别：</span>
                                <span class="font-medium text-gray-900">自然科学奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">学科领域：</span>
                                <span class="font-medium text-gray-900">材料科学</span>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-md font-medium text-gray-900 mb-2">项目技术亮点</h4>
                        <p class="text-sm text-gray-700">
                            本项目针对能源存储领域的关键科学问题，创新性地设计并制备了一系列新型纳米材料，显著提高了储能器件的能量密度和循环稳定性。研究成果在Nature Energy等顶级期刊发表论文12篇，获授权发明专利8项。
                        </p>
                    </div>

                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-md font-medium text-gray-900 mb-2">完成人与分工</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-700">张教授</span>
                                <span class="text-gray-500">项目负责人，材料设计与合成</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">李研究员</span>
                                <span class="text-gray-500">性能测试与数据分析</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-700">王博士</span>
                                <span class="text-gray-500">理论计算与机理研究</span>
                            </div>
                        </div>
                    </div>

                    <div class="border-t border-gray-200 pt-4">
                        <h4 class="text-md font-medium text-gray-900 mb-2">获奖证书</h4>
                        <div class="bg-gray-100 rounded-lg p-4 flex justify-center">
                            <img src="https://source.unsplash.com/300x200?certificate" alt="获奖证书" class="h-40 rounded">
                        </div>
                        <button class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                            下载证书
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 bg-gray-50">
                <div class="flex space-x-3">
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出PDF
                    </button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                        </svg>
                        分享链接
                    </button>
                    <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function showDetail(id) {
            document.getElementById('detailSidebar').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function hideDetail() {
            document.getElementById('detailSidebar').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        function filterByLevel(level) {
            console.log('按级别筛选:', level);
            // 实际应用中这里会调用API获取筛选结果
        }

        function filterByType(type) {
            console.log('按类型筛选:', type);
            // 实际应用中这里会调用API获取筛选结果
        }

        function toggleLevel(button) {
            if (button.classList.contains('border-blue-500')) {
                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');
                button.classList.add('border-gray-300', 'text-gray-700');
            } else {
                button.classList.remove('border-gray-300', 'text-gray-700');
                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');
            }
        }

        function toggleType(button) {
            if (button.classList.contains('border-blue-500')) {
                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');
                button.classList.add('border-gray-300', 'text-gray-700');
            } else {
                button.classList.remove('border-gray-300', 'text-gray-700');
                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');
            }
        }

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 按级别分布图表
            const levelCtx = document.getElementById('levelChart').getContext('2d');
            new Chart(levelCtx, {
                type: 'bar',
                data: {
                    labels: ['国家级', '省级', '市级', '专利奖'],
                    datasets: [{
                        label: '数量',
                        data: [28, 56, 124, 42],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 年度获奖趋势图表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '获奖数量',
                        data: [32, 38, 45, 52, 68, 87],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 学科领域分布图表
            const fieldCtx = document.getElementById('fieldChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'radar',
                data: {
                    labels: ['信息科学', '材料科学', '生物医药', '工程技术', '环境科学', '基础研究'],
                    datasets: [{
                        label: '获奖数量',
                        data: [45, 38, 32, 56, 28, 19],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: '#3B82F6',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // 奖励类别占比图表
            const typeCtx = document.getElementById('typeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'pie',
                data: {
                    labels: ['自然科学奖', '技术发明奖', '科技进步奖', '专利奖'],
                    datasets: [{
                        data: [32, 28, 56, 42],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 点击详情侧栏外部关闭
            document.getElementById('detailSidebar').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideDetail();
                }
            });

            // ESC键关闭侧栏
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailSidebar').classList.contains('translate-x-full')) {
                    hideDetail();
                }
            });
        });
    </script>
</body>
</html>