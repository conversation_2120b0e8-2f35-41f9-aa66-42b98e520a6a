'use client'

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, 
  Calendar,
  Download,
  Filter,
  RefreshCcw,
  Settings,
  Share2
} from "lucide-react"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { OperationStats } from "@/components/assets/operation-stats"
import { OperationTrends } from "@/components/assets/operation-trends"
import { OperationEfficiency } from "@/components/assets/operation-efficiency"
import { OperationRecords } from "@/components/assets/operation-records"
import { Badge } from "@/components/ui/badge"
import { DateRange } from "react-day-picker"

export default function OperationsPage() {
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2023, 9, 1),
    to: new Date(2023, 9, 31)
  })
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      {/* 头部区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-medium text-gray-900">运维统计</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Share2 className="mr-2 h-4 w-4" />
              分享
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              设置
            </Button>
          </div>
        </div>

        {/* 筛选工具栏 */}
        <Card className="border-[#E5E9EF]">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <DatePickerWithRange className="w-[360px]" date={date} setDate={setDate} />
                <Select defaultValue="all">
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="运维类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部类型</SelectItem>
                    <SelectItem value="incident">故障处理</SelectItem>
                    <SelectItem value="change">变更管理</SelectItem>
                    <SelectItem value="release">发布部署</SelectItem>
                    <SelectItem value="maintenance">日常维护</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[160px]">
                    <SelectValue placeholder="处理团队" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部团队</SelectItem>
                    <SelectItem value="platform">平台组</SelectItem>
                    <SelectItem value="network">网络组</SelectItem>
                    <SelectItem value="security">安全组</SelectItem>
                    <SelectItem value="app">应用组</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" />
                  高级筛选
                </Button>
                <Button variant="outline">
                  <RefreshCcw className="mr-2 h-4 w-4" />
                  刷新数据
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  导出数据
                </Button>
                <Button className="bg-blue-500 hover:bg-blue-600">
                  <FileText className="mr-2 h-4 w-4" />
                  生成报告
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 统计卡片 */}
      <OperationStats />
      
      {/* 趋势和效能分析 */}
      <div className="grid grid-cols-2 gap-6">
        <OperationTrends />
        <OperationEfficiency />
      </div>

      {/* 运维记录 */}
      <Card className="border-[#E5E9EF]">
        <CardHeader className="flex flex-row items-center justify-between border-b border-[#E5E9EF]">
          <CardTitle>运维记录</CardTitle>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-blue-50 text-blue-600">
              今日: 28条
            </Badge>
            <Badge variant="outline" className="bg-green-50 text-green-600">
              已完成: 24条
            </Badge>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-600">
              处理中: 4条
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <OperationRecords />
        </CardContent>
      </Card>
    </div>
  )
} 