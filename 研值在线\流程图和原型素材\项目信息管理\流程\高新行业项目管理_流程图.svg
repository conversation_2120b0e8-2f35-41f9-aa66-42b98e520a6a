<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1300 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="650" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">高新行业项目管理业务流程</text>

  <!-- 阶段一：项目检索与展示 -->
  <text x="650" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：项目检索与展示</text>
  
  <!-- 节点1: 筛选条件设定 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户自定义条件)</text>
  </g>

  <!-- 节点2: 系统自动检索 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统自动检索</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(匹配高新项目)</text>
  </g>

  <!-- 节点3: 项目列表展示 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目列表展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(供查看处理)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：项目操作管理 -->
  <text x="650" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：项目操作管理</text>

  <!-- 节点4: 新增项目 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">规范录入模板</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">实时数据校验</text>
  </g>

  <!-- 节点5: 项目编辑删除 -->
  <g transform="translate(350, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目编辑删除</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">权限校验</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">操作日志记录</text>
  </g>

  <!-- 节点6: 批量导入 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">标准模板下载</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">自动校验归档</text>
  </g>

  <!-- 节点7: 项目详情页 -->
  <g transform="translate(850, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情页</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">全要素查阅</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">材料上传批注</text>
  </g>

  <!-- 从项目列表到各操作的连接线 -->
  <path d="M 850 200 C 800 250, 250 280, 190 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 870 200 C 820 250, 500 280, 440 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 200 C 890 250, 750 280, 690 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 200 C 950 250, 980 280, 940 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据处理与校验 -->
  <text x="650" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与校验</text>

  <!-- 节点8: 数据校验入库 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验入库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(投资结构、工程费用)</text>
  </g>

  <!-- 节点9: 数据导出 -->
  <g transform="translate(500, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(结构化Excel文件)</text>
  </g>

  <!-- 节点10: 异常处理提示 -->
  <g transform="translate(800, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">异常处理提示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(数据一致性保障)</text>
  </g>

  <!-- 从操作节点到处理节点的连接线 -->
  <path d="M 190 400 C 190 450, 250 480, 300 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 940 400 C 940 450, 850 480, 900 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 690 400 C 690 450, 650 480, 600 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：日志记录与审计 -->
  <text x="650" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：日志记录与审计</text>
  
  <!-- 节点11: 操作日志记录 -->
  <g transform="translate(300, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">全程可追溯记录</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">安全日志管理</text>
  </g>

  <!-- 节点12: 审计分析统计 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计分析统计</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">合规分析</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">数据统计报告</text>
  </g>

  <!-- 从处理节点到最终日志的连接线 -->
  <path d="M 300 590 C 300 650, 400 680, 425 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 590 C 600 650, 700 680, 775 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 590 C 900 650, 800 680, 775 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 日志记录到审计分析的连接线 -->
  <path d="M 550 760 Q 600 760 650 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从审计分析回到项目列表 -->
  <path d="M 775 720 C 1100 650, 1100 200, 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 1050, 400)">持续优化改进</text>

</svg>