'use client'

import React, { useState } from 'react'
import { ChatMessage } from '../difyznwd/DifyChat/ChatMessage'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare } from 'lucide-react'

// 模拟消息数据
const mockMessages = [
  {
    id: '1',
    role: 'user' as const,
    content: '我想海外知识产权纠纷应对指导，请问如何申请？',
    timestamp: Date.now() - 60000,
    status: 'sent' as const
  },
  {
    id: '2', 
    role: 'assistant' as const,
    content: `您好！申请海外知识产权纠纷应对指导，您可以通过以下方式：

## 申请方式包括：

### 线下窗口申请
- **地址**: 宁波市鄞州区梅墟街道天童北路2660号新材料国际创新中心A区A5幢知识产权大厦一楼11号窗口
- **联系电话**: 0574-87971230

### 邮寄申请
- 接收邮寄至上述地址（浙江省宁波市鄞州区梅墟街道天童北路2660号新材料国际创新中心A区A5幢知识产权大厦一楼11号窗口）

### 邮箱申请
- 受理申请邮箱地址: **<EMAIL>**

为了帮您高效启动申请流程，您能简单描述一下您的纠纷涉及的国家或具体知识产权类型（如专利、商标等）吗？`,
    timestamp: Date.now() - 30000,
    status: 'received' as const,
    metadata: {
      usage: {
        total_tokens: 1250,
        latency: 2.3
      },
      retrieverResources: [
        {
          position: 1,
          dataset_name: '知识产权保护中心服务指南',
          document_name: '海外知识产权纠纷应对指导申请指南.pdf',
          score: 0.95,
          content: '申请海外知识产权纠纷应对指导的方式包括：线下窗口申请、邮寄申请、邮箱申请等多种便民申请方式...',
          hit_count: 3,
          word_count: 156
        }
      ]
    }
  }
]

export default function ChatDemo() {
  const [rating, setRating] = useState<{[key: string]: 'up' | 'down' | null}>({})

  const handleRating = (messageId: string, newRating: 'up' | 'down') => {
    setRating(prev => ({
      ...prev,
      [messageId]: prev[messageId] === newRating ? null : newRating
    }))
  }

  const handleRetry = (messageId: string) => {
    console.log('重新生成消息:', messageId)
  }

  const handleAddNote = (messageId: string) => {
    console.log('添加备注:', messageId)
  }

  return (
    <div className="h-screen bg-gradient-to-b from-blue-50/30 to-white/80 p-4">
      <Card className="h-full shadow-lg rounded-xl border-border/50">
        <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-xl">
          <CardTitle className="flex items-center gap-3 text-xl">
            <MessageSquare className="h-6 w-6 text-blue-600" />
            甬知AI智能问答助手 - 界面演示
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 p-6 bg-gradient-to-b from-blue-50/30 to-white/80 overflow-auto">
          <div className="space-y-6 max-w-full">
            {mockMessages.map((message) => (
              <ChatMessage
                key={message.id}
                message={message}
                config={{ baseURL: 'http://10.26.37.118:8081/v1' }}
                onRetry={message.role === 'assistant' ? () => handleRetry(message.id) : undefined}
                onRating={handleRating}
                onAddNote={handleAddNote}
                showWorkflow={true}
              />
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
