'use client'

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ServerIcon,
  HardDriveIcon,
  NetworkIcon,
  ShieldIcon,
  ActivityIcon,
  CpuIcon,
  CircuitBoardIcon,
  WifiIcon,
  ThermometerIcon,
  BatteryIcon
} from "lucide-react"

export function InfrastructureStatus() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>设备状态监控</CardTitle>
        <Badge variant="outline">实时数据</Badge>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 服务器状态 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ServerIcon className="h-5 w-5 text-blue-500" />
              <span className="font-medium">核心服务器群</span>
            </div>
            <Badge className="bg-green-50 text-green-600">运行正常</Badge>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CpuIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">CPU 使用率</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-semibold">76%</span>
                <div className="text-xs text-gray-500">
                  <div>峰值: 89%</div>
                  <div>均值: 65%</div>
                </div>
              </div>
              <div className="mt-2 w-full h-2 bg-gray-200 rounded-full">
                <div className="h-full w-[76%] bg-blue-500 rounded-full"></div>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CircuitBoardIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">内存使用率</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-2xl font-semibold">82%</span>
                <div className="text-xs text-gray-500">
                  <div>峰值: 94%</div>
                  <div>均值: 78%</div>
                </div>
              </div>
              <div className="mt-2 w-full h-2 bg-gray-200 rounded-full">
                <div className="h-full w-[82%] bg-yellow-500 rounded-full"></div>
              </div>
            </div>
          </div>
        </div>

        {/* 存储状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HardDriveIcon className="h-5 w-5 text-purple-500" />
              <span className="font-medium">存储系统</span>
            </div>
            <Badge className="bg-yellow-50 text-yellow-600">需要注意</Badge>
          </div>
          <div className="p-3 bg-gray-50 rounded-lg">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <div className="text-sm text-gray-500">总容量</div>
                <div className="text-lg font-semibold">256TB</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">已使用</div>
                <div className="text-lg font-semibold">198TB</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">使用率</div>
                <div className="text-lg font-semibold text-yellow-600">77.3%</div>
              </div>
            </div>
          </div>
        </div>

        {/* 网络状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <NetworkIcon className="h-5 w-5 text-green-500" />
              <span className="font-medium">网络系统</span>
            </div>
            <Badge className="bg-green-50 text-green-600">运行正常</Badge>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <WifiIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">带宽利用率</span>
              </div>
              <div className="text-2xl font-semibold">45.8%</div>
              <div className="mt-1 text-xs text-gray-500">峰值: 78.2%</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <ActivityIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">网络延迟</span>
              </div>
              <div className="text-2xl font-semibold">12ms</div>
              <div className="mt-1 text-xs text-gray-500">峰值: 45ms</div>
            </div>
          </div>
        </div>

        {/* 环境状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ShieldIcon className="h-5 w-5 text-indigo-500" />
              <span className="font-medium">环境监控</span>
            </div>
            <Badge className="bg-green-50 text-green-600">正常</Badge>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <ThermometerIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">机房温度</span>
              </div>
              <div className="text-2xl font-semibold">23.5°C</div>
              <div className="mt-1 text-xs text-gray-500">标准范围: 18-26°C</div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <BatteryIcon className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">UPS状态</span>
              </div>
              <div className="text-2xl font-semibold">98%</div>
              <div className="mt-1 text-xs text-gray-500">续航时间: 4.5h</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 