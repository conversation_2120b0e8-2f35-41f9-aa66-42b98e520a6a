<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业成长历程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        national: '#ef4444',
                        provincial: '#f59e0b',
                        municipal: '#10b981'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">企业成长历程</h1>
            <p class="mt-2 text-gray-600">可视化呈现企业从初创到高成长阶段的政策认定轨迹与研发能力跃升</p>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">认定级别</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-national focus:ring-national border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">国家级</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-provincial focus:ring-provincial border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">省级</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-municipal focus:ring-municipal border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-700">市级</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年份区间</label>
                    <div class="flex items-center space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>2018</option>
                            <option selected>2019</option>
                            <option>2020</option>
                            <option>2021</option>
                            <option>2022</option>
                            <option>2023</option>
                            <option>2024</option>
                        </select>
                        <span class="text-gray-500">至</span>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option>2018</option>
                            <option>2019</option>
                            <option>2020</option>
                            <option>2021</option>
                            <option selected>2022</option>
                            <option>2023</option>
                            <option>2024</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">业务主题</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                        <option value="">全部主题</option>
                        <option>高新技术企业</option>
                        <option>专精特新</option>
                        <option>企业技术中心</option>
                        <option>研发机构</option>
                        <option>人才计划</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end mt-6 space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    重置
                </button>
                <button class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    查询
                </button>
            </div>
        </div>

        <!-- 视图切换 -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex space-x-2">
                <button id="timelineView" class="px-4 py-2 bg-primary text-white rounded-md text-sm">时间轴视图</button>
                <button id="stageView" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">分段阶段视图</button>
                <button id="radarView" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">对比雷达视图</button>
            </div>
            <button class="flex items-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                导出成长报告
            </button>
        </div>

        <!-- 时间轴视图 -->
        <div id="timelineContent" class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">企业成长时间轴</h2>
            <div class="relative">
                <!-- 时间轴主线 -->
                <div class="absolute left-1/2 h-full w-1 bg-gray-200 transform -translate-x-1/2"></div>
                
                <!-- 里程碑节点 -->
                <div class="relative space-y-12">
                    <!-- 2018年 -->
                    <div class="flex items-center">
                        <div class="w-1/2 pr-8 text-right">
                            <div class="inline-block px-4 py-2 bg-gray-100 rounded-lg">
                                <p class="text-sm text-gray-600">2018年</p>
                            </div>
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="relative">
                                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-6 h-6 rounded-full bg-municipal flex items-center justify-center text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-8 p-4 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer" onclick="openDetailModal('1')">
                                    <h3 class="text-md font-medium text-gray-900">宁波市科技型中小企业认定</h3>
                                    <p class="text-sm text-gray-500 mt-1">2018年5月 | 宁波市科技局</p>
                                    <p class="text-sm text-gray-600 mt-2">企业首次获得市级科技型中小企业认定，标志着正式进入科技企业行列。</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2019年 -->
                    <div class="flex items-center">
                        <div class="w-1/2 pr-8 text-right">
                            <div class="relative">
                                <div class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2 w-8 h-8 rounded-full bg-provincial flex items-center justify-center text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="mr-8 p-4 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer" onclick="openDetailModal('2')">
                                    <h3 class="text-md font-medium text-gray-900">浙江省科技型中小企业认定</h3>
                                    <p class="text-sm text-gray-500 mt-1">2019年7月 | 浙江省科技厅</p>
                                    <p class="text-sm text-gray-600 mt-2">企业研发投入占比达到5%，获得省级科技型中小企业认定。</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="inline-block px-4 py-2 bg-gray-100 rounded-lg">
                                <p class="text-sm text-gray-600">2019年</p>
                            </div>
                        </div>
                    </div>

                    <!-- 2020年 -->
                    <div class="flex items-center">
                        <div class="w-1/2 pr-8 text-right">
                            <div class="inline-block px-4 py-2 bg-gray-100 rounded-lg">
                                <p class="text-sm text-gray-600">2020年</p>
                            </div>
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="relative">
                                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-6 h-6 rounded-full bg-municipal flex items-center justify-center text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-8 p-4 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer" onclick="openDetailModal('3')">
                                    <h3 class="text-md font-medium text-gray-900">宁波市企业工程（技术）中心</h3>
                                    <p class="text-sm text-gray-500 mt-1">2020年11月 | 宁波市科技局</p>
                                    <p class="text-sm text-gray-600 mt-2">企业研发团队扩大至30人，建立首个市级研发平台。</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 2021年 -->
                    <div class="flex items-center">
                        <div class="w-1/2 pr-8 text-right">
                            <div class="relative">
                                <div class="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-1/2 w-10 h-10 rounded-full bg-national flex items-center justify-center text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="mr-8 p-4 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer" onclick="openDetailModal('4')">
                                    <h3 class="text-md font-medium text-gray-900">国家高新技术企业认定</h3>
                                    <p class="text-sm text-gray-500 mt-1">2021年9月 | 科技部火炬中心</p>
                                    <p class="text-sm text-gray-600 mt-2">企业通过国家高新技术企业认定，享受15%税收优惠，研发投入占比达8%。</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="inline-block px-4 py-2 bg-gray-100 rounded-lg">
                                <p class="text-sm text-gray-600">2021年</p>
                            </div>
                        </div>
                    </div>

                    <!-- 2022年 -->
                    <div class="flex items-center">
                        <div class="w-1/2 pr-8 text-right">
                            <div class="inline-block px-4 py-2 bg-gray-100 rounded-lg">
                                <p class="text-sm text-gray-600">2022年</p>
                            </div>
                        </div>
                        <div class="w-1/2 pl-8">
                            <div class="relative">
                                <div class="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-8 h-8 rounded-full bg-provincial flex items-center justify-center text-white">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <div class="ml-8 p-4 border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer" onclick="openDetailModal('5')">
                                    <h3 class="text-md font-medium text-gray-900">浙江省专精特新中小企业</h3>
                                    <p class="text-sm text-gray-500 mt-1">2022年6月 | 浙江省经信厅</p>
                                    <p class="text-sm text-gray-600 mt-2">企业在细分市场占有率进入全省前三，获得专精特新称号。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分段阶段视图 (默认隐藏) -->
        <div id="stageContent" class="bg-white rounded-lg shadow-md p-6 mb-8 hidden">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">企业成长阶段分析</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- 创业期 -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="openStageDetail('startup')">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-md font-medium text-gray-900">创业期</h3>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">2018-2019</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500">累计认定</p>
                            <p class="text-lg font-semibold">2项</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">研发投入增速</p>
                            <p class="text-lg font-semibold text-green-600">+120%</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">知识产权</p>
                            <p class="text-lg font-semibold">5件</p>
                        </div>
                    </div>
                </div>

                <!-- 成长初期 -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="openStageDetail('growth')">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-md font-medium text-gray-900">成长初期</h3>
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">2020-2021</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500">累计认定</p>
                            <p class="text-lg font-semibold">4项</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">研发投入增速</p>
                            <p class="text-lg font-semibold text-green-600">+85%</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">知识产权</p>
                            <p class="text-lg font-semibold">18件</p>
                        </div>
                    </div>
                </div>

                <!-- 快速扩张 -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="openStageDetail('expansion')">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-md font-medium text-gray-900">快速扩张</h3>
                        <span class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">2022-2023</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500">累计认定</p>
                            <p class="text-lg font-semibold">7项</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">研发投入增速</p>
                            <p class="text-lg font-semibold text-green-600">+65%</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">知识产权</p>
                            <p class="text-lg font-semibold">32件</p>
                        </div>
                    </div>
                </div>

                <!-- 成熟深化 -->
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="openStageDetail('mature')">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="text-md font-medium text-gray-900">成熟深化</h3>
                        <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">2024-</span>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <p class="text-sm text-gray-500">累计认定</p>
                            <p class="text-lg font-semibold">3项</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">研发投入增速</p>
                            <p class="text-lg font-semibold text-green-600">+40%</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">知识产权</p>
                            <p class="text-lg font-semibold">15件</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 雷达对比视图 (默认隐藏) -->
        <div id="radarContent" class="bg-white rounded-lg shadow-md p-6 mb-8 hidden">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">企业成长维度对比</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="h-96">
                        <canvas id="radarChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-md font-medium text-gray-900 mb-3">对比说明</h3>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-primary mr-2"></div>
                                <span class="text-sm">本企业</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                                <span class="text-sm">行业平均值</span>
                            </div>
                            <div class="border-t border-gray-200 pt-4">
                                <p class="text-sm text-gray-600">企业优势维度：</p>
                                <div class="mt-2 flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">成果产出</span>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">政策认定</span>
                                </div>
                            </div>
                            <div class="border-t border-gray-200 pt-4">
                                <p class="text-sm text-gray-600">需提升维度：</p>
                                <div class="mt-2 flex flex-wrap gap-2">
                                    <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">平台建设</span>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">人才结构</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择对比企业</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                            <option value="">行业平均值</option>
                            <option>宁波A科技公司</option>
                            <option>宁波B电子公司</option>
                            <option>宁波C新材料公司</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">认定详情</h3>
                        <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">基本信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">认定名称</p>
                                    <p class="text-gray-900 font-medium mt-1" id="detail-title">国家高新技术企业</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">认定级别</p>
                                    <p class="text-gray-900 font-medium mt-1" id="detail-level">国家级</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">认定日期</p>
                                    <p class="text-gray-900 font-medium mt-1" id="detail-date">2021年9月15日</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">主管部门</p>
                                    <p class="text-gray-900 font-medium mt-1" id="detail-department">科技部火炬中心</p>
                                </div>
                                <div class="col-span-2">
                                    <p class="text-gray-500">批文编号</p>
                                    <p class="text-gray-900 font-medium mt-1" id="detail-number">GR202133000001</p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">证书信息</h4>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <img src="https://via.placeholder.com/600x400?text=证书扫描件" alt="证书" class="w-full rounded">
                                <div class="mt-4 flex space-x-3">
                                    <button class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-700 text-sm">
                                        下载证书
                                    </button>
                                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                                        查看申报材料
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-2">关联政策</h4>
                            <div class="border border-gray-200 rounded-lg divide-y divide-gray-200">
                                <div class="p-4 hover:bg-gray-50">
                                    <p class="text-sm font-medium text-gray-900">高新技术企业税收优惠政策</p>
                                    <p class="text-sm text-gray-500 mt-1">企业所得税减免至15%</p>
                                </div>
                                <div class="p-4 hover:bg-gray-50">
                                    <p class="text-sm font-medium text-gray-900">宁波市科技型企业研发补助</p>
                                    <p class="text-sm text-gray-500 mt-1">按研发投入的10%给予补助</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 阶段详情弹窗 -->
    <div id="stageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold text-gray-900" id="stage-title">创业期 (2018-2019)</h3>
                        <button onclick="closeModal('stageModal')" class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">核心指标</h4>
                            <div class="space-y-4">
                                <div>
                                    <p class="text-sm text-gray-500">研发投入占比</p>
                                    <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">从3%提升至8%</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">知识产权增长</p>
                                    <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 70%"></div>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">从2件增至18件</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">人才规模</p>
                                    <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">从15人增至45人</p>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">政策扶持记录</h4>
                            <div class="border border-gray-200 rounded-lg divide-y divide-gray-200">
                                <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('2')">
                                    <p class="text-sm font-medium text-gray-900">浙江省科技型中小企业认定</p>
                                    <p class="text-sm text-gray-500 mt-1">2019年7月 | 研发补助50万元</p>
                                </div>
                                <div class="p-4 hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('1')">
                                    <p class="text-sm font-medium text-gray-900">宁波市科技型中小企业认定</p>
                                    <p class="text-sm text-gray-500 mt-1">2018年5月 | 税收减免30万元</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            // 模拟不同ID加载不同数据
            const details = [
                {
                    title: "宁波市科技型中小企业认定",
                    level: "市级",
                    date: "2018年5月20日",
                    department: "宁波市科技局",
                    number: "甬科企认[2018]0123号"
                },
                {
                    title: "浙江省科技型中小企业认定",
                    level: "省级",
                    date: "2019年7月15日",
                    department: "浙江省科技厅",
                    number: "浙科企认[2019]0456号"
                },
                {
                    title: "宁波市企业工程（技术）中心",
                    level: "市级",
                    date: "2020年11月5日",
                    department: "宁波市科技局",
                    number: "甬工技[2020]0789号"
                },
                {
                    title: "国家高新技术企业认定",
                    level: "国家级",
                    date: "2021年9月15日",
                    department: "科技部火炬中心",
                    number: "GR202133000001"
                },
                {
                    title: "浙江省专精特新中小企业",
                    level: "省级",
                    date: "2022年6月20日",
                    department: "浙江省经信厅",
                    number: "浙经信企[2022]123号"
                }
            ];

            const detail = details[id-1];
            document.getElementById('detail-title').textContent = detail.title;
            document.getElementById('detail-level').textContent = detail.level;
            document.getElementById('detail-date').textContent = detail.date;
            document.getElementById('detail-department').textContent = detail.department;
            document.getElementById('detail-number').textContent = detail.number;

            openModal('detailModal');
        }

        function openStageDetail(stage) {
            const titles = {
                'startup': '创业期 (2018-2019)',
                'growth': '成长初期 (2020-2021)',
                'expansion': '快速扩张 (2022-2023)',
                'mature': '成熟深化 (2024-)'
            };
            document.getElementById('stage-title').textContent = titles[stage];
            openModal('stageModal');
        }

        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 视图切换
        document.getElementById('timelineView').addEventListener('click', function() {
            document.getElementById('timelineContent').classList.remove('hidden');
            document.getElementById('stageContent').classList.add('hidden');
            document.getElementById('radarContent').classList.add('hidden');
            this.classList.remove('bg-white', 'border', 'text-gray-700');
            this.classList.add('bg-primary', 'text-white');
            document.getElementById('stageView').classList.remove('bg-primary', 'text-white');
            document.getElementById('stageView').classList.add('bg-white', 'border', 'text-gray-700');
            document.getElementById('radarView').classList.remove('bg-primary', 'text-white');
            document.getElementById('radarView').classList.add('bg-white', 'border', 'text-gray-700');
        });

        document.getElementById('stageView').addEventListener('click', function() {
            document.getElementById('timelineContent').classList.add('hidden');
            document.getElementById('stageContent').classList.remove('hidden');
            document.getElementById('radarContent').classList.add('hidden');
            this.classList.remove('bg-white', 'border', 'text-gray-700');
            this.classList.add('bg-primary', 'text-white');
            document.getElementById('timelineView').classList.remove('bg-primary', 'text-white');
            document.getElementById('timelineView').classList.add('bg-white', 'border', 'text-gray-700');
            document.getElementById('radarView').classList.remove('bg-primary', 'text-white');
            document.getElementById('radarView').classList.add('bg-white', 'border', 'text-gray-700');
        });

        document.getElementById('radarView').addEventListener('click', function() {
            document.getElementById('timelineContent').classList.add('hidden');
            document.getElementById('stageContent').classList.add('hidden');
            document.getElementById('radarContent').classList.remove('hidden');
            this.classList.remove('bg-white', 'border', 'text-gray-700');
            this.classList.add('bg-primary', 'text-white');
            document.getElementById('timelineView').classList.remove('bg-primary', 'text-white');
            document.getElementById('timelineView').classList.add('bg-white', 'border', 'text-gray-700');
            document.getElementById('stageView').classList.remove('bg-primary', 'text-white');
            document.getElementById('stageView').classList.add('bg-white', 'border', 'text-gray-700');
        });

        // 初始化雷达图
        document.addEventListener('DOMContentLoaded', function() {
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['政策认定', '研发投入', '成果产出', '平台建设', '人才结构'],
                    datasets: [
                        {
                            label: '本企业',
                            data: [85, 75, 90, 60, 70],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(59, 130, 246, 1)'
                        },
                        {
                            label: '行业平均值',
                            data: [65, 70, 75, 80, 75],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(16, 185, 129, 1)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });
        });

        // 点击弹窗外部关闭
        document.querySelectorAll('.fixed.inset-0').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal(this.id);
                }
            });
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.querySelectorAll('.fixed.inset-0').forEach(modal => {
                    if (!modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            }
        });
    </script>
</body>
</html>