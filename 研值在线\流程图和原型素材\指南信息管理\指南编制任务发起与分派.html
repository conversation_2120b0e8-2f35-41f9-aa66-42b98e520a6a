<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指南编制任务发起与分派</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex-1 p-6">
        <div class="h-[calc(100vh-120px)] flex flex-col">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                    <svg class="mr-2 h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    指南编制任务发起与分派
                </h1>
                
                <div class="flex gap-2">
                    <button class="flex items-center gap-1 px-3 py-2 text-sm border border-blue-200 text-blue-700 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        任务统计
                    </button>
                    
                    <button class="flex items-center gap-1 px-3 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建任务
                    </button>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="flex-1 bg-white rounded-lg shadow-lg border border-blue-100">
                <!-- 选项卡导航 -->
                <div class="border-b border-blue-100 bg-blue-50/50 p-4">
                    <div class="flex space-x-1 bg-blue-100 p-1 rounded-lg w-fit">
                        <button id="tab-create" class="tab-btn active px-4 py-2 text-sm font-medium rounded-md transition-colors bg-blue-600 text-white">
                            <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            任务创建
                        </button>
                        <button id="tab-list" class="tab-btn px-4 py-2 text-sm font-medium rounded-md transition-colors text-blue-700 hover:bg-blue-200">
                            <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            任务列表
                        </button>
                        <button id="tab-assign" class="tab-btn px-4 py-2 text-sm font-medium rounded-md transition-colors text-blue-700 hover:bg-blue-200">
                            <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            认领分派
                        </button>
                    </div>
                </div>
                
                <!-- 任务创建区 -->
                <div id="content-create" class="tab-content p-6">
                    <div class="max-w-4xl mx-auto">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- 左侧表单 -->
                            <div class="space-y-6">
                                <!-- 需求征集事项 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        需求征集事项 <span class="text-red-500">*</span>
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">请选择需求征集事项</option>
                                        <option value="req001">农业技术推广指南编制需求</option>
                                        <option value="req002">农产品质量安全管理指南需求</option>
                                        <option value="req003">农村电商发展指导意见需求</option>
                                        <option value="req004">农业机械化推进指南需求</option>
                                        <option value="req005">农业绿色发展技术指南需求</option>
                                    </select>
                                </div>
                                
                                <!-- 指南适用范围 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        指南适用范围 <span class="text-red-500">*</span>
                                    </label>
                                    <textarea 
                                        rows="3" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                        placeholder="请描述指南的适用范围，如适用地区、适用对象、适用场景等"
                                    ></textarea>
                                </div>
                                
                                <!-- 有效期限 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        有效期限 <span class="text-red-500">*</span>
                                    </label>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label class="block text-xs text-gray-500 mb-1">开始日期</label>
                                            <input 
                                                type="date" 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            />
                                        </div>
                                        <div>
                                            <label class="block text-xs text-gray-500 mb-1">结束日期</label>
                                            <input 
                                                type="date" 
                                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                            />
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 计划完成时间 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        计划完成时间 <span class="text-red-500">*</span>
                                    </label>
                                    <input 
                                        type="datetime-local" 
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    />
                                </div>
                            </div>
                            
                            <!-- 右侧表单 -->
                            <div class="space-y-6">
                                <!-- 编制要求 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        编制要求 <span class="text-red-500">*</span>
                                    </label>
                                    <div class="border border-gray-300 rounded-md">
                                        <!-- 富文本编辑器工具栏 -->
                                        <div class="border-b border-gray-200 p-2 bg-gray-50 flex gap-1">
                                            <button class="p-1 hover:bg-gray-200 rounded" title="加粗">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M6 4v12h4.5c2.5 0 4.5-2 4.5-4.5 0-1.5-.8-2.8-2-3.5 1.2-.7 2-2 2-3.5C15 2 13 0 10.5 0H6v4zm2-2h2.5C11.3 2 12 2.7 12 3.5S11.3 5 10.5 5H8V2zm0 5h3c1.1 0 2 .9 2 2s-.9 2-2 2H8V7z"/>
                                                </svg>
                                            </button>
                                            <button class="p-1 hover:bg-gray-200 rounded" title="斜体">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M8 2h8v2h-2.5l-3 12H13v2H5v-2h2.5l3-12H8V2z"/>
                                                </svg>
                                            </button>
                                            <button class="p-1 hover:bg-gray-200 rounded" title="下划线">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M4 18h12v2H4v-2zm6-16C7.8 2 6 3.8 6 6v6c0 2.2 1.8 4 4 4s4-1.8 4-4V6c0-2.2-1.8-4-4-4zm2 10c0 1.1-.9 2-2 2s-2-.9-2-2V6c0-1.1.9-2 2-2s2 .9 2 2v6z"/>
                                                </svg>
                                            </button>
                                            <div class="border-l border-gray-300 mx-1"></div>
                                            <button class="p-1 hover:bg-gray-200 rounded" title="项目符号">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M4 6a2 2 0 100-4 2 2 0 000 4zM4 12a2 2 0 100-4 2 2 0 000 4zM4 18a2 2 0 100-4 2 2 0 000 4zM8 5h10v2H8V5zM8 11h10v2H8v-2zM8 17h10v2H8v-2z"/>
                                                </svg>
                                            </button>
                                            <button class="p-1 hover:bg-gray-200 rounded" title="编号列表">
                                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M3 4h1v1H3V4zm0 3h1v1H3V7zm0 3h1v1H3v-1zm0 3h1v1H3v-1zM6 4h12v2H6V4zm0 4h12v2H6V8zm0 4h12v2H6v-2zm0 4h12v2H6v-2z"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <textarea 
                                            rows="8" 
                                            class="w-full px-3 py-2 border-0 focus:outline-none focus:ring-0 resize-none"
                                            placeholder="请详细描述指南编制的具体要求，包括内容结构、技术标准、格式规范等"
                                        ></textarea>
                                    </div>
                                </div>
                                
                                <!-- 目标处室 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        目标处室 <span class="text-red-500">*</span>
                                    </label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <option value="">请选择目标处室</option>
                                        <option value="dept001">农业技术推广处</option>
                                        <option value="dept002">农产品质量安全监管处</option>
                                        <option value="dept003">农村经济发展处</option>
                                        <option value="dept004">农业机械化管理处</option>
                                        <option value="dept005">农业生态环境处</option>
                                    </select>
                                </div>
                                
                                <!-- 优先级 -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">
                                        优先级
                                    </label>
                                    <div class="flex gap-4">
                                        <label class="flex items-center">
                                            <input type="radio" name="priority" value="high" class="mr-2 text-blue-600 focus:ring-blue-500">
                                            <span class="text-sm text-red-600">高</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="priority" value="medium" class="mr-2 text-blue-600 focus:ring-blue-500" checked>
                                            <span class="text-sm text-yellow-600">中</span>
                                        </label>
                                        <label class="flex items-center">
                                            <input type="radio" name="priority" value="low" class="mr-2 text-blue-600 focus:ring-blue-500">
                                            <span class="text-sm text-green-600">低</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 操作按钮 -->
                        <div class="flex justify-center gap-4 mt-8 pt-6 border-t border-gray-200">
                            <button class="px-6 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md transition-colors">
                                保存草稿
                            </button>
                            <button class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                                立即发布
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 任务列表区 -->
                <div id="content-list" class="tab-content hidden p-6">
                    <!-- 筛选区 -->
                    <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">任务状态</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部状态</option>
                                    <option value="pending">待认领</option>
                                    <option value="assigned">已分派</option>
                                    <option value="in_progress">进行中</option>
                                    <option value="completed">已完成</option>
                                    <option value="overdue">已逾期</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">目标处室</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部处室</option>
                                    <option value="dept001">农业技术推广处</option>
                                    <option value="dept002">农产品质量安全监管处</option>
                                    <option value="dept003">农村经济发展处</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">优先级</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部优先级</option>
                                    <option value="high">高</option>
                                    <option value="medium">中</option>
                                    <option value="low">低</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                                <div class="relative">
                                    <input 
                                        type="text" 
                                        placeholder="搜索任务编号或事项名称"
                                        class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                    <svg class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 任务表格 -->
                    <div class="overflow-hidden border border-gray-200 rounded-lg">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">任务编号</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求征集事项</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标处室</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适用范围</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划完成时间</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余天数</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">TASK-2024-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业技术推广指南编制需求</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业技术推广处</td>
                                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">适用于全省各级农业技术推广机构</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待认领</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-06-30 18:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">21天</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900">查看</button>
                                                <button class="text-green-600 hover:text-green-900">编辑</button>
                                                <button class="text-red-600 hover:text-red-900">撤销</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">TASK-2024-002</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农产品质量安全管理指南需求</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农产品质量安全监管处</td>
                                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">适用于农产品生产、加工、流通环节</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已分派</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-07-15 18:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">36天</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900">查看</button>
                                                <button class="text-green-600 hover:text-green-900">编辑</button>
                                                <button class="text-red-600 hover:text-red-900">撤销</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">TASK-2024-003</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农村电商发展指导意见需求</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农村经济发展处</td>
                                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">适用于农村电商平台及相关企业</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-08-01 18:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">53天</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900">查看</button>
                                                <button class="text-green-600 hover:text-green-900">编辑</button>
                                                <button class="text-red-600 hover:text-red-900">撤销</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">TASK-2024-004</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业机械化推进指南需求</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业机械化管理处</td>
                                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">适用于农业机械化服务组织</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已逾期</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-05-30 18:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">-10天</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900">查看</button>
                                                <button class="text-green-600 hover:text-green-900">编辑</button>
                                                <button class="text-red-600 hover:text-red-900">撤销</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">TASK-2024-005</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业绿色发展技术指南需求</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">农业生态环境处</td>
                                        <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">适用于绿色农业生产基地</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">已完成</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-05-15 18:00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <button class="text-blue-600 hover:text-blue-900">查看</button>
                                                <button class="text-gray-400 cursor-not-allowed">编辑</button>
                                                <button class="text-gray-400 cursor-not-allowed">撤销</button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="flex items-center justify-between mt-6">
                        <div class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">5</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 text-gray-500 bg-white rounded-md cursor-not-allowed">上一页</button>
                            <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 text-gray-500 bg-white rounded-md cursor-not-allowed">下一页</button>
                        </div>
                    </div>
                </div>
                
                <!-- 认领分派区 -->
                <div id="content-assign" class="tab-content hidden p-6">
                    <div class="max-w-4xl mx-auto">
                        <!-- 任务信息卡片 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="text-lg font-semibold text-blue-800 mb-4">任务信息</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <span class="text-sm font-medium text-gray-600">任务编号：</span>
                                    <span class="text-sm text-gray-900">TASK-2024-001</span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">需求征集事项：</span>
                                    <span class="text-sm text-gray-900">农业技术推广指南编制需求</span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">目标处室：</span>
                                    <span class="text-sm text-gray-900">农业技术推广处</span>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-600">计划完成时间：</span>
                                    <span class="text-sm text-gray-900">2024-06-30 18:00</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="text-sm font-medium text-gray-600">适用范围：</span>
                                    <span class="text-sm text-gray-900">适用于全省各级农业技术推广机构，包括省、市、县三级推广体系</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="text-sm font-medium text-gray-600">编制要求：</span>
                                    <span class="text-sm text-gray-900">制定农业技术推广工作标准化流程，明确推广方法、技术标准、质量控制等要求</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 认领分派表单 -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-6">任务认领与分派</h3>
                            
                            <!-- 主负责人 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    主负责人 <span class="text-red-500">*</span>
                                </label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择主负责人</option>
                                    <option value="user001">张三 - 高级农艺师</option>
                                    <option value="user002">李四 - 农业技术推广研究员</option>
                                    <option value="user003">王五 - 农业技术推广高级工程师</option>
                                    <option value="user004">赵六 - 农业技术推广工程师</option>
                                </select>
                            </div>
                            
                            <!-- 子任务列表 -->
                            <div class="mb-6">
                                <div class="flex justify-between items-center mb-4">
                                    <label class="block text-sm font-medium text-gray-700">
                                        子任务分派 <span class="text-red-500">*</span>
                                    </label>
                                    <button id="add-subtask" class="flex items-center gap-1 px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        添加子任务
                                    </button>
                                </div>
                                
                                <div id="subtask-list" class="space-y-4">
                                    <!-- 子任务项 -->
                                    <div class="subtask-item border border-gray-200 rounded-lg p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">子任务名称</label>
                                                <input 
                                                    type="text" 
                                                    placeholder="请输入子任务名称"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                    value="技术标准制定"
                                                />
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">负责人</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                                    <option value="">请选择负责人</option>
                                                    <option value="user001" selected>张三 - 高级农艺师</option>
                                                    <option value="user002">李四 - 农业技术推广研究员</option>
                                                    <option value="user003">王五 - 农业技术推广高级工程师</option>
                                                </select>
                                            </div>
                                            <div class="flex gap-2">
                                                <div class="flex-1">
                                                    <label class="block text-xs font-medium text-gray-600 mb-1">截止日期</label>
                                                    <input 
                                                        type="date" 
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                        value="2024-06-15"
                                                    />
                                                </div>
                                                <div class="flex items-end">
                                                    <button class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="subtask-item border border-gray-200 rounded-lg p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">子任务名称</label>
                                                <input 
                                                    type="text" 
                                                    placeholder="请输入子任务名称"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                    value="推广流程设计"
                                                />
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">负责人</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                                    <option value="">请选择负责人</option>
                                                    <option value="user001">张三 - 高级农艺师</option>
                                                    <option value="user002" selected>李四 - 农业技术推广研究员</option>
                                                    <option value="user003">王五 - 农业技术推广高级工程师</option>
                                                </select>
                                            </div>
                                            <div class="flex gap-2">
                                                <div class="flex-1">
                                                    <label class="block text-xs font-medium text-gray-600 mb-1">截止日期</label>
                                                    <input 
                                                        type="date" 
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                        value="2024-06-20"
                                                    />
                                                </div>
                                                <div class="flex items-end">
                                                    <button class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="subtask-item border border-gray-200 rounded-lg p-4">
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">子任务名称</label>
                                                <input 
                                                    type="text" 
                                                    placeholder="请输入子任务名称"
                                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                    value="质量控制体系"
                                                />
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-gray-600 mb-1">负责人</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                                    <option value="">请选择负责人</option>
                                                    <option value="user001">张三 - 高级农艺师</option>
                                                    <option value="user002">李四 - 农业技术推广研究员</option>
                                                    <option value="user003" selected>王五 - 农业技术推广高级工程师</option>
                                                </select>
                                            </div>
                                            <div class="flex gap-2">
                                                <div class="flex-1">
                                                    <label class="block text-xs font-medium text-gray-600 mb-1">截止日期</label>
                                                    <input 
                                                        type="date" 
                                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                                        value="2024-06-25"
                                                    />
                                                </div>
                                                <div class="flex items-end">
                                                    <button class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 备注 -->
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">备注说明</label>
                                <textarea 
                                    rows="3" 
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    placeholder="请输入任务分派的备注说明（可选）"
                                ></textarea>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="flex justify-center gap-4">
                                <button class="px-6 py-2 border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 rounded-md transition-colors">
                                    取消
                                </button>
                                <button class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                                    确认认领
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 选项卡切换功能
        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.id.replace('tab-', 'content-');
                    
                    // 移除所有活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'bg-blue-600', 'text-white');
                        btn.classList.add('text-blue-700', 'hover:bg-blue-200');
                    });
                    
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // 激活当前选项卡
                    button.classList.add('active', 'bg-blue-600', 'text-white');
                    button.classList.remove('text-blue-700', 'hover:bg-blue-200');
                    
                    document.getElementById(targetTab).classList.remove('hidden');
                });
            });
        }
        
        // 添加子任务功能
        function initSubtaskManagement() {
            const addButton = document.getElementById('add-subtask');
            const subtaskList = document.getElementById('subtask-list');
            
            if (addButton && subtaskList) {
                addButton.addEventListener('click', () => {
                    const newSubtask = createSubtaskElement();
                    subtaskList.appendChild(newSubtask);
                });
                
                // 为现有的删除按钮添加事件监听
                subtaskList.addEventListener('click', (e) => {
                    if (e.target.closest('button') && e.target.closest('button').querySelector('svg')) {
                        const subtaskItem = e.target.closest('.subtask-item');
                        if (subtaskItem && subtaskList.children.length > 1) {
                            subtaskItem.remove();
                        }
                    }
                });
            }
        }
        
        function createSubtaskElement() {
            const div = document.createElement('div');
            div.className = 'subtask-item border border-gray-200 rounded-lg p-4';
            div.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">子任务名称</label>
                        <input 
                            type="text" 
                            placeholder="请输入子任务名称"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        />
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">负责人</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option value="">请选择负责人</option>
                            <option value="user001">张三 - 高级农艺师</option>
                            <option value="user002">李四 - 农业技术推广研究员</option>
                            <option value="user003">王五 - 农业技术推广高级工程师</option>
                            <option value="user004">赵六 - 农业技术推广工程师</option>
                        </select>
                    </div>
                    <div class="flex gap-2">
                        <div class="flex-1">
                            <label class="block text-xs font-medium text-gray-600 mb-1">截止日期</label>
                            <input 
                                type="date" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                            />
                        </div>
                        <div class="flex items-end">
                            <button class="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            return div;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initTabs();
            initSubtaskManagement();
        });
    </script>
</body>
</html>