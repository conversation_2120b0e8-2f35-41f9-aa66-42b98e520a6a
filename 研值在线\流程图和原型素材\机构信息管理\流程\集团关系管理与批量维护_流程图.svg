<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">集团关系管理与批量维护流程图</text>

  <!-- 阶段一：查询与检索 -->
  <text x="700" y="80" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：查询与检索</text>
  
  <!-- 节点1: 用户查询 -->
  <g transform="translate(100, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户查询</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">设定查询条件</text>
  </g>

  <!-- 节点2: 数据检索 -->
  <g transform="translate(400, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据检索</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">集团及企业信息</text>
  </g>

  <!-- 节点3: 层级树视图 -->
  <g transform="translate(700, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">层级树视图</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">供用户浏览</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 135 Q 350 135 400 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 620 135 Q 660 135 700 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据操作与校验 -->
  <text x="700" y="230" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据操作与校验</text>

  <!-- 节点4: 用户操作 -->
  <g transform="translate(200, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户操作</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">新增或编辑</text>
  </g>

  <!-- 节点5: 前端校验 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">格式校验</text>
  </g>

  <!-- 节点6: 后台校验 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">后台校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">唯一性校验</text>
  </g>

  <!-- 连接线从层级树视图到用户操作 -->
  <path d="M 810 170 C 810 200, 300 220, 300 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 285 Q 450 285 500 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据处理与依赖检查 -->
  <text x="700" y="380" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与依赖检查</text>

  <!-- 节点7: 数据写入 -->
  <g transform="translate(200, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据写入</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">集团-企业关系</text>
  </g>

  <!-- 节点8: 依赖检查 -->
  <g transform="translate(500, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">依赖检查</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">科研活动、诚信记录</text>
  </g>

  <!-- 节点9: 批量导入 -->
  <g transform="translate(800, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">文件解析、模板验证</text>
  </g>

  <!-- 连接线从后台校验到数据写入 -->
  <path d="M 900 320 C 900 350, 300 370, 300 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 7 -> 8 -->
  <path d="M 400 435 Q 450 435 500 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线从后台校验到批量导入 -->
  <path d="M 900 320 Q 900 360 900 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="920" y="360" font-size="11" fill="#666">批量场景</text>

  <!-- 阶段四：结果处理与日志记录 -->
  <text x="700" y="530" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：结果处理与日志记录</text>

  <!-- 节点10: 视图刷新 -->
  <g transform="translate(150, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">视图刷新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">异步刷新层级视图</text>
  </g>

  <!-- 节点11: 操作日志 -->
  <g transform="translate(400, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">变更信息与操作者</text>
  </g>

  <!-- 节点12: 结果返回 -->
  <g transform="translate(650, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果返回</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">操作结果</text>
  </g>

  <!-- 节点13: 消息推送 -->
  <g transform="translate(900, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息推送</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">导入完成或异常报告</text>
  </g>

  <!-- 连接线从数据写入到视图刷新 -->
  <path d="M 300 470 Q 300 510 250 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线从依赖检查到操作日志 -->
  <path d="M 600 470 Q 600 510 500 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线从批量导入到结果返回和消息推送 -->
  <path d="M 900 470 Q 900 510 750 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 470 Q 900 510 1000 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：错误处理与反馈循环 -->
  <text x="700" y="680" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段五：错误处理与反馈循环</text>

  <!-- 节点14: 错误处理 -->
  <g transform="translate(300, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">错误处理</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">冲突阻断、依赖拒绝</text>
  </g>

  <!-- 节点15: 状态更新 -->
  <g transform="translate(600, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">批量任务状态</text>
  </g>

  <!-- 连接线从后台校验到错误处理 -->
  <path d="M 900 320 C 1000 350, 1000 650, 400 680, 400 700" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1020" y="500" font-size="11" fill="#666">校验失败</text>

  <!-- 连接线从依赖检查到错误处理 -->
  <path d="M 600 470 C 600 600, 400 650, 400 700" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="520" y="580" font-size="11" fill="#666">依赖冲突</text>

  <!-- 连接线从批量导入到状态更新 -->
  <path d="M 900 470 C 900 600, 700 650, 700 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从错误处理回到用户操作 -->
  <path d="M 300 735 C 200 750, 100 750, 50 750, 50 300, 50 285, 200 285" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="780" font-size="11" fill="#666">错误反馈循环</text>

  <!-- 反馈循环：从视图刷新回到层级树视图 -->
  <path d="M 250 620 C 250 850, 1200 850, 1200 200, 1200 135, 920 135" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="700" y="880" font-size="11" fill="#666">视图更新循环</text>

</svg>