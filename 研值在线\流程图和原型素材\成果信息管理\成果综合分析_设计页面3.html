<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果综合分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">成果综合分析</h1>

        <!-- 筛选控制区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            科技奖励
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            知识产权
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            论文期刊
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        <option>全部状态</option>
                        <option>已授权</option>
                        <option>申请中</option>
                        <option>已转化</option>
                        <option>未转化</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统计口径</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        <option>按年度统计</option>
                        <option>按季度统计</option>
                        <option>按月统计</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">地域范围</label>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                        <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option>浙江省</option>
                            <option>江苏省</option>
                            <option>上海市</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option>宁波市</option>
                            <option>杭州市</option>
                            <option>温州市</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option>全市</option>
                            <option>海曙区</option>
                            <option>江北区</option>
                            <option>鄞州区</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm">
                    分析
                </button>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">成果总量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">12,847</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="totalTrendChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">年度新增</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">2,156</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="newTrendChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">转化率</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">32.5%</p>
                    </div>
                    <div class="bg-yellow-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="conversionTrendChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">交易金额(万)</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">8,923</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="transactionTrendChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 分类统计区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">成果分类统计</h3>
                <div class="h-80">
                    <canvas id="categoryBarChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">成果分类占比</h3>
                <div class="h-80">
                    <canvas id="categoryPieChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 区域分布区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">区域分布分析</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">浙江省</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">宁波市</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                        <img src="https://via.placeholder.com/800x400?text=浙江省成果分布热力图" alt="区域分布地图" class="w-full h-full object-cover rounded-lg">
                    </div>
                </div>
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">区域排名</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">宁波市</p>
                                <p class="text-xs text-gray-500">高新技术开发区</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">3,245项</p>
                                <p class="text-xs text-gray-500">25.3%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">杭州市</p>
                                <p class="text-xs text-gray-500">西湖区</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">2,876项</p>
                                <p class="text-xs text-gray-500">22.4%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">温州市</p>
                                <p class="text-xs text-gray-500">鹿城区</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">2,134项</p>
                                <p class="text-xs text-gray-500">16.6%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">绍兴市</p>
                                <p class="text-xs text-gray-500">柯桥区</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">1,592项</p>
                                <p class="text-xs text-gray-500">12.4%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主体分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">创新主体分析</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">企业</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">高校</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">科研院所</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">主体成果排行榜</h4>
                    <div class="space-y-3">
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-500 mr-3">1</span>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">宁波大学</p>
                                <p class="text-xs text-gray-500">高校</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">1,245项</p>
                                <p class="text-xs text-gray-500">高价值成果: 156项</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-500 mr-3">2</span>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">宁波市科技研究院</p>
                                <p class="text-xs text-gray-500">科研院所</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">987项</p>
                                <p class="text-xs text-gray-500">高价值成果: 89项</p>
                            </div>
                        </div>
                        <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm font-medium text-gray-500 mr-3">3</span>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">宁波吉利汽车研究院</p>
                                <p class="text-xs text-gray-500">企业</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">756项</p>
                                <p class="text-xs text-gray-500">高价值成果: 67项</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">主体能力雷达图</h4>
                    <div class="h-64">
                        <canvas id="radarChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 转化交易与技术分布区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">转化交易与技术分布</h3>
            <div class="space-y-6">
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">转化交易趋势</h4>
                    <div class="h-80">
                        <canvas id="transactionChart"></canvas>
                    </div>
                </div>
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4">技术领域分布</h4>
                    <div class="h-80">
                        <canvas id="techStackedChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输出与分享区 -->
        <div class="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg p-4">
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出Excel
                </button>
                <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    生成PDF
                </button>
                <button class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    复制链接
                </button>
            </div>
        </div>
    </div>

    <!-- Chart.js 初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 指标趋势小图
            const createMiniChart = (id, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [12, 19, 3, 5, 2, 3],
                            borderColor: color,
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        }
                    }
                });
            };

            createMiniChart('totalTrendChart', '#3B82F6');
            createMiniChart('newTrendChart', '#10B981');
            createMiniChart('conversionTrendChart', '#F59E0B');
            createMiniChart('transactionTrendChart', '#8B5CF6');

            // 分类柱状图
            const categoryBarCtx = document.getElementById('categoryBarChart').getContext('2d');
            new Chart(categoryBarCtx, {
                type: 'bar',
                data: {
                    labels: ['科技奖励', '知识产权', '论文期刊', '技术标准', '新产品'],
                    datasets: [{
                        label: '成果数量',
                        data: [3245, 2876, 1987, 1654, 1432],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 分类饼图
            const categoryPieCtx = document.getElementById('categoryPieChart').getContext('2d');
            new Chart(categoryPieCtx, {
                type: 'pie',
                data: {
                    labels: ['科技奖励', '知识产权', '论文期刊', '技术标准', '新产品'],
                    datasets: [{
                        data: [3245, 2876, 1987, 1654, 1432],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'right' }
                    }
                }
            });

            // 雷达图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['成果数量', '转化率', '高价值成果', '产学研合作', '国际影响力'],
                    datasets: [
                        {
                            label: '宁波大学',
                            data: [85, 72, 68, 79, 65],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 2
                        },
                        {
                            label: '宁波市科技研究院',
                            data: [78, 65, 72, 70, 60],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: { display: true },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 转化交易趋势图
            const transactionCtx = document.getElementById('transactionChart').getContext('2d');
            new Chart(transactionCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '转化率(%)',
                            data: [25, 28, 30, 32, 35],
                            borderColor: 'rgba(59, 130, 246, 1)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            yAxisID: 'y'
                        },
                        {
                            label: '交易金额(万元)',
                            data: [5200, 6500, 7200, 8500, 9200],
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '转化率(%)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: { drawOnChartArea: false },
                            title: { display: true, text: '交易金额(万元)' }
                        }
                    }
                }
            });

            // 技术领域堆叠图
            const techStackedCtx = document.getElementById('techStackedChart').getContext('2d');
            new Chart(techStackedCtx, {
                type: 'bar',
                data: {
                    labels: ['新一代信息技术', '高端装备', '新材料', '新能源', '生物医药'],
                    datasets: [
                        {
                            label: '科技奖励',
                            data: [320, 280, 240, 180, 150],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)'
                        },
                        {
                            label: '知识产权',
                            data: [450, 380, 320, 250, 200],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)'
                        },
                        {
                            label: '论文期刊',
                            data: [380, 320, 280, 200, 180],
                            backgroundColor: 'rgba(245, 158, 11, 0.8)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: { stacked: true },
                        y: { stacked: true }
                    }
                }
            });
        });
    </script>
</body>
</html>