<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标引管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-semibold text-gray-900">数据治理平台</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-blue-600 border-b-2 border-blue-600 px-3 py-2 text-sm font-medium">标引管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">指南管理</a>
                    </div>
                </div>
                <div class="flex items-center">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-700">管理员</span>
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-medium">管</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">标引管理</h1>
            <p class="mt-2 text-gray-600">管理指南标引的新增、修改、删除与查询，确保标签标注的一致性与可追溯性</p>
        </div>

        <!-- 内容卡片 -->
        <div class="bg-white rounded-lg shadow-md h-[calc(100vh-200px)] flex flex-col">
            <!-- 条件筛选区 -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-800">筛选条件</h3>
                </div>
                
                <!-- 第一行：标引名称和标引类型 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- 标引名称搜索 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">标引名称</label>
                        <div class="relative">
                            <input type="text" placeholder="请输入标引名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <svg class="absolute right-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>

                    <!-- 标引类型 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">标引类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">全部类型</option>
                            <option value="tech">技术领域</option>
                            <option value="theme">关键主题</option>
                            <option value="scenario">应用场景</option>
                            <option value="industry">行业分类</option>
                        </select>
                    </div>
                </div>

                <!-- 第二行：启用状态和创建时间 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <!-- 启用状态 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 bg-blue-500 text-white rounded-md text-sm font-medium hover:bg-blue-600 transition-colors">全部</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">启用</button>
                            <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">停用</button>
                        </div>
                    </div>

                    <!-- 创建时间 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">创建时间</label>
                        <div class="flex space-x-2">
                            <input type="date" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <span class="flex items-center text-gray-500">至</span>
                            <input type="date" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="mb-6">
                        <div class="flex items-center justify-center mb-4">
                            <div class="flex items-center space-x-4">
                                <button class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors" onclick="searchTags()">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                    查询
                                </button>
                                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors" onclick="resetFilters()">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    重置
                                </button>
                            </div>
                        </div>
                        <div class="flex items-center justify-end">
                            <div class="flex items-center space-x-3">
                                <button class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors" onclick="openTagModal('add')">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增标引
                                </button>
                                <div class="relative">
                                    <button class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors" onclick="toggleImportExport()">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                                        </svg>
                                        导入导出
                                    </button>
                                    <!-- 导入导出下拉菜单 -->
                                    <div id="importExportMenu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-gray-200">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                下载模板
                                            </a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" onclick="openImportModal()">
                                                <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                批量导入
                                            </a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M4 7h16"></path>
                                                </svg>
                                                导出数据
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>

            <!-- 标引列表区 -->
            <div class="flex-1 overflow-hidden" style="height: 600px;">
                <div class="h-full overflow-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50 sticky top-0">
                            <tr>
                                <th class="w-12 px-6 py-3 text-left">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标引编码</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标引名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标引类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">父级标引</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                <th class="w-32 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <!-- 一级标引 -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">TECH001</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900">人工智能</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">技术领域</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">-</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">2024-01-15 10:30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('view', 'TECH001')">
                                            查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 hover:bg-green-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('edit', 'TECH001')">
                                            编辑
                                        </button>
                                        <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-50 px-2 py-1 rounded transition-colors" onclick="showTagReferences('TECH001')">
                                            引用
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 rounded transition-colors" onclick="deleteTag('TECH001')">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 二级标引 -->
                            <tr class="hover:bg-gray-50 bg-gray-25">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">TECH001001</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center pl-6">
                                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <span class="text-sm text-gray-900">机器学习</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">技术领域</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">人工智能</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">2024-01-15 10:35</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('view', 'TECH001001')">
                                            查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 hover:bg-green-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('edit', 'TECH001001')">
                                            编辑
                                        </button>
                                        <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-50 px-2 py-1 rounded transition-colors" onclick="showTagReferences('TECH001001')">
                                            引用
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 rounded transition-colors" onclick="deleteTag('TECH001001')">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 三级标引 -->
                            <tr class="hover:bg-gray-50 bg-gray-25">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">TECH001001001</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center pl-12">
                                        <span class="text-sm text-gray-900">深度学习</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">技术领域</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">机器学习</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">2024-01-15 10:40</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('view', 'TECH001001001')">
                                            查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 hover:bg-green-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('edit', 'TECH001001001')">
                                            编辑
                                        </button>
                                        <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-50 px-2 py-1 rounded transition-colors" onclick="showTagReferences('TECH001001001')">
                                            引用
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 rounded transition-colors" onclick="deleteTag('TECH001001001')">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- 其他标引示例 -->
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">THEME001</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-gray-900">数字化转型</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">关键主题</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">-</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">2024-01-16 09:15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('view', 'THEME001')">
                                            查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 hover:bg-green-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('edit', 'THEME001')">
                                            编辑
                                        </button>
                                        <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-50 px-2 py-1 rounded transition-colors" onclick="showTagReferences('THEME001')">
                                            引用
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 rounded transition-colors" onclick="deleteTag('THEME001')">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-6 py-4 text-sm font-medium text-gray-900">SCENE001</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900">智慧城市</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">应用场景</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">-</td>
                                <td class="px-6 py-4">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">停用</span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">2024-01-14 16:20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <div class="flex space-x-2">
                                        <button class="text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('view', 'SCENE001')">
                                            查看
                                        </button>
                                        <button class="text-green-600 hover:text-green-800 hover:bg-green-50 px-2 py-1 rounded transition-colors" onclick="openTagModal('edit', 'SCENE001')">
                                            编辑
                                        </button>
                                        <button class="text-orange-600 hover:text-orange-800 hover:bg-orange-50 px-2 py-1 rounded transition-colors" onclick="showTagReferences('SCENE001')">
                                            引用
                                        </button>
                                        <button class="text-red-600 hover:text-red-800 hover:bg-red-50 px-2 py-1 rounded transition-colors" onclick="deleteTag('SCENE001')">
                                            删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-700">共 156 条记录，每页显示</span>
                        <select class="px-2 py-1 border border-gray-300 rounded text-sm">
                            <option>10</option>
                            <option>20</option>
                            <option>50</option>
                        </select>
                        <span class="text-sm text-gray-700">条</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100">上一页</button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100">3</button>
                        <span class="text-sm text-gray-500">...</span>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100">16</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm hover:bg-gray-100">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标引编辑弹窗 -->
    <div id="tagModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">新增标引</h3>
                <button class="text-gray-400 hover:text-gray-600" onclick="closeTagModal()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="flex">
                <!-- 左侧编辑表单 -->
                <div class="flex-1 pr-6">
                    <div class="space-y-6">
                        <!-- 标引名称 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标引名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="tagName" placeholder="请输入标引名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="mt-1 text-sm text-red-500 hidden" id="tagNameError">标引名称不能为空</p>
                        </div>

                        <!-- 标引编码 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标引编码 <span class="text-red-500">*</span></label>
                            <div class="flex space-x-2">
                                <input type="text" id="tagCode" placeholder="系统自动生成或手动输入" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">自动生成</button>
                            </div>
                            <p class="mt-1 text-sm text-red-500 hidden" id="tagCodeError">标引编码已存在</p>
                        </div>

                        <!-- 标引类型 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标引类型 <span class="text-red-500">*</span></label>
                            <div class="grid grid-cols-2 gap-4">
                                <label class="flex items-center">
                                    <input type="radio" name="tagType" value="tech" class="mr-2 text-blue-600 focus:ring-blue-500">
                                    <span class="text-sm">技术领域</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="tagType" value="theme" class="mr-2 text-blue-600 focus:ring-blue-500">
                                    <span class="text-sm">关键主题</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="tagType" value="scenario" class="mr-2 text-blue-600 focus:ring-blue-500">
                                    <span class="text-sm">应用场景</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="tagType" value="industry" class="mr-2 text-blue-600 focus:ring-blue-500">
                                    <span class="text-sm">行业分类</span>
                                </label>
                            </div>
                        </div>

                        <!-- 父级标引 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">父级标引</label>
                            <select id="parentTag" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">无父级标引（作为一级标引）</option>
                                <option value="TECH001">人工智能</option>
                                <option value="TECH001001">├─ 机器学习</option>
                                <option value="THEME001">数字化转型</option>
                                <option value="SCENE001">智慧城市</option>
                            </select>
                        </div>

                        <!-- 启用状态 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">启用状态</label>
                            <div class="flex items-center">
                                <input type="checkbox" id="tagStatus" checked class="mr-2 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="text-sm text-gray-700">启用</span>
                            </div>
                        </div>

                        <!-- 描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                            <textarea id="tagDescription" rows="4" placeholder="请输入标引描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-3 pt-6">
                            <button class="px-6 py-2 bg-gray-100 text-gray-700 rounded-md font-medium hover:bg-gray-200 transition-colors" onclick="closeTagModal()">取消</button>
                            <button class="px-6 py-2 bg-blue-500 text-white rounded-md font-medium hover:bg-blue-600 transition-colors" onclick="saveTag()">保存</button>
                        </div>
                    </div>
                </div>

                <!-- 右侧标引引用面板 -->
                <div class="w-80 border-l border-gray-200 pl-6">
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-gray-900 mb-2">标引引用情况</h4>
                        <div class="bg-blue-50 rounded-lg p-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">23</div>
                                <div class="text-sm text-gray-600">个指南引用此标引</div>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <h5 class="text-sm font-medium text-gray-700">引用指南列表</h5>
                        <div class="max-h-64 overflow-y-auto space-y-2">
                            <div class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer" onclick="openGuideDetail('G001')">
                                <div class="text-sm font-medium text-gray-900">人工智能技术发展指南</div>
                                <div class="text-xs text-gray-500 mt-1">发布时间：2024-01-10</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer" onclick="openGuideDetail('G002')">
                                <div class="text-sm font-medium text-gray-900">机器学习应用规范</div>
                                <div class="text-xs text-gray-500 mt-1">发布时间：2024-01-08</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer" onclick="openGuideDetail('G003')">
                                <div class="text-sm font-medium text-gray-900">深度学习框架选择指南</div>
                                <div class="text-xs text-gray-500 mt-1">发布时间：2024-01-05</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer">
                                <div class="text-sm font-medium text-gray-900">AI伦理与安全指导原则</div>
                                <div class="text-xs text-gray-500 mt-1">发布时间：2024-01-03</div>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 hover:bg-gray-100 cursor-pointer">
                                <div class="text-sm font-medium text-gray-900">智能算法评估标准</div>
                                <div class="text-xs text-gray-500 mt-1">发布时间：2023-12-28</div>
                            </div>
                        </div>
                        <div class="text-center">
                            <button class="text-blue-600 hover:text-blue-800 text-sm">查看全部引用</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入弹窗 -->
    <div id="importModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-900">批量导入标引</h3>
                <button class="text-gray-400 hover:text-gray-600" onclick="closeImportModal()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- 文件上传 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">选择文件</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </label>
                            <p class="mt-1 text-xs text-gray-500">支持 .xlsx, .xls 格式</p>
                        </div>
                    </div>
                </div>

                <!-- 上传进度 -->
                <div class="hidden" id="uploadProgress">
                    <label class="block text-sm font-medium text-gray-700 mb-2">上传进度</label>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">正在上传... 45%</p>
                </div>

                <!-- 校验结果 -->
                <div class="hidden" id="validationResult">
                    <label class="block text-sm font-medium text-gray-700 mb-2">校验结果</label>
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">发现 3 个错误</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>第2行：标引编码 "TECH001" 已存在</li>
                                        <li>第5行：父级标引 "TECH999" 不存在</li>
                                        <li>第8行：标引名称不能为空</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-3">
                    <button class="px-6 py-2 bg-gray-100 text-gray-700 rounded-md font-medium hover:bg-gray-200 transition-colors" onclick="closeImportModal()">取消</button>
                    <button class="px-6 py-2 bg-blue-500 text-white rounded-md font-medium hover:bg-blue-600 transition-colors">确认导入</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 切换导入导出菜单
        function toggleImportExport() {
            const menu = document.getElementById('importExportMenu');
            menu.classList.toggle('hidden');
        }

        // 点击其他地方关闭菜单
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('importExportMenu');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleImportExport') === -1) {
                menu.classList.add('hidden');
            }
        });

        // 打开标引编辑弹窗
        function openTagModal(mode = 'create', tagId = null) {
            const modal = document.getElementById('tagModal');
            const title = document.getElementById('modalTitle');
            
            if (mode === 'create') {
                title.textContent = '新增标引';
                // 清空表单
                document.getElementById('tagName').value = '';
                document.getElementById('tagCode').value = '';
                document.getElementById('tagDescription').value = '';
                document.getElementById('tagStatus').checked = true;
            } else if (mode === 'edit') {
                title.textContent = '编辑标引';
                // 加载标引数据
                loadTagData(tagId);
            } else if (mode === 'view') {
                title.textContent = '查看标引';
                // 加载标引数据并设为只读
                loadTagData(tagId);
                setFormReadonly(true);
            }
            
            modal.classList.remove('hidden');
        }

        // 关闭标引编辑弹窗
        function closeTagModal() {
            const modal = document.getElementById('tagModal');
            modal.classList.add('hidden');
            setFormReadonly(false);
        }

        // 查看标引
        function viewTag(tagId) {
            openTagModal('view', tagId);
        }

        // 编辑标引
        function editTag(tagId) {
            openTagModal('edit', tagId);
        }

        // 加载标引数据
        function loadTagData(tagId) {
            // 模拟加载数据
            if (tagId === 'TECH001') {
                document.getElementById('tagName').value = '人工智能';
                document.getElementById('tagCode').value = 'TECH001';
                document.getElementById('tagDescription').value = '人工智能相关技术领域标引';
                document.querySelector('input[name="tagType"][value="tech"]').checked = true;
            }
        }

        // 设置表单只读
        function setFormReadonly(readonly) {
            const inputs = document.querySelectorAll('#tagModal input, #tagModal select, #tagModal textarea');
            inputs.forEach(input => {
                input.disabled = readonly;
            });
        }

        // 保存标引
        function saveTag() {
            // 表单验证
            const tagName = document.getElementById('tagName').value.trim();
            const tagCode = document.getElementById('tagCode').value.trim();
            
            if (!tagName) {
                document.getElementById('tagNameError').classList.remove('hidden');
                return;
            }
            
            if (!tagCode) {
                document.getElementById('tagCodeError').textContent = '标引编码不能为空';
                document.getElementById('tagCodeError').classList.remove('hidden');
                return;
            }
            
            // 模拟保存
            alert('标引保存成功！');
            closeTagModal();
        }

        // 打开导入弹窗
        function openImportModal() {
            const modal = document.getElementById('importModal');
            modal.classList.remove('hidden');
        }

        // 关闭导入弹窗
        function closeImportModal() {
            const modal = document.getElementById('importModal');
            modal.classList.add('hidden');
        }

        // 打开指南详情
        function openGuideDetail(guideId) {
            window.open(`/guide-detail.html?id=${guideId}`, '_blank');
        }

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 显示上传进度
                document.getElementById('uploadProgress').classList.remove('hidden');
                
                // 模拟上传和校验过程
                setTimeout(() => {
                    document.getElementById('uploadProgress').classList.add('hidden');
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
    </script>
</body>
</html>