'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

// 测试用的renderMarkdown函数（简化版）
const renderMarkdown = (content: string) => {
  if (!content) return content

  // 处理行内元素
  let processed = content
    .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded-lg my-3 overflow-x-auto"><code class="text-sm font-mono whitespace-pre-wrap">$1</code></pre>')
    .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>')
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
    .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>')

  // 按行处理
  const lines = processed.split('\n')
  let result = ''
  let currentParagraph = ''

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const trimmed = line.trim()

    // 空行处理
    if (trimmed === '') {
      if (currentParagraph) {
        result += `<p class="mb-4 leading-relaxed text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }
      result += '<div class="mb-2"></div>'
      continue
    }

    // 普通文本行
    if (currentParagraph) {
      currentParagraph += '<br>' + trimmed
    } else {
      currentParagraph = trimmed
    }
  }

  // 处理最后的内容
  if (currentParagraph) {
    result += `<p class="mb-4 leading-relaxed text-gray-700 break-words">${currentParagraph.trim()}</p>`
  }

  return result
}

export default function TestNewlinesPage() {
  // 测试内容（模拟您提供的历史消息）
  const testContent = `您好，您想了解宁波知识产权保护中心预审领域对应的IPC分类号。根据官方信息，预审领域主要 分为汽车及零部件和智能制造两大产业领域，共包含177个IPC分类号。

具体分类号清单可通过以下方式获取：
宁波知识产权保护中心分类号（汽车及零部件领域94个+智能制造产业领域83个）
<a href="https://www.nbippc.cn/upload/202010/26/202417301149277534.pdf">分类号官网PDF版（177个）</a>

您目前的技术方案主要涉及哪个具体领域？我可以帮您进一步确认是否符合预审范围`

  return (
    <div className="h-screen bg-gradient-to-b from-blue-50/30 to-white/80 p-4">
      <Card className="h-full shadow-lg rounded-xl border-border/50">
        <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-xl">
          <CardTitle className="flex items-center gap-3 text-xl">
            换行符处理测试
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 p-6 bg-gradient-to-b from-blue-50/30 to-white/80 overflow-auto">
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">原始内容（带换行符）</h2>
              <pre className="bg-gray-100 p-4 rounded text-sm whitespace-pre-wrap">
{testContent}
              </pre>
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">渲染后效果</h2>
              <div 
                className="prose max-w-none"
                dangerouslySetInnerHTML={{
                  __html: renderMarkdown(testContent)
                }}
              />
            </div>
            
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">使用 whitespace-pre-wrap 的效果</h2>
              <div className="whitespace-pre-wrap break-words text-gray-700 leading-relaxed">
                {testContent}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
