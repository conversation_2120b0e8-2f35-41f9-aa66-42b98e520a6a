<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖励展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">奖励展示</h1>
                    <p class="mt-2 text-sm text-gray-600">集中呈现创新主体所获得的国家级、省级、市级及其他类别科技奖项与专利奖信息</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出数据
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        添加奖项
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 奖项概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 国家级奖项 -->
            <div class="bg-white rounded-lg shadow-md p-6 flex items-start">
                <div class="flex-shrink-0 w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-500">国家级奖项</p>
                    <p class="text-2xl font-semibold text-gray-900 mt-1">24</p>
                    <div class="mt-2 h-10 w-20">
                        <canvas id="nationalChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 省级奖项 -->
            <div class="bg-white rounded-lg shadow-md p-6 flex items-start">
                <div class="flex-shrink-0 w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-500">省级奖项</p>
                    <p class="text-2xl font-semibold text-gray-900 mt-1">56</p>
                    <div class="mt-2 h-10 w-20">
                        <canvas id="provincialChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 市级奖项 -->
            <div class="bg-white rounded-lg shadow-md p-6 flex items-start">
                <div class="flex-shrink-0 w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-500">市级奖项</p>
                    <p class="text-2xl font-semibold text-gray-900 mt-1">128</p>
                    <div class="mt-2 h-10 w-20">
                        <canvas id="municipalChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 专利奖 -->
            <div class="bg-white rounded-lg shadow-md p-6 flex items-start">
                <div class="flex-shrink-0 w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4 flex-1">
                    <p class="text-sm font-medium text-gray-500">专利奖</p>
                    <p class="text-2xl font-semibold text-gray-900 mt-1">87</p>
                    <div class="mt-2 h-10 w-20">
                        <canvas id="patentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">筛选条件</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖项级别</label>
                    <select id="awardLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖项类别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            科技进步奖
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            技术发明奖
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            专利奖
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">获奖年度</label>
                    <div class="flex space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">开始年份</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                            <option value="2020">2020</option>
                        </select>
                        <span class="flex items-center text-gray-500">至</span>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                            <option value="2020">2020</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">授奖单位</label>
                    <input type="text" placeholder="请输入授奖单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="lg:col-span-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <div class="flex">
                        <input type="text" placeholder="请输入奖项名称、成果名称等关键词" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex justify-end mt-6 space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    应用筛选
                </button>
            </div>
        </div>

        <!-- 荣誉列表和图表区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 荣誉列表区 -->
            <div class="lg:col-span-2 bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">荣誉列表</h3>
                        <div class="text-sm text-gray-500">
                            共找到 <span class="font-medium text-gray-900">295</span> 条记录
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项级别</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授奖单位</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年度</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家科学技术进步奖</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">国家级</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国务院</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新型智能控制系统研发</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openModal('certificateModal')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省科技进步奖</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">省级</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省人民政府</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高性能传感器技术研究</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openModal('certificateModal')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市科技创新奖</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">市级</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源材料开发与应用</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openModal('certificateModal')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">中国专利奖</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">国家级</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家知识产权局</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一种新型环保材料及其制备方法</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openModal('certificateModal')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省专利奖</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">省级</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省知识产权局</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一种高效节能电机</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button onclick="openModal('certificateModal')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            显示第 1-5 条，共 295 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关图表区 -->
            <div class="space-y-6">
                <!-- 奖项趋势图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">奖项趋势</h3>
                        <button onclick="toggleChart('trendChart')" class="text-gray-500 hover:text-gray-700">
                            <svg id="trendChartIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="trendChart" class="h-64">
                        <canvas id="trendChartCanvas"></canvas>
                    </div>
                </div>

                <!-- 奖项类别分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">奖项类别分布</h3>
                        <button onclick="toggleChart('categoryChart')" class="text-gray-500 hover:text-gray-700">
                            <svg id="categoryChartIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="categoryChart" class="h-64">
                        <canvas id="categoryChartCanvas"></canvas>
                    </div>
                </div>

                <!-- 证书状态统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">证书状态统计</h3>
                        <button onclick="toggleChart('statusChart')" class="text-gray-500 hover:text-gray-700">
                            <svg id="statusChartIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                    <div id="statusChart" class="h-64">
                        <canvas id="statusChartCanvas"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 证书预览抽屉 -->
    <div id="certificateModal" class="fixed inset-y-0 right-0 w-full max-w-2xl bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 modal-overlay">
        <div class="flex flex-col h-full">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">证书预览</h3>
                <div class="flex space-x-3">
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                    </button>
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17H7m4 0H5a2 2 0 01-2-2V5a2 2 0 012-2h2a2 2 0 012 2v2m3 0v6m0 0v6a2 2 0 01-2 2h-2a2 2 0 01-2-2v-6m3 0V5a2 2 0 00-2-2h-2a2 2 0 00-2 2v2m3 0h6m-3 0h6m2 8H5a2 2 0 01-2-2v-6a2 2 0 012-2h2a2 2 0 012 2v6a2 2 0 01-2 2zm7-5a2 2 0 11-4 0 2 0 014 0z"></path>
                        </svg>
                    </button>
                    <button onclick="closeModal('certificateModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <div class="bg-gray-100 rounded-lg p-4 flex items-center justify-center">
                        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='500' height='350' viewBox='0 0 500 350'%3E%3Crect width='500' height='350' fill='white' stroke='%23e5e7eb' stroke-width='1'/%3E%3Ctext x='250' y='150' font-family='Arial' font-size='24' fill='%236b7280' text-anchor='middle'%3E证书预览图%3C/text%3E%3Ctext x='250' y='180' font-family='Arial' font-size='16' fill='%239ca3af' text-anchor='middle'%3E国家科学技术进步奖证书%3C/text%3E%3C/svg%3E" alt="证书预览" class="max-w-full max-h-[60vh] object-contain">
                    </div>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">证书信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">奖项名称：</span>
                                <span class="font-medium text-gray-900">国家科学技术进步奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">证书编号：</span>
                                <span class="font-medium text-gray-900">2023-J-235-2-01-D01</span>
                            </div>
                            <div>
                                <span class="text-gray-500">获奖等级：</span>
                                <span class="font-medium text-gray-900">二等奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">获奖年度：</span>
                                <span class="font-medium text-gray-900">2023年</span>
                            </div>
                            <div>
                                <span class="text-gray-500">授奖单位：</span>
                                <span class="font-medium text-gray-900">中华人民共和国国务院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">颁奖日期：</span>
                                <span class="font-medium text-gray-900">2023年12月10日</span>
                            </div>
                            <div class="md:col-span-2">
                                <span class="text-gray-500">成果名称：</span>
                                <span class="font-medium text-gray-900">新型智能控制系统研发与产业化应用</span>
                            </div>
                            <div class="md:col-span-2">
                                <span class="text-gray-500">主要完成单位：</span>
                                <span class="font-medium text-gray-900">宁波市智能科技有限公司、浙江大学宁波理工学院、宁波市工业自动化研究院</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    返回列表定位
                </button>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        打印证书
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                        </svg>
                        下载证书
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                // 对于抽屉式模态框
                if (modalId === 'certificateModal') {
                    modal.classList.remove('translate-x-full');
                } else {
                    modal.classList.remove('hidden');
                }
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                // 对于抽屉式模态框
                if (modalId === 'certificateModal') {
                    modal.classList.add('translate-x-full');
                } else {
                    modal.classList.add('hidden');
                }
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 图表折叠/展开功能
        function toggleChart(chartId) {
            const chartElement = document.getElementById(chartId);
            const iconElement = document.getElementById(chartId + 'Icon');
            
            if (chartElement.style.display === 'none') {
                chartElement.style.display = 'block';
                iconElement.style.transform = 'rotate(0deg)';
            } else {
                chartElement.style.display = 'none';
                iconElement.style.transform = 'rotate(180deg)';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化微型雷达图
            const createMiniRadarChart = (id, data) => {
                const ctx = document.getElementById(id).getContext('2d');
                return new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['科技进步奖', '技术发明奖', '专利奖'],
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.7)',
                                'rgba(16, 185, 129, 0.7)',
                                'rgba(245, 158, 11, 0.7)'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        cutout: '70%',
                        plugins: { legend: { display: false } },
                        elements: { arc: { borderWidth: 0 } }
                    }
                });
            };

            // 初始化概览区微型图表
            createMiniRadarChart('nationalChart', [15, 5, 4]);
            createMiniRadarChart('provincialChart', [30, 15, 11]);
            createMiniRadarChart('municipalChart', [70, 35, 23]);
            createMiniRadarChart('patentChart', [0, 0, 87]);

            // 奖项趋势图
            const trendCtx = document.getElementById('trendChartCanvas').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '国家级',
                            data: [12, 15, 18, 21, 24],
                            borderColor: 'rgb(239, 68, 68)',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '省级',
                            data: [30, 35, 42, 48, 56],
                            borderColor: 'rgb(245, 158, 11)',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '市级',
                            data: [80, 95, 105, 118, 128],
                            borderColor: 'rgb(59, 130, 246)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '奖项数量'
                            }
                        }
                    }
                }
            });

            // 奖项类别分布
            const categoryCtx = document.getElementById('categoryChartCanvas').getContext('2d');
            new Chart(categoryCtx, {
                type: 'bar',
                data: {
                    labels: ['科技进步奖', '技术发明奖', '专利奖', '其他'],
                    datasets: [{
                        label: '奖项数量',
                        data: [156, 85, 87, 32],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 证书状态统计
            const statusCtx = document.getElementById('statusChartCanvas').getContext('2d');
            new Chart(statusCtx, {
                type: 'pie',
                data: {
                    labels: ['有效', '即将过期', '已过期', '未上传'],
                    datasets: [{
                        data: [275, 12, 5, 3],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('translate-x-full')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>