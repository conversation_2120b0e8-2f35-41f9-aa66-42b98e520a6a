unit HelpFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.OleCtrls, SHDocVw_EWB, EwbCore,
  EmbeddedWB;

type
  THelpForm = class(TForm)
    EmbeddedWB1: TEmbeddedWB;
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  HelpForm: THelpForm;

implementation

{$R *.dfm}

procedure THelpForm.FormShow(Sender: TObject);
begin
  EmbeddedWB1.ClearCache;
  self.EmbeddedWB1.LoadSettings;
  self.EmbeddedWB1.Go(ExtractFilePath(Application.ExeName) +
    'Templet\help.html');

end;

end.
