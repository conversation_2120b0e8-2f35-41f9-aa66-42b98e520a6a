unit CcYsGtbdj;

interface
uses
  Classes;

type
  TCcYsGtbdj = class
  private
    FYsgtbdjid: Integer;
    FYsid: Integer;
    FTgs: Double;
    FPbj: Double;
    FGtbcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysgtbdjid: integer read FYsgtbdjid write FYsgtbdjid;
    property Ysid: integer read FYsid write FYsid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Gtbcb: double read FGtbcb write FGtbcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

