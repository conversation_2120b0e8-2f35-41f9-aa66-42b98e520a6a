'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  FileText, 
  Filter, 
  RefreshCcw, 
  BarChart2,
  Calendar,
  Download,
  Upload,
  Settings,
  AlertCircle
} from "lucide-react"
import { InfrastructureTable } from "@/components/assets/infrastructure-table"
import { InfrastructureStatus } from "@/components/assets/infrastructure-status"
import { InfrastructureAlerts } from "@/components/assets/infrastructure-alerts"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function InfrastructurePage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      {/* 头部区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-medium text-gray-900">基础设施资产</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Settings className="mr-2 h-4 w-4" />
              配置管理
            </Button>
            <Button variant="outline" size="sm">
              <AlertCircle className="mr-2 h-4 w-4" />
              问题反馈
            </Button>
          </div>
        </div>

        {/* 快速统计 */}
        <div className="grid grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">总设备数</p>
                  <p className="text-2xl font-semibold mt-1">1,286</p>
                </div>
                <Badge className="bg-blue-50 text-blue-600">
                  <BarChart2 className="h-4 w-4" />
                </Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">在线设备</p>
                  <p className="text-2xl font-semibold mt-1">1,156</p>
                </div>
                <Badge className="bg-green-50 text-green-600">89.9%</Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">告警设备</p>
                  <p className="text-2xl font-semibold mt-1">24</p>
                </div>
                <Badge className="bg-yellow-50 text-yellow-600">1.9%</Badge>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">故障设备</p>
                  <p className="text-2xl font-semibold mt-1">8</p>
                </div>
                <Badge className="bg-red-50 text-red-600">0.6%</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 筛选和操作区 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="设备类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部设备</SelectItem>
                  <SelectItem value="server">服务器</SelectItem>
                  <SelectItem value="storage">存储设备</SelectItem>
                  <SelectItem value="network">网络设备</SelectItem>
                  <SelectItem value="security">安全设备</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="status">
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="设备状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="status">全部状态</SelectItem>
                  <SelectItem value="online">在线</SelectItem>
                  <SelectItem value="offline">离线</SelectItem>
                  <SelectItem value="warning">告警</SelectItem>
                  <SelectItem value="error">故障</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                高级筛选
              </Button>
              <Button variant="outline">
                <RefreshCcw className="mr-2 h-4 w-4" />
                刷新数据
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                导入
              </Button>
              <Button variant="outline">
                <Download className="mr-2 h-4 w-4" />
                导出
              </Button>
              <Button className="bg-blue-500 hover:bg-blue-600">
                <FileText className="mr-2 h-4 w-4" />
                生成报表
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 状态和告警概览 */}
      <div className="grid grid-cols-2 gap-6">
        <InfrastructureStatus />
        <InfrastructureAlerts />
      </div>
      
      {/* 资产列表 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>资产列表</CardTitle>
            <Tabs defaultValue="all" className="w-[400px]">
              <TabsList>
                <TabsTrigger value="all">全部</TabsTrigger>
                <TabsTrigger value="server">服务器</TabsTrigger>
                <TabsTrigger value="storage">存储</TabsTrigger>
                <TabsTrigger value="network">网络</TabsTrigger>
                <TabsTrigger value="security">安全</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <InfrastructureTable />
        </CardContent>
      </Card>
    </div>
  )
} 