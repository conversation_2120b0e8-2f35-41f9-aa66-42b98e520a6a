<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1200" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">专利管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入专利管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">权限验证</text>
  </g>

  <!-- 节点2: 数据加载 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">加载可见专利数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成统计指标，状态：已加载</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 500 165 Q 650 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点3: 用户提交筛选 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录参数</text>
  </g>

  <!-- 节点4: 专利检索服务 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专利检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取数据，状态：查询中</text>
  </g>

  <!-- 节点5: 关联服务 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取项目和主体信息</text>
  </g>

  <!-- 节点6: 页面刷新 -->
  <g transform="translate(1100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已完成</text>
  </g>

  <!-- 连接线 数据加载 -> 筛选条件 -->
  <path d="M 850 200 C 850 250, 350 250, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 筛选 -> 检索 -> 关联 -> 刷新 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 355 Q 1050 355 1100 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点7: 新增编辑操作 -->
  <g transform="translate(50, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增/编辑操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">字段合法性校验</text>
  </g>

  <!-- 节点8: 运营管理 -->
  <g transform="translate(270, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">运营管理</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">转移/许可/质押</text>
  </g>

  <!-- 节点9: 批量导入 -->
  <g transform="translate(490, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">文件格式校验</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(710, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">生成Excel文件</text>
  </g>

  <!-- 节点11: 缓存刷新 -->
  <g transform="translate(930, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">清空本地缓存</text>
  </g>

  <!-- 节点12: 数据库更新 -->
  <g transform="translate(1150, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据库更新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">写入专利库</text>
  </g>

  <!-- 连接线 页面刷新 -> 各种操作 -->
  <path d="M 1150 390 C 1150 450, 150 450, 140 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1150 390 C 1150 450, 360 450, 360 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1150 390 C 1150 450, 580 450, 580 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1150 390 C 1150 450, 800 450, 800 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1150 390 C 1150 450, 1020 450, 1020 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 操作 -> 数据库更新 -->
  <path d="M 230 555 C 500 555, 1000 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 555 C 700 555, 1000 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 555 C 850 555, 1000 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 555 C 1000 555, 1100 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1110 555 Q 1130 555 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>

  <!-- 节点13: 定时同步任务 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步任务</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">每日同步国家知识产权局与地方数据源</text>
  </g>

  <!-- 节点14: 数据比对更新 -->
  <g transform="translate(550, 720)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据比对更新</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">专利状态、法律事件及运营记录</text>
  </g>

  <!-- 节点15: 质量监控 -->
  <g transform="translate(900, 720)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量监控</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">确保数据实时性与准确性</text>
  </g>

  <!-- 连接线 数据库更新 -> 定时同步 -->
  <path d="M 1240 590 C 1240 650, 350 650, 350 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 定时同步 -> 数据比对 -->
  <path d="M 500 755 Q 525 755 550 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 数据比对 -> 质量监控 -->
  <path d="M 850 755 Q 875 755 900 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：质量监控 -> 数据加载 -->
  <path d="M 1050 720 C 1300 720, 1300 100, 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1350" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1350, 400)">数据同步反馈</text>

  <!-- 运营管理详细流程 -->
  <g transform="translate(200, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">权属状态校验</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">转移/许可/质押</text>
  </g>

  <g transform="translate(400, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">写入运营台账</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">记录运营信息</text>
  </g>

  <g transform="translate(600, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">同步更新状态</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">运营状态及指标</text>
  </g>

  <!-- 运营管理流程连接线 -->
  <path d="M 350 875 Q 375 875 400 875" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 875 Q 575 875 600 875" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接运营管理到详细流程 -->
  <path d="M 360 590 C 360 750, 275 750, 275 850" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />

  <!-- 批量导入详细流程 -->
  <g transform="translate(800, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">异步解析数据</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">后台处理</text>
  </g>

  <g transform="translate(1000, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">生成导入报告</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">推送至消息中心</text>
  </g>

  <!-- 批量导入流程连接线 -->
  <path d="M 950 875 Q 975 875 1000 875" stroke="#4CAF50" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接批量导入到详细流程 -->
  <path d="M 580 590 C 580 750, 875 750, 875 850" stroke="#4CAF50" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />

</svg>