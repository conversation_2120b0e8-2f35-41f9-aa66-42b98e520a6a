<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员社会活动情况管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">科研管理人员社会活动情况管理</h1>
                <p class="text-gray-600 mt-1">系统化管理和维护科研管理人员参与各类社会活动的详细信息</p>
            </div>
            <div class="flex space-x-3 mt-4 md:mt-0">
                <div class="relative" id="exportDropdown">
                    <button onclick="toggleExportDropdown()" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        导出数据
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 hidden z-10">
                        <div class="py-1">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为PDF</a>
                        </div>
                    </div>
                </div>
                <button onclick="openModal('addActivityModal')" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    新增活动
                </button>
            </div>
        </div>

        <!-- 查询筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-5 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">查询条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="managerName" class="block text-sm font-medium text-gray-700 mb-1">科研管理人员姓名</label>
                    <input type="text" id="managerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                </div>
                <div>
                    <label for="organization" class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                    <select id="organization" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部单位</option>
                        <option value="ningbo-university">宁波大学</option>
                        <option value="ningbo-institute">宁波市科学技术研究院</option>
                        <option value="ningbo-hospital">宁波市第一医院</option>
                        <option value="ningbo-enterprise">宁波高新技术企业</option>
                    </select>
                </div>
                <div>
                    <label for="activityType" class="block text-sm font-medium text-gray-700 mb-1">活动类型</label>
                    <select id="activityType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="academic">学术交流</option>
                        <option value="industry">行业会议</option>
                        <option value="consultation">咨询服务</option>
                        <option value="evaluation">评审评估</option>
                        <option value="training">培训讲座</option>
                    </select>
                </div>
                <div>
                    <label for="activityName" class="block text-sm font-medium text-gray-700 mb-1">活动名称</label>
                    <input type="text" id="activityName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入活动名称">
                </div>
                <div class="md:col-span-2 lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">活动时间范围</label>
                    <div class="flex space-x-3">
                        <input type="date" id="startDate" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" id="endDate" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="mt-5 flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 活动信息列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">张明</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">学术交流</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024年浙江省科技创新论坛</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-16 09:23</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                <button onclick="openModal('viewActivityModal')" class="text-blue-600 hover:text-blue-900 transition-colors duration-150">查看详情</button>
                                <button onclick="openModal('editActivityModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">编辑</button>
                                <button onclick="confirmDelete(1)" class="text-red-600 hover:text-red-900 transition-colors duration-150">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">李华</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市科学技术研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">行业会议</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024宁波智能制造产业发展峰会</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-10</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-11 14:56</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                <button onclick="openModal('viewActivityModal')" class="text-blue-600 hover:text-blue-900 transition-colors duration-150">查看详情</button>
                                <button onclick="openModal('editActivityModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">编辑</button>
                                <button onclick="confirmDelete(2)" class="text-red-600 hover:text-red-900 transition-colors duration-150">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王芳</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市第一医院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">咨询服务</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市医疗技术创新政策咨询会</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-05</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-03-06 10:15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                <button onclick="openModal('viewActivityModal')" class="text-blue-600 hover:text-blue-900 transition-colors duration-150">查看详情</button>
                                <button onclick="openModal('editActivityModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">编辑</button>
                                <button onclick="confirmDelete(3)" class="text-red-600 hover:text-red-900 transition-colors duration-150">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">赵强</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波高新技术企业</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">评审评估</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024年度宁波市科技项目评审会</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-02-28</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-02-29 16:42</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                <button onclick="openModal('viewActivityModal')" class="text-blue-600 hover:text-blue-900 transition-colors duration-150">查看详情</button>
                                <button onclick="openModal('editActivityModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">编辑</button>
                                <button onclick="confirmDelete(4)" class="text-red-600 hover:text-red-900 transition-colors duration-150">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">陈静</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">培训讲座</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">科研项目申报与管理专题培训</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-02-20</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">2024-02-21 09:30</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-3">
                                <button onclick="openModal('viewActivityModal')" class="text-blue-600 hover:text-blue-900 transition-colors duration-150">查看详情</button>
                                <button onclick="openModal('editActivityModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors duration-150">编辑</button>
                                <button onclick="confirmDelete(5)" class="text-red-600 hover:text-red-900 transition-colors duration-150">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">24</span> 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>上一页</button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">4</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">5</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增活动模态框 -->
    <div id="addActivityModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0" id="addActivityModalContent">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">新增社会活动记录</h3>
                <button onclick="closeModal('addActivityModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <form>
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="add-managerName" class="block text-sm font-medium text-gray-700 mb-1">科研管理人员姓名 <span class="text-red-500">*</span></label>
                                <input type="text" id="add-managerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                            </div>
                            <div>
                                <label for="add-organization" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                                <select id="add-organization" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择单位</option>
                                    <option value="ningbo-university">宁波大学</option>
                                    <option value="ningbo-institute">宁波市科学技术研究院</option>
                                    <option value="ningbo-hospital">宁波市第一医院</option>
                                    <option value="ningbo-enterprise">宁波高新技术企业</option>
                                </select>
                            </div>
                            <div>
                                <label for="add-activityType" class="block text-sm font-medium text-gray-700 mb-1">活动类型 <span class="text-red-500">*</span></label>
                                <select id="add-activityType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择活动类型</option>
                                    <option value="academic">学术交流</option>
                                    <option value="industry">行业会议</option>
                                    <option value="consultation">咨询服务</option>
                                    <option value="evaluation">评审评估</option>
                                    <option value="training">培训讲座</option>
                                </select>
                            </div>
                            <div>
                                <label for="add-activityDate" class="block text-sm font-medium text-gray-700 mb-1">活动时间 <span class="text-red-500">*</span></label>
                                <input type="date" id="add-activityDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="add-activityName" class="block text-sm font-medium text-gray-700 mb-1">活动名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="add-activityName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入活动名称">
                        </div>
                        <div>
                            <label for="add-activityLocation" class="block text-sm font-medium text-gray-700 mb-1">活动地点</label>
                            <input type="text" id="add-activityLocation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入活动地点">
                        </div>
                        <div>
                            <label for="add-activityDescription" class="block text-sm font-medium text-gray-700 mb-1">活动情况说明 <span class="text-red-500">*</span></label>
                            <textarea id="add-activityDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述活动情况、参与角色、主要成果等"></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel格式，单个文件不超过10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeModal('addActivityModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存记录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑活动模态框 -->
    <div id="editActivityModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0" id="editActivityModalContent">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">编辑社会活动记录</h3>
                <button onclick="closeModal('editActivityModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <form>
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="edit-managerName" class="block text-sm font-medium text-gray-700 mb-1">科研管理人员姓名 <span class="text-red-500">*</span></label>
                                <input type="text" id="edit-managerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张明">
                            </div>
                            <div>
                                <label for="edit-organization" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                                <select id="edit-organization" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择单位</option>
                                    <option value="ningbo-university" selected>宁波大学</option>
                                    <option value="ningbo-institute">宁波市科学技术研究院</option>
                                    <option value="ningbo-hospital">宁波市第一医院</option>
                                    <option value="ningbo-enterprise">宁波高新技术企业</option>
                                </select>
                            </div>
                            <div>
                                <label for="edit-activityType" class="block text-sm font-medium text-gray-700 mb-1">活动类型 <span class="text-red-500">*</span></label>
                                <select id="edit-activityType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择活动类型</option>
                                    <option value="academic" selected>学术交流</option>
                                    <option value="industry">行业会议</option>
                                    <option value="consultation">咨询服务</option>
                                    <option value="evaluation">评审评估</option>
                                    <option value="training">培训讲座</option>
                                </select>
                            </div>
                            <div>
                                <label for="edit-activityDate" class="block text-sm font-medium text-gray-700 mb-1">活动时间 <span class="text-red-500">*</span></label>
                                <input type="date" id="edit-activityDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2024-03-15">
                            </div>
                        </div>
                        <div>
                            <label for="edit-activityName" class="block text-sm font-medium text-gray-700 mb-1">活动名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="edit-activityName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2024年浙江省科技创新论坛">
                        </div>
                        <div>
                            <label for="edit-activityLocation" class="block text-sm font-medium text-gray-700 mb-1">活动地点</label>
                            <input type="text" id="edit-activityLocation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="浙江省人民大会堂">
                        </div>
                        <div>
                            <label for="edit-activityDescription" class="block text-sm font-medium text-gray-700 mb-1">活动情况说明 <span class="text-red-500">*</span></label>
                            <textarea id="edit-activityDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">参加2024年浙江省科技创新论坛，作为特邀嘉宾发表题为"宁波市科研管理创新与实践"的主题演讲，分享宁波市在科研管理方面的经验和成果。与省内多所高校和科研机构的同行进行了深入交流，建立了良好的合作关系。</textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">已上传附件</label>
                            <div class="flex items-center p-3 bg-gray-50 rounded-md mb-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017a2 2 0 01-1.789-2.894l-3.5-7A2 2 0 015.236 10H10m0 0V8m0 8v8m0-8a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">2024年浙江省科技创新论坛演讲稿.pdf</p>
                                    <p class="text-xs text-gray-500">2.4 MB · 上传于 2024-03-16</p>
                                </div>
                                <button type="button" class="ml-4 text-red-600 hover:text-red-900">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">上传新附件</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <div class="mt-4">
                                    <label for="edit-file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="edit-file-upload" name="edit-file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel格式，单个文件不超过10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 mt-6">
                        <button type="button" onclick="closeModal('editActivityModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            更新记录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 查看活动详情模态框 -->
    <div id="viewActivityModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0" id="viewActivityModalContent">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">社会活动详情</h3>
                <button onclick="closeModal('viewActivityModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">2024年浙江省科技创新论坛</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3 text-sm">
                        <div>
                            <span class="text-gray-500">科研管理人员姓名：</span>
                            <span class="font-medium text-gray-900">张明</span>
                        </div>
                        <div>
                            <span class="text-gray-500">所属单位：</span>
                            <span class="font-medium text-gray-900">宁波大学</span>
                        </div>
                        <div>
                            <span class="text-gray-500">活动类型：</span>
                            <span class="font-medium text-gray-900">学术交流</span>
                        </div>
                        <div>
                            <span class="text-gray-500">活动时间：</span>
                            <span class="font-medium text-gray-900">2024-03-15</span>
                        </div>
                        <div>
                            <span class="text-gray-500">活动地点：</span>
                            <span class="font-medium text-gray-900">浙江省人民大会堂</span>
                        </div>
                        <div>
                            <span class="text-gray-500">创建时间：</span>
                            <span class="font-medium text-gray-900">2024-03-16 09:23</span>
                        </div>
                        <div>
                            <span class="text-gray-500">创建人：</span>
                            <span class="font-medium text-gray-900">系统管理员</span>
                        </div>
                        <div>
                            <span class="text-gray-500">最后更新时间：</span>
                            <span class="font-medium text-gray-900">2024-03-16 09:23</span>
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-3">活动情况说明</h4>
                    <div class="bg-gray-50 p-4 rounded-md text-sm text-gray-700">
                        <p>参加2024年浙江省科技创新论坛，作为特邀嘉宾发表题为"宁波市科研管理创新与实践"的主题演讲，分享宁波市在科研管理方面的经验和成果。</p>
                        <p class="mt-2">与省内多所高校和科研机构的同行进行了深入交流，建立了良好的合作关系。论坛期间，与浙江大学、杭州电子科技大学等单位的科研管理人员就科研项目管理、科研成果转化等议题进行了专题研讨。</p>
                        <p class="mt-2">通过本次学术交流活动，获取了省内其他地区的先进经验，为进一步优化宁波市科研管理工作提供了参考。</p>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-3">相关附件</h4>
                    <div class="flex items-center p-3 bg-gray-50 rounded-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017a2 2 0 01-1.789-2.894l-3.5-7A2 2 0 015.236 10H10m0 0V8m0 8v8m0-8a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900 truncate">2024年浙江省科技创新论坛演讲稿.pdf</p>
                            <p class="text-xs text-gray-500">2.4 MB · 上传于 2024-03-16</p>
                        </div>
                        <button type="button" class="ml-4 text-blue-600 hover:text-blue-900">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button onclick="closeModal('viewActivityModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full transform transition-all duration-300 scale-95 opacity-0" id="deleteConfirmModalContent">
            <div class="p-6 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
                <p class="text-gray-500 mb-6">您确定要删除这条社会活动记录吗？此操作不可撤销。</p>
                <div class="flex justify-center space-x-3">
                    <button onclick="closeModal('deleteConfirmModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button onclick="confirmDeleteAction()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
                
                // 显示动画
                setTimeout(() => {
                    const content = document.getElementById(`${modalId}Content`);
                    if (content) {
                        content.classList.remove('scale-95', 'opacity-0');
                        content.classList.add('scale-100', 'opacity-100');
                    }
                }, 10);
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                const content = document.getElementById(`${modalId}Content`);
                if (content) {
                    content.classList.remove('scale-100', 'opacity-100');
                    content.classList.add('scale-95', 'opacity-0');
                }
                
                // 等待动画完成后隐藏
                setTimeout(() => {
                    modal.classList.add('hidden');
                    document.body.style.overflow = 'auto';
                }, 300);
            }
        }

        // 导出下拉菜单控制
        function toggleExportDropdown() {
            const options = document.getElementById('exportOptions');
            if (options) {
                options.classList.toggle('hidden');
            }
        }

        // 删除确认
        let currentDeleteId = null;
        function confirmDelete(id) {
            currentDeleteId = id;
            openModal('deleteConfirmModal');
        }

        function confirmDeleteAction() {
            if (currentDeleteId) {
                // 这里应该有实际的删除API调用
                console.log(`删除ID为${currentDeleteId}的记录`);
                alert('记录已成功删除');
                closeModal('deleteConfirmModal');
                currentDeleteId = null;
                // 实际应用中应该刷新列表数据
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 点击页面其他地方关闭导出下拉菜单
            document.addEventListener('click', function(event) {
                const exportDropdown = document.getElementById('exportDropdown');
                const exportOptions = document.getElementById('exportOptions');
                
                if (exportOptions && !exportOptions.classList.contains('hidden') && 
                    !exportDropdown.contains(event.target)) {
                    exportOptions.classList.add('hidden');
                }
            });
            
            // 为所有弹窗绑定“点击外部关闭”事件和ESC键关闭
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });
                
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
            
            // 文件上传处理
            const fileInputs = document.querySelectorAll('input[type="file"]');
            fileInputs.forEach(input => {
                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        const fileName = e.target.files[0].name;
                        alert(`文件 "${fileName}" 已选择 (原型演示，未实际上传)`);
                    }
                });
            });
        });
    </script>
</body>
</html>