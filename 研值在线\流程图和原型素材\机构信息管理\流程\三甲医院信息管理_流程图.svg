<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">三甲医院信息管理流程</text>

  <!-- 阶段一：数据录入与初始化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据录入与初始化</text>
  
  <!-- 节点1: 数据提交 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据提交</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(新增/批量导入)</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(必填项与唯一性)</text>
  </g>

  <!-- 节点3: 初始档案生成 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">初始档案生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(标记为"待完善")</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据补充与完善 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据补充与完善</text>

  <!-- 节点4: 外部数据补全 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部数据补全</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(共享目录自动补全)</text>
  </g>

  <!-- 节点5: 任务推送 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务推送</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(待维护任务至责任人)</text>
  </g>

  <!-- 节点6: 信息补录 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息补录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(责任人在待办中心)</text>
  </g>

  <!-- 连接线 初始档案 -> 外部补全 -->
  <path d="M 750 200 C 750 240, 350 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 外部补全 -> 任务推送 -> 信息补录 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据维护与管理 -->
  <text x="700" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据维护与管理</text>

  <!-- 节点7: 状态更新 -->
  <g transform="translate(150, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(更新为"已完善")</text>
  </g>

  <!-- 节点8: 关联管理 -->
  <g transform="translate(450, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(平台/设备/项目关系)</text>
  </g>

  <!-- 节点9: 删除管理 -->
  <g transform="translate(750, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(依赖检测与逻辑删除)</text>
  </g>

  <!-- 连接线 信息补录 -> 状态更新 -->
  <path d="M 850 390 C 850 430, 300 470, 250 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 状态更新 -> 关联管理 -> 删除管理 -->
  <path d="M 350 545 Q 400 545 450 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 545 Q 700 545 750 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：持续优化循环 -->
  <text x="700" y="660" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：持续优化循环</text>
  
  <!-- 节点10: 质量监控 -->
  <g transform="translate(300, 700)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量监控与治理</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">定期扫描低完整度信息</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">生成质量提升清单</text>
  </g>

  <!-- 节点11: 闭环治理 -->
  <g transform="translate(650, 700)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">闭环治理</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">确保医院信息长期准确</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">持续可用性保障</text>
  </g>

  <!-- 连接线 删除管理 -> 质量监控 -->
  <path d="M 800 580 C 800 620, 450 660, 425 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 质量监控 -> 闭环治理 -->
  <path d="M 550 740 Q 600 740 650 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 质量反馈循环 -->
  <path d="M 425 700 C 200 650, 200 400, 300 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="500" font-size="11" fill="#666" transform="rotate(-90, 150, 500)">质量反馈循环</text>

  <!-- 数据完善循环 -->
  <path d="M 775 700 C 1100 650, 1100 200, 900 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" font-size="11" fill="#666" transform="rotate(90, 1050, 400)">数据完善循环</text>

</svg>