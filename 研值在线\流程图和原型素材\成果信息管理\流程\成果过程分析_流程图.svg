<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果过程分析业务流程</text>

  <!-- 阶段一：任务初始化与记录 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：任务初始化与记录</text>
  
  <!-- 节点1: 用户提交筛选 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设置筛选条件</text>
  </g>

  <!-- 节点2: 记录分析任务 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录分析任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态：处理中</text>
  </g>

  <!-- 节点3: 写入操作日志 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入操作日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">便于后续复现</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据聚合与处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据聚合与处理</text>

  <!-- 节点4: 成果过程聚合 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果过程聚合</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">专利与论文维度</text>
  </g>

  <!-- 节点5: 提取原始记录 -->
  <g transform="translate(350, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">提取原始记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按维度分别提取</text>
  </g>

  <!-- 节点6: 生命周期归类 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生命周期归类</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按节点汇总</text>
  </g>

  <!-- 节点7: 计算过程指标 -->
  <g transform="translate(850, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">计算过程指标</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">中间数据集</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 300 355 Q 325 355 350 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 355 Q 575 355 600 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 355 Q 825 355 850 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：可视化渲染与缓存 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：可视化渲染与缓存</text>

  <!-- 节点8: 触发渲染流程 -->
  <g transform="translate(150, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">触发渲染流程</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">可视化处理</text>
  </g>

  <!-- 节点9: 写入前端缓存 -->
  <g transform="translate(400, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入前端缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">过程指标数据</text>
  </g>

  <!-- 节点10: 更新任务状态 -->
  <g transform="translate(650, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新任务状态</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已完成</text>
  </g>

  <!-- 节点11: 用户交互响应 -->
  <g transform="translate(900, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互响应</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">局部刷新组件</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 350 555 Q 375 555 400 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 555 Q 625 555 650 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 555 Q 875 555 900 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与同步 -->
  <text x="700" y="700" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与同步</text>
  
  <!-- 节点12: 导出操作 -->
  <g transform="translate(200, 740)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据导出服务</text>
    <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-80">筛选结果</tspan>
      <tspan dx="40">图表快照</tspan>
      <tspan dx="40">明细数据</tspan>
    </text>
  </g>

  <!-- 节点13: 增量同步 -->
  <g transform="translate(600, 740)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">定时增量同步</text>
    <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-80">专利数据</tspan>
      <tspan dx="40">论文数据</tspan>
      <tspan dx="40">过程索引</tspan>
    </text>
  </g>

  <!-- 连接线 阶段一到阶段二 -->
  <path d="M 600 200 C 600 240, 200 240, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 阶段二到阶段三 -->
  <path d="M 950 390 C 950 450, 250 450, 250 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 阶段三到阶段四 -->
  <path d="M 500 590 C 500 650, 350 650, 350 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 590 C 750 650, 750 650, 750 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 用户交互反馈循环 -->
  <path d="M 1000 590 C 1100 590, 1100 400, 950 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="500" text-anchor="middle" font-size="12" fill="#555">交互反馈</text>

</svg>