<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">院校基础信息展示流程图</text>

  <!-- 阶段一：页面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与数据加载</text>
  
  <!-- 节点1: 用户访问页面 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">院校基础信息页面</text>
  </g>

  <!-- 节点2: 院校主数据服务 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">院校主数据服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取基础字段、标签、统计指标</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：地图服务与图表渲染 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：地图服务与图表渲染</text>

  <!-- 节点3: 地图服务 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据地理坐标生成定位标记</text>
  </g>

  <!-- 节点4: 图表渲染组件 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">图表渲染组件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载学科实力等指标数据</text>
  </g>

  <!-- 连接线 数据服务 -> 地图服务 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据服务 -> 图表渲染 -->
  <path d="M 750 320 C 850 350, 950 380, 1000 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：用户交互与实时更新 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：用户交互与实时更新</text>
  
  <!-- 节点5: 特色标签交互 -->
  <g transform="translate(200, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">特色标签交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">高亮学科信息</text>
  </g>

  <!-- 节点6: 地图标记交互 -->
  <g transform="translate(600, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图标记交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">高亮校区信息</text>
  </g>

  <!-- 节点7: 实时指标对比 -->
  <g transform="translate(1000, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时指标对比</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">图表区更新展示</text>
  </g>

  <!-- 连接线 地图服务 -> 特色标签 -->
  <path d="M 350 490 C 320 520, 280 550, 300 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 地图服务 -> 地图标记 -->
  <path d="M 450 490 C 500 520, 600 550, 650 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 图表渲染 -> 实时指标 -->
  <path d="M 1000 490 C 1000 520, 1000 550, 1100 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：外部访问与反馈管理 -->
  <text x="700" y="780" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：外部访问与反馈管理</text>
  
  <!-- 节点8: 官网访问与日志记录 -->
  <g transform="translate(300, 820)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">官网访问与日志记录</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">记录点击行为，写入访问轨迹</text>
  </g>

  <!-- 节点9: 纠错反馈与数据更新 -->
  <g transform="translate(750, 820)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">纠错反馈与数据更新</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">生成工单，审核后更新数据</text>
  </g>

  <!-- 连接线 特色标签 -> 官网访问 -->
  <path d="M 300 690 C 300 720, 350 750, 375 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 实时指标 -> 纠错反馈 -->
  <path d="M 1100 690 C 1100 720, 950 750, 875 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：数据更新 -->
  <path d="M 750 820 C 200 750, 200 400, 300 490" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="650" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 250, 650)">数据更新反馈</text>

  <!-- 反馈循环：统计分析 -->
  <path d="M 425 820 C 100 750, 100 300, 600 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="550" text-anchor="middle" font-size="11" fill="#666">统计分析反馈</text>

</svg>