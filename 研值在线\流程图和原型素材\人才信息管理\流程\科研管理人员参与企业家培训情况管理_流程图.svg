<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="24" text-anchor="middle" font-weight="600" fill="#333">科研管理人员参与企业家培训情况管理流程</text>

  <!-- 阶段一：数据归集与标准化 -->
  <text x="700" y="100" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与标准化</text>
  
  <!-- 节点1: 多渠道数据源 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多渠道数据源</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">企业、平台、主管部门</text>
  </g>

  <!-- 节点2: 数据归集处理 -->
  <g transform="translate(350, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归集处理</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">标准化字段与关联</text>
  </g>

  <!-- 节点3: 培训数据库 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">培训数据库</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">结构化存储</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 280 165 Q 315 165 350 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 530 165 Q 565 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：查询检索与展示 -->
  <text x="700" y="270" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询检索与展示</text>

  <!-- 节点4: 筛选条件设置 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设置</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">姓名、主题、时间、单位</text>
  </g>

  <!-- 节点5: 即时检索 -->
  <g transform="translate(450, 300)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">即时检索</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">系统智能匹配</text>
  </g>

  <!-- 节点6: 培训记录列表 -->
  <g transform="translate(700, 300)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">培训记录列表</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">满足条件的记录</text>
  </g>

  <!-- 连接线 数据库 -> 筛选 -->
  <path d="M 690 200 C 690 230, 400 250, 290 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 380 335 Q 415 335 450 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 630 335 Q 665 335 700 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与操作 -->
  <text x="700" y="440" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与操作</text>

  <!-- 节点7: 详情查看 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">完整信息展示</text>
  </g>

  <!-- 节点8: 新增编辑 -->
  <g transform="translate(350, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增编辑</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">弹窗表单操作</text>
  </g>

  <!-- 节点9: 删除确认 -->
  <g transform="translate(550, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除确认</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">安全删除机制</text>
  </g>

  <!-- 节点10: 批量导出 -->
  <g transform="translate(750, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导出</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">数据文件生成</text>
  </g>

  <!-- 连接线 列表 -> 各操作 -->
  <path d="M 750 370 C 650 400, 350 430, 230 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 770 370 C 700 400, 500 430, 430 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 790 370 C 750 400, 700 430, 630 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 370 C 820 400, 830 430, 830 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统记录与审计 -->
  <text x="700" y="610" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统记录与审计</text>

  <!-- 节点11: 操作日志记录 -->
  <g transform="translate(300, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">新增、编辑、删除、导出</text>
  </g>

  <!-- 节点12: 合规性保障 -->
  <g transform="translate(550, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">合规性保障</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">数据完整性与可追溯</text>
  </g>

  <!-- 连接线 各操作 -> 日志记录 -->
  <path d="M 230 540 C 230 580, 350 600, 380 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 430 540 C 430 580, 420 600, 420 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 630 540 C 630 580, 480 600, 460 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 830 540 C 830 580, 500 600, 500 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 日志 -> 合规 -->
  <path d="M 500 675 Q 525 675 550 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从合规保障回到数据归集 -->
  <path d="M 650 640 C 1100 600, 1150 300, 1100 165 C 1050 165, 850 165, 780 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" text-anchor="middle" font-size="11" fill="#666">经验反馈</text>

  <!-- 反馈循环：从详情查看回到筛选条件 -->
  <path d="M 150 505 C 50 505, 50 335, 150 335 C 170 335, 190 335, 200 335" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="420" text-anchor="middle" font-size="11" fill="#666">重新筛选</text>

</svg>