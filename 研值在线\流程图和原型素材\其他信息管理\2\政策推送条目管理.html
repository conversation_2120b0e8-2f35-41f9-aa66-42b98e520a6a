<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策推送条目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">政策推送条目管理</h1>
                    <p class="mt-2 text-sm text-gray-600">对已计算出高匹配度的政策进行二次筛选、排期与状态跟踪，确保创新主体及时收到最相关的政策信息</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 批量导出下拉 -->
                    <div class="relative">
                        <button onclick="toggleExportDropdown()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            批量导出
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="exportDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">导出选项</h3>
                                <div class="space-y-3">
                                    <button onclick="exportData('targets')" class="w-full text-left p-3 bg-blue-50 rounded-lg hover:bg-blue-100 border border-blue-200">
                                        <div class="font-medium text-gray-900">导出目标主体列表</div>
                                        <div class="text-sm text-gray-600">按当前筛选条件导出推送对象信息</div>
                                    </button>
                                    <button onclick="exportData('logs')" class="w-full text-left p-3 bg-green-50 rounded-lg hover:bg-green-100 border border-green-200">
                                        <div class="font-medium text-gray-900">导出推送日志</div>
                                        <div class="text-sm text-gray-600">导出详细的推送记录与状态信息</div>
                                    </button>
                                </div>
                                <div class="mt-4 pt-3 border-t border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">导出历史</h4>
                                    <div class="space-y-2 max-h-32 overflow-y-auto">
                                        <div class="flex items-center justify-between text-xs text-gray-600">
                                            <span>目标主体列表_20240115.xlsx</span>
                                            <button class="text-blue-600 hover:text-blue-800">下载</button>
                                        </div>
                                        <div class="flex items-center justify-between text-xs text-gray-600">
                                            <span>推送日志_20240110.csv</span>
                                            <button class="text-blue-600 hover:text-blue-800">下载</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button onclick="openPushModal('create')" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新增推送
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">政策名称</label>
                    <input type="text" placeholder="请输入政策名称" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">首次推送时间</label>
                    <input type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">推送次数范围</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" placeholder="最小" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <span class="text-gray-500">-</span>
                        <input type="number" placeholder="最大" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">目标覆盖量</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部范围</option>
                        <option value="1-100">1-100</option>
                        <option value="101-500">101-500</option>
                        <option value="501-1000">501-1000</option>
                        <option value="1000+">1000+</option>
                    </select>
                </div>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm font-medium text-gray-700">推送状态：</span>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm rounded-full bg-white text-blue-600 border border-blue-300">待推送</button>
                        <button class="px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 border border-blue-300">推送中</button>
                        <button class="px-3 py-1 text-sm rounded-full bg-blue-600 text-white border border-blue-600">已完成</button>
                        <button class="px-3 py-1 text-sm rounded-full bg-white text-gray-600 border border-gray-300">已取消</button>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 推送总览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已推送条目数</p>
                        <p class="text-2xl font-semibold text-gray-900">1,234</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">待推送条目数</p>
                        <p class="text-2xl font-semibold text-gray-900">89</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">今日推送成功率</p>
                        <p class="text-2xl font-semibold text-gray-900">95.2%</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">今日取消率</p>
                        <p class="text-2xl font-semibold text-gray-900">2.1%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 推送列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">推送列表</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">首次推送时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新推送时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已推送次数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">推送状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标覆盖量</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">高新技术企业认定政策</div>
                                <div class="text-sm text-gray-500">支持高新技术企业发展的优惠政策</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-10 09:00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">已完成</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,256</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewPushDetail(1)" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="cancelPush(1)" class="text-yellow-600 hover:text-yellow-900">取消推送</button>
                                    <button onclick="exportTargets(1)" class="text-green-600 hover:text-green-900">导出对象</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">科技型中小企业评价政策</div>
                                <div class="text-sm text-gray-500">科技型中小企业入库评价相关政策</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-12 10:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-14 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">推送中</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">856</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewPushDetail(2)" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="cancelPush(2)" class="text-yellow-600 hover:text-yellow-900">取消推送</button>
                                    <button onclick="exportTargets(2)" class="text-green-600 hover:text-green-900">导出对象</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">制造业数字化转型政策</div>
                                <div class="text-sm text-gray-500">支持制造业企业数字化转型升级</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-08 14:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">待推送</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">432</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewPushDetail(3)" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="startPush(3)" class="text-green-600 hover:text-green-900">开始推送</button>
                                    <button onclick="exportTargets(3)" class="text-green-600 hover:text-green-900">导出对象</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <button class="bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </button>
                            <button class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                2
                            </button>
                            <button class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                3
                            </button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 推送详情弹窗 -->
    <div id="pushDetailModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 id="detailModalTitle" class="text-xl font-semibold text-gray-900">推送详情</h3>
                <button onclick="closePushDetailModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 政策信息与推送统计 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-medium text-gray-900 mb-3">政策信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">政策名称：</span>
                            <span class="text-sm text-gray-900">高新技术企业认定政策</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">政策类型：</span>
                            <span class="text-sm text-gray-900">税收优惠</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">发布机构：</span>
                            <span class="text-sm text-gray-900">科技部</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">匹配度：</span>
                            <span class="text-sm text-gray-900">95.2%</span>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h4 class="text-lg font-medium text-gray-900 mb-3">推送统计</h4>
                    <div class="h-32">
                        <canvas id="pushChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 推送对象列表 -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-medium text-gray-900">推送对象列表</h4>
                    <div class="flex items-center space-x-3">
                        <input type="text" placeholder="搜索企业名称" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <button class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 text-sm">
                            搜索
                        </button>
                    </div>
                </div>
                <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">企业名称</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">推送时间</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">推送状态</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">响应状态</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">北京科技创新有限公司</td>
                                <td class="px-4 py-2 text-sm text-gray-900">2024-01-15 14:30</td>
                                <td class="px-4 py-2 text-sm text-gray-900">已推送</td>
                                <td class="px-4 py-2 text-sm text-gray-900">已查看</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">上海智能制造股份公司</td>
                                <td class="px-4 py-2 text-sm text-gray-900">2024-01-15 14:30</td>
                                <td class="px-4 py-2 text-sm text-gray-900">已推送</td>
                                <td class="px-4 py-2 text-sm text-gray-900">未查看</td>
                            </tr>
                            <tr>
                                <td class="px-4 py-2 text-sm text-gray-900">深圳新能源技术有限公司</td>
                                <td class="px-4 py-2 text-sm text-gray-900">2024-01-15 14:30</td>
                                <td class="px-4 py-2 text-sm text-gray-900">已推送</td>
                                <td class="px-4 py-2 text-sm text-gray-900">已申请</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 推送记录时间轴 -->
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">推送记录时间轴</h4>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-3 h-3 bg-green-500 rounded-full mt-1"></div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">第三次推送完成</div>
                            <div class="text-sm text-gray-500">2024-01-15 14:30 - 推送至1,256个目标主体</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-3 h-3 bg-blue-500 rounded-full mt-1"></div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">第二次推送完成</div>
                            <div class="text-sm text-gray-500">2024-01-12 10:15 - 推送至1,156个目标主体</div>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0 w-3 h-3 bg-gray-400 rounded-full mt-1"></div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">首次推送完成</div>
                            <div class="text-sm text-gray-500">2024-01-10 09:00 - 推送至1,000个目标主体</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button onclick="repush()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    手动重推
                </button>
                <button onclick="closePushDetailModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 新增推送弹窗 -->
    <div id="pushModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 id="pushModalTitle" class="text-xl font-semibold text-gray-900">新增推送</h3>
                <button onclick="closePushModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 政策选择 -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">选择政策 <span class="text-red-500">*</span></label>
                        <select id="policySelect" name="policySelect" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择政策</option>
                            <option value="1">高新技术企业认定政策</option>
                            <option value="2">科技型中小企业评价政策</option>
                            <option value="3">制造业数字化转型政策</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">推送时间 <span class="text-red-500">*</span></label>
                        <input type="datetime-local" id="pushTime" name="pushTime" required class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">推送方式</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">短信推送</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">邮件推送</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">系统通知</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- 目标选择 -->
                <div>
                    <h4 class="text-lg font-medium text-gray-900 mb-4">目标主体选择</h4>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">匹配度阈值</label>
                            <div class="flex items-center space-x-3">
                                <input type="range" min="60" max="100" value="80" class="flex-1">
                                <span class="text-sm text-gray-900">80%</span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">企业类型</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">高新技术企业</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">科技型中小企业</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">一般企业</span>
                                </label>
                            </div>
                        </div>
                        <div class="bg-blue-50 rounded-lg p-3">
                            <div class="text-sm text-blue-800">
                                <strong>预计推送对象：</strong>约 1,256 个企业
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button type="button" onclick="closePushModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                    取消
                </button>
                <button type="button" onclick="savePush()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    确认推送
                </button>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.classList.toggle('hidden');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleExportDropdown') === -1) {
                dropdown.classList.add('hidden');
            }
        });

        // 导出数据
        function exportData(type) {
            const typeText = type === 'targets' ? '目标主体列表' : '推送日志';
            alert(`正在导出${typeText}，请稍候...`);
            document.getElementById('exportDropdown').classList.add('hidden');
        }

        // 查看推送详情
        function viewPushDetail(pushId) {
            const modal = document.getElementById('pushDetailModal');
            modal.classList.remove('hidden');
            
            // 初始化图表
            setTimeout(() => {
                initPushChart();
            }, 100);
        }

        // 关闭推送详情弹窗
        function closePushDetailModal() {
            document.getElementById('pushDetailModal').classList.add('hidden');
        }

        // 取消推送
        function cancelPush(pushId) {
            if (confirm('确定要取消这个推送吗？')) {
                alert(`推送 ${pushId} 已取消`);
            }
        }

        // 开始推送
        function startPush(pushId) {
            if (confirm('确定要开始推送吗？')) {
                alert(`推送 ${pushId} 已开始`);
            }
        }

        // 导出目标对象
        function exportTargets(pushId) {
            alert(`正在导出推送 ${pushId} 的目标对象列表...`);
        }

        // 手动重推
        function repush() {
            if (confirm('确定要手动重推吗？')) {
                alert('重推任务已启动');
            }
        }

        // 打开新增推送弹窗
        function openPushModal(mode) {
            const modal = document.getElementById('pushModal');
            modal.classList.remove('hidden');
        }

        // 关闭新增推送弹窗
        function closePushModal() {
            document.getElementById('pushModal').classList.add('hidden');
        }

        // 保存推送
        function savePush() {
            const policySelect = document.getElementById('policySelect').value;
            const pushTime = document.getElementById('pushTime').value;
            
            if (!policySelect || !pushTime) {
                alert('请填写必填字段！');
                return;
            }
            
            // 模拟保存过程
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '推送中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                alert('推送任务已创建！');
                closePushModal();
            }, 2000);
        }

        // 初始化推送统计图表
        function initPushChart() {
            const ctx = document.getElementById('pushChart');
            if (!ctx) return;
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['已查看', '未查看', '已申请'],
                    datasets: [{
                        data: [65, 25, 10],
                        backgroundColor: [
                            '#10B981',
                            '#F59E0B',
                            '#3B82F6'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            });
        }

        // 点击模态框外部关闭
        document.getElementById('pushDetailModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closePushDetailModal();
            }
        });

        document.getElementById('pushModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closePushModal();
            }
        });
    </script>
</body>
</html>