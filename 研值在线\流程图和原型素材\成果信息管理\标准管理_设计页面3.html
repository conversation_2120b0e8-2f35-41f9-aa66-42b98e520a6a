<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标准管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">标准管理</h1>

        <!-- 筛选检索区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">标准状态</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50">在执行</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">已废止</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">即将废止</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">修订中</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">标准级别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家标准</option>
                        <option value="industry">行业标准</option>
                        <option value="local">地方标准</option>
                        <option value="group">团体标准</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">发布日期</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="标准号/标题名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">标准总量</p>
                        <p class="text-xl font-semibold text-gray-900">1,245</p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex items-center">
                    <div class="bg-green-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">年度新增</p>
                        <p class="text-xl font-semibold text-gray-900">156 <span class="text-sm text-green-500">↑12%</span></p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">在执行标准</p>
                        <p class="text-xl font-semibold text-gray-900">856 <span class="text-sm text-gray-500">(68.7%)</span></p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">即将废止</p>
                        <p class="text-xl font-semibold text-gray-900">89</p>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex items-center">
                    <div class="bg-red-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">关联主体覆盖率</p>
                        <p class="text-xl font-semibold text-gray-900">78.5%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据表格区 -->
        <div class="bg-white shadow-md rounded-lg mb-6">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">标准列表</h2>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        批量导入
                    </button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        批量导出
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增标准
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标准号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标准级别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布日期</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">实施日期</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起草单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">GB/T 12345-2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能制造 工业机器人系统通用技术条件</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家标准</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在执行</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">T/CNIA 1234-2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新能源汽车用铝合金电池壳体技术规范</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">团体标准</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">即将废止</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-08-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-10-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市新能源汽车产业联盟</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">DB33/T 5678-2021</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智慧城市建设 数据共享与交换规范</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">地方标准</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已废止</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-06-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市大数据发展管理局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">JB/T 9010-2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">工业机器人 安全要求</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">行业标准</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">修订中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-01</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市机器人行业协会</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-4 条，共 1,245 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- 标准状态分布 -->
            <div class="bg-white shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">标准状态分布</h3>
                <div class="h-64">
                    <canvas id="statusChart"></canvas>
                </div>
            </div>
            
            <!-- 年度发布趋势 -->
            <div class="bg-white shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">年度发布趋势</h3>
                <div class="h-64">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
            
            <!-- 废止提醒 -->
            <div class="bg-white shadow-md rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">废止提醒</h3>
                <div class="h-64">
                    <canvas id="expireChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 标准详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/2 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="p-6 h-full overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">标准详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- 基础信息 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-lg font-medium text-gray-900 mb-3">基础信息</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">标准号：</span>
                            <span class="font-medium text-gray-900">GB/T 12345-2023</span>
                        </div>
                        <div>
                            <span class="text-gray-500">标准级别：</span>
                            <span class="font-medium text-gray-900">国家标准</span>
                        </div>
                        <div>
                            <span class="text-gray-500">标题名称：</span>
                            <span class="font-medium text-gray-900">智能制造 工业机器人系统通用技术条件</span>
                        </div>
                        <div>
                            <span class="text-gray-500">状态：</span>
                            <span class="font-medium text-gray-900">在执行</span>
                        </div>
                        <div>
                            <span class="text-gray-500">发布日期：</span>
                            <span class="font-medium text-gray-900">2023-05-15</span>
                        </div>
                        <div>
                            <span class="text-gray-500">实施日期：</span>
                            <span class="font-medium text-gray-900">2023-12-01</span>
                        </div>
                        <div>
                            <span class="text-gray-500">起草单位：</span>
                            <span class="font-medium text-gray-900">宁波市智能制造研究院</span>
                        </div>
                        <div>
                            <span class="text-gray-500">关联主体数：</span>
                            <span class="font-medium text-gray-900">12</span>
                        </div>
                    </div>
                </div>
                
                <!-- 修订历史 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-lg font-medium text-gray-900 mb-3">修订历史</h4>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between border-b pb-2">
                            <span class="text-gray-500">2023-05-15</span>
                            <span class="font-medium text-gray-900">首次发布</span>
                        </div>
                        <div class="flex justify-between border-b pb-2">
                            <span class="text-gray-500">2021-08-20</span>
                            <span class="font-medium text-gray-900">修订草案征求意见</span>
                        </div>
                        <div class="flex justify-between border-b pb-2">
                            <span class="text-gray-500">2020-11-10</span>
                            <span class="font-medium text-gray-900">立项</span>
                        </div>
                    </div>
                </div>
                
                <!-- 原文文件 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-lg font-medium text-gray-900 mb-3">原文文件</h4>
                    <div class="flex items-center space-x-4">
                        <div class="bg-blue-100 p-3 rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900">GB_T 12345-2023.pdf</p>
                            <p class="text-sm text-gray-500">2.5 MB</p>
                        </div>
                        <button class="ml-auto px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                            下载
                        </button>
                    </div>
                </div>
                
                <!-- 关联主体 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">关联主体</h4>
                        <button class="text-sm text-blue-600 hover:text-blue-800">查看全部</button>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <span class="text-blue-600 font-medium">A</span>
                            </div>
                            <span class="font-medium text-gray-900">宁波市智能制造研究院</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <span class="text-green-600 font-medium">B</span>
                            </div>
                            <span class="font-medium text-gray-900">宁波市机器人科技有限公司</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                <span class="text-purple-600 font-medium">C</span>
                            </div>
                            <span class="font-medium text-gray-900">宁波市自动化研究所</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                <span class="text-yellow-600 font-medium">D</span>
                            </div>
                            <span class="font-medium text-gray-900">宁波市工业设计院</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑标准模态框 -->
    <div id="standardModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增标准</h3>
                    <button onclick="closeStandardModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标准号 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入标准号">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">标准级别 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择标准级别</option>
                                <option value="national">国家标准</option>
                                <option value="industry">行业标准</option>
                                <option value="local">地方标准</option>
                                <option value="group">团体标准</option>
                            </select>
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">标题名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入标题名称">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">发布日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">实施日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择状态</option>
                                <option value="active">在执行</option>
                                <option value="expired">已废止</option>
                                <option value="will_expire">即将废止</option>
                                <option value="revising">修订中</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">起草单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入起草单位">
                        </div>
                        <div class="col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">标准文件</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <div class="mt-4">
                                    <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">支持PDF、DOC格式</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeStandardModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 图表初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 标准状态分布图表
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['在执行', '已废止', '即将废止', '修订中'],
                    datasets: [{
                        data: [856, 234, 89, 66],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(59, 130, 246, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 年度发布趋势图表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '发布数量',
                        data: [89, 112, 134, 145, 156],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 废止提醒图表
            const expireCtx = document.getElementById('expireChart').getContext('2d');
            new Chart(expireCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '废止数量',
                        data: [5, 8, 10, 7, 12, 15, 9, 11, 6, 4, 7, 3],
                        borderColor: 'rgba(245, 158, 11, 0.8)',
                        tension: 0.4,
                        fill: true,
                        backgroundColor: 'rgba(245, 158, 11, 0.1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });

        // 详情抽屉控制
        function openDetailDrawer() {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        // 标准模态框控制
        function openStandardModal() {
            document.getElementById('standardModal').classList.remove('hidden');
        }

        function closeStandardModal() {
            document.getElementById('standardModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('standardModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStandardModal();
            }
        });

        // 模拟详情按钮点击
        document.querySelectorAll('[onclick*="showDetail"]').forEach(btn => {
            btn.onclick = openDetailDrawer;
        });

        // 模拟新增按钮点击
        document.querySelectorAll('[onclick*="openStandardModal"]').forEach(btn => {
            btn.onclick = openStandardModal;
        });
    </script>
</body>
</html>