<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">仪器设备综合分析流程图</text>

  <!-- 阶段一：数据汇聚与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与标准化</text>
  
  <!-- 节点1: 数据源汇聚 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据源汇聚</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">每日自动汇聚各数据源</text>
  </g>

  <!-- 节点2: 数据标准化 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据标准化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">基础数据及状态数据处理</text>
  </g>

  <!-- 节点3: 可分析数据集 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可分析数据集</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">形成统一的数据集</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据分析与统计 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据分析与统计</text>

  <!-- 节点4: 多维度统计 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多维度统计</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">分类/区域/状态统计</text>
  </g>

  <!-- 节点5: 统计结果生成 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计结果生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成统计结果和可视化数据</text>
  </g>

  <!-- 节点6: 定期更新 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定期更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据分析模块定期执行</text>
  </g>

  <!-- 连接线 数据集 -> 统计 -->
  <path d="M 900 200 C 900 250, 300 250, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：用户交互与展示 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：用户交互与展示</text>

  <!-- 节点7: 用户访问页面 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">进入综合分析页面</text>
  </g>

  <!-- 节点8: 权限验证与加载 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据权限加载图表数据</text>
  </g>

  <!-- 节点9: 图表交互 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">图表交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击图表板块联动刷新</text>
  </g>

  <!-- 节点10: 实时数据更新 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时数据更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据操作实时更新展示</text>
  </g>

  <!-- 连接线 统计结果 -> 用户访问 -->
  <path d="M 600 380 C 600 430, 200 430, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与应用 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与应用</text>

  <!-- 节点11: 数据导出 -->
  <g transform="translate(200, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">导出统计报表和明细数据</text>
  </g>

  <!-- 节点12: 外部引用 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部引用</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">支持外部模块和管理决策</text>
  </g>

  <!-- 节点13: 资源优化 -->
  <g transform="translate(800, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">资源优化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设备配置和绩效考核</text>
  </g>

  <!-- 连接线 实时更新 -> 导出 -->
  <path d="M 850 560 C 850 610, 300 610, 300 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12 -> 13 -->
  <path d="M 400 705 Q 450 705 500 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 705 Q 750 705 800 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据流循环箭头 -->
  <path d="M 1100 705 C 1200 705, 1200 165, 1000 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="435" text-anchor="middle" font-size="12" fill="#666" transform="rotate(90, 1150, 435)">持续分析循环</text>

  <!-- 明细数据联动线 -->
  <path d="M 700 560 C 700 600, 700 600, 700 600" stroke="#999" stroke-width="1" fill="none" stroke-dasharray="3,3" />
  <text x="720" y="580" font-size="11" fill="#666">明细数据联动</text>

</svg>