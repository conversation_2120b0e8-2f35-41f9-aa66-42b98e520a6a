unit ManCm;

interface

uses
  Classes;

type
  TManCm = class
  private
    F2XS: string;
    FXS: string;
    FS: string;
    FM: string;
    FL: string;
    FXL: string;
    F2XL: string;
    F3XL: string;
    F4XL: string;
    F5XL: string;
    F7XL: string;
  public
    property Man_2XS: string read F2XS write F2XS;
    property Man_XS: string read FXS write FXS;
    property Man_S: string read FS write FS;
    property Man_M: string read FM write FM;
    property Man_L: string read FL write FL;
    property Man_XL: string read FXL write FXL;
    property Man_2XL: string read F2XL write F2XL;
    property Man_3XL: string read F3XL write F3XL;
    property Man_4XL: string read F4XL write F4XL;
    property Man_5XL: string read F5XL write F5XL;
    property Man_7XL: string read F7XL write F7XL;
  end;

implementation

end.
