<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宁波市指南管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">宁波市指南管理</h1>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="guideName" class="block text-sm font-medium text-gray-700 mb-1">指南名称</label>
                    <input type="text" id="guideName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入指南名称">
                </div>
                <div>
                    <label for="publishYear" class="block text-sm font-medium text-gray-700 mb-1">发布年份</label>
                    <div class="flex space-x-2">
                        <select id="startYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">起始年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                        <select id="endYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label for="techField" class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                    <select id="techField" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="new-energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">计划类别</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">重大专项</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">重点研发</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">基础研究</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" value="draft" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">草稿</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" value="published" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">已发布</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 指南列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">指南列表</h2>
                <div class="flex space-x-3">
                    <div class="relative">
                        <button id="exportDropdownBtn" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出
                        </button>
                        <div id="exportDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                            <div class="py-1">
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出Excel</a>
                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出CSV</a>
                            </div>
                        </div>
                    </div>
                    <button onclick="openEditModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增指南
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">指南名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划类别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市人工智能产业发展指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">重大专项</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张主任</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewDetail('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市生物医药创新指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市卫健委</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">重点研发</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">草稿</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 14:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewDetail('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新能源技术发展指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市发改委</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full">基础研究</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewDetail('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">12</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 指南编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">指南编辑</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 基本信息 -->
                    <div class="md:col-span-2 space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">指南名称 <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入指南名称">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">发布单位 <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入发布单位">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">技术领域 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择技术领域</option>
                                    <option value="ai">人工智能</option>
                                    <option value="bio">生物医药</option>
                                    <option value="new-energy">新能源</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">计划类别 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择计划类别</option>
                                    <option value="major">重大专项</option>
                                    <option value="key">重点研发</option>
                                    <option value="basic">基础研究</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">发布日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">截止日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">研究内容 <span class="text-red-500">*</span></label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入研究内容"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">考核指标 <span class="text-red-500">*</span></label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入考核指标"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">申报要求</label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入申报要求"></textarea>
                        </div>
                    </div>
                    
                    <!-- 关联管理与附件 -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-2">关联专家</h4>
                            <div class="space-y-2">
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200">
                                    <img class="w-8 h-8 rounded-full mr-3" src="https://randomuser.me/api/portraits/men/1.jpg" alt="专家头像">
                                    <div>
                                        <p class="text-sm font-medium">张教授</p>
                                        <p class="text-xs text-gray-500">人工智能</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200">
                                    <img class="w-8 h-8 rounded-full mr-3" src="https://randomuser.me/api/portraits/women/1.jpg" alt="专家头像">
                                    <div>
                                        <p class="text-sm font-medium">李研究员</p>
                                        <p class="text-xs text-gray-500">生物医药</p>
                                    </div>
                                </div>
                            </div>
                            <button class="w-full mt-2 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                添加关联专家
                            </button>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-2">附件上传</h4>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                        <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .doc, .docx 格式</span>
                                    </label>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".pdf,.doc,.docx">
                                </div>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                <p>已上传附件：</p>
                                <ul class="mt-1 space-y-1">
                                    <li class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        申报指南.pdf
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-2">状态设置</h4>
                            <div class="flex items-center">
                                <label class="flex items-center">
                                    <input type="radio" name="editStatus" value="draft" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">草稿</span>
                                </label>
                                <label class="flex items-center ml-4">
                                    <input type="radio" name="editStatus" value="published" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">发布</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-6">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        保存草稿
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        提交审核
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 指南详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">指南详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 基本信息 -->
                    <div class="md:col-span-2 space-y-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">指南名称：</span>
                                    <span class="font-medium">宁波市人工智能产业发展指南</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">发布单位：</span>
                                    <span class="font-medium">宁波市科技局</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">技术领域：</span>
                                    <span class="font-medium">人工智能</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">计划类别：</span>
                                    <span class="font-medium">重大专项</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">发布日期：</span>
                                    <span class="font-medium">2024-01-10</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">截止日期：</span>
                                    <span class="font-medium">2024-03-31</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">状态：</span>
                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">已发布</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">研究内容</h4>
                            <div class="text-sm text-gray-700">
                                <p>本指南围绕宁波市人工智能产业发展需求，重点支持以下方向：</p>
                                <ul class="mt-2 space-y-1 list-disc list-inside">
                                    <li>人工智能核心算法与关键技术研发</li>
                                    <li>智能装备与系统集成应用</li>
                                    <li>人工智能在制造业中的创新应用</li>
                                    <li>人工智能安全与伦理研究</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">考核指标</h4>
                            <div class="text-sm text-gray-700">
                                <ul class="space-y-1 list-disc list-inside">
                                    <li>形成3-5项具有自主知识产权的人工智能核心技术</li>
                                    <li>开发2-3个典型应用场景示范项目</li>
                                    <li>培养人工智能领域高层次人才团队</li>
                                    <li>推动相关产业产值增长10%以上</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">申报要求</h4>
                            <div class="text-sm text-gray-700">
                                <ul class="space-y-1 list-disc list-inside">
                                    <li>申报单位需在宁波市注册并具有独立法人资格</li>
                                    <li>项目负责人应具有高级职称或博士学位</li>
                                    <li>申报材料需真实、完整、规范</li>
                                    <li>申报截止日期前完成在线提交</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 关联信息与附件 -->
                    <div class="space-y-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">关联专家</h4>
                            <div class="space-y-3">
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200">
                                    <img class="w-10 h-10 rounded-full mr-3" src="https://randomuser.me/api/portraits/men/1.jpg" alt="专家头像">
                                    <div>
                                        <p class="text-sm font-medium">张教授</p>
                                        <p class="text-xs text-gray-500">宁波大学人工智能研究所</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200">
                                    <img class="w-10 h-10 rounded-full mr-3" src="https://randomuser.me/api/portraits/women/1.jpg" alt="专家头像">
                                    <div>
                                        <p class="text-sm font-medium">李研究员</p>
                                        <p class="text-xs text-gray-500">宁波市人工智能研究院</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">附件下载</h4>
                            <div class="space-y-2">
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200 hover:bg-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm">申报指南.pdf</span>
                                    <button class="ml-auto text-blue-600 hover:text-blue-800 text-sm">
                                        下载
                                    </button>
                                </div>
                                <div class="flex items-center p-2 bg-white rounded border border-gray-200 hover:bg-gray-100">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm">申报书模板.docx</span>
                                    <button class="ml-auto text-blue-600 hover:text-blue-800 text-sm">
                                        下载
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-3">操作记录</h4>
                            <div class="space-y-3 text-sm">
                                <div>
                                    <p class="font-medium">创建</p>
                                    <p class="text-gray-500">张主任 - 2024-01-05 14:30</p>
                                </div>
                                <div>
                                    <p class="font-medium">审核通过</p>
                                    <p class="text-gray-500">王局长 - 2024-01-10 09:15</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-6">
                    <button type="button" onclick="closeDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        document.getElementById('exportDropdownBtn').addEventListener('click', function() {
            document.getElementById('exportDropdown').classList.toggle('hidden');
        });
        
        // 点击外部关闭导出下拉菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('#exportDropdownBtn') && !e.target.closest('#exportDropdown')) {
                document.getElementById('exportDropdown').classList.add('hidden');
            }
        });
        
        // 编辑弹窗
        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
        }
        
        function closeModal() {
            document.getElementById('editModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // 详情弹窗
        function viewDetail(id) {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
        
        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                alert('文件上传成功！');
            }
        });
    </script>
</body>
</html>