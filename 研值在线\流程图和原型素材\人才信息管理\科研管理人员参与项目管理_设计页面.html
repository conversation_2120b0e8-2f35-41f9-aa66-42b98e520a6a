<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研管理人员项目管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                项目筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="projectName" class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input type="text" id="projectName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                </div>
                <div>
                    <label for="projectLevel" class="block text-sm font-medium text-gray-700 mb-1">项目级别</label>
                    <select id="projectLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="district">区级</option>
                    </select>
                </div>
                <div>
                    <label for="projectType" class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                    <select id="projectType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="natural">自然科学</option>
                        <option value="social">社会科学</option>
                        <option value="technology">技术开发</option>
                        <option value="application">应用研究</option>
                    </select>
                </div>
                <div>
                    <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" id="startDate" class="w-1/2 px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" id="endDate" class="w-1/2 px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label for="projectStatus" class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                    <select id="projectStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="planning">立项中</option>
                        <option value="in-progress">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="terminated">已终止</option>
                    </select>
                </div>
                <div>
                    <label for="participant" class="block text-sm font-medium text-gray-700 mb-1">参与人员</label>
                    <input type="text" id="participant" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入人员姓名">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 项目列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">项目参与列表</h2>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出
                    </button>
                    <button onclick="openProjectModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增项目
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目级别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目负责人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总投资(万元)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研究与应用</div>
                                <div class="text-sm text-gray-500">项目编号：NB2023-001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术开发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15 至 2024-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">850.00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showProjectDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openProjectModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeProject('1')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">浙江省海洋生物资源可持续利用研究</div>
                                <div class="text-sm text-gray-500">项目编号：ZJ2022-005</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自然科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-03-01 至 2023-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1200.00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showProjectDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openProjectModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeProject('2')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新材料产业创新发展战略研究</div>
                                <div class="text-sm text-gray-500">项目编号：NB2023-008</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">社会科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-01 至 2023-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">300.00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">立项中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showProjectDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openProjectModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeProject('3')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智慧城市建设关键技术集成与应用</div>
                                <div class="text-sm text-gray-500">项目编号：NB2021-012</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">应用研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-01-01 至 2022-12-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1500.00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showProjectDetail('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openProjectModal('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeProject('4')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市生态环境监测与治理技术研究</div>
                                <div class="text-sm text-gray-500">项目编号：NB2022-003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术开发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-05-01 至 2023-05-31</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">陈研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">600.00</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已终止</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showProjectDetail('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openProjectModal('5')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeProject('5')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 23 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目编辑弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">新增项目参与</h3>
                    <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="modalProjectName" class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                            <input type="text" id="modalProjectName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                        </div>
                        <div>
                            <label for="modalProjectCode" class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
                            <input type="text" id="modalProjectCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目编号">
                        </div>
                        <div>
                            <label for="modalProjectLevel" class="block text-sm font-medium text-gray-700 mb-1">项目级别</label>
                            <select id="modalProjectLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择项目级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalProjectType" class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                            <select id="modalProjectType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择项目类型</option>
                                <option value="natural">自然科学</option>
                                <option value="social">社会科学</option>
                                <option value="technology">技术开发</option>
                                <option value="application">应用研究</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalStartDate" class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                            <input type="date" id="modalStartDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="modalEndDate" class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                            <input type="date" id="modalEndDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="modalProjectLeader" class="block text-sm font-medium text-gray-700 mb-1">项目负责人</label>
                            <input type="text" id="modalProjectLeader" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目负责人">
                        </div>
                        <div>
                            <label for="modalTotalInvestment" class="block text-sm font-medium text-gray-700 mb-1">总投资(万元)</label>
                            <input type="number" id="modalTotalInvestment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入总投资金额">
                        </div>
                        <div>
                            <label for="modalProjectStatus" class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                            <select id="modalProjectStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="planning">立项中</option>
                                <option value="in-progress">进行中</option>
                                <option value="completed">已完成</option>
                                <option value="terminated">已终止</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalParticipantRole" class="block text-sm font-medium text-gray-700 mb-1">参与角色</label>
                            <select id="modalParticipantRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="leader">项目负责人</option>
                                <option value="core">核心成员</option>
                                <option value="member">一般成员</option>
                                <option value="advisor">顾问</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="modalProjectDescription" class="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
                        <textarea id="modalProjectDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目描述"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeProjectModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeProjectDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-6">
                    <!-- 基础信息 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <span class="text-sm text-gray-500">项目名称：</span>
                                <span class="text-sm font-medium text-gray-900">宁波市智能制造关键技术研究与应用</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">项目编号：</span>
                                <span class="text-sm font-medium text-gray-900">NB2023-001</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">项目级别：</span>
                                <span class="text-sm font-medium text-gray-900">市级</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">项目类型：</span>
                                <span class="text-sm font-medium text-gray-900">技术开发</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">起止时间：</span>
                                <span class="text-sm font-medium text-gray-900">2023-01-15 至 2024-12-31</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">项目负责人：</span>
                                <span class="text-sm font-medium text-gray-900">张研究员</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">总投资：</span>
                                <span class="text-sm font-medium text-gray-900">850.00万元</span>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500">项目状态：</span>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                            </div>
                        </div>
                    </div>

                    <!-- 项目进展 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">项目进展</h4>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">总体进度</span>
                                    <span class="text-sm font-medium text-gray-700">45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-white p-3 rounded-lg border border-gray-200">
                                    <div class="text-sm font-medium text-gray-700 mb-1">已完成任务</div>
                                    <div class="text-2xl font-bold text-blue-600">12</div>
                                </div>
                                <div class="bg-white p-3 rounded-lg border border-gray-200">
                                    <div class="text-sm font-medium text-gray-700 mb-1">进行中任务</div>
                                    <div class="text-2xl font-bold text-yellow-600">8</div>
                                </div>
                                <div class="bg-white p-3 rounded-lg border border-gray-200">
                                    <div class="text-sm font-medium text-gray-700 mb-1">未开始任务</div>
                                    <div class="text-2xl font-bold text-gray-600">5</div>
                                </div>
                            </div>
                            <div class="text-sm text-gray-700">
                                <p>最近更新：2023-08-15 完成第一阶段技术调研</p>
                                <p>下阶段计划：2023-09-01 开始原型开发</p>
                            </div>
                        </div>
                    </div>

                    <!-- 成员结构 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">成员结构</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分工</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张研究员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">总体协调</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李工程师</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">核心成员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术开发</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王教授</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">核心成员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">算法设计</td>
                                    </tr>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">陈研究员</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技信息研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">顾问</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">政策咨询</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 财务情况 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">财务情况</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                            <div class="bg-white p-3 rounded-lg border border-gray-200">
                                <div class="text-sm font-medium text-gray-700 mb-1">总预算</div>
                                <div class="text-2xl font-bold text-gray-900">850.00万元</div>
                            </div>
                            <div class="bg-white p-3 rounded-lg border border-gray-200">
                                <div class="text-sm font-medium text-gray-700 mb-1">已使用</div>
                                <div class="text-2xl font-bold text-blue-600">382.50万元</div>
                            </div>
                            <div class="bg-white p-3 rounded-lg border border-gray-200">
                                <div class="text-sm font-medium text-gray-700 mb-1">剩余预算</div>
                                <div class="text-2xl font-bold text-green-600">467.50万元</div>
                            </div>
                        </div>
                        <div class="text-sm text-gray-700">
                            <p>最近支出：2023-08-10 设备采购 125,000元</p>
                            <p>下阶段预算：2023-09-01 预计支出 200,000元</p>
                        </div>
                    </div>

                    <!-- 成果产出 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">成果产出</h4>
                        <div class="space-y-3">
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">已发表论文2篇</p>
                                    <p class="text-xs text-gray-500">《智能制造关键技术研究》发表于《机械工程学报》</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">申请专利3项</p>
                                    <p class="text-xs text-gray-500">其中发明专利1项，实用新型专利2项</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-600 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">完成技术报告1份</p>
                                    <p class="text-xs text-gray-500">《智能制造技术现状与发展趋势》</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end mt-6">
                    <button onclick="closeProjectDetail()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 项目编辑弹窗控制
        function openProjectModal(id) {
            const modal = document.getElementById('projectModal');
            const title = document.getElementById('modalTitle');
            
            if (id) {
                title.textContent = '编辑项目参与';
                // 这里应该加载项目数据
            } else {
                title.textContent = '新增项目参与';
            }
            
            modal.classList.remove('hidden');
        }

        function closeProjectModal() {
            document.getElementById('projectModal').classList.add('hidden');
        }

        // 项目详情弹窗控制
        function showProjectDetail(id) {
            document.getElementById('projectDetailModal').classList.remove('hidden');
            // 这里应该加载项目详情数据
        }

        function closeProjectDetail() {
            document.getElementById('projectDetailModal').classList.add('hidden');
        }

        // 移除项目
        function removeProject(id) {
            if (confirm('确定要移除这个项目参与记录吗？此操作不可恢复！')) {
                console.log('移除项目:', id);
                // 这里应该发送请求到后端删除项目
                alert('项目参与记录已移除');
            }
        }

        // 点击弹窗外部关闭
        document.getElementById('projectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProjectModal();
            }
        });

        document.getElementById('projectDetailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProjectDetail();
            }
        });
    </script>
</body>
</html>