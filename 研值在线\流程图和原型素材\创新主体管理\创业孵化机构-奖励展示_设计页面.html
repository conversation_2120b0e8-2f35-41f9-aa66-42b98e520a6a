<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业孵化机构-奖励展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">创业孵化机构奖励展示</h1>

        <!-- 奖励概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="bg-white shadow-md rounded-lg p-6 border-l-4 border-blue-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">国家级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">28</p>
                        <p class="text-xs text-gray-500 mt-1">最近获奖: 2023-12-15</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm">12.5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-6 border-l-4 border-green-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">省级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">156</p>
                        <p class="text-xs text-gray-500 mt-1">最近获奖: 2024-01-10</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm">8.3%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-6 border-l-4 border-yellow-500">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">市级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">342</p>
                        <p class="text-xs text-gray-500 mt-1">最近获奖: 2024-02-05</p>
                    </div>
                    <div class="text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        <span class="text-sm">2.1%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 奖励级别切换区 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between">
                <div class="flex space-x-1">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md">国家级</button>
                    <button class="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md">省级</button>
                    <button class="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md">市级</button>
                    <button class="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-md">专利奖</button>
                </div>
                <div class="mt-2 md:mt-0">
                    <input type="text" placeholder="搜索奖励名称或年份" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
        </div>

        <!-- 奖励列表区 -->
        <div class="bg-white shadow-md rounded-lg mb-6">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">国家级奖励列表</h2>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出Excel
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授奖部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖等级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书预览</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家科技进步奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国务院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技进步</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造关键技术研究与应用</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <button onclick="openCertificateModal()" class="w-16 h-12 bg-gray-100 rounded flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家技术发明奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术发明</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新型环保材料研发与应用</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <button onclick="openCertificateModal()" class="w-16 h-12 bg-gray-100 rounded flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家自然科学奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">教育部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自然科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">量子计算理论研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <button onclick="openCertificateModal()" class="w-16 h-12 bg-gray-100 rounded flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 28 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 奖励趋势区 -->
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">奖励趋势分析</h2>
            <div class="h-80">
                <canvas id="awardTrendChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 证书查看弹窗 -->
    <div id="certificateModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">证书详情</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                            下载证书
                        </button>
                        <button onclick="closeCertificateModal()" class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <img src="https://source.unsplash.com/800x600/?certificate" alt="证书图片" class="w-full h-auto rounded border border-gray-200">
                </div>
                <div class="p-4 border-t bg-gray-50">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">奖项名称:</span>
                            <span class="font-medium text-gray-900">国家科技进步奖</span>
                        </div>
                        <div>
                            <span class="text-gray-500">获奖年度:</span>
                            <span class="font-medium text-gray-900">2023</span>
                        </div>
                        <div>
                            <span class="text-gray-500">授奖部门:</span>
                            <span class="font-medium text-gray-900">国务院</span>
                        </div>
                        <div>
                            <span class="text-gray-500">获奖等级:</span>
                            <span class="font-medium text-gray-900">二等奖</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 证书弹窗控制
        function openCertificateModal() {
            document.getElementById('certificateModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('certificateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCertificateModal();
            }
        });

        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            const ctx = document.getElementById('awardTrendChart').getContext('2d');
            const gradient1 = ctx.createLinearGradient(0, 0, 0, ctx.canvas.clientHeight);
            gradient1.addColorStop(0, 'rgba(59, 130, 246, 0.5)');
            gradient1.addColorStop(1, 'rgba(59, 130, 246, 0)');

            const gradient2 = ctx.createLinearGradient(0, 0, 0, ctx.canvas.clientHeight);
            gradient2.addColorStop(0, 'rgba(16, 185, 129, 0.5)');
            gradient2.addColorStop(1, 'rgba(16, 185, 129, 0)');

            const gradient3 = ctx.createLinearGradient(0, 0, 0, ctx.canvas.clientHeight);
            gradient3.addColorStop(0, 'rgba(245, 158, 11, 0.5)');
            gradient3.addColorStop(1, 'rgba(245, 158, 11, 0)');

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '国家级',
                            data: [5, 7, 9, 12, 15],
                            backgroundColor: gradient1,
                            borderColor: '#3B82F6',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '省级',
                            data: [25, 32, 45, 60, 78],
                            backgroundColor: gradient2,
                            borderColor: '#10B981',
                            borderWidth: 1,
                            borderRadius: 4
                        },
                        {
                            label: '市级',
                            data: [50, 65, 80, 110, 130],
                            backgroundColor: gradient3,
                            borderColor: '#F59E0B',
                            borderWidth: 1,
                            borderRadius: 4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        }
                    },
                    scales: {
                        x: {
                            stacked: false,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: false,
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>