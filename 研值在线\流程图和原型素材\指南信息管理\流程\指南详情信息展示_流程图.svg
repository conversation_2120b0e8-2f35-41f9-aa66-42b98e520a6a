<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">指南详情信息展示流程图</text>

  <!-- 阶段一：用户请求与数据查询 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户请求与数据查询</text>
  
  <!-- 节点1: 用户点击指南记录 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户点击指南记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">钻取结果中的条目</text>
  </g>

  <!-- 节点2: 发送指南ID -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">发送指南ID</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">前端请求后端服务</text>
  </g>

  <!-- 节点3: 查询主表及关联表 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询主表及关联表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据指南ID检索</text>
  </g>

  <!-- 节点4: 聚合数据 -->
  <g transform="translate(1100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">聚合数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">封装为JSON对象</text>
  </g>
  
  <!-- 连接线 阶段一 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 165 Q 1050 165 1100 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：权限校验与数据处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：权限校验与数据处理</text>

  <!-- 节点5: 权限校验组件 -->
  <g transform="translate(300, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限校验组件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">过滤敏感字段</text>
  </g>

  <!-- 节点6: 返回最终数据 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">返回最终数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">成功或错误提示</text>
  </g>

  <!-- 节点7: 错误处理 -->
  <g transform="translate(900, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">错误处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调用失败时返回</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 500 355 Q 550 355 600 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 400 320 C 400 280, 450 280, 500 280 C 550 280, 600 280, 650 280 C 700 280, 750 280, 800 280 C 850 280, 900 280, 1000 320" stroke="#d32f2f" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="700" y="270" font-size="12" fill="#d32f2f">校验失败</text>

  <!-- 阶段三：前端渲染与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端渲染与交互</text>

  <!-- 节点8: 渲染弹窗 -->
  <g transform="translate(200, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">渲染弹窗</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">展示指南详情</text>
  </g>

  <!-- 节点9: 关键词高亮 -->
  <g transform="translate(450, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关键词高亮</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">折叠逻辑初始化</text>
  </g>

  <!-- 节点10: 记录打开事件 -->
  <g transform="translate(700, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录打开事件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">写入埋点日志</text>
  </g>

  <!-- 节点11: 用户交互操作 -->
  <g transform="translate(950, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">复制、点击标签云</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 400 535 Q 425 535 450 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 535 Q 675 535 700 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 535 Q 925 535 950 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：事件记录与日志分析 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：事件记录与日志分析</text>
  
  <!-- 节点12: 记录交互事件 -->
  <g transform="translate(300, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录交互事件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">前端事件追踪</text>
  </g>

  <!-- 节点13: 调用相应接口 -->
  <g transform="translate(600, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">调用相应接口</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">复制、跳转或统计</text>
  </g>

  <!-- 节点14: 异步写入操作日志 -->
  <g transform="translate(900, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">异步写入操作日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">供后续分析使用</text>
  </g>

  <!-- 连接线 阶段四 -->
  <path d="M 500 715 Q 550 715 600 715" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 715 Q 850 715 900 715" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 聚合数据到权限校验 -->
  <path d="M 1200 200 C 1250 200, 1250 280, 1250 320 C 1250 360, 1200 360, 500 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 返回数据到前端渲染 -->
  <path d="M 700 390 C 700 430, 650 460, 600 460 C 550 460, 500 460, 450 460 C 400 460, 350 460, 300 500" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 用户交互到事件记录 -->
  <path d="M 1050 570 C 1050 610, 1000 640, 950 640 C 900 640, 850 640, 800 640 C 750 640, 700 640, 650 640 C 600 640, 550 640, 500 640 C 450 640, 400 680 400 680" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>