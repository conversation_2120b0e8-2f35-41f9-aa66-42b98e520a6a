<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">多维度钻取交互分析流程</text>

  <!-- 阶段一：初始化与加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：初始化与加载</text>
  
  <!-- 节点1: 页面加载 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">系统启动</text>
  </g>

  <!-- 节点2: 拉取统计缓存 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">拉取统计缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取初始数据</text>
  </g>

  <!-- 节点3: 渲染初始图表 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">渲染初始图表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">初始化面包屑与面板</text>
  </g>

  <!-- 连接线 阶段一 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与下钻 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与下钻</text>

  <!-- 节点4: 用户点击图表 -->
  <g transform="translate(150, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户点击图表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">捕获事件</text>
  </g>

  <!-- 节点5: 压入面包屑栈 -->
  <g transform="translate(400, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">压入面包屑栈</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录维度及值</text>
  </g>

  <!-- 节点6: 发送筛选请求 -->
  <g transform="translate(650, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">发送筛选请求</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">向后端请求数据</text>
  </g>

  <!-- 节点7: 后端聚合处理 -->
  <g transform="translate(900, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">后端聚合处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成下钻数据</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 350 345 Q 375 345 400 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 345 Q 625 345 650 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 345 Q 875 345 900 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据更新与历史记录 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据更新与历史记录</text>

  <!-- 节点8: 返回聚合结果 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">返回聚合结果</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">后端响应数据</text>
  </g>

  <!-- 节点9: 更新图表面板 -->
  <g transform="translate(450, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新图表面板</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">刷新界面显示</text>
  </g>

  <!-- 节点10: 记录历史栈 -->
  <g transform="translate(700, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录历史栈</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">保存操作状态</text>
  </g>

  <!-- 节点11: 维度联动面板 -->
  <g transform="translate(950, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">维度联动面板</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">附加过滤条件</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 400 525 Q 425 525 450 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 525 Q 675 525 700 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 525 Q 925 525 950 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：导航与会话管理 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：导航与会话管理</text>

  <!-- 节点12: 面包屑导航 -->
  <g transform="translate(250, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">面包屑导航</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">返回上一级</text>
  </g>

  <!-- 节点13: 复位操作 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">复位操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">清空栈结束会话</text>
  </g>

  <!-- 节点14: 上下文一致性 -->
  <g transform="translate(750, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">上下文一致性</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">保持状态同步</text>
  </g>

  <!-- 连接线：跨阶段连接 -->
  <path d="M 900 200 C 900 250, 250 260, 250 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 380 C 1000 430, 300 440, 300 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 560 C 800 610, 350 620, 350 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 705 Q 650 705 700 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 705 Q 875 705 900 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 钻取循环连接线 -->
  <path d="M 1050 560 C 1200 560, 1200 200, 250 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="1100" y="380" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 1100 380)">钻取循环</text>

  <!-- 返回上一级循环 -->
  <path d="M 350 740 C 100 740, 100 380, 150 380" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="125" y="560" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 125 560)">返回循环</text>

  <!-- 历史栈弹出连接线 -->
  <path d="M 800 560 C 800 600, 350 610, 350 670" stroke="#9C27B0" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="575" y="615" text-anchor="middle" font-size="12" fill="#9C27B0">弹出历史栈</text>

</svg>