<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研机构信息管理业务流程</text>

  <!-- 阶段一：数据录入与初始化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据录入与初始化</text>
  
  <!-- 节点1: 新增机构操作 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增科研机构</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">用户界面发起操作</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">校验必填项及唯一性</text>
  </g>

  <!-- 节点2: 批量导入功能 -->
  <g transform="translate(980, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入数据</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">文件解析与校验</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">错误反馈与修正</text>
  </g>

  <!-- 节点3: 初始档案记录 -->
  <g transform="translate(590, 250)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成初始档案</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">状态标记为"待完善"</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">数据存储完成</text>
  </g>

  <!-- 连接线 新增 -> 初始档案 -->
  <path d="M 420 170 C 500 170, 520 210, 590 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 批量导入 -> 初始档案 -->
  <path d="M 980 170 C 900 170, 880 210, 810 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据补充与完善 -->
  <text x="700" y="400" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据补充与完善</text>

  <!-- 节点4: 外部服务补充 -->
  <g transform="translate(200, 430)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部服务补充</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">调用共享服务</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">自动补充缺失字段</text>
  </g>

  <!-- 节点5: 人工补录任务 -->
  <g transform="translate(590, 430)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人工补录任务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">生成待维护任务</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">通知责任人补录</text>
  </g>

  <!-- 节点6: 数据完善状态 -->
  <g transform="translate(980, 430)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新完善</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">标记为"已完善"</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">刷新统计信息</text>
  </g>

  <!-- 连接线 初始档案 -> 外部服务 -->
  <path d="M 590 330 C 500 350, 400 380, 310 430" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 外部服务 -> 人工补录 -->
  <path d="M 420 470 C 500 470, 520 470, 590 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="505" y="460" text-anchor="middle" font-size="12" fill="#555">无法自动补充</text>
  
  <!-- 连接线 人工补录 -> 状态更新 -->
  <path d="M 810 470 C 880 470, 900 470, 980 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="895" y="460" text-anchor="middle" font-size="12" fill="#555">补充完成</text>

  <!-- 阶段三：数据维护与管理 -->
  <text x="700" y="600" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据维护与管理</text>

  <!-- 节点7: 删除操作检查 -->
  <g transform="translate(200, 630)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除关联检查</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">检查数据关联关系</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">确认级联删除</text>
  </g>

  <!-- 节点8: 质量检测任务 -->
  <g transform="translate(590, 630)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定期质量检测</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">自动检测数据质量</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">生成维护任务</text>
  </g>

  <!-- 节点9: 日志记录归档 -->
  <g transform="translate(980, 630)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志记录归档</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">操作日志记录</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">数据归档备查</text>
  </g>

  <!-- 连接线 删除检查 -> 日志归档 -->
  <path d="M 420 670 C 650 670, 800 670, 980 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="660" text-anchor="middle" font-size="12" fill="#555">删除完成</text>

  <!-- 阶段四：持续优化循环 -->
  <text x="700" y="800" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：持续优化循环</text>
  
  <!-- 节点10: 数据准确性保持 -->
  <g transform="translate(500, 830)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">科研机构信息持续准确性保障</text>
    <text x="200" y="55" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">数据完整性</tspan>
      <tspan dx="60">质量监控</tspan>
      <tspan dx="60">持续优化</tspan>
    </text>
  </g>

  <!-- 连接线 质量检测 -> 持续保障 -->
  <path d="M 700 710 C 700 750, 700 780, 700 830" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 反馈循环：持续保障 -> 人工补录 -->
  <path d="M 500 870 C 200 870, 100 600, 100 470 C 100 470, 150 470, 200 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="680" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 150, 680)">质量反馈循环</text>

  <!-- 反馈循环：持续保障 -> 质量检测 -->
  <path d="M 700 830 C 700 800, 700 750, 700 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="720" y="770" text-anchor="middle" font-size="12" fill="#555">检测循环</text>

</svg>