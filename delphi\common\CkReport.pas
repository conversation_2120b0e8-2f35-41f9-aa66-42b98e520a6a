unit CkReport;

interface

uses
  Classes;

type
  TCkReport = class
  private
    FCkcname: string;
    FPmtype: string;
    FPm: string;
    FGg: string;
    FAmount: double;
    FAmount1: double;
    FAmount2: double;
    FAmount3: double;
    FSb: double;
    FSunh: double;
    FCkType: string;

    FGyscd: string;
    FLb: string;
    FKz: string;
    FCk: string;
    FYs: string;
    FGh: string;
    FSh: string;
    FCkdate: string;
    FYt: string;

    FPs: integer;
    Fsearchpmgg: string;
    FAmountFlag: integer;
  public
    property Ckcname: string read FCkcname write FCkcname;
    property Pmtype: string read FPmtype write FPmtype;
    property Pm: string read FPm write FPm;
    property Gg: string read FGg write FGg;
    property Amount: double read FAmount write FAmount;
    property Amount1: double read FAmount1 write FAmount1;
    property Amount2: double read FAmount2 write FAmount2;
    property Amount3: double read FAmount3 write FAmount3;
    property Sb: double read FSb write FSb;
    property Sunh: double read FSunh write FSunh;
    property CkType: string read FCkType write FCkType;
    property Gyscd: string read FGyscd write FGyscd;
    property Lb: string read FLb write FLb;
    property Kz: string read FKz write FKz;
    property Ck: string read FCk write FCk;
    property Ys: string read FYs write FYs;
    property Gh: string read FGh write FGh;
    property Sh: string read FSh write FSh;
    property Ckdate: string read FCkdate write FCkdate;
    property Yt: string read FYt write FYt;
    property Ps: integer read FPs write FPs;
    property searchpmgg: string read Fsearchpmgg write Fsearchpmgg;
    property AmountFlag: integer read FAmountFlag write FAmountFlag;
  end;

implementation

end.
