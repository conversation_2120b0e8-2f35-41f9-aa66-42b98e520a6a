object CC_Ccgk_XsFrame: TCC_Ccgk_XsFrame
  Left = 0
  Top = 0
  Width = 773
  Height = 704
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel3: TRzPanel
    Left = 0
    Top = 57
    Width = 773
    Height = 375
    Align = alClient
    BorderOuter = fsNone
    TabOrder = 0
    object RzPanel4: TRzPanel
      Left = 0
      Top = 0
      Width = 45
      Height = 375
      Align = alLeft
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 0
    end
    object RzPanel5: TRzPanel
      Left = 728
      Top = 0
      Width = 45
      Height = 375
      Align = alRight
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 1
    end
    object RzPanel6: TRzPanel
      Left = 45
      Top = 0
      Width = 683
      Height = 375
      Align = alClient
      BorderOuter = fsNone
      TabOrder = 2
      object Chart1: TChart
        Left = 0
        Top = 0
        Width = 683
        Height = 375
        BackWall.Dark3D = False
        BackWall.Pen.Color = clGray
        BackWall.Transparent = False
        BackWall.Visible = False
        Border.SmallSpace = 1
        Border.Visible = True
        BottomWall.Color = clGray
        BottomWall.Dark3D = False
        BottomWall.Pen.Color = clGray
        Foot.Brush.Gradient.Colors = <
          item
            Color = clYellow
          end>
        Foot.Brush.Gradient.StartColor = clYellow
        Foot.Font.Color = clBlue
        Foot.Font.Style = []
        Gradient.Colors = <
          item
            Color = clYellow
          end>
        Gradient.StartColor = clYellow
        LeftWall.Color = clSilver
        LeftWall.Dark3D = False
        LeftWall.Pen.Color = clGray
        LeftWall.Pen.SmallSpace = 1
        Legend.Color = clInfoBk
        Legend.ColorWidth = 40
        Legend.Font.Height = -13
        Legend.Shadow.Color = 13421772
        Legend.Shadow.HorizSize = 0
        Legend.Shadow.Transparency = 0
        Legend.Shadow.VertSize = 0
        Legend.Symbol.Width = 40
        RightWall.Dark3D = False
        RightWall.Pen.Color = clGray
        RightWall.Visible = True
        SubFoot.Brush.Gradient.Colors = <
          item
            Color = clYellow
          end>
        SubFoot.Brush.Gradient.StartColor = clYellow
        SubFoot.Font.Color = clBlue
        SubFoot.Font.Style = []
        SubTitle.Brush.Gradient.Colors = <
          item
            Color = clYellow
          end>
        SubTitle.Brush.Gradient.StartColor = clYellow
        Title.Brush.Gradient.Colors = <
          item
            Color = clYellow
          end>
        Title.Brush.Gradient.StartColor = clYellow
        Title.Color = clInfoBk
        Title.Font.Color = clBlack
        Title.Font.Height = -13
        Title.Font.Style = [fsBold]
        Title.Frame.Color = clGray
        Title.Shadow.HorizSize = 0
        Title.Shadow.VertSize = 0
        Title.Text.Strings = (
          #38144#21806#36208#21183#22270)
        Title.Visible = False
        BottomAxis.Axis.Color = clSilver
        BottomAxis.Axis.Width = 1
        BottomAxis.Grid.Color = clBlack
        BottomAxis.Grid.Visible = False
        BottomAxis.GridCentered = True
        BottomAxis.LabelsFormat.Font.Height = -13
        BottomAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        BottomAxis.LabelsFormat.TextAlignment = taCenter
        BottomAxis.MinorTicks.Visible = False
        BottomAxis.RoundFirstLabel = False
        BottomAxis.Ticks.Color = clBlack
        BottomAxis.TicksInner.Visible = False
        BottomAxis.Title.Font.Height = -13
        Chart3DPercent = 8
        ClipPoints = False
        DepthAxis.Axis.Color = clSilver
        DepthAxis.Axis.Width = 1
        DepthAxis.Grid.Color = clBlack
        DepthAxis.LabelsFormat.Font.Height = -13
        DepthAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        DepthAxis.LabelsFormat.TextAlignment = taCenter
        DepthAxis.MinorTicks.Visible = False
        DepthAxis.Ticks.Color = clBlack
        DepthAxis.TicksInner.Visible = False
        DepthTopAxis.Axis.Color = clSilver
        DepthTopAxis.Axis.Width = 1
        DepthTopAxis.Grid.Color = clBlack
        DepthTopAxis.LabelsFormat.Font.Height = -13
        DepthTopAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        DepthTopAxis.LabelsFormat.TextAlignment = taCenter
        DepthTopAxis.MinorTicks.Visible = False
        DepthTopAxis.Ticks.Color = clBlack
        DepthTopAxis.TicksInner.Visible = False
        Frame.Color = clGray
        LeftAxis.Axis.Color = clSilver
        LeftAxis.Axis.Width = 1
        LeftAxis.Axis.SmallSpace = 1
        LeftAxis.AxisValuesFormat = '0'
        LeftAxis.ExactDateTime = False
        LeftAxis.Grid.Width = 0
        LeftAxis.Grid.SmallSpace = 1
        LeftAxis.LabelsAlign = alOpposite
        LeftAxis.LabelsFormat.Font.Height = -13
        LeftAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        LeftAxis.LabelsFormat.Font.OutLine.Color = clDefault
        LeftAxis.LabelsFormat.Font.OutLine.SmallSpace = 1
        LeftAxis.LabelsFormat.Frame.Color = clWhite
        LeftAxis.LabelsFormat.Frame.Width = 0
        LeftAxis.LabelsFormat.Frame.Fill.Gradient.Visible = True
        LeftAxis.LabelsFormat.Frame.SmallSpace = 1
        LeftAxis.LabelsFormat.Frame.Visible = False
        LeftAxis.LabelsFormat.TextAlignment = taCenter
        LeftAxis.MinorGrid.Width = 0
        LeftAxis.MinorGrid.SmallSpace = 1
        LeftAxis.MinorTickCount = 0
        LeftAxis.MinorTicks.Visible = False
        LeftAxis.Ticks.Color = clBlack
        LeftAxis.Ticks.SmallSpace = 1
        LeftAxis.TicksInner.Visible = False
        LeftAxis.TickOnLabelsOnly = False
        RightAxis.Axis.Color = clSilver
        RightAxis.Axis.Width = 1
        RightAxis.Grid.Color = clBlack
        RightAxis.LabelsFormat.Font.Height = -13
        RightAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        RightAxis.LabelsFormat.TextAlignment = taCenter
        RightAxis.MinorTicks.Visible = False
        RightAxis.Ticks.Color = clBlack
        RightAxis.TicksInner.Visible = False
        TopAxis.Axis.Color = clSilver
        TopAxis.Axis.Width = 1
        TopAxis.Grid.Color = clBlack
        TopAxis.Grid.Visible = False
        TopAxis.LabelsFormat.Font.Height = -13
        TopAxis.LabelsFormat.Font.Name = #24494#36719#38597#40657
        TopAxis.LabelsFormat.TextAlignment = taCenter
        TopAxis.MinorTicks.Visible = False
        TopAxis.Ticks.Color = clBlack
        TopAxis.TicksInner.Visible = False
        View3D = False
        View3DOptions.OrthoAngle = 35
        View3DOptions.Perspective = 16
        Zoom.Allow = False
        Zoom.Animated = True
        Zoom.Pen.Mode = pmNotXor
        Zoom.Pen.SmallSpace = 1
        Zoom.Pen.Visible = False
        Align = alClient
        BevelOuter = bvNone
        Color = clWhite
        TabOrder = 0
        DefaultCanvas = 'TGDIPlusCanvas'
        PrintMargins = (
          15
          20
          15
          20)
        ColorPaletteIndex = -2
        ColorPalette = (
          16751001
          6697881
          13434879
          16777164
          6684774
          8421631
          13395456
          16764108
          8388608
          16711935
          65535
          16776960
          8388736
          128
          8421376
          16711680
          16763904
          16777164
          13434828
          65535
          16764057
          13408767)
        object Series1: TBarSeries
          BarBrush.Gradient.EndColor = 16751001
          BarPen.Visible = False
          Marks.Font.Height = -13
          Marks.Frame.Color = clGray
          Marks.Shadow.Color = 13421772
          Marks.Shadow.HorizSize = 2
          Marks.Shadow.VertSize = 2
          Marks.Transparent = True
          Marks.Visible = False
          DataSource = SeriesTextSource1
          SeriesColor = 12419406
          Title = '15'#24180
          BarWidthPercent = 40
          Gradient.EndColor = 16751001
          SideMargins = False
          TickLines.SmallSpace = 1
          XValues.Name = 'X'
          XValues.Order = loAscending
          YValues.Name = 'Y'
          YValues.Order = loNone
        end
        object Series2: TBarSeries
          BarBrush.Gradient.EndColor = 6697881
          BarPen.SmallSpace = 1
          BarPen.Visible = False
          Marks.Font.Height = -13
          Marks.Frame.Color = clGray
          Marks.Shadow.Color = 13421772
          Marks.Shadow.HorizSize = 2
          Marks.Shadow.VertSize = 2
          Marks.Transparent = True
          Marks.Visible = False
          DataSource = SeriesTextSource2
          SeriesColor = 5001409
          Title = '16'#24180
          BarWidthPercent = 40
          Gradient.EndColor = 6697881
          SideMargins = False
          Sides = 23
          XValues.Name = 'X'
          XValues.Order = loAscending
          YValues.Name = #38271#26465
          YValues.Order = loNone
        end
        object GridBandTool1: TGridBandTool
          Band1.Color = 15329769
          AxisID = 2
        end
        object GridBandTool2: TGridBandTool
          Band1.Color = 15329769
          AxisID = 2
        end
      end
    end
  end
  object RzPanel1: TRzPanel
    Left = 0
    Top = 432
    Width = 773
    Height = 272
    Align = alBottom
    BorderOuter = fsNone
    TabOrder = 1
    object XsAdvStringGrid: TAdvStringGrid
      Left = 45
      Top = 25
      Width = 683
      Height = 247
      Cursor = crDefault
      Align = alClient
      BevelInner = bvNone
      BevelOuter = bvNone
      Ctl3D = True
      DefaultRowHeight = 25
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      RowCount = 8
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing, goTabs]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      ScrollBars = ssVertical
      TabOrder = 0
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #24494#36719#38597#40657
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      EnhRowColMove = False
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -11
      FilterDropDown.Font.Name = 'Tahoma'
      FilterDropDown.Font.Style = []
      FilterDropDown.TextChecked = 'Checked'
      FilterDropDown.TextUnChecked = 'Unchecked'
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 35
      FixedRowHeight = 25
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      FloatFormat = '%.2f'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '7.2.8.0'
      ColWidths = (
        35
        155
        253
        84
        64)
    end
    object RzPanel7: TRzPanel
      Left = 0
      Top = 25
      Width = 45
      Height = 247
      Align = alLeft
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 1
    end
    object RzPanel8: TRzPanel
      Left = 728
      Top = 25
      Width = 45
      Height = 247
      Align = alRight
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 2
    end
    object RzPanel9: TRzPanel
      Left = 0
      Top = 0
      Width = 773
      Height = 25
      Align = alTop
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 3
    end
  end
  object RzPanel2: TRzPanel
    Left = 0
    Top = 0
    Width = 773
    Height = 57
    Align = alTop
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 2
    object RzLabel1: TRzLabel
      Left = 586
      Top = 16
      Width = 62
      Height = 19
      Caption = #38144'  '#21806
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -19
      Font.Name = #23435#20307
      Font.Style = [fsBold]
      ParentFont = False
    end
    object RzLabel2: TRzLabel
      Left = 658
      Top = 18
      Width = 48
      Height = 16
      Caption = #36208#21183#22270
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -16
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
    end
  end
  object SeriesTextSource1: TSeriesTextSource
    Active = True
    Automatic = False
    Chart = Chart1
    HeaderLines = 1
    Fields = <>
    FieldSeparator = ','
    Series = Series1
    Text.Strings = (
      #26376#20221','#25968#37327
      '1'#26376',10'
      '2'#26376',20'
      '3'#26376',30'
      '4'#26376',40'
      '5'#26376',40'
      '6'#26376',40'
      '7'#26376',40'
      '8'#26376',40'
      '9'#26376',40'
      '10'#26376',40'
      '11'#26376',40'
      '12'#26376',40')
    Left = 136
    Top = 8
  end
  object SeriesTextSource2: TSeriesTextSource
    Active = True
    Automatic = False
    HeaderLines = 1
    Fields = <>
    FieldSeparator = ','
    Series = Series2
    Text.Strings = (
      #26376#20221','#25968#37327
      '1'#26376',11'
      '2'#26376',22'
      '3'#26376',33'
      '4'#26376',44'
      '5'#26376',55'
      '6'#26376',66'
      '7'#26376',77'
      '8'#26376',0'
      '9'#26376',0'
      '10'#26376',0'
      '11'#26376',0'
      '12'#26376',0')
    Left = 64
    Top = 8
  end
end
