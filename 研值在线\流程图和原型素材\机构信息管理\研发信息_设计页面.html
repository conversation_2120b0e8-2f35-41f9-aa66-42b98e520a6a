<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">研发信息管理</h1>
            <p class="text-gray-600">整合展示机构在研发经费与人员两大核心要素上的投入情况，支持决策分析与数据管理</p>
        </div>

        <!-- 年度切换区 -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex space-x-2 overflow-x-auto pb-2">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md whitespace-nowrap">2023</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md whitespace-nowrap hover:bg-gray-50">2022</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md whitespace-nowrap hover:bg-gray-50">2021</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md whitespace-nowrap hover:bg-gray-50">2020</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md whitespace-nowrap hover:bg-gray-50">2019</button>
                </div>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    新增年度
                </button>
            </div>
        </div>

        <!-- 研发费用卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- 研发费用合计 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">研发费用合计</h3>
                        <p class="text-2xl font-bold text-blue-600">¥12,568,900</p>
                        <p class="text-sm text-gray-500 mt-2">同比增长 +12.5%</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm flex items-center">
                        详情
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: 100%"></div>
                    </div>
                </div>
            </div>

            <!-- 企业内部研发费用 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">企业内部研发费用</h3>
                        <p class="text-2xl font-bold text-green-600">¥8,245,300</p>
                        <p class="text-sm text-gray-500 mt-2">占比 65.6%</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm flex items-center">
                        详情
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: 65.6%"></div>
                    </div>
                </div>
            </div>

            <!-- 委托外部研发费用 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">委托外部研发费用</h3>
                        <p class="text-2xl font-bold text-purple-600">¥3,125,400</p>
                        <p class="text-sm text-gray-500 mt-2">占比 24.9%</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm flex items-center">
                        详情
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-purple-600 h-2 rounded-full" style="width: 24.9%"></div>
                    </div>
                </div>
            </div>

            <!-- 政府资金占比 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="flex justify-between items-start">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">政府资金占比</h3>
                        <p class="text-2xl font-bold text-yellow-600">¥1,198,200</p>
                        <p class="text-sm text-gray-500 mt-2">占比 9.5%</p>
                    </div>
                    <a href="#" class="text-blue-600 text-sm flex items-center">
                        详情
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 9.5%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 费用结构趋势区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">研发费用结构趋势 (2019-2023)</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        PNG
                    </button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Excel
                    </button>
                </div>
            </div>
            <div id="cost-trend-chart" class="w-full h-[400px]"></div>
        </div>

        <!-- 研发人员统计区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">研发人员统计 (2023)</h2>
                <div class="flex items-center">
                    <span class="text-sm font-medium text-gray-700 mr-2">研究院职工总数:</span>
                    <span class="text-xl font-bold text-blue-600">156</span>
                    <span class="text-sm text-green-600 ml-2">(+12.3%)</span>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学历/职称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人数</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">同比变化</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20.5%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+5.2%</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">68</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">43.6%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+8.1%</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">本科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28.8%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">-3.4%</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高级职称</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">28</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">17.9%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+2.7%</td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">中级职称</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">56</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">35.9%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600">+4.3%</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 人员梯度趋势区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">研发人员梯度趋势 (2019-2023)</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        PNG
                    </button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Excel
                    </button>
                </div>
            </div>
            <div id="personnel-trend-chart" class="w-full h-[400px]"></div>
        </div>

        <!-- 数据质量与预警区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">数据质量与预警</h2>
                <span class="text-sm text-gray-500">最后检查时间: 2023-12-15 14:30</span>
            </div>
            <div class="space-y-4">
                <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-red-800">研发费用异常波动: 2023年委托外部研发费用同比增长45.6%</p>
                            <div class="mt-1 flex items-center">
                                <a href="#" class="text-sm text-red-700 hover:text-red-900 mr-4">去修复</a>
                                <a href="#" class="text-sm text-red-700 hover:text-red-900">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-yellow-800">3个数据字段缺失: 2023年研发人员博士后流动站数据未填写</p>
                            <div class="mt-1 flex items-center">
                                <a href="#" class="text-sm text-yellow-700 hover:text-yellow-900 mr-4">去补录</a>
                                <a href="#" class="text-sm text-yellow-700 hover:text-yellow-900">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-blue-800">提醒: 2024年度研发计划预算尚未提交</p>
                            <div class="mt-1 flex items-center">
                                <a href="#" class="text-sm text-blue-700 hover:text-blue-900 mr-4">去填写</a>
                                <a href="#" class="text-sm text-blue-700 hover:text-blue-900">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通用操作区 -->
    <div class="fixed right-6 top-6 flex space-x-2">
        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            编辑全部信息
        </button>
        <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            批量导入
        </button>
        <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            历史版本
        </button>
        <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            数据导出
        </button>
        <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
            </svg>
            打印
        </button>
    </div>

    <script>
        // 费用结构趋势图
        const costTrendChart = echarts.init(document.getElementById('cost-trend-chart'));
        const costTrendOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['企业内部研发', '委托外部研发', '政府资金']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2019', '2020', '2021', '2022', '2023']
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value} 万'
                }
            },
            series: [
                {
                    name: '企业内部研发',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [520, 632, 701, 734, 824],
                    itemStyle: {
                        color: '#91CC75'
                    }
                },
                {
                    name: '委托外部研发',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [120, 132, 141, 174, 312],
                    itemStyle: {
                        color: '#FAC858'
                    }
                },
                {
                    name: '政府资金',
                    type: 'bar',
                    stack: 'total',
                    emphasis: {
                        focus: 'series'
                    },
                    data: [60, 72, 81, 94, 119],
                    itemStyle: {
                        color: '#EE6666'
                    }
                }
            ]
        };
        costTrendChart.setOption(costTrendOption);

        // 人员梯度趋势图
        const personnelTrendChart = echarts.init(document.getElementById('personnel-trend-chart'));
        const personnelTrendOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['博士', '硕士', '高级职称']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['2019', '2020', '2021', '2022', '2023']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '博士',
                    type: 'line',
                    data: [18, 22, 25, 27, 32],
                    itemStyle: {
                        color: '#5470C6'
                    },
                    symbolSize: 8,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '硕士',
                    type: 'line',
                    data: [45, 52, 58, 63, 68],
                    itemStyle: {
                        color: '#91CC75'
                    },
                    symbolSize: 8,
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '高级职称',
                    type: 'line',
                    data: [20, 22, 24, 26, 28],
                    itemStyle: {
                        color: '#EE6666'
                    },
                    symbolSize: 8,
                    lineStyle: {
                        width: 3
                    }
                }
            ]
        };
        personnelTrendChart.setOption(personnelTrendOption);

        // 窗口大小变化时重绘图表
        window.addEventListener('resize', function() {
            costTrendChart.resize();
            personnelTrendChart.resize();
        });
    </script>
</body>
</html>