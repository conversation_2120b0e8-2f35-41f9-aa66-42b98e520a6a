'use client'

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'
import { Badge } from "@/components/ui/badge"

const data = [
  { name: '1月', 硬件故障: 4, 软件异常: 7, 安全事件: 2 },
  { name: '2月', 硬件故障: 3, 软件异常: 5, 安全事件: 1 },
  { name: '3月', 硬件故障: 2, 软件异常: 6, 安全事件: 3 },
  { name: '4月', 硬件故障: 5, 软件异常: 4, 安全事件: 0 },
  { name: '5月', 硬件故障: 1, 软件异常: 3, 安全事件: 1 },
  { name: '6月', 硬件故障: 3, 软件异常: 2, 安全事件: 2 },
]

const suggestions = [
  { id: 1, description: '升级数据库服务器以提高性能', status: '已采纳', impact: '预计可减少30%的数据库相关故障' },
  { id: 2, description: '实施双因素身份验证以增强安全性', status: '评估中', impact: '可显著降低未授权访问的风险' },
  { id: 3, description: '优化负载均衡算法', status: '计划中', impact: '预计可提高系统整体响应速度15%' },
]

export function TrendsAndSuggestions() {
  // 更专业的配色方案
  const chartColors = {
    primary: '#8884d8',    // 紫色
    secondary: '#82ca9d',  // 绿色
    tertiary: '#ffc658',   // 黄色
    grid: '#f0f0f0',      // 网格线颜色
    text: '#666666'       // 文字颜色
  };

  return (
    <div className="space-y-4">
      <Card className="border-[#E5E9EF] shadow-sm">
        <CardHeader className="border-b border-[#E5E9EF]">
          <CardTitle className="text-[#1E293B] text-lg font-medium">历史趋势分析</CardTitle>
        </CardHeader>
        <CardContent className="pt-6">
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <CartesianGrid 
                strokeDasharray="3 3" 
                stroke={chartColors.grid} 
                vertical={false}
              />
              <XAxis 
                dataKey="name" 
                stroke={chartColors.text}
                axisLine={{ stroke: chartColors.grid }}
                tickLine={false}
              />
              <YAxis 
                stroke={chartColors.text}
                axisLine={{ stroke: chartColors.grid }}
                tickLine={false}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #E5E9EF',
                  borderRadius: '4px',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                }}
              />
              <Legend 
                wrapperStyle={{
                  paddingTop: '20px'
                }}
              />
              <Bar 
                dataKey="硬件故障" 
                fill={chartColors.primary} 
                radius={[4, 4, 0, 0]}
              />
              <Bar 
                dataKey="软件异常" 
                fill={chartColors.secondary} 
                radius={[4, 4, 0, 0]}
              />
              <Bar 
                dataKey="安全事件" 
                fill={chartColors.tertiary} 
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <Card className="border-[#E5E9EF] shadow-sm">
        <CardHeader className="border-b border-[#E5E9EF]">
          <CardTitle className="text-[#1E293B] text-lg font-medium">功能性拓展建议</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow className="bg-[#F8FAFC] hover:bg-[#F8FAFC]">
                <TableHead className="text-[#666666]">建议</TableHead>
                <TableHead className="text-[#666666]">状态</TableHead>
                <TableHead className="text-[#666666]">预期影响</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suggestions.map((suggestion) => (
                <TableRow key={suggestion.id} className="hover:bg-[#F8FAFC]">
                  <TableCell className="font-medium">{suggestion.description}</TableCell>
                  <TableCell>
                    <Badge 
                      className={
                        suggestion.status === "已采纳" 
                          ? "bg-[#EDF7ED] text-[#1B5E20]" 
                          : suggestion.status === "评估中"
                          ? "bg-[#FFF4E5] text-[#E65100]"
                          : "bg-[#F5F5F5] text-[#666666]"
                      }
                    >
                      {suggestion.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-[#666666]">{suggestion.impact}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
