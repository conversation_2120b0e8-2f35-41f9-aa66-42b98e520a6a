'use client'

import { Card, CardContent } from "@/components/ui/card"
import { Database, Server, Layout, Activity } from "lucide-react"

const stats = [
  {
    title: "资产总数",
    value: "1,234",
    change: "+12.3%",
    icon: Database,
    gradient: "from-[#0EA5E9] to-[#2563EB]"
  },
  {
    title: "基础设施资产",
    value: "456",
    change: "+8.2%",
    icon: Server,
    gradient: "from-[#10B981] to-[#059669]"
  },
  {
    title: "应用资产",
    value: "778",
    change: "+15.8%",
    icon: Layout,
    gradient: "from-[#F59E0B] to-[#D97706]"
  },
  {
    title: "健康度",
    value: "98.5%",
    change: "+1.2%",
    icon: Activity,
    gradient: "from-[#8B5CF6] to-[#6D28D9]"
  }
]

export function AssetOverviewStats() {
  return (
    <div className="grid gap-6 grid-cols-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <Card key={index} className="relative overflow-hidden">
            <div className={`absolute inset-0 bg-gradient-to-r ${stat.gradient} opacity-10`} />
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">{stat.title}</p>
                  <h3 className="text-2xl font-bold mt-2">{stat.value}</h3>
                  <p className={`text-sm mt-1 ${
                    stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change} 较上期
                  </p>
                </div>
                <Icon className="h-8 w-8 text-gray-400" />
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
} 