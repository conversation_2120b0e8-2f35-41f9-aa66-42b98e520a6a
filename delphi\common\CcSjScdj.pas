unit CcSjScdj;

interface

uses
  Classes;

type
  TCcSjScdj = class
  private
    FSjscdjid: Integer;
    FSjschzdjid: Integer;
    FDdid: Integer;
    FScid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FScxh: Integer;
    FSccj: string;
    FSccj_a: string;
    FSccj_b: string;
    FScgs: double;
    FYxts: double;
    FJyts: double;
    FYjtq: double;
    FRcl: double;
    FGs: double;
    FDefaultCj: string;
    FChangeCj: string;
    FScJhBeginTime: string;
    FScJhEndTime: string;
    FScSjBeginTime: string;
    FScSjEndTime: string;
    FScState: string;
    FScNote: string;
    FScNote1: string;
    FScCHRQ: string;
    FScCHRQ1: string;
    FScWcrq: string;
    FScJqrq1: string;
    FScJqrq2: string;
    FLy: string;
    FShangx1: string;
    FXiax1: string;
    FWanc: string;
    FHouz_jd: double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
    FYjState: Integer;

    FGh_a: string;
    FGh_b: string;
    FGh_c: string;
    FGh_d: string;

    FHzDate: string;
    FHz1: double;
    FHz2: double;
    FHz3: double;
    FHz4: double;
    FHz5: double;
    FHz6: double;
    FHz7: double;
    FHz8: double;
    FHz9: double;
    FHz10: double;
    FHz11: double;
    FHz12: double;
    FHz13: double;
    FHz14: double;
    FHz15: double;
    FHz1Bl: double;
    FHz2Bl: double;
    FHz3Bl: double;
    FHz4Bl: double;
    FHz5Bl: double;
    FHz6Bl: double;
    FHz7Bl: double;
    FHz8Bl: double;
    FHz9Bl: double;
    FHz10Bl: double;
    FHz11Bl: double;
    FHz12Bl: double;
    FHz13Bl: double;
    FHz14Bl: double;
    FHz15Bl: double;
    FHzSum: double;
    FHzSumBl: double;

  public
    property Sjscdjid: Integer read FSjscdjid write FSjscdjid;
    property Sjschzdjid: Integer read FSjschzdjid write FSjschzdjid;
    property Ddid: Integer read FDdid write FDdid;
    property Scid: Integer read FScid write FScid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Scxh: Integer read FScxh write FScxh;
    property Sccj: string read FSccj write FSccj;
    property Sccj_a: string read FSccj_a write FSccj_a;
    property Sccj_b: string read FSccj_b write FSccj_b;
    property Scgs: double read FScgs write FScgs;
    property Yxts: double read FYxts write FYxts;
    property Jyts: double read FJyts write FJyts;
    property Yjtq: double read FYjtq write FYjtq;
    property Rcl: double read FRcl write FRcl;
    property Gs: double read FGs write FGs;
    property DefaultCj: string read FDefaultCj write FDefaultCj;
    property ChangeCj: string read FChangeCj write FChangeCj;

    property ScJhBeginTime: string read FScJhBeginTime write FScJhBeginTime;
    property ScJhEndTime: string read FScJhEndTime write FScJhEndTime;
    property ScSjBeginTime: string read FScSjBeginTime write FScSjBeginTime;
    property ScSjEndTime: string read FScSjEndTime write FScSjEndTime;
    property ScState: string read FScState write FScState;
    property ScNote: string read FScNote write FScNote;
    property ScNote1: string read FScNote1 write FScNote1;
    property ScCHRQ: string read FScCHRQ write FScCHRQ;
    property ScCHRQ1: string read FScCHRQ1 write FScCHRQ1;
    property ScJqrq1: string read FScJqrq1 write FScJqrq1;
    property ScWcrq: string read FScWcrq write FScWcrq;
    property ScJqrq2: string read FScJqrq2 write FScJqrq2;
    property Shangx1: string read FShangx1 write FShangx1;
    property Xiax1: string read FXiax1 write FXiax1;
    property Wanc: string read FWanc write FWanc;
    property Houz_jd: double read FHouz_jd write FHouz_jd;
    property Ly: string read FLy write FLy;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
    property YjState: Integer read FYjState write FYjState;

    property Gh_a: string read FGh_a write FGh_a;
    property Gh_b: string read FGh_b write FGh_b;
    property Gh_c: string read FGh_c write FGh_c;
    property Gh_d: string read FGh_d write FGh_d;

    property HzDate: string read FHzDate write FHzDate;
    property Hz1: double read FHz1 write FHz1;
    property Hz2: double read FHz2 write FHz2;
    property Hz3: double read FHz3 write FHz3;
    property Hz4: double read FHz4 write FHz4;
    property Hz5: double read FHz5 write FHz5;
    property Hz6: double read FHz6 write FHz6;
    property Hz7: double read FHz7 write FHz7;
    property Hz8: double read FHz8 write FHz8;
    property Hz9: double read FHz9 write FHz9;
    property Hz10: double read FHz10 write FHz10;
    property Hz11: double read FHz11 write FHz11;
    property Hz12: double read FHz12 write FHz12;
    property Hz13: double read FHz13 write FHz13;
    property Hz14: double read FHz14 write FHz14;
    property Hz15: double read FHz15 write FHz15;
    property Hz1Bl: double read FHz1Bl write FHz1Bl;
    property Hz2Bl: double read FHz2Bl write FHz2Bl;
    property Hz3Bl: double read FHz3Bl write FHz3Bl;
    property Hz4Bl: double read FHz4Bl write FHz4Bl;
    property Hz5Bl: double read FHz5Bl write FHz5Bl;
    property Hz6Bl: double read FHz6Bl write FHz6Bl;
    property Hz7Bl: double read FHz7Bl write FHz7Bl;
    property Hz8Bl: double read FHz8Bl write FHz8Bl;
    property Hz9Bl: double read FHz9Bl write FHz9Bl;
    property Hz10Bl: double read FHz10Bl write FHz10Bl;
    property Hz11Bl: double read FHz11Bl write FHz11Bl;
    property Hz12Bl: double read FHz12Bl write FHz12Bl;
    property Hz13Bl: double read FHz13Bl write FHz13Bl;
    property Hz14Bl: double read FHz14Bl write FHz14Bl;
    property Hz15Bl: double read FHz15Bl write FHz15Bl;
    property HzSum: double read FHzSum write FHzSum;
    property HzSumBl: double read FHzSumBl write FHzSumBl;

  end;

implementation

end.
