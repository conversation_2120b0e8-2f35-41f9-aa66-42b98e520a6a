unit CcSjSzbdj;

interface
uses
  Classes;

type
  TCcSjSzbdj = class
  private

    FSjszbdjid: Integer;
    FDdid: Integer;
    FSzbpmid: string;
    FSzbggid: string;
    FTgs: Double;
    FPbj: Double;
    FZl: Double;
    FRsj: Double;
    FSzbcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjszbdjid: integer read FSjszbdjid write FSjszbdjid;
    property Ddid: integer read FDdid write FDdid;
    property Szbpmid: string read FSzbpmid write FSzbpmid;
    property Szbggid: string read FSzbggid write FSzbggid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Zl: double read FZl write FZl;
    property Rsj: double read FRsj write FRsj;
    property Szbcb: double read FSzbcb write FSzbcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

