<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重大项目监测 - 科技创新监测平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white border-b border-gray-200 card-shadow sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-project-diagram text-orange-500 mr-3"></i>
                        重大项目监测
                    </h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-calendar-alt"></i>
                        <span>数据时间：2025年1~6月</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-orange-50 text-orange-600 rounded-lg hover:bg-orange-100 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出项目清单
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="gradient-bg min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            
            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">项目总数</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">486</p>
                            <p class="text-xs text-orange-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+28个
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-project-diagram text-orange-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">总投资额</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">85.6亿</p>
                            <p class="text-xs text-green-600 mt-1">
                                <i class="fas fa-chart-line mr-1"></i>+12.3%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-coins text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">在研项目</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">298</p>
                            <p class="text-xs text-blue-600 mt-1">
                                <i class="fas fa-play mr-1"></i>进行中
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-play-circle text-blue-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">预警项目</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">20</p>
                            <p class="text-xs text-red-600 mt-1">
                                <i class="fas fa-exclamation-triangle mr-1"></i>需关注
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">项目筛选与分析</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-orange-100 text-orange-700 rounded-lg text-sm">列表视图</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">投资排名</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">进度图</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">项目类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                            <option value="">全部类型</option>
                            <option value="hitech">高新投资项目</option>
                            <option value="rd">企业自研项目</option>
                            <option value="transform">成果转化项目</option>
                            <option value="cooperation">产学研合作</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">"510"领域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                            <option value="">全部领域</option>
                            <option value="smart">智能制造</option>
                            <option value="bio">生物医药</option>
                            <option value="material">新材料</option>
                            <option value="energy">新能源</option>
                            <option value="digital">数字经济</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">项目状态</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                            <option value="">全部状态</option>
                            <option value="planning">立项阶段</option>
                            <option value="ongoing">执行中</option>
                            <option value="delay">延期</option>
                            <option value="completed">已完成</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">投资规模</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                            <option value="">全部规模</option>
                            <option value="large">超大型(>1亿)</option>
                            <option value="medium">大型(1000万-1亿)</option>
                            <option value="small">中小型(<1000万)</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">承担单位</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                            <option value="">全部单位</option>
                            <option value="enterprise">企业</option>
                            <option value="university">高等院校</option>
                            <option value="institute">科研院所</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 项目分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- "510"领域分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">各类项目"510"领域分布</h3>
                    <div class="space-y-4">
                        <div class="text-center p-3 bg-orange-50 rounded-lg border">
                            <p class="text-sm text-gray-600">总体覆盖率</p>
                            <p class="text-2xl font-bold text-orange-600">78.6%</p>
                        </div>
                        
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                    <span class="text-sm text-gray-700">智能制造</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-semibold text-gray-900">156个</span>
                                    <span class="text-xs text-gray-500 block">32.1%</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                    <span class="text-sm text-gray-700">新材料</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-semibold text-gray-900">98个</span>
                                    <span class="text-xs text-gray-500 block">20.2%</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                    <span class="text-sm text-gray-700">生物医药</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-semibold text-gray-900">78个</span>
                                    <span class="text-xs text-gray-500 block">16.0%</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                                    <span class="text-sm text-gray-700">新能源</span>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm font-semibold text-gray-900">50个</span>
                                    <span class="text-xs text-gray-500 block">10.3%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 投资分析 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">项目投资分析</h3>
                    <div class="h-64 bg-gradient-to-br from-gray-50 to-orange-50 rounded-lg flex items-center justify-center relative">
                        <img src="https://images.unsplash.com/photo-1590283603385-17ffb3a7f29f?w=400&h=300&fit=crop&crop=entropy" 
                             alt="项目投资分析图" 
                             class="w-full h-full object-cover rounded-lg opacity-70">
                        <div class="absolute inset-0 bg-orange-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                            <div class="text-center text-orange-800">
                                <i class="fas fa-chart-bar text-3xl mb-2"></i>
                                <p class="font-semibold">项目投资分布图</p>
                                <p class="text-sm mt-1">按领域和规模分类</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-4 text-center">
                        <div class="p-3 bg-green-50 rounded-lg">
                            <p class="text-lg font-bold text-green-600">85.6亿</p>
                            <p class="text-xs text-gray-600">总投资额</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <p class="text-lg font-bold text-blue-600">1,761万</p>
                            <p class="text-xs text-gray-600">平均投资</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TOP投资项目 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 高新投资项目TOP10 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">高新投资项目 TOP10</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">1</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">智能制造产业园项目</p>
                                    <p class="text-xs text-gray-600">宁波智能制造有限公司</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-yellow-600">3.2亿</span>
                                <p class="text-xs text-gray-500">投资额</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-gray-400 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">2</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">新材料研发中心</p>
                                    <p class="text-xs text-gray-600">江北新材料科技园</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-gray-600">2.8亿</span>
                                <p class="text-xs text-gray-500">投资额</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">3</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">生物医药产业基地</p>
                                    <p class="text-xs text-gray-600">鄞州生物医药园</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-orange-600">2.5亿</span>
                                <p class="text-xs text-gray-500">投资额</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">4. 新能源技术研发项目</span>
                                <span class="font-semibold">1.9亿</span>
                            </div>
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">5. 数字经济创新中心</span>
                                <span class="font-semibold">1.6亿</span>
                            </div>
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">6. 海洋工程装备项目</span>
                                <span class="font-semibold">1.4亿</span>
                            </div>
                        </div>
                        
                        <div class="pt-3 border-t border-gray-200 text-center">
                            <button class="text-orange-600 hover:text-orange-800 text-sm font-medium">
                                查看完整排名 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 企业自研项目投资额TOP10 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">企业自研项目投资额 TOP10</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">1</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">5G智能终端研发</p>
                                    <p class="text-xs text-gray-600">宁波通信科技公司</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-blue-600">8,500万</span>
                                <p class="text-xs text-gray-500">自研投入</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">2</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">高端装备制造平台</p>
                                    <p class="text-xs text-gray-600">宁波重工集团</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-green-600">7,200万</span>
                                <p class="text-xs text-gray-500">自研投入</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                            <div class="flex items-center">
                                <div class="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center mr-3 text-xs font-bold">3</div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">新药创制研发项目</p>
                                    <p class="text-xs text-gray-600">华海药业</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-bold text-purple-600">6,800万</span>
                                <p class="text-xs text-gray-500">自研投入</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2">
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">4. 新材料技术创新</span>
                                <span class="font-semibold">5,400万</span>
                            </div>
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">5. 智能控制系统开发</span>
                                <span class="font-semibold">4,900万</span>
                            </div>
                            <div class="flex justify-between text-sm p-2 bg-gray-50 rounded">
                                <span class="text-gray-600">6. 环保设备技术升级</span>
                                <span class="font-semibold">4,200万</span>
                            </div>
                        </div>
                        
                        <div class="pt-3 border-t border-gray-200 text-center">
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                查看完整排名 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目预警清单 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                        项目延期/验收预警清单
                    </h3>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-red-600 bg-red-50 px-3 py-1 rounded-full">
                            <i class="fas fa-bell mr-1"></i>
                            20个项目需要关注
                        </span>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="bg-red-50 border-b border-red-200">
                                <th class="px-4 py-3 text-left font-medium text-gray-700">项目名称</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">承担单位</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">预警类型</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">计划完成时间</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">当前进度</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">风险等级</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr class="hover:bg-red-50 bg-red-25">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-exclamation-triangle text-red-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">智能制造系统集成项目</p>
                                            <p class="text-xs text-gray-500">项目编号: PRJ-2024-001</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波智能制造有限公司</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">项目延期</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">2024-08-30</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">45%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">高风险</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-red-600 hover:text-red-800 text-xs mr-2">处理</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">详情</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-yellow-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-clock text-yellow-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">新材料技术研发项目</p>
                                            <p class="text-xs text-gray-500">项目编号: PRJ-2024-002</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">江北新材料研究院</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">验收预警</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">2024-09-15</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">78%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">中风险</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-yellow-600 hover:text-yellow-800 text-xs mr-2">跟进</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">详情</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-orange-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-hourglass-half text-orange-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">生物医药创新平台建设</p>
                                            <p class="text-xs text-gray-500">项目编号: PRJ-2024-003</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">鄞州生物医药园</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">进度滞后</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">2024-10-30</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 62%"></div>
                                        </div>
                                        <span class="text-xs text-gray-600">62%</span>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">中风险</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-orange-600 hover:text-orange-800 text-xs mr-2">关注</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-500">
                        显示 1-10 条，共 20 条预警记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-500">上一页</button>
                        <button class="px-3 py-1 bg-orange-500 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 项目统计总结 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">项目执行情况总结</h3>
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="text-center p-4 bg-green-50 rounded-lg">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <p class="text-lg font-bold text-green-600">298</p>
                        <p class="text-sm text-gray-600">正常执行项目</p>
                    </div>
                    
                    <div class="text-center p-4 bg-yellow-50 rounded-lg">
                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-clock text-yellow-600"></i>
                        </div>
                        <p class="text-lg font-bold text-yellow-600">12</p>
                        <p class="text-sm text-gray-600">延期项目</p>
                    </div>
                    
                    <div class="text-center p-4 bg-red-50 rounded-lg">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <p class="text-lg font-bold text-red-600">8</p>
                        <p class="text-sm text-gray-600">验收预警项目</p>
                    </div>
                    
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-trophy text-blue-600"></i>
                        </div>
                        <p class="text-lg font-bold text-blue-600">168</p>
                        <p class="text-sm text-gray-600">已完成项目</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技创新监测平台. 保留所有权利.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>数据更新频率：实时</span>
                    <span>|</span>
                    <span>监测项目总数：486个</span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html> 