<!-- Generated by SpecStory -->

# Designing a management backend or data dashboard (2025-07-07 07:23Z)

_**User**_



---

_**Assistant**_



---

_**User**_



---

_**Assistant**_

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',

---

_**User**_


### **【提示词模板正文 V2.1 - 优化版】**

你是一位追求卓越、经验丰富的全栈开发专家与UI/UX设计师。你不仅精通技术实现，更对用户体验和视觉美学有深刻的理解和严格的标准。你当前的任务是为一个已有的复杂Web应用项目，根据详细的需求文档，并**绝对严格地遵循**项目既定的设计规范，输出一个功能完整、代码优雅、视觉精致的**单文件HTML原型页面**。

### **一、 设计与实现心法 (Design & Implementation Philosophy)**

*   **结构至上 (Structure First):** 任何页面都必须是逻辑清晰的。使用卡片(`Card`)将功能块（如“筛选区”、“列表区”、“统计区”）明确分离。先思考布局骨架，再填充细节。
*   **空间呼吸感 (Whitespace is Key):** 善用空间创造秩序和焦点。
    *   页面级内边距: `p-6` 或 `p-8`。
    *   卡片间距: `gap-6`。
    *   卡片内部元素的堆叠间距: `space-y-4` 或 `space-y-6`。
*   **信息层级 (Hierarchy Matters):** 通过字号、字重和颜色来区分信息主次。
    *   页面主标题: `text-3xl font-bold`。
    *   卡片/区域标题: `text-lg font-semibold text-gray-900`。
    *   正文/标签: `text-sm text-gray-700`。
    *   辅助/提示文字: `text-xs text-gray-500`。
*   **交互一致性 (Consistent Interaction):** 遵循已建立的模式。
    *   **创建/编辑:** 统一使用模态框（Modal）或抽屉（Drawer）。
    *   **查看详情:** 跳转到新页面或在模态框中展示。
    *   **复杂视图切换:** 优先使用选项卡（Tabs）。
*   **细节是魔鬼 (Details Create Magic):**
    *   **交互反馈:** 所有可点击元素（按钮、链接）必须有 `hover:` 效果，如背景色变化、下划线等。
    *   **过渡动画:** 为 `hover` 效果和模态框的显隐添加 `transition` 和 `duration-200` 等类，增加流畅感。
    *   **图标点缀:** 在按钮、标题、关键信息旁恰当使用SVG图标，提升信息识别度。

### **二、 核心指令与项目规范 (Core Directives & Project Specifications)**

**1. 设计系统 (Design System):**
*   **布局:** 统一采用卡片式布局。结构为“页面大标题 `<h1>` + 内容区域 `<div>`”。内容区域高度常设为 `h-[calc(100vh-120px)]` 或类似计算值，以确保主要内容区在视口内滚动。
*   **颜色:** 主色调为蓝色 (`blue-600`)。页面背景使用 `bg-gradient-to-br from-gray-50 to-blue-50`。
*   **组件:** 必须使用 **Tailwind CSS** 构建一切。常用组件包括卡片（`bg-white rounded-lg shadow-md`）、按钮、输入框、选项卡、徽章、下拉菜单、模态框等。
*   **响应式:** 默认使用 Flexbox (`flex`) 和 Grid (`grid`) 进行响应式布局。

**2. 代码与风格参考 (Code & Style References):**
*   **这是圣经！** 下面提供的两个已开发页面是项目的黄金标准。你必须仔细分析它们的HTML结构、组件实现、Tailwind类名用法、布局方式和整体风格。**你即将创建的页面在视觉和代码风格上必须与它们保持高度一致，如同出自同一位开发者之手。**
    *   `[<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策对象管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">政策对象管理</h1>
            <p class="text-gray-600">构建覆盖主体类型、规模层级与行业领域的统一对象分类体系，为创新主体在政策享受分析与精准匹配过程中提供可信的标签依据</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">主体类型</label>
                            <div class="flex flex-wrap gap-2">
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    企业
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    高校
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    科研院所
                                </label>
                                <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                    个人
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">规模区间</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部规模</option>
                                <option value="micro">微型（10人以下）</option>
                                <option value="small">小型（10-99人）</option>
                                <option value="medium">中型（100-499人）</option>
                                <option value="large">大型（500人以上）</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">行业类别</label>
                            <div class="relative">
                                <button id="industryButton" onclick="toggleIndustryTree()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                                    <span id="selectedIndustry">请选择行业类别</span>
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div id="industryTree" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                                    <ul class="space-y-2">
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    制造业
                                                </label>
                                            </div>
                                            <ul class="pl-6 mt-2 space-y-2 hidden">
                                                <li>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                        计算机、通信和其他电子设备制造业
                                                    </label>
                                                </li>
                                                <li>
                                                    <label class="flex items-center">
                                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                        医药制造业
                                                    </label>
                                                </li>
                                            </ul>
                                        </li>
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    信息传输、软件和信息技术服务业
                                                </label>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                </svg>
                                                <label class="ml-2 flex items-center">
                                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                    科学研究和技术服务业
                                                </label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                            <input type="text" placeholder="输入对象编码或描述关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效状态</label>
                            <div class="flex items-center space-x-4 mt-2">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">全部</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已启用</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="inactive" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已停用</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 对象分类列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                对象分类列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openObjectModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增分类
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对象编码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主体类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规模级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属行业</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">ENT-SM-MFG-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">小型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">制造业-计算机、通信和其他电子设备制造业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已启用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-20 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="disableObject('obj1')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                            <button onclick="deleteObject('obj1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">UNI-LG-EDU-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">高校</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">大型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">教育</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已启用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15 09:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="disableObject('obj2')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                            <button onclick="deleteObject('obj2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">INS-MD-RES-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科研院所</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">中型</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科学研究和技术服务业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已停用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-10 16:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewObject('obj3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editObject('obj3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="enableObject('obj3')" class="text-green-600 hover:text-green-900">启用</button>
                                            <button onclick="deleteObject('obj3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        分类统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已启用分类总数</div>
                                <div class="text-2xl font-bold text-blue-600">38</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近一月新增</div>
                                <div class="text-2xl font-bold text-green-600">12</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已停用分类</div>
                                <div class="text-2xl font-bold text-yellow-600">4</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量导入
                            </h2>
                            <button onclick="toggleImportPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="importPanelToggle">展开</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500">支持Excel批量导入对象分类信息</p>
                    </div>
                    <div id="importPanel" class="hidden">
                        <div class="p-6 space-y-4">
                            <div class="flex items-center justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                    </svg>
                                    下载导入模板
                                </a>
                                <span class="text-xs text-gray-500">支持.xlsx格式</span>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                            </div>
                            <div id="uploadProgress" class="hidden">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">上传进度</span>
                                    <span class="text-sm font-medium text-gray-700">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                        <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                            <li>第3行：对象编码已存在</li>
                                            <li>第5行：主体类型为必填项</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类编辑弹窗 -->
    <div id="objectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">对象分类编辑</h3>
                    <button onclick="closeObjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">对象编码 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入对象编码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-xs text-gray-500">建议使用主体类型-规模-行业的缩写组合</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">主体类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择主体类型</option>
                                    <option value="enterprise">企业</option>
                                    <option value="university">高校</option>
                                    <option value="institute">科研院所</option>
                                    <option value="individual">个人</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">规模级别 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择规模级别</option>
                                    <option value="micro">微型（10人以下）</option>
                                    <option value="small">小型（10-99人）</option>
                                    <option value="medium">中型（100-499人）</option>
                                    <option value="large">大型（500人以上）</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属行业 <span class="text-red-500">*</span></label>
                                <div class="relative">
                                    <button id="modalIndustryButton" onclick="toggleModalIndustrySelect()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                                        <span id="modalSelectedIndustry">请选择所属行业</span>
                                        <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="modalIndustrySelect" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                                        <div class="space-y-2">
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind1" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind1" class="ml-2 text-sm text-gray-700">制造业-计算机、通信和其他电子设备制造业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind2" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind2" class="ml-2 text-sm text-gray-700">制造业-医药制造业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind3" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind3" class="ml-2 text-sm text-gray-700">信息传输、软件和信息技术服务业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind4" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind4" class="ml-2 text-sm text-gray-700">科学研究和技术服务业</label>
                                            </div>
                                            <div class="flex items-center">
                                                <input type="radio" name="industry" id="ind5" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                                <label for="ind5" class="ml-2 text-sm text-gray-700">教育</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 分类描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分类描述</label>
                            <textarea rows="3" placeholder="请输入分类描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <!-- 生效状态 -->
                        <div>
                            <label class="flex items-center">
                                <span class="mr-3 text-sm font-medium text-gray-700">生效状态:</span>
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox" id="statusToggle" class="sr-only">
                                    <div class="block h-6 bg-gray-300 rounded-full w-12"></div>
                                    <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                                </div>
                                <span id="statusText" class="text-sm text-gray-700">已停用</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeObjectModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 行业树形选择器
        function toggleIndustryTree() {
            const tree = document.getElementById('industryTree');
            tree.classList.toggle('hidden');
        }
        
        // 弹窗中的行业选择器
        function toggleModalIndustrySelect() {
            const select = document.getElementById('modalIndustrySelect');
            select.classList.toggle('hidden');
        }
        
        // 对象分类编辑弹窗
        function openObjectModal() {
            document.getElementById('objectModal').classList.remove('hidden');
        }
        
        function closeObjectModal() {
            document.getElementById('objectModal').classList.add('hidden');
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 批量导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('importPanel');
            const toggle = document.getElementById('importPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 状态开关
        document.getElementById('statusToggle').addEventListener('change', function() {
            const statusText = document.getElementById('statusText');
            if (this.checked) {
                statusText.textContent = '已启用';
                document.querySelector('.dot').classList.add('transform', 'translate-x-6');
            } else {
                statusText.textContent = '已停用';
                document.querySelector('.dot').classList.remove('transform', 'translate-x-6');
            }
        });
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 其他功能函数
        function viewObject(objectId) {
            console.log('查看对象:', objectId);
        }
        
        function editObject(objectId) {
            openObjectModal();
            console.log('编辑对象:', objectId);
        }
        
        function disableObject(objectId) {
            if (confirm('确定要停用这个对象分类吗？')) {
                console.log('停用对象:', objectId);
            }
        }
        
        function enableObject(objectId) {
            if (confirm('确定要启用这个对象分类吗？')) {
                console.log('启用对象:', objectId);
            }
        }
        
        function deleteObject(objectId) {
            if (confirm('确定要删除这个对象分类吗？此操作不可恢复！')) {
                console.log('删除对象:', objectId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('objectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeObjectModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 行业树形选择器
            const industryButton = document.getElementById('industryButton');
            const industryTree = document.getElementById('industryTree');
            if (!industryButton.contains(e.target) && !industryTree.contains(e.target) && !industryTree.classList.contains('hidden')) {
                industryTree.classList.add('hidden');
            }
            
            // 弹窗中的行业选择器
            const modalIndustryButton = document.getElementById('modalIndustryButton');
            const modalIndustrySelect = document.getElementById('modalIndustrySelect');
            if (modalIndustryButton && modalIndustrySelect && !modalIndustryButton.contains(e.target) && !modalIndustrySelect.contains(e.target) && !modalIndustrySelect.classList.contains('hidden')) {
                modalIndustrySelect.classList.add('hidden');
            }
            
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 自定义样式
        document.addEventListener('DOMContentLoaded', function() {
            // 状态开关样式
            const style = document.createElement('style');
            style.textContent = `
                #statusToggle:checked + .block {
                    background-color: #2563eb;
                }
                #statusToggle:checked ~ .dot {
                    transform: translateX(100%);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>]`
    *   `[<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策级别管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">政策级别管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-4/5 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-3 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-1">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="policyName" class="block text-sm font-medium text-gray-700 mb-0.5">政策名称</label>
                            <input type="text" id="policyName" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入政策名称">
                        </div>
                        <div>
                            <label for="documentNumber" class="block text-sm font-medium text-gray-700 mb-0.5">发文文号</label>
                            <input type="text" id="documentNumber" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入发文文号">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-0.5">政策级别</label>
                            <div class="flex flex-wrap gap-1">
                                <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">国家级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">省级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">市级</button>
                                <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleLevel(this)">区级</button>
                            </div>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-0.5">生效状态</label>
                            <select id="status" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="effective">生效</option>
                                <option value="ineffective">失效</option>
                            </select>
                        </div>
                        <div>
                            <label for="effectiveDate" class="block text-sm font-medium text-gray-700 mb-0.5">生效日期</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-0.5">发文部门</label>
                            <select id="department" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="state-council">国务院</option>
                                <option value="ministry">部委</option>
                                <option value="provincial">省政府</option>
                                <option value="municipal">市政府</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-1.5 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 政策级别列表区 -->
                    <div class="w-full">
                        <!-- 级别列表区 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800">政策级别列表</h2>
                                <button onclick="openEditModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                    </svg>
                                    批量变更级别
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发文文号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前级别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引用次数</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">关于促进科技创新发展的若干意见</div>
                                            <div class="text-sm text-gray-500">科技创新政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国发〔2024〕15号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">156</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">省级产业发展扶持政策实施细则</div>
                                            <div class="text-sm text-gray-500">产业发展政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙政发〔2024〕8号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">89</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 14:20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">市级中小企业发展专项资金管理办法</div>
                                            <div class="text-sm text-gray-500">资金管理政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">杭政办〔2024〕12号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">失效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 09:15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">区级人才引进激励政策</div>
                                            <div class="text-sm text-gray-500">人才政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">西湖政发〔2024〕3号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">区级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">67</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05 16:45</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('4')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">数字经济发展三年行动计划</div>
                                            <div class="text-sm text-gray-500">数字经济政策</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">未分级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生效</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-03 11:20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openEditModal('5')" class="text-indigo-600 hover:text-indigo-900">变更级别</button>
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="w-full lg:w-1/5 lg:min-w-[280px] space-y-4">
                <!-- 统计与预警区 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">统计与预警</h3>
                    <div class="space-y-6">
                        <!-- 政策级别分布 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">政策级别分布</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">国家级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">1,245</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">省级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 70%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">2,156</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">市级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">3,892</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">区级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 45%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">1,567</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 近30天级别变更趋势 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">近30天级别变更趋势</h4>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-2xl font-bold text-blue-600">156</span>
                                <span class="text-xs text-gray-500">次变更</span>
                            </div>
                            <div class="h-16 flex items-end space-x-1">
                                <div class="w-3 bg-blue-200 rounded-t" style="height: 30%"></div>
                                <div class="w-3 bg-blue-300 rounded-t" style="height: 50%"></div>
                                <div class="w-3 bg-blue-400 rounded-t" style="height: 40%"></div>
                                <div class="w-3 bg-blue-500 rounded-t" style="height: 70%"></div>
                                <div class="w-3 bg-blue-600 rounded-t" style="height: 100%"></div>
                                <div class="w-3 bg-blue-500 rounded-t" style="height: 80%"></div>
                                <div class="w-3 bg-blue-400 rounded-t" style="height: 60%"></div>
                            </div>
                        </div>

                        <!-- 预警信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">预警信息</h4>
                            <div class="space-y-3">
                                <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                        </svg>
                                        <div>
                                            <p class="text-xs font-medium text-red-800">23个政策缺失级别标签</p>
                                            <a href="#" class="text-xs text-red-700 hover:text-red-900">立即处理 →</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <div>
                                            <p class="text-xs font-medium text-yellow-800">8个政策即将到期</p>
                                            <a href="#" class="text-xs text-yellow-700 hover:text-yellow-900">查看详情 →</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量维护区（可折叠） -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <button onclick="toggleBatchMaintenance()" class="w-full flex items-center justify-between text-left">
                            <h3 class="text-lg font-medium text-gray-800">批量维护</h3>
                            <svg id="batch-toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div id="batch-content" class="p-4 space-y-4">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                下载标准模板
                            </a>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div class="hidden" id="upload-progress">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">上传进度</span>
                                <span class="text-gray-600">75%</span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 级别编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">变更政策级别</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择新级别</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="level" value="national" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">国家级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="provincial" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">省级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="municipal" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">市级</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="level" value="district" class="text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">区级</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label for="changeReason" class="block text-sm font-medium text-gray-700 mb-1">变更说明</label>
                        <textarea id="changeReason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入变更原因和说明"></textarea>
                    </div>
                    <div>
                        <label for="effectiveDate" class="block text-sm font-medium text-gray-700 mb-1">生效时间</label>
                        <input type="datetime-local" id="effectiveDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存变更
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function toggleLevel(button) {
            if (button.classList.contains('border-blue-500')) {
                button.classList.remove('border-blue-500', 'text-blue-500', 'bg-blue-50');
                button.classList.add('border-gray-300', 'text-gray-700');
            } else {
                button.classList.remove('border-gray-300', 'text-gray-700');
                button.classList.add('border-blue-500', 'text-blue-500', 'bg-blue-50');
            }
        }

        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 批量维护折叠功能
        function toggleBatchMaintenance() {
            const content = document.getElementById('batch-content');
            const icon = document.getElementById('batch-toggle-icon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            }
        }

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('upload-progress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('upload-progress').classList.add('hidden');
                            alert('文件上传成功！');
                        }, 500);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>]`

**3. 技术与输出规范 (Technical & Output Specifications):**
*   **最终产物:** **一个独立的、完整的、无外部依赖的HTML文件。**
*   **文件命名:** 基于下方提供的 `[功能名]`，将其翻译为有意义的英文并使用连字符（kebab-case）命名，例如“地址匹配查询”应命名为 `address-matching-query.html`。
*   **样式:** **绝对禁止**使用内联 `<style>` 标签或外部CSS文件。所有样式**必须**通过 Tailwind CSS 类名实现。
*   **图片与图标:**
    *   **图标:** **必须**使用 Heroicons (`https://heroicons.com/`) 的SVG代码直接嵌入HTML，并使用Tailwind（如 `w-5 h-5 text-blue-600`）控制样式。
    *   **图片占位符:** **禁止使用 `source.unsplash.com` 或任何外部图片链接，因为这会导致本地文件无法显示图片。** 请使用自包含的SVG占位符。例如，一个灰色的100x100占位符：
        `<svg class="w-24 h-24 bg-gray-200 text-gray-500 rounded-md" xmlns="http://www.w3.org/2000/svg" aria-hidden="true"><text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle">占位图</text></svg>`
*   **语言:** 所有输出内容，包括页面上的文本、标签、按钮、表头以及代码中的注释，**必须全部使用简体中文**。
*   **模拟数据 (Mock Data):** 所有占位数据（如企业名称、项目名称、人名等）**必须具有“宁波市”的地域特色**。请遵循以下格式：
    *   **企业名称:** `宁波市XX科技有限公司`、`宁波XX集团`、`宁波XX智能制造有限公司` 等。
    *   **项目名称:** `宁波市智慧港口XX项目`、`宁波市新材料产业园XX计划` 等。
    *   **人名:** 可使用常见中文名（如“张伟”、“王芳”），若涉及单位，则应为宁波的企业。

---

### **--- 本次开发任务 ---**

**一、功能名:**
`[企业信息展示]`

**二、功能设计需求:**
`[**一、功能说明**

企业信息展示功能面向创新主体科研码的企业视图，汇聚工商注册、股东结构、对外投资、参保情况、股权穿透、行业与产业归属、集团关系等多源数据，通过可视化组件与层级下钻页面，向用户直观呈现企业经营与股权全貌，并支持关键要素的深度分析与关联跳转，为风险研判、投融资决策、产业链画像等业务场景提供权威基础信息和洞察支撑。

**二、界面结构与要素说明**

企业概况区位于页面顶部，集中展示企业名称、统一社会信用代码、注册时间、成立日期、注册资本、企业类型、注册地址及营业执照副本缩略图，辅以企业状态与行业分类标签，便于用户快速获取核心工商信息与经营资质。

股东信息与出资占比区采用多维柱状图及列表联动方式呈现主要股东及持股比例，每个股东条目包含名称、股东类型和出资额，支持点击条目跳转至股东详情下钻页，进一步查看该股东的投资关系及关联企业网络。

对外投资总览区以卡片式统计和可展开列表结合的形式展示企业对外投资总额、投资企业数量及地区分布，用户可点击“查看详情”进入对外投资列表页，获取每笔投资的企业名称、投资金额、出资比例、行业领域等明细信息并支持进一步跳转。

参保信息区通过折线图叠加数据表格展示企业历年员工参保人数、参保类型和社保缴费趋势，列表列出当前有效参保人数、五险缴纳基数等关键指标，并提供“参保详情”链接以查询员工参保记录。

股权穿透可视化区以互动式股权穿透图呈现企业股权链条，显示直接和间接持股主体、出资额与持股比例并支持节点点击展开二级股权关系，用户可切换视角查看自然人或法人最终受益结构并在独立页面进行全屏分析。

行业产业与集团关系区利用雷达图和关系网络图展示企业在行业分类、产业链位置及集团母子公司层级，网络图节点标识集团成员企业，边表示控股关系及比例，实现集团结构的全景展现。

**三、业务流程**

系统接收用户选择的企业科研码，调用工商、股东、投资、社保、股权链等多源接口聚合数据并完成字段标准化与画像计算。

后台服务根据企业统一社会信用代码匹配股东及对外投资信息，生成股权结构数据并同步图形渲染服务输出可视化组件。

数据组装完成后，前端按预设布局依次渲染企业概况、股东信息、投资、参保、股权穿透、行业产业及集团关系各模块，实现首页展示。

当用户触发下钻操作（如点击股东条目或对外投资卡片），系统按所选实体加载对应详情页数据，提供二级或三级深度信息并记录用户访问日志。

用户在股权穿透图或集团关系图中展开节点时，前端通过异步请求获取新增股权链数据并动态更新可视化层级，后台同步缓存已查询结果。

所有用户浏览、下钻、导出等行为写入操作日志并供后续统计分析，为企业画像准确度与使用体验持续优化提供数据依据。

**四、操作流程**

用户在科研码门户选择目标企业科研码并进入信息展示页面，首先浏览顶部企业概况获取工商注册核心数据与经营状态。

用户向下滚动至股东信息区，通过柱状图观察持股比例，点击某股东条目自动跳转至股东详情页，查看该股东的基本信息及其投资网络后点击浏览器返回回到主页面。

用户在对外投资区点击“查看详情”进入投资列表，筛选或排序投资金额并跳转至指定投资企业的科研码页面进行交叉分析。

用户切换至参保信息区，通过折线图查看近三年参保人数变化趋势，如需下载数据点击“导出”按钮获取 CSV 文件。

用户在股权穿透区点击“全屏查看”打开独立页面，逐层展开股权节点直至最终受益人并对关键节点右键“复制链路”以备报告引用。

用户最后在行业产业与集团关系区查看集团网络图，对集团成员企业进行悬停查看比例或双击节点跳转其科研码页面，完成集团层级多点分析后关闭页面结束浏览。

研发人员展示

展示企业内部研发人员和企业技术顾问情况、参与项目情况、知识产权情况、工作经历、教育经历、职称证书、政策补助、获奖信息、合作工作轨迹等内容；

研发人员数或企业技术顾问数，支持下钻页面展示企业研发人员清册；

学历，支持下钻展示该研发人员的教育经历清册；

职称，支持下钻页面展示该研发人员的职称晋升详情；

参与项目数，支持下钻页面展示该研发人员参与项目；

研发成果数，支持下钻页面展示该研发人员负责或参与的专利、论文、奖励等明细清册；

参与项目数标签，支持下钻页面展示该企业历年参与项目清册；

知识产权数量标签，支持下钻页面展示该企业历次主导或参与的研发成果清册及各类知识产权数量变化趋势；

政策补助详情，支持下钻页面展示该企业历史获得政策补助清册；

企业获奖数，支持下钻页面展示该企业历史获奖清册及各类奖项分级占比饼图；

**一、功能说明**

研发人员展示功能面向企业、高校与科研机构的管理与洞察场景，通过统一界面将内部研发人员与外部技术顾问的数量、学历层级、职称分布、项目参与度、研发成果、知识产权、政策补助与获奖情况等多维信息进行集中呈现，并支持从宏观指标到明细清册的逐级下钻，以帮助管理层快速识别人才结构、创新贡献与激励成效，亦为业务人员提供便捷的数据查询、对比和导出通道，从而提升研发资源配置效率与决策准确性。

**二、界面结构与要素说明**

指标概览区位于页面顶部，以信息卡形式展示研发人员总数、技术顾问总数、硕博士及以上学历人数、各级职称人数、累计参与项目数、已取得知识产权数量、累计政策补助次数与企业获奖总数等关键指标，每张信息卡内嵌同比环比箭头与点击下钻联动，用户可通过点击任一指标进入对应明细或趋势分析视图。

条件筛选区紧跟指标区展开，包含主体名称智能搜索框、人员类别多选、学历下拉、职称下拉、项目年份范围选择与成果类型多选等筛选元素，并提供“查询”“重置”按钮，可组合条件精准定位特定研发人员或顾问群体。

人员列表区以可分页数据表格展示检索结果，核心列涵盖姓名、人员类别、最高学历、当前职称、在职状态、参与项目数、成果数、知识产权数、最近政策补助、最近获奖信息及操作列，操作列内置“查看画像”“下钻项目”“下钻成果”“下钻补助”“下钻奖项”快捷入口，支持单行展开查看教育经历与工作经历摘要。

人员画像侧栏在点击“查看画像”后自右侧滑入，顶部展示个人基础信息与高清头像，中部通过标签页分别呈现教育经历、工作经历、职称证书、参与项目、知识产权、政策补助与获奖记录，底部配有时间轴视图展示合作工作轨迹，侧栏支持一键导出个人报告与发送邮件分享。

统计分析区位于页面下方，默认以柱状图与堆积面积图组合展示学历分布、职称晋升趋势、年度参与项目数量变化与知识产权累积趋势，同时提供切换按钮在散点图、雷达图及饼图之间切换以观察奖项级别占比与补助类别分布。

**三、业务流程**

用户进入研发人员展示模块，系统根据用户权限拉取可见主体范围并初始化指标概览区所需的聚合统计结果。

当用户修改筛选条件并点击查询时，系统将筛选参数发送至数据服务层，按条件过滤研发人员主数据、项目信息与成果数据后返回列表结果与更新后的指标概览。

用户点击某指标卡片或列表内下钻入口时，系统判断所选指标对应的数据域，并触发明细数据拉取与视图跳转或侧栏加载，同时记录用户行为用于后续推荐与审计。

在人员画像侧栏中切换标签页或展开时间轴时，系统按需调用对应数据集，例如教育经历表、项目参与表、知识产权表等，组装成统一输出对象并渲染到前端组件。

若用户执行导出或分享操作，系统根据当前可见数据生成符合模板规范的PDF或Excel文件，或生成加密分享链接，并在日志中心记录导出内容、时间与受众信息。

当用户关闭侧栏或返回主页面时，系统释放本次会话的缓存资源，同时异步更新访问统计与热度排行，为后续推荐与性能优化提供数据支撑。

**四、操作流程**

进入研发人员展示页面后先浏览顶部指标概览，以快速了解人才规模与创新贡献概况。

在条件筛选区设置主体名称、人员类别及时间范围等过滤条件后点击查询，获取符合要求的人员列表与更新后的指标。

在人员列表中点击某位研发人员的“查看画像”以打开侧栏，切换教育经历、职称证书等标签检查详细履历或合作轨迹。

返回列表后点击“下钻项目”“下钻成果”等操作按钮，查看该人员参与的项目清册、专利论文清册或奖项分级占比饼图。

若需整体分析趋势，可下拉至统计分析区，通过切换不同图表类型观察学历分布、职称晋升与知识产权增长等维度的年度变化。

在任意视图中如需线下存档或共享，点击“导出”选择PDF或Excel，或点击“分享”复制外链，并根据提示完成发送或下载。]`

**三、核心要素拆解 (Key Elements Breakdown):**
`[{核心要素}]`
（例如：1. 一个包含“名称”、“状态”、“日期范围”的筛选卡片。 2. 一个展示设备列表的表格，包含“设备图片”、“设备名称”、“规格”、“状态”和“操作”列。 3. 一个用于“新增/编辑设备”的模态框，包含多个输入字段和文件上传功能。 4. 页面顶部有“批量导入”和“新增设备”按钮。）

---

### **最终指令 (Final Command)**

**在你开始编码之前，请在脑海中快速回顾并确认以下清单：**
1.  我已经完全理解了“设计心法”的每一条原则。
2.  我已经仔细研究了两个参考范例，并准备100%复刻其风格。
3.  我清楚所有技术规范，特别是**图片、图标和模拟数据**的使用规则。
4.  我已阅读并理解了本次任务的核心要素，确保不会遗漏任何功能。

确认完毕。现在，请化身为那位追求极致的工程师与设计师，基于以上所有信息，为本次指定的开发任务生成对应的HTML原型页面代码。

**直接开始设计并输出最终的HTML文件代码。不要在代码前添加任何解释性文字或总结。你的输出必须直接以 `<!DOCTYPE html>` 开始。**
---,在public文件夹下保存html页面

---

_**Assistant**_



---

_**User**_

### **通用原型开发模板 (可以直接复制使用)**
---

### **【提示词模板正文】**

你是一位资深的全栈工程师，精通产品规划和UI/UX设计，目前正在为一个已有的复杂Web应用项目贡献代码。你的核心任务是根据详细的需求文档，并严格遵循项目既定的设计规范，输出一个功能完整的、高质量的HTML原型页面。

### **核心指令与项目规范 (每次任务均需严格遵守)**

**1. 设计系统分析 (Design System Analysis):**
*   **布局:** 统一采用卡片式布局，结构为“页面大标题 + 内容区域”。内容区域常使用 `h-[calc(100vh-120px)]` 来计算高度以实现视口内滚动。
*   **颜色方案:** 主色调为蓝色。页面背景使用从 `from-gray-50` 到 `to-blue-50` 的微妙渐变。
*   **组件库:** 广泛使用Tailwind CSS构建通用UI组件，如卡片（Card）、按钮（Button）、输入框（Input）、选项卡（Tabs）、徽章（Badge）、下拉菜单（Dropdown）等。
*   **视觉元素:** 卡片具有阴影效果 (`shadow-md`)，按钮常带有图标，状态通常用不同颜色的徽章表示。
*   **交互模式:** 普遍使用选项卡（Tabs）切换内容视图，以及“列表-详情”的交互模式。
*   **响应式:** 默认使用Flexbox和Grid进行响应式布局。

**2. 代码与风格参考 (Code & Style References):**
*   请参考以下已开发页面的代码和实现，以确保新页面在视觉和结构上的一致性：
    *   `makemoney-web/src/app/template/ai-budget-review/page.tsx`
    *   `makemoney-web/src/app/template/assets/applications/page.tsx`
    *   `makemoney-web/src/app/template/customer-profile/page.tsx`
    *   `makemoney-web/src/app/template/document-management/page.tsx`
    *   `makemoney-web/src/app/template/zfsq/page.tsx`

**3. 技术与输出规范 (Technical & Output Specifications):**
*   **最终产物:** 一个独立的、完整的HTML文件。
*   **文件命名:** 基于下方提供的 `[功能名]`，将其翻译为有意义的英文并使用连字符（kebab-case）命名，例如“地址匹配查询”应命名为 `address-matching-query.html`。
*   **存放路径:** 所有HTML文件都将被放置在 `public` 目录下，这意味着所有资源路径（如图片）都应该是相对或绝对的，以便直接在浏览器中打开。
*   **样式:** **必须**完全使用 **Tailwind CSS** 类名。禁止使用内联 `<style>` 标签或外部CSS文件。
*   **图片:** 所有图片（如图标占位符、地图预览）均使用 **Unsplash API** (`https://source.unsplash.com/random/宽x高?主题`)。
*   **图标:** 请使用 Heroicons 或类似的 SVG 图标库，并直接将 SVG 代码嵌入 HTML 中，同时用 Tailwind CSS 控制其样式。
*   **语言:** 所有输出内容，包括页面上的文本、标签、按钮、表头以及代码中的注释，**必须全部使用简体中文**。
*   **质量标准:** 请以顶级UX设计师的眼光和审美标准进行创作，确保页面布局合理、信息层次清晰、交互直观，创造出令人满意的、专业级别的原型。

---

### **--- 本次开发任务 ---**

请替换下面的占位符内容以定义本次的具体任务：

**一、功能名:**
`[企业信息展示]`

**二、功能设计需求:**
`[**一、功能说明**

企业信息展示功能面向创新主体科研码的企业视图，汇聚工商注册、股东结构、对外投资、参保情况、股权穿透、行业与产业归属、集团关系等多源数据，通过可视化组件与层级下钻页面，向用户直观呈现企业经营与股权全貌，并支持关键要素的深度分析与关联跳转，为风险研判、投融资决策、产业链画像等业务场景提供权威基础信息和洞察支撑。

**二、界面结构与要素说明**

企业概况区位于页面顶部，集中展示企业名称、统一社会信用代码、注册时间、成立日期、注册资本、企业类型、注册地址及营业执照副本缩略图，辅以企业状态与行业分类标签，便于用户快速获取核心工商信息与经营资质。

股东信息与出资占比区采用多维柱状图及列表联动方式呈现主要股东及持股比例，每个股东条目包含名称、股东类型和出资额，支持点击条目跳转至股东详情下钻页，进一步查看该股东的投资关系及关联企业网络。

对外投资总览区以卡片式统计和可展开列表结合的形式展示企业对外投资总额、投资企业数量及地区分布，用户可点击“查看详情”进入对外投资列表页，获取每笔投资的企业名称、投资金额、出资比例、行业领域等明细信息并支持进一步跳转。

参保信息区通过折线图叠加数据表格展示企业历年员工参保人数、参保类型和社保缴费趋势，列表列出当前有效参保人数、五险缴纳基数等关键指标，并提供“参保详情”链接以查询员工参保记录。

股权穿透可视化区以互动式股权穿透图呈现企业股权链条，显示直接和间接持股主体、出资额与持股比例并支持节点点击展开二级股权关系，用户可切换视角查看自然人或法人最终受益结构并在独立页面进行全屏分析。

行业产业与集团关系区利用雷达图和关系网络图展示企业在行业分类、产业链位置及集团母子公司层级，网络图节点标识集团成员企业，边表示控股关系及比例，实现集团结构的全景展现。

**三、业务流程**

系统接收用户选择的企业科研码，调用工商、股东、投资、社保、股权链等多源接口聚合数据并完成字段标准化与画像计算。

后台服务根据企业统一社会信用代码匹配股东及对外投资信息，生成股权结构数据并同步图形渲染服务输出可视化组件。

数据组装完成后，前端按预设布局依次渲染企业概况、股东信息、投资、参保、股权穿透、行业产业及集团关系各模块，实现首页展示。

当用户触发下钻操作（如点击股东条目或对外投资卡片），系统按所选实体加载对应详情页数据，提供二级或三级深度信息并记录用户访问日志。

用户在股权穿透图或集团关系图中展开节点时，前端通过异步请求获取新增股权链数据并动态更新可视化层级，后台同步缓存已查询结果。

所有用户浏览、下钻、导出等行为写入操作日志并供后续统计分析，为企业画像准确度与使用体验持续优化提供数据依据。

**四、操作流程**

用户在科研码门户选择目标企业科研码并进入信息展示页面，首先浏览顶部企业概况获取工商注册核心数据与经营状态。

用户向下滚动至股东信息区，通过柱状图观察持股比例，点击某股东条目自动跳转至股东详情页，查看该股东的基本信息及其投资网络后点击浏览器返回回到主页面。

用户在对外投资区点击“查看详情”进入投资列表，筛选或排序投资金额并跳转至指定投资企业的科研码页面进行交叉分析。

用户切换至参保信息区，通过折线图查看近三年参保人数变化趋势，如需下载数据点击“导出”按钮获取 CSV 文件。

用户在股权穿透区点击“全屏查看”打开独立页面，逐层展开股权节点直至最终受益人并对关键节点右键“复制链路”以备报告引用。

用户最后在行业产业与集团关系区查看集团网络图，对集团成员企业进行悬停查看比例或双击节点跳转其科研码页面，完成集团层级多点分析后关闭页面结束浏览。

研发人员展示

展示企业内部研发人员和企业技术顾问情况、参与项目情况、知识产权情况、工作经历、教育经历、职称证书、政策补助、获奖信息、合作工作轨迹等内容；

研发人员数或企业技术顾问数，支持下钻页面展示企业研发人员清册；

学历，支持下钻展示该研发人员的教育经历清册；

职称，支持下钻页面展示该研发人员的职称晋升详情；

参与项目数，支持下钻页面展示该研发人员参与项目；

研发成果数，支持下钻页面展示该研发人员负责或参与的专利、论文、奖励等明细清册；

参与项目数标签，支持下钻页面展示该企业历年参与项目清册；

知识产权数量标签，支持下钻页面展示该企业历次主导或参与的研发成果清册及各类知识产权数量变化趋势；

政策补助详情，支持下钻页面展示该企业历史获得政策补助清册；

企业获奖数，支持下钻页面展示该企业历史获奖清册及各类奖项分级占比饼图；

**一、功能说明**

研发人员展示功能面向企业、高校与科研机构的管理与洞察场景，通过统一界面将内部研发人员与外部技术顾问的数量、学历层级、职称分布、项目参与度、研发成果、知识产权、政策补助与获奖情况等多维信息进行集中呈现，并支持从宏观指标到明细清册的逐级下钻，以帮助管理层快速识别人才结构、创新贡献与激励成效，亦为业务人员提供便捷的数据查询、对比和导出通道，从而提升研发资源配置效率与决策准确性。

**二、界面结构与要素说明**

指标概览区位于页面顶部，以信息卡形式展示研发人员总数、技术顾问总数、硕博士及以上学历人数、各级职称人数、累计参与项目数、已取得知识产权数量、累计政策补助次数与企业获奖总数等关键指标，每张信息卡内嵌同比环比箭头与点击下钻联动，用户可通过点击任一指标进入对应明细或趋势分析视图。

条件筛选区紧跟指标区展开，包含主体名称智能搜索框、人员类别多选、学历下拉、职称下拉、项目年份范围选择与成果类型多选等筛选元素，并提供“查询”“重置”按钮，可组合条件精准定位特定研发人员或顾问群体。

人员列表区以可分页数据表格展示检索结果，核心列涵盖姓名、人员类别、最高学历、当前职称、在职状态、参与项目数、成果数、知识产权数、最近政策补助、最近获奖信息及操作列，操作列内置“查看画像”“下钻项目”“下钻成果”“下钻补助”“下钻奖项”快捷入口，支持单行展开查看教育经历与工作经历摘要。

人员画像侧栏在点击“查看画像”后自右侧滑入，顶部展示个人基础信息与高清头像，中部通过标签页分别呈现教育经历、工作经历、职称证书、参与项目、知识产权、政策补助与获奖记录，底部配有时间轴视图展示合作工作轨迹，侧栏支持一键导出个人报告与发送邮件分享。

统计分析区位于页面下方，默认以柱状图与堆积面积图组合展示学历分布、职称晋升趋势、年度参与项目数量变化与知识产权累积趋势，同时提供切换按钮在散点图、雷达图及饼图之间切换以观察奖项级别占比与补助类别分布。

**三、业务流程**

用户进入研发人员展示模块，系统根据用户权限拉取可见主体范围并初始化指标概览区所需的聚合统计结果。

当用户修改筛选条件并点击查询时，系统将筛选参数发送至数据服务层，按条件过滤研发人员主数据、项目信息与成果数据后返回列表结果与更新后的指标概览。

用户点击某指标卡片或列表内下钻入口时，系统判断所选指标对应的数据域，并触发明细数据拉取与视图跳转或侧栏加载，同时记录用户行为用于后续推荐与审计。

在人员画像侧栏中切换标签页或展开时间轴时，系统按需调用对应数据集，例如教育经历表、项目参与表、知识产权表等，组装成统一输出对象并渲染到前端组件。

若用户执行导出或分享操作，系统根据当前可见数据生成符合模板规范的PDF或Excel文件，或生成加密分享链接，并在日志中心记录导出内容、时间与受众信息。

当用户关闭侧栏或返回主页面时，系统释放本次会话的缓存资源，同时异步更新访问统计与热度排行，为后续推荐与性能优化提供数据支撑。

**四、操作流程**

进入研发人员展示页面后先浏览顶部指标概览，以快速了解人才规模与创新贡献概况。

在条件筛选区设置主体名称、人员类别及时间范围等过滤条件后点击查询，获取符合要求的人员列表与更新后的指标。

在人员列表中点击某位研发人员的“查看画像”以打开侧栏，切换教育经历、职称证书等标签检查详细履历或合作轨迹。

返回列表后点击“下钻项目”“下钻成果”等操作按钮，查看该人员参与的项目清册、专利论文清册或奖项分级占比饼图。

若需整体分析趋势，可下拉至统计分析区，通过切换不同图表类型观察学历分布、职称晋升与知识产权增长等维度的年度变化。

在任意视图中如需线下存档或共享，点击“导出”选择PDF或Excel，或点击“分享”复制外链，并根据提示完成发送或下载。]`

---

### **最终指令**

请基于以上所有信息，特别是严格遵循项目规范和参考文件风格，为本次指定的开发任务生成对应的HTML原型页面代码。

**请直接开始设计并输出最终的HTML文件代码。不要在代码前添加任何解释性文字，直接从 `<!DOCTYPE html>` 开始。**

在public文件夹下保存html页面

---

_**Assistant**_

我来为您创建企业信息展示页面的HTML原型文件。首先让我分析这个任务的需求，然后创建一个符合项目设计规范的页面。

---

Error calling tool 'edit_file'.

---



---

_**User**_

Request ID: 002070f3-5ab6-43d9-9231-7ccd118d263f
{"error":"ERROR_CUSTOM_MESSAGE","details":{"title":"Model provider Anthropic ended response early.","detail":"Model provider Anthropic ended response early.","isRetryable":false,"additionalInfo":{},"buttons":[]},"isExpected":true}
ConnectError: [invalid_argument] Error
    at C9a.$endAiConnectTransportReportError (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4757:223764)
    at uir.S (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:17741)
    at uir.Q (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:17519)
    at uir.M (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:16607)
    at uir.L (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:15708)
    at Bwt.value (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:492:14500)
    at ve.B (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:48:2398)
    at ve.fire (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:48:2617)
    at Git.fire (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:4744:10379)
    at u.onmessage (vscode-file://vscode-app/c:/Users/<USER>/AppData/Local/Programs/cursor/resources/app/out/vs/workbench/workbench.desktop.main.js:6968:12271) 请继续

---

_**Assistant**_



---

