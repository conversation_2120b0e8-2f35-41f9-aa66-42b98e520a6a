<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技特派员详情展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技特派员详情展示</h1>
            <p class="text-gray-600">全面展示个人、法人和团队三类科技特派员的详细信息，助力科技服务资源精准匹配</p>
        </div>

        <!-- 特派员类型选择 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                特派员类型选择
            </h2>
            <div class="flex space-x-4">
                <button id="personalTab" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    个人特派员
                </button>
                <button id="legalTab" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    法人特派员
                </button>
                <button id="teamTab" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    团队特派员
                </button>
            </div>
        </div>

        <!-- 个人特派员详情 -->
        <div id="personalDetail" class="space-y-6">
            <!-- 基本信息卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    基本信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">姓名</label>
                        <p class="text-sm text-gray-900">张伟</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">性别</label>
                        <p class="text-sm text-gray-900">男</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">出生年月</label>
                        <p class="text-sm text-gray-900">1985-05-15</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">政治面貌</label>
                        <p class="text-sm text-gray-900">中共党员</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">学历</label>
                        <p class="text-sm text-gray-900">博士</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">职务</label>
                        <p class="text-sm text-gray-900">高级工程师</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">联系方式</label>
                        <p class="text-sm text-gray-900">13805748888</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">身体状况</label>
                        <p class="text-sm text-gray-900">健康</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">工作单位</label>
                        <p class="text-sm text-gray-900">宁波市智能制造研究院</p>
                    </div>
                </div>
            </div>

            <!-- 专业技术特长 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    专业技术特长
                </h2>
                <div class="space-y-2">
                    <p class="text-sm text-gray-700">1. 智能制造系统设计与优化</p>
                    <p class="text-sm text-gray-700">2. 工业机器人应用与开发</p>
                    <p class="text-sm text-gray-700">3. 数字化工厂规划与实施</p>
                </div>
            </div>

            <!-- 服务单位信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    服务单位信息
                </h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主营业务</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市鄞州区科技大道88号</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">李经理</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">0574-88123456</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">智能制造设备研发与生产</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">宁波XX智能制造有限公司</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市江北区工业路66号</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">王总监</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">0574-87654321</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">工业机器人系统集成</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 科技服务内容 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                    科技服务内容
                </h2>
                <div class="space-y-4">
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <h3 class="text-sm font-medium text-gray-900">宁波市XX科技有限公司服务内容</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 智能制造生产线优化方案设计<br>2. 工业机器人应用技术培训<br>3. 数字化工厂规划咨询</p>
                    </div>
                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                        <h3 class="text-sm font-medium text-gray-900">宁波XX智能制造有限公司服务内容</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 工业机器人系统集成方案设计<br>2. 自动化生产线改造技术指导<br>3. 智能制造设备选型咨询</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 法人特派员详情 (初始隐藏) -->
        <div id="legalDetail" class="space-y-6 hidden">
            <!-- 法人基本信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    法人基本信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">单位名称</label>
                        <p class="text-sm text-gray-900">宁波市智能制造研究院</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">单位性质</label>
                        <p class="text-sm text-gray-900">事业单位</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">法人代表</label>
                        <p class="text-sm text-gray-900">王建国</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">单位地址</label>
                        <p class="text-sm text-gray-900">宁波市鄞州区科技大道188号</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">授权负责人</label>
                        <p class="text-sm text-gray-900">李强</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">联系方式</label>
                        <p class="text-sm text-gray-900">0574-88123456</p>
                    </div>
                </div>
            </div>

            <!-- 单位简介 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"></path>
                    </svg>
                    单位简介
                </h2>
                <p class="text-sm text-gray-700">宁波市智能制造研究院成立于2015年，是宁波市政府重点支持的高端智能制造研究机构，主要开展智能制造技术研发、成果转化、人才培养等工作。现有专职研究人员68人，其中高级职称25人，博士32人。</p>
            </div>

            <!-- 结对信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    结对农业园区/行政主体信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">园区/主体名称</label>
                        <p class="text-sm text-gray-900">宁波市鄞州区智能制造产业园</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">联系人</label>
                        <p class="text-sm text-gray-900">张主任</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">联系方式</label>
                        <p class="text-sm text-gray-900">0574-88223344</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">地址</label>
                        <p class="text-sm text-gray-900">宁波市鄞州区科技大道199号</p>
                    </div>
                </div>
            </div>

            <!-- 科技服务协议 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    科技服务协议主要内容
                </h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">协议编号</h3>
                        <p class="text-sm text-gray-600 mt-1">NB2023-0012</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">协议内容</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 为园区企业提供智能制造技术咨询<br>2. 开展智能制造技术培训<br>3. 协助园区企业进行数字化改造</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">预期目标</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 提升园区企业智能制造水平<br>2. 培养智能制造技术人才50名<br>3. 协助5家企业完成数字化改造</p>
                    </div>
                </div>
            </div>

            <!-- 主要成员明细 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                    主要成员明细
                </h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出生年月</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务内容</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">李强</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">男</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1980-08-12</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">高级工程师</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">智能制造系统设计</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">王芳</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">女</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1985-03-25</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">工程师</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">工业机器人应用</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">张伟</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">男</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1982-11-08</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">研究员</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">数字化工厂规划</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 团队特派员详情 (初始隐藏) -->
        <div id="teamDetail" class="space-y-6 hidden">
            <!-- 团队基本信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    团队基本信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">团队名称</label>
                        <p class="text-sm text-gray-900">智能制造技术服务团队</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">特色产业</label>
                        <p class="text-sm text-gray-900">智能制造</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">团队负责人</label>
                        <p class="text-sm text-gray-900">李强</p>
                    </div>
                </div>
            </div>

            <!-- 团队负责人信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0zm6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    团队负责人信息
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">姓名</label>
                        <p class="text-sm text-gray-900">李强</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">性别</label>
                        <p class="text-sm text-gray-900">男</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">出生年月</label>
                        <p class="text-sm text-gray-900">1980-08-12</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">单位</label>
                        <p class="text-sm text-gray-900">宁波市智能制造研究院</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">职务</label>
                        <p class="text-sm text-gray-900">高级工程师</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">政治面貌</label>
                        <p class="text-sm text-gray-900">中共党员</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">联系方式</label>
                        <p class="text-sm text-gray-900">13805748888</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">专业技术特长</label>
                        <p class="text-sm text-gray-900">智能制造系统设计</p>
                    </div>
                </div>
            </div>

            <!-- 团队成员信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                    团队成员信息
                </h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出生年月</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在单位</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">王芳</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1985-03-25</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">工业机器人</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">工程师</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">13805747777</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市智能制造研究院</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">张伟</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1982-11-08</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">数字化工厂</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">研究员</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">13805746666</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市智能制造研究院</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">陈明</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">1983-07-19</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">自动化控制</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">高级工程师</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">13805745555</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波XX智能制造有限公司</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 产业链信息 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                    </svg>
                    产业链信息
                </h2>
                <div class="space-y-6">
                    <!-- 核心生产经营主体 -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-3">核心生产经营主体</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主营业务</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">基本情况</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">宁波市鄞州区科技大道88号</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">李经理</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">0574-88123456</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">智能制造设备研发与生产</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">年产值5000万，员工120人</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">宁波XX智能制造有限公司</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">宁波市江北区工业路66号</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">王总监</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">0574-87654321</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">工业机器人系统集成</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">年产值3000万，员工80人</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 其他关联单位 -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-3">其他关联单位</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主营业务</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">基本情况</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">宁波市智能制造研究院</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">宁波市鄞州区科技大道188号</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">李强</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">0574-88123456</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">智能制造技术研发与咨询</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">专职研究人员68人</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">宁波市鄞州区智能制造产业园</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">宁波市鄞州区科技大道199号</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">张主任</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">0574-88223344</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">智能制造产业园区运营</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-500">入驻企业50家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科技服务协议 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    科技服务协议主要内容
                </h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">协议编号</h3>
                        <p class="text-sm text-gray-600 mt-1">NB2023-0015</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">协议内容</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 为产业链企业提供智能制造技术咨询<br>2. 开展智能制造技术培训<br>3. 协助产业链企业进行数字化改造</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-900">预期目标</h3>
                        <p class="text-sm text-gray-600 mt-1">1. 提升产业链企业智能制造水平<br>2. 培养智能制造技术人才100名<br>3. 协助10家企业完成数字化改造</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <div class="mt-6 flex justify-end space-x-4">
            <button class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                返回列表
            </button>
            <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                导出报告
            </button>
        </div>
    </div>

    <script>
        // 切换特派员类型
        document.getElementById('personalTab').addEventListener('click', function() {
            document.getElementById('personalDetail').classList.remove('hidden');
            document.getElementById('legalDetail').classList.add('hidden');
            document.getElementById('teamDetail').classList.add('hidden');
            this.classList.remove('border', 'border-gray-300', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
            document.getElementById('legalTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('legalTab').classList.add('border', 'border-gray-300', 'text-gray-700');
            document.getElementById('teamTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('teamTab').classList.add('border', 'border-gray-300', 'text-gray-700');
        });

        document.getElementById('legalTab').addEventListener('click', function() {
            document.getElementById('personalDetail').classList.add('hidden');
            document.getElementById('legalDetail').classList.remove('hidden');
            document.getElementById('teamDetail').classList.add('hidden');
            this.classList.remove('border', 'border-gray-300', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
            document.getElementById('personalTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('personalTab').classList.add('border', 'border-gray-300', 'text-gray-700');
            document.getElementById('teamTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('teamTab').classList.add('border', 'border-gray-300', 'text-gray-700');
        });

        document.getElementById('teamTab').addEventListener('click', function() {
            document.getElementById('personalDetail').classList.add('hidden');
            document.getElementById('legalDetail').classList.add('hidden');
            document.getElementById('teamDetail').classList.remove('hidden');
            this.classList.remove('border', 'border-gray-300', 'text-gray-700');
            this.classList.add('bg-blue-600', 'text-white');
            document.getElementById('personalTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('personalTab').classList.add('border', 'border-gray-300', 'text-gray-700');
            document.getElementById('legalTab').classList.remove('bg-blue-600', 'text-white');
            document.getElementById('legalTab').classList.add('border', 'border-gray-300', 'text-gray-700');
        });
    </script>
</body>
</html>