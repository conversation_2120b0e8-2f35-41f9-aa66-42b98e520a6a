unit CcSjCysxdj;

interface

uses
  Classes;

type
  TCcSjCysxdj = class
  private

    FSjCysxdjid: Integer;
    FDdid: Integer;

    FCysxjgc: string;
    FCysxKprq: string;
    FCyrxjgc: string;
    FCyrxKprq: string;
    FCyyhjgc: string;
    FCyyhKprq: string;

    FCysxTgs: Double;
    FCysxDj: Double;
    FCysxSh: Double;
    FCysxCb: Double;

    FCyrxTgs: Double;
    FCyrxChs: Double;
    FCyrxDj: Double;
    FCyrxSh: Double;
    FCyrxCb: Double;

    FCyyhTgs: Double;
    FCyyhChs: Double;
    FCyyhDj: Double;
    FCyyhSh: Double;
    FCyyhCb: Double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property SjCysxdjid: Integer read FSjCysxdjid write FSjCysxdjid;
    property Ddid: Integer read FDdid write FDdid;

    property Cysxjgc: string read FCysxjgc write FCysxjgc;
    property CysxKprq: string read FCysxKprq write FCysxKprq;

    property Cyrxjgc: string read FCyrxjgc write FCyrxjgc;
    property CyrxKprq: string read FCyrxKprq write FCyrxKprq;

    property Cyyhjgc: string read FCyyhjgc write FCyyhjgc;
    property CyyhKprq: string read FCyyhKprq write FCyyhKprq;

    property CysxTgs: Double read FCysxTgs write FCysxTgs;
    property CysxDj: Double read FCysxDj write FCysxDj;
    property CysxSh: Double read FCysxSh write FCysxSh;
    property CysxCb: Double read FCysxCb write FCysxCb;
    property CyrxTgs: Double read FCyrxTgs write FCyrxTgs;
    property CyrxChs: Double read FCyrxChs write FCyrxChs;
    property CyrxDj: Double read FCyrxDj write FCyrxDj;
    property CyrxSh: Double read FCyrxSh write FCyrxSh;
    property CyrxCb: Double read FCyrxCb write FCyrxCb;
    property CyyhTgs: Double read FCyyhTgs write FCyyhTgs;
    property CyyhChs: Double read FCyyhChs write FCyyhChs;
    property CyyhDj: Double read FCyyhDj write FCyyhDj;
    property CyyhSh: Double read FCyyhSh write FCyyhSh;
    property CyyhCb: Double read FCyyhCb write FCyyhCb;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
