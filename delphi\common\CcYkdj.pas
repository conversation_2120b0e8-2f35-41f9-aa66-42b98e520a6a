unit CcYkdj;

interface

uses
  Classes;

type
  TCcYkdj = class
  private
    FDdid: Integer;
    FYsid: Integer;
    FChuhs: double;
    FDjlrC: double;
    FLrlC: double;
    FNotRecordCount: Integer;
    FXm: double;
    FNotRecordYsSum: double;
    FyjlrC: double;
    FZcbC: double;
    FChuhje: double;
    FRmb: double;
    FMjdj: double;
    FHl: double;
    FChs: double;
    FChje: double;
    FGj: double;
    FScs: double;
    FGjZj: double;
    FCwfy: double;
    FCaiwfy: double;
    FDdh: string;
    FPort: string;
    FKh: string;
    FKh1: string;
    FKh2: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FNotRecordListStr: string;
    FShenhe: string;
    FShoukuan: string;
    FSumxxe: double;
    FHtjj: double;
    Fbil: double;
    FDdsBb: double;
    FMonth: string;
    FDds1: Integer;
    FDds2: Integer;
    FDds3: Integer;
    FDds4: Integer;
    FDds5: Integer;
    FDds6: Integer;
    FCe: Integer;
  public
    property Ddid: Integer read FDdid write FDdid;
    property Ysid: Integer read FYsid write FYsid;
    property Chuhs: double read FChuhs write FChuhs;
    property DjlrC: double read FDjlrC write FDjlrC;
    property LrlC: double read FLrlC write FLrlC;
    property NotRecordCount: Integer read FNotRecordCount write FNotRecordCount;
    property Xm: double read FXm write FXm;
    property NotRecordYsSum: double read FNotRecordYsSum write FNotRecordYsSum;
    property yjlrC: double read FyjlrC write FyjlrC;
    property ZcbC: double read FZcbC write FZcbC;
    property Chuhje: double read FChuhje write FChuhje;
    property Rmb: double read FRmb write FRmb;
    property Mjdj: double read FMjdj write FMjdj;
    property Hl: double read FHl write FHl;
    property Chs: double read FChs write FChs;
    property Chje: double read FChje write FChje;
    property Gj: double read FGj write FGj;
    property Scs: double read FScs write FScs;
    property GjZj: double read FGjZj write FGjZj;
    property Cwfy: double read FCwfy write FCwfy;
    property Caiwfy: double read FCaiwfy write FCaiwfy;
    property Ddh: string read FDdh write FDdh;
    property Port: string read FPort write FPort;
    property Kh: string read FKh write FKh;
    property Kh1: string read FKh1 write FKh1;
    property Kh2: string read FKh2 write FKh2;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Dds: Integer read FDds write FDds;
    property Ce: Integer read FCe write FCe;
    property Dds1: Integer read FDds1 write FDds1;
    property Dds2: Integer read FDds2 write FDds2;
    property Dds3: Integer read FDds3 write FDds3;
    property Dds4: Integer read FDds4 write FDds4;
    property Dds5: Integer read FDds5 write FDds5;
    property Dds6: Integer read FDds6 write FDds6;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property NotRecordListStr: string read FNotRecordListStr
      write FNotRecordListStr;
    property Shenhe: string read FShenhe write FShenhe;
    property Shoukuan: string read FShoukuan write FShoukuan;
    property Sumxxe: double read FSumxxe write FSumxxe;
    property Htjj: double read FHtjj write FHtjj;
    property bil: double read Fbil write Fbil;
    property DdsBb: double read FDdsBb write FDdsBb;
    property Month: string read FMonth write FMonth;
  end;

implementation

end.
