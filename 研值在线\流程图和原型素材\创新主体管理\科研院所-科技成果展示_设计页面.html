<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技成果展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">科技成果展示</h1>
            <p class="mt-2 text-gray-600">全面展示科研院所的知识产权与学术成果产出情况</p>
        </div>

        <!-- 数据概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">知识产权申请量</p>
                        <p class="text-2xl font-bold text-blue-600 mt-2">156</p>
                        <p class="text-sm text-gray-500 mt-1">本年度</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">12%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">知识产权授权量</p>
                        <p class="text-2xl font-bold text-green-600 mt-2">89</p>
                        <p class="text-sm text-gray-500 mt-1">本年度</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">8%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">知识产权总持有量</p>
                        <p class="text-2xl font-bold text-purple-600 mt-2">523</p>
                        <p class="text-sm text-gray-500 mt-1">累计</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">15%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">年度论文发表量</p>
                        <p class="text-2xl font-bold text-yellow-600 mt-2">67</p>
                        <p class="text-sm text-gray-500 mt-1">本年度</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">5%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">成果转化项目数</p>
                        <p class="text-2xl font-bold text-red-600 mt-2">23</p>
                        <p class="text-sm text-gray-500 mt-1">本年度</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">18%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">成果转化收益总额</p>
                        <p class="text-2xl font-bold text-indigo-600 mt-2">¥1,245万</p>
                        <p class="text-sm text-gray-500 mt-1">本年度</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        <span class="text-sm ml-1">25%</span>
                    </div>
                </div>
                <button class="mt-4 text-blue-600 text-sm flex items-center">
                    查看详情
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 知识产权申请与授权趋势 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">知识产权申请与授权趋势</h3>
                <div class="h-80">
                    <canvas id="ipTrendChart"></canvas>
                </div>
            </div>

            <!-- 成果转化类型分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">成果转化类型分布</h3>
                <div class="h-80">
                    <canvas id="transferTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 论文质量分布 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">论文质量分布</h3>
            <div class="flex flex-col lg:flex-row items-center">
                <div class="w-full lg:w-1/2 h-80">
                    <canvas id="paperQualityChart"></canvas>
                </div>
                <div class="w-full lg:w-1/2 mt-6 lg:mt-0 lg:pl-6">
                    <div class="bg-blue-50 rounded-lg p-6 text-center">
                        <p class="text-sm text-blue-700 mb-2">平均影响因子</p>
                        <p class="text-3xl font-bold text-blue-800">5.67</p>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mt-6">
                        <div class="bg-green-50 rounded-lg p-4">
                            <p class="text-sm text-green-700 mb-1">SCI Q1</p>
                            <p class="text-xl font-bold text-green-800">23</p>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4">
                            <p class="text-sm text-green-700 mb-1">SCI Q2</p>
                            <p class="text-xl font-bold text-green-800">18</p>
                        </div>
                        <div class="bg-yellow-50 rounded-lg p-4">
                            <p class="text-sm text-yellow-700 mb-1">EI</p>
                            <p class="text-xl font-bold text-yellow-800">12</p>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700 mb-1">CSSCI</p>
                            <p class="text-xl font-bold text-gray-800">14</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 知识产权结构 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">知识产权结构</h3>
                <div class="flex items-center">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>当前年度</option>
                        <option>全部年度</option>
                    </select>
                </div>
            </div>
            <div class="flex flex-col lg:flex-row items-center">
                <div class="w-full lg:w-1/2 h-80">
                    <canvas id="ipStructureChart"></canvas>
                </div>
                <div class="w-full lg:w-1/2 mt-6 lg:mt-0 lg:pl-6">
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-700">发明专利</p>
                            </div>
                            <p class="text-sm font-bold text-gray-900">245</p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-700">实用新型</p>
                            </div>
                            <p class="text-sm font-bold text-gray-900">156</p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-700">外观设计</p>
                            </div>
                            <p class="text-sm font-bold text-gray-900">67</p>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-purple-500 rounded-full mr-3"></div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-700">软件著作权</p>
                            </div>
                            <p class="text-sm font-bold text-gray-900">55</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成果清册 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">成果清册</h3>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出Excel
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            新增成果
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号/刊号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">一种新型纳米材料制备方法</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">发明专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">基于深度学习的图像识别系统</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">软件著作权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023SR123456</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-25</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">Advanced Materials Research</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">SCI论文</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">DOI:10.1016/j.amr.2023.123456</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02-18</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-30</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已发表</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">一种环保包装设计</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">外观设计</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202330123456.X</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">审核中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能控制系统</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">发明专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310654321.8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">实质审查</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 156 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成果详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">成果详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">基本信息</h4>
                            <div class="space-y-4 text-sm">
                                <div>
                                    <p class="text-gray-500 font-medium">成果名称</p>
                                    <p class="text-gray-900 mt-1">一种新型纳米材料制备方法</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">成果类型</p>
                                    <p class="text-gray-900 mt-1">发明专利</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">申请号/刊号</p>
                                    <p class="text-gray-900 mt-1">CN202310123456.7</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">申请日期</p>
                                    <p class="text-gray-900 mt-1">2023-03-15</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">授权日期</p>
                                    <p class="text-gray-900 mt-1">2023-09-20</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">成果状态</p>
                                    <p class="text-gray-900 mt-1">已授权</p>
                                </div>
                            </div>
                        </div>

                        <!-- 成果详情 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">成果详情</h4>
                            <div class="space-y-4 text-sm">
                                <div>
                                    <p class="text-gray-500 font-medium">成果持有人</p>
                                    <p class="text-gray-900 mt-1">宁波市材料研究所</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">发明人/作者</p>
                                    <p class="text-gray-900 mt-1">张研究员、李教授、王博士</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">转化方式</p>
                                    <p class="text-gray-900 mt-1">专利许可</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">转化收益</p>
                                    <p class="text-gray-900 mt-1">¥120万元</p>
                                </div>
                                <div>
                                    <p class="text-gray-500 font-medium">技术领域</p>
                                    <p class="text-gray-900 mt-1">新材料</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附件下载 -->
                    <div class="mt-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">附件下载</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm text-gray-700">专利说明书.pdf</span>
                                </div>
                                <button class="text-blue-600 text-sm hover:text-blue-800">下载</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-sm text-gray-700">许可合同.docx</span>
                                </div>
                                <button class="text-blue-600 text-sm hover:text-blue-800">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 mr-3">关闭</button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">编辑</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 知识产权趋势图表
            const ipTrendCtx = document.getElementById('ipTrendChart');
            if (ipTrendCtx) {
                new Chart(ipTrendCtx, {
                    type: 'bar',
                    data: {
                        labels: ['2021', '2022', '2023'],
                        datasets: [
                            {
                                label: '申请量',
                                type: 'line',
                                data: [120, 145, 156],
                                borderColor: '#3B82F6',
                                backgroundColor: 'transparent',
                                borderWidth: 2,
                                pointBackgroundColor: '#3B82F6',
                                pointRadius: 4,
                                tension: 0.4
                            },
                            {
                                label: '授权量',
                                data: [75, 82, 89],
                                backgroundColor: '#10B981',
                                borderColor: '#10B981',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 成果转化类型分布图表
            const transferTypeCtx = document.getElementById('transferTypeChart');
            if (transferTypeCtx) {
                new Chart(transferTypeCtx, {
                    type: 'bar',
                    data: {
                        labels: ['专利转让', '专利许可', '技术入股', '其他'],
                        datasets: [
                            {
                                label: '项目数',
                                data: [12, 8, 2, 1],
                                backgroundColor: '#3B82F6',
                                borderColor: '#3B82F6',
                                borderWidth: 1
                            },
                            {
                                label: '收益(万元)',
                                data: [480, 640, 100, 25],
                                backgroundColor: '#8B5CF6',
                                borderColor: '#8B5CF6',
                                borderWidth: 1
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            x: {
                                stacked: false,
                            },
                            y: {
                                stacked: false,
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 论文质量分布图表
            const paperQualityCtx = document.getElementById('paperQualityChart');
            if (paperQualityCtx) {
                new Chart(paperQualityCtx, {
                    type: 'radar',
                    data: {
                        labels: ['SCI Q1', 'SCI Q2', 'SCI Q3', 'SCI Q4', 'EI', 'CSSCI'],
                        datasets: [{
                            label: '论文数量',
                            data: [23, 18, 12, 8, 12, 14],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: '#3B82F6',
                            pointBackgroundColor: '#3B82F6',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#3B82F6'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            r: {
                                angleLines: {
                                    display: true
                                },
                                suggestedMin: 0,
                                suggestedMax: 30
                            }
                        }
                    }
                });
            }

            // 知识产权结构图表
            const ipStructureCtx = document.getElementById('ipStructureChart');
            if (ipStructureCtx) {
                new Chart(ipStructureCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['发明专利', '实用新型', '外观设计', '软件著作权'],
                        datasets: [{
                            data: [245, 156, 67, 55],
                            backgroundColor: [
                                '#3B82F6',
                                '#10B981',
                                '#F59E0B',
                                '#8B5CF6'
                            ],
                            borderColor: [
                                '#3B82F6',
                                '#10B981',
                                '#F59E0B',
                                '#8B5CF6'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // 为所有详情按钮绑定事件
            document.querySelectorAll('button').forEach(btn => {
                if (btn.textContent === '详情') {
                    btn.addEventListener('click', () => openModal('detailModal'));
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('detailModal');
                }
            });
        });
    </script>
</body>
</html>