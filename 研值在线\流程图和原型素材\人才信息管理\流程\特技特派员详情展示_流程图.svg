<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技特派员详情展示业务流程</text>

  <!-- 阶段一：用户入口与类别选择 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户入口与类别选择</text>
  
  <!-- 节点1: 用户入口 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户入口选择</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">特技特派员详情展示</text>
  </g>

  <!-- 节点2: 类别指定 -->
  <g transform="translate(880, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">类别指定</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">个人/法人/团队</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 520 165 Q 650 165, 880 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据同步与信息聚合 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据同步与信息聚合</text>

  <!-- 节点3: 数据同步 -->
  <g transform="translate(150, 300)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据中心同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">获取全量信息</text>
  </g>

  <!-- 节点4: 个人特派员处理 -->
  <g transform="translate(450, 300)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">个人特派员</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">基础身份与服务内容</text>
  </g>

  <!-- 节点5: 法人特派员处理 -->
  <g transform="translate(750, 300)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">法人特派员</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">单位信息与成员明细</text>
  </g>

  <!-- 节点6: 团队特派员处理 -->
  <g transform="translate(1050, 300)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">团队特派员</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">多维度成员资料</text>
  </g>

  <!-- 连接线 类别 -> 各处理模块 -->
  <path d="M 990 200 C 990 250, 260 250, 260 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 990 200 C 990 250, 560 250, 560 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 990 200 C 990 250, 860 250, 860 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 990 200 C 990 250, 1160 250, 1160 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据校验与展示管理 -->
  <text x="700" y="450" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据校验与展示管理</text>

  <!-- 节点7: 数据校验 -->
  <g transform="translate(200, 480)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">完整性与合规性检查</text>
  </g>

  <!-- 节点8: 模块切换浏览 -->
  <g transform="translate(500, 480)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模块切换浏览</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">成员/协议/产业链</text>
  </g>

  <!-- 节点9: 详情钻取 -->
  <g transform="translate(800, 480)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">成员档案与服务对象</text>
  </g>

  <!-- 连接线 各处理模块 -> 校验展示 -->
  <path d="M 260 370 C 260 425, 310 425, 310 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 560 370 C 560 425, 610 425, 610 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 860 370 C 860 425, 910 425, 910 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1160 370 C 1160 425, 310 425, 310 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 校验 -> 浏览 -> 钻取 -->
  <path d="M 420 515 Q 460 515, 500 515" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 515 Q 760 515, 800 515" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与审计管理 -->
  <text x="700" y="630" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与审计管理</text>
  
  <!-- 节点10: 数据导出 -->
  <g transform="translate(400, 660)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">详情信息报告</text>
  </g>

  <!-- 节点11: 操作审计 -->
  <g transform="translate(700, 660)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作审计</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">用户操作日志记录</text>
  </g>

  <!-- 连接线 钻取 -> 导出审计 -->
  <path d="M 910 550 C 910 605, 510 605, 510 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 910 550 C 910 605, 810 605, 810 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 从操作审计回到数据同步 -->
  <path d="M 700 695 C 50 695, 50 335, 150 335" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="520" text-anchor="middle" font-size="11" fill="#666">审计反馈</text>

  <!-- 反馈循环2: 从详情钻取回到模块浏览 -->
  <path d="M 800 515 C 750 515, 750 515, 720 515" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="760" y="505" text-anchor="middle" font-size="11" fill="#666">钻取反馈</text>

  <!-- 全流程监控标识 -->
  <g transform="translate(1100, 800)" filter="url(#soft-shadow)">
    <rect width="180" height="50" rx="8" ry="8" fill="#F0F0F0" stroke="#CCCCCC" stroke-width="1" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="500" fill="#666">全流程监控</text>
    <text x="90" y="40" text-anchor="middle" font-size="10" fill="#888">实时状态跟踪</text>
  </g>

</svg>