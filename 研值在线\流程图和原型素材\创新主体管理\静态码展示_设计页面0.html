<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态码展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">静态码展示</h1>
            <p class="text-gray-600">为创新主体生成唯一科研码及二维码，便于信息展示与传播</p>
        </div>

        <!-- 生成配置区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                生成配置
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">主体类型 <span class="text-red-500">*</span></label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择主体类型</option>
                        <option value="enterprise">企业</option>
                        <option value="university">高校</option>
                        <option value="institute">科研院所</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码 <span class="text-red-500">*</span></label>
                    <input type="text" placeholder="请输入18位信用代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业/领域代码 <span class="text-red-500">*</span></label>
                    <div class="relative">
                        <button class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                            <span>请选择行业领域</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">科研活动类型 <span class="text-red-500">*</span></label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            基础研究
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            应用研究
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            技术开发
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">随机标识符</label>
                    <div class="flex items-center">
                        <input type="text" placeholder="系统自动生成" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                        <button class="ml-2 p-2 bg-gray-100 rounded-md hover:bg-gray-200">
                            <svg class="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex justify-end mt-6">
                <button class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    生成科研码
                </button>
            </div>
        </div>

        <!-- 静态码预览区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                </svg>
                静态码预览
            </h2>
            <div class="flex flex-col items-center">
                <div class="bg-gray-100 p-6 rounded-lg shadow-inner mb-4">
                    <!-- 二维码占位图 -->
                    <svg class="w-64 h-64 bg-white p-4 rounded" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                        <rect x="10%" y="10%" width="80%" height="80%" fill="none" stroke="black" stroke-width="2"/>
                        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-size="12">科研码二维码</text>
                    </svg>
                </div>
                <div class="text-center mb-6">
                    <p class="text-sm font-mono bg-gray-100 p-2 rounded">KYM-ENT-3302-IT-8A3B5C</p>
                    <p class="text-sm text-gray-500 mt-2">宁波市智能科技股份有限公司</p>
                    <p class="text-xs text-gray-500">企业 | 信息技术 | 技术开发</p>
                </div>
                <div class="flex space-x-4">
                    <div class="relative">
                        <select class="appearance-none bg-white border border-gray-300 rounded-md px-4 py-2 pr-8 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>300×300</option>
                            <option>500×500</option>
                            <option>800×800</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </div>
                    </div>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        下载PNG
                    </button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                        </svg>
                        复制链接
                    </button>
                </div>
            </div>
        </div>

        <!-- 信息摘要区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                    信息摘要
                </h2>
                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                    查看完整档案
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">工商注册信息</h3>
                    <p class="text-sm text-gray-600">统一社会信用代码: 91330200MA2A1B2C3D</p>
                    <p class="text-sm text-gray-600">注册资本: 5000万元</p>
                    <p class="text-sm text-gray-600">成立日期: 2018-05-15</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">最新科技成果</h3>
                    <p class="text-sm text-gray-600">发明专利: 23项</p>
                    <p class="text-sm text-gray-600">软件著作权: 15项</p>
                    <p class="text-sm text-gray-600">论文发表: 12篇</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">仪器设备概览</h3>
                    <p class="text-sm text-gray-600">大型设备: 8台</p>
                    <p class="text-sm text-gray-600">总值: 3200万元</p>
                    <p class="text-sm text-gray-600">共享设备: 5台</p>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-2">获奖荣誉概览</h3>
                    <p class="text-sm text-gray-600">宁波市科技进步奖: 2项</p>
                    <p class="text-sm text-gray-600">高新技术企业: 是</p>
                    <p class="text-sm text-gray-600">专精特新: 是</p>
                </div>
            </div>
            <div class="mt-4 text-right text-xs text-gray-500">
                最后更新时间: 2024-01-20 14:30:45
            </div>
        </div>

        <!-- 合规与防伪提示区 -->
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-lg">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">合规与防伪提示</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>• 本二维码采用AES-256加密和数字签名技术，确保唯一性和真实性</p>
                        <p>• 请勿将二维码用于非法用途或擅自修改内容</p>
                        <p>• 如需验证真伪，请使用官方验证工具扫描二维码</p>
                    </div>
                    <div class="mt-4">
                        <div class="-mx-2 -my-1.5 flex">
                            <button class="bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50">
                                验证真伪
                            </button>
                            <button class="ml-3 bg-yellow-50 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-yellow-600 focus:ring-offset-2 focus:ring-offset-yellow-50">
                                联系我们
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 生成状态模拟
        document.querySelector('button:contains("生成科研码")').addEventListener('click', function() {
            // 模拟生成过程
            const previewSection = document.querySelector('.bg-white.rounded-lg.shadow-md.p-6.mb-6:nth-child(3)');
            previewSection.classList.remove('hidden');
            
            // 显示成功提示
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center';
            toast.innerHTML = `
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                生成成功，可下载或分享
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        });

        // 复制链接功能
        document.querySelector('button:contains("复制链接")').addEventListener('click', function() {
            navigator.clipboard.writeText('https://example.com/kym/ENT-3302-IT-8A3B5C');
            
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-md shadow-lg flex items-center';
            toast.innerHTML = `
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                </svg>
                链接已复制到剪贴板
            `;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.classList.add('opacity-0', 'transition-opacity', 'duration-300');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        });
    </script>
</body>
</html>