<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">原政策关系管理流程</text>

  <!-- 阶段一：关系创建与校验 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：关系创建与校验</text>
  
  <!-- 节点1: 关系创建 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关系创建</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(运维人员发起新旧政策关系创建)</text>
  </g>

  <!-- 节点2: 校验 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(政策条目存在性与同类关系唯一性)</text>
  </g>

  <!-- 节点3: 生成关系记录 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成关系记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(标记状态为"待审核")</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 L 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 165 L 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与生效 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与生效</text>

  <!-- 节点4: 推送至管理员 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送至管理员</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(系统将待审核记录推送)</text>
  </g>

  <!-- 节点5: 管理员审核 -->
  <g transform="translate(500, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(核对关系类型与生效时间)</text>
  </g>

  <!-- 节点6: 状态更新 -->
  <g transform="translate(800, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(更新为"已生效"并写入缓存)</text>
  </g>
  
  <!-- 连接线 3 -> 4 -->
  <path d="M 900 200 C 900 240, 400 240, 300 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 400 335 L 500 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 335 L 800 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：变更通知与依赖处理 -->
  <text x="600" y="420" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：变更通知与依赖处理</text>

  <!-- 节点7: 变更通知 -->
  <g transform="translate(200, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更通知</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(通知引用旧政策的业务模块)</text>
  </g>

  <!-- 节点8: 业务适配 -->
  <g transform="translate(200, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">业务适配</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(根据新政策进行适配或标记替代)</text>
  </g>

  <!-- 节点9: 关系修改/删除 -->
  <g transform="translate(500, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关系修改/删除</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户操作已生效关系)</text>
  </g>

  <!-- 节点10: 依赖检查 -->
  <g transform="translate(800, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">依赖检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(检查下游依赖)</text>
  </g>

  <!-- 节点11: 风险提示 -->
  <g transform="translate(700, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">风险提示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(存在阻断风险时提示处理依赖)</text>
  </g>

  <!-- 节点12: 变更记录 -->
  <g transform="translate(900, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">变更记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(无风险时记录变更并更新缓存)</text>
  </g>
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 900 370 C 900 410, 400 410, 300 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 7 -> 8 -->
  <path d="M 300 540 L 300 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 -->
  <path d="M 700 505 L 800 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 10 -> 11 (有风险) -->
  <path d="M 900 540 C 900 565, 850 565, 800 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="850" y="565" text-anchor="middle" font-size="12" fill="#555">有风险</text>

  <!-- 连接线 10 -> 12 (无风险) -->
  <path d="M 900 540 C 900 565, 950 565, 1000 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="950" y="565" text-anchor="middle" font-size="12" fill="#555">无风险</text>

  <!-- 阶段四：审计与报告 -->
  <text x="600" y="700" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审计与报告</text>

  <!-- 节点13: 审计库 -->
  <g transform="translate(400, 730)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(归档历史关系与操作日志)</text>
  </g>

  <!-- 节点14: 政策沿革报告 -->
  <g transform="translate(700, 730)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">政策沿革报告</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(供合规与管理层查询)</text>
  </g>

  <!-- 连接线 8 -> 13 -->
  <path d="M 300 660 C 300 695, 350 695, 400 730" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 11 -> 13 -->
  <path d="M 800 660 C 800 695, 600 695, 500 730" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 12 -> 13 -->
  <path d="M 1000 660 C 1000 695, 650 695, 550 730" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 13 -> 14 -->
  <path d="M 600 765 L 700 765" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="650" y="750" text-anchor="middle" font-size="12" fill="#555">定时生成</text>

  <!-- 定时任务标识 -->
  <text x="400" y="710" font-size="12" text-anchor="middle" font-style="italic" fill="#555">每日定时任务</text>

</svg>