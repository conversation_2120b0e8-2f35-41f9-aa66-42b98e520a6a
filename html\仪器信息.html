<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">仪器信息</h1>
                    <p class="mt-2 text-sm text-gray-600">为科研管理与创新主体用户提供全方位、结构化的仪器设备信息展示和检索服务</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出数据
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 数据总览区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 设备类型分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">设备类型分布</h3>
                <div class="h-64">
                    <canvas id="typeChart"></canvas>
                </div>
            </div>
            
            <!-- 领域分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">领域分布</h3>
                <div class="h-64">
                    <canvas id="fieldChart"></canvas>
                </div>
            </div>
            
            <!-- 区域分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">区域分布</h3>
                <div class="h-64">
                    <canvas id="regionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 设备原值分布 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">设备原值分布</h3>
            <div class="h-80">
                <canvas id="valueChart"></canvas>
            </div>
        </div>

        <!-- 查询与筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">查询与筛选</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">仪器设备名称</label>
                    <input type="text" placeholder="请输入设备名称" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部分类</option>
                        <option>光谱仪器</option>
                        <option>色谱仪器</option>
                        <option>质谱仪器</option>
                        <option>电化学仪器</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">生产制造商</label>
                    <input type="text" placeholder="请输入制造商" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">规格型号</label>
                    <input type="text" placeholder="请输入规格型号" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属单位</label>
                    <input type="text" placeholder="请输入所属单位" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">启用时间（开始）</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">启用时间（结束）</label>
                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">共享状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部状态</option>
                        <option>对外共享</option>
                        <option>内部使用</option>
                        <option>暂停共享</option>
                    </select>
                </div>
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置筛选
                </button>
            </div>
        </div>

        <!-- 仪器设备列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">仪器设备列表</h3>
                    <div class="text-sm text-gray-500">
                        共找到 <span class="font-medium text-gray-900">2,847</span> 台设备
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仪器名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原值（万元）</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">制造商</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产地国别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在地</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">启用时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">共享状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高分辨率质谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">285.6</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赛默飞世尔</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Q Exactive HF-X</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">美国</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">质谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市科学院</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">对外共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">液相色谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156.8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">安捷伦</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1290 Infinity II</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">美国</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">色谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市第一医院</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">内部使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">原子吸收光谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">89.5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">岛津</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AA-7000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">日本</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">经开区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-08</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市环保局</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">对外共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">电化学工作站</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45.2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">普林斯顿</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">VersaSTAT 4</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">美国</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">电化学仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市大学</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">暂停共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="showDetail('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">红外光谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">67.3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">布鲁克</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">VERTEX 80v</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">德国</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-25</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市检测中心</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">对外共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="showDetail('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 2,847 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 仪器详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">仪器详情</h3>
                    <button onclick="hideDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- 基础信息 -->
                        <div class="lg:col-span-2">
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">仪器名称：</span>
                                        <span class="font-medium text-gray-900" id="detail-name">高分辨率质谱仪</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">设备编号：</span>
                                        <span class="font-medium text-gray-900">MS-2024-001</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">原值：</span>
                                        <span class="font-medium text-gray-900">285.6万元</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">制造商：</span>
                                        <span class="font-medium text-gray-900">赛默飞世尔</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">规格型号：</span>
                                        <span class="font-medium text-gray-900">Q Exactive HF-X</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">产地国别：</span>
                                        <span class="font-medium text-gray-900">美国</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">分类：</span>
                                        <span class="font-medium text-gray-900">质谱仪器</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">启用时间：</span>
                                        <span class="font-medium text-gray-900">2023-03-15</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 功能和技术指标 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">功能和技术指标</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p><span class="font-medium">分辨率：</span>240,000 FWHM (m/z 200)</p>
                                    <p><span class="font-medium">质量范围：</span>50-6,000 m/z</p>
                                    <p><span class="font-medium">质量精度：</span>&lt;1 ppm RMS</p>
                                    <p><span class="font-medium">扫描速度：</span>最高18 Hz</p>
                                    <p><span class="font-medium">离子源：</span>HESI-II、APCI、ESI</p>
                                </div>
                            </div>
                            
                            <!-- 主要学科领域 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">主要学科领域</h4>
                                <div class="flex flex-wrap gap-2">
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">生物医学</span>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">药物分析</span>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">环境科学</span>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">食品安全</span>
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">代谢组学</span>
                                </div>
                            </div>
                            
                            <!-- 服务内容 -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">服务内容</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p>• 小分子化合物定性定量分析</p>
                                    <p>• 蛋白质组学分析</p>
                                    <p>• 代谢组学研究</p>
                                    <p>• 药物代谢产物分析</p>
                                    <p>• 环境污染物检测</p>
                                    <p>• 食品添加剂及农药残留检测</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧信息 -->
                        <div>
                            <!-- 仪器图片 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">仪器图片</h4>
                                <div class="space-y-4">
                                    <img src="https://source.unsplash.com/400x300?laboratory,equipment" alt="仪器图片" class="w-full rounded-lg">
                                    <div class="grid grid-cols-2 gap-2">
                                        <img src="https://source.unsplash.com/200x150?scientific,instrument" alt="仪器图片" class="w-full rounded-lg">
                                        <img src="https://source.unsplash.com/200x150?mass,spectrometer" alt="仪器图片" class="w-full rounded-lg">
                                    </div>
                                    <button class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                                        查看更多图片
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 使用状态与预约管理 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">使用状态与预约管理</h4>
                                <div class="text-sm space-y-3">
                                    <div>
                                        <span class="text-gray-500">当前状态：</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">空闲可用</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">当前使用人：</span>
                                        <span class="font-medium text-gray-900">无</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">下次预约：</span>
                                        <span class="font-medium text-gray-900">2024-01-20 09:00</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">预约用户：</span>
                                        <span class="font-medium text-gray-900">张研究员</span>
                                    </div>
                                    <button class="w-full mt-3 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                        查看预约日历
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 仪器提供方信息 -->
                            <div class="bg-gray-50 rounded-lg p-6">
                                <h4 class="text-md font-semibold text-gray-900 mb-4">仪器提供方信息</h4>
                                <div class="text-sm space-y-3">
                                    <div>
                                        <span class="text-gray-500">所属单位：</span>
                                        <span class="font-medium text-gray-900">市科学院</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">所在地：</span>
                                        <span class="font-medium text-gray-900">高新区科技大道123号</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">联系人：</span>
                                        <span class="font-medium text-gray-900">李主任</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">联系电话：</span>
                                        <span class="font-medium text-gray-900">0571-88888888</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">共享服务范围：</span>
                                        <span class="font-medium text-gray-900">全市范围</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">对外可见：</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">是</span>
                                    </div>
                                    <button class="w-full mt-3 px-3 py-2 border border-gray-300 text-gray-700 text-sm rounded-md hover:bg-gray-50">
                                        查看单位详情
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设备类型分布图表
            const typeCtx = document.getElementById('typeChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],
                    datasets: [{
                        data: [856, 642, 523, 387, 298, 141],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 领域分布图表
            const fieldCtx = document.getElementById('fieldChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'bar',
                data: {
                    labels: ['生物医学', '材料科学', '环境科学', '化学分析', '物理研究'],
                    datasets: [{
                        label: '设备数量',
                        data: [1245, 987, 756, 623, 236],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 区域分布图表
            const regionCtx = document.getElementById('regionChart').getContext('2d');
            new Chart(regionCtx, {
                type: 'pie',
                data: {
                    labels: ['高新区', '市中心区', '经开区', '其他区域'],
                    datasets: [{
                        data: [1156, 823, 567, 301],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 设备原值分布图表
            const valueCtx = document.getElementById('valueChart').getContext('2d');
            new Chart(valueCtx, {
                type: 'bar',
                data: {
                    labels: ['10万以下', '10-50万', '50-100万', '100-200万', '200-500万', '500万以上'],
                    datasets: [{
                        label: '设备数量',
                        data: [1245, 856, 423, 234, 67, 22],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });

        // 显示详情弹窗
        function showDetail(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏详情弹窗
        function hideDetail() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDetail();
            }
        });
    </script>
</body>
</html>