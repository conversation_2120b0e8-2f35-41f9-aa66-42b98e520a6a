object CC_XmLbFrame: TCC_XmLbFrame
  Left = 0
  Top = 0
  Width = 614
  Height = 595
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel2: TRzPanel
    Left = 0
    Top = 130
    Width = 614
    Height = 465
    Align = alClient
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 0
    object AdvStringGrid1: TAdvStringGrid
      Left = 0
      Top = 0
      Width = 614
      Height = 465
      Cursor = crDefault
      Align = alClient
      BevelInner = bvNone
      BevelOuter = bvNone
      Ctl3D = False
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVert<PERSON>ine, goHorzLine, goRowSelect]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      ScrollBars = ssBoth
      TabOrder = 0
      OnMouseMove = AdvStringGrid1MouseMove
      GridLineColor = 15855083
      GridFixedLineColor = 13745060
      HoverRowCells = [hcNormal, hcSelected]
      OnAnchorClick = AdvStringGrid1AnchorClick
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #23435#20307
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = 10344697
      ActiveCellColorTo = 6210033
      ColumnHeaders.Strings = (
        #32534#21495
        #21517#31216)
      ControlLook.FixedGradientFrom = 16513526
      ControlLook.FixedGradientTo = 15260626
      ControlLook.FixedGradientHoverFrom = 15000287
      ControlLook.FixedGradientHoverTo = 14406605
      ControlLook.FixedGradientHoverMirrorFrom = 14406605
      ControlLook.FixedGradientHoverMirrorTo = 13813180
      ControlLook.FixedGradientHoverBorder = 12033927
      ControlLook.FixedGradientDownFrom = 14991773
      ControlLook.FixedGradientDownTo = 14991773
      ControlLook.FixedGradientDownMirrorFrom = 14991773
      ControlLook.FixedGradientDownMirrorTo = 14991773
      ControlLook.FixedGradientDownBorder = 14991773
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -12
      FilterDropDown.Font.Name = #24494#36719#38597#40657
      FilterDropDown.Font.Style = []
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 80
      FixedRowHeight = 22
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      Flat = True
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = 16513526
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      ShowSelection = False
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ColWidths = (
        80
        270
        122
        64
        64)
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 0
    Width = 614
    Height = 130
    Align = alTop
    BorderOuter = fsFlat
    Color = clWhite
    TabOrder = 1
    object RzPanel4: TRzPanel
      Left = 1
      Top = 1
      Width = 612
      Height = 32
      Align = alTop
      BorderOuter = fsFlat
      Color = clWhite
      TabOrder = 0
      object RzToolbar1: TRzToolbar
        Left = 1
        Top = 1
        Width = 610
        Height = 30
        Align = alClient
        AutoStyle = False
        BorderInner = fsNone
        BorderOuter = fsNone
        BorderSides = [sdTop]
        BorderWidth = 0
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        VisualStyle = vsGradient
        ToolbarControls = (
          Btn_Add
          Btn_Modify)
        object Btn_Add: TAdvGlowButton
          Left = 4
          Top = -2
          Width = 80
          Height = 33
          BorderStyle = bsNone
          Caption = #26032'  '#22686
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
            61000000D04944415438CBA592BB0DC240104407E4424C07848410D105455000
            08B9051A8046A8801E9C5103C7318F009B9FD6922D365CDD9B5B3D8DF4E78CA2
            65759EAE201FCC557612A4BA9A5D26D1DB225A9A54C2AD856552D975411CE0AB
            20B7B0EC9B06054012E4176CF2B000B7010D0CF76E8981B0AF9FE12EDB02CBB6
            6C644BA07ABFD4A40884F58165AB94A42212D60396DD388884F581E115908FE6
            0D36D0023CFF81B7B6440363D59D4D5C9FC61B60F7F9F37E19BFED281282EFB3
            073691E7993FC2A219C74D8C850DA8B28E91B0681E24C055969B0887E8000000
            0049454E44AE426082}
          Transparent = True
          TabOrder = 0
          OnClick = Btn_AddClick
          Appearance.Color = clSilver
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 16316405
          Appearance.ColorDownTo = 16316405
          Appearance.ColorHot = 16316405
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 16316405
          Appearance.ColorMirrorDownTo = 16316405
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Modify: TAdvGlowButton
          Left = 84
          Top = -2
          Width = 80
          Height = 33
          BorderStyle = bsNone
          Caption = #20445'  '#23384
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Picture.Data = {
            89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
            61000000097048597300000B1300000B1301009A9C180000000467414D410000
            B18E7CFB5193000000206348524D00007A25000080830000F9FF000080E90000
            75300000EA6000003A980000176F925FC5460000023F4944415478DAA4924F48
            545118C57FEFBEE7E8589AD9206A9B302BC8B185F68F2021243208A17FD2CA20
            0BCA2C905611156D835695524991105221ADA210155C6541B5280B9769FE1BC7
            C4DEBCE7BC79F7DDDBC26674E5047D70561FE77CE79E7B0CAD35FF33D6D5D730
            D0DD7507B80084FF91B70874D6379FBD62EC6A7A541ACA0B4FED6C3C4973C577
            4239E6AACC8584C7CB896A867B9FA182205FA8C0CFC51078299D950CA0012FA5
            3184000859005A6B1C57525C5C9C55401A0E8E2BD11A02DF352CAD7C4369B09D
            148585855905A67E79D84E6AC90A9071E0BA3EE17076819C502EAEEB23AABA50
            7AF686581280A4E7639A665648ED635BA7886E2BC10AA9169176E0A77C66E6EC
            55AF7BD2E3E6600BFBA21B693B7A8C83B57BD758E96520258DB7678099E5C445
            0C4395A4E3436CB8C4EE688413F575F40CBDA1FFE30747F0F70D81F43378D15E
            4A6DCD2DF2CB2ED2B0FF01DD6D11CA2BDBA9A92EA0A1BE96F1C4247DEF3FE14F
            54D52C858826903273F97ADF694491C39996433CEF196260F4089B2AD7B2A76E
            2B3F7E4FF0B0AB0F73A4155C3567A5DB213D6FB9DF84B0FD38B120CE81A62AE6
            E336EB23058C2526E97D3C8CFCD98929C7009B15217A19B446AFB1CE2963E8ED
            57E2324E50E431694FF3EAC93BF267EFE22FE6912E82C010B6A1037CCF23F016
            D1499BD1F1048723EDE4CD97F3B97F8C9833C7E0D36F5CDEDE4132918B4EDA18
            5AE1CE8F270CC0A83DDE714F98D639ADF58256C1F4CAAF533BBA2B022B16B6BE
            9C1F3555D1663072B492235A059D716BCBFD3F030085191CC405D3C4A0000000
            0049454E44AE426082}
          Transparent = True
          TabOrder = 1
          OnClick = Btn_ModifyClick
          Appearance.Color = clSilver
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 16316405
          Appearance.ColorCheckedTo = 16316405
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 16316405
          Appearance.ColorDownTo = 16316405
          Appearance.ColorHot = 16316405
          Appearance.ColorHotTo = 16316405
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 16316405
          Appearance.ColorMirrorHotTo = 16316405
          Appearance.ColorMirrorDown = 16316405
          Appearance.ColorMirrorDownTo = 16316405
          Appearance.ColorMirrorChecked = 16316405
          Appearance.ColorMirrorCheckedTo = 16316405
          Appearance.ColorMirrorDisabled = 16316405
          Appearance.ColorMirrorDisabledTo = 16316405
          Layout = blGlyphLeftAdjusted
        end
      end
    end
    object RzPanel5: TRzPanel
      Left = 1
      Top = 33
      Width = 612
      Height = 96
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 1
      object RzLabel1: TRzLabel
        Left = 16
        Top = 56
        Width = 44
        Height = 17
        Caption = #31867'  '#21035#65306
      end
      object RzLabel2: TRzLabel
        Left = 16
        Top = 16
        Width = 44
        Height = 17
        Caption = #39033'  '#30446#65306
      end
      object Edit_Lb: TRzEdit
        Left = 66
        Top = 52
        Width = 250
        Height = 25
        Text = ''
        TabOrder = 0
        OnChange = Edit_LbChange
      end
      object ComboBox_Xm: TRzComboBox
        Left = 66
        Top = 13
        Width = 250
        Height = 25
        DropDownCount = 20
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        OnChange = ComboBox_XmChange
      end
    end
  end
end
