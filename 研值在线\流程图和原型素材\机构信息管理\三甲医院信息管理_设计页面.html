<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">三甲医院信息管理</h1>

        <!-- 筛选区域 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">查询筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">医院名称</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入医院名称">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">举办单位</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="gov">政府</option>
                        <option value="university">高校</option>
                        <option value="enterprise">企业</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行政区划</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="haishu">海曙区</option>
                        <option value="jiangdong">江东区</option>
                        <option value="jiangbei">江北区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">医院等级</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">三甲</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">三乙</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">二甲</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">专科类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="general">综合医院</option>
                        <option value="tumor">肿瘤医院</option>
                        <option value="children">儿童医院</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-lg font-medium text-gray-800">医院列表</h2>
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    导出
                </button>
                <button class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    导入
                </button>
                <button onclick="openHospitalModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增医院
                </button>
            </div>
        </div>

        <!-- 医院列表 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">医院名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">信用代码</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举办单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">床位数</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">重点科室</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科创平台</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在研项目</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科研成果</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完整度</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市第一医院</div>
                                <div class="text-sm text-gray-500">三甲综合医院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345678</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市卫健委</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openHospitalModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市第二医院</div>
                                <div class="text-sm text-gray-500">三甲综合医院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345679</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市卫健委</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1200</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">18</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">32</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 65%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openHospitalModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市妇女儿童医院</div>
                                <div class="text-sm text-gray-500">三甲专科医院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345680</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市卫健委</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">800</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">18</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openHospitalModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 医院编辑弹窗 -->
    <div id="hospitalModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">医院信息</h3>
                    <button onclick="closeModal('hospitalModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">医院名称 <span class="text-red-500">*</span></label>
                            <input type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入医院名称">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码 <span class="text-red-500">*</span></label>
                            <input type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">举办单位 <span class="text-red-500">*</span></label>
                            <select required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择举办单位</option>
                                <option value="gov">政府</option>
                                <option value="university">高校</option>
                                <option value="enterprise">企业</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">行政区划 <span class="text-red-500">*</span></label>
                            <select required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择行政区划</option>
                                <option value="haishu">海曙区</option>
                                <option value="jiangdong">江东区</option>
                                <option value="jiangbei">江北区</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">医院等级 <span class="text-red-500">*</span></label>
                            <select required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择医院等级</option>
                                <option value="3a">三甲</option>
                                <option value="3b">三乙</option>
                                <option value="2a">二甲</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">专科类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择专科类型</option>
                                <option value="general">综合医院</option>
                                <option value="tumor">肿瘤医院</option>
                                <option value="children">儿童医院</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">床位数</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入床位数">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">医院地址</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入医院地址">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">医院简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入医院简介"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('hospitalModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/2 bg-white shadow-xl transform transition-transform duration-300 ease-in-out translate-x-full">
        <div class="h-full overflow-y-auto p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">宁波市第一医院</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- 基本信息 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">基本信息</h4>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">编辑</button>
                    </div>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">统一社会信用代码：</span>
                            <span class="font-medium text-gray-900">123456789012345678</span>
                        </div>
                        <div>
                            <span class="text-gray-500">举办单位：</span>
                            <span class="font-medium text-gray-900">宁波市卫健委</span>
                        </div>
                        <div>
                            <span class="text-gray-500">行政区划：</span>
                            <span class="font-medium text-gray-900">海曙区</span>
                        </div>
                        <div>
                            <span class="text-gray-500">医院等级：</span>
                            <span class="font-medium text-gray-900">三甲</span>
                        </div>
                        <div>
                            <span class="text-gray-500">专科类型：</span>
                            <span class="font-medium text-gray-900">综合医院</span>
                        </div>
                        <div>
                            <span class="text-gray-500">床位数：</span>
                            <span class="font-medium text-gray-900">1500</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-500">医院地址：</span>
                            <span class="font-medium text-gray-900">宁波市海曙区柳汀街59号</span>
                        </div>
                        <div class="col-span-2">
                            <span class="text-gray-500">医院简介：</span>
                            <p class="font-medium text-gray-900">宁波市第一医院创建于1913年，是宁波市规模最大的集医疗、科研、教学、预防为一体的综合性三级甲等医院。</p>
                        </div>
                    </div>
                </div>
                
                <!-- 重点科室 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">重点科室 (12)</h4>
                        <button onclick="openRelationModal('department')" class="text-blue-600 hover:text-blue-800 text-sm">管理关联</button>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="bg-white p-2 rounded border border-gray-200">心血管内科</div>
                        <div class="bg-white p-2 rounded border border-gray-200">神经外科</div>
                        <div class="bg-white p-2 rounded border border-gray-200">消化内科</div>
                        <div class="bg-white p-2 rounded border border-gray-200">呼吸内科</div>
                        <div class="bg-white p-2 rounded border border-gray-200">骨科</div>
                        <div class="bg-white p-2 rounded border border-gray-200">肿瘤科</div>
                    </div>
                </div>
                
                <!-- 科创平台 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">科创平台 (5)</h4>
                        <button onclick="openRelationModal('platform')" class="text-blue-600 hover:text-blue-800 text-sm">管理关联</button>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="bg-white p-2 rounded border border-gray-200">宁波市心血管病研究所</div>
                        <div class="bg-white p-2 rounded border border-gray-200">宁波市肿瘤防治中心</div>
                        <div class="bg-white p-2 rounded border border-gray-200">宁波市医学影像中心</div>
                    </div>
                </div>
                
                <!-- 医疗设备 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">医疗设备 (23)</h4>
                        <button onclick="openRelationModal('equipment')" class="text-blue-600 hover:text-blue-800 text-sm">管理关联</button>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="bg-white p-2 rounded border border-gray-200">PET-CT</div>
                        <div class="bg-white p-2 rounded border border-gray-200">3.0T磁共振</div>
                        <div class="bg-white p-2 rounded border border-gray-200">256排CT</div>
                        <div class="bg-white p-2 rounded border border-gray-200">直线加速器</div>
                    </div>
                </div>
                
                <!-- 科研项目 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">在研项目 (23)</h4>
                        <button onclick="openRelationModal('project')" class="text-blue-600 hover:text-blue-800 text-sm">管理关联</button>
                    </div>
                    <div class="grid grid-cols-1 gap-2 text-sm">
                        <div class="bg-white p-2 rounded border border-gray-200">心血管疾病早期诊断标志物的研究</div>
                        <div class="bg-white p-2 rounded border border-gray-200">肿瘤免疫治疗新方法探索</div>
                    </div>
                </div>
                
                <!-- 科研成果 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-3">
                        <h4 class="text-lg font-medium text-gray-900">科研成果 (45)</h4>
                        <button onclick="openRelationModal('achievement')" class="text-blue-600 hover:text-blue-800 text-sm">管理关联</button>
                    </div>
                    <div class="grid grid-cols-1 gap-2 text-sm">
                        <div class="bg-white p-2 rounded border border-gray-200">一种新型心血管支架的研发与应用</div>
                        <div class="bg-white p-2 rounded border border-gray-200">肿瘤早期筛查试剂盒的开发</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联管理弹窗 -->
    <div id="relationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="relationModalTitle">管理关联</h3>
                    <button onclick="closeModal('relationModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4">
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="搜索...">
                </div>
                
                <div class="h-96 overflow-y-auto border border-gray-200 rounded-md">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">心血管内科</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点科室</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">神经外科</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点科室</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                            </tr>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市心血管病研究所</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科创平台</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('relationModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        保存关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function openHospitalModal(id) {
            if (id) {
                // 这里可以加载对应id的数据
                console.log('编辑医院ID:', id);
            }
            openModal('hospitalModal');
        }

        function openDetailDrawer() {
            const drawer = document.getElementById('detailDrawer');
            drawer.classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailDrawer() {
            const drawer = document.getElementById('detailDrawer');
            drawer.classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        function openRelationModal(type) {
            let title = '';
            switch(type) {
                case 'department':
                    title = '管理重点科室关联';
                    break;
                case 'platform':
                    title = '管理科创平台关联';
                    break;
                case 'equipment':
                    title = '管理医疗设备关联';
                    break;
                case 'project':
                    title = '管理科研项目关联';
                    break;
                case 'achievement':
                    title = '管理科研成果关联';
                    break;
            }
            document.getElementById('relationModalTitle').textContent = title;
            openModal('relationModal');
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (原型演示)');
                    if (form.closest('.modal-overlay')) {
                        closeModal(form.closest('.modal-overlay').id);
                    }
                });
            });
        });
    </script>
</body>
</html>