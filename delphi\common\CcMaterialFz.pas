unit CcMaterialFz;

interface

uses
  Classes;

type
  TCcMaterialFz = class
  private

    FMaterialFzid: Integer;
    FMaterialid: Integer;
    FDdid: Integer;
    FFzTypeNum: Integer;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property MaterialFzid: Integer read FMaterialFzid write FMaterialFzid;
    property Materialid: Integer read FMaterialid write FMaterialid;
    property Ddid: Integer read FDdid write FDdid;
    property FzTypeNum: Integer read FFzTypeNum write FFzTypeNum;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
