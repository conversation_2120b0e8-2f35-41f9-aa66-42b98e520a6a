unit CcSjCjgjdj;

interface

uses
  Classes;

type
  TCcSjCjgjdj = class
  private

    FSjgjdjid: Integer;
    FDdid: Integer;
    FKsid: Integer;
    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FScs: Integer;
    FJss: Integer;
    FZje: double;
    FScState: string;
    FJsState: string;

    FXlhj_1: double;
    FXlhj_2: double;
    FXlhj_3: double;
    FZhj: double;
    FXs: double;
    FZgj: double;

    FWfxldw_1: string;
    FWfxldw_2: string;
    FWfxldw_3: string;
    FWfxlhj_1: double;
    FWfxlhj_2: double;
    FWfxlhj_3: double;
    FWfzhj: double;

    Fa_Xl_1: double;
    Fa_Xl_2: double;
    Fa_Xl_3: double;
    Fa_Xl_4: double;
    Fa_Xl_5: double;
    Fa_Xl_6: double;
    Fa_Xl_7: double;
    Fa_Xl_8: double;
    Fa_Xl_9: double;
    Fa_Xl_10: double;
    Fa_Xl_11: double;
    Fa_Xl_12: double;
    Fa_Xl_13: double;
    Fa_Xl_14: double;
    Fa_Xl_15: double;
    Fa_Xl_16: double;
    Fa_Xl_17: double;
    Fa_Xl_18: double;
    Fa_Xl_19: double;
    Fa_Xl_20: double;
    Fa_Xl_21: double;
    Fa_Xl_22: double;
    Fa_Xl_23: double;
    Fa_Xl_24: double;
    Fa_Xl_25: double;
    Fa_Xl_26: double;
    Fa_Xl_27: double;
    Fa_Xl_28: double;
    Fa_Xl_29: double;
    Fa_Xl_30: double;
    Fa_Xl_31: double;
    Fa_Xl_32: double;
    Fa_Xl_33: double;
    Fa_Xl_34: double;
    Fa_Xl_35: double;
    Fa_Xl_36: double;
    Fa_Xl_37: double;
    Fa_Xl_38: double;
    Fa_Xl_39: double;
    Fa_Xl_40: double;
    Fa_Xl_41: double;
    Fa_Xl_42: double;
    Fa_Xl_43: double;
    Fa_Xl_44: double;
    Fa_Xl_45: double;
    Fa_Xl_46: double;
    Fa_Xl_47: double;
    Fa_Xl_48: double;
    Fa_Xl_49: double;
    Fa_Xl_50: double;
    Fb_Xl_1: double;
    Fb_Xl_2: double;
    Fb_Xl_3: double;
    Fb_Xl_4: double;
    Fb_Xl_5: double;
    Fb_Xl_6: double;
    Fb_Xl_7: double;
    Fb_Xl_8: double;
    Fb_Xl_9: double;
    Fb_Xl_10: double;
    Fb_Xl_11: double;
    Fb_Xl_12: double;
    Fb_Xl_13: double;
    Fb_Xl_14: double;
    Fb_Xl_15: double;
    Fb_Xl_16: double;
    Fb_Xl_17: double;
    Fb_Xl_18: double;
    Fb_Xl_19: double;
    Fb_Xl_20: double;
    Fb_Xl_21: double;
    Fb_Xl_22: double;
    Fb_Xl_23: double;
    Fb_Xl_24: double;
    Fb_Xl_25: double;
    Fb_Xl_26: double;
    Fb_Xl_27: double;
    Fb_Xl_28: double;
    Fb_Xl_29: double;
    Fb_Xl_30: double;
    Fb_Xl_31: double;
    Fb_Xl_32: double;
    Fb_Xl_33: double;
    Fb_Xl_34: double;
    Fb_Xl_35: double;
    Fb_Xl_36: double;
    Fb_Xl_37: double;
    Fb_Xl_38: double;
    Fb_Xl_39: double;
    Fb_Xl_40: double;
    Fb_Xl_41: double;
    Fb_Xl_42: double;
    Fb_Xl_43: double;
    Fb_Xl_44: double;
    Fb_Xl_45: double;
    Fb_Xl_46: double;
    Fb_Xl_47: double;
    Fb_Xl_48: double;
    Fb_Xl_49: double;
    Fb_Xl_50: double;
    Fc_Xl_1: double;
    Fc_Xl_2: double;
    Fc_Xl_3: double;
    Fc_Xl_4: double;
    Fc_Xl_5: double;
    Fc_Xl_6: double;
    Fc_Xl_7: double;
    Fc_Xl_8: double;
    Fc_Xl_9: double;
    Fc_Xl_10: double;
    Fc_Xl_11: double;
    Fc_Xl_12: double;
    Fc_Xl_13: double;
    Fc_Xl_14: double;
    Fc_Xl_15: double;
    Fc_Xl_16: double;
    Fc_Xl_17: double;
    Fc_Xl_18: double;
    Fc_Xl_19: double;
    Fc_Xl_20: double;
    Fc_Xl_21: double;
    Fc_Xl_22: double;
    Fc_Xl_23: double;
    Fc_Xl_24: double;
    Fc_Xl_25: double;
    Fc_Xl_26: double;
    Fc_Xl_27: double;
    Fc_Xl_28: double;
    Fc_Xl_29: double;
    Fc_Xl_30: double;
    Fc_Xl_31: double;
    Fc_Xl_32: double;
    Fc_Xl_33: double;
    Fc_Xl_34: double;
    Fc_Xl_35: double;
    Fc_Xl_36: double;
    Fc_Xl_37: double;
    Fc_Xl_38: double;
    Fc_Xl_39: double;
    Fc_Xl_40: double;
    Fc_Xl_41: double;
    Fc_Xl_42: double;
    Fc_Xl_43: double;
    Fc_Xl_44: double;
    Fc_Xl_45: double;
    Fc_Xl_46: double;
    Fc_Xl_47: double;
    Fc_Xl_48: double;
    Fc_Xl_49: double;
    Fc_Xl_50: double;
    Fd_Xl_1: double;
    Fd_Xl_2: double;
    Fd_Xl_3: double;
    Fd_Xl_4: double;
    Fd_Xl_5: double;
    Fd_Xl_6: double;
    Fd_Xl_7: double;
    Fd_Xl_8: double;
    Fd_Xl_9: double;
    Fd_Xl_10: double;
    Fd_Xl_11: double;
    Fd_Xl_12: double;
    Fd_Xl_13: double;
    Fd_Xl_14: double;
    Fd_Xl_15: double;
    Fd_Xl_16: double;
    Fd_Xl_17: double;
    Fd_Xl_18: double;
    Fd_Xl_19: double;
    Fd_Xl_20: double;
    Fd_Xl_21: double;
    Fd_Xl_22: double;
    Fd_Xl_23: double;
    Fd_Xl_24: double;
    Fd_Xl_25: double;
    Fd_Xl_26: double;
    Fd_Xl_27: double;
    Fd_Xl_28: double;
    Fd_Xl_29: double;
    Fd_Xl_30: double;
    Fd_Xl_31: double;
    Fd_Xl_32: double;
    Fd_Xl_33: double;
    Fd_Xl_34: double;
    Fd_Xl_35: double;
    Fd_Xl_36: double;
    Fd_Xl_37: double;
    Fd_Xl_38: double;
    Fd_Xl_39: double;
    Fd_Xl_40: double;
    Fd_Xl_41: double;
    Fd_Xl_42: double;
    Fd_Xl_43: double;
    Fd_Xl_44: double;
    Fd_Xl_45: double;
    Fd_Xl_46: double;
    Fd_Xl_47: double;
    Fd_Xl_48: double;
    Fd_Xl_49: double;
    Fd_Xl_50: double;
    Fe_Xl_1: double;
    Fe_Xl_2: double;
    Fe_Xl_3: double;
    Fe_Xl_4: double;
    Fe_Xl_5: double;
    Fe_Xl_6: double;
    Fe_Xl_7: double;
    Fe_Xl_8: double;
    Fe_Xl_9: double;
    Fe_Xl_10: double;
    Fe_Xl_11: double;
    Fe_Xl_12: double;
    Fe_Xl_13: double;
    Fe_Xl_14: double;
    Fe_Xl_15: double;
    Fe_Xl_16: double;
    Fe_Xl_17: double;
    Fe_Xl_18: double;
    Fe_Xl_19: double;
    Fe_Xl_20: double;
    Ff_Xl_1: double;
    Ff_Xl_2: double;
    Ff_Xl_3: double;
    Ff_Xl_4: double;
    Ff_Xl_5: double;
    Ff_Xl_6: double;
    Ff_Xl_7: double;
    Ff_Xl_8: double;
    Ff_Xl_9: double;
    Ff_Xl_10: double;
    Ff_Xl_11: double;
    Ff_Xl_12: double;
    Ff_Xl_13: double;
    Ff_Xl_14: double;
    Ff_Xl_15: double;
    Ff_Xl_16: double;
    Ff_Xl_17: double;
    Ff_Xl_18: double;
    Ff_Xl_19: double;
    Ff_Xl_20: double;
    Fg_Xl_1: double;
    Fg_Xl_2: double;
    Fg_Xl_3: double;
    Fg_Xl_4: double;
    Fg_Xl_5: double;
    Fg_Xl_6: double;
    Fg_Xl_7: double;
    Fg_Xl_8: double;
    Fg_Xl_9: double;
    Fg_Xl_10: double;
    Fg_Xl_11: double;
    Fg_Xl_12: double;
    Fg_Xl_13: double;
    Fg_Xl_14: double;
    Fg_Xl_15: double;
    Fg_Xl_16: double;
    Fg_Xl_17: double;
    Fg_Xl_18: double;
    Fg_Xl_19: double;
    Fg_Xl_20: double;
    Fh_Xl_1: double;
    Fh_Xl_2: double;
    Fh_Xl_3: double;
    Fh_Xl_4: double;
    Fh_Xl_5: double;
    Fh_Xl_6: double;
    Fh_Xl_7: double;
    Fh_Xl_8: double;
    Fh_Xl_9: double;
    Fh_Xl_10: double;
    Fh_Xl_11: double;
    Fh_Xl_12: double;
    Fh_Xl_13: double;
    Fh_Xl_14: double;
    Fh_Xl_15: double;
    Fh_Xl_16: double;
    Fh_Xl_17: double;
    Fh_Xl_18: double;
    Fh_Xl_19: double;
    Fh_Xl_20: double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjgjdjid: Integer read FSjgjdjid write FSjgjdjid;
    property Ddid: Integer read FDdid write FDdid;
    property Ksid: Integer read FKsid write FKsid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Scs: Integer read FScs write FScs;
    property Jss: Integer read FJss write FJss;
    property Zje: double read FZje write FZje;
    property ScState: string read FScState write FScState;
    property JsState: string read FJsState write FJsState;

    property Xlhj_1: double read FXlhj_1 write FXlhj_1;
    property Xlhj_2: double read FXlhj_2 write FXlhj_2;
    property Xlhj_3: double read FXlhj_3 write FXlhj_3;
    property Zhj: double read FZhj write FZhj;
    property Xs: double read FXs write FXs;
    property Zgj: double read FZgj write FZgj;

    property Wfxldw_1: string read FWfxldw_1 write FWfxldw_1;
    property Wfxldw_2: string read FWfxldw_2 write FWfxldw_2;
    property Wfxldw_3: string read FWfxldw_3 write FWfxldw_3;
    property Wfxlhj_1: double read FWfxlhj_1 write FWfxlhj_1;
    property Wfxlhj_2: double read FWfxlhj_2 write FWfxlhj_2;
    property Wfxlhj_3: double read FWfxlhj_3 write FWfxlhj_3;
    property Wfzhj: double read FWfzhj write FWfzhj;

    property a_Xl_1: double read Fa_Xl_1 write Fa_Xl_1;
    property a_Xl_2: double read Fa_Xl_2 write Fa_Xl_2;
    property a_Xl_3: double read Fa_Xl_3 write Fa_Xl_3;
    property a_Xl_4: double read Fa_Xl_4 write Fa_Xl_4;
    property a_Xl_5: double read Fa_Xl_5 write Fa_Xl_5;
    property a_Xl_6: double read Fa_Xl_6 write Fa_Xl_6;
    property a_Xl_7: double read Fa_Xl_7 write Fa_Xl_7;
    property a_Xl_8: double read Fa_Xl_8 write Fa_Xl_8;
    property a_Xl_9: double read Fa_Xl_9 write Fa_Xl_9;
    property a_Xl_10: double read Fa_Xl_10 write Fa_Xl_10;
    property a_Xl_11: double read Fa_Xl_11 write Fa_Xl_11;
    property a_Xl_12: double read Fa_Xl_12 write Fa_Xl_12;
    property a_Xl_13: double read Fa_Xl_13 write Fa_Xl_13;
    property a_Xl_14: double read Fa_Xl_14 write Fa_Xl_14;
    property a_Xl_15: double read Fa_Xl_15 write Fa_Xl_15;
    property a_Xl_16: double read Fa_Xl_16 write Fa_Xl_16;
    property a_Xl_17: double read Fa_Xl_17 write Fa_Xl_17;
    property a_Xl_18: double read Fa_Xl_18 write Fa_Xl_18;
    property a_Xl_19: double read Fa_Xl_19 write Fa_Xl_19;
    property a_Xl_20: double read Fa_Xl_20 write Fa_Xl_20;
    property a_Xl_21: double read Fa_Xl_21 write Fa_Xl_21;
    property a_Xl_22: double read Fa_Xl_22 write Fa_Xl_22;
    property a_Xl_23: double read Fa_Xl_23 write Fa_Xl_23;
    property a_Xl_24: double read Fa_Xl_24 write Fa_Xl_24;
    property a_Xl_25: double read Fa_Xl_25 write Fa_Xl_25;
    property a_Xl_26: double read Fa_Xl_26 write Fa_Xl_26;
    property a_Xl_27: double read Fa_Xl_27 write Fa_Xl_27;
    property a_Xl_28: double read Fa_Xl_28 write Fa_Xl_28;
    property a_Xl_29: double read Fa_Xl_29 write Fa_Xl_29;
    property a_Xl_30: double read Fa_Xl_30 write Fa_Xl_30;
    property a_Xl_31: double read Fa_Xl_31 write Fa_Xl_31;
    property a_Xl_32: double read Fa_Xl_32 write Fa_Xl_32;
    property a_Xl_33: double read Fa_Xl_33 write Fa_Xl_33;
    property a_Xl_34: double read Fa_Xl_34 write Fa_Xl_34;
    property a_Xl_35: double read Fa_Xl_35 write Fa_Xl_35;
    property a_Xl_36: double read Fa_Xl_36 write Fa_Xl_36;
    property a_Xl_37: double read Fa_Xl_37 write Fa_Xl_37;
    property a_Xl_38: double read Fa_Xl_38 write Fa_Xl_38;
    property a_Xl_39: double read Fa_Xl_39 write Fa_Xl_39;
    property a_Xl_40: double read Fa_Xl_40 write Fa_Xl_40;
    property a_Xl_41: double read Fa_Xl_41 write Fa_Xl_41;
    property a_Xl_42: double read Fa_Xl_42 write Fa_Xl_42;
    property a_Xl_43: double read Fa_Xl_43 write Fa_Xl_43;
    property a_Xl_44: double read Fa_Xl_44 write Fa_Xl_44;
    property a_Xl_45: double read Fa_Xl_45 write Fa_Xl_45;
    property a_Xl_46: double read Fa_Xl_46 write Fa_Xl_46;
    property a_Xl_47: double read Fa_Xl_47 write Fa_Xl_47;
    property a_Xl_48: double read Fa_Xl_48 write Fa_Xl_48;
    property a_Xl_49: double read Fa_Xl_49 write Fa_Xl_49;
    property a_Xl_50: double read Fa_Xl_50 write Fa_Xl_50;
    property b_Xl_1: double read Fb_Xl_1 write Fb_Xl_1;
    property b_Xl_2: double read Fb_Xl_2 write Fb_Xl_2;
    property b_Xl_3: double read Fb_Xl_3 write Fb_Xl_3;
    property b_Xl_4: double read Fb_Xl_4 write Fb_Xl_4;
    property b_Xl_5: double read Fb_Xl_5 write Fb_Xl_5;
    property b_Xl_6: double read Fb_Xl_6 write Fb_Xl_6;
    property b_Xl_7: double read Fb_Xl_7 write Fb_Xl_7;
    property b_Xl_8: double read Fb_Xl_8 write Fb_Xl_8;
    property b_Xl_9: double read Fb_Xl_9 write Fb_Xl_9;
    property b_Xl_10: double read Fb_Xl_10 write Fb_Xl_10;
    property b_Xl_11: double read Fb_Xl_11 write Fb_Xl_11;
    property b_Xl_12: double read Fb_Xl_12 write Fb_Xl_12;
    property b_Xl_13: double read Fb_Xl_13 write Fb_Xl_13;
    property b_Xl_14: double read Fb_Xl_14 write Fb_Xl_14;
    property b_Xl_15: double read Fb_Xl_15 write Fb_Xl_15;
    property b_Xl_16: double read Fb_Xl_16 write Fb_Xl_16;
    property b_Xl_17: double read Fb_Xl_17 write Fb_Xl_17;
    property b_Xl_18: double read Fb_Xl_18 write Fb_Xl_18;
    property b_Xl_19: double read Fb_Xl_19 write Fb_Xl_19;
    property b_Xl_20: double read Fb_Xl_20 write Fb_Xl_20;
    property b_Xl_21: double read Fb_Xl_21 write Fb_Xl_21;
    property b_Xl_22: double read Fb_Xl_22 write Fb_Xl_22;
    property b_Xl_23: double read Fb_Xl_23 write Fb_Xl_23;
    property b_Xl_24: double read Fb_Xl_24 write Fb_Xl_24;
    property b_Xl_25: double read Fb_Xl_25 write Fb_Xl_25;
    property b_Xl_26: double read Fb_Xl_26 write Fb_Xl_26;
    property b_Xl_27: double read Fb_Xl_27 write Fb_Xl_27;
    property b_Xl_28: double read Fb_Xl_28 write Fb_Xl_28;
    property b_Xl_29: double read Fb_Xl_29 write Fb_Xl_29;
    property b_Xl_30: double read Fb_Xl_30 write Fb_Xl_30;
    property b_Xl_31: double read Fb_Xl_31 write Fb_Xl_31;
    property b_Xl_32: double read Fb_Xl_32 write Fb_Xl_32;
    property b_Xl_33: double read Fb_Xl_33 write Fb_Xl_33;
    property b_Xl_34: double read Fb_Xl_34 write Fb_Xl_34;
    property b_Xl_35: double read Fb_Xl_35 write Fb_Xl_35;
    property b_Xl_36: double read Fb_Xl_36 write Fb_Xl_36;
    property b_Xl_37: double read Fb_Xl_37 write Fb_Xl_37;
    property b_Xl_38: double read Fb_Xl_38 write Fb_Xl_38;
    property b_Xl_39: double read Fb_Xl_39 write Fb_Xl_39;
    property b_Xl_40: double read Fb_Xl_40 write Fb_Xl_40;
    property b_Xl_41: double read Fb_Xl_41 write Fb_Xl_41;
    property b_Xl_42: double read Fb_Xl_42 write Fb_Xl_42;
    property b_Xl_43: double read Fb_Xl_43 write Fb_Xl_43;
    property b_Xl_44: double read Fb_Xl_44 write Fb_Xl_44;
    property b_Xl_45: double read Fb_Xl_45 write Fb_Xl_45;
    property b_Xl_46: double read Fb_Xl_46 write Fb_Xl_46;
    property b_Xl_47: double read Fb_Xl_47 write Fb_Xl_47;
    property b_Xl_48: double read Fb_Xl_48 write Fb_Xl_48;
    property b_Xl_49: double read Fb_Xl_49 write Fb_Xl_49;
    property b_Xl_50: double read Fb_Xl_50 write Fb_Xl_50;
    property c_Xl_1: double read Fc_Xl_1 write Fc_Xl_1;
    property c_Xl_2: double read Fc_Xl_2 write Fc_Xl_2;
    property c_Xl_3: double read Fc_Xl_3 write Fc_Xl_3;
    property c_Xl_4: double read Fc_Xl_4 write Fc_Xl_4;
    property c_Xl_5: double read Fc_Xl_5 write Fc_Xl_5;
    property c_Xl_6: double read Fc_Xl_6 write Fc_Xl_6;
    property c_Xl_7: double read Fc_Xl_7 write Fc_Xl_7;
    property c_Xl_8: double read Fc_Xl_8 write Fc_Xl_8;
    property c_Xl_9: double read Fc_Xl_9 write Fc_Xl_9;
    property c_Xl_10: double read Fc_Xl_10 write Fc_Xl_10;
    property c_Xl_11: double read Fc_Xl_11 write Fc_Xl_11;
    property c_Xl_12: double read Fc_Xl_12 write Fc_Xl_12;
    property c_Xl_13: double read Fc_Xl_13 write Fc_Xl_13;
    property c_Xl_14: double read Fc_Xl_14 write Fc_Xl_14;
    property c_Xl_15: double read Fc_Xl_15 write Fc_Xl_15;
    property c_Xl_16: double read Fc_Xl_16 write Fc_Xl_16;
    property c_Xl_17: double read Fc_Xl_17 write Fc_Xl_17;
    property c_Xl_18: double read Fc_Xl_18 write Fc_Xl_18;
    property c_Xl_19: double read Fc_Xl_19 write Fc_Xl_19;
    property c_Xl_20: double read Fc_Xl_20 write Fc_Xl_20;
    property c_Xl_21: double read Fc_Xl_21 write Fc_Xl_21;
    property c_Xl_22: double read Fc_Xl_22 write Fc_Xl_22;
    property c_Xl_23: double read Fc_Xl_23 write Fc_Xl_23;
    property c_Xl_24: double read Fc_Xl_24 write Fc_Xl_24;
    property c_Xl_25: double read Fc_Xl_25 write Fc_Xl_25;
    property c_Xl_26: double read Fc_Xl_26 write Fc_Xl_26;
    property c_Xl_27: double read Fc_Xl_27 write Fc_Xl_27;
    property c_Xl_28: double read Fc_Xl_28 write Fc_Xl_28;
    property c_Xl_29: double read Fc_Xl_29 write Fc_Xl_29;
    property c_Xl_30: double read Fc_Xl_30 write Fc_Xl_30;
    property c_Xl_31: double read Fc_Xl_31 write Fc_Xl_31;
    property c_Xl_32: double read Fc_Xl_32 write Fc_Xl_32;
    property c_Xl_33: double read Fc_Xl_33 write Fc_Xl_33;
    property c_Xl_34: double read Fc_Xl_34 write Fc_Xl_34;
    property c_Xl_35: double read Fc_Xl_35 write Fc_Xl_35;
    property c_Xl_36: double read Fc_Xl_36 write Fc_Xl_36;
    property c_Xl_37: double read Fc_Xl_37 write Fc_Xl_37;
    property c_Xl_38: double read Fc_Xl_38 write Fc_Xl_38;
    property c_Xl_39: double read Fc_Xl_39 write Fc_Xl_39;
    property c_Xl_40: double read Fc_Xl_40 write Fc_Xl_40;
    property c_Xl_41: double read Fc_Xl_41 write Fc_Xl_41;
    property c_Xl_42: double read Fc_Xl_42 write Fc_Xl_42;
    property c_Xl_43: double read Fc_Xl_43 write Fc_Xl_43;
    property c_Xl_44: double read Fc_Xl_44 write Fc_Xl_44;
    property c_Xl_45: double read Fc_Xl_45 write Fc_Xl_45;
    property c_Xl_46: double read Fc_Xl_46 write Fc_Xl_46;
    property c_Xl_47: double read Fc_Xl_47 write Fc_Xl_47;
    property c_Xl_48: double read Fc_Xl_48 write Fc_Xl_48;
    property c_Xl_49: double read Fc_Xl_49 write Fc_Xl_49;
    property c_Xl_50: double read Fc_Xl_50 write Fc_Xl_50;
    property d_Xl_1: double read Fd_Xl_1 write Fd_Xl_1;
    property d_Xl_2: double read Fd_Xl_2 write Fd_Xl_2;
    property d_Xl_3: double read Fd_Xl_3 write Fd_Xl_3;
    property d_Xl_4: double read Fd_Xl_4 write Fd_Xl_4;
    property d_Xl_5: double read Fd_Xl_5 write Fd_Xl_5;
    property d_Xl_6: double read Fd_Xl_6 write Fd_Xl_6;
    property d_Xl_7: double read Fd_Xl_7 write Fd_Xl_7;
    property d_Xl_8: double read Fd_Xl_8 write Fd_Xl_8;
    property d_Xl_9: double read Fd_Xl_9 write Fd_Xl_9;
    property d_Xl_10: double read Fd_Xl_10 write Fd_Xl_10;
    property d_Xl_11: double read Fd_Xl_11 write Fd_Xl_11;
    property d_Xl_12: double read Fd_Xl_12 write Fd_Xl_12;
    property d_Xl_13: double read Fd_Xl_13 write Fd_Xl_13;
    property d_Xl_14: double read Fd_Xl_14 write Fd_Xl_14;
    property d_Xl_15: double read Fd_Xl_15 write Fd_Xl_15;
    property d_Xl_16: double read Fd_Xl_16 write Fd_Xl_16;
    property d_Xl_17: double read Fd_Xl_17 write Fd_Xl_17;
    property d_Xl_18: double read Fd_Xl_18 write Fd_Xl_18;
    property d_Xl_19: double read Fd_Xl_19 write Fd_Xl_19;
    property d_Xl_20: double read Fd_Xl_20 write Fd_Xl_20;
    property d_Xl_21: double read Fd_Xl_21 write Fd_Xl_21;
    property d_Xl_22: double read Fd_Xl_22 write Fd_Xl_22;
    property d_Xl_23: double read Fd_Xl_23 write Fd_Xl_23;
    property d_Xl_24: double read Fd_Xl_24 write Fd_Xl_24;
    property d_Xl_25: double read Fd_Xl_25 write Fd_Xl_25;
    property d_Xl_26: double read Fd_Xl_26 write Fd_Xl_26;
    property d_Xl_27: double read Fd_Xl_27 write Fd_Xl_27;
    property d_Xl_28: double read Fd_Xl_28 write Fd_Xl_28;
    property d_Xl_29: double read Fd_Xl_29 write Fd_Xl_29;
    property d_Xl_30: double read Fd_Xl_30 write Fd_Xl_30;
    property d_Xl_31: double read Fd_Xl_31 write Fd_Xl_31;
    property d_Xl_32: double read Fd_Xl_32 write Fd_Xl_32;
    property d_Xl_33: double read Fd_Xl_33 write Fd_Xl_33;
    property d_Xl_34: double read Fd_Xl_34 write Fd_Xl_34;
    property d_Xl_35: double read Fd_Xl_35 write Fd_Xl_35;
    property d_Xl_36: double read Fd_Xl_36 write Fd_Xl_36;
    property d_Xl_37: double read Fd_Xl_37 write Fd_Xl_37;
    property d_Xl_38: double read Fd_Xl_38 write Fd_Xl_38;
    property d_Xl_39: double read Fd_Xl_39 write Fd_Xl_39;
    property d_Xl_40: double read Fd_Xl_40 write Fd_Xl_40;
    property d_Xl_41: double read Fd_Xl_41 write Fd_Xl_41;
    property d_Xl_42: double read Fd_Xl_42 write Fd_Xl_42;
    property d_Xl_43: double read Fd_Xl_43 write Fd_Xl_43;
    property d_Xl_44: double read Fd_Xl_44 write Fd_Xl_44;
    property d_Xl_45: double read Fd_Xl_45 write Fd_Xl_45;
    property d_Xl_46: double read Fd_Xl_46 write Fd_Xl_46;
    property d_Xl_47: double read Fd_Xl_47 write Fd_Xl_47;
    property d_Xl_48: double read Fd_Xl_48 write Fd_Xl_48;
    property d_Xl_49: double read Fd_Xl_49 write Fd_Xl_49;
    property d_Xl_50: double read Fd_Xl_50 write Fd_Xl_50;
    property e_Xl_1: double read Fe_Xl_1 write Fe_Xl_1;
    property e_Xl_2: double read Fe_Xl_2 write Fe_Xl_2;
    property e_Xl_3: double read Fe_Xl_3 write Fe_Xl_3;
    property e_Xl_4: double read Fe_Xl_4 write Fe_Xl_4;
    property e_Xl_5: double read Fe_Xl_5 write Fe_Xl_5;
    property e_Xl_6: double read Fe_Xl_6 write Fe_Xl_6;
    property e_Xl_7: double read Fe_Xl_7 write Fe_Xl_7;
    property e_Xl_8: double read Fe_Xl_8 write Fe_Xl_8;
    property e_Xl_9: double read Fe_Xl_9 write Fe_Xl_9;
    property e_Xl_10: double read Fe_Xl_10 write Fe_Xl_10;
    property e_Xl_11: double read Fe_Xl_11 write Fe_Xl_11;
    property e_Xl_12: double read Fe_Xl_12 write Fe_Xl_12;
    property e_Xl_13: double read Fe_Xl_13 write Fe_Xl_13;
    property e_Xl_14: double read Fe_Xl_14 write Fe_Xl_14;
    property e_Xl_15: double read Fe_Xl_15 write Fe_Xl_15;
    property e_Xl_16: double read Fe_Xl_16 write Fe_Xl_16;
    property e_Xl_17: double read Fe_Xl_17 write Fe_Xl_17;
    property e_Xl_18: double read Fe_Xl_18 write Fe_Xl_18;
    property e_Xl_19: double read Fe_Xl_19 write Fe_Xl_19;
    property e_Xl_20: double read Fe_Xl_20 write Fe_Xl_20;
    property f_Xl_1: double read Ff_Xl_1 write Ff_Xl_1;
    property f_Xl_2: double read Ff_Xl_2 write Ff_Xl_2;
    property f_Xl_3: double read Ff_Xl_3 write Ff_Xl_3;
    property f_Xl_4: double read Ff_Xl_4 write Ff_Xl_4;
    property f_Xl_5: double read Ff_Xl_5 write Ff_Xl_5;
    property f_Xl_6: double read Ff_Xl_6 write Ff_Xl_6;
    property f_Xl_7: double read Ff_Xl_7 write Ff_Xl_7;
    property f_Xl_8: double read Ff_Xl_8 write Ff_Xl_8;
    property f_Xl_9: double read Ff_Xl_9 write Ff_Xl_9;
    property f_Xl_10: double read Ff_Xl_10 write Ff_Xl_10;
    property f_Xl_11: double read Ff_Xl_11 write Ff_Xl_11;
    property f_Xl_12: double read Ff_Xl_12 write Ff_Xl_12;
    property f_Xl_13: double read Ff_Xl_13 write Ff_Xl_13;
    property f_Xl_14: double read Ff_Xl_14 write Ff_Xl_14;
    property f_Xl_15: double read Ff_Xl_15 write Ff_Xl_15;
    property f_Xl_16: double read Ff_Xl_16 write Ff_Xl_16;
    property f_Xl_17: double read Ff_Xl_17 write Ff_Xl_17;
    property f_Xl_18: double read Ff_Xl_18 write Ff_Xl_18;
    property f_Xl_19: double read Ff_Xl_19 write Ff_Xl_19;
    property f_Xl_20: double read Ff_Xl_20 write Ff_Xl_20;
    property g_Xl_1: double read Fg_Xl_1 write Fg_Xl_1;
    property g_Xl_2: double read Fg_Xl_2 write Fg_Xl_2;
    property g_Xl_3: double read Fg_Xl_3 write Fg_Xl_3;
    property g_Xl_4: double read Fg_Xl_4 write Fg_Xl_4;
    property g_Xl_5: double read Fg_Xl_5 write Fg_Xl_5;
    property g_Xl_6: double read Fg_Xl_6 write Fg_Xl_6;
    property g_Xl_7: double read Fg_Xl_7 write Fg_Xl_7;
    property g_Xl_8: double read Fg_Xl_8 write Fg_Xl_8;
    property g_Xl_9: double read Fg_Xl_9 write Fg_Xl_9;
    property g_Xl_10: double read Fg_Xl_10 write Fg_Xl_10;
    property g_Xl_11: double read Fg_Xl_11 write Fg_Xl_11;
    property g_Xl_12: double read Fg_Xl_12 write Fg_Xl_12;
    property g_Xl_13: double read Fg_Xl_13 write Fg_Xl_13;
    property g_Xl_14: double read Fg_Xl_14 write Fg_Xl_14;
    property g_Xl_15: double read Fg_Xl_15 write Fg_Xl_15;
    property g_Xl_16: double read Fg_Xl_16 write Fg_Xl_16;
    property g_Xl_17: double read Fg_Xl_17 write Fg_Xl_17;
    property g_Xl_18: double read Fg_Xl_18 write Fg_Xl_18;
    property g_Xl_19: double read Fg_Xl_19 write Fg_Xl_19;
    property g_Xl_20: double read Fg_Xl_20 write Fg_Xl_20;
    property h_Xl_1: double read Fh_Xl_1 write Fh_Xl_1;
    property h_Xl_2: double read Fh_Xl_2 write Fh_Xl_2;
    property h_Xl_3: double read Fh_Xl_3 write Fh_Xl_3;
    property h_Xl_4: double read Fh_Xl_4 write Fh_Xl_4;
    property h_Xl_5: double read Fh_Xl_5 write Fh_Xl_5;
    property h_Xl_6: double read Fh_Xl_6 write Fh_Xl_6;
    property h_Xl_7: double read Fh_Xl_7 write Fh_Xl_7;
    property h_Xl_8: double read Fh_Xl_8 write Fh_Xl_8;
    property h_Xl_9: double read Fh_Xl_9 write Fh_Xl_9;
    property h_Xl_10: double read Fh_Xl_10 write Fh_Xl_10;
    property h_Xl_11: double read Fh_Xl_11 write Fh_Xl_11;
    property h_Xl_12: double read Fh_Xl_12 write Fh_Xl_12;
    property h_Xl_13: double read Fh_Xl_13 write Fh_Xl_13;
    property h_Xl_14: double read Fh_Xl_14 write Fh_Xl_14;
    property h_Xl_15: double read Fh_Xl_15 write Fh_Xl_15;
    property h_Xl_16: double read Fh_Xl_16 write Fh_Xl_16;
    property h_Xl_17: double read Fh_Xl_17 write Fh_Xl_17;
    property h_Xl_18: double read Fh_Xl_18 write Fh_Xl_18;
    property h_Xl_19: double read Fh_Xl_19 write Fh_Xl_19;
    property h_Xl_20: double read Fh_Xl_20 write Fh_Xl_20;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
