unit CunK;

interface

uses
  Classes;

type
  TCunK = class
  private
    FCunKid: integer;
    FCunKname: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property CunKid: integer read FCunKid write FCunKid;
    property CunKname: string read FCunKname write <PERSON>unKname;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
