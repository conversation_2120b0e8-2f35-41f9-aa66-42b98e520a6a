<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策推送条目管理流程</text>

  <!-- 阶段一：推送准备 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：推送准备与记录生成</text>
  
  <!-- 节点1: 创建选择条目 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">创建/选择政策条目</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">管理人员在推送列表区操作</text>
  </g>

  <!-- 节点2: 提取主体集合 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">提取符合条件主体</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">从高匹配度主体池中提取</text>
  </g>

  <!-- 节点3: 生成推送记录 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成推送记录</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态初始为"待推送"</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：推送执行 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：推送执行与状态管理</text>

  <!-- 节点4: 触发推送 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">触发推送</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">排期时间或手动触发</text>
  </g>

  <!-- 节点5: 推送服务 -->
  <g transform="translate(350, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">调用推送服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">向目标主体下发消息</text>
  </g>

  <!-- 节点6: 记录结果 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时记录结果</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">成功/失败状态更新</text>
  </g>

  <!-- 节点7: 重试队列 -->
  <g transform="translate(850, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">失败重试队列</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">失败条目进入重试</text>
  </g>

  <!-- 连接线 推送准备 -> 触发推送 -->
  <path d="M 860 200 C 860 250, 200 280, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 300 355 Q 325 355 350 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 550 355 Q 575 355 600 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 (失败分支) -->
  <path d="M 800 355 Q 825 355 850 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="825" y="345" text-anchor="middle" font-size="12" fill="#555">失败条目</text>

  <!-- 阶段三：状态更新与统计 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：状态更新与统计分析</text>

  <!-- 节点8: 统计分析 -->
  <g transform="translate(250, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计成功失败</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">统计次数与失败原因</text>
  </g>

  <!-- 节点9: 状态更新 -->
  <g transform="translate(550, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新推送状态</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">已完成/部分失败</text>
  </g>

  <!-- 节点10: 监控指标 -->
  <g transform="translate(850, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入监控指标</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">汇总数据记录</text>
  </g>

  <!-- 连接线 推送完成 -> 统计 -->
  <path d="M 700 390 C 700 450, 360 490, 360 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 470 555 Q 510 555 550 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 770 555 Q 810 555 850 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：取消处理与归档 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：取消处理与数据归档</text>

  <!-- 节点11: 取消推送 -->
  <g transform="translate(150, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">取消推送</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">停止未执行任务</text>
  </g>

  <!-- 节点12: 取消日志 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成取消日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">供审计使用</text>
  </g>

  <!-- 节点13: 数据归档 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时数据归档</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">推送日志与指标归档至报告库</text>
  </g>

  <!-- 连接线 取消分支 -->
  <path d="M 450 390 C 300 450, 250 650, 250 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="300" y="550" text-anchor="middle" font-size="12" fill="#555">取消操作</text>
  
  <!-- 连接线 11 -> 12 -->
  <path d="M 350 755 Q 375 755 400 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 监控指标 -> 归档 -->
  <path d="M 960 590 C 960 650, 775 690, 775 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>