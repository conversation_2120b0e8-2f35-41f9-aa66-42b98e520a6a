<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1100" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">中科院成果管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入页面 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">中科院成果管理</text>
  </g>

  <!-- 节点2: 权限验证与数据加载 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成指标概览，状态：已加载</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 500 165 Q 650 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点3: 用户提交筛选条件 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据查询请求</text>
  </g>

  <!-- 节点4: 检索服务 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时整合与过滤数据</text>
  </g>

  <!-- 节点5: 数据展示更新 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据展示更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新列表和图表，状态：已完成</text>
  </g>

  <!-- 连接线 权限验证 -> 筛选条件 -->
  <path d="M 850 200 C 850 250, 350 250, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 筛选条件 -> 检索服务 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 检索服务 -> 数据展示 -->
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点6: 新增/编辑操作 -->
  <g transform="translate(50, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增/编辑操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">数据完整性检查</text>
  </g>

  <!-- 节点7: PDF上传解析 -->
  <g transform="translate(270, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">PDF上传解析</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">智能解析与结构化</text>
  </g>

  <!-- 节点8: 批量导出操作 -->
  <g transform="translate(490, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导出操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">生成Excel文件</text>
  </g>

  <!-- 节点9: 缓存刷新操作 -->
  <g transform="translate(710, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">实时更新数据</text>
  </g>

  <!-- 节点10: 数据库更新 -->
  <g transform="translate(930, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据库更新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">存入成果库，重算指标</text>
  </g>

  <!-- 节点11: 消息中心通知 -->
  <g transform="translate(1150, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息中心通知</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">推送任务报告</text>
  </g>

  <!-- 连接线 数据展示 -> 各种操作 -->
  <path d="M 850 390 C 850 450, 150 450, 140 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 390 C 850 450, 360 450, 360 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 390 C 850 450, 580 450, 580 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 390 C 850 450, 800 450, 800 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 操作 -> 数据库更新 -->
  <path d="M 230 555 C 500 555, 800 555, 930 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 555 C 650 555, 800 555, 930 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 555 Q 910 555 930 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 PDF解析和导出 -> 消息中心 -->
  <path d="M 450 555 C 700 555, 1000 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 555 C 850 555, 1000 555, 1150 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>

  <!-- 节点12: 定时同步任务 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步任务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">每日从中科院官网同步数据</text>
  </g>

  <!-- 节点13: 数据比对更新 -->
  <g transform="translate(550, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据比对更新</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">成果状态、创新成果信息</text>
  </g>

  <!-- 节点14: 质量监控 -->
  <g transform="translate(900, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量监控</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">确保数据全面性和及时性</text>
  </g>

  <!-- 连接线 数据库更新 -> 定时同步 -->
  <path d="M 1020 590 C 1020 650, 325 650, 325 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 定时同步 -> 数据比对 -->
  <path d="M 450 755 Q 500 755 550 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 数据比对 -> 质量监控 -->
  <path d="M 800 755 Q 850 755 900 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：质量监控 -> 权限验证 -->
  <path d="M 1000 720 C 1200 720, 1200 100, 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1250, 400)">数据同步反馈</text>

  <!-- PDF解析流程细节 -->
  <g transform="translate(200, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">文件格式验证</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">PDF格式检查</text>
  </g>

  <g transform="translate(400, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">智能解析处理</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">异步结构化</text>
  </g>

  <g transform="translate(600, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">预览确认</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">用户确认结果</text>
  </g>

  <g transform="translate(800, 850)" filter="url(#soft-shadow)">
    <rect width="150" height="50" rx="6" ry="6" fill="#FFF3E0" stroke="#FFB74D" stroke-width="1" />
    <text x="75" y="25" text-anchor="middle" font-size="12" font-weight="500" fill="#333">自动入库</text>
    <text x="75" y="40" text-anchor="middle" font-size="10" fill="#666">生成任务报告</text>
  </g>

  <!-- PDF解析流程连接线 -->
  <path d="M 350 875 Q 375 875 400 875" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 875 Q 575 875 600 875" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 875 Q 775 875 800 875" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接PDF上传到详细流程 -->
  <path d="M 360 590 C 360 750, 275 750, 275 850" stroke="#FF9800" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />

</svg>