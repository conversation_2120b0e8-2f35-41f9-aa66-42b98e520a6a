<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研合作展示流程图</text>

  <!-- 阶段一：模块初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：模块初始化与数据加载</text>
  
  <!-- 节点1: 用户访问 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">触发初始化事件</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">进入科研合作展示</text>
  </g>

  <!-- 节点2: 数据聚合服务 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">合作数据聚合服务</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">拉取合作类型枚举</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">行业领域枚举及统计数据</text>
  </g>

  <!-- 节点3: 默认视图生成 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">默认视图生成</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">概览视图渲染</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">合作单位列表</text>
  </g>

  <!-- 阶段二：筛选与查询 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选与查询</text>

  <!-- 节点4: 用户筛选操作 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户筛选操作</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">调整筛选条件</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">输入关键字</text>
  </g>

  <!-- 节点5: 数据实时刷新 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据实时刷新</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">重新调用数据服务</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">确保多区块数据一致</text>
  </g>

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>

  <!-- 节点6: 项目详情查看 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情查看</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">检索项目全量数据</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">写入会话缓存</text>
  </g>

  <!-- 节点7: 报告生成导出 -->
  <g transform="translate(450, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">报告生成导出</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">调用报告生成服务</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">输出PDF文件</text>
  </g>

  <!-- 阶段四：会话管理与日志记录 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：会话管理与日志记录</text>

  <!-- 节点8: 会话管理 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">会话管理与日志记录</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">保留筛选条件</text>
    <text x="200" y="50" text-anchor="middle" font-size="12" fill="#555">记录浏览日志</text>
    <text x="300" y="50" text-anchor="middle" font-size="12" fill="#555">操作统计审计</text>
    <text x="200" y="65" text-anchor="middle" font-size="12" fill="#555">销毁会话缓存并写入最终日志</text>
  </g>

  <!-- 连接线 -->
  <!-- 用户访问 -> 数据聚合 -->
  <path d="M 300 170 Q 350 170 400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 数据聚合 -> 默认视图 -->
  <path d="M 620 170 Q 660 170 700 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 默认视图 -> 用户筛选 (反馈循环) -->
  <path d="M 750 210 C 750 250, 350 280, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 用户筛选 -> 数据刷新 -->
  <path d="M 400 350 Q 450 350 500 350" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 数据刷新 -> 项目详情 -->
  <path d="M 550 390 C 500 430, 350 460, 250 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 项目详情 -> 报告生成 -->
  <path d="M 350 530 Q 400 530 450 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 报告生成 -> 会话管理 -->
  <path d="M 550 570 C 550 620, 520 650, 500 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 会话管理反馈到筛选 (虚线表示状态保持) -->
  <path d="M 300 670 C 100 600, 100 400, 200 390" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="120" y="530" font-size="11" fill="#666">状态保持</text>

  <!-- 标注文字 -->
  <text x="350" y="155" font-size="11" fill="#666">触发</text>
  <text x="660" y="155" font-size="11" fill="#666">生成</text>
  <text x="450" y="335" font-size="11" fill="#666">查询参数</text>
  <text x="400" y="515" font-size="11" fill="#666">导出操作</text>
  <text x="525" y="600" font-size="11" fill="#666">记录统计</text>

</svg>