'use client'

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { BarChart2, TrendingUp, TrendingDown } from "lucide-react"

export function AssetTrends() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm h-[calc(80vh-13rem)]">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>资产趋势分析</CardTitle>
        <Badge variant="outline">近30天</Badge>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="h-[calc(80vh-20rem)] bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="space-y-1">
                <div className="text-sm font-medium">总资产数量</div>
                <div className="text-2xl font-semibold">1,842</div>
              </div>
              <div className="flex items-center gap-2 text-green-600">
                <TrendingUp className="h-4 w-4" />
                <span className="text-sm">+12.5%</span>
              </div>
            </div>

            <div className="space-y-2 mt-4">
              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-blue-500" />
                  </div>
                  <div>
                    <div className="font-medium">服务器设备</div>
                    <div className="text-sm text-gray-500">数量: 486</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span>8.3%</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-purple-500" />
                  </div>
                  <div>
                    <div className="font-medium">网络设备</div>
                    <div className="text-sm text-gray-500">数量: 324</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-red-600">
                  <TrendingDown className="h-4 w-4" />
                  <span>-2.1%</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <div className="font-medium">存储设备</div>
                    <div className="text-sm text-gray-500">数量: 256</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span>15.7%</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-orange-500" />
                  </div>
                  <div>
                    <div className="font-medium">安全设备</div>
                    <div className="text-sm text-gray-500">数量: 198</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span>6.2%</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-pink-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-pink-500" />
                  </div>
                  <div>
                    <div className="font-medium">办公设备</div>
                    <div className="text-sm text-gray-500">数量: 432</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-green-600">
                  <TrendingUp className="h-4 w-4" />
                  <span>4.8%</span>
                </div>
              </div>

              <div className="flex items-center justify-between p-2.5 bg-white rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gray-50 rounded-lg">
                    <BarChart2 className="h-4 w-4 text-gray-500" />
                  </div>
                  <div>
                    <div className="font-medium">其他设备</div>
                    <div className="text-sm text-gray-500">数量: 146</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-red-600">
                  <TrendingDown className="h-4 w-4" />
                  <span>-1.5%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 