unit CcFlCkFlowView;

interface

uses
  Classes;

type
  TCcFlCkFlowView = class
  private
    FItem_7: string;
    FItem_8: string;
    FItem_9: string;
    FItem_10: string;
    FItem_11: string;
    FItem_12_1: string;
    FItem_12_2: string;
    FItem_13_1: string;
    FItem_13_2: string;
    FJhs_7: double;
    FJhs_8: double;
    FJhs_9: double;
    FJhs_10: double;
    FJhs_11: double;
    FJhs_12: double;
    FJhs_13: double;
    FKsid: integer;
    FDdid: integer;
  public
    property Item_7: string read FItem_7 write FItem_7;
    property Item_8: string read FItem_8 write FItem_8;
    property Item_9: string read FItem_9 write FItem_9;
    property Item_10: string read FItem_10 write FItem_10;
    property Item_11: string read FItem_11 write FItem_11;
    property Item_12_1: string read FItem_12_1 write FItem_12_1;
    property Item_12_2: string read FItem_12_2 write FItem_12_2;
    property Item_13_1: string read FItem_13_1 write FItem_13_1;
    property Item_13_2: string read FItem_13_2 write FItem_13_2;

    property Jhs_7: double read FJhs_7 write FJhs_7;
    property Jhs_8: double read FJhs_8 write FJhs_8;
    property Jhs_9: double read FJhs_9 write FJhs_9;
    property Jhs_10: double read FJhs_10 write FJhs_10;
    property Jhs_11: double read FJhs_11 write FJhs_11;
    property Jhs_12: double read FJhs_12 write FJhs_12;
    property Jhs_13: double read FJhs_13 write FJhs_13;
    property Ksid: integer read FKsid write FKsid;
    property Ddid: integer read FDdid write FDdid;

  end;

implementation

end.
