'use client'

import { useState, useRef } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Label } from "@/components/ui/label"
import { 
  Upload, 
  Download, 
  Play, 
  Pause, 
  RotateCcw, 
  FileSpreadsheet, 
  CheckCircle, 
  XCircle, 
  Clock,
  Settings,
  UserPlus,
  Trash2,
  Plus
} from "lucide-react"
import { callLLMApi } from '@/lib/llm-api'

interface TestCase {
  id: number
  question: string // 问题
  answer: string // 答案
  scenarioDescription: string // 测试场景说明
  testUrl: string // 测试地址
  testKey: string // 测试key
  modelTestResult?: string // 模型测试结果（要写入的）
  aiJudgment?: string // AI结果判定
  status: 'pending' | 'running' | 'completed' | 'error'
  error?: string
  startTime?: Date
  endTime?: Date
  responseTime?: number
  conversationId?: string
  messageId?: string
  usage?: {
    total_tokens?: number
    prompt_tokens?: number
    completion_tokens?: number
  }
}

interface TestConfig {
  concurrency: number
  delayBetweenRequests: number
  enableJudgment: boolean // 是否启用AI判定
}



export default function BatchTestPage() {
  const [testCases, setTestCases] = useState<TestCase[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [isPaused, setIsPaused] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [progress, setProgress] = useState(0)
  const [config, setConfig] = useState<TestConfig>({
    concurrency: 1,
    delayBetweenRequests: 1000,
    enableJudgment: true // 默认开启AI判定
  })
  const [showAddForm, setShowAddForm] = useState(false)
  const [newTestCase, setNewTestCase] = useState({
    question: '',
    answer: '',
    scenarioDescription: '',
    testUrl: 'http://************:8081/v1',
    testKey: 'app-hXBah6VG2H7FcwuHQ6iSiI4A'
  })
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'running' | 'completed' | 'error'>('all')
  const [judgmentFilter, setJudgmentFilter] = useState<'all' | 'correct' | 'incorrect' | 'partial' | 'error' | 'unjudged'>('all')
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 处理Excel文件上传
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/parse-excel', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '文件解析失败')
      }

      setTestCases(result.data)
      setProgress(0)
      setCurrentIndex(0)
      
      console.log('文件上传成功，解析到', result.data.length, '个测试用例')
      alert(result.message)
      
    } catch (error) {
      console.error('文件解析失败:', error)
      alert(error instanceof Error ? error.message : '文件解析失败，请检查文件格式')
    }
  }

  // 调用Dify API获取模型回答（带元数据）
  const callDifyAPIWithMetadata = async (testCase: TestCase): Promise<{
    answer: string
    metadata: {
      conversationId?: string
      messageId?: string
      usage?: {
        total_tokens?: number
        prompt_tokens?: number
        completion_tokens?: number
      }
    }
  }> => {
    try {
      // 如果测试用例有自定义的API配置，则直接调用Dify API
      // 否则使用默认的路由
      const useCustomAPI = testCase.testUrl !== 'http://************:8081/v1' || 
                          testCase.testKey !== 'app-hXBah6VG2H7FcwuHQ6iSiI4A'
      
      let response: Response
      
      if (useCustomAPI) {
        // 使用批量测试API端点处理自定义配置
        response = await fetch('/api/batch-test-dify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: testCase.question,
            user: `batch-test-user-${testCase.id}`,
            baseUrl: testCase.testUrl,
            apiKey: testCase.testKey
          }),
          signal: abortControllerRef.current?.signal
        })
      } else {
        // 使用默认的API路由
        response = await fetch('/api/dify-chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: testCase.question,
            user: `batch-test-user-${testCase.id}`
          }),
          signal: abortControllerRef.current?.signal
        })
      }

      if (!response.ok) {
        throw new Error(`API调用失败: ${response.status}`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      let accumulatedAnswer = ''
      let conversationId: string | undefined
      let messageId: string | undefined
      let usage: any

      if (reader) {
        let buffer = ''
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk
          
          // 处理完整的SSE事件
          const events = buffer.split('\n\n')
          buffer = events.pop() || ''
          
          for (const event of events) {
            if (!event.trim() || !event.startsWith('data:')) continue
            
            try {
              const jsonStr = event.substring(5).trim()
              const data = JSON.parse(jsonStr)
              
              // 获取会话ID
              if (data.conversation_id) {
                conversationId = data.conversation_id
              }
              
              // 获取消息ID
              if (data.message_id) {
                messageId = data.message_id
              }
              
              // 处理增量消息内容
              if (data.event === 'message' && data.answer !== undefined) {
                accumulatedAnswer += data.answer
              }
              
              // 处理消息结束事件
              if (data.event === 'message_end') {
                if (data.metadata?.usage) {
                  usage = data.metadata.usage
                }
                return {
                  answer: accumulatedAnswer,
                  metadata: {
                    conversationId,
                    messageId,
                    usage
                  }
                }
              }
              
            } catch (e) {
              console.error("处理SSE事件失败:", e)
            }
          }
        }
      }

      return {
        answer: accumulatedAnswer || '获取回答失败',
        metadata: {
          conversationId,
          messageId,
          usage
        }
      }
      
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求被取消')
      }
      throw error
    }
  }

  // 调用AI判定API
  const callJudgmentAPI = async (question: string, correctAnswer: string, modelAnswer: string): Promise<string> => {
    try {
      const judgmentPrompt = `你是一个专业的答案评估专家。请仔细比较以下问题的标准答案和模型回答，判断模型回答是否正确。

**评估要求：**
1. 重点关注语义一致性，而不是字面完全相同
2. 允许不同的表达方式，只要核心意思正确
3. 考虑答案的完整性和准确性
4. 如果模型回答包含标准答案的核心内容，即使表达方式不同，也应判断为正确
5. 如果模型回答完全错误、遗漏关键信息或与标准答案相矛盾，则判断为错误

**问题：**
${question}

**标准答案：**
${correctAnswer}

**模型回答：**
${modelAnswer}

**判断要求：**
请基于以上内容，判断模型回答是否正确。请只回答以下选项之一：
- "正确" - 如果模型回答在语义上与标准答案一致，包含了核心正确信息
- "错误" - 如果模型回答与标准答案相矛盾、遗漏关键信息或完全错误
- "部分正确" - 如果模型回答包含部分正确信息但不完整

请只输出判断结果，不要添加其他解释。`

      console.log('调用AI判定API: 火山方舟LLM')
      console.log('判定提示词:', judgmentPrompt)

      // 使用llm-api.ts中的callLLMApi函数
      const judgment = await callLLMApi(judgmentPrompt, (logEntry) => {
        console.log('LLM API日志:', logEntry)
      })
      
      const cleanJudgment = judgment?.trim() || 'unknown'
      console.log('AI判定结果:', cleanJudgment)
      return cleanJudgment

    } catch (error) {
      console.error('AI判定失败:', error)
      return 'error'
    }
  }



  // 开始批量测试
  const startBatchTest = async () => {
    if (testCases.length === 0) {
      alert('请先上传测试文件')
      return
    }

    setIsRunning(true)
    setIsPaused(false)
    abortControllerRef.current = new AbortController()

    try {
      for (let i = currentIndex; i < testCases.length; i++) {
        if (isPaused || abortControllerRef.current?.signal.aborted) {
          break
        }

        setCurrentIndex(i)
        
        // 更新当前测试用例状态为运行中
        setTestCases(prev => prev.map(tc => 
          tc.id === testCases[i].id 
            ? { ...tc, status: 'running' }
            : tc
        ))

        try {
          const startTime = new Date()
          
          // 更新开始时间
          setTestCases(prev => prev.map(tc => 
            tc.id === testCases[i].id 
              ? { ...tc, startTime }
              : tc
          ))

          // 1. 调用模型API获取回答
          console.log(`开始测试第 ${i + 1} 个用例:`, testCases[i].question)
          const { answer: rawModelAnswer, metadata } = await callDifyAPIWithMetadata(testCases[i])
          console.log(`原始模型回答:`, rawModelAnswer)
          
          // 过滤掉<think>...</think>部分，只保留正式结果
          const modelAnswer = rawModelAnswer.replace(/<think>[\s\S]*?<\/think>/gi, '').trim()
          console.log(`过滤后模型回答:`, modelAnswer)
          
          // 更新模型回答和元数据
          setTestCases(prev => prev.map(tc => 
            tc.id === testCases[i].id 
              ? { 
                  ...tc, 
                  modelTestResult: modelAnswer,
                  conversationId: metadata.conversationId,
                  messageId: metadata.messageId,
                  usage: metadata.usage
                }
              : tc
          ))

          // 延迟防止过快请求
          await new Promise(resolve => setTimeout(resolve, config.delayBetweenRequests))

          // 2. 执行AI判定（如果启用）
          let aiJudgment = ''
          if (config.enableJudgment && modelAnswer) {
            console.log(`开始AI判定第 ${i + 1} 个用例`)
            try {
              aiJudgment = await callJudgmentAPI(
                testCases[i].question,
                testCases[i].answer,
                modelAnswer
              )
              console.log(`AI判定结果:`, aiJudgment)
            } catch (judgmentError) {
              console.error('AI判定失败:', judgmentError)
              aiJudgment = 'error'
            }
          } else {
            console.log(`第 ${i + 1} 个用例模型测试完成，AI判定功能已禁用`)
          }

          const endTime = new Date()
          const responseTime = endTime.getTime() - startTime.getTime()

          // 更新最终结果
          setTestCases(prev => prev.map(tc => 
            tc.id === testCases[i].id 
              ? { 
                  ...tc, 
                  aiJudgment, 
                  status: 'completed',
                  endTime,
                  responseTime
                }
              : tc
          ))

        } catch (error) {
          console.error(`测试用例 ${i + 1} 失败:`, error)
          // 更新错误状态
          setTestCases(prev => prev.map(tc => 
            tc.id === testCases[i].id 
              ? { 
                  ...tc, 
                  status: 'error',
                  error: error instanceof Error ? error.message : '未知错误'
                }
              : tc
          ))
        }

        // 更新进度
        setProgress(((i + 1) / testCases.length) * 100)
      }

      console.log('批量测试完成')
    } catch (error) {
      console.error('批量测试失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  // 暂停测试
  const pauseTest = () => {
    setIsPaused(true)
    setIsRunning(false)
    console.log('测试已暂停')
  }

  // 继续测试
  const resumeTest = () => {
    setIsPaused(false)
    console.log('继续测试，从第', currentIndex + 1, '个用例开始')
    startBatchTest()
  }

  // 停止测试
  const stopTest = () => {
    setIsRunning(false)
    setIsPaused(false)
    abortControllerRef.current?.abort()
    setCurrentIndex(0)
    setProgress(0)
  }

  // 重置测试
  const resetTest = () => {
    stopTest()
    setTestCases(prev => prev.map(tc => ({
      ...tc,
      modelTestResult: undefined,
      aiJudgment: undefined,
      status: 'pending',
      error: undefined
    })))
  }

  // 添加单个测试用例
  const addTestCase = () => {
    if (!newTestCase.question.trim() || !newTestCase.answer.trim()) {
      alert('请填写问题和答案')
      return
    }

    const newCase: TestCase = {
      id: testCases.length > 0 ? Math.max(...testCases.map(tc => tc.id)) + 1 : 1,
      question: newTestCase.question.trim(),
      answer: newTestCase.answer.trim(),
      scenarioDescription: newTestCase.scenarioDescription.trim(),
      testUrl: newTestCase.testUrl,
      testKey: newTestCase.testKey,
      status: 'pending'
    }

    setTestCases(prev => [...prev, newCase])
    setNewTestCase({
      question: '',
      answer: '',
      scenarioDescription: '',
      testUrl: 'http://************:8081/v1',
      testKey: 'app-hXBah6VG2H7FcwuHQ6iSiI4A'
    })
    setShowAddForm(false)
    console.log('添加新测试用例:', newCase)
  }

  // 删除测试用例
  const deleteTestCase = (id: number) => {
    if (isRunning) {
      alert('测试进行中，无法删除用例')
      return
    }
    
    setTestCases(prev => prev.filter(tc => tc.id !== id))
    console.log('删除测试用例:', id)
  }

  // 导出结果为Excel格式
  const exportResults = async () => {
    try {
      // 动态导入ExcelJS
      const ExcelJS = (await import('exceljs')).default
      
      // 创建工作簿和工作表
      const workbook = new ExcelJS.Workbook()
      const worksheet = workbook.addWorksheet('测试结果')
      
      // 设置列标题 - 只保留需要的7列
      worksheet.columns = [
        { header: '问题', key: 'question', width: 50 },
        { header: '答案', key: 'answer', width: 50 },
        { header: '测试场景说明', key: 'scenarioDescription', width: 30 },
        { header: '测试地址', key: 'testUrl', width: 30 },
        { header: '测试key', key: 'testKey', width: 30 },
        { header: '模型测试结果', key: 'modelTestResult', width: 50 },
        { header: 'AI结果判定', key: 'aiJudgment', width: 20 }
      ]
      
      // 添加数据行
      testCases.forEach(tc => {
        worksheet.addRow({
          question: tc.question,
          answer: tc.answer,
          scenarioDescription: tc.scenarioDescription,
          testUrl: tc.testUrl,
          testKey: tc.testKey,
          modelTestResult: tc.modelTestResult || '',
          aiJudgment: tc.aiJudgment || ''
        })
      })
      
      // 设置表头样式
      const headerRow = worksheet.getRow(1)
      headerRow.font = { bold: true }
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6E6FA' }
      }
      
      // 设置边框
      worksheet.eachRow((row, rowNumber) => {
        row.eachCell((cell) => {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          }
        })
      })
      
      // 生成Excel文件并下载
      const buffer = await workbook.xlsx.writeBuffer()
      const blob = new Blob([buffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      })
      
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `批量测试结果_${new Date().toISOString().slice(0, 10)}.xlsx`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      console.log('Excel文件导出成功')
    } catch (error) {
      console.error('导出Excel失败:', error)
      alert('导出失败，请检查控制台错误信息')
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'pending':
        return <Clock size={16} className="text-gray-500" />
      case 'running':
        return <div className="w-4 h-4 border-2 border-t-transparent border-blue-500 rounded-full animate-spin" />
      case 'completed':
        return <CheckCircle size={16} className="text-green-500" />
      case 'error':
        return <XCircle size={16} className="text-red-500" />
    }
  }

  // 获取判定图标
  const getJudgmentIcon = (judgment?: string) => {
    if (!judgment) {
      return <Badge variant="outline">待判定</Badge>
    }
    
    const lowerJudgment = judgment.toLowerCase()
    
    if (lowerJudgment.includes('正确') || lowerJudgment === 'correct') {
      return <Badge variant="secondary" className="bg-green-100 text-green-800">正确</Badge>
    } else if (lowerJudgment.includes('错误') || lowerJudgment === 'incorrect') {
      return <Badge variant="secondary" className="bg-red-100 text-red-800">错误</Badge>
    } else if (lowerJudgment.includes('部分正确') || lowerJudgment === 'partial') {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">部分正确</Badge>
    } else if (lowerJudgment === 'error') {
      return <Badge variant="secondary" className="bg-gray-100 text-gray-600">判定失败</Badge>
    } else {
      return <Badge variant="outline">{judgment}</Badge>
    }
  }

  // 筛选测试用例
  const filteredTestCases = testCases.filter(testCase => {
    // 状态筛选
    if (statusFilter !== 'all' && testCase.status !== statusFilter) {
      return false
    }
    
    // 判定结果筛选
    if (judgmentFilter !== 'all') {
      if (judgmentFilter === 'unjudged' && testCase.aiJudgment) {
        return false
      }
      if (judgmentFilter !== 'unjudged') {
        if (!testCase.aiJudgment) return false
        
        const lowerJudgment = testCase.aiJudgment.toLowerCase()
        if (judgmentFilter === 'correct' && !(lowerJudgment.includes('正确') || lowerJudgment === 'correct')) {
          return false
        }
        if (judgmentFilter === 'incorrect' && !(lowerJudgment.includes('错误') || lowerJudgment === 'incorrect')) {
          return false
        }
        if (judgmentFilter === 'partial' && !(lowerJudgment.includes('部分正确') || lowerJudgment === 'partial')) {
          return false
        }
        if (judgmentFilter === 'error' && lowerJudgment !== 'error') {
          return false
        }
      }
    }
    
    return true
  })

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">批量测试工具</h1>
        <p className="text-gray-600">上传Excel文件进行批量API测试和答案准确性判定</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧：配置和控制面板 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 文件上传 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload size={20} />
                文件上传
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">选择Excel文件</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    ref={fileInputRef}
                    accept=".xlsx,.xls,.csv"
                    onChange={handleFileUpload}
                    className="mt-1"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    支持格式：.xlsx, .xls, .csv
                  </p>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const link = document.createElement('a')
                        link.href = '/batch-test-template.csv'
                        link.download = 'batch-test-template.csv'
                        link.click()
                      }}
                    >
                      <Download size={14} className="mr-1" />
                      下载模板
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowAddForm(true)}
                      disabled={isRunning}
                    >
                      <UserPlus size={14} className="mr-1" />
                      手动添加
                    </Button>
                  </div>
                </div>
                {testCases.length > 0 && (
                  <div className="text-sm text-green-600">
                    已加载 {testCases.length} 个测试用例
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 全局配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings size={20} />
                全局配置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="delay">请求间隔(ms)</Label>
                  <Input
                    id="delay"
                    type="number"
                    value={config.delayBetweenRequests}
                    onChange={(e) => setConfig(prev => ({ ...prev, delayBetweenRequests: Number(e.target.value) }))}
                    className="mt-1"
                    min="0"
                  />
                </div>
                
                {/* AI判定配置 */}
                <div className="border-t pt-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <input
                      id="enable-judgment"
                      type="checkbox"
                      checked={config.enableJudgment}
                      onChange={(e) => setConfig(prev => ({ ...prev, enableJudgment: e.target.checked }))}
                      className="rounded"
                    />
                    <Label htmlFor="enable-judgment" className="text-sm font-medium">启用AI结果判定</Label>
                  </div>
                  
                  <p className="text-xs text-gray-500 mt-2">
                    AI判定功能会比较标准答案和模型回答的语义一致性
                    {config.enableJudgment && (
                      <>
                        <br />
                        <span className="text-blue-600">判定API配置: 火山方舟LLM (llm-api.ts)</span>
                      </>
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 手动添加测试用例 */}
          {showAddForm && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus size={20} />
                  添加测试用例
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="new-question">问题</Label>
                    <textarea
                      id="new-question"
                      className="w-full mt-1 p-2 border rounded-md resize-none"
                      rows={3}
                      value={newTestCase.question}
                      onChange={(e) => setNewTestCase(prev => ({ ...prev, question: e.target.value }))}
                      placeholder="请输入测试问题"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-answer">答案</Label>
                    <textarea
                      id="new-answer"
                      className="w-full mt-1 p-2 border rounded-md resize-none"
                      rows={3}
                      value={newTestCase.answer}
                      onChange={(e) => setNewTestCase(prev => ({ ...prev, answer: e.target.value }))}
                      placeholder="请输入答案"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-scenario">测试场景说明</Label>
                    <textarea
                      id="new-scenario"
                      className="w-full mt-1 p-2 border rounded-md resize-none"
                      rows={2}
                      value={newTestCase.scenarioDescription}
                      onChange={(e) => setNewTestCase(prev => ({ ...prev, scenarioDescription: e.target.value }))}
                      placeholder="请输入测试场景说明"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-testurl">测试地址</Label>
                    <Input
                      id="new-testurl"
                      value={newTestCase.testUrl}
                      onChange={(e) => setNewTestCase(prev => ({ ...prev, testUrl: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-testkey">测试Key</Label>
                    <Input
                      id="new-testkey"
                      type="password"
                      value={newTestCase.testKey}
                      onChange={(e) => setNewTestCase(prev => ({ ...prev, testKey: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button onClick={addTestCase} size="sm" className="flex-1">
                      <Plus size={14} className="mr-1" />
                      添加
                    </Button>
                    <Button 
                      onClick={() => setShowAddForm(false)} 
                      variant="outline" 
                      size="sm"
                      className="flex-1"
                    >
                      取消
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 控制按钮 */}
          <Card>
            <CardHeader>
              <CardTitle>测试控制</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex gap-2">
                  {!isRunning && !isPaused && (
                    <Button 
                      onClick={startBatchTest}
                      disabled={testCases.length === 0}
                      className="flex-1"
                    >
                      <Play size={16} className="mr-2" />
                      开始测试
                    </Button>
                  )}
                  
                  {isRunning && (
                    <Button 
                      onClick={pauseTest}
                      variant="outline"
                      className="flex-1"
                    >
                      <Pause size={16} className="mr-2" />
                      暂停
                    </Button>
                  )}
                  
                  {isPaused && (
                    <Button 
                      onClick={resumeTest}
                      className="flex-1"
                    >
                      <Play size={16} className="mr-2" />
                      继续
                    </Button>
                  )}
                  
                  <Button 
                    onClick={stopTest}
                    variant="destructive"
                    disabled={!isRunning && !isPaused}
                  >
                    停止
                  </Button>
                </div>
                
                <Button 
                  onClick={resetTest}
                  variant="outline"
                  className="w-full"
                  disabled={isRunning}
                >
                  <RotateCcw size={16} className="mr-2" />
                  重置
                </Button>
                
                <Button 
                  onClick={exportResults}
                  variant="outline"
                  className="w-full"
                  disabled={testCases.length === 0}
                >
                  <Download size={16} className="mr-2" />
                  导出结果
                </Button>
                
                {/* 测试完成提示 */}
                {!isRunning && !isPaused && testCases.length > 0 && 
                 testCases.filter(tc => tc.status === 'completed').length === testCases.length && (
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-sm font-medium text-green-800">
                      🎉 测试完成！
                    </div>
                    <div className="text-xs text-green-600 mt-1">
                      {testCases.length} 个测试用例全部完成，
                      正确率 {(() => {
                        const judgedCases = testCases.filter(tc => tc.aiJudgment && tc.aiJudgment !== 'error')
                        const correctCases = judgedCases.filter(tc => {
                          const lowerJudgment = tc.aiJudgment?.toLowerCase() || ''
                          return lowerJudgment.includes('正确') || lowerJudgment === 'correct'
                        })
                        return judgedCases.length > 0 
                          ? Math.round((correctCases.length / judgedCases.length) * 100)
                          : 0
                      })()}%
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 进度统计 */}
                      {testCases.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>测试进度</CardTitle>
              </CardHeader>
              <CardContent>
                {/* 当前测试状态 */}
                {isRunning && currentIndex < testCases.length && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="text-sm font-medium text-blue-800 mb-2">
                      正在测试第 {currentIndex + 1} 个用例
                    </div>
                    <div className="text-xs text-blue-600 bg-white p-2 rounded border">
                      {testCases[currentIndex]?.question.substring(0, 80)}
                      {testCases[currentIndex]?.question.length > 80 && '...'}
                    </div>
                  </div>
                )}
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>总体进度</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">已完成</div>
                      <div className="font-medium text-green-600">
                        {testCases.filter(tc => tc.status === 'completed').length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">错误</div>
                      <div className="font-medium text-red-600">
                        {testCases.filter(tc => tc.status === 'error').length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">正确率</div>
                      <div className="font-medium text-blue-600">
                        {(() => {
                          const judgedCases = testCases.filter(tc => tc.aiJudgment && tc.aiJudgment !== 'error')
                          const correctCases = judgedCases.filter(tc => {
                            const lowerJudgment = tc.aiJudgment?.toLowerCase() || ''
                            return lowerJudgment.includes('正确') || lowerJudgment === 'correct'
                          })
                          return judgedCases.length > 0 
                            ? Math.round((correctCases.length / judgedCases.length) * 100)
                            : 0
                        })()}%
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">当前</div>
                      <div className="font-medium">
                        {currentIndex + 1} / {testCases.length}
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">平均响应时间</div>
                      <div className="font-medium text-purple-600">
                        {testCases.filter(tc => tc.responseTime).length > 0
                          ? Math.round(testCases.filter(tc => tc.responseTime).reduce((sum, tc) => sum + (tc.responseTime || 0), 0) / testCases.filter(tc => tc.responseTime).length)
                          : 0} ms
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-500">总Token数</div>
                      <div className="font-medium text-orange-600">
                        {testCases.filter(tc => tc.usage?.total_tokens).reduce((sum, tc) => sum + (tc.usage?.total_tokens || 0), 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* 右侧：测试结果列表 */}
        <div className="lg:col-span-2">
          <Card className="h-[800px]">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <FileSpreadsheet size={20} />
                  测试结果
                </span>
                {testCases.length > 0 && (
                  <Badge variant="outline">
                    {filteredTestCases.length} / {testCases.length} 个测试用例
                  </Badge>
                )}
              </CardTitle>
              {testCases.length > 0 && (
                <div className="flex gap-2 mt-2">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value as any)}
                    className="px-2 py-1 text-sm border rounded"
                  >
                    <option value="all">全部状态</option>
                    <option value="pending">待执行</option>
                    <option value="running">执行中</option>
                    <option value="completed">已完成</option>
                    <option value="error">错误</option>
                  </select>
                  <select
                    value={judgmentFilter}
                    onChange={(e) => setJudgmentFilter(e.target.value as any)}
                    className="px-2 py-1 text-sm border rounded"
                  >
                    <option value="all">全部结果</option>
                    <option value="correct">正确</option>
                    <option value="incorrect">错误</option>
                    <option value="partial">部分正确</option>
                    <option value="error">判定失败</option>
                    <option value="unjudged">未判定</option>
                  </select>
                </div>
              )}
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[720px]">
                {testCases.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-[500px] text-center p-6">
                    <FileSpreadsheet size={64} className="text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-500 mb-2">暂无测试用例</h3>
                    <p className="text-gray-400">请上传Excel文件开始批量测试</p>
                  </div>
                ) : filteredTestCases.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-[500px] text-center p-6">
                    <FileSpreadsheet size={64} className="text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-500 mb-2">没有符合条件的测试用例</h3>
                    <p className="text-gray-400">请调整筛选条件</p>
                  </div>
                ) : (
                  <div className="divide-y">
                    {filteredTestCases.map((testCase, index) => (
                      <div 
                        key={testCase.id} 
                        className={`p-4 ${currentIndex === index && isRunning ? 'bg-blue-50' : ''}`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(testCase.status)}
                              <span className="font-medium text-gray-700">#{testCase.id}</span>
                            </div>
                            {getJudgmentIcon(testCase.aiJudgment)}
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="text-xs text-gray-500">
                              {testCase.testUrl.replace('http://', '').replace('https://', '')}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteTestCase(testCase.id)}
                              disabled={isRunning}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                            >
                              <Trash2 size={12} />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <div className="text-sm font-medium text-gray-700 mb-1">问题</div>
                            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                              {testCase.question}
                            </div>
                          </div>
                          
                          <div>
                            <div className="text-sm font-medium text-gray-700 mb-1">答案</div>
                            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                              {testCase.answer}
                            </div>
                          </div>
                          
                          {testCase.scenarioDescription && (
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-1">测试场景说明</div>
                              <div className="text-sm text-gray-600 bg-yellow-50 p-2 rounded">
                                {testCase.scenarioDescription}
                              </div>
                            </div>
                          )}
                          
                          {testCase.modelTestResult && (
                            <div>
                              <div className="text-sm font-medium text-gray-700 mb-1">模型测试结果</div>
                              <div className="text-sm text-gray-600 bg-blue-50 p-2 rounded">
                                {testCase.modelTestResult}
                              </div>
                            </div>
                          )}
                          
                          {testCase.error && (
                            <div>
                              <div className="text-sm font-medium text-red-700 mb-1">错误信息</div>
                              <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                                {testCase.error}
                              </div>
                            </div>
                          )}
                          
                          {testCase.status === 'completed' && (
                            <div className="mt-3 p-2 bg-gray-50 rounded text-xs text-gray-600">
                              <div className="grid grid-cols-2 gap-2">
                                {testCase.responseTime && (
                                  <div>响应时间: {testCase.responseTime}ms</div>
                                )}
                                {testCase.usage?.total_tokens && (
                                  <div>Token数: {testCase.usage.total_tokens.toLocaleString()}</div>
                                )}
                                {testCase.conversationId && (
                                  <div className="col-span-2 truncate">会话ID: {testCase.conversationId}</div>
                                )}
                                {testCase.messageId && (
                                  <div className="col-span-2 truncate">消息ID: {testCase.messageId}</div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 