'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Building,
  Users,
  TrendingUp,
  Download,
  RefreshCw,
  Eye,
  ExternalLink,
  Building2,
  UserCheck,
  Network,
  BarChart3,
  Target,
  Brain,
  TrendingDown,
  AlertTriangle,
  Lightbulb
} from "lucide-react"

export default function EnterpriseInfoPage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
      {/* 头部区域 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <Building className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-blue-800">0708企业信息展示</h1>
            <p className="text-gray-600 mt-1">创新主体科研码 - 全方位企业画像分析</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" className="border-blue-200 text-blue-600 hover:bg-blue-50">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <RefreshCw className="mr-2 h-4 w-4" />
            刷新数据
          </Button>
        </div>
      </div>

      {/* 企业概况 */}
      <Card className="border border-blue-200 shadow-md">
        <CardHeader className="border-b border-blue-100">
          <CardTitle className="flex items-center text-blue-800">
            <Building className="mr-2 h-5 w-5" />
            企业概况
          </CardTitle>
          <div className="flex gap-2 mt-2">
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">存续</Badge>
            <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">高新技术企业</Badge>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-6">
              {/* 左侧信息 */}
              <div>
                <label className="text-sm text-gray-500">企业名称</label>
                <p className="text-lg font-medium text-gray-900 mt-1">宁波创新科技股份有限公司</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">统一社会信用代码</label>
                <p className="text-lg font-mono text-gray-900 mt-1">91330200MA2H7YU12X</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">企业类型</label>
                <p className="text-lg text-gray-900 mt-1">股份有限公司</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">注册资本</label>
                <p className="text-lg text-gray-900 mt-1">5,000万元</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">成立日期</label>
                <p className="text-lg text-gray-900 mt-1">2018年3月15日</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">行业分类</label>
                <p className="text-lg text-gray-900 mt-1">软件和信息技术服务业</p>
              </div>
              <div>
                <label className="text-sm text-gray-500">注册地址</label>
                <p className="text-lg text-gray-900 mt-1">浙江省宁波市高新区创新大道188号科技园A座</p>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center">
              <label className="text-sm text-gray-500 mb-4">营业执照</label>
              <div className="w-80 h-48 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center mb-4">
                <div className="w-60 h-40 bg-gray-200 rounded"></div>
              </div>
              <Button variant="link" className="text-blue-600">
                <Eye className="mr-2 h-4 w-4" />
                查看大图
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-4 gap-6">
        <Card className="border border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">股东数量</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">8</p>
                <p className="text-sm text-green-600 mt-2">较上年增长 +2</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">对外投资</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">12</p>
                <p className="text-sm text-blue-600 mt-2">投资总额 3.2亿元</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">参保人数</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">326</p>
                <p className="text-sm text-purple-600 mt-2">本月 +15 人</p>
              </div>
              <div className="h-12 w-12 bg-purple-50 rounded-lg flex items-center justify-center">
                <UserCheck className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">研发人员</p>
                <p className="text-2xl font-bold text-gray-900 mt-1">158</p>
                <p className="text-sm text-orange-600 mt-2">占比 48.5%</p>
              </div>
              <div className="h-12 w-12 bg-orange-50 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* 股东信息与出资占比 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <Users className="mr-2 h-5 w-5" />
                股东信息与出资占比
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                查看详情
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {/* 股东图表占位 */}
            <div className="h-40 bg-gray-50 rounded-lg mb-6 flex items-center justify-center">
              <div className="text-gray-400">股东持股比例图表</div>
            </div>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">张明</p>
                    <p className="text-sm text-gray-500">自然人</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">35.0%</p>
                  <p className="text-sm text-gray-500">1,750万元</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">宁波投资有限公司</p>
                    <p className="text-sm text-gray-500">法人</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">25.0%</p>
                  <p className="text-sm text-gray-500">1,250万元</p>
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                  <div>
                    <p className="font-medium text-gray-900">其他股东</p>
                    <p className="text-sm text-gray-500">6位</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-bold text-gray-900">40.0%</p>
                  <p className="text-sm text-gray-500">2,000万元</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 对外投资总览 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <TrendingUp className="mr-2 h-5 w-5" />
                对外投资总览
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                查看详情
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">12</p>
                <p className="text-sm text-gray-500">投资企业</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">3.2亿</p>
                <p className="text-sm text-gray-500">投资总额</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">6</p>
                <p className="text-sm text-gray-500">地区分布</p>
              </div>
            </div>
            <div className="space-y-3">
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">杭州智能科技有限公司</p>
                    <p className="text-sm text-gray-500">人工智能 · 控股子公司</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-blue-600">8,000万元</p>
                    <p className="text-sm text-gray-500">80%</p>
                  </div>
                </div>
              </div>
              <div className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-gray-900">上海数据科技有限公司</p>
                    <p className="text-sm text-gray-500">大数据 · 参股公司</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-blue-600">5,000万元</p>
                    <p className="text-sm text-gray-500">25%</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* 参保信息 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <UserCheck className="mr-2 h-5 w-5" />
                参保信息
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                查看详情
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            {/* 图表占位 */}
            <div className="h-32 bg-gray-50 rounded-lg mb-6 flex items-center justify-center">
              <div className="text-gray-400">参保人数趋势图</div>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">当前参保人数</p>
                <p className="text-xl font-bold text-gray-900">326人</p>
                <p className="text-sm text-green-600">↗ +4.8%</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">缴费基数</p>
                <p className="text-xl font-bold text-gray-900">8,500元</p>
                <p className="text-sm text-blue-600">月平均</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">养老保险</span>
                <span className="font-medium">326人</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">医疗保险</span>
                <span className="font-medium">326人</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">失业保险</span>
                <span className="font-medium">326人</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 股权穿透可视化 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <Network className="mr-2 h-5 w-5" />
                股权穿透可视化
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                全屏查看
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="text-sm text-gray-600 mb-4">
              • 显示2级股权关系<br/>
              • 自然人最终受益人：3位<br/>
              • 法人股东穿透层级：4层
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center justify-center space-x-4 mb-4">
                <div className="bg-blue-100 border-2 border-blue-300 px-3 py-2 rounded text-sm text-blue-800">
                  宁波创新科技<br/>目标企业
                </div>
              </div>
              <div className="flex items-center justify-center space-x-4">
                <div className="bg-green-100 border border-green-300 px-2 py-1 rounded text-xs text-green-800">
                  张明<br/>35%
                </div>
                <div className="bg-yellow-100 border border-yellow-300 px-2 py-1 rounded text-xs text-yellow-800">
                  宁波投资<br/>25%
                </div>
                <div className="bg-purple-100 border border-purple-300 px-2 py-1 rounded text-xs text-purple-800">
                  其他<br/>40%
                </div>
              </div>
              <div className="flex justify-center mt-4">
                <Button variant="link" size="sm" className="text-blue-600">
                  <Network className="mr-1 h-3 w-3" />
                  展开
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-2 gap-6">
        {/* 行业产业与集团关系 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <Building2 className="mr-2 h-5 w-5" />
                行业产业与集团关系
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                查看详情
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">行业分类</p>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">软件和信息技术服务业</Badge>
                  <Badge className="bg-green-100 text-green-800 hover:bg-green-100">人工智能</Badge>
                  <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">大数据</Badge>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">产业链位置</p>
                <div className="bg-gray-50 p-3 rounded-lg text-sm text-gray-600">
                  上游供应商：15家<br/>
                  下游客户：180+家<br/>
                  产业链层级：中游服务商
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700 mb-2">集团关系</p>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>母公司：无</p>
                  <p>子公司：12家</p>
                  <p>关联企业：25家</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 研发人员展示 */}
        <Card className="border border-blue-200 shadow-md">
          <CardHeader className="border-b border-blue-100">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center text-blue-800">
                <Users className="mr-2 h-5 w-5" />
                研发人员展示
              </CardTitle>
              <Button variant="link" className="text-blue-600 p-0">
                查看详情
                <ExternalLink className="ml-1 h-4 w-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-orange-50 p-4 rounded-lg">
                <p className="text-sm text-orange-700">研发人员数</p>
                <p className="text-xl font-bold text-orange-900">158人</p>
                <p className="text-sm text-orange-700">占总员工48.5%</p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-700">技术顾问</p>
                <p className="text-xl font-bold text-blue-900">8人</p>
                <p className="text-sm text-blue-700">外聘专家</p>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">硕士及以上学历</span>
                <span className="font-medium">45%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">高级职称人员</span>
                <span className="font-medium">28人</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">参与项目数</span>
                <span className="font-medium">86个</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">知识产权数量</span>
                <span className="font-medium">142件</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">政策补助</span>
                <span className="font-medium">680万元</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI 分析洞察 */}
      <Card className="border border-blue-200 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50">
        <CardHeader className="border-b border-blue-100">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Brain className="mr-2 h-5 w-5 text-blue-600" />
              <CardTitle className="text-blue-800">AI 分析洞察</CardTitle>
              <Badge className="ml-2 bg-blue-100 text-blue-800 hover:bg-blue-100">智能分析</Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-3 gap-6">
            <Card className="border border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <TrendingUp className="h-4 w-4 text-blue-600 mr-2" />
                  <h4 className="font-medium text-gray-900">发展趋势</h4>
                </div>
                <p className="text-sm text-gray-600">
                  企业整体呈现稳健增长态势，研发投入持续加大，技术团队结构合理，具备较强的创新能力和市场竞争力。
                </p>
              </CardContent>
            </Card>
            <Card className="border border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                  <h4 className="font-medium text-gray-900">风险提示</h4>
                </div>
                <p className="text-sm text-gray-600">
                  对外投资较为集中在科技领域，建议关注行业周期性风险。股权结构相对集中，需注意决策风险。
                </p>
              </CardContent>
            </Card>
            <Card className="border border-gray-200">
              <CardContent className="p-4">
                <div className="flex items-center mb-3">
                  <Lightbulb className="h-4 w-4 text-green-600 mr-2" />
                  <h4 className="font-medium text-gray-900">投资建议</h4>
                </div>
                <p className="text-sm text-gray-600">
                  企业技术实力较强，在人工智能和大数据领域具有一定优势，适合作为长期战略投资标的。
                </p>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* 页面右下角的浮动按钮 */}
      <div className="fixed bottom-8 right-8">
        <Button size="lg" className="rounded-full h-12 w-12 bg-blue-600 hover:bg-blue-700 shadow-lg">
          <span className="text-white font-bold">P</span>
        </Button>
      </div>
    </div>
  )
} 