unit CC_FulDetailFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, RzLabel, Vcl.Grids,
  AdvObj, BaseGrid, AdvGrid, Vcl.ExtCtrls, RzPanel, CcYjRole, AdvGlowButton,
  RzCmboBx, DMUtil, CcSjPbdjGhxx, CommonUtil, CC_FulWinFrm;

type
  TFulDetailForm = class(TForm)
    RzPanel1: TRzPanel;
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_FulWinFrame: TCC_FulWinFrame;
  public
    procedure Init(opentype: integer; DdhStringList:TStringList; showtab: integer;begintime,endtime:string);

  end;

var
  FulDetailForm: TFulDetailForm;

implementation

{$R *.dfm}
{ TMianlForm }

procedure TFulDetailForm.Init(opentype: integer;
  DdhStringList:TStringList; showtab: integer;begintime,endtime:string);
var
  FilterConditionStringList: TStringList;
  Yjtypename: string;
  i: integer;
  CheckStr_Kh, CheckStr_Ddh, CheckStr_Kz, CheckStr_Lb, CheckStr_Ml, CheckStr_Ks,
    CheckStr_Ys, CheckStr_Sl, CheckStr_Dds, CheckStr_Sja, CheckStr_Tgsa: string;
  IndexStringList: TStringList;
  gh: string;
  ghs: string;
  StringList: TStringList;
  Begindate, EndDate: String;
  FilterStringList: TStringList;
  ddh, kh, kz, lb, ml, ks, ys, dds, sja, tgsa: string;
  flag5, flag6, flag7, flag8: Boolean;
begin

  if (FCC_FulWinFrame <> nil) then
  begin
    FCC_FulWinFrame.Free;
    FCC_FulWinFrame := nil;
  end;

  if (FCC_FulWinFrame = nil) then
  begin
    FCC_FulWinFrame := TCC_FulWinFrame.Create(Application);
    FCC_FulWinFrame.Parent := self.RzPanel1;
    FCC_FulWinFrame.Align := alClient;
    FCC_FulWinFrame.Init(begintime,endtime);

    CheckStr_Ddh := '';
    for i := 0 to DdhStringList.Count - 1 do
    begin
      CheckStr_Ddh := CheckStr_Ddh + DdhStringList.Strings[i];
      CheckStr_Ddh := CheckStr_Ddh + '^';
    end;
    Delete(CheckStr_Ddh, Length(CheckStr_Ddh), Length(CheckStr_Ddh) + 1);

    FCC_FulWinFrame.FilterConditionStringList.Delete(1);
    FCC_FulWinFrame.FilterConditionStringList.Insert(1,
      'Ddh=' + CheckStr_Ddh);

    FCC_FulWinFrame.filterRefresh;
    FCC_FulWinFrame.filterButtomPic;
    FCC_FulWinFrame.TabChange(showtab);

  end;
end;

end.
