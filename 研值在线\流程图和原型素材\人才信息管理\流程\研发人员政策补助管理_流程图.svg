<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员政策补助管理业务流程</text>

  <!-- 阶段一：界面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：界面初始化与数据加载</text>
  
  <!-- 节点1: 系统初始化 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统初始化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载补助项目列表</text>
  </g>

  <!-- 阶段二：用户交互与筛选 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与筛选</text>

  <!-- 节点2: 条件筛选 -->
  <g transform="translate(300, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选参数</text>
  </g>

  <!-- 节点3: 列表刷新 -->
  <g transform="translate(900, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">列表刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时更新显示</text>
  </g>

  <!-- 连接线 系统初始化 -> 条件筛选 -->
  <path d="M 650 200 C 550 230, 450 260, 400 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 条件筛选 -> 列表刷新 -->
  <path d="M 500 335 C 650 335, 750 335, 900 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：补助操作与管理 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：补助操作与管理</text>

  <!-- 节点4: 新增补助 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增补助</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">填写补助信息</text>
  </g>

  <!-- 节点5: 编辑补助 -->
  <g transform="translate(380, 470)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑补助</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">修改相关信息</text>
  </g>

  <!-- 节点6: 删除补助 -->
  <g transform="translate(610, 470)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除补助</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">逻辑删除记录</text>
  </g>

  <!-- 节点7: 查看详情 -->
  <g transform="translate(840, 470)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看详情</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">展示完整信息</text>
  </g>

  <!-- 连接线 列表刷新 -> 各操作 -->
  <path d="M 950 370 C 900 400, 350 430, 240 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 980 370 C 950 400, 550 430, 470 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 370 C 980 400, 750 430, 700 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 370 C 1000 400, 950 430, 930 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据管理与监控 -->
  <text x="700" y="610" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据管理与监控</text>

  <!-- 节点8: 数据归档 -->
  <g transform="translate(400, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">历史数据管理</text>
  </g>

  <!-- 节点9: 数据导出 -->
  <g transform="translate(700, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">统计与审计支持</text>
  </g>

  <!-- 连接线 操作 -> 数据管理 -->
  <path d="M 470 540 C 470 580, 500 600, 500 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 540 C 750 580, 780 600, 800 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 数据导出 -> 条件筛选 -->
  <path d="M 750 640 C 650 600, 350 550, 300 400" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="520" y="520" text-anchor="middle" font-size="11" fill="#666">导出反馈</text>

  <!-- 反馈循环2: 数据归档 -> 系统初始化 -->
  <path d="M 450 640 C 350 580, 250 400, 600 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="420" text-anchor="middle" font-size="11" fill="#666">归档同步</text>

  <!-- 节点10: 操作日志 -->
  <g transform="translate(1100, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全程操作记录</text>
  </g>

  <!-- 连接线 所有操作 -> 操作日志 -->
  <path d="M 930 505 C 1050 550, 1150 580, 1200 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>