import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// 月度与均值对比（对应 FillGridByDate1 逻辑的汇总）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const year = searchParams.get('year') || '2025'
    const month = searchParams.get('month') || '05'

    const beginTime = `${year}-01-01`
    const endTime = `${year}-12-31`

    // 月份起止（简单按 31 天兜底；如需精确可服务端计算自然月）
    const beginTimeMonth = `${year}-${month}-01`
    const endTimeMonth = `${year}-${month}-31`

    // 对应 Delphi 的三段拼接查询，做成单条 SQL 的多个子查询再关联
    const sql = `
      select bb.Sccj_Xl2,
             cc.dds,
             ifnull(dd.monthzj_xl2,0) as monthzj_xl2,
             (ifnull(dd.monthzj_xl2,0) - cc.dds) as ce,
             bb.zj_xl2
      from (
        select * from (
          select sum(dds) as zj_xl2, concat(ddlb,ddml,ddks) as Sccj_Xl2
          from (
            select xx_scdd.dds, xx_scdd.ddlb, xx_scdd.ddml, xx_scdd.ddks
            from xx_scdd, cc_sj_scdj
            where cc_sj_scdj.scid = xx_scdd.scid
              and xx_scdd.kh = ?
              and xx_scdd.xdrq >= ? and xx_scdd.xdrq <= ?
          ) a
          group by concat(ddlb,ddml,ddks)
        ) aa
      ) bb
      left join (
        select sum(dds)/12 as dds, concat(ddlb,ddml,ddks) as ddksddlb
        from xx_dd
        where xx_dd.kh = ?
          and xx_dd.xdrq >= ? and xx_dd.xdrq <= ?
        group by concat(ddlb,ddml,ddks)
      ) cc on bb.Sccj_Xl2 = cc.ddksddlb
      left join (
        select sum(dds) as monthzj_xl2, concat(ddlb,ddml,ddks) as ddksddlb
        from (
          select xx_scdd.dds, xx_scdd.ddlb, xx_scdd.ddml, xx_scdd.ddks
          from xx_scdd, cc_sj_scdj
          where cc_sj_scdj.scid = xx_scdd.scid
            and xx_scdd.kh = ?
            and xx_scdd.xdrq >= ? and xx_scdd.xdrq <= ?
        ) a
        group by concat(ddlb,ddml,ddks)
      ) dd on bb.Sccj_Xl2 = dd.ddksddlb
      order by bb.zj_xl2 desc
      limit 99
    `

    const rows = await query(sql, [
      'J B', beginTime, endTime,
      'J B', `${Number(year)}-01-01`, `${Number(year)+1}-01-01`,
      'J B', beginTimeMonth, endTimeMonth,
    ])

    const data = rows.map((r: any, idx: number) => ({
      rank: idx + 1,
      key: r.Sccj_Xl2,
      monthValue: Number(r.monthzj_xl2) || 0,
      avgValue: Number(r.dds) || 0,
      delta: Number(r.ce) || 0,
    }))

    return NextResponse.json({ success: true, data })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e?.message || '查询失败' }, { status: 500 })
  }
}


