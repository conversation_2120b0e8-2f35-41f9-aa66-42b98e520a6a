<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果过程分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">成果过程分析</h1>

        <!-- 筛选控制区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类别</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="category" class="text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="category" class="text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">专利</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="category" class="text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">论文</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部领域</option>
                        <option>电子信息</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>智能制造</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50">申请中</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">授权</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">失效</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">投稿中</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50">已发表</button>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    分析
                </button>
            </div>
        </div>

        <!-- 过程指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">专利申请量</p>
                        <p class="text-xl font-semibold text-gray-900">1,245</p>
                    </div>
                    <div class="flex items-center text-green-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span>12%</span>
                    </div>
                </div>
                <div class="h-12 mt-2">
                    <canvas id="sparkline1"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">专利授权率</p>
                        <p class="text-xl font-semibold text-gray-900">68%</p>
                    </div>
                    <div class="flex items-center text-red-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                        <span>3%</span>
                    </div>
                </div>
                <div class="h-12 mt-2">
                    <canvas id="sparkline2"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">平均授权周期</p>
                        <p class="text-xl font-semibold text-gray-900">156天</p>
                    </div>
                    <div class="flex items-center text-green-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span>8%</span>
                    </div>
                </div>
                <div class="h-12 mt-2">
                    <canvas id="sparkline3"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">论文发表量</p>
                        <p class="text-xl font-semibold text-gray-900">876</p>
                    </div>
                    <div class="flex items-center text-green-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span>15%</span>
                    </div>
                </div>
                <div class="h-12 mt-2">
                    <canvas id="sparkline4"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">期刊平均IF</p>
                        <p class="text-xl font-semibold text-gray-900">4.56</p>
                    </div>
                    <div class="flex items-center text-green-500 text-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        <span>0.3</span>
                    </div>
                </div>
                <div class="h-12 mt-2">
                    <canvas id="sparkline5"></canvas>
                </div>
            </div>
        </div>

        <!-- 专利过程分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-800">专利过程分析</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">发明</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">实用新型</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">外观设计</button>
                </div>
            </div>
            
            <div class="h-80">
                <canvas id="patentProcessChart"></canvas>
            </div>
        </div>

        <!-- 论文过程分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div class="lg:col-span-2 bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-medium text-gray-800 mb-4">论文投稿-发表趋势</h2>
                <div class="h-80">
                    <canvas id="paperTrendChart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-medium text-gray-800 mb-4">期刊等级分布</h2>
                <div class="h-80">
                    <canvas id="journalDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 对比趋势区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-medium text-gray-800">授权率与发表率对比</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">季度</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">半年</button>
                    <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">年度</button>
                </div>
            </div>
            
            <div class="h-80">
                <canvas id="comparisonChart"></canvas>
            </div>
        </div>

        <!-- 明细表格区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">成果明细</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">专利</button>
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">论文</button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前阶段</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">历时</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CN202310123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种基于深度学习的图像识别方法</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实质审查</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156天</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">10.1016/j.jcis.2023.05.123</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新型纳米材料在催化反应中的应用研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SCI论文</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">已发表</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">210天</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CN202320123456.X</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种智能家居控制系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">已授权</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">98天</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200 flex justify-between items-center">
                <div class="text-sm text-gray-500">
                    显示第 1-3 条，共 128 条记录
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                    <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>

        <!-- 输出与共享区 -->
        <div class="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg p-4">
            <div class="flex space-x-3">
                <button class="p-2 bg-blue-100 rounded-md text-blue-600 hover:bg-blue-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </button>
                <button class="p-2 bg-green-100 rounded-md text-green-600 hover:bg-green-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </button>
                <button class="p-2 bg-purple-100 rounded-md text-purple-600 hover:bg-purple-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
            </div>
            <div class="mt-2 w-full bg-gray-200 rounded-full h-1 hidden">
                <div class="bg-blue-600 h-1 rounded-full" style="width: 45%"></div>
            </div>
        </div>
    </div>

    <!-- 图表初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sparkline 图表
            const sparklineOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: { x: { display: false }, y: { display: false } },
                elements: { line: { tension: 0.4 } }
            };
            
            // 专利申请量趋势
            const sparkline1 = new Chart(
                document.getElementById('sparkline1'),
                {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [65, 59, 80, 81, 56, 55],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                }
            );
            
            // 专利授权率趋势
            const sparkline2 = new Chart(
                document.getElementById('sparkline2'),
                {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [72, 68, 65, 70, 67, 69],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                }
            );
            
            // 专利过程分析图表
            const patentProcessChart = new Chart(
                document.getElementById('patentProcessChart'),
                {
                    type: 'bar',
                    data: {
                        labels: ['申请', '初审', '实审', '授权', '维持', '失效'],
                        datasets: [
                            {
                                label: '数量',
                                data: [1245, 987, 756, 523, 423, 156],
                                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                                yAxisID: 'y'
                            },
                            {
                                label: '比例',
                                data: [100, 79.3, 60.7, 42.0, 34.0, 12.5],
                                borderColor: '#F59E0B',
                                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                type: 'line',
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                title: {
                                    display: true,
                                    text: '数量'
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                title: {
                                    display: true,
                                    text: '比例(%)'
                                },
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        }
                    }
                }
            );
            
            // 论文投稿-发表趋势
            const paperTrendChart = new Chart(
                document.getElementById('paperTrendChart'),
                {
                    type: 'line',
                    data: {
                        labels: ['2022Q1', '2022Q2', '2022Q3', '2022Q4', '2023Q1', '2023Q2'],
                        datasets: [
                            {
                                label: '投稿',
                                data: [45, 52, 68, 59, 72, 65],
                                borderColor: '#3B82F6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true
                            },
                            {
                                label: '录用',
                                data: [32, 38, 42, 45, 48, 52],
                                borderColor: '#10B981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                fill: true
                            },
                            {
                                label: '发表',
                                data: [28, 32, 36, 40, 42, 45],
                                borderColor: '#F59E0B',
                                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                }
            );
            
            // 期刊等级分布
            const journalDistributionChart = new Chart(
                document.getElementById('journalDistributionChart'),
                {
                    type: 'pie',
                    data: {
                        labels: ['SCI一区', 'SCI二区', 'SCI三区', 'SCI四区', 'EI', '其他'],
                        datasets: [{
                            data: [156, 234, 187, 123, 98, 78],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(139, 92, 246, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right'
                            }
                        }
                    }
                }
            );
            
            // 授权率与发表率对比
            const comparisonChart = new Chart(
                document.getElementById('comparisonChart'),
                {
                    type: 'line',
                    data: {
                        labels: ['2022Q1', '2022Q2', '2022Q3', '2022Q4', '2023Q1', '2023Q2'],
                        datasets: [
                            {
                                label: '专利授权率',
                                data: [68, 72, 70, 75, 73, 76],
                                borderColor: '#3B82F6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true,
                                yAxisID: 'y'
                            },
                            {
                                label: '论文发表率',
                                data: [62, 65, 68, 70, 72, 74],
                                borderColor: '#10B981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                fill: true,
                                yAxisID: 'y'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                min: 50,
                                max: 80,
                                ticks: {
                                    callback: function(value) {
                                        return value + '%';
                                    }
                                }
                            }
                        }
                    }
                }
            );
        });
    </script>
</body>
</html>