object CC_BankModelConfigFrame: TCC_BankModelConfigFrame
  Left = 0
  Top = 0
  Width = 714
  Height = 572
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel2: TRzPanel
    Left = 0
    Top = 190
    Width = 714
    Height = 382
    Align = alClient
    BorderOuter = fsNone
    TabOrder = 0
    object BankModelConfigAdvStringGrid: TAdvStringGrid
      Left = 0
      Top = 0
      Width = 714
      Height = 382
      Cursor = crDefault
      Align = alClient
      BevelInner = bvNone
      BevelOuter = bvNone
      Ctl3D = True
      DefaultRowHeight = 28
      DoubleBuffered = False
      DrawingStyle = gdsClassic
      FixedColor = clWhite
      FixedCols = 0
      RowCount = 8
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing, goTabs]
      ParentCtl3D = False
      ParentDoubleBuffered = False
      ParentFont = False
      PopupMenu = AdvPopupMenu2
      ScrollBars = ssVertical
      TabOrder = 0
      OnMouseDown = BankModelConfigAdvStringGridMouseDown
      OnMouseMove = BankModelConfigAdvStringGridMouseMove
      GridLineColor = ********
      GridFixedLineColor = ********
      HoverRowCells = [hcNormal, hcSelected]
      OnDblClickCell = BankModelConfigAdvStringGridDblClickCell
      OnAnchorClick = BankModelConfigAdvStringGridAnchorClick
      OnGetEditorType = BankModelConfigAdvStringGridGetEditorType
      OnGetEditorProp = BankModelConfigAdvStringGridGetEditorProp
      OnEditChange = BankModelConfigAdvStringGridEditChange
      HighlightColor = clNone
      ActiveCellFont.Charset = DEFAULT_CHARSET
      ActiveCellFont.Color = clWindowText
      ActiveCellFont.Height = -12
      ActiveCellFont.Name = #24494#36719#38597#40657
      ActiveCellFont.Style = [fsBold]
      ActiveCellColor = ********
      ActiveCellColorTo = 6210033
      ControlLook.FixedGradientFrom = ********
      ControlLook.FixedGradientTo = ********
      ControlLook.FixedGradientHoverFrom = ********
      ControlLook.FixedGradientHoverTo = ********
      ControlLook.FixedGradientHoverMirrorFrom = ********
      ControlLook.FixedGradientHoverMirrorTo = ********
      ControlLook.FixedGradientHoverBorder = ********
      ControlLook.FixedGradientDownFrom = ********
      ControlLook.FixedGradientDownTo = ********
      ControlLook.FixedGradientDownMirrorFrom = ********
      ControlLook.FixedGradientDownMirrorTo = ********
      ControlLook.FixedGradientDownBorder = ********
      ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownHeader.Font.Color = clWindowText
      ControlLook.DropDownHeader.Font.Height = -11
      ControlLook.DropDownHeader.Font.Name = 'Tahoma'
      ControlLook.DropDownHeader.Font.Style = []
      ControlLook.DropDownHeader.Visible = True
      ControlLook.DropDownHeader.Buttons = <>
      ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
      ControlLook.DropDownFooter.Font.Color = clWindowText
      ControlLook.DropDownFooter.Font.Height = -11
      ControlLook.DropDownFooter.Font.Name = 'Tahoma'
      ControlLook.DropDownFooter.Font.Style = []
      ControlLook.DropDownFooter.Visible = True
      ControlLook.DropDownFooter.Buttons = <>
      EnhRowColMove = False
      Filter = <>
      FilterDropDown.Font.Charset = DEFAULT_CHARSET
      FilterDropDown.Font.Color = clWindowText
      FilterDropDown.Font.Height = -11
      FilterDropDown.Font.Name = 'Tahoma'
      FilterDropDown.Font.Style = []
      FilterDropDownClear = '(All)'
      FilterEdit.TypeNames.Strings = (
        'Starts with'
        'Ends with'
        'Contains'
        'Not contains'
        'Equal'
        'Not equal'
        'Clear')
      FixedColWidth = 35
      FixedRowHeight = 28
      FixedRowAlways = True
      FixedFont.Charset = DEFAULT_CHARSET
      FixedFont.Color = clBlack
      FixedFont.Height = -12
      FixedFont.Name = #24494#36719#38597#40657
      FixedFont.Style = [fsBold]
      FloatFormat = '%.2f'
      HoverButtons.Buttons = <>
      HoverButtons.Position = hbLeftFromColumnLeft
      HTMLSettings.ImageFolder = 'images'
      HTMLSettings.ImageBaseName = 'img'
      Look = glOffice2007
      PrintSettings.DateFormat = 'dd/mm/yyyy'
      PrintSettings.Font.Charset = DEFAULT_CHARSET
      PrintSettings.Font.Color = clWindowText
      PrintSettings.Font.Height = -11
      PrintSettings.Font.Name = 'Tahoma'
      PrintSettings.Font.Style = []
      PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
      PrintSettings.FixedFont.Color = clWindowText
      PrintSettings.FixedFont.Height = -11
      PrintSettings.FixedFont.Name = 'Tahoma'
      PrintSettings.FixedFont.Style = []
      PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
      PrintSettings.HeaderFont.Color = clWindowText
      PrintSettings.HeaderFont.Height = -11
      PrintSettings.HeaderFont.Name = 'Tahoma'
      PrintSettings.HeaderFont.Style = []
      PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
      PrintSettings.FooterFont.Color = clWindowText
      PrintSettings.FooterFont.Height = -11
      PrintSettings.FooterFont.Name = 'Tahoma'
      PrintSettings.FooterFont.Style = []
      PrintSettings.PageNumSep = '/'
      SearchFooter.Color = ********
      SearchFooter.ColorTo = clNone
      SearchFooter.FindNextCaption = 'Find &next'
      SearchFooter.FindPrevCaption = 'Find &previous'
      SearchFooter.Font.Charset = DEFAULT_CHARSET
      SearchFooter.Font.Color = clWindowText
      SearchFooter.Font.Height = -11
      SearchFooter.Font.Name = 'Tahoma'
      SearchFooter.Font.Style = []
      SearchFooter.HighLightCaption = 'Highlight'
      SearchFooter.HintClose = 'Close'
      SearchFooter.HintFindNext = 'Find next occurrence'
      SearchFooter.HintFindPrev = 'Find previous occurrence'
      SearchFooter.HintHighlight = 'Highlight occurrences'
      SearchFooter.MatchCaseCaption = 'Match case'
      SelectionColor = 6210033
      SortSettings.DefaultFormat = ssAutomatic
      URLUnderlineOnHover = True
      UseInternalHintClass = False
      VAlignment = vtaCenter
      Version = '8.1.3.0'
      ColWidths = (
        35
        155
        253
        84
        64)
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 0
    Width = 714
    Height = 190
    Align = alTop
    BorderOuter = fsFlat
    Color = clWhite
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    object RzPanel5: TRzPanel
      Left = 1
      Top = 41
      Width = 712
      Height = 148
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object RzLabel1: TRzLabel
        Left = 16
        Top = 90
        Width = 60
        Height = 17
        Caption = #38134#34892#21517#31216#65306
      end
      object RzLabel2: TRzLabel
        Left = 16
        Top = 16
        Width = 60
        Height = 17
        Caption = #38134#34892#24207#21495#65306
      end
      object RzLabel3: TRzLabel
        Left = 16
        Top = 51
        Width = 36
        Height = 17
        Caption = #26174#31034#65306
      end
      object RzLabel4: TRzLabel
        Left = 16
        Top = 117
        Width = 76
        Height = 26
        Caption = #38134#34892#20449#24687
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel5: TRzLabel
        Left = 102
        Top = 121
        Width = 60
        Height = 19
        Caption = #24120#29992#25968#25454
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object Edit_Xh: TRzEdit
        Left = 79
        Top = 13
        Width = 250
        Height = 25
        Text = ''
        TabOrder = 0
        OnChange = Edit_XhChange
      end
      object ComboBox_Yh: TRzComboBox
        Left = 79
        Top = 86
        Width = 250
        Height = 25
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        OnChange = ComboBox_YhChange
        Items.Strings = (
          #24037#21830'  '#38134#34892
          #20892#19994'  '#38134#34892
          #20013#22269'  '#38134#34892
          #24314#35774'  '#38134#34892
          #20132#36890'  '#38134#34892
          #37038#25919#20648#33988'  '#38134#34892
          #20013#20449'  '#38134#34892
          #20809#22823'  '#38134#34892
          #21326#22799'  '#38134#34892
          #24191#21457'  '#38134#34892
          #24179#23433'  '#38134#34892
          #25307#21830'  '#38134#34892
          #28006#21457'  '#38134#34892
          #20852#19994'  '#38134#34892
          #27665#29983'  '#38134#34892
          #24658#20016'  '#38134#34892
          #27993#21830'  '#38134#34892)
      end
      object ComboBox_Xs: TRzComboBox
        Left = 79
        Top = 48
        Width = 250
        Height = 25
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        OnChange = ComboBox_XsChange
        Items.Strings = (
          #26159
          #21542)
      end
    end
    object RzPanel4: TRzPanel
      Left = 1
      Top = 1
      Width = 712
      Height = 40
      Align = alTop
      BorderOuter = fsFlat
      Color = clWhite
      TabOrder = 1
      object Btn_Save: TAdvGlowButton
        Left = 592
        Top = 6
        Width = 82
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 0
        OnClick = Btn_SaveClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
    end
  end
  object AdvPopupMenu2: TAdvPopupMenu
    Version = '2.6.2.1'
    Left = 655
    Top = 120
    object CopyRecord: TMenuItem
      Caption = #22797#21046#35760#24405
      OnClick = CopyRecordClick
    end
  end
end
