<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家详情展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题区 -->
        <div class="mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">专家详情展示</h1>
                    <p class="text-sm text-gray-500 mt-1">全面了解专家专业背景、学术贡献及科研能力</p>
                </div>
                <div class="flex space-x-3 mt-4 md:mt-0">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出简历报告
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center text-sm">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        联系专家
                    </button>
                </div>
            </div>
        </div>

        <!-- 基础信息区 -->
        <div class="bg-white rounded-lg shadow-md mb-6 overflow-hidden">
            <div class="p-6">
                <div class="flex flex-col md:flex-row md:items-start md:space-x-6">
                    <!-- 头像区域 -->
                    <div class="flex-shrink-0 mb-6 md:mb-0">
                        <div class="w-32 h-32 bg-gray-200 rounded-full overflow-hidden border-4 border-white shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-full h-full text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <button class="mt-3 w-full py-1.5 text-sm text-blue-600 border border-blue-600 rounded-md hover:bg-blue-50 transition-colors">
                            更换头像
                        </button>
                    </div>

                    <!-- 基本信息 -->
                    <div class="flex-1">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900">张明远</h2>
                                <div class="flex items-center mt-1">
                                    <span class="inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                                        正高级工程师
                                    </span>
                                    <span class="inline-flex px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        领军人才
                                    </span>
                                </div>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-2">
                                <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                                    </svg>
                                </button>
                                <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                </button>
                                <button class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z" />
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 详细信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4 text-sm">
                            <div>
                                <p class="text-gray-500">工作单位</p>
                                <p class="font-medium text-gray-900">宁波市智能装备研究院</p>
                            </div>
                            <div>
                                <p class="text-gray-500">技术领域</p>
                                <p class="font-medium text-gray-900">人工智能、智能制造、机器人技术</p>
                            </div>
                            <div>
                                <p class="text-gray-500">出生日期</p>
                                <p class="font-medium text-gray-900">1978-05-12 (45岁)</p>
                            </div>
                            <div>
                                <p class="text-gray-500">学历/学位</p>
                                <p class="font-medium text-gray-900">博士研究生/工学博士</p>
                            </div>
                            <div>
                                <p class="text-gray-500">毕业院校</p>
                                <p class="font-medium text-gray-900">浙江大学 机械工程专业</p>
                            </div>
                            <div>
                                <p class="text-gray-500">政治面貌</p>
                                <p class="font-medium text-gray-900">中共党员</p>
                            </div>
                            <div>
                                <p class="text-gray-500">电子邮箱</p>
                                <p class="font-medium text-gray-900"><EMAIL></p>
                            </div>
                            <div>
                                <p class="text-gray-500">联系电话</p>
                                <p class="font-medium text-gray-900">0574-87654321</p>
                            </div>
                            <div>
                                <p class="text-gray-500">专家标签</p>
                                <div class="flex flex-wrap gap-1 mt-1">
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">人工智能</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">智能制造</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">机器人</span>
                                    <span class="px-2 py-0.5 bg-gray-100 text-gray-800 rounded-full text-xs">机器学习</span>
                                </div>
                            </div>
                        </div>

                        <!-- 个人简介 -->
                        <div class="mt-6">
                            <p class="text-gray-500 mb-2">个人简介</p>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-sm text-gray-700 leading-relaxed">
                                    张明远，工学博士，正高级工程师，现任宁波市智能装备研究院副院长。长期从事人工智能与智能制造领域研究，主持国家级科研项目5项，省部级项目12项，发表SCI/EI论文40余篇，授权发明专利28项。曾获浙江省科技进步一等奖、宁波市杰出人才等荣誉。兼任浙江大学兼职教授、中国人工智能学会理事、浙江省机器人产业联盟副理事长等职。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成果信息区 -->
        <div class="bg-white rounded-lg shadow-md mb-6 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">科技成果</h2>
                    <div class="mt-3 md:mt-0 flex flex-wrap items-center gap-3">
                        <!-- 标签页切换 -->
                        <div class="inline-flex rounded-md shadow-sm" role="group">
                            <button type="button" id="patentTab" class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-l-lg hover:bg-blue-100 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700">
                                专利 (28)
                            </button>
                            <button type="button" id="paperTab" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border-t border-b border-gray-200 hover:bg-gray-50 hover:text-gray-900 focus:z-10 focus:ring-2 focus:ring-blue-500 focus:text-blue-700">
                                论文 (42)
                            </button>
                        </div>

                        <!-- 筛选条件 -->
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>全部类型</option>
                                <option>发明专利</option>
                                <option>实用新型</option>
                                <option>外观设计</option>
                            </select>
                            <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>近五年</option>
                                <option>近三年</option>
                                <option>近一年</option>
                                <option>全部时间</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 专利列表 -->
            <div id="patentContent" class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">一种基于深度学习的工业机器人视觉定位方法</div>
                                    <div class="text-xs text-gray-500 mt-1">智能制造</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202210345678.9</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-18</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一发明人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">一种自适应抓取的智能机械臂控制系统</div>
                                    <div class="text-xs text-gray-500 mt-1">机器人技术</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202110876543.2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11-05</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一发明人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">基于工业互联网的设备远程监控平台</div>
                                    <div class="text-xs text-gray-500 mt-1">工业互联网</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202010567890.1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-03-22</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第二发明人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">一种轻量化协作机器人结构</div>
                                    <div class="text-xs text-gray-500 mt-1">机器人技术</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202220456789.0</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一发明人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-500">
                        显示 1-4 条，共 28 条记录
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 论文列表 (默认隐藏) -->
            <div id="paperContent" class="p-6 hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">论文标题</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">期刊/会议</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发表时间</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者排序</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">影响因子</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Deep Learning-based Visual Servoing for Industrial Robots</div>
                                    <div class="text-xs text-gray-500 mt-1">IEEE Transactions on Industrial Electronics</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SCI 一区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">通讯作者</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7.893</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看全文</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">自适应控制在协作机器人中的应用研究</div>
                                    <div class="text-xs text-gray-500 mt-1">自动化学报</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">EI 核心</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一作者</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看全文</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">A Survey on Industrial Internet of Things: Architecture, Technologies and Applications</div>
                                    <div class="text-xs text-gray-500 mt-1">Journal of Network and Computer Applications</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SCI 二区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-05</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第二作者</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4.562</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看全文</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">基于数字孪生的智能制造系统构建与应用</div>
                                    <div class="text-xs text-gray-500 mt-1">中国机械工程</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中文核心</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-09</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一作者</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看全文</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-500">
                        显示 1-4 条，共 42 条记录
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 奖励信息区 -->
        <div class="bg-white rounded-lg shadow-md mb-6 overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">获奖荣誉</h2>
                    <div class="mt-3 md:mt-0 flex items-center space-x-2">
                        <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部级别</option>
                            <option>国家级</option>
                            <option>省部级</option>
                            <option>市级</option>
                        </select>
                        <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>近五年</option>
                            <option>近三年</option>
                            <option>全部时间</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="space-y-6">
                    <!-- 国家级奖励 -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <span class="w-2 h-2 bg-red-500 rounded-full mr-2"></span>
                            国家级奖励 (2项)
                        </h3>
                        <div class="space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-red-500">
                                <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">国家技术发明奖二等奖</h4>
                                        <p class="text-sm text-gray-500 mt-1">基于深度学习的工业机器人智能控制技术及应用</p>
                                    </div>
                                    <div class="mt-3 md:mt-0 text-right">
                                        <p class="text-sm font-medium text-gray-900">2021年</p>
                                        <p class="text-xs text-gray-500">中华人民共和国国务院</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        排名第2
                                    </span>
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        证书编号：2021-F-306-2-01-R02
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 省部级奖励 -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <span class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                            省部级奖励 (5项)
                        </h3>
                        <div class="space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-yellow-500">
                                <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">浙江省科技进步奖一等奖</h4>
                                        <p class="text-sm text-gray-500 mt-1">智能协作机器人关键技术及产业化应用</p>
                                    </div>
                                    <div class="mt-3 md:mt-0 text-right">
                                        <p class="text-sm font-medium text-gray-900">2022年</p>
                                        <p class="text-xs text-gray-500">浙江省人民政府</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        排名第1
                                    </span>
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        证书编号：2022JB-1-008-R01
                                    </span>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-yellow-500">
                                <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">中国机械工业科学技术奖一等奖</h4>
                                        <p class="text-sm text-gray-500 mt-1">工业机器人视觉伺服与柔顺控制技术</p>
                                    </div>
                                    <div class="mt-3 md:mt-0 text-right">
                                        <p class="text-sm font-medium text-gray-900">2020年</p>
                                        <p class="text-xs text-gray-500">中国机械工业联合会</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        排名第2
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 市级奖励 -->
                    <div>
                        <h3 class="text-md font-medium text-gray-900 mb-4 flex items-center">
                            <span class="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                            市级奖励 (8项)
                        </h3>
                        <div class="space-y-4">
                            <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500">
                                <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">宁波市杰出人才</h4>
                                        <p class="text-sm text-gray-500 mt-1">宁波市人才发展专项资金资助项目</p>
                                    </div>
                                    <div class="mt-3 md:mt-0 text-right">
                                        <p class="text-sm font-medium text-gray-900">2023年</p>
                                        <p class="text-xs text-gray-500">中共宁波市委、宁波市人民政府</p>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-lg border-l-4 border-blue-500">
                                <div class="flex flex-col md:flex-row md:items-start md:justify-between">
                                    <div>
                                        <h4 class="text-base font-semibold text-gray-900">宁波市科技进步奖一等奖</h4>
                                        <p class="text-sm text-gray-500 mt-1">工业机器人智能感知与自主决策技术</p>
                                    </div>
                                    <div class="mt-3 md:mt-0 text-right">
                                        <p class="text-sm font-medium text-gray-900">2021年</p>
                                        <p class="text-xs text-gray-500">宁波市人民政府</p>
                                    </div>
                                </div>
                                <div class="mt-3 flex flex-wrap gap-2">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                        排名第1
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目信息区 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">科研项目</h2>
                    <div class="mt-3 md:mt-0 flex flex-wrap items-center gap-3">
                        <div class="flex items-center space-x-2">
                            <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>全部类型</option>
                                <option>国家级</option>
                                <option>省部级</option>
                                <option>市级</option>
                                <option>横向项目</option>
                            </select>
                            <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>全部角色</option>
                                <option>项目负责人</option>
                                <option>核心参与人</option>
                            </select>
                            <select class="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option>全部状态</option>
                                <option>在研</option>
                                <option>已结题</option>
                                <option>暂停</option>
                            </select>
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center text-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            导出项目清单
                        </button>
                    </div>
                </div>
            </div>

            <div class="p-6">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目来源</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资助金额(万元)</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">基于数字孪生的智能工厂关键技术研究与应用</div>
                                    <div class="text-xs text-gray-500 mt-1">国家重点研发计划</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022YFB3303800</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-07 至 2025-06</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">865</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">人机协作机器人自适应控制技术研究</div>
                                    <div class="text-xs text-gray-500 mt-1">国家自然科学基金面上项目</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">62073196</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-01 至 2024-12</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">58</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">工业机器人智能感知与自主决策技术研究</div>
                                    <div class="text-xs text-gray-500 mt-1">浙江省重点研发计划项目</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省部级</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021C01008</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-01 至 2023-12</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已结题</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">智能工厂系统集成技术研发</div>
                                    <div class="text-xs text-gray-500 mt-1">企业横向合作项目</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">横向项目</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">HX20220056</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-03 至 2023-09</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">580</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已结题</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">工业互联网平台关键技术与应用</div>
                                    <div class="text-xs text-gray-500 mt-1">宁波市重大科技专项</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020Z011</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-06 至 2022-12</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">150</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">核心参与人</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已结题</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600">
                                    <button class="hover:text-blue-900">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="flex items-center justify-between mt-4">
                    <div class="text-sm text-gray-500">
                        显示 1-5 条，共 23 条记录
                    </div>
                    <div class="flex space-x-1">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 成果标签页切换
        document.getElementById('patentTab').addEventListener('click', function() {
            // 显示专利内容，隐藏论文内容
            document.getElementById('patentContent').classList.remove('hidden');
            document.getElementById('paperContent').classList.add('hidden');
            
            // 更新标签样式
            this.classList.remove('bg-white', 'text-gray-700', 'border-t', 'border-b', 'border-gray-200');
            this.classList.add('bg-blue-50', 'text-blue-600', 'border-blue-200');
            
            document.getElementById('paperTab').classList.remove('bg-blue-50', 'text-blue-600', 'border-blue-200');
            document.getElementById('paperTab').classList.add('bg-white', 'text-gray-700', 'border-t', 'border-b', 'border-gray-200');
        });
        
        document.getElementById('paperTab').addEventListener('click', function() {
            // 显示论文内容，隐藏专利内容
            document.getElementById('paperContent').classList.remove('hidden');
            document.getElementById('patentContent').classList.add('hidden');
            
            // 更新标签样式
            this.classList.remove('bg-white', 'text-gray-700', 'border-t', 'border-b', 'border-gray-200');
            this.classList.add('bg-blue-50', 'text-blue-600', 'border-blue-200');
            
            document.getElementById('patentTab').classList.remove('bg-blue-50', 'text-blue-600', 'border-blue-200');
            document.getElementById('patentTab').classList.add('bg-white', 'text-gray-700', 'border-t', 'border-b', 'border-gray-200');
        });
        
        // 详情查看模态框
        function openDetailModal() {
            // 实际项目中这里会打开模态框
            alert('详情查看功能将在后续版本中实现');
        }
        
        // 为所有"查看详情"按钮添加点击事件
        document.querySelectorAll('button.hover\\:text-blue-900').forEach(button => {
            button.addEventListener('click', openDetailModal);
        });
    </script>
</body>
</html>