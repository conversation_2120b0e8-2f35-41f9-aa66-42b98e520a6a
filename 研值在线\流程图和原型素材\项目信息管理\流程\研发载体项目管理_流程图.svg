<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发载体项目管理业务流程</text>

  <!-- 阶段一：查询与检索 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：查询与检索</text>
  
  <!-- 节点1: 条件查询 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件查询与筛选</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">用户设定检索条件</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">系统筛选研发载体项目并展示</text>
  </g>

  <!-- 阶段二：项目管理操作 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：项目管理操作</text>

  <!-- 节点2: 新增项目 -->
  <g transform="translate(150, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">数据模板及校验</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">录入基础信息及各要素</text>
  </g>

  <!-- 节点3: 编辑删除 -->
  <g transform="translate(420, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑删除</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">权限控制与操作日志</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">信息更新或逻辑删除</text>
  </g>

  <!-- 节点4: 批量导入 -->
  <g transform="translate(690, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">标准模板上传校验</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">自动归档导入记录</text>
  </g>

  <!-- 节点5: 项目详情 -->
  <g transform="translate(960, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">查看全部信息与历史版本</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">科技成果数据联动</text>
  </g>

  <!-- 阶段三：数据处理与输出 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与输出</text>

  <!-- 节点6: 数据导出 -->
  <g transform="translate(400, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">按条件导出项目列表</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">生成结构化Excel文件</text>
  </g>

  <!-- 节点7: 安全审计 -->
  <g transform="translate(750, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">安全审计</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">导出操作纳入审计</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">确保数据安全合规</text>
  </g>

  <!-- 阶段四：全程追溯 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：全程追溯</text>
  
  <!-- 节点8: 业务日志与溯源 -->
  <g transform="translate(500, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">业务日志与全程溯源</text>
    <text x="200" y="55" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">自动生成日志</tspan>
      <tspan dx="60">审计分析</tspan>
      <tspan dx="60">数据留痕</tspan>
    </text>
  </g>

  <!-- 连接线 1 -> 2,3,4,5 (曲线) -->
  <path d="M 650 210 C 600 250, 350 280, 260 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 210 C 700 250, 530 280, 530 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 210 C 750 250, 800 280, 800 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 210 C 850 250, 1000 280, 1070 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2,3,4,5 -> 6,7 (曲线) -->
  <path d="M 260 400 C 350 450, 450 480, 500 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 530 400 C 600 450, 700 480, 750 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 400 C 800 450, 800 480, 800 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1070 400 C 1000 450, 900 480, 875 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6,7 -> 8 (曲线) -->
  <path d="M 525 600 C 600 650, 650 680, 650 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 875 600 C 800 650, 750 680, 750 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从业务日志回到查询检索 -->
  <path d="M 500 760 C 200 820, 100 400, 200 200 C 300 100, 500 80, 650 130" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="850" font-size="12" fill="#666">持续优化反馈</text>

</svg>