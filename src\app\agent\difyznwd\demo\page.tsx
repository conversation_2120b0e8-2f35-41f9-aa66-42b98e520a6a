'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageSquare } from 'lucide-react'

export default function DifyDemoPage() {
  return (
    <div className="h-screen bg-gradient-to-b from-blue-50/30 to-white/80 p-4">
      <Card className="h-full shadow-lg rounded-xl border-border/50">
        <CardHeader className="border-b bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-xl">
          <CardTitle className="flex items-center gap-3 text-xl">
            <MessageSquare className="h-6 w-6 text-blue-600" />
            甬知AI智能问答助手 - 界面调整演示
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 p-6 bg-gradient-to-b from-blue-50/30 to-white/80">
          <div className="space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h2 className="text-lg font-semibold mb-4 text-gray-800">界面调整完成</h2>
              
              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">✅ 已完成的调整：</h3>
                  <ul className="space-y-1 ml-4">
                    <li>• <strong>提问人的头像</strong> - 蓝色背景 + 白色用户图标 + 蓝色边框</li>
                    <li>• <strong>助手的头像</strong> - 灰色背景 + 灰色机器人图标 + 灰色边框</li>
                    <li>• <strong>用户消息样式</strong> - 白色背景 + 黑色文字 + 灰色边框</li>
                    <li>• <strong>回答内容的区域灰色底色</strong> - 助手消息使用灰色容器 + 白色内容卡片</li>
                    <li>• <strong>底部时间+复制+重新执行等常用按钮</strong> - 完整的操作按钮组</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">🎯 具体功能：</h3>
                  <ul className="space-y-1 ml-4">
                    <li>• 头像区分：用户蓝色，助手灰色</li>
                    <li>• 消息样式：用户白底黑字，助手灰色容器+白色卡片</li>
                    <li>• 消息布局：用户消息右对齐，助手消息左对齐</li>
                    <li>• 操作按钮：复制、点赞、点踩、重新生成（悬停显示）</li>
                    <li>• 时间显示：格式化的时间戳，颜色适配消息类型</li>
                    <li>• Token信息：显示使用量和响应时间</li>
                    <li>• 复制反馈：复制成功时显示绿色勾号</li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">📝 使用说明：</h3>
                  <ul className="space-y-1 ml-4">
                    <li>• 访问 <code className="bg-gray-100 px-2 py-1 rounded">/agent/difyznwd</code> 查看实际效果</li>
                    <li>• 需要输入访问密钥 <code className="bg-gray-100 px-2 py-1 rounded">zscq</code></li>
                    <li>• 发送消息后可以看到新的界面样式</li>
                    <li>• 鼠标悬停在助手消息上可以看到操作按钮</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800 mb-2">💡 设计亮点</h3>
              <p className="text-sm text-blue-700">
                界面调整参考了您提供的截图，实现了清晰的视觉层次和良好的用户体验。
                头像颜色区分用户和助手，灰色底色让回答内容更加突出，底部操作按钮提供了完整的交互功能。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
