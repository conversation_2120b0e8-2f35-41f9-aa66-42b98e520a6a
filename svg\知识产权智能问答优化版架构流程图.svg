<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">知识产权智能问答系统优化版架构</text>
  <text x="600" y="65" font-size="16" text-anchor="middle" fill="#555">内外双活、统一网关、集中治理</text>

  <!-- 接入层 -->
  <text x="600" y="120" font-size="20" text-anchor="middle" font-weight="500" fill="#1d4ed8">接入层 (Presentation Layer)</text>
  
  <!-- H5智能问答页面 -->
  <g transform="translate(200, 150)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">H5 智能问答页面</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">微信自定义菜单 / 关键词回复</text>
    <text x="140" y="60" text-anchor="middle" font-size="12" fill="#555">公共服务平台浮窗、内部平台入口</text>
  </g>

  <!-- OAuth认证 -->
  <g transform="translate(520, 150)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">浙政钉 / 微信 OAuth</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">用户身份解析</text>
    <text x="140" y="60" text-anchor="middle" font-size="12" fill="#555">生成最小权限访问 Token</text>
  </g>

  <!-- 业务层 -->
  <text x="600" y="290" font-size="20" text-anchor="middle" font-weight="500" fill="#059669">业务层 (Application Layer)</text>
  
  <!-- 外网智能问答应用 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能问答应用</text>
    <text x="100" y="40" text-anchor="middle" font-size="14" font-weight="500" fill="#059669">(外网实例)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">部署在互联网区</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">负责公众流量</text>
  </g>

  <!-- 内网智能问答应用 -->
  <g transform="translate(350, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能问答应用</text>
    <text x="100" y="40" text-anchor="middle" font-size="14" font-weight="500" fill="#059669">(内网实例)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">部署在内部平台区</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">服务内部人员</text>
  </g>

  <!-- 统一智能体服务 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统一智能体服务</text>
    <text x="150" y="40" text-anchor="middle" font-size="12" fill="#555">会话管理与限流</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">内容安全四级检测</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">模型路由策略</text>
  </g>

  <!-- AI服务层 -->
  <text x="600" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#7c3aed">AI 服务层 (Model Layer)</text>
  
  <!-- DeepSeek-R1 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">DeepSeek-R1 GPU 集群</text>
    <text x="150" y="40" text-anchor="middle" font-size="12" fill="#555">内部平台区 - 主力推理</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">保证数据不出域</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">可开放给外网实例使用</text>
  </g>

  <!-- 互联网大模型API -->
  <g transform="translate(550, 490)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">互联网大模型 API</text>
    <text x="150" y="40" text-anchor="middle" font-size="12" fill="#555">按量计费，应对公众高并发</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">返回结果经 Gateway 统一过滤</text>
  </g>

  <!-- 知识层 -->
  <text x="600" y="630" font-size="20" text-anchor="middle" font-weight="500" fill="#ea580c">知识层 (Knowledge Layer)</text>
  
  <!-- 智能体平台 -->
  <g transform="translate(350, 660)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能体平台 (双域)</text>
    <text x="150" y="40" text-anchor="middle" font-size="12" fill="#555">文档切分、Embedding</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">RAG 检索</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">双域各一套部署</text>
  </g>

  <!-- 右侧治理层 -->
  <text x="950" y="120" font-size="18" font-weight="500" fill="#dc2626">数据治理与安全层</text>
  
  <!-- 四级安全检测 -->
  <g transform="translate(850, 150)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#FEF2F2" stroke="#FCA5A5" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">四级敏感词检测</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">请求、Prompt、检索结果、回复</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">违规内容即时阻断</text>
    <text x="140" y="70" text-anchor="middle" font-size="12" fill="#555">安全策略引擎过滤</text>
  </g>

  <!-- 日志审计 -->
  <g transform="translate(850, 270)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#FEF2F2" stroke="#FCA5A5" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志与审计</text>
    <text x="140" y="40" text-anchor="middle" font-size="12" fill="#555">外网会话匿名化存储 7 天</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">内网日志内网服务器 180 天</text>
    <text x="140" y="70" text-anchor="middle" font-size="12" fill="#555">支持全链路溯源</text>
  </g>

  <!-- 主要流程箭头 -->
  
  <!-- 接入层内部连接 -->
  <path d="M 480 190 C 500 190, 500 190, 520 190" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 接入层到业务层 -->
  <path d="M 340 230 C 340 260, 200 290, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 660 230 C 660 260, 450 290, 450 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 业务层到统一智能体服务 -->
  <path d="M 300 360 C 450 360, 550 360, 600 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 360 C 575 360, 575 360, 600 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 统一智能体服务到AI服务层 -->
  <path d="M 700 400 C 700 430, 350 460, 350 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 400 C 800 430, 700 460, 700 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 统一智能体服务到知识层 -->
  <path d="M 750 400 C 750 530, 500 630, 500 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 到治理层的连接 -->
  <path d="M 900 360 C 920 360, 920 190, 850 190" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 路由标签 -->
  <text x="425" y="450" font-size="12" text-anchor="middle" fill="#7c3aed" font-weight="500">内网→DeepSeek-R1</text>
  <text x="775" y="450" font-size="12" text-anchor="middle" fill="#7c3aed" font-weight="500">外网→大模型API</text>
  
  <!-- 整体流程标签 -->
  <text x="600" y="820" font-size="14" text-anchor="middle" font-weight="500" fill="#333">整体流程：</text>
  <text x="600" y="840" font-size="12" text-anchor="middle" fill="#555">用户 → 接入层 → 智能问答应用 → 统一智能体服务 →</text>
  <text x="600" y="860" font-size="12" text-anchor="middle" fill="#555">① 路由模型（DeepSeek-R1 或 API）② 并行拉取向量检索结果 → 四级内容安全 → 统一格式化 → 返回前端</text>

  <!-- 核心理念标签 -->
  <g transform="translate(50, 780)" filter="url(#soft-shadow)">
    <rect width="500" height="60" rx="8" ry="8" fill="#F0F9FF" stroke="#0EA5E9" stroke-width="2" />
    <text x="250" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#0EA5E9">核心架构理念</text>
    <text x="250" y="45" text-anchor="middle" font-size="12" fill="#555">内外双活 + 统一网关 + 集中治理 → 弹性扩容 + 数据可控 + 快速响应</text>
  </g>

</svg> 