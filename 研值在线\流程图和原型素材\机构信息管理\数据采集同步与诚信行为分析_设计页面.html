<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据采集同步与诚信行为分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">数据采集同步与诚信行为分析</h1>
            <p class="text-gray-600">构建自动化机构诚信信息管理与分析闭环，提供失信记录采集、匹配与可视化分析能力</p>
        </div>

        <!-- 选项卡导航 -->
        <div class="mb-6">
            <div class="flex border-b border-gray-200">
                <button class="px-4 py-2 font-medium text-blue-600 border-b-2 border-blue-600">待处理记录管理</button>
                <button class="px-4 py-2 font-medium text-gray-500 hover:text-blue-600">诚信行为分析仪表盘</button>
            </div>
        </div>

        <!-- 待处理记录管理 -->
        <div class="space-y-6">
            <!-- 条件筛选区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    条件筛选
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导入日期范围</label>
                        <div class="flex space-x-2">
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="flex items-center text-gray-500">至</span>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">错误类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="match_failed">匹配失败</option>
                            <option value="field_missing">字段缺失</option>
                            <option value="duplicate">重复记录</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">处理状态</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="pending">待处理</option>
                            <option value="processed">已处理</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">机构类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="enterprise">企业</option>
                            <option value="university">高校</option>
                            <option value="institute">科研机构</option>
                        </select>
                    </div>
                </div>
                <div class="flex space-x-3 mt-4">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                </div>
            </div>

            <!-- 待处理记录列表 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            待处理记录列表
                        </h2>
                        <div class="flex space-x-2">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                批量处理
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失信记录摘要</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">错误原因</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">机构类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">处理状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">导入时间</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">宁波XX科技公司 - 未按时缴纳社保</div>
                                    <div class="text-sm text-gray-500">统一信用代码: 91330200MA2XXXXXX</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">匹配失败</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">企业</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">待处理</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-20 14:30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="openProcessModal()" class="text-blue-600 hover:text-blue-900">处理</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">宁波XX大学 - 科研经费使用违规</div>
                                    <div class="text-sm text-gray-500">统一信用代码: 1234567890XXXXXX</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">字段缺失</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高校</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">待处理</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-18 09:15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="openProcessModal()" class="text-blue-600 hover:text-blue-900">处理</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">宁波XX研究院 - 数据造假</div>
                                    <div class="text-sm text-gray-500">统一信用代码: 91330200MA2YYYYYY</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">匹配失败</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研机构</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已处理</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15 16:45</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="openProcessModal()" class="text-blue-600 hover:text-blue-900">查看</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 诚信行为分析仪表盘 -->
        <div class="hidden space-y-6">
            <!-- 分析维度筛选器 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    分析维度筛选
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1_year">近一年</option>
                            <option value="6_month">近半年</option>
                            <option value="3_month">近三个月</option>
                            <option value="1_month">近一个月</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">机构类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="enterprise">企业</option>
                            <option value="university">高校</option>
                            <option value="institute">科研机构</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">信用等级</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="A">A级</option>
                            <option value="B">B级</option>
                            <option value="C">C级</option>
                            <option value="D">D级</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">失信类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="tax">税务违规</option>
                            <option value="social_security">社保违规</option>
                            <option value="environment">环保违规</option>
                            <option value="research">科研违规</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end mt-4">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        应用筛选
                    </button>
                </div>
            </div>

            <!-- 数据图表展示区 -->
            <div class="space-y-6">
                <!-- 失信行为分布图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            失信行为分布
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            导出图表
                        </button>
                    </div>
                    <div id="behavior-distribution-chart" class="w-full h-[400px]"></div>
                </div>

                <!-- 失信行为趋势图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l5-5 5 5m-5 5l5-5 5 5"></path>
                            </svg>
                            失信行为趋势
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            导出图表
                        </button>
                    </div>
                    <div id="behavior-trend-chart" class="w-full h-[400px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 处理记录模态框 -->
    <div id="processModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">处理失信记录</h3>
                    <button onclick="closeProcessModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 失信记录详情 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">失信记录详情</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">机构名称</label>
                                    <p class="text-sm text-gray-900">宁波XX科技公司</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">统一信用代码</label>
                                    <p class="text-sm text-gray-900">91330200MA2XXXXXX</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">失信行为</label>
                                    <p class="text-sm text-gray-900">未按时缴纳社保</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">发生时间</label>
                                    <p class="text-sm text-gray-900">2023-05-15</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">错误原因</label>
                                    <p class="text-sm text-gray-900">匹配失败 - 未找到对应机构</p>
                                </div>
                            </div>
                        </div>
                        <!-- 机构匹配 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4">机构匹配</h4>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">搜索机构</label>
                                    <div class="flex space-x-2">
                                        <input type="text" placeholder="输入机构名称或信用代码" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            搜索
                                        </button>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-md p-4 h-48 overflow-y-auto">
                                    <div class="space-y-2">
                                        <div class="p-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100">
                                            <p class="text-sm font-medium text-gray-900">宁波XX科技有限公司</p>
                                            <p class="text-xs text-gray-500">统一信用代码: 91330200MA2XXXXXX</p>
                                        </div>
                                        <div class="p-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100">
                                            <p class="text-sm font-medium text-gray-900">宁波XX科技发展有限公司</p>
                                            <p class="text-xs text-gray-500">统一信用代码: 91330200MA2YYYYYY</p>
                                        </div>
                                        <div class="p-2 hover:bg-gray-50 cursor-pointer">
                                            <p class="text-sm font-medium text-gray-900">宁波XX科技集团有限公司</p>
                                            <p class="text-xs text-gray-500">统一信用代码: 91330200MA2ZZZZZZ</p>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">匹配说明</label>
                                    <textarea rows="3" placeholder="请输入匹配说明（可选）" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeProcessModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        确认匹配
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 处理记录模态框
        function openProcessModal() {
            document.getElementById('processModal').classList.remove('hidden');
        }
        
        function closeProcessModal() {
            document.getElementById('processModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('processModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProcessModal();
            }
        });

        // 选项卡切换
        document.querySelectorAll('button').forEach(button => {
            button.addEventListener('click', function() {
                if (this.textContent === '诚信行为分析仪表盘') {
                    document.querySelector('.space-y-6').classList.add('hidden');
                    document.querySelectorAll('.space-y-6')[1].classList.remove('hidden');
                    document.querySelectorAll('button')[0].classList.remove('border-b-2', 'border-blue-600', 'text-blue-600');
                    document.querySelectorAll('button')[0].classList.add('text-gray-500');
                    this.classList.add('border-b-2', 'border-blue-600', 'text-blue-600');
                    this.classList.remove('text-gray-500');
                    
                    // 初始化图表
                    initCharts();
                } else if (this.textContent === '待处理记录管理') {
                    document.querySelector('.space-y-6').classList.remove('hidden');
                    document.querySelectorAll('.space-y-6')[1].classList.add('hidden');
                    document.querySelectorAll('button')[1].classList.remove('border-b-2', 'border-blue-600', 'text-blue-600');
                    document.querySelectorAll('button')[1].classList.add('text-gray-500');
                    this.classList.add('border-b-2', 'border-blue-600', 'text-blue-600');
                    this.classList.remove('text-gray-500');
                }
            });
        });

        // 初始化图表
        function initCharts() {
            // 失信行为分布图
            const distributionChart = echarts.init(document.getElementById('behavior-distribution-chart'));
            const distributionOption = {
                title: {
                    text: '失信行为类型分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    data: ['税务违规', '社保违规', '环保违规', '科研违规', '其他']
                },
                series: [
                    {
                        name: '失信行为类型',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 335, name: '税务违规', itemStyle: { color: '#5470C6' } },
                            { value: 310, name: '社保违规', itemStyle: { color: '#91CC75' } },
                            { value: 234, name: '环保违规', itemStyle: { color: '#FAC858' } },
                            { value: 135, name: '科研违规', itemStyle: { color: '#EE6666' } },
                            { value: 154, name: '其他', itemStyle: { color: '#73C0DE' } }
                        ]
                    }
                ]
            };
            distributionChart.setOption(distributionOption);

            // 失信行为趋势图
            const trendChart = echarts.init(document.getElementById('behavior-trend-chart'));
            const trendOption = {
                title: {
                    text: '失信行为趋势',
                    left: 'center',
                    textStyle: {
                        fontSize: 16,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['企业', '高校', '科研机构'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                    axisLine: {
                        lineStyle: {
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: '#999'
                        }
                    },
                    axisLabel: {
                        color: '#666'
                    },
                    splitLine: {
                        lineStyle: {
                            type: 'dashed',
                            color: '#eee'
                        }
                    }
                },
                series: [
                    {
                        name: '企业',
                        type: 'line',
                        smooth: true,
                        data: [120, 132, 101, 134, 90, 230, 210],
                        itemStyle: {
                            color: '#5470C6'
                        },
                        lineStyle: {
                            width: 3
                        },
                        symbolSize: 8
                    },
                    {
                        name: '高校',
                        type: 'line',
                        smooth: true,
                        data: [60, 72, 51, 74, 40, 130, 110],
                        itemStyle: {
                            color: '#91CC75'
                        },
                        lineStyle: {
                            width: 3
                        },
                        symbolSize: 8
                    },
                    {
                        name: '科研机构',
                        type: 'line',
                        smooth: true,
                        data: [20, 32, 21, 34, 30, 50, 40],
                        itemStyle: {
                            color: '#EE6666'
                        },
                        lineStyle: {
                            width: 3
                        },
                        symbolSize: 8
                    }
                ]
            };
            trendChart.setOption(trendOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                distributionChart.resize();
                trendChart.resize();
            });
        }
    </script>
</body>
</html>