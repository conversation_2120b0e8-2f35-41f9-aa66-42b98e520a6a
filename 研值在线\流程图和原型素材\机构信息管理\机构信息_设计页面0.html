<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题与搜索区 -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">机构信息管理</h1>
                    <p class="text-gray-600">整合宁波市各类研发主体的多维度数据，提供一站式信息检索与决策支持</p>
                </div>
                <div class="relative w-96">
                    <input type="text" placeholder="搜索机构名称/统一社会信用代码..." class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button class="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 基本信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            基本信息
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            编辑
                        </button>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">机构名称</p>
                            <p class="text-sm font-medium text-gray-900">宁波市智能科技研究院</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">统一社会信用代码</p>
                            <p class="text-sm font-medium text-gray-900">91330201MA2XXXXXX</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">机构类型</p>
                            <p class="text-sm font-medium text-gray-900">科研院所</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">法定代表人</p>
                            <p class="text-sm font-medium text-gray-900">张伟</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">联系人</p>
                            <p class="text-sm font-medium text-gray-900">王芳 / 0574-88XXXXXX</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">成立日期</p>
                            <p class="text-sm font-medium text-gray-900">2018-05-15</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">注册资本</p>
                            <p class="text-sm font-medium text-gray-900">5000万元</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">人员规模</p>
                            <p class="text-sm font-medium text-gray-900">中型（150人）</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">注册地址</p>
                            <p class="text-sm font-medium text-gray-900">宁波市鄞州区科技大道88号</p>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-gray-700 mr-4">风险状态:</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="-ml-0.5 mr-1.5 h-2 w-2 text-green-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3"></circle>
                                </svg>
                                正常
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 机构层级可视化 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            机构层级关系
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            刷新
                        </button>
                    </div>
                    
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-center">
                            <div class="text-center">
                                <div class="bg-blue-50 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-2">
                                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <p class="text-sm font-medium text-gray-900">宁波市智能科技研究院</p>
                                <p class="text-xs text-gray-500">母公司</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-center mt-4">
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 13l-7 7-7-7m14-8l-7 7-7-7"></path>
                            </svg>
                        </div>
                        
                        <div class="grid grid-cols-3 gap-4 mt-2">
                            <div class="text-center">
                                <div class="bg-gray-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <p class="text-xs font-medium text-gray-900">宁波智能科技研发中心</p>
                                <p class="text-xs text-gray-500">研发机构</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-gray-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <p class="text-xs font-medium text-gray-900">宁波智能科技产业园</p>
                                <p class="text-xs text-gray-500">生产基地</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-gray-50 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-2">
                                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                </div>
                                <p class="text-xs font-medium text-gray-900">宁波智能科技投资公司</p>
                                <p class="text-xs text-gray-500">投资机构</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计指标区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            统计指标
                        </h2>
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                PNG
                            </button>
                            <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                Excel
                            </button>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">项目参与数</p>
                                    <p class="text-2xl font-bold text-blue-600">86</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,30 30,10 50,20 70,5 90,25" fill="none" stroke="#3b82f6" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">承担项目数</p>
                                    <p class="text-2xl font-bold text-green-600">32</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,35 30,15 50,25 70,10 90,30" fill="none" stroke="#10b981" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">发明专利数</p>
                                    <p class="text-2xl font-bold text-purple-600">24</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,20 30,10 50,30 70,15 90,35" fill="none" stroke="#8b5cf6" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">实用新型专利</p>
                                    <p class="text-2xl font-bold text-yellow-600">45</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,25 30,15 50,35 70,20 90,30" fill="none" stroke="#f59e0b" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">风险预警</p>
                                    <p class="text-2xl font-bold text-red-600">2</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,15 30,35 50,25 70,30 90,10" fill="none" stroke="#ef4444" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="bg-indigo-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-500">软件著作权</p>
                                    <p class="text-2xl font-bold text-indigo-600">18</p>
                                </div>
                                <div class="w-20 h-12">
                                    <svg viewBox="0 0 100 40" class="w-full h-full">
                                        <polyline points="10,30 30,20 50,35 70,15 90,25" fill="none" stroke="#6366f1" stroke-width="2"></polyline>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 荣誉与资质区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m8-8v13m-7-13h-2v5h2V8zm-4 0H9v5h2V8zm6-5h2v5h-2V3zm-4 0h-2v5h2V3zM7 8H5v5h2V8zm-4-5H1v5h2V3z"></path>
                                </svg>
                                荣誉与资质
                            </h2>
                            <div class="flex space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                    新增
                                </button>
                                <button class="text-blue-600 hover:text-blue-800 flex items-center text-sm">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    批量导入
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书编号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发证机关</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高新技术企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">GR20223310XXXX</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-12-01 至 2025-11-30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技部</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900 ml-2">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">专精特新企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZJTX202210XXXX</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-08-15 至 2025-08-14</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省经信厅</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900 ml-2">删除</button>
                                    </td>
                                </tr>
                                <tr class="bg-red-50 hover:bg-red-100">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">ISO9001认证</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ISO9001-2018-XXXX</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国际</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019-05-20 至 2023-05-19</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SGS</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900 ml-2">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市科技型企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NBKJ2021XXXX</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-11-01 至 2024-10-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看</button>
                                        <button class="text-red-600 hover:text-red-900 ml-2">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">12</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 经营数据区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        经营数据
                    </h2>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">资产总额</span>
                            <span class="text-sm font-medium text-gray-900">1.25亿元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">负债总额</span>
                            <span class="text-sm font-medium text-gray-900">0.45亿元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">营业收入</span>
                            <span class="text-sm font-medium text-gray-900">0.68亿元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">净利润</span>
                            <span class="text-sm font-medium text-gray-900">0.12亿元</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-500">纳税额</span>
                            <span class="text-sm font-medium text-gray-900">0.08亿元</span>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="h-40">
                            <svg viewBox="0 0 300 150" class="w-full h-full">
                                <rect x="20" y="30" width="40" height="120" fill="#3b82f6" opacity="0.7"></rect>
                                <rect x="80" y="50" width="40" height="100" fill="#3b82f6" opacity="0.7"></rect>
                                <rect x="140" y="70" width="40" height="80" fill="#3b82f6" opacity="0.7"></rect>
                                <rect x="200" y="40" width="40" height="110" fill="#3b82f6" opacity="0.7"></rect>
                                <rect x="260" y="60" width="40" height="90" fill="#3b82f6" opacity="0.7"></rect>
                                <polyline points="20,30 80,50 140,70 200,40 260,60" fill="none" stroke="#1d4ed8" stroke-width="2"></polyline>
                            </svg>
                        </div>
                        <div class="flex justify-between text-xs text-gray-500 mt-2">
                            <span>2020</span>
                            <span>2021</span>
                            <span>2022</span>
                            <span>2023</span>
                            <span>2024</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            下载PDF报表
                        </button>
                    </div>
                </div>

                <!-- 辅助功能区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        辅助功能
                    </h2>
                    
                    <div class="space-y-3">
                        <button class="w-full px-4 py-2 bg-white border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            编辑全部信息
                        </button>
                        <button class="w-full px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            历史版本
                        </button>
                        <button class="w-full px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            风险扫描
                        </button>
                        <button class="w-full px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12"></path>
                            </svg>
                            数据导出
                        </button>
                        <button class="w-full px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                            </svg>
                            打印
                        </button>
                    </div>
                </div>

                <!-- 风险预警区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                        </svg>
                        风险预警
                    </h2>
                    
                    <div class="space-y-3">
                        <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500">
                            <div class="flex items-start">
                                <svg class="h-4 w-4 text-red-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-red-800">ISO9001认证已过期</p>
                                    <p class="text-xs text-red-700 mt-1">有效期至2023-05-19</p>
                                </div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-500">
                            <div class="flex items-start">
                                <svg class="h-4 w-4 text-yellow-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-yellow-800">2023年纳税额下降30%</p>
                                    <p class="text-xs text-yellow-700 mt-1">建议关注企业经营状况</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑机构信息抽屉 -->
    <div id="editDrawer" class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform transition-transform duration-300 translate-x-full z-50">
        <div class="h-full flex flex-col">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">编辑机构信息</h3>
                <button onclick="closeEditDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">机构名称 <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市智能科技研究院">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码 <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="91330201MA2XXXXXX">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">机构类型 <span class="text-red-500">*</span></label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="enterprise">企业</option>
                            <option value="university">高校</option>
                            <option value="institute" selected>科研院所</option>
                            <option value="individual">个人</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">法定代表人 <span class="text-red-500">*</span></label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张伟">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">联系人信息 <span class="text-red-500">*</span></label>
                        <div class="grid grid-cols-2 gap-4">
                            <input type="text" placeholder="联系人姓名" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="王芳">
                            <input type="text" placeholder="联系电话" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="0574-88XXXXXX">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">注册地址 <span class="text-red-500">*</span></label>
                        <textarea rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">宁波市鄞州区科技大道88号</textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">风险状态</label>
                        <div class="flex items-center">
                            <input type="checkbox" id="riskStatus" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="riskStatus" class="ml-2 block text-sm text-gray-700">标记为正常状态</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="p-6 border-t bg-gray-50">
                <div class="flex justify-end space-x-3">
                    <button onclick="closeEditDrawer()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        保存更改
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 编辑抽屉控制
        function openEditDrawer() {
            document.getElementById('editDrawer').classList.remove('translate-x-full');
        }
        
        function closeEditDrawer() {
            document.getElementById('editDrawer').classList.add('translate-x-full');
        }
        
        // 风险扫描模拟
        function runRiskScan() {
            alert('风险扫描已完成，发现2个潜在风险点');
        }
        
        // 点击外部关闭抽屉
        document.addEventListener('click', function(e) {
            const drawer = document.getElementById('editDrawer');
            if (!drawer.contains(e.target) && !e.target.closest('[onclick="openEditDrawer()"]') && !drawer.classList.contains('translate-x-full')) {
                closeEditDrawer();
            }
        });
        
        // 文件上传处理
        document.addEventListener('DOMContentLoaded', function() {
            const fileInputs = document.querySelectorAll('input[type="file"]');
            fileInputs.forEach(input => {
                input.addEventListener('change', function(e) {
                    if (e.target.files.length > 0) {
                        const fileName = e.target.files[0].name;
                        const uploadProgress = e.target.closest('div').querySelector('#upload-progress');
                        if (uploadProgress) {
                            uploadProgress.classList.remove('hidden');
                            // 模拟上传进度
                            let progress = 0;
                            const interval = setInterval(() => {
                                progress += 10;
                                uploadProgress.querySelector('.bg-blue-600').style.width = progress + '%';
                                uploadProgress.querySelector('span:last-child').textContent = progress + '%';
                                if (progress >= 100) {
                                    clearInterval(interval);
                                    setTimeout(() => {
                                        uploadProgress.classList.add('hidden');
                                        alert('文件上传成功！');
                                    }, 500);
                                }
                            }, 200);
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>