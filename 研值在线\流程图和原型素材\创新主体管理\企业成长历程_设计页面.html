<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业成长历程</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">企业成长历程</h1>
                    <p class="mt-2 text-sm text-gray-600">以时间轴为主线，直观呈现企业从初创到高成长阶段的政策认定轨迹、研发能力跃升及产业地位变迁</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出成长报告
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 企业基本信息卡片 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div class="flex items-start mb-4 md:mb-0">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <h2 class="text-xl font-bold text-gray-900">宁波科技创新有限公司</h2>
                        <div class="flex flex-wrap gap-3 mt-2">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">国家级高新技术企业</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">省级专精特新企业</span>
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">成立年份: 2015</span>
                        </div>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full md:w-auto">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">7</div>
                        <div class="text-xs text-gray-500">政策认定</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">3</div>
                        <div class="text-xs text-gray-500">研发机构</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">42</div>
                        <div class="text-xs text-gray-500">知识产权</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-gray-900">4</div>
                        <div class="text-xs text-gray-500">发展阶段</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选与视图切换区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">认定级别</h3>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">国家级</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">省级</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">市级</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">区级</button>
                    </div>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">年份区间</h3>
                    <div class="flex space-x-3">
                        <select class="w-full px-3 py-1.5 border border-gray-300 rounded-md text-sm">
                            <option>2015-2024</option>
                            <option>2020-2024</option>
                            <option>2018-2022</option>
                            <option>2015-2019</option>
                        </select>
                    </div>
                </div>
                <div>
                    <h3 class="text-sm font-medium text-gray-700 mb-3">业务主题</h3>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">高企</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">专精特新</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">技术中心</button>
                        <button class="px-3 py-1.5 bg-gray-100 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-200">工程中心</button>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-6 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-700 mb-3">视图切换</h3>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        时间轴视图
                    </button>
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium flex items-center hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                        </svg>
                        分段阶段视图
                    </button>
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium flex items-center hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        对比雷达视图
                    </button>
                </div>
            </div>
        </div>

        <!-- 时间轴总览区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">成长时间轴</h3>
            
            <!-- 时间轴容器 -->
            <div class="relative">
                <!-- 时间轴中心线 -->
                <div class="absolute left-0 md:left-1/2 top-0 bottom-0 w-0.5 bg-gray-200 transform md:translate-x-px"></div>
                
                <!-- 2015年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 md:text-right mb-4 md:mb-0">
                        <div class="inline-block md:float-right">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200">
                                <h4 class="text-sm font-semibold text-gray-900">公司成立</h4>
                                <p class="text-xs text-gray-500 mt-1">宁波科技创新有限公司在宁波国家高新区正式注册成立</p>
                                <div class="mt-2 flex justify-end">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-gray-100 text-gray-800">基础信息</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-5 h-5 rounded-full bg-gray-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12 hidden md:block"></div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2015</div>
                </div>
                
                <!-- 2017年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-5 h-5 rounded-full bg-yellow-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12">
                        <div class="inline-block">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">市级高新技术企业认定</h4>
                                <p class="text-xs text-gray-500 mt-1">获得宁波市高新技术企业认定证书，编号：甬科高20170123</p>
                                <div class="mt-2 flex">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">市级认定</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2017</div>
                </div>
                
                <!-- 2018年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 md:text-right mb-4 md:mb-0">
                        <div class="inline-block md:float-right">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">市级企业工程技术中心</h4>
                                <p class="text-xs text-gray-500 mt-1">获批组建宁波市智能装备工程技术中心</p>
                                <div class="mt-2 flex justify-end">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">研发机构</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-5 h-5 rounded-full bg-yellow-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12 hidden md:block"></div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2018</div>
                </div>
                
                <!-- 2019年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-green-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12">
                        <div class="inline-block">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">省级高新技术企业认定</h4>
                                <p class="text-xs text-gray-500 mt-1">获得浙江省高新技术企业认定证书，编号：浙科高20191234</p>
                                <div class="mt-2 flex">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">省级认定</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2019</div>
                </div>
                
                <!-- 2020年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 md:text-right mb-4 md:mb-0">
                        <div class="inline-block md:float-right">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">省级专精特新中小企业</h4>
                                <p class="text-xs text-gray-500 mt-1">被认定为浙江省专精特新中小企业</p>
                                <div class="mt-2 flex justify-end">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">省级认定</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-green-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12 hidden md:block"></div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2020</div>
                </div>
                
                <!-- 2021年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-blue-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12">
                        <div class="inline-block">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">国家级高新技术企业</h4>
                                <p class="text-xs text-gray-500 mt-1">获得国家级高新技术企业认定证书，编号：GR202133100456</p>
                                <div class="mt-2 flex">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">国家级认定</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2021</div>
                </div>
                
                <!-- 2022年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 md:text-right mb-4 md:mb-0">
                        <div class="inline-block md:float-right">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">省级企业技术中心</h4>
                                <p class="text-xs text-gray-500 mt-1">获批组建浙江省智能装备企业技术中心</p>
                                <div class="mt-2 flex justify-end">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">研发机构</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-green-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12 hidden md:block"></div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2022</div>
                </div>
                
                <!-- 2023年 -->
                <div class="relative pl-8 md:pl-0 md:flex mb-12 last:mb-0">
                    <div class="md:w-1/2 md:pr-12 hidden md:block"></div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-blue-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12">
                        <div class="inline-block">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">国家级专精特新小巨人企业</h4>
                                <p class="text-xs text-gray-500 mt-1">被认定为国家级专精特新小巨人企业</p>
                                <div class="mt-2 flex">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">国家级认定</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2023</div>
                </div>
                
                <!-- 2024年 -->
                <div class="relative pl-8 md:pl-0 md:flex">
                    <div class="md:w-1/2 md:pr-12 md:text-right mb-4 md:mb-0">
                        <div class="inline-block md:float-right">
                            <div class="bg-gray-50 p-4 rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:border-blue-300 transition-colors" onclick="openModal('milestoneModal')">
                                <h4 class="text-sm font-semibold text-gray-900">国家级企业技术中心</h4>
                                <p class="text-xs text-gray-500 mt-1">获批组建国家级企业技术中心</p>
                                <div class="mt-2 flex justify-end">
                                    <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">研发机构</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 md:left-1/2 w-6 h-6 rounded-full bg-blue-500 border-4 border-white transform -translate-x-1/2"></div>
                    <div class="md:w-1/2 md:pl-12 hidden md:block"></div>
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 text-sm font-medium text-gray-700">2024</div>
                </div>
            </div>
        </div>
        
        <!-- 阶段对比区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">成长阶段分析</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- 创业期 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-blue-300 transition-colors cursor-pointer">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h4 class="text-base font-medium text-gray-900">创业期 (2015-2017)</h4>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">政策认定</span>
                                    <span class="font-medium">1项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 15%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发投入占比</span>
                                    <span class="font-medium">8.5%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">知识产权</span>
                                    <span class="font-medium">5项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 12%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发人员</span>
                                    <span class="font-medium">12人</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 20%"></div>
                                </div>
                            </div>
                        </div>
                        <button class="mt-4 w-full py-1.5 text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                    </div>
                </div>
                
                <!-- 成长初期 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-blue-300 transition-colors cursor-pointer">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h4 class="text-base font-medium text-gray-900">成长初期 (2018-2019)</h4>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">政策认定</span>
                                    <span class="font-medium">2项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发投入占比</span>
                                    <span class="font-medium">12.3%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">知识产权</span>
                                    <span class="font-medium">12项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 28%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发人员</span>
                                    <span class="font-medium">25人</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 42%"></div>
                                </div>
                            </div>
                        </div>
                        <button class="mt-4 w-full py-1.5 text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                    </div>
                </div>
                
                <!-- 快速扩张期 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-blue-300 transition-colors cursor-pointer">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h4 class="text-base font-medium text-gray-900">快速扩张期 (2020-2022)</h4>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">政策认定</span>
                                    <span class="font-medium">3项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发投入占比</span>
                                    <span class="font-medium">18.7%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 65%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">知识产权</span>
                                    <span class="font-medium">28项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 67%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发人员</span>
                                    <span class="font-medium">48人</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 80%"></div>
                                </div>
                            </div>
                        </div>
                        <button class="mt-4 w-full py-1.5 text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                    </div>
                </div>
                
                <!-- 成熟深化期 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden hover:border-blue-300 transition-colors cursor-pointer">
                    <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
                        <h4 class="text-base font-medium text-gray-900">成熟深化期 (2023-至今)</h4>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">政策认定</span>
                                    <span class="font-medium">2项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发投入占比</span>
                                    <span class="font-medium">22.5%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 80%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">知识产权</span>
                                    <span class="font-medium">42项</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between text-sm mb-1">
                                    <span class="text-gray-500">研发人员</span>
                                    <span class="font-medium">65人</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 100%"></div>
                                </div>
                            </div>
                        </div>
                        <button class="mt-4 w-full py-1.5 text-sm text-blue-600 hover:text-blue-800">查看详情</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 里程碑详情模态框 -->
    <div id="milestoneModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">国家级高新技术企业认定详情</h3>
                    <button onclick="closeModal('milestoneModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 左侧主要信息 -->
                        <div class="md:col-span-2">
                            <div class="space-y-6">
                                <div>
                                    <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-500">认定名称：</span>
                                            <span class="font-medium text-gray-900">国家级高新技术企业</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">证书编号：</span>
                                            <span class="font-medium text-gray-900">GR202133100456</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">认定级别：</span>
                                            <span class="inline-flex px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">国家级认定</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">认定日期：</span>
                                            <span class="font-medium text-gray-900">2021年10月26日</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">有效期至：</span>
                                            <span class="font-medium text-gray-900">2024年10月25日</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">主管部门：</span>
                                            <span class="font-medium text-gray-900">科学技术部火炬高技术产业开发中心</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="text-md font-semibold text-gray-900 mb-3">认定条件</h4>
                                    <ul class="text-sm text-gray-700 space-y-2">
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>核心技术属于《国家重点支持的高新技术领域》规定的范围</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>企业近三个会计年度的研究开发费用总额占同期销售收入总额的比例不低于5%</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>近一年高新技术产品（服务）收入占企业同期总收入的比例不低于60%</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span>企业从事研发和相关技术创新活动的科技人员占企业当年职工总数的比例不低于10%</span>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div>
                                    <h4 class="text-md font-semibold text-gray-900 mb-3">关联政策与奖励</h4>
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <h5 class="text-sm font-medium text-blue-900 mb-2">宁波市高新技术企业培育奖励政策</h5>
                                        <p class="text-xs text-blue-700 mb-3">对首次认定的高新技术企业，给予30万元奖励；对重新认定的高新技术企业，给予10万元奖励。</p>
                                        <div class="flex space-x-2">
                                            <a href="#" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                查看政策原文
                                            </a>
                                            <a href="#" class="text-xs text-blue-600 hover:text-blue-800 flex items-center">
                                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                                </svg>
                                                下载奖励文件
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧信息 -->
                        <div>
                            <div class="space-y-6">
                                <!-- 证书预览 -->
                                <div>
                                    <h4 class="text-md font-semibold text-gray-900 mb-3">证书预览</h4>
                                    <div class="bg-gray-100 rounded-lg p-4 text-center">
                                        <img src="https://source.unsplash.com/300x200?certificate,document" alt="证书预览" class="w-full rounded">
                                        <button class="mt-3 w-full py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                            下载证书
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 申报材料 -->
                                <div>
                                    <h4 class="text-md font-semibold text-gray-900 mb-3">申报材料</h4>
                                    <div class="space-y-2">
                                        <a href="#" class="flex items-center justify-between p-2 bg-gray-50 rounded-md text-sm hover:bg-gray-100">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span>高新技术企业认定申请书.pdf</span>
                                            </div>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                            </svg>
                                        </a>
                                        <a href="#" class="flex items-center justify-between p-2 bg-gray-50 rounded-md text-sm hover:bg-gray-100">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span>企业知识产权证明材料.zip</span>
                                            </div>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                            </svg>
                                        </a>
                                        <a href="#" class="flex items-center justify-between p-2 bg-gray-50 rounded-md text-sm hover:bg-gray-100">
                                            <div class="flex items-center">
                                                <svg class="w-4 h-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                                <span>企业近三年审计报告.rar</span>
                                            </div>
                                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-end">
                    <button onclick="closeModal('milestoneModal')" class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>