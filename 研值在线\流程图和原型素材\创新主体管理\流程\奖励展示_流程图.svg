<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 1100" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="750" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技奖励展示系统流程图</text>

  <!-- 阶段一：数据同步与处理 -->
  <text x="750" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与处理</text>
  
  <!-- 节点1: 数据源接口 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多级奖励数据源</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">国家、省、市科技奖励</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">中国专利奖公告库</text>
  </g>

  <!-- 节点2: 数据处理 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理中心</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">奖项名称规范化</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">载体匹配、去重处理</text>
  </g>

  <!-- 节点3: 奖励主题数据库 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">奖励主题数据库</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">获奖清单存储</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">证书影像管理</text>
  </g>

  <!-- 连接线 数据源 -> 数据处理 -->
  <path d="M 320 170 Q 360 170 400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="360" y="160" text-anchor="middle" font-size="12" fill="#555">每日凌晨增量拉取</text>

  <!-- 连接线 数据处理 -> 主题数据库 -->
  <path d="M 620 170 Q 660 170 700 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计服务与存储 -->
  <text x="750" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计服务与存储</text>

  <!-- 节点4: 统计服务 -->
  <g transform="translate(400, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">按级别年度聚合</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">概览卡片、趋势数据</text>
  </g>

  <!-- 节点5: 对象存储 -->
  <g transform="translate(700, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">对象存储服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">证书影像存储</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">访问URL生成</text>
  </g>

  <!-- 连接线 主题数据库 -> 统计服务 -->
  <path d="M 750 210 C 750 250, 550 280, 510 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 主题数据库 -> 对象存储 -->
  <path d="M 810 210 Q 810 260 810 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与交互 -->
  <text x="750" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点6: 前端页面 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端页面</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">概览数据展示</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">奖励列表渲染</text>
  </g>

  <!-- 节点7: 权限控制 -->
  <g transform="translate(500, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限控制接口</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">用户权限验证</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">下载链接控制</text>
  </g>

  <!-- 节点8: 搜索筛选 -->
  <g transform="translate(800, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">搜索筛选服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">条件查询</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">分页返回结果</text>
  </g>

  <!-- 连接线 统计服务 -> 前端页面 -->
  <path d="M 450 390 C 450 430, 350 460, 310 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 前端页面 -> 权限控制 -->
  <path d="M 420 530 Q 460 530 500 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 权限控制 -> 搜索筛选 -->
  <path d="M 720 530 Q 760 530 800 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：证书查看与下载 -->
  <text x="750" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：证书查看与下载</text>

  <!-- 节点9: 证书查看 -->
  <g transform="translate(200, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">证书查看功能</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">缩略图点击</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">弹窗显示原图</text>
  </g>

  <!-- 节点10: 日志服务 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">浏览行为记录</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">签名URL请求</text>
  </g>

  <!-- 节点11: 导出服务 -->
  <g transform="translate(800, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">Excel文件生成</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">异步下载提供</text>
  </g>

  <!-- 连接线 搜索筛选 -> 证书查看 -->
  <path d="M 850 570 C 850 610, 350 640, 310 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 证书查看 -> 日志服务 -->
  <path d="M 420 710 Q 460 710 500 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 搜索筛选 -> 导出服务 -->
  <path d="M 910 570 Q 910 620 910 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="930" y="620" text-anchor="middle" font-size="12" fill="#555">筛选导出</text>

  <!-- 连接线 日志服务 -> 对象存储 (签名URL) -->
  <path d="M 610 670 C 610 600, 750 450, 810 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="720" y="530" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-45, 720, 530)">签名URL</text>

  <!-- 阶段五：数据质量监控 -->
  <text x="750" y="820" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：数据质量监控</text>

  <!-- 节点12: 质量监控 -->
  <g transform="translate(400, 850)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#FFE0E6" stroke="#FF8A95" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据质量监控</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">新增记录比对</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">缺失信息检测</text>
  </g>

  <!-- 节点13: 数据修正 -->
  <g transform="translate(700, 850)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#FFE0E6" stroke="#FF8A95" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据修正任务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">自动提交修正</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">数据库回填</text>
  </g>

  <!-- 连接线 主题数据库 -> 质量监控 -->
  <path d="M 810 210 C 1100 210, 1100 820, 510 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="1120" y="530" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1120, 530)">定期监控</text>

  <!-- 连接线 质量监控 -> 数据修正 -->
  <path d="M 620 890 Q 660 890 700 890" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据修正 -> 主题数据库 (回填) -->
  <path d="M 810 850 C 810 800, 1200 300, 1200 170 C 1200 170, 970 170, 920 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1220" y="510" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1220, 510)">修正回填</text>

</svg>