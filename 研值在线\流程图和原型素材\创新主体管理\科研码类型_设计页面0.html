<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研码类型管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科研码类型管理</h1>
            <p class="text-gray-600">统一维护科研码类型体系，为创新主体科研码生成、展示与统计提供规范化基础</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">使用范围</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部范围</option>
                                <option value="enterprise">企业</option>
                                <option value="university">高等院校</option>
                                <option value="institute">科研院所</option>
                                <option value="individual">个人</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">类型名称</label>
                            <input type="text" placeholder="请输入类型名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效状态</label>
                            <div class="flex items-center space-x-4 mt-2">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">全部</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已启用</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="inactive" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已停用</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 科研码类型列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                科研码类型列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openTypeModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增类型
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用范围</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型说明</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">企业科研码</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">企业</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-500 line-clamp-2">用于标识宁波市各类企业的科研活动，适用于宁波市科技型企业、高新技术企业等</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-20 10:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" checked class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewType('type1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editType('type1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteType('type1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">高校科研码</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">高等院校</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-500 line-clamp-2">用于标识宁波市高等院校的科研活动，适用于宁波大学、宁波工程学院等高校</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-15 14:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" checked class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewType('type2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editType('type2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteType('type2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">科研院所科研码</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科研院所</div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-sm text-gray-500 line-clamp-2">用于标识宁波市科研院所的科研活动，适用于中科院宁波材料所、宁波市海洋研究院等</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-10 09:15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" class="sr-only peer">
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewType('type3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editType('type3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="enableType('type3')" class="text-green-600 hover:text-green-900">启用</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">15</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量操作区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        分类统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已启用类型总数</div>
                                <div class="text-2xl font-bold text-blue-600">12</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近一月新增</div>
                                <div class="text-2xl font-bold text-green-600">3</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已停用类型</div>
                                <div class="text-2xl font-bold text-yellow-600">3</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量操作区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            批量操作
                        </h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            批量启用
                        </button>
                        <button class="w-full px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            批量停用
                        </button>
                        <button class="w-full px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                            批量删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 类型编辑弹窗 -->
    <div id="typeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">新增科研码类型</h3>
                    <button onclick="closeTypeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">类型名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入类型名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <p class="mt-1 text-xs text-gray-500">2-20个字符，支持中英文、数字和下划线</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">使用范围 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择使用范围</option>
                                    <option value="enterprise">企业</option>
                                    <option value="university">高等院校</option>
                                    <option value="institute">科研院所</option>
                                    <option value="individual">个人</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">类型说明</label>
                                <textarea rows="4" placeholder="请输入类型说明" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                            </div>
                        </div>

                        <!-- 生效状态 -->
                        <div>
                            <label class="flex items-center">
                                <span class="mr-3 text-sm font-medium text-gray-700">生效状态:</span>
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox" id="statusToggle" class="sr-only">
                                    <div class="block h-6 bg-gray-300 rounded-full w-12"></div>
                                    <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                                </div>
                                <span id="statusText" class="text-sm text-gray-700">已停用</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeTypeModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 类型详情侧滑面板 -->
    <div id="detailPanel" class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">科研码类型详情</h3>
                <button onclick="closeDetailPanel()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">基本信息</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <p class="text-xs text-gray-500">类型名称</p>
                                    <p class="text-sm font-medium text-gray-900">企业科研码</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">使用范围</p>
                                    <p class="text-sm font-medium text-gray-900">企业</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">创建时间</p>
                                    <p class="text-sm font-medium text-gray-900">2023-05-20 10:30</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">状态</p>
                                    <p class="text-sm font-medium text-green-600">已启用</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">类型说明</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-700">用于标识宁波市各类企业的科研活动，适用于宁波市科技型企业、高新技术企业等。该科研码将作为企业参与科研项目、申请科研资助的唯一标识码。</p>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">引用统计</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <p class="text-sm text-gray-700">被引用次数</p>
                                <p class="text-sm font-medium text-blue-600">156</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">历史修改记录</h4>
                        <div class="space-y-3">
                            <div class="border-l-2 border-blue-500 pl-4 py-1">
                                <p class="text-xs text-gray-500">2023-06-15 14:30</p>
                                <p class="text-sm text-gray-700">更新了类型说明</p>
                            </div>
                            <div class="border-l-2 border-blue-500 pl-4 py-1">
                                <p class="text-xs text-gray-500">2023-05-20 10:30</p>
                                <p class="text-sm text-gray-700">创建了该科研码类型</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 border-t bg-gray-50">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    编辑
                </button>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 类型编辑弹窗
        function openTypeModal() {
            document.getElementById('typeModal').classList.remove('hidden');
        }
        
        function closeTypeModal() {
            document.getElementById('typeModal').classList.add('hidden');
        }
        
        // 类型详情侧滑面板
        function openDetailPanel() {
            document.getElementById('detailPanel').classList.remove('translate-x-full');
        }
        
        function closeDetailPanel() {
            document.getElementById('detailPanel').classList.add('translate-x-full');
        }
        
        // 状态开关
        document.getElementById('statusToggle').addEventListener('change', function() {
            const statusText = document.getElementById('statusText');
            if (this.checked) {
                statusText.textContent = '已启用';
                document.querySelector('.dot').classList.add('transform', 'translate-x-6');
            } else {
                statusText.textContent = '已停用';
                document.querySelector('.dot').classList.remove('transform', 'translate-x-6');
            }
        });
        
        // 其他功能函数
        function viewType(typeId) {
            openDetailPanel();
            console.log('查看类型:', typeId);
        }
        
        function editType(typeId) {
            document.getElementById('modalTitle').textContent = '编辑科研码类型';
            openTypeModal();
            console.log('编辑类型:', typeId);
        }
        
        function enableType(typeId) {
            if (confirm('确定要启用这个科研码类型吗？')) {
                console.log('启用类型:', typeId);
            }
        }
        
        function deleteType(typeId) {
            if (confirm('确定要删除这个科研码类型吗？此操作不可恢复！')) {
                console.log('删除类型:', typeId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('typeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTypeModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 自定义样式
        document.addEventListener('DOMContentLoaded', function() {
            // 状态开关样式
            const style = document.createElement('style');
            style.textContent = `
                #statusToggle:checked + .block {
                    background-color: #2563eb;
                }
                #statusToggle:checked ~ .dot {
                    transform: translateX(100%);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>