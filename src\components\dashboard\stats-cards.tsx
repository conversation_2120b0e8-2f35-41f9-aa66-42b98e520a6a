import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Brain, Users, ArrowUpIcon, Activity } from "lucide-react"

export function StatsCards() {
  const cards = [
    {
      title: "总访问量",
      value: "1,234",
      change: "+20%",
      icon: Activity,
      gradient: "from-[#0EA5E9] to-[#2563EB]"
    },
    {
      title: "活跃用户",
      value: "456",
      change: "+15%",
      icon: Users,
      gradient: "from-[#3B82F6] to-[#1D4ED8]"
    },
    {
      title: "应用调用",
      value: "789",
      change: "+25%",
      icon: Brain,
      gradient: "from-[#6366F1] to-[#4338CA]"
    },
    {
      title: "平均响应",
      value: "123ms",
      change: "-10%",
      icon: Activity,
      gradient: "from-[#8B5CF6] to-[#6D28D9]"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => (
        <Card 
          key={index} 
          className={`bg-gradient-to-br ${card.gradient} border-none shadow-sm`}
        >
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm font-medium text-white/90">
                {card.title}
              </CardTitle>
              <card.icon className="h-4 w-4 text-white/80" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">{card.value}</div>
            <div className="flex items-center text-xs text-white/80 mt-1">
              <ArrowUpIcon className="h-4 w-4 mr-1" />
              较上月 {card.change}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 