<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="750" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员详情展示业务流程</text>

  <!-- 阶段一：数据归集与档案建立 -->
  <text x="750" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与档案建立</text>
  
  <!-- 节点1: 数据源同步 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据源同步</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时/周期性接口同步</text>
  </g>

  <!-- 节点2: 信息归集 -->
  <g transform="translate(650, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息归集</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全量信息整合处理</text>
  </g>

  <!-- 节点3: 人才档案建立 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才档案建立</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">完整档案自动生成</text>
  </g>

  <!-- 阶段二：详情页面渲染与展示 -->
  <text x="750" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：详情页面渲染与展示</text>

  <!-- 节点4: 用户点击入口 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户点击入口</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">从总览或关联模块进入</text>
  </g>

  <!-- 节点5: 详情页面调取 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情页面调取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">系统渲染全量详情</text>
  </g>

  <!-- 节点6: 分区数据加载 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分区数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">依次加载各板块信息</text>
  </g>

  <!-- 节点7: 可视化组件 -->
  <g transform="translate(1100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化组件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">图表与网络展示</text>
  </g>

  <!-- 阶段三：用户交互与操作管理 -->
  <text x="750" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：用户交互与操作管理</text>

  <!-- 节点8: 板块切换浏览 -->
  <g transform="translate(100, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">板块切换浏览</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">各信息区域切换</text>
  </g>

  <!-- 节点9: 筛选排序操作 -->
  <g transform="translate(350, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选排序操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">成果奖励项目筛选</text>
  </g>

  <!-- 节点10: 合作网络追溯 -->
  <g transform="translate(600, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">合作网络追溯</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击节点查看详情</text>
  </g>

  <!-- 节点11: 动态数据刷新 -->
  <g transform="translate(850, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态数据刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">根据业务场景更新</text>
  </g>

  <!-- 节点12: 一键导出功能 -->
  <g transform="translate(1100, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">一键导出功能</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">简历报告信息快照</text>
  </g>

  <!-- 阶段四：系统监控与质量保障 -->
  <text x="750" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统监控与质量保障</text>

  <!-- 节点13: 行为记录分析 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">行为记录分析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">用户操作行为监控</text>
  </g>

  <!-- 节点14: 导出历史管理 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出历史管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录导出操作历史</text>
  </g>

  <!-- 节点15: 数据溯源归档 -->
  <g transform="translate(900, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据溯源归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">变更溯源版本管理</text>
  </g>

  <!-- 连接线：阶段一内部连接 -->
  <path d="M 500 165 C 550 165, 600 165, 650 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 165 C 900 165, 950 165, 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：阶段一到阶段二 -->
  <path d="M 750 200 C 750 240, 600 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：阶段二内部连接 -->
  <path d="M 400 355 C 450 355, 450 355, 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 355 C 750 355, 750 355, 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 355 C 1050 355, 1050 355, 1100 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：阶段二到阶段三 -->
  <path d="M 600 390 C 400 450, 300 480, 200 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 390 C 500 450, 450 480, 450 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 390 C 700 450, 700 480, 700 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 390 C 900 450, 950 480, 950 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1200 390 C 1200 450, 1200 480, 1200 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线：阶段三到阶段四 -->
  <path d="M 200 590 C 300 650, 400 680, 450 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 590 C 500 650, 550 680, 600 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 590 C 700 650, 700 680, 750 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 590 C 950 650, 950 680, 950 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1200 590 C 1100 650, 1000 680, 950 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：数据溯源归档 -> 数据源同步 -->
  <path d="M 900 720 C 50 650, 50 400, 50 165 C 50 165, 200 165, 300 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="400" font-size="11" fill="#666" transform="rotate(-90, 50, 400)">数据溯源反馈</text>

  <!-- 反馈循环：行为记录分析 -> 动态数据刷新 -->
  <path d="M 500 720 C 1400 650, 1400 555, 1400 555 C 1400 555, 1100 555, 950 555" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1400" y="550" font-size="11" fill="#666">服务优化</text>

</svg>