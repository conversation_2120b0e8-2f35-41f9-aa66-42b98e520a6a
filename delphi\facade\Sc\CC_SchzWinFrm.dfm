object CC_SchzWinForm: TCC_SchzWinForm
  Left = 0
  Top = 0
  ClientHeight = 821
  ClientWidth = 1424
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -11
  Font.Name = 'Tahoma'
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 13
  object RzPanel1: TRzPanel
    Left = 0
    Top = 35
    Width = 1424
    Height = 786
    Align = alClient
    BorderOuter = fsNone
    Color = clBlue
    TabOrder = 0
  end
  object RzPanel2: TRzPanel
    Left = 0
    Top = 0
    Width = 1424
    Height = 35
    Align = alTop
    BorderOuter = fsNone
    Color = clBlue
    TabOrder = 1
    object RzLabel5: TRzLabel
      Left = 500
      Top = 9
      Width = 73
      Height = 26
      Caption = '2024 '#24180
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWhite
      Font.Height = -19
      Font.Name = #24494#36719#38597#40657
      Font.Style = [fsBold]
      ParentFont = False
    end
    object Btn_Type1: TAdvGlowButton
      Left = 133
      Top = 2
      Width = 90
      Height = 33
      Caption = '     '#27719'  '#24635
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clNavy
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      TabOrder = 0
      OnClick = Btn_Type1Click
      Appearance.ColorChecked = clWhite
      Appearance.ColorCheckedTo = clWhite
      Appearance.ColorDisabled = clWhite
      Appearance.ColorDisabledTo = clWhite
      Appearance.ColorDown = clWhite
      Appearance.ColorDownTo = clWhite
      Appearance.ColorHot = clWhite
      Appearance.ColorHotTo = clWhite
      Appearance.ColorMirror = clWhite
      Appearance.ColorMirrorHot = clWhite
      Appearance.ColorMirrorHotTo = clWhite
      Appearance.ColorMirrorDown = clWhite
      Appearance.ColorMirrorDownTo = clWhite
      Appearance.ColorMirrorChecked = clWhite
      Appearance.ColorMirrorCheckedTo = clWhite
      Appearance.ColorMirrorDisabled = clWhite
      Appearance.ColorMirrorDisabledTo = clWhite
      Layout = blGlyphLeftAdjusted
    end
    object Btn_Type4: TAdvGlowButton
      Left = 21
      Top = 2
      Width = 90
      Height = 33
      Caption = '  '#27454#24335' '#20998#37197
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clNavy
      Font.Height = -15
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      TabOrder = 1
      OnClick = Btn_Type4Click
      Appearance.ColorChecked = clWhite
      Appearance.ColorCheckedTo = clWhite
      Appearance.ColorDisabled = clWhite
      Appearance.ColorDisabledTo = clWhite
      Appearance.ColorDown = clWhite
      Appearance.ColorDownTo = clWhite
      Appearance.ColorHot = clWhite
      Appearance.ColorHotTo = clWhite
      Appearance.ColorMirror = clWhite
      Appearance.ColorMirrorHot = clWhite
      Appearance.ColorMirrorHotTo = clWhite
      Appearance.ColorMirrorDown = clWhite
      Appearance.ColorMirrorDownTo = clWhite
      Appearance.ColorMirrorChecked = clWhite
      Appearance.ColorMirrorCheckedTo = clWhite
      Appearance.ColorMirrorDisabled = clWhite
      Appearance.ColorMirrorDisabledTo = clWhite
      Layout = blGlyphLeftAdjusted
    end
  end
end
