unit CcSjChdj;

interface

uses
  Classes;

type
  TCcSjChdj = class
  private
    FSjchdjid: Integer;
    FDdid: Integer;

    FScChrq: string;
    FWczt: string;
    FPort: string;
    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FKsbm: string;
    FSjh: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FDds: Integer;
    FMjdj: double;
    FRmbdj: double;

    FSccj: string;
    FtChrq_a: string;
    FtChrq_b: string;
    FtChrq_c: string;
    FtChrq_d: string;
    FtChrq_e: string;
    FtChrq_f: string;

    FChrq_a: string;
    FChs_a: Integer;
    FZxsl_a: double;
    FTjmz_a: double;
    FTjjz_a: double;
    FTj_a: double;

    FChrq_b: string;
    FChs_b: Integer;
    FZxsl_b: double;
    FTjmz_b: double;
    FTjjz_b: double;
    FTj_b: double;

    FChrq_c: string;
    FChs_c: Integer;
    FZxsl_c: double;
    FTjmz_c: double;
    FTjjz_c: double;
    FTj_c: double;

    FChrq_d: string;
    FChs_d: Integer;
    FZxsl_d: double;
    FTjmz_d: double;
    FTjjz_d: double;
    FTj_d: double;

    FChrq_e: string;
    FChs_e: Integer;
    FZxsl_e: double;
    FTjmz_e: double;
    FTjjz_e: double;
    FTj_e: double;

    FChrq_f: string;
    FChs_f: Integer;
    FZxsl_f: double;
    FTjmz_f: double;
    FTjjz_f: double;
    FTj_f: double;

    FBgHs: string;
    FBgPmYw: string;
    FBgPmZw: string;
    FBgSpmc: string;
    FBgCf: string;

    FZxdYwms: string;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjchdjid: Integer read FSjchdjid write FSjchdjid;
    property Ddid: Integer read FDdid write FDdid;

    property ScChrq: string read FScChrq write FScChrq;
    property Wczt: string read FWczt write FWczt;
    property Port: string read FPort write FPort;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Dds: Integer read FDds write FDds;

    property Sccj: string read FSccj write FSccj;
    property tChrq_a: string read FtChrq_a write FtChrq_a;
    property tChrq_b: string read FtChrq_b write FtChrq_b;
    property tChrq_c: string read FtChrq_c write FtChrq_c;
    property tChrq_d: string read FtChrq_d write FtChrq_d;
    property tChrq_e: string read FtChrq_e write FtChrq_e;
    property tChrq_f: string read FtChrq_f write FtChrq_f;

    property Chrq_a: string read FChrq_a write FChrq_a;
    property Chs_a: Integer read FChs_a write FChs_a;
    property Zxsl_a: double read FZxsl_a write FZxsl_a;
    property Tjmz_a: double read FTjmz_a write FTjmz_a;
    property Tjjz_a: double read FTjjz_a write FTjjz_a;
    property Tj_a: double read FTj_a write FTj_a;

    property Chrq_b: string read FChrq_b write FChrq_b;
    property Chs_b: Integer read FChs_b write FChs_b;
    property Zxsl_b: double read FZxsl_b write FZxsl_b;
    property Tjmz_b: double read FTjmz_b write FTjmz_b;
    property Tjjz_b: double read FTjjz_b write FTjjz_b;
    property Tj_b: double read FTj_b write FTj_b;

    property Chrq_c: string read FChrq_c write FChrq_c;
    property Chs_c: Integer read FChs_c write FChs_c;
    property Zxsl_c: double read FZxsl_c write FZxsl_c;
    property Tjmz_c: double read FTjmz_c write FTjmz_c;
    property Tjjz_c: double read FTjjz_c write FTjjz_c;
    property Tj_c: double read FTj_c write FTj_c;

    property Chrq_d: string read FChrq_d write FChrq_d;
    property Chs_d: Integer read FChs_d write FChs_d;
    property Zxsl_d: double read FZxsl_d write FZxsl_d;
    property Tjmz_d: double read FTjmz_d write FTjmz_d;
    property Tjjz_d: double read FTjjz_d write FTjjz_d;
    property Tj_d: double read FTj_d write FTj_d;

    property Chrq_e: string read FChrq_e write FChrq_e;
    property Chs_e: Integer read FChs_e write FChs_e;
    property Zxsl_e: double read FZxsl_e write FZxsl_e;
    property Tjmz_e: double read FTjmz_e write FTjmz_e;
    property Tjjz_e: double read FTjjz_e write FTjjz_e;
    property Tj_e: double read FTj_e write FTj_e;

    property Chrq_f: string read FChrq_f write FChrq_f;
    property Chs_f: Integer read FChs_f write FChs_f;
    property Zxsl_f: double read FZxsl_f write FZxsl_f;
    property Tjmz_f: double read FTjmz_f write FTjmz_f;
    property Tjjz_f: double read FTjjz_f write FTjjz_f;
    property Tj_f: double read FTj_f write FTj_f;

    property Mjdj: double read FMjdj write FMjdj;
    property Rmbdj: double read FRmbdj write FRmbdj;

    property BgHs: string read FBgHs write FBgHs;
    property BgPmYw: string read FBgPmYw write FBgPmYw;
    property BgPmZw: string read FBgPmZw write FBgPmZw;
    property BgSpmc: string read FBgSpmc write FBgSpmc;
    property BgCf: string read FBgCf write FBgCf;
    property ZxdYwms: string read FZxdYwms write FZxdYwms;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
