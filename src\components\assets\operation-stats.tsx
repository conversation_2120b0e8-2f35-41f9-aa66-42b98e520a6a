'use client'

import { Card, CardContent } from "@/components/ui/card"
import { 
  AlertCircle, 
  Clock, 
  CheckCircle2Icon, 
  ArrowUpIcon,
  ArrowDownIcon,
  Users,
  Activity,
  Zap
} from "lucide-react"

export function OperationStats() {
  const stats = [
    {
      title: "平均响应时间",
      value: "5.2分钟",
      change: "-12.5%",
      trend: "down",
      description: "较上周期",
      icon: Clock,
      color: "text-blue-500",
      bgColor: "bg-blue-50"
    },
    {
      title: "故障解决率",
      value: "98.6%",
      change: "+2.3%",
      trend: "up",
      description: "较上周期",
      icon: CheckCircle2Icon,
      color: "text-green-500",
      bgColor: "bg-green-50"
    },
    {
      title: "运维工单总量",
      value: "1,286",
      change: "+15.8%",
      trend: "up",
      description: "较上月",
      icon: Activity,
      color: "text-purple-500",
      bgColor: "bg-purple-50"
    },
    {
      title: "人均处理工单",
      value: "42.8",
      change: "+8.5%",
      trend: "up",
      description: "较上月",
      icon: Users,
      color: "text-orange-500",
      bgColor: "bg-orange-50"
    }
  ]

  return (
    <div className="grid grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card key={index} className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className={`${stat.bgColor} p-2 rounded-lg`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
              <div className="flex items-center gap-1">
                {stat.trend === 'up' ? (
                  <ArrowUpIcon className="h-4 w-4 text-green-500" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 text-red-500" />
                )}
                <span className={stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}>
                  {stat.change}
                </span>
              </div>
            </div>
            <div className="mt-3">
              <div className="text-sm text-gray-500">{stat.title}</div>
              <div className="text-2xl font-semibold mt-1">{stat.value}</div>
              <div className="text-xs text-gray-400 mt-1">{stat.description}</div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
} 