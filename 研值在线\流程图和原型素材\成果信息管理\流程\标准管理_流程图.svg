<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">标准管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标准管理模块入口</text>
  </g>

  <!-- 节点2: 权限加载 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载可见标准数据</text>
  </g>

  <!-- 节点3: 指标概览 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成指标概览</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已加载</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据检索与处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据检索与处理</text>

  <!-- 节点4: 筛选条件 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">筛选条件设置</text>
  </g>

  <!-- 节点5: 标准检索 -->
  <g transform="translate(400, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标准检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取符合条件数据</text>
  </g>

  <!-- 节点6: 主体关联 -->
  <g transform="translate(700, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主体关联服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取创新主体信息</text>
  </g>

  <!-- 节点7: 数据整合 -->
  <g transform="translate(1000, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据整合刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已完成</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 300 355 Q 350 355 400 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 320 C 500 300, 600 300, 700 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 320 C 800 300, 900 300, 1000 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 390 C 800 410, 900 410, 1000 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="500" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点8: 新增编辑 -->
  <g transform="translate(100, 540)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增编辑操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">字段合法性校验</text>
  </g>

  <!-- 节点9: 批量导入 -->
  <g transform="translate(320, 540)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入任务</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">文件格式校验</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(540, 540)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">Excel文件生成</text>
  </g>

  <!-- 节点11: 缓存刷新 -->
  <g transform="translate(760, 540)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">重载最新数据</text>
  </g>

  <!-- 节点12: 写入标准库 -->
  <g transform="translate(980, 540)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入标准库</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">数据持久化存储</text>
  </g>

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>
  
  <!-- 节点13: 定时同步 -->
  <g transform="translate(200, 760)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">系统定时同步服务</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">国家标准系统</tspan>
      <tspan dx="40">行业协会平台</tspan>
      <tspan dx="40">地方标准平台</tspan>
    </text>
  </g>

  <!-- 节点14: 状态监控 -->
  <g transform="translate(700, 760)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">标准状态监控与更新</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-100">修订变动</tspan>
      <tspan dx="40">废止提醒</tspan>
      <tspan dx="40">时效性保障</tspan>
    </text>
  </g>

  <!-- 连接线 阶段一到阶段二 -->
  <path d="M 800 200 C 800 240, 200 240, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 阶段二到阶段三 -->
  <path d="M 1100 390 C 1100 460, 190 460, 190 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 阶段三内部 -->
  <path d="M 190 610 C 190 650, 410 650, 410 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 410 610 C 410 650, 630 650, 630 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 630 610 C 630 650, 850 650, 850 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 610 C 850 650, 1070 650, 1070 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 阶段三到阶段四 -->
  <path d="M 580 610 C 580 680, 400 680, 400 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 610 C 900 680, 900 680, 900 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>