<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股东详情 - 企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="min-h-screen p-6">
        <!-- 导航栏 -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center text-blue-600 hover:text-blue-800 mr-4">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-1"></i>
                        返回企业概览
                    </a>
                    <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                        <i data-lucide="users" class="mr-3 h-6 w-6 text-blue-600"></i>
                        股东详情
                    </h1>
                </div>
                <div class="flex gap-2">
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        导出股东信息
                    </button>
                </div>
            </div>
            <p class="text-gray-600 mt-2">宁波创新科技股份有限公司 - 股东结构分析</p>
        </div>

        <!-- 股权结构概览 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                    <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                        <i data-lucide="pie-chart" class="mr-2 h-5 w-5 text-blue-600"></i>
                        股权结构图
                    </h2>
                    <div class="h-80">
                        <canvas id="shareholderDetailChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="space-y-4">
                <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                    <h3 class="font-medium text-gray-700 mb-3">基本统计</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">股东总数</span>
                            <span class="font-semibold">8人</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">自然人股东</span>
                            <span class="font-semibold">5人</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">法人股东</span>
                            <span class="font-semibold">3家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">注册资本</span>
                            <span class="font-semibold">5,000万元</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                    <h3 class="font-medium text-gray-700 mb-3">集中度分析</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">前三大股东占比</span>
                            <span class="font-semibold">73.5%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">控股股东</span>
                            <span class="font-semibold">张明</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">实际控制人</span>
                            <span class="font-semibold">张明</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 股东详细列表 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100 mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-blue-800 flex items-center">
                        <i data-lucide="list" class="mr-2 h-5 w-5 text-blue-600"></i>
                        股东详细信息
                    </h2>
                    <div class="flex gap-2">
                        <input type="text" placeholder="搜索股东..." class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部类型</option>
                            <option>自然人</option>
                            <option>法人</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股东名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股东类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">持股比例</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资方式</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张明</div>
                                        <div class="text-sm text-gray-500">董事长</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">自然人</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,750万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">35.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 35%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">货币</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">关联企业</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <i data-lucide="building" class="w-5 h-5 text-blue-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">宁波投资有限公司</div>
                                        <div class="text-sm text-gray-500">91330200XXX</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">法人</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,250万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">25.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 25%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">货币</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">关联企业</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李华</div>
                                        <div class="text-sm text-gray-500">副董事长</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">自然人</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">625万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">12.5%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 12.5%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">货币</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">关联企业</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div class="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                                            <i data-lucide="building" class="w-5 h-5 text-purple-600"></i>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">上海科技基金</div>
                                        <div class="text-sm text-gray-500">投资基金</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">法人</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">500万元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-gray-900">10.0%</div>
                                    <div class="w-16 bg-gray-200 rounded-full h-2 ml-2">
                                        <div class="bg-purple-600 h-2 rounded-full" style="width: 10%"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">货币</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">关联企业</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 股权变更历史 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-blue-800 flex items-center">
                    <i data-lucide="history" class="mr-2 h-5 w-5 text-blue-600"></i>
                    股权变更历史
                </h2>
            </div>
            
            <div class="p-6">
                <div class="flow-root">
                    <ul class="-mb-8">
                        <li>
                            <div class="relative pb-8">
                                <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                            <i data-lucide="plus" class="w-4 h-4 text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">股东增资 <span class="font-medium text-gray-900">张明</span></p>
                                            <p class="text-sm text-gray-500">增资金额：500万元，持股比例由30%增至35%</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time>2024年3月15日</time>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li>
                            <div class="relative pb-8">
                                <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                            <i data-lucide="user-plus" class="w-4 h-4 text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">新增股东 <span class="font-medium text-gray-900">上海科技基金</span></p>
                                            <p class="text-sm text-gray-500">投资金额：500万元，持股比例10%</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time>2023年8月20日</time>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li>
                            <div class="relative pb-8">
                                <div class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center ring-8 ring-white">
                                            <i data-lucide="transfer" class="w-4 h-4 text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">股权转让 <span class="font-medium text-gray-900">王强 → 李华</span></p>
                                            <p class="text-sm text-gray-500">转让股份：2.5%，转让价格：125万元</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time>2022年12月10日</time>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                        
                        <li>
                            <div class="relative">
                                <div class="relative flex space-x-3">
                                    <div>
                                        <span class="h-8 w-8 rounded-full bg-gray-500 flex items-center justify-center ring-8 ring-white">
                                            <i data-lucide="building" class="w-4 h-4 text-white"></i>
                                        </span>
                                    </div>
                                    <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                        <div>
                                            <p class="text-sm text-gray-500">公司成立</p>
                                            <p class="text-sm text-gray-500">注册资本：3,000万元，创始股东：张明、宁波投资有限公司</p>
                                        </div>
                                        <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                            <time>2018年3月15日</time>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 股东详细饼图
        const ctx = document.getElementById('shareholderDetailChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['张明', '宁波投资有限公司', '李华', '上海科技基金', '其他股东'],
                datasets: [{
                    data: [35, 25, 12.5, 10, 17.5],
                    backgroundColor: [
                        '#3B82F6',
                        '#10B981',
                        '#F59E0B',
                        '#8B5CF6',
                        '#EF4444'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + '%';
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 