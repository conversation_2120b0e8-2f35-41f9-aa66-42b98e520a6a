<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下方区域联动 - 科研管理人员业务流程</text>

  <!-- 阶段一：数据初始化与地图生成 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与地图生成</text>
  
  <!-- 节点1: 系统启动 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入大屏</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">人才大屏入口</text>
  </g>

  <!-- 节点2: 数据加载 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">基础数据及空间分布</text>
  </g>

  <!-- 节点3: 图表生成 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图热力图生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">空间分布可视化</text>
  </g>

  <!-- 节点4: 统计图表 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计图表初始化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">多维度数据展示</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与类型选择 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与类型选择</text>

  <!-- 节点5: 用户选择 -->
  <g transform="translate(250, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">类型选择</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择"科研管理人员"</text>
  </g>

  <!-- 节点6: 页面聚焦 -->
  <g transform="translate(550, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面自动聚焦</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">视图切换与调整</text>
  </g>

  <!-- 节点7: 数据刷新 -->
  <g transform="translate(850, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图热力图刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">多维度统计图更新</text>
  </g>

  <!-- 连接线 阶段一 -> 阶段二 -->
  <path d="M 500 200 C 500 230, 350 270, 350 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 450 335 Q 500 335 550 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 750 335 Q 800 335 850 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：联动展示与数据筛选 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：联动展示与数据筛选</text>

  <!-- 节点8: 地图交互 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图区域交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">鼠标悬停/点击区域</text>
  </g>

  <!-- 节点9: 联动展示 -->
  <g transform="translate(450, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">联动展示名单</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">区域人员与统计数据</text>
  </g>

  <!-- 节点10: 图表交互 -->
  <g transform="translate(750, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计图表交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择维度/分组</text>
  </g>

  <!-- 节点11: 条件筛选 -->
  <g transform="translate(1050, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">符合条件的人员名单</text>
  </g>

  <!-- 连接线 阶段二 -> 阶段三 -->
  <path d="M 350 370 C 350 400, 250 440, 250 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 370 C 950 400, 850 440, 850 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 350 505 Q 400 505 450 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 950 505 Q 1000 505 1050 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：清册操作与数据应用 -->
  <text x="700" y="610" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：清册操作与数据应用</text>

  <!-- 节点12: 清册操作 -->
  <g transform="translate(200, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">清册区操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">筛选、排序、查看详情</text>
  </g>

  <!-- 节点13: 数据导出 -->
  <g transform="translate(500, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选结果导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据利用与分析</text>
  </g>

  <!-- 节点14: 操作记录 -->
  <g transform="translate(800, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作行为记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据追踪与留痕</text>
  </g>

  <!-- 节点15: 业务保障 -->
  <g transform="translate(1100, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">业务合规保障</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据安全与合规性</text>
  </g>

  <!-- 连接线 阶段三 -> 阶段四 -->
  <path d="M 450 540 C 450 570, 300 610, 300 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1050 540 C 1050 570, 1200 610, 1200 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 12 -> 13 -->
  <path d="M 400 675 Q 450 675 500 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 13 -> 14 -->
  <path d="M 700 675 Q 750 675 800 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 14 -> 15 -->
  <path d="M 1000 675 Q 1050 675 1100 675" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 从操作记录回到数据加载 -->
  <path d="M 900 640 C 1300 640, 1300 100, 500 100 C 500 120, 500 130, 500 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1200" y="370" text-anchor="middle" font-size="12" fill="#666">数据更新反馈</text>

  <!-- 反馈循环2: 从清册操作回到图表交互 -->
  <path d="M 200 640 C 50 640, 50 470, 650 470 C 650 490, 650 500, 650 500" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="550" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 100, 550)">交互优化</text>

</svg>