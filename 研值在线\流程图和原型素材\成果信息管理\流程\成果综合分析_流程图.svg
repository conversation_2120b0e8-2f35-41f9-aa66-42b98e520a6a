<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果综合分析业务流程</text>

  <!-- 阶段一：任务初始化与记录 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：任务初始化与记录</text>
  
  <!-- 节点1: 用户提交筛选条件 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选条件</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">启动分析流程</text>
  </g>

  <!-- 节点2: 记录分析任务 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录分析任务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态设为处理中</text>
  </g>

  <!-- 节点3: 写入查询日志 -->
  <g transform="translate(900, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入查询日志</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">支持后续复现</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 520 165 Q 560 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 820 165 Q 860 165 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据聚合与处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据聚合与处理</text>

  <!-- 节点4: 调用成果聚合服务 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">调用成果聚合服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">读取成果库数据</text>
  </g>

  <!-- 节点5: 多维度汇总计算 -->
  <g transform="translate(450, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多维度汇总计算</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">类别、地域、主体、技术</text>
  </g>

  <!-- 节点6: 生成中间数据集 -->
  <g transform="translate(700, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成中间数据集</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">聚合计算结果</text>
  </g>

  <!-- 连接线 阶段一 -> 阶段二 -->
  <path d="M 1010 200 C 1010 240, 300 270, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 425 345 450 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 345 Q 675 345 700 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：可视化渲染与缓存 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：可视化渲染与缓存</text>

  <!-- 节点7: 触发可视化渲染 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">触发可视化渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">启动渲染服务</text>
  </g>

  <!-- 节点8: 生成图表数据 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成图表数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">概览、统计、热力、排名</text>
  </g>

  <!-- 节点9: 写入前端缓存 -->
  <g transform="translate(650, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入前端缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">提升访问性能</text>
  </g>

  <!-- 节点10: 更新任务状态 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新任务状态</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标记为已完成</text>
  </g>

  <!-- 连接线 阶段二 -> 阶段三 -->
  <path d="M 800 380 C 800 420, 250 460, 250 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 350 525 Q 375 525 400 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 525 Q 625 525 650 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 525 Q 875 525 900 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：交互操作与维护 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：交互操作与维护</text>

  <!-- 节点11: 钻取联动操作 -->
  <g transform="translate(150, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">钻取联动操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">重新计算子集数据</text>
  </g>

  <!-- 节点12: 导出操作 -->
  <g transform="translate(400, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成离线文件</text>
  </g>

  <!-- 节点13: 消息推送 -->
  <g transform="translate(650, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息推送</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">下载通知</text>
  </g>

  <!-- 节点14: 增量同步任务 -->
  <g transform="translate(900, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">增量同步任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">每日凌晨执行</text>
  </g>

  <!-- 连接线 阶段三 -> 阶段四 -->
  <path d="M 1000 560 C 1000 600, 250 640, 250 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12 -> 13, 14 -->
  <path d="M 350 705 Q 375 705 400 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 705 Q 625 705 650 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 705 Q 875 705 900 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 用户交互反馈循环 -->
  <path d="M 250 670 C 100 650, 100 200, 300 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="80" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 80, 400)">用户交互反馈</text>

  <!-- 数据同步反馈循环 -->
  <path d="M 1000 670 C 1150 650, 1150 200, 1010 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1170" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 1170, 400)">数据同步反馈</text>

</svg>