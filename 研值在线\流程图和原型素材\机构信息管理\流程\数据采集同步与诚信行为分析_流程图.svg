<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">数据采集同步与诚信行为分析流程</text>

  <!-- 阶段一：数据同步与采集 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与采集</text>
  
  <!-- 节点1: 定时触发 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时触发</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(预设调度策略)</text>
  </g>

  <!-- 节点2: 数据接口请求 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据接口请求</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(宁波市公共信用平台)</text>
  </g>

  <!-- 节点3: 数据接收 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据接收</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(新增/变更失信记录)</text>
  </g>

  <!-- 节点4: 数据清洗 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据清洗</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(预处理与去重)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -> 4 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：主体匹配与处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：主体匹配与处理</text>

  <!-- 节点5: 主体匹配服务 -->
  <g transform="translate(300, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主体匹配服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(机构名称/信用代码)</text>
  </g>

  <!-- 节点6: 成功匹配 -->
  <g transform="translate(100, 450)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成功匹配</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(关联主体)</text>
  </g>

  <!-- 节点7: 匹配失败 -->
  <g transform="translate(500, 450)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">匹配失败</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(待处理池)</text>
  </g>

  <!-- 连接线 数据清洗 -> 主体匹配 -->
  <path d="M 1050 200 C 1050 240, 450 280, 400 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 主体匹配 -> 成功/失败 -->
  <path d="M 350 390 C 300 410, 250 430, 200 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 390 C 500 410, 550 430, 600 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="275" y="425" font-size="11" fill="#555">匹配成功</text>
  <text x="525" y="425" font-size="11" fill="#555">匹配失败</text>

  <!-- 阶段三：数据存储与记录 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据存储与记录</text>

  <!-- 节点8: 诚信信息库 -->
  <g transform="translate(150, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">诚信信息库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(标记"同步成功")</text>
  </g>

  <!-- 节点9: 待处理池 -->
  <g transform="translate(450, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">待处理池</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(异常状态标记)</text>
  </g>

  <!-- 节点10: 执行结果摘要 -->
  <g transform="translate(750, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">执行结果摘要</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(成功/失败/待处理)</text>
  </g>

  <!-- 连接线 成功匹配 -> 诚信信息库 -->
  <path d="M 200 520 Q 225 570 250 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 匹配失败 -> 待处理池 -->
  <path d="M 600 520 Q 575 570 550 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 -> 执行结果摘要 -->
  <path d="M 350 655 C 500 655, 600 655, 750 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 655 Q 700 655 750 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：分析与可视化 -->
  <text x="700" y="770" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：分析与可视化</text>
  
  <!-- 节点11: 用户访问 -->
  <g transform="translate(200, 800)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(分析仪表盘)</text>
  </g>

  <!-- 节点12: 数据聚合 -->
  <g transform="translate(450, 800)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据聚合</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(筛选维度计算)</text>
  </g>

  <!-- 节点13: 分析模型 -->
  <g transform="translate(700, 800)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分析模型</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(统计数据生成)</text>
  </g>

  <!-- 节点14: 可视化图表 -->
  <g transform="translate(950, 800)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化图表</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(用户界面呈现)</text>
  </g>

  <!-- 连接线 诚信信息库 -> 用户访问 -->
  <path d="M 250 690 C 250 730, 270 760, 290 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 用户访问 -> 数据聚合 -> 分析模型 -> 可视化图表 -->
  <path d="M 380 835 Q 415 835 450 835" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 630 835 Q 665 835 700 835" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 880 835 Q 915 835 950 835" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据反馈循环 -->
  <path d="M 290 800 C 150 750, 150 300, 300 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="550" font-size="11" fill="#666" transform="rotate(-90, 100, 550)">数据反馈循环</text>

  <!-- 同步循环 -->
  <path d="M 850 690 C 1250 650, 1250 200, 1100 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1200" y="400" font-size="11" fill="#666" transform="rotate(90, 1200, 400)">定时同步循环</text>

</svg>