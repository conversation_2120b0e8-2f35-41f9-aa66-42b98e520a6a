<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下方区域联动-研发人员业务流程</text>

  <!-- 阶段一：数据初始化与地图生成 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与地图生成</text>
  
  <!-- 节点1: 数据拉取同步 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据拉取同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人才库、项目成员基础数据</text>
  </g>

  <!-- 节点2: 数据归集整合 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归集整合</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">区域分布与属性标签生成</text>
  </g>

  <!-- 节点3: 地图热力图生成 -->
  <g transform="translate(950, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图热力图生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">宁波地图12个区县分布</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 670 165 Q 810 165 950 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与类型选择 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与类型选择</text>

  <!-- 节点4: 页面访问加载 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面访问加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">研发人员联动区域页面</text>
  </g>

  <!-- 节点5: 地图交互操作 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地图交互操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">悬停/点击区域联动</text>
  </g>

  <!-- 节点6: 条件筛选设置 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选设置</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人才类型、技术领域等</text>
  </g>

  <!-- 连接线 地图生成 -> 页面访问 -->
  <path d="M 1060 200 C 1060 240, 400 270, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 420 345 Q 460 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 720 345 Q 760 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：联动展示与数据钻取 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：联动展示与数据钻取</text>

  <!-- 节点7: 统计总览更新 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计总览更新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">区域统计数据联动</text>
  </g>

  <!-- 节点8: 多维属性分析 -->
  <g transform="translate(450, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多维属性分析</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">交互式数据钻取对比</text>
  </g>

  <!-- 节点9: 清册明细展示 -->
  <g transform="translate(750, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">清册明细展示</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人才列表详细信息</text>
  </g>

  <!-- 连接线 地图交互 -> 统计总览 -->
  <path d="M 500 380 C 400 420, 300 450, 260 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 条件筛选 -> 多维分析 -->
  <path d="M 800 380 C 700 420, 600 450, 560 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -->
  <path d="M 370 525 Q 410 525 450 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 670 525 Q 710 525 750 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据管理与决策支撑 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据管理与决策支撑</text>

  <!-- 节点10: 数据导出管理 -->
  <g transform="translate(250, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出管理</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">支持后续管理操作</text>
  </g>

  <!-- 节点11: 操作日志归档 -->
  <g transform="translate(550, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志归档</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">分析结果持续记录</text>
  </g>

  <!-- 节点12: 决策数据支撑 -->
  <g transform="translate(850, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">决策数据支撑</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">政策制定与研值服务</text>
  </g>

  <!-- 连接线 清册明细 -> 数据导出 -->
  <path d="M 750 560 C 650 600, 450 640, 360 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 470 705 Q 510 705 550 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12 -->
  <path d="M 770 705 Q 810 705 850 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 决策支撑 -> 数据拉取 -->
  <path d="M 960 670 C 1200 600, 1300 300, 1200 200, 1100 100, 300 100, 260 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="350" text-anchor="middle" font-size="11" fill="#666">数据更新反馈</text>

  <!-- 反馈循环2: 操作日志 -> 地图交互 -->
  <path d="M 550 705 C 100 650, 50 500, 100 400, 200 350, 500 350" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="550" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 100, 550)">行为分析优化</text>

</svg>