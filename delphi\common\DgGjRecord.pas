unit DgGjRecord;

interface

uses
  Classes;

type
  TDgGjRecord = class
  private
    FDgccsjgzid: integer;
    FDdh: string;
    FKh: string;
    FKsbm: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FCheckState: string;
    FDds: integer;
    FScs: integer;
    FODP_Date: string;
    FGzrqq: string;
    FGzrqz: string;
    FEmployee_Id: string;
    FEmployee_Name: string;
    FGx: string;
    FGj: double;
    FXs: double;
    FGjhj: double;
    FRecordType: string;
    FLine_Number: string;
    FStyle_Id: string;
    FOperation_Code: string;
    FQuantity: integer;
    FColour: string;
    FSize: string;
    Fstyle_order_code: string;
    Fpay_Rate: string;
    Ffmis_op_code: string;
    FOperation_Desc: string;
    FChar_Codes: string;
    FEmployeeID_codes: string;
    FKsAll: string;
    FStyle_ks: string;
  public
    property Dgccsjgzid: integer read FDgccsjgzid write FDgccsjgzid;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Ksbm: string read FKsbm write FKsbm;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Dds: integer read FDds write FDds;
    property Scs: integer read FScs write FScs;
    property CheckState: string read FCheckState write FCheckState;
    property ODP_Date: string read FODP_Date write FODP_Date;
    property Gzrqq: string read FGzrqq write FGzrqq;
    property Gzrqz: string read FGzrqz write FGzrqz;
    property Employee_Id: string read FEmployee_Id write FEmployee_Id;
    property Employee_Name: string read FEmployee_Name write FEmployee_Name;
    property Gx: string read FGx write FGx;
    property Gj: double read FGj write FGj;
    property Xs: double read FXs write FXs;
    property Gjhj: double read FGjhj write FGjhj;
    property RecordType: string read FRecordType write FRecordType;
    property Line_Number: string read FLine_Number write FLine_Number;
    property Style_Id: string read FStyle_Id write FStyle_Id;
    property Operation_Code: string read FOperation_Code write FOperation_Code;
    property Quantity: integer read FQuantity write FQuantity;
    property Colour: string read FColour write FColour;
    property Size: string read FSize write FSize;
    property style_order_code: string read Fstyle_order_code
      write Fstyle_order_code;
    property pay_Rate: string read Fpay_Rate write Fpay_Rate;
    property fmis_op_code: string read Ffmis_op_code write Ffmis_op_code;
    property Operation_Desc: string read FOperation_Desc write FOperation_Desc;
    property EmployeeID_codes: string read FEmployeeID_codes
      write FEmployeeID_codes;
    property Char_Codes: string read FChar_Codes write FChar_Codes;
    property KsAll: string read FKsAll write FKsAll;
    property Style_ks: string read FStyle_ks write FStyle_ks;
  end;

implementation

end.
