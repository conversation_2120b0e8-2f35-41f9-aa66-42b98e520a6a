<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果信息管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入成果信息模块</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">根据机构及权限预加载</text>
  </g>

  <!-- 节点2: 默认筛选条件 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">加载默认筛选条件</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">拉取成果类别统计数据</text>
  </g>

  <!-- 节点3: 指标总览区 -->
  <g transform="translate(900, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">顶部指标总览区</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">展现统计数据</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 520 165 Q 560 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 820 165 Q 860 165 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点4: 用户调整筛选 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户调整筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击查询按钮</text>
  </g>

  <!-- 节点5: 记录查询日志 -->
  <g transform="translate(450, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录查询日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新任务状态为查询中</text>
  </g>

  <!-- 节点6: 成果库检索 -->
  <g transform="translate(700, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果库检索</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">返回结果集</text>
  </g>

  <!-- 连接线 阶段一 -> 阶段二 -->
  <path d="M 710 200 C 710 240, 300 270, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 425 345 450 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 345 Q 675 345 700 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据处理与展示 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与展示</text>

  <!-- 节点7: 字段映射处理 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段映射处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">结构化处理数据</text>
  </g>

  <!-- 节点8: 数据源写入 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据源写入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">表格与图表数据源</text>
  </g>

  <!-- 节点9: 关联关系解析 -->
  <g transform="translate(650, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联关系解析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">项目、人才、创新主体</text>
  </g>

  <!-- 节点10: 界面刷新 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">界面刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">表格区与图表分析区</text>
  </g>

  <!-- 连接线 阶段二 -> 阶段三 -->
  <path d="M 800 380 C 800 420, 250 460, 250 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 350 525 Q 375 525 400 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 525 Q 625 525 650 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 525 Q 875 525 900 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与维护 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与维护</text>

  <!-- 节点11: 导出操作 -->
  <g transform="translate(200, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成导出任务记录</text>
  </g>

  <!-- 节点12: 文件生成 -->
  <g transform="translate(450, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">封装结果集与图表</text>
  </g>

  <!-- 节点13: 缓存视图 -->
  <g transform="translate(700, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存查询视图</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">后续快速复用</text>
  </g>

  <!-- 节点14: 增量同步 -->
  <g transform="translate(950, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">增量同步</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">定时维护任务</text>
  </g>

  <!-- 连接线 阶段三 -> 阶段四 -->
  <path d="M 1000 560 C 1000 600, 300 640, 300 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 560 C 750 600, 800 640, 800 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12, 13, 14 -->
  <path d="M 400 705 Q 425 705 450 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 705 Q 675 705 700 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 705 Q 925 705 950 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <path d="M 1050 670 C 1100 650, 1100 200, 1010 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1120" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 1120, 400)">数据同步反馈</text>

</svg>