<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">风险评估与预警分析流程图</text>

  <!-- 阶段一：数据触发与加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据触发与加载</text>
  
  <!-- 节点1: 用户操作触发 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户操作触发</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">进入页面或调整筛选条件</text>
  </g>

  <!-- 节点2: 综合数据服务 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">综合数据服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取多维度数据并合并</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据处理与分析 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据处理与分析</text>

  <!-- 节点3: 数据校验与清洗 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验与清洗</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">完整性校验，生成质量报告</text>
  </g>

  <!-- 节点4: 风险分计算 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">风险分计算</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">依据阈值计算综合风险分</text>
  </g>

  <!-- 连接线 数据服务 -> 校验清洗 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据服务 -> 风险计算 -->
  <path d="M 750 320 C 850 350, 950 380, 1000 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：模型生成与可视化 -->
  <text x="700" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：模型生成与可视化</text>

  <!-- 节点5: 热力模型生成 -->
  <g transform="translate(200, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">热力模型生成</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">区域维度聚合风险数据</text>
  </g>

  <!-- 节点6: 学历职称分析 -->
  <g transform="translate(600, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">学历职称分析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">计算比例并评估风险阈值</text>
  </g>

  <!-- 节点7: 前端渲染 -->
  <g transform="translate(1000, 600)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">热力图、风险列表、分布图</text>
  </g>

  <!-- 连接线 校验清洗 -> 热力模型 -->
  <path d="M 350 490 C 320 520, 320 560, 300 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 风险计算 -> 学历职称 -->
  <path d="M 950 490 C 850 520, 750 560, 700 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 学历职称 -> 前端渲染 -->
  <path d="M 800 635 C 850 635, 900 635, 1000 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 热力模型 -> 前端渲染 -->
  <path d="M 400 635 C 600 635, 800 635, 1000 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：交互与监控 -->
  <text x="700" y="750" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：交互与监控</text>
  
  <!-- 节点8: 用户交互操作 -->
  <g transform="translate(200, 800)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">修改阈值、确认预警、导出</text>
  </g>

  <!-- 节点9: 持续监控 -->
  <g transform="translate(600, 800)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">持续监控</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">风险事件与人员结构变化</text>
  </g>

  <!-- 节点10: 预警推送 -->
  <g transform="translate(1000, 800)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">预警推送</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">消息中心、邮件、短信通知</text>
  </g>

  <!-- 连接线 前端渲染 -> 用户交互 -->
  <path d="M 1000 670 C 800 720, 500 750, 300 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 持续监控 -> 预警推送 -->
  <path d="M 800 835 C 850 835, 900 835, 1000 835" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：用户交互 -> 数据服务 -->
  <path d="M 250 800 C 150 700, 150 400, 550 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="550" text-anchor="middle" font-size="12" fill="#555">反馈循环</text>

  <!-- 监控循环：持续监控 -> 综合数据服务 -->
  <path d="M 650 800 C 650 750, 650 400, 650 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="580" y="560" text-anchor="middle" font-size="12" fill="#555">监控循环</text>

</svg>