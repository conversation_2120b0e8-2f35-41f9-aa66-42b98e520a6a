<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>法人科技特派员备案登记管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900">法人科技特派员备案登记管理</h1>
            <button onclick="openModal('addRecordModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                新增备案
            </button>
        </div>

        <!-- 筛选区域 -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">单位名称</label>
                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入单位名称">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">备案状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                        <option value="draft">草稿</option>
                        <option value="pending">待审核</option>
                        <option value="approved">已备案</option>
                        <option value="rejected">已退回</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">备案批次</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部批次</option>
                        <option value="2023">2023年度</option>
                        <option value="2022">2022年度</option>
                        <option value="2021">2021年度</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">提交日期</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-1/2 px-2 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                        <input type="date" class="w-1/2 px-2 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    查询
                </button>
            </div>
        </div>

        <!-- 备案记录列表 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">统一社会信用代码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备案批次</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">提交时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备案状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市科技创新研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91330201MA2XXXXXX</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年度</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已备案</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewRecord('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editRecord('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市农业科技服务有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91330201MA2XXXXXX</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年度</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-20 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewRecord('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editRecord('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市生物技术研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91330201MA2XXXXXX</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年度</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10 11:20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已退回</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewRecord('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editRecord('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市环保科技服务中心</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91330201MA2XXXXXX</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023年度</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-25 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">草稿</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewRecord('4')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editRecord('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑备案记录模态框 -->
    <div id="addRecordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">法人科技特派员备案登记</h3>
                <button onclick="closeModal('addRecordModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <form class="space-y-6">
                <!-- 备案批次信息 -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="text-md font-medium text-blue-800 mb-2">备案批次信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案年度</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                                <option>2023年度</option>
                                <option>2022年度</option>
                                <option>2021年度</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案开始时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" value="2023-05-01">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案截止时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" value="2023-06-30">
                        </div>
                    </div>
                </div>

                <!-- 法人单位基本信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">法人单位基本信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入单位名称" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入18位信用代码" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位性质 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                <option value="">请选择单位性质</option>
                                <option value="enterprise">企业</option>
                                <option value="institute">科研院所</option>
                                <option value="university">高校</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">法人代表 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入法人代表姓名" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位地址 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入详细地址" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                            <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入联系电话" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                            <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入电子邮箱">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">授权负责人</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入负责人姓名">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">负责人职务</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入负责人职务">
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">单位简介</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入单位简介（500字以内）"></textarea>
                    </div>
                </div>

                <!-- 结对园区信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">结对园区信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">园区名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入园区名称" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">园区地址 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入园区地址" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入管理单位" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">区域类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                <option value="">请选择区域类型</option>
                                <option value="agricultural">农业园区</option>
                                <option value="industrial">工业园区</option>
                                <option value="technology">科技园区</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">服务联系窗口</label>
                        <textarea rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入服务联系窗口信息"></textarea>
                    </div>
                </div>

                <!-- 服务任务与协议信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">服务任务与协议信息</h4>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">服务协议主要内容 <span class="text-red-500">*</span></label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入服务协议主要内容（1000字以内）" required></textarea>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务说明 <span class="text-red-500">*</span></label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入任务说明（500字以内）" required></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">预期目标 <span class="text-red-500">*</span></label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500" placeholder="请输入预期目标（300字以内）" required></textarea>
                    </div>
                </div>

                <!-- 主要成员信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">主要成员信息</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出生年月</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务内容</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded-md" placeholder="姓名">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <select class="w-full px-2 py-1 border border-gray-300 rounded-md">
                                            <option value="">选择</option>
                                            <option value="male">男</option>
                                            <option value="female">女</option>
                                        </select>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="date" class="w-full px-2 py-1 border border-gray-300 rounded-md">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded-md" placeholder="职务">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="text" class="w-full px-2 py-1 border border-gray-300 rounded-md" placeholder="服务内容">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button type="button" class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-between">
                        <button type="button" class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                            导入模板
                        </button>
                        <button type="button" class="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200">
                            添加成员
                        </button>
                    </div>
                </div>

                <!-- 附件上传 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">附件上传</h4>
                    <div class="space-y-4">
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传科技服务协议</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .doc, .docx 格式，最大10MB</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only">
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload2" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传单位授权函</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .jpg, .png 格式，最大5MB</span>
                                </label>
                                <input id="file-upload2" name="file-upload2" type="file" class="sr-only">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 pt-6">
                    <button type="button" onclick="closeModal('addRecordModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                        保存草稿
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        提交备案
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 查看备案详情模态框 -->
    <div id="viewRecordModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">法人科技特派员备案详情</h3>
                <button onclick="closeModal('viewRecordModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <!-- 备案批次信息 -->
                <div class="bg-blue-50 p-4 rounded-lg">
                    <h4 class="text-md font-medium text-blue-800 mb-2">备案批次信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案年度</label>
                            <p class="text-sm text-gray-900">2023年度</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案开始时间</label>
                            <p class="text-sm text-gray-900">2023-05-01</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">备案截止时间</label>
                            <p class="text-sm text-gray-900">2023-06-30</p>
                        </div>
                    </div>
                </div>

                <!-- 法人单位基本信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">法人单位基本信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位名称</label>
                            <p class="text-sm text-gray-900">宁波市科技创新研究院</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码</label>
                            <p class="text-sm text-gray-900">91330201MA2XXXXXX</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位性质</label>
                            <p class="text-sm text-gray-900">科研院所</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">法人代表</label>
                            <p class="text-sm text-gray-900">张某某</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">单位地址</label>
                            <p class="text-sm text-gray-900">宁波市高新区科技大道123号</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <p class="text-sm text-gray-900">0571-88888888</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">授权负责人</label>
                            <p class="text-sm text-gray-900">李某某</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">负责人职务</label>
                            <p class="text-sm text-gray-900">科技服务部主任</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">单位简介</label>
                        <div class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                            宁波市科技创新研究院成立于2010年，是宁波市重点科研机构，主要从事科技创新研究、技术转移转化和科技服务等工作。现有科研人员120人，其中高级职称45人，博士学历60人。近年来承担国家级项目15项，省部级项目30余项，获得省部级以上科技奖励8项。
                        </div>
                    </div>
                </div>

                <!-- 结对园区信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">结对园区信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">园区名称</label>
                            <p class="text-sm text-gray-900">宁波市现代农业科技园区</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">园区地址</label>
                            <p class="text-sm text-gray-900">宁波市鄞州区科技路88号</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理单位</label>
                            <p class="text-sm text-gray-900">宁波市农业科技发展有限公司</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">区域类型</label>
                            <p class="text-sm text-gray-900">农业园区</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">服务联系窗口</label>
                        <div class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                            联系人：王某某
                            联系电话：0571-66666666
                            电子邮箱：<EMAIL>
                            办公地址：宁波市鄞州区科技路88号农业科技园区3号楼201室
                        </div>
                    </div>
                </div>

                <!-- 服务任务与协议信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">服务任务与协议信息</h4>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">服务协议主要内容</label>
                        <div class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                            根据协议，宁波市科技创新研究院将为宁波市现代农业科技园区提供以下科技服务：
                            1. 农业科技创新咨询服务，包括新品种引进、新技术推广等；
                            2. 农业科技人才培养，每年组织不少于4次专题培训；
                            3. 农业科技成果转化服务，促进园区科技成果产业化；
                            4. 农业科技项目合作申报，共同申报各级科技项目；
                            5. 农业科技信息共享，建立科技资源数据库。
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">任务说明</label>
                        <div class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                            1. 组织专家团队定期走访园区，了解科技需求；
                            2. 针对园区企业技术难题，提供解决方案；
                            3. 协助园区申报农业科技项目，争取政策支持；
                            4. 开展农业科技培训，提升园区科技水平；
                            5. 促进科技成果转化，推动园区创新发展。
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">预期目标</label>
                        <div class="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">
                            1. 帮助园区企业解决技术难题10项以上；
                            2. 协助园区申报科技项目5项以上；
                            3. 组织科技培训4次，培训人员200人次；
                            4. 促进科技成果转化3项以上；
                            5. 提升园区科技水平，促进农业增效农民增收。
                        </div>
                    </div>
                </div>

                <!-- 主要成员信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">主要成员信息</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出生年月</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务内容</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张某某</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1975-05-12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农业科技创新咨询</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李某某</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1980-08-23</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">副研究员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技人才培养</td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王某某</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1985-11-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">助理研究员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技成果转化</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div>
                    <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">附件信息</h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-700">科技服务协议.pdf</span>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-700">单位授权函.jpg</span>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-end space-x-4 pt-6">
                    <button type="button" onclick="closeModal('viewRecordModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        关闭
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        打印备案表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // 查看备案记录
        function viewRecord(id) {
            openModal('viewRecordModal');
            // 这里可以添加根据id加载数据的逻辑
        }

        // 编辑备案记录
        function editRecord(id) {
            openModal('addRecordModal');
            // 这里可以添加根据id加载数据的逻辑
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定所有模态框的点击外部关闭事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 绑定ESC键关闭
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });

            // 表单提交处理（原型演示）
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (原型演示)');
                    closeModal('addRecordModal');
                });
            });
        });
    </script>
</body>
</html>