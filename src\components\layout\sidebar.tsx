'use client'

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  ChevronDown,
  ChevronRight,
  Settings,
  Users,
  FileText,
  Brain,
  Database,
  Calculator,
  UserPlus,
  Building,
  Monitor,
  Workflow,
  Code2,
  Search,
  UserCheck,
  Star,
  Briefcase,
  BarChart3,
  FolderOpen,
  Microscope,
  Lightbulb,
  Building2,
  BookOpen,
  MapPin,
  Scale
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"
import { usePathname } from "next/navigation"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {}

export function Sidebar({ className }: SidebarProps) {
  const [openMenus, setOpenMenus] = useState<string[]>(["基础模板"])
  const [openSubMenus, setOpenSubMenus] = useState<string[]>(["资源概览"])
  const [openThirdMenus, setOpenThirdMenus] = useState<string[]>(["创新库管理"])
  const [openFourthMenus, setOpenFourthMenus] = useState<string[]>([])
  const pathname = usePathname()

  const menuItems = [
    {
      title: "商剑分析平台",
      icon: BarChart3,
      href: "#",
      subItems: [
        {
          title: "生产统计分析",
          icon: Monitor,
          href: "/sjfx",
        }
      ]
    },
    {
      title: "基础模板",
      icon: FileText,
      href: "#",
      subItems: [
        {
          title: "AI预算项目审核",
          icon: Calculator,
          href: "/template/ai-budget-review",
        },
        {
          title: "客户详情管理",
          icon: Users,
          href: "/template/customer-profile",
        },
        {
          title: "文档处理系统",
          icon: FileText,
          href: "/template/document-management",
        },
        {
          title: "支付申请系统",
          icon: Database,
          href: "/template/zfsq",
        },
        {
          title: "资源概览",
          icon: Monitor,
          href: "#",
          subItems: [
            {
              title: "基础设施",
              icon: Building,
              href: "/template/assets/infrastructure",
            },
            {
              title: "应用管理",
              icon: Brain,
              href: "/template/assets/applications",
            },
            {
              title: "运维监控",
              icon: Workflow,
              href: "/template/assets/operations",
            },
            {
              title: "概览仪表盘",
              icon: Monitor,
              href: "/template/assets/overview",
            },
            {
              title: "可视化工具",
              icon: Calculator,
              href: "/template/assets/visualization",
            }
          ]
        }
      ]
    },
    {
      title: "智能体测试",
      icon: FileText,
      href: "#",
      subItems: [
        {
          title: "dify单条",
          icon: Calculator,
          href: "/agent/dify",
        },
        {
          title: "智能问答",
          icon: Calculator,
          href: "/agent/difyznwd",
        },
        {
          title: "批量测试",
          icon: Database,
          href: "/agent/batch-test",
        }
      ]
    },
    {
      title: "研值在线",
      icon: Brain,
      href: "#",
      subItems: [
        {
          title: "创新主体科研码管理",
          icon: Code2,
          href: "#",
          subItems: [
            {
              title: "科研码管理",
              icon: Settings,
              href: "/research/innovation-code/management",
            },
            {
              title: "科研码展示",
              icon: Monitor,
              href: "/research/innovation-code/display",
            }
          ]
        },
        {
          title: "创新库管理",
          icon: Database,
          href: "#",
          subItems: [
            {
              title: "人才信息管理",
              icon: Users,
              href: "#",
              subItems: [
                {
                  title: "人才总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/talent/overview",
                },
                {
                  title: "各类人才管理",
                  icon: UserCheck,
                  href: "/research/innovation-lib/talent/management",
                },
                {
                  title: "科技特派员活动管理",
                  icon: Star,
                  href: "/research/innovation-lib/talent/specialist",
                }
              ]
            },
            {
              title: "项目信息管理",
              icon: Briefcase,
              href: "#",
              subItems: [
                {
                  title: "项目总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/project/overview",
                },
                {
                  title: "各类项目管理",
                  icon: FolderOpen,
                  href: "/research/innovation-lib/project/management",
                }
              ]
            },
            {
              title: "设备信息管理",
              icon: Microscope,
              href: "#",
              subItems: [
                {
                  title: "仪器设备总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/equipment/overview",
                },
                {
                  title: "仪器设备管理",
                  icon: Settings,
                  href: "/research/innovation-lib/equipment/management",
                }
              ]
            },
            {
              title: "成果信息管理",
              icon: Lightbulb,
              href: "#",
              subItems: [
                {
                  title: "成果总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/achievement/overview",
                },
                {
                  title: "科技成果管理",
                  icon: Brain,
                  href: "/research/innovation-lib/achievement/management",
                }
              ]
            },
            {
              title: "机构信息管理",
              icon: Building2,
              href: "#",
              subItems: [
                {
                  title: "机构总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/institution/overview",
                },
                {
                  title: "机构管理",
                  icon: Building,
                  href: "/research/innovation-lib/institution/management",
                },
                {
                  title: "企业信息展示",
                  icon: Building,
                  href: "/research/innovation-lib/institution/enterprise-info",
                }
              ]
            },
            {
              title: "指南信息管理",
              icon: BookOpen,
              href: "#",
              subItems: [
                {
                  title: "指南总览分析",
                  icon: BarChart3,
                  href: "/research/innovation-lib/guide/overview",
                },
                {
                  title: "指南管理",
                  icon: FileText,
                  href: "/research/innovation-lib/guide/management",
                },
                {
                  title: "指南编制",
                  icon: Settings,
                  href: "/research/innovation-lib/guide/creation",
                }
              ]
            },
            {
              title: "其他信息管理",
              icon: Database,
              href: "#",
              subItems: [
                {
                  title: "地理信息分析管理",
                  icon: MapPin,
                  href: "#",
                  subItems: [
                    {
                      title: "地址匹配查询",
                      icon: MapPin,
                      href: "/research/innovation-lib/other/geography/address-matching",
                    }
                  ]
                },
                {
                  title: "政策法规库管理",
                  icon: Scale,
                  href: "/research/innovation-lib/other/policy",
                }
              ]
            }
          ]
        }
      ]
    }
  ]

  const toggleMenu = (title: string) => {
    setOpenMenus(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const toggleSubMenu = (title: string) => {
    setOpenSubMenus(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const toggleThirdMenu = (title: string) => {
    setOpenThirdMenus(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const toggleFourthMenu = (title: string) => {
    setOpenFourthMenus(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const renderMenuItem = (item: any, level: number = 0) => {
    // 增加缩进量，让层次更清楚
    const paddingLeft = level === 0 ? 12 : 
                       level === 1 ? 24 : 
                       level === 2 ? 36 : 
                       level === 3 ? 48 : 60
    const paddingClass = `pl-[${paddingLeft}px]`
    const iconSize = level >= 2 ? "h-4 w-4" : "h-5 w-5"
    const textSize = level === 0 ? "text-base font-semibold text-blue-800" :
                     level === 1 ? "text-sm font-medium text-gray-700" :
                     "text-sm font-normal text-gray-600"
    const hoverBg = level === 0 ? "hover:bg-[#F0F7FF]" : 
                   level === 1 ? "hover:bg-[#F5F9FF]" : 
                   level === 2 ? "hover:bg-[#FAFBFF]" : "hover:bg-[#FCFDFF]"
    const baseBg = level === 0 ? "bg-blue-50 border-l-4 border-blue-500" : 
                   level === 1 ? "bg-gray-50" : ""
    
    if (item.subItems) {
      const isOpen = level === 0 ? openMenus.includes(item.title) :
                   level === 1 ? openSubMenus.includes(item.title) :
                   level === 2 ? openThirdMenus.includes(item.title) :
                   openFourthMenus.includes(item.title)
      
      const toggleFunction = level === 0 ? toggleMenu :
                           level === 1 ? toggleSubMenu :
                           level === 2 ? toggleThirdMenu :
                           toggleFourthMenu

      return (
        <div key={item.title} className={level === 0 ? "mt-4 first:mt-0" : ""}>
          <Button
            variant="ghost"
            className={cn("w-full justify-start", hoverBg, baseBg, paddingClass, textSize)}
            onClick={() => toggleFunction(item.title)}
          >
            <item.icon className={cn(
              "mr-2", 
              level === 0 ? "text-blue-600" : 
              level === 1 ? "text-blue-500" : "text-gray-500", 
              iconSize
            )} />
            <span>{item.title}</span>
            {level === 0 ? (
              <ChevronDown 
                className={cn(
                  "ml-auto h-4 w-4 transition-transform",
                  isOpen && "transform rotate-180"
                )} 
              />
            ) : (
              <ChevronRight 
                className={cn(
                  "ml-auto h-4 w-4 transition-transform",
                  isOpen && "transform rotate-90"
                )} 
              />
            )}
          </Button>
          {isOpen && (
            <div className={cn(
              "space-y-1",
              level > 0 && "ml-2 border-l-2 border-gray-100"
            )}>
              {item.subItems.map((subItem: any) => renderMenuItem(subItem, level + 1))}
            </div>
          )}
        </div>
      )
    } else {
      return (
        <div key={item.title} className={level === 0 ? "mt-4 first:mt-0" : ""}>
          <Link href={item.href}>
            <Button
            variant="ghost"
            className={cn(
              "w-full justify-start",
              hoverBg,
              baseBg,
              paddingClass,
              textSize,
              pathname === item.href && "bg-[#F0F7FF]"
            )}
          >
            <item.icon className={cn(
              "mr-2", 
              level === 0 ? "text-blue-600" : 
              level === 1 ? "text-blue-500" : "text-gray-500", 
              iconSize
            )} />
            <span>{item.title}</span>
            </Button>
          </Link>
        </div>
      )
    }
  }

  return (
    <div className={cn("pb-12 min-h-screen", className)}>
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <div className="space-y-1">
            {menuItems.map((item) => renderMenuItem(item))}
          </div>
        </div>
      </div>
    </div>
  )
}