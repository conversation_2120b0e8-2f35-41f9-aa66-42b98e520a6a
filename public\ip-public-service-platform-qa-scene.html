<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识产权公共服务平台智能问答</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .floating-qa {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 380px;
            height: 500px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .qa-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1001;
        }
        .message-bubble {
            animation: fadeInUp 0.3s ease-out;
        }
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(15px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .typing-indicator {
            animation: pulse 1.5s infinite;
        }
        .scenario-card {
            transition: all 0.3s ease;
        }
        .scenario-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <!-- 主页面内容 -->
    <div class="flex-1 p-6">
        <div class="h-[calc(100vh-120px)] flex flex-col">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-blue-800 flex items-center">
                    <svg class="mr-3 h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                    </svg>
                    知识产权智能问答平台
                </h1>
                <div class="flex gap-3">
                    <button onclick="openQAWindow()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg flex items-center gap-2 transition-colors shadow-md">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        甬知AI问答
                    </button>
                    <button onclick="userLogin()" class="bg-white border-2 border-blue-200 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                        <span id="loginText">登录</span>
                    </button>
                </div>
            </div>

            <!-- 智能问答场景展示 -->
            <div class="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 左侧：问答场景示例 -->
                <div class="space-y-6">
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h2 class="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                            <svg class="mr-2 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.12 9l3 3m-3 0l3-3m-3 3H3m9 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            热门咨询场景
                        </h2>
                        <div class="space-y-4">
                            <div class="scenario-card bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-4 cursor-pointer border border-blue-100" onclick="askScenario('专利申请流程')">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium text-blue-800 mb-1">专利申请指导</h3>
                                        <p class="text-sm text-gray-600">"我想申请一项发明专利，需要准备什么材料？"</p>
                                        <div class="flex items-center mt-2 text-xs text-blue-600">
                                            <span class="mr-2">AI智能回答</span>
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="scenario-card bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 cursor-pointer border border-green-100" onclick="askScenario('商标注册查询')">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0-8h2m-2 0V5a2 2 0 012-2h2a2 2 0 012 2v2m-4 0h4m0 0v6a2 2 0 01-2 2h-2m0-6V5"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium text-green-800 mb-1">商标注册咨询</h3>
                                        <p class="text-sm text-gray-600">"我的商标能否注册？需要多长时间？"</p>
                                        <div class="flex items-center mt-2 text-xs text-green-600">
                                            <span class="mr-2">实时政策解读</span>
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="scenario-card bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-4 cursor-pointer border border-purple-100" onclick="askScenario('版权保护')">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1">
                                        <h3 class="font-medium text-purple-800 mb-1">版权保护指导</h3>
                                        <p class="text-sm text-gray-600">"如何保护我的软件著作权？"</p>
                                        <div class="flex items-center mt-2 text-xs text-purple-600">
                                            <span class="mr-2">专业法律解答</span>
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用统计 -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">今日服务统计</h3>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center bg-blue-50 rounded-lg p-3">
                                <div class="text-xl font-bold text-blue-600">2,845</div>
                                <div class="text-xs text-gray-600">智能问答次数</div>
                            </div>
                            <div class="text-center bg-green-50 rounded-lg p-3">
                                <div class="text-xl font-bold text-green-600">96.8%</div>
                                <div class="text-xs text-gray-600">问题解决率</div>
                            </div>
                            <div class="text-center bg-purple-50 rounded-lg p-3">
                                <div class="text-xl font-bold text-purple-600">1.2s</div>
                                <div class="text-xs text-gray-600">平均响应时间</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧：实时对话演示 -->
                <div class="space-y-6">
                    <div class="bg-white rounded-xl shadow-md border border-blue-100 h-96">
                        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-t-xl px-4 py-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="text-white font-medium">甬知AI助手</h3>
                                        <p class="text-blue-100 text-xs">知识产权专业咨询</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-1">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-blue-100 text-xs">在线</span>
                                </div>
                            </div>
                        </div>

                        <div id="demoChat" class="h-64 overflow-y-auto p-4 space-y-3">
                            <!-- 演示对话内容 -->
                            <div class="message-bubble">
                                <div class="flex items-start space-x-2">
                                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                                        </svg>
                                    </div>
                                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                                        <p class="text-sm text-gray-800">您好！我是甬知AI，专门为您提供知识产权咨询服务。请问需要了解什么？</p>
                                        <div class="mt-2 text-xs text-gray-500">10:30</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 p-3">
                            <div class="flex items-center space-x-2">
                                <input
                                    id="demoInput"
                                    type="text"
                                    placeholder="输入您的问题体验智能问答..."
                                    class="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    onkeypress="handleDemoInput(event)"
                                />
                                <button
                                    onclick="sendDemoMessage()"
                                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-3 py-2 transition-colors"
                                >
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 用户状态面板 -->
                    <div class="bg-white rounded-xl shadow-md p-4 border border-blue-100">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-medium text-blue-800">当前状态</h3>
                            <div id="userMode" class="px-2 py-1 bg-orange-100 text-orange-800 text-xs rounded-full">匿名模式</div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">会话ID:</span>
                                <span id="sessionId" class="text-gray-500 font-mono text-xs">UUID-xxx...</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">服务状态:</span>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                                    <span class="text-green-600 text-xs">正常</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮窗问答触发按钮 -->
    <div id="qaTrigger" class="qa-trigger">
        <button
            onclick="toggleQAFloat()"
            class="w-14 h-14 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 rounded-full shadow-lg text-white flex items-center justify-center transition-all duration-300 group"
        >
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
        </button>
    </div>

    <!-- 浮窗问答窗口 -->
    <div id="qaFloat" class="floating-qa bg-white rounded-xl shadow-2xl border border-blue-200 hidden">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-t-xl px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-white font-medium text-sm">甬知AI智能问答</h3>
                    <p class="text-blue-100 text-xs">知识产权专业助手</p>
                </div>
            </div>
            <button onclick="toggleQAFloat()" class="text-white hover:bg-blue-500 rounded p-1">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div id="qaMessages" class="h-80 overflow-y-auto p-4 space-y-3">
            <div class="message-bubble">
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">欢迎使用甬知AI智能问答！我可以为您解答知识产权相关问题。</p>
                        <div class="mt-2 text-xs text-gray-500">现在</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="border-t border-gray-200 p-3">
            <div class="flex items-end space-x-2">
                <div class="flex-1">
                    <textarea
                        id="qaInput"
                        placeholder="请输入您的问题..."
                        rows="1"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onkeydown="handleQAInput(event)"
                    ></textarea>
                </div>
                <button
                    onclick="sendQAMessage()"
                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-3 py-2 transition-colors"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        let isLoggedIn = false;
        let sessionUUID = 'uuid_' + Date.now().toString().slice(-8);
        let isQAOpen = false;
        let heartbeatErrors = 0;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSessionDisplay();
            startHeartbeat();
        });

        // 场景问答
        function askScenario(scenario) {
            openQAWindow();
            setTimeout(() => {
                document.getElementById('qaInput').value = `我想了解${scenario}的相关信息`;
                sendQAMessage();
            }, 500);
        }

        // 打开问答窗口
        function openQAWindow() {
            toggleQAFloat();
        }

        // 切换浮窗显示
        function toggleQAFloat() {
            const qaFloat = document.getElementById('qaFloat');
            const trigger = document.getElementById('qaTrigger');
            
            isQAOpen = !isQAOpen;
            
            if (isQAOpen) {
                qaFloat.classList.remove('hidden');
                trigger.style.display = 'none';
            } else {
                qaFloat.classList.add('hidden');
                trigger.style.display = 'block';
            }
        }

        // 处理问答输入
        function handleQAInput(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendQAMessage();
            }
        }

        // 发送问答消息
        function sendQAMessage() {
            const input = document.getElementById('qaInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            addQAMessage(message, 'user');
            input.value = '';
            
            // 模拟AI回复
            simulateQAResponse(message);
        }

        // 添加问答消息
        function addQAMessage(content, type, hasButtons = false) {
            const container = document.getElementById('qaMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-bubble';
            
            const time = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2 justify-end">
                        <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                            <p class="text-sm">${content}</p>
                            <div class="mt-2 text-xs text-blue-100">${time}</div>
                        </div>
                        <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                `;
            } else {
                const feedbackHTML = hasButtons ? `
                    <div class="flex items-center justify-between mt-3 pt-2 border-t border-blue-100">
                        <span class="text-xs text-gray-500">这个回答有帮助吗？</span>
                        <div class="flex space-x-2">
                            <button onclick="submitFeedback(true)" class="text-green-600 hover:bg-green-50 p-1 rounded transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                </svg>
                            </button>
                            <button onclick="submitFeedback(false)" class="text-red-600 hover:bg-red-50 p-1 rounded transition-colors">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                ` : '';

                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                            <p class="text-sm text-gray-800">${content}</p>
                            ${feedbackHTML}
                            <div class="mt-2 text-xs text-gray-500">${time}</div>
                        </div>
                    </div>
                `;
            }
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 模拟AI响应
        function simulateQAResponse(message) {
            // 检查心跳状态
            if (heartbeatErrors >= 3) {
                showFallbackService();
                return;
            }

            setTimeout(() => {
                let response = '';
                
                if (message.includes('专利')) {
                    response = '关于专利申请，您需要准备以下材料：\n\n1. 专利申请请求书\n2. 说明书及摘要\n3. 权利要求书\n4. 相关图纸（如有）\n\n申请流程包括：提交申请→形式审查→公布→实质审查→授权，整个周期约2-3年。\n\n建议您先进行专利检索，确保发明具有新颖性。';
                } else if (message.includes('商标')) {
                    response = '商标注册流程如下：\n\n1. 商标查询（推荐）\n2. 准备申请材料\n3. 提交申请\n4. 形式审查（1个月）\n5. 实质审查（6-8个月）\n6. 公告期（3个月）\n7. 颁发证书\n\n总计约9-12个月。注册费用：官费300元/类别，代理费另计。';
                } else if (message.includes('版权') || message.includes('著作权')) {
                    response = '软件著作权登记流程：\n\n1. 准备申请文件\n2. 提交到版权保护中心\n3. 受理审查（5-10工作日）\n4. 发证\n\n所需材料：申请表、源代码前后各连续30页、说明书等。登记费用：300元，加急另收费。';
                } else {
                    response = '我是甬知AI智能助手，专门为您提供知识产权咨询服务。\n\n我可以帮您解答：\n• 专利申请与保护\n• 商标注册与管理\n• 版权登记与维权\n• 知识产权政策解读\n• 申请流程指导\n\n请告诉我您具体想了解什么？';
                }
                
                addQAMessage(response, 'assistant', true);
                logInteraction(message);
                
            }, 1000 + Math.random() * 1500);
        }

        // 演示聊天
        function handleDemoInput(event) {
            if (event.key === 'Enter') {
                sendDemoMessage();
            }
        }

        function sendDemoMessage() {
            const input = document.getElementById('demoInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            addDemoMessage(message, 'user');
            input.value = '';
            
            setTimeout(() => {
                let response = '这是一个演示回答。实际使用时，AI会基于最新的知识产权法规和政策为您提供准确的专业解答。';
                if (message.includes('专利')) {
                    response = '根据《专利法》规定，发明专利申请需要具备新颖性、创造性和实用性。建议您先进行专利检索...';
                }
                addDemoMessage(response, 'assistant');
            }, 1000);
        }

        function addDemoMessage(content, type) {
            const container = document.getElementById('demoChat');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-bubble';
            
            const time = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2 justify-end">
                        <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                            <p class="text-sm">${content}</p>
                            <div class="mt-2 text-xs text-blue-100">${time}</div>
                        </div>
                        <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                `;
            } else {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                            <p class="text-sm text-gray-800">${content}</p>
                            <div class="mt-2 text-xs text-gray-500">${time}</div>
                        </div>
                    </div>
                `;
            }
            
            container.appendChild(messageDiv);
            container.scrollTop = container.scrollHeight;
        }

        // 用户登录
        function userLogin() {
            if (!isLoggedIn) {
                isLoggedIn = true;
                document.getElementById('loginText').textContent = '张先生';
                document.getElementById('userMode').textContent = '实名用户';
                document.getElementById('userMode').className = 'px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full';
                // 合并匿名会话
                console.log('合并匿名会话记录到用户账户');
            }
        }

        // 更新会话显示
        function updateSessionDisplay() {
            document.getElementById('sessionId').textContent = sessionUUID;
        }

        // 心跳监控
        function startHeartbeat() {
            setInterval(() => {
                // 模拟心跳检测
                if (Math.random() < 0.05) { // 5% 概率出错
                    heartbeatErrors++;
                    console.log('心跳异常，错误次数:', heartbeatErrors);
                } else {
                    heartbeatErrors = 0;
                }
            }, 10000);
        }

        // 显示备用服务
        function showFallbackService() {
            toggleQAFloat();
            alert('AI服务暂时不可用，已为您转接人工服务。\n\n人工热线：************\n在线客服：正在为您连接...');
        }

        // 提交反馈
        function submitFeedback(isPositive) {
            const message = isPositive ? '感谢您的反馈！' : '抱歉没有帮到您，我们会继续改进。';
            console.log('用户反馈:', isPositive ? '有帮助' : '无帮助');
            
            // 显示反馈提示
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 3000);
        }

        // 记录交互日志
        function logInteraction(question) {
            const logData = {
                summary: question.substring(0, 50) + '...',
                timestamp: Date.now(),
                sessionId: sessionUUID,
                userId: isLoggedIn ? 'user_123' : 'anonymous'
            };
            console.log('记录脱敏日志:', logData);
        }
    </script>
</body>
</html> 