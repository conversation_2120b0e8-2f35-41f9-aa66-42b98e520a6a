'use client'

import { ScrollArea } from "@/components/ui/scroll-area"
import { But<PERSON> } from "@/components/ui/button"
import { 
  RotateCcw, 
  Clock, 
  ArrowLeftRight,
  Check,
  X
} from "lucide-react"

interface HistoryItem {
  id: string
  timestamp: string
  user: string
  action: string
  changes: string[]
}

const historyData: HistoryItem[] = [
  {
    id: '1',
    timestamp: '2024-03-20 14:30',
    user: '张三',
    action: '修改布局',
    changes: ['调整组件位置', '更新样式配置']
  },
  {
    id: '2',
    timestamp: '2024-03-20 11:15',
    user: '李四',
    action: '添加组件',
    changes: ['添加趋势图', '配置数据源']
  },
  // ... 更多历史记录
]

export function VisualizationHistory() {
  return (
    <div className="p-4 h-[600px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">版本历史</h3>
        <Button variant="outline" size="sm">
          <RotateCcw className="h-4 w-4 mr-2" />
          还原到此版本
        </Button>
      </div>

      <ScrollArea className="flex-1">
        <div className="space-y-4">
          {historyData.map((item) => (
            <div
              key={item.id}
              className="relative pl-6 pb-4 border-l-2 border-gray-200 last:border-l-0"
            >
              {/* 时间线节点 */}
              <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-white border-2 border-primary" />
              
              {/* 内容 */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-4 w-4 mr-1" />
                    {item.timestamp}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <ArrowLeftRight className="h-4 w-4 mr-1" />
                      对比
                    </Button>
                    <Button variant="ghost" size="sm">
                      <RotateCcw className="h-4 w-4 mr-1" />
                      还原
                    </Button>
                  </div>
                </div>

                <div className="mb-2">
                  <span className="font-medium">{item.user}</span>
                  <span className="mx-2">·</span>
                  <span>{item.action}</span>
                </div>

                <div className="space-y-1">
                  {item.changes.map((change, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-600">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      {change}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
} 