<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">地址区域匹配管理流程</text>

  <!-- 阶段一：规则创建与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：规则创建与校验</text>
  
  <!-- 节点1: 创建/修改规则 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">创建/修改规则</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">运维人员操作</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">系统记录新版本</text>
  </g>

  <!-- 节点2: 校验规则 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验规则</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">公式合法性检查</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">关键词唯一性验证</text>
  </g>

  <!-- 节点3: 标记待发布 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标记待发布</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">校验通过后</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">状态置为"待发布"</text>
  </g>

  <!-- 连接线 阶段一 -->
  <path d="M 300 170 Q 350 170 400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 170 Q 650 170 700 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：规则发布与生效 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：规则发布与生效</text>

  <!-- 节点4: 自动生效 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动生效</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">到达生效时间点</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">状态切换为"生效中"</text>
  </g>

  <!-- 节点5: 写入缓存 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入缓存</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">规则写入缓存</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">推送变更事件</text>
  </g>

  <!-- 节点6: 解析引擎更新 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">解析引擎更新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">刷新内存索引</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">使用新版逻辑</text>
  </g>

  <!-- 连接线 待发布到生效 -->
  <path d="M 800 210 C 800 250, 400 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 阶段二 -->
  <path d="M 400 360 Q 450 360 500 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 360 Q 750 360 800 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：地址解析与追溯 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：地址解析与追溯</text>

  <!-- 节点7: 地址解析 -->
  <g transform="translate(300, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地址解析</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">使用新版逻辑</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">记录版本号</text>
  </g>

  <!-- 节点8: 结果追溯 -->
  <g transform="translate(600, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果追溯</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">保障定位结果</text>
    <text x="100" y="70" text-anchor="middle" font-size="12" fill="#555">可追溯性</text>
  </g>

  <!-- 连接线 引擎到解析 -->
  <path d="M 900 400 C 900 450, 600 480, 400 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 解析到追溯 -->
  <path d="M 500 560 Q 550 560 600 560" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：版本管理与审计 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：版本管理与审计</text>

  <!-- 节点9: 回滚操作 -->
  <g transform="translate(100, 720)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">回滚操作</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">选定历史版本</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">生成回滚记录</text>
  </g>

  <!-- 节点10: 通知引擎 -->
  <g transform="translate(350, 720)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">通知引擎</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">同步通知</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">替换现行规则</text>
  </g>

  <!-- 节点11: 定时归档 -->
  <g transform="translate(600, 720)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时归档</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">失效版本归档</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">操作日志存储</text>
  </g>

  <!-- 节点12: 审计检查 -->
  <g transform="translate(850, 720)" filter="url(#soft-shadow)">
    <rect width="180" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计检查</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">周期性检查</text>
    <text x="90" y="70" text-anchor="middle" font-size="12" fill="#555">报表统计</text>
  </g>

  <!-- 连接线 阶段四 -->
  <path d="M 280 760 Q 315 760 350 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 530 760 Q 565 760 600 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 760 Q 815 760 850 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 回滚反馈循环 -->
  <path d="M 190 720 C 50 650, 50 400, 200 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="80" y="540" text-anchor="middle" font-size="12" fill="#555">回滚</text>

  <!-- 归档连接 -->
  <path d="M 700 600 C 700 650, 700 680, 690 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="720" y="660" text-anchor="middle" font-size="12" fill="#555">定时归档</text>

</svg>