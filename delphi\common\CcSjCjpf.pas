unit CcSjCjpf;

interface

uses
  Classes;

type
  TCcSjCjpf = class
  private
    FSjsccjpfid: Integer;
    FDdid: Integer;
    FScid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FScs: Integer;
    FScts: Integer;
    FKjts: Integer;
    FYuany: string;
    FBzcl: Integer;
    FScgs: Integer;
    FSlxs: double;
    FNdxs: double;
    FShuz: double;
    FZdds: Integer;
    FBil: double;
    FFenz: double;
    FPfyf: string;
    FPfnf: string;
  public
    property Sjsccjpfid: Integer read FSjsccjpfid write FSjsccjpfid;
    property Ddid: Integer read FDdid write FDdid;
    property Scid: Integer read FScid write FScid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Scs: Integer read FScs write FScs;
    property Scts: Integer read FScts write FScts;
    property Kjts: Integer read FKjts write FKjts;
    property Yuany: string read FYuany write FYuany;
    property Bzcl: Integer read FBzcl write FBzcl;
    property Scgs: Integer read FScgs write FScgs;
    property Slxs: double read FSlxs write FSlxs;
    property Ndxs: double read FNdxs write FNdxs;
    property Shuz: double read FShuz write FShuz;
    property Zdds: Integer read FZdds write FZdds;
    property Bil: double read FBil write FBil;
    property Fenz: double read FFenz write FFenz;
    property Pfnf: string read FPfnf write FPfnf;
    property Pfyf: string read FPfyf write FPfyf;
  end;

implementation

end.
