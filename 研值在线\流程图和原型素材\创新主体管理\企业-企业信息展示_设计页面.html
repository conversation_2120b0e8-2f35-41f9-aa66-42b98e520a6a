<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">企业信息展示</h1>
            <p class="mt-2 text-gray-600">宁波市科技企业综合信息管理平台</p>
        </div>

        <!-- 企业概况区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 企业基本信息 -->
                <div class="flex-1">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-xl font-semibold text-gray-900">宁波市智能科技有限公司</h2>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            正常经营
                        </span>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">统一社会信用代码</p>
                            <p class="font-medium">91330201MA2B2X1234</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">注册时间</p>
                            <p class="font-medium">2018-05-15</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">注册资本</p>
                            <p class="font-medium">1000万元人民币</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">企业类型</p>
                            <p class="font-medium">有限责任公司</p>
                        </div>
                        <div class="md:col-span-2">
                            <p class="text-sm text-gray-500">注册地址</p>
                            <p class="font-medium">宁波市高新区聚贤路1299号</p>
                        </div>
                    </div>
                </div>
                
                <!-- 营业执照 -->
                <div class="flex-shrink-0">
                    <div class="border border-gray-200 rounded-md p-2 w-48">
                        <div class="bg-gray-100 h-32 flex items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <p class="text-xs text-center mt-2 text-gray-500">营业执照副本</p>
                    </div>
                </div>
            </div>
            
            <!-- 行业标签 -->
            <div class="mt-4 flex flex-wrap gap-2">
                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">高新技术企业</span>
                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">软件和信息技术服务业</span>
                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">人工智能</span>
            </div>
        </div>

        <!-- 股东信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">股东信息</h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm">查看全部股东</button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 股东占比图表 -->
                <div class="h-64">
                    <canvas id="shareholderChart"></canvas>
                </div>
                
                <!-- 股东列表 -->
                <div>
                    <div class="overflow-hidden border border-gray-200 rounded-lg">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股东名称</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资额</th>
                                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="showShareholderDetail('1')">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波智能投资有限公司</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">企业法人</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">500万元</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">50%</td>
                                </tr>
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="showShareholderDetail('2')">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张明</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">自然人</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">300万元</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">30%</td>
                                </tr>
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="showShareholderDetail('3')">
                                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李华</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">自然人</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">200万元</td>
                                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">20%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对外投资区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">对外投资</h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm">查看全部投资</button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-sm text-blue-800 mb-2">投资总额</p>
                    <p class="text-2xl font-bold text-blue-900">1,250万元</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-sm text-green-800 mb-2">投资企业数</p>
                    <p class="text-2xl font-bold text-green-900">5家</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <p class="text-sm text-purple-800 mb-2">主要投资地区</p>
                    <p class="text-2xl font-bold text-purple-900">宁波市</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">被投资企业</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资金额</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资比例</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波智能科技研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500万元</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">60%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科学研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showCompanyDetail('1')">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波智能数据有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">300万元</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30%</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">大数据</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showCompanyDetail('2')">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 参保信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">参保信息</h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm">参保详情</button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- 参保趋势图 -->
                <div class="h-64">
                    <canvas id="insuranceChart"></canvas>
                </div>
                
                <!-- 参保数据 -->
                <div>
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <p class="text-sm text-gray-500 mb-2">当前有效参保人数</p>
                        <p class="text-2xl font-bold text-gray-900">86人</p>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">养老保险基数</p>
                            <p class="font-medium">8,500元</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">医疗保险基数</p>
                            <p class="font-medium">8,500元</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">失业保险基数</p>
                            <p class="font-medium">8,500元</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">工伤保险基数</p>
                            <p class="font-medium">8,500元</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 研发人员展示区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">研发人员展示</h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm">查看更多</button>
            </div>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <p class="text-sm text-gray-500 mb-1">研发人员总数</p>
                    <p class="text-xl font-bold text-gray-900">45人</p>
                    <p class="text-xs text-green-600 mt-1">↑ 12% 同比</p>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <p class="text-sm text-gray-500 mb-1">技术顾问数</p>
                    <p class="text-xl font-bold text-gray-900">8人</p>
                    <p class="text-xs text-green-600 mt-1">↑ 5% 同比</p>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <p class="text-sm text-gray-500 mb-1">硕博士及以上</p>
                    <p class="text-xl font-bold text-gray-900">32人</p>
                    <p class="text-xs text-green-600 mt-1">↑ 8% 同比</p>
                </div>
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <p class="text-sm text-gray-500 mb-1">高级职称</p>
                    <p class="text-xl font-bold text-gray-900">15人</p>
                    <p class="text-xs text-green-600 mt-1">↑ 3% 同比</p>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学历</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">研发成果</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王强</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6个</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12项</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showResearcherDetail('1')">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李娜</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4个</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8项</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button onclick="showResearcherDetail('2')">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 股东详情弹窗 -->
    <div id="shareholderModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">股东详情</h3>
                    <button onclick="closeModal('shareholderModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">宁波智能投资有限公司</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">股东类型</p>
                                <p class="font-medium">企业法人</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">出资额</p>
                                <p class="font-medium">500万元</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">出资比例</p>
                                <p class="font-medium">50%</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">认缴日期</p>
                                <p class="font-medium">2018-05-15</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">投资关系</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center justify-center h-32">
                                <p class="text-gray-500">股权穿透图展示区域</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 企业详情弹窗 -->
    <div id="companyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">企业详情</h3>
                    <button onclick="closeModal('companyModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-6">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">宁波智能科技研究院</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">统一社会信用代码</p>
                                <p class="font-medium">91330201MA2B3X5678</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">注册资本</p>
                                <p class="font-medium">1000万元</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">成立日期</p>
                                <p class="font-medium">2019-03-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">企业状态</p>
                                <p class="font-medium">存续</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">投资信息</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">投资金额</p>
                                <p class="font-medium">500万元</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">出资比例</p>
                                <p class="font-medium">60%</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">投资日期</p>
                                <p class="font-medium">2019-03-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">行业</p>
                                <p class="font-medium">科学研究</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 研发人员详情弹窗 -->
    <div id="researcherModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">研发人员详情</h3>
                    <button onclick="closeModal('researcherModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="flex items-start gap-6 mb-6">
                        <div class="flex-shrink-0">
                            <div class="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center">
                                <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-2">王强</h4>
                            <div class="flex flex-wrap gap-2 mb-3">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">博士</span>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">高级工程师</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">研发主管</span>
                            </div>
                            <p class="text-sm text-gray-500">入职时间: 2018-06-01</p>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div>
                            <h5 class="text-sm font-medium text-gray-900 mb-2">教育经历</h5>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm font-medium">浙江大学 - 计算机科学与技术 - 博士</p>
                                <p class="text-xs text-gray-500">2010-09 至 2015-06</p>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="text-sm font-medium text-gray-900 mb-2">参与项目</h5>
                            <div class="space-y-2">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <p class="text-sm font-medium">人工智能算法优化项目</p>
                                    <p class="text-xs text-gray-500">2022-03 至 2023-02 | 项目负责人</p>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <p class="text-sm font-medium">大数据分析平台开发</p>
                                    <p class="text-xs text-gray-500">2021-05 至 2022-02 | 核心开发</p>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="text-sm font-medium text-gray-900 mb-2">研发成果</h5>
                            <div class="grid grid-cols-2 gap-2">
                                <div class="bg-blue-50 rounded-lg p-2">
                                    <p class="text-xs text-blue-800 mb-1">发明专利</p>
                                    <p class="text-sm font-medium">5项</p>
                                </div>
                                <div class="bg-green-50 rounded-lg p-2">
                                    <p class="text-xs text-green-800 mb-1">实用新型</p>
                                    <p class="text-sm font-medium">3项</p>
                                </div>
                                <div class="bg-purple-50 rounded-lg p-2">
                                    <p class="text-xs text-purple-800 mb-1">软件著作权</p>
                                    <p class="text-sm font-medium">4项</p>
                                </div>
                                <div class="bg-yellow-50 rounded-lg p-2">
                                    <p class="text-xs text-yellow-800 mb-1">学术论文</p>
                                    <p class="text-sm font-medium">8篇</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function showShareholderDetail(id) {
            document.getElementById('shareholderModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function showCompanyDetail(id) {
            document.getElementById('companyModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function showResearcherDetail(id) {
            document.getElementById('researcherModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
        
        // --- 图表初始化 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 股东占比图表
            const shareholderCtx = document.getElementById('shareholderChart').getContext('2d');
            new Chart(shareholderCtx, {
                type: 'bar',
                data: {
                    labels: ['宁波智能投资', '张明', '李华'],
                    datasets: [{
                        label: '持股比例',
                        data: [50, 30, 20],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
            
            // 参保趋势图表
            const insuranceCtx = document.getElementById('insuranceChart').getContext('2d');
            new Chart(insuranceCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '参保人数',
                        data: [45, 62, 78, 86],
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 点击弹窗外部关闭
            document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                        if (!modal.classList.contains('hidden')) {
                            closeModal(modal.id);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>