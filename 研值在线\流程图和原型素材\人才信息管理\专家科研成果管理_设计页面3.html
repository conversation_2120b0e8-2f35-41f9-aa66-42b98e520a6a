<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家科研成果管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">专家科研成果管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">专家姓名</label>
                    <input type="text" placeholder="请输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                    <input type="text" placeholder="请输入成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="paper">论文</option>
                        <option value="patent">专利</option>
                        <option value="project">项目</option>
                        <option value="award">获奖</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">所属领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="material">新材料</option>
                        <option value="energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">获奖情况</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">完成时间</label>
                    <div class="flex space-x-1">
                        <input type="date" class="w-full px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500 text-xs">至</span>
                        <input type="date" class="w-full px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="valid">有效</option>
                        <option value="invalid">无效</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="text-sm text-gray-600">
                共找到 <span class="font-medium text-gray-900">1,245</span> 条科研成果记录
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出
                </button>
                <button onclick="openLinkModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    关联成果
                </button>
            </div>
        </div>

        <!-- 成果列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属专家</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖情况</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">基于深度学习的宁波市交通流量预测模型</div>
                                <div class="text-sm text-gray-500">计算机学报, 2023</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">论文</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王教授</div>
                                <div class="text-sm text-gray-500">宁波大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">人工智能</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-green-800 bg-green-100 rounded-full">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeItem('1')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">一种新型海洋生物活性物质的提取方法</div>
                                <div class="text-sm text-gray-500">专利号: ZL202310123456.7</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">李研究员</div>
                                <div class="text-sm text-gray-500">宁波海洋研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-02-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技进步一等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-green-800 bg-green-100 rounded-full">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeItem('2')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造产业升级关键技术研究</div>
                                <div class="text-sm text-gray-500">宁波市科技计划项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">项目</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">张教授</div>
                                <div class="text-sm text-gray-500">宁波工程学院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022-12-30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能制造</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省科技进步二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-green-800 bg-green-100 rounded-full">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeItem('3')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新型石墨烯复合材料在电池中的应用</div>
                                <div class="text-sm text-gray-500">Advanced Materials, 2023</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">论文</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">陈教授</div>
                                <div class="text-sm text-gray-500">宁波诺丁汉大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-04-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新材料</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-yellow-800 bg-yellow-100 rounded-full">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeItem('4')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能家居控制系统V1.0</div>
                                <div class="text-sm text-gray-500">软件著作权</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">软著</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">赵工程师</div>
                                <div class="text-sm text-gray-500">宁波智能科技</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-01-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">物联网</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold leading-tight text-red-800 bg-red-100 rounded-full">已过期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="openEditModal('5')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="removeItem('5')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 1,245 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科研成果详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="md:col-span-2">
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">基本信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-600">成果名称:</p>
                                    <p class="font-medium">基于深度学习的宁波市交通流量预测模型</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">成果类型:</p>
                                    <p class="font-medium">论文</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">所属专家:</p>
                                    <p class="font-medium">王教授 (宁波大学)</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">完成时间:</p>
                                    <p class="font-medium">2023-05-15</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">所属领域:</p>
                                    <p class="font-medium">人工智能</p>
                                </div>
                                <div>
                                    <p class="text-gray-600">成果状态:</p>
                                    <p class="font-medium">有效</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">核心技术内容</h4>
                            <div class="text-sm text-gray-700">
                                <p>本研究提出了一种基于Transformer架构的交通流量预测模型，针对宁波市特有的交通网络结构和车流特点进行了优化。模型在宁波市核心城区数据集上的预测准确率达到92.3%，较传统方法提升15.6%。</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">支撑材料</h4>
                            <div class="flex flex-wrap gap-2">
                                <div class="border border-gray-200 rounded p-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <p class="text-xs text-center mt-1">论文全文.pdf</p>
                                </div>
                                <div class="border border-gray-200 rounded p-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v14a1 1 0 01-1 1H5a1 1 0 01-1-1V5z" />
                                    </svg>
                                    <p class="text-xs text-center mt-1">实验数据.xlsx</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">获奖情况</h4>
                            <div class="text-sm text-gray-700">
                                <p>暂无获奖信息</p>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg mb-4">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">参与成员</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                        <span class="text-blue-600 text-xs font-medium">王</span>
                                    </div>
                                    <div>
                                        <p class="font-medium">王教授</p>
                                        <p class="text-gray-500 text-xs">第一作者</p>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                                        <span class="text-green-600 text-xs font-medium">李</span>
                                    </div>
                                    <div>
                                        <p class="font-medium">李研究员</p>
                                        <p class="text-gray-500 text-xs">通讯作者</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-800 mb-3">关联项目</h4>
                            <div class="text-sm text-gray-700">
                                <p>宁波市智慧交通关键技术研究 (项目编号: NB2023001)</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑科研成果</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="基于深度学习的宁波市交通流量预测模型">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="paper" selected>论文</option>
                                <option value="patent">专利</option>
                                <option value="project">项目</option>
                                <option value="award">获奖</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属专家</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="1" selected>王教授 (宁波大学)</option>
                                <option value="2">李研究员 (宁波海洋研究院)</option>
                                <option value="3">张教授 (宁波工程学院)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">完成时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2023-05-15">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所属领域</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="ai" selected>人工智能</option>
                                <option value="bio">生物医药</option>
                                <option value="material">新材料</option>
                                <option value="energy">新能源</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="valid" selected>有效</option>
                                <option value="invalid">无效</option>
                                <option value="pending">待审核</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">核心技术内容</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">本研究提出了一种基于Transformer架构的交通流量预测模型，针对宁波市特有的交通网络结构和车流特点进行了优化。模型在宁波市核心城区数据集上的预测准确率达到92.3%，较传统方法提升15.6%。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">获奖情况</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖信息">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">上传附件</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <p class="mt-1 text-sm text-gray-600">拖拽文件到此处或点击上传</p>
                            <p class="mt-1 text-xs text-gray-500">支持PDF、DOC、XLS格式，最大10MB</p>
                            <input type="file" class="sr-only">
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 关联成果弹窗 -->
    <div id="linkModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">关联科研成果</h3>
                    <button onclick="closeModal('linkModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="mb-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">专家姓名</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部专家</option>
                                <option value="1">王教授 (宁波大学)</option>
                                <option value="2">李研究员 (宁波海洋研究院)</option>
                                <option value="3">张教授 (宁波工程学院)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                            <input type="text" placeholder="请输入成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="paper">论文</option>
                                <option value="patent">专利</option>
                                <option value="project">项目</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow overflow-hidden mb-4">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属领域</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">宁波市智能制造产业升级关键技术研究</div>
                                    <div class="text-sm text-gray-500">宁波市科技计划项目</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">项目</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022-12-30</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能制造</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">新型石墨烯复合材料在电池中的应用</div>
                                    <div class="text-sm text-gray-500">Advanced Materials, 2023</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">论文</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-04-10</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新材料</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">智能家居控制系统V1.0</div>
                                    <div class="text-sm text-gray-500">软件著作权</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">软著</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-01-05</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">物联网</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex justify-end space-x-3">
                    <button onclick="closeModal('linkModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        确认关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 打开详情弹窗
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
        }

        // 打开编辑弹窗
        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
        }

        // 打开关联成果弹窗
        function openLinkModal() {
            document.getElementById('linkModal').classList.remove('hidden');
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // 移除成果
        function removeItem(id) {
            if (confirm('确定要移除这条科研成果吗？')) {
                console.log('移除成果:', id);
                // 这里添加实际的移除逻辑
            }
        }

        // 点击弹窗外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('detailModal');
            }
        });

        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('editModal');
            }
        });

        document.getElementById('linkModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('linkModal');
            }
        });
    </script>
</body>
</html>