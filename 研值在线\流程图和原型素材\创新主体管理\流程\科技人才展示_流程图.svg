<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技人才展示模块流程图</text>

  <!-- 阶段一：模块初始化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：模块初始化与数据加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入科技人才展示模块</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">权限验证，加载主数据，计算聚合指标</text>
  </g>

  <!-- 节点2: 卡片概览区刷新 -->
  <g transform="translate(550, 250)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">卡片概览区刷新</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">显示研发团队与人才统计信息</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选与查询 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选与查询</text>

  <!-- 节点3: 筛选条件设定 -->
  <g transform="translate(100, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户设定筛选条件</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">部门、类别、学历、职称等</text>
  </g>

  <!-- 节点4: 数据服务处理 -->
  <g transform="translate(450, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">人才数据服务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">过滤参数处理，返回列表数据</text>
  </g>

  <!-- 节点5: 汇总指标更新 -->
  <g transform="translate(800, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">汇总指标更新</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">实时统计与展示</text>
  </g>

  <!-- 连接线 概览区 -> 筛选条件 -->
  <path d="M 600 320 C 400 350, 300 380, 225 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选条件 -> 数据服务 -->
  <path d="M 350 455 C 400 455, 400 455, 450 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据服务 -> 汇总指标 -->
  <path d="M 700 455 C 750 455, 750 455, 800 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>

  <!-- 节点6: 下钻详情 -->
  <g transform="translate(200, 580)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下钻详情查看</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">团队清册、专家清册、个人详情</text>
  </g>

  <!-- 节点7: 可视化分析 -->
  <g transform="translate(560, 580)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化分析交互</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合数据，生成图表</text>
  </g>

  <!-- 节点8: 数据导出与日志 -->
  <g transform="translate(920, 580)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出与审计</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">报告导出，审计日志记录</text>
  </g>

  <!-- 连接线 汇总指标 -> 下钻详情 -->
  <path d="M 850 490 C 800 520, 500 550, 340 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 汇总指标 -> 可视化分析 -->
  <path d="M 900 490 C 850 520, 750 550, 700 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 下钻详情 -> 数据导出 -->
  <path d="M 480 615 C 650 615, 800 615, 920 615" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：资源管理与优化 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：资源管理与优化</text>
  
  <!-- 节点9: 资源释放与偏好保存 -->
  <g transform="translate(500, 750)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">资源释放与偏好保存</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">释放缓存资源</tspan>
        <tspan dx="60">保存筛选偏好</tspan>
        <tspan dx="60">优化后续加载</tspan>
      </text>
  </g>

  <!-- 连接线 可视化分析 -> 资源释放 -->
  <path d="M 700 650 C 700 680, 700 720, 700 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 资源释放 -> 模块初始化 -->
  <path d="M 500 790 C 200 820, 100 500, 400 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="810" text-anchor="middle" font-size="12" fill="#666">优化反馈循环</text>

</svg>