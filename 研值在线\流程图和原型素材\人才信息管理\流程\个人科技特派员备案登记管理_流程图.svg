<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">个人科技特派员备案登记管理流程</text>

  <!-- 阶段一：备案申请与填报 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：备案申请与填报</text>
  
  <!-- 节点1: 批次选择 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">选择指定批次</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">用户进入系统选择批次</text>
  </g>

  <!-- 节点2: 备案填报 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增备案填报</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写个人信息与服务内容</text>
  </g>

  <!-- 节点3: 系统校验 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">完整性与格式验证</text>
  </g>

  <!-- 节点4: 保存提交 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">保存或提交</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">草稿保存或直接上报</text>
  </g>

  <!-- 连接线 阶段一 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：三级审核流程 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：三级审核流程</text>

  <!-- 节点5: 乡镇审核 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">乡镇（街道）审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">首轮审核，待审核状态</text>
  </g>

  <!-- 节点6: 派出单位审核 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">派出单位复审</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">二级审核与复查</text>
  </g>

  <!-- 节点7: 科技局终审 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">区县科技局终审</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">最终审核与意见记录</text>
  </g>

  <!-- 节点8: 退回修改 -->
  <g transform="translate(1100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">退回修改</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">问题反馈与补充完善</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 1100 200 C 1100 250, 300 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 退回循环线 -->
  <path d="M 1100 355 C 1150 355, 1150 280, 1100 280 C 1050 280, 1000 280, 950 280 C 900 280, 850 280, 800 280 C 750 280, 700 280, 650 280 C 600 280, 550 280, 500 280 C 450 280, 400 280, 350 280 C 300 280, 250 280, 200 280 C 150 280, 100 280, 100 320" stroke="#CE93D8" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="600" y="270" text-anchor="middle" font-size="12" fill="#CE93D8">退回重新填报</text>

  <!-- 阶段三：归档与管理 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：归档与管理</text>

  <!-- 节点9: 自动归档 -->
  <g transform="translate(300, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">备案自动归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态更新与数据沉淀</text>
  </g>

  <!-- 节点10: 批次管理 -->
  <g transform="translate(600, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批次管理维护</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">年度批次分组管理</text>
  </g>

  <!-- 节点11: 查询导出 -->
  <g transform="translate(900, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">权限查询与数据导出</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 900 390 C 900 450, 400 480, 400 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 555 Q 550 555 600 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 555 Q 850 555 900 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：全链路追溯 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：全链路追溯</text>

  <!-- 节点12: 历史库管理 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">历史库与全程追溯</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">数据统计</tspan>
      <tspan dx="40">流程追溯</tspan>
      <tspan dx="40">业务分析</tspan>
      <tspan dx="40">监管支撑</tspan>
    </text>
  </g>

  <!-- 连接线 到历史库 -->
  <path d="M 400 590 C 400 650, 500 680, 500 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 590 C 700 650, 600 680, 600 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 590 C 1000 650, 700 680, 700 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线：从历史库回到批次选择 -->
  <path d="M 400 760 C 350 760, 50 760, 50 400 C 50 200, 100 150, 100 165" stroke="#CE93D8" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="480" text-anchor="middle" font-size="12" fill="#CE93D8" transform="rotate(-90, 50, 480)">历史数据反馈</text>

</svg>