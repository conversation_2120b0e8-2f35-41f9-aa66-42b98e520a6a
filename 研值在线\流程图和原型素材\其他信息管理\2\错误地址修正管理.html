<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误地址修正管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        border: "hsl(214.3 31.8% 91.4%)",
                        input: "hsl(214.3 31.8% 91.4%)",
                        ring: "hsl(222.2 84% 4.9%)",
                        background: "hsl(0 0% 100%)",
                        foreground: "hsl(222.2 84% 4.9%)",
                        primary: {
                            DEFAULT: "hsl(222.2 47.4% 11.2%)",
                            foreground: "hsl(210 40% 98%)",
                        },
                        secondary: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        destructive: {
                            DEFAULT: "hsl(0 84.2% 60.2%)",
                            foreground: "hsl(210 40% 98%)",
                        },
                        muted: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(215.4 16.3% 46.9%)",
                        },
                        accent: {
                            DEFAULT: "hsl(210 40% 96%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        popover: {
                            DEFAULT: "hsl(0 0% 100%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                        card: {
                            DEFAULT: "hsl(0 0% 100%)",
                            foreground: "hsl(222.2 84% 4.9%)",
                        },
                    },
                }
            }
        }
    </script>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-white">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">错误地址修正管理</h1>
            <p class="text-gray-600">地理信息治理质量闭环，提供地址匹配结果的纠错申请、审批流转与变更历史追溯</p>
        </div>

        <!-- 统计与监控区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">待审批数量</p>
                        <p class="text-2xl font-bold text-blue-600">12</p>
                    </div>
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">平均审批用时</p>
                        <p class="text-2xl font-bold text-blue-600">2.3天</p>
                    </div>
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">纠错成功率</p>
                        <p class="text-2xl font-bold text-blue-600">89.5%</p>
                    </div>
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">本月处理量</p>
                        <p class="text-2xl font-bold text-blue-600">156</p>
                    </div>
                    <div class="p-2 bg-blue-100 rounded-lg">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-280px)]">
            <!-- 申请发起区 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md h-full">
                    <div class="p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            发起纠错申请
                        </h2>
                    </div>
                    <div class="p-6 space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">地址搜索</label>
                            <div class="relative">
                                <input type="text" placeholder="输入需要纠错的地址" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button class="absolute right-2 top-2 text-gray-400 hover:text-gray-600">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">原始行政区</label>
                                <input type="text" value="浙江省宁波市海曙区" readonly class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-600">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">原始功能区</label>
                                <input type="text" value="宁波高新技术产业开发区" readonly class="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-600">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">纠正行政区</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option>请选择正确的行政区</option>
                                    <option>浙江省宁波市海曙区</option>
                                    <option>浙江省宁波市江北区</option>
                                    <option>浙江省宁波市鄞州区</option>
                                    <option>浙江省宁波市北仑区</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">纠正功能区</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option>请选择正确的功能区</option>
                                    <option>宁波高新技术产业开发区</option>
                                    <option>宁波经济技术开发区</option>
                                    <option>宁波保税区</option>
                                    <option>宁波大榭开发区</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">纠错理由</label>
                            <textarea rows="4" placeholder="请详细说明纠错原因和依据" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>
                        
                        <button class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                            提交申请
                        </button>
                    </div>
                </div>
            </div>

            <!-- 申请列表区 -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md h-full flex flex-col">
                    <div class="p-6 border-b">
                        <div class="flex items-center justify-between">
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                申请列表
                            </h2>
                            <div class="flex space-x-2">
                                <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                                    <option>全部状态</option>
                                    <option>待审批</option>
                                    <option>审批中</option>
                                    <option>已通过</option>
                                    <option>已驳回</option>
                                </select>
                                <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                                    </svg>
                                    筛选
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 overflow-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50 sticky top-0">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">地址</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原始匹配</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">纠正匹配</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请人</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前处理人</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市海曙区中山西路138号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">海曙区/海曙商务区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">海曙区/宁波高新区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">李审批员</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">2024-01-15 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            待审批
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900" onclick="openDetailDrawer()">查看详情</button>
                                        <button class="text-red-600 hover:text-red-900">撤回</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市江北区人民路132号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">江北区/江北工业区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">江北区/宁波经开区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">王主管</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">2024-01-14 09:15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-200 text-blue-900">
                                            审批中
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900" onclick="openDetailDrawer()">查看详情</button>
                                        <button class="text-gray-400 cursor-not-allowed">撤回</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市鄞州区首南街道学士路1号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">鄞州区/鄞州商务区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">鄞州区/宁波南部商务区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王五</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">-</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">2024-01-13 16:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-600 text-white">
                                            已通过
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900" onclick="openDetailDrawer()">查看详情</button>
                                        <button class="text-blue-600 hover:text-blue-900" onclick="openHistoryModal()">变更历史</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市北仑区新碶街道长江路1166号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">北仑区/北仑港区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">北仑区/宁波保税区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵六</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">-</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">2024-01-12 11:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-200 text-gray-700">
                                            已驳回
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900" onclick="openDetailDrawer()">查看详情</button>
                                        <button class="text-blue-600 hover:text-blue-900">重新申请</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情与审批侧边抽屉 -->
    <div id="detailDrawer" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex justify-end h-full">
            <div class="bg-white w-full max-w-2xl h-full overflow-hidden shadow-xl transform transition-transform duration-300 translate-x-full" id="drawerContent">
                <div class="flex items-center justify-between p-6 border-b bg-blue-50">
                    <h3 class="text-lg font-semibold text-gray-900">申请详情与审批</h3>
                    <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto h-full">
                    <!-- 申请基本信息 -->
                    <div class="space-y-6">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                申请基本信息
                            </h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申请编号</label>
                                    <p class="text-sm text-gray-900 bg-white p-2 rounded border">ADDR-2024-001</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申请状态</label>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        待审批
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申请人</label>
                                    <p class="text-sm text-gray-900 bg-white p-2 rounded border">张三</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申请部门</label>
                                    <p class="text-sm text-gray-900 bg-white p-2 rounded border">科研管理部</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申请时间</label>
                                    <p class="text-sm text-gray-900 bg-white p-2 rounded border">2024-01-15 14:30:25</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">当前处理人</label>
                                    <p class="text-sm text-gray-900 bg-white p-2 rounded border">李审批员</p>
                                </div>
                            </div>
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">错误地址</label>
                                <p class="text-sm text-gray-900 bg-white p-3 rounded border">宁波市海曙区中山西路138号</p>
                            </div>
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-1">纠错理由</label>
                                <p class="text-sm text-gray-900 bg-white p-3 rounded border">该地址位于宁波高新技术产业开发区范围内，不属于海曙商务区。根据最新的行政区划调整，应归属于宁波高新区管辖。</p>
                            </div>
                        </div>
                        
                        <!-- 系统自动比对差异 -->
                        <div class="bg-white border rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                系统自动比对差异
                            </h4>
                            <div class="space-y-4">
                                <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                                    <label class="block text-sm font-medium text-red-700 mb-2">🔍 原始匹配结果</label>
                                    <div class="space-y-1">
                                        <p class="text-sm text-red-900">行政区：浙江省宁波市海曙区</p>
                                        <p class="text-sm text-red-900 bg-red-100 px-2 py-1 rounded">功能区：<mark class="bg-red-200">海曙商务区</mark></p>
                                        <p class="text-sm text-red-700">匹配置信度：85%</p>
                                    </div>
                                </div>
                                <div class="flex justify-center">
                                    <div class="bg-gray-100 rounded-full p-2">
                                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                    <label class="block text-sm font-medium text-blue-700 mb-2">✅ 纠正匹配结果</label>
                                    <div class="space-y-1">
                                        <p class="text-sm text-blue-900">行政区：浙江省宁波市海曙区</p>
                                        <p class="text-sm text-blue-900 bg-blue-100 px-2 py-1 rounded">功能区：<mark class="bg-blue-200">宁波高新技术产业开发区</mark></p>
                                        <p class="text-sm text-blue-700">匹配置信度：95%</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 附件上传 -->
                        <div class="bg-white border rounded-lg p-4">
                            <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                </svg>
                                相关附件
                            </h4>
                            <div class="space-y-3">
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                                    <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                    <p class="text-sm text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                                    <p class="text-xs text-gray-500">支持 PDF、DOC、DOCX、JPG、PNG 格式，单个文件不超过 10MB</p>
                                    <input type="file" class="hidden" id="fileUpload" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <button onclick="document.getElementById('fileUpload').click()" class="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                                        选择文件
                                    </button>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-gray-50 rounded border">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                            </svg>
                                            <span class="text-sm text-gray-700">地址证明材料.pdf</span>
                                        </div>
                                        <button class="text-red-600 hover:text-red-800">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史审批意见 -->
                    <div class="mt-6">
                        <h4 class="font-semibold text-gray-900 border-b pb-2 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            历史审批意见
                        </h4>
                        <div class="space-y-3">
                            <div class="border-l-4 border-blue-500 bg-blue-50 p-4 rounded-r">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900">系统管理员</span>
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            已提交
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500">2024-01-15 14:30</span>
                                </div>
                                <p class="text-sm text-gray-700">申请已提交，等待审批。系统已自动进行地址匹配分析。</p>
                            </div>
                            <div class="border-l-4 border-yellow-500 bg-yellow-50 p-4 rounded-r">
                                <div class="flex items-center justify-between mb-2">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900">李审批员</span>
                                        <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                            审批中
                                        </span>
                                    </div>
                                    <span class="text-xs text-gray-500">2024-01-15 15:30</span>
                                </div>
                                <p class="text-sm text-gray-700">申请材料完整，理由充分。经核实，该地址确实位于宁波高新区范围内，建议通过此次纠错申请。</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审批操作 -->
                    <div class="mt-6 border-t pt-6">
                        <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            审批操作
                        </h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">审批意见</label>
                                <textarea rows="3" placeholder="请填写审批意见" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"></textarea>
                            </div>
                            <div class="grid grid-cols-3 gap-3">
                                <button class="px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    同意
                                </button>
                                <button class="px-4 py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    驳回
                                </button>
                                <button class="px-4 py-3 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                                    </svg>
                                    退回修改
                                </button>
                            </div>
                            <button class="w-full bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                                保存草稿
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 变更历史模态框 -->
    <div id="historyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">变更历史</h3>
                    <button onclick="closeHistoryModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
                    <div class="space-y-6">
                        <!-- 时间轴 -->
                        <div class="relative">
                            <div class="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                            
                            <!-- 历史记录项 -->
                            <div class="relative flex items-start space-x-4 pb-6">
                                <div class="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-900">纠错申请通过</p>
                                        <p class="text-xs text-gray-500">2024-01-13 16:45</p>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">申请人：王五 | 审批人：李审批员</p>
                                    <div class="mt-2 text-sm">
                                        <p class="text-gray-700">变更内容：功能区从"上城商业区"修正为"湖滨商圈"</p>
                                        <p class="text-gray-600 mt-1">审批意见：经核实，该地址确实位于湖滨商圈范围内，同意此次纠错申请。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="relative flex items-start space-x-4 pb-6">
                                <div class="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-900">提交纠错申请</p>
                                        <p class="text-xs text-gray-500">2024-01-13 14:20</p>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">申请人：王五</p>
                                    <div class="mt-2 text-sm">
                                        <p class="text-gray-700">申请理由：该地址位于宁波南部商务区核心区域，不应归属于鄞州商务区。</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="relative flex items-start space-x-4">
                                <div class="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <p class="text-sm font-medium text-gray-900">初始地址匹配</p>
                                        <p class="text-xs text-gray-500">2024-01-10 10:30</p>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">系统自动匹配</p>
                                    <div class="mt-2 text-sm">
                                        <p class="text-gray-700">匹配结果：上城区/上城商业区</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 详情侧边抽屉
        function openDetailDrawer() {
            const drawer = document.getElementById('detailDrawer');
            const drawerContent = document.getElementById('drawerContent');
            
            drawer.classList.remove('hidden');
            
            // 添加动画效果
            setTimeout(() => {
                drawerContent.classList.remove('translate-x-full');
            }, 10);
        }
        
        function closeDetailDrawer() {
            const drawer = document.getElementById('detailDrawer');
            const drawerContent = document.getElementById('drawerContent');
            
            drawerContent.classList.add('translate-x-full');
            
            // 等待动画完成后隐藏
            setTimeout(() => {
                drawer.classList.add('hidden');
            }, 300);
        }
        
        // 点击背景关闭抽屉
        document.getElementById('detailDrawer').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailDrawer();
            }
        });
        
        function openHistoryModal() {
            document.getElementById('historyModal').classList.remove('hidden');
        }
        
        function closeHistoryModal() {
            document.getElementById('historyModal').classList.add('hidden');
        }
        
        document.getElementById('historyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeHistoryModal();
            }
        });
    </script>
</body>
</html>