<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="24" text-anchor="middle" font-weight="600" fill="#333">科研管理人员参政咨政情况管理流程</text>

  <!-- 阶段一：数据加载与展示 -->
  <text x="700" y="100" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与展示</text>
  
  <!-- 节点1: 模块进入 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模块进入</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">参政咨政管理模块</text>
  </g>

  <!-- 节点2: 数据加载 -->
  <g transform="translate(610, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据加载</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">全部参政咨政数据</text>
  </g>

  <!-- 节点3: 列表展示 -->
  <g transform="translate(820, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">列表展示</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">参政咨政记录</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 580 165 Q 595 165 610 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 790 165 Q 805 165 820 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选与检索 -->
  <text x="700" y="270" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选与检索</text>

  <!-- 节点4: 检索条件设置 -->
  <g transform="translate(450, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">检索条件设置</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">多项筛选条件</text>
  </g>

  <!-- 节点5: 实时刷新 -->
  <g transform="translate(700, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时刷新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">目标数据展示</text>
  </g>

  <!-- 连接线 列表展示 -> 检索条件 -->
  <path d="M 910 200 C 910 230, 650 250, 550 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 650 335 Q 675 335 700 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：记录操作管理 -->
  <text x="700" y="440" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：记录操作管理</text>

  <!-- 节点6: 新增记录 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增记录</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">录入参政信息</text>
  </g>

  <!-- 节点7: 编辑记录 -->
  <g transform="translate(350, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑记录</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">信息修订更新</text>
  </g>

  <!-- 节点8: 移除记录 -->
  <g transform="translate(550, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除记录</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">确认后逻辑删除</text>
  </g>

  <!-- 节点9: 详情查看 -->
  <g transform="translate(750, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">全部履历文档</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(950, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">格式化文件生成</text>
  </g>

  <!-- 连接线 实时刷新 -> 各操作 -->
  <path d="M 750 370 C 650 400, 350 430, 230 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 770 370 C 700 400, 500 430, 430 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 790 370 C 750 400, 700 430, 630 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 370 C 810 400, 830 430, 830 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 830 370 C 870 400, 950 430, 1030 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统处理与应用 -->
  <text x="700" y="610" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统处理与应用</text>

  <!-- 节点11: 校验入库 -->
  <g transform="translate(250, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验入库</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">新增编辑处理</text>
  </g>

  <!-- 节点12: 履历展示 -->
  <g transform="translate(500, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">履历展示</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">参政议政全貌</text>
  </g>

  <!-- 节点13: 文件应用 -->
  <g transform="translate(750, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件应用</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">下载与使用</text>
  </g>

  <!-- 连接线 新增编辑 -> 校验入库 -->
  <path d="M 230 540 C 230 580, 300 600, 320 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 430 540 C 430 580, 370 600, 360 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情查看 -> 履历展示 -->
  <path d="M 830 540 C 830 580, 630 600, 590 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据导出 -> 文件应用 -->
  <path d="M 1030 540 C 1030 580, 880 600, 840 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从文件应用回到数据加载 -->
  <path d="M 840 640 C 1150 600, 1200 300, 1150 165 C 1100 165, 850 165, 790 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1100" y="400" text-anchor="middle" font-size="11" fill="#666">应用反馈</text>

  <!-- 反馈循环：从履历展示回到检索条件 -->
  <path d="M 500 675 C 400 675, 350 600, 350 400 C 350 380, 400 360, 450 335" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="400" y="550" text-anchor="middle" font-size="11" fill="#666">深度查询</text>

</svg>