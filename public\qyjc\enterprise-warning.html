<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业预警系统 - 企业检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'blue-50': '#EFF6FF',
                        'blue-100': '#DBEAFE',
                        'blue-500': '#3B82F6',
                        'blue-600': '#2563EB'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F1F5F9 0%, #EFF6FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
        }
        .warning-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .warning-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        .risk-high {
            border-left-color: #EF4444;
            background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
        }
        .risk-medium {
            border-left-color: #F59E0B;
            background: linear-gradient(135deg, #FFFBEB 0%, #FED7AA 100%);
        }
        .risk-low {
            border-left-color: #3B82F6;
            background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
        }
        .alert-blink {
            animation: blink 2s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.5; }
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-blue-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">企业预警系统</h1>
                        <p class="text-sm text-gray-600">识别企业潜在风险及异常情况，提供预警提示</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm">
                        <div class="w-2 h-2 bg-red-500 rounded-full alert-blink"></div>
                        <span class="text-red-600 font-medium">23个预警</span>
                    </div>
                    <button class="flex items-center px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors">
                        <i class="fas fa-bell mr-2"></i>
                        预警设置
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- 预警概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl card-shadow p-6 border-l-4 border-red-500">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-fire text-red-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full alert-blink">严重</span>
                </div>
                <p class="text-3xl font-bold text-red-600">8</p>
                <p class="text-sm text-gray-600">高风险企业</p>
                <div class="mt-3 flex items-center text-xs text-red-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>较上月+3</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 border-l-4 border-yellow-500">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">中等</span>
                </div>
                <p class="text-3xl font-bold text-yellow-600">15</p>
                <p class="text-sm text-gray-600">中风险企业</p>
                <div class="mt-3 flex items-center text-xs text-yellow-600">
                    <i class="fas fa-arrow-down mr-1"></i>
                    <span>较上月-2</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 border-l-4 border-gray-500">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sleep text-gray-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">沉默</span>
                </div>
                <p class="text-3xl font-bold text-gray-600">42</p>
                <p class="text-sm text-gray-600">沉默企业</p>
                <div class="mt-3 flex items-center text-xs text-gray-600">
                    <i class="fas fa-minus mr-1"></i>
                    <span>较上月无变化</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 border-l-4 border-blue-500">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-award text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">资质</span>
                </div>
                <p class="text-3xl font-bold text-blue-600">12</p>
                <p class="text-sm text-gray-600">摘牌风险</p>
                <div class="mt-3 flex items-center text-xs text-blue-600">
                    <i class="fas fa-arrow-up mr-1"></i>
                    <span>较上月+5</span>
                </div>
            </div>
        </div>

        <!-- 预警地图和分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 三色预警地图 -->
            <div class="lg:col-span-2 bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">三色预警地图</h3>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-red-500 rounded"></div>
                            <span class="text-sm text-gray-600">高风险</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-yellow-500 rounded"></div>
                            <span class="text-sm text-gray-600">中风险</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded"></div>
                            <span class="text-sm text-gray-600">低风险</span>
                        </div>
                    </div>
                </div>
                
                <div class="relative h-80 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1524661135-423995f22d0b?w=800&h=400&fit=crop&crop=entropy" 
                         alt="预警地图" 
                         class="w-full h-full object-cover opacity-70">
                    
                    <!-- 预警点位 -->
                    <div class="absolute inset-0">
                        <!-- 高风险点位 -->
                        <div class="absolute top-1/4 left-1/3 w-4 h-4 bg-red-500 rounded-full animate-pulse border-2 border-white shadow-lg"></div>
                        <div class="absolute top-1/2 left-1/2 w-4 h-4 bg-red-500 rounded-full animate-pulse border-2 border-white shadow-lg"></div>
                        <div class="absolute bottom-1/3 right-1/4 w-4 h-4 bg-red-500 rounded-full animate-pulse border-2 border-white shadow-lg"></div>
                        
                        <!-- 中风险点位 -->
                        <div class="absolute top-1/3 right-1/3 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white shadow-lg"></div>
                        <div class="absolute bottom-1/4 left-1/4 w-3 h-3 bg-yellow-500 rounded-full border-2 border-white shadow-lg"></div>
                        
                        <!-- 低风险点位 -->
                        <div class="absolute top-2/3 left-2/3 w-2 h-2 bg-blue-500 rounded-full border border-white shadow-lg"></div>
                        <div class="absolute bottom-1/2 right-1/2 w-2 h-2 bg-blue-500 rounded-full border border-white shadow-lg"></div>
                    </div>
                    
                    <div class="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3">
                        <p class="text-sm font-medium text-gray-900">宁波市企业风险分布</p>
                        <p class="text-xs text-gray-600 mt-1">实时更新 • 点击查看详情</p>
                    </div>
                </div>
            </div>

            <!-- 预警统计 -->
            <div class="space-y-6">
                <!-- 风险等级分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">风险等级分布</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-red-500 rounded"></div>
                                <span class="text-sm text-gray-700">高风险</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-red-500 h-2 rounded-full" style="width: 12%"></div>
                                </div>
                                <span class="text-sm font-medium text-red-600">8家</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                                <span class="text-sm text-gray-700">中风险</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 23%"></div>
                                </div>
                                <span class="text-sm font-medium text-yellow-600">15家</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-4 h-4 bg-blue-500 rounded"></div>
                                <span class="text-sm text-gray-700">低风险</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-20 bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full" style="width: 65%"></div>
                                </div>
                                <span class="text-sm font-medium text-blue-600">42家</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 行业风险分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">行业风险分布</h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-2 bg-red-50 rounded">
                            <span class="text-sm text-gray-700">制造业</span>
                            <span class="text-sm font-medium text-red-600">高风险 5家</span>
                        </div>
                        <div class="flex items-center justify-between p-2 bg-yellow-50 rounded">
                            <span class="text-sm text-gray-700">生物医药</span>
                            <span class="text-sm font-medium text-yellow-600">中风险 8家</span>
                        </div>
                        <div class="flex items-center justify-between p-2 bg-blue-50 rounded">
                            <span class="text-sm text-gray-700">信息技术</span>
                            <span class="text-sm font-medium text-blue-600">低风险 12家</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预警详情列表 -->
        <div class="space-y-6">
            <!-- 高风险企业 -->
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-red-900 flex items-center">
                            <i class="fas fa-fire text-red-600 mr-2"></i>
                            高风险企业 (8家)
                        </h3>
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center px-4 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors text-sm">
                                <i class="fas fa-download mr-2"></i>
                                导出清单
                            </button>
                            <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm">
                                <i class="fas fa-eye mr-2"></i>
                                全部详情
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="warning-card risk-high p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                    <i class="fas fa-industry text-red-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">宁波XX制造有限公司</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>智能制造</span>
                                        <span>•</span>
                                        <span>鄞州区</span>
                                        <span>•</span>
                                        <span>成立时间：2018年</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">严重风险</span>
                                    <span class="text-xs text-gray-500">评分: 85/100</span>
                                </div>
                                <button class="text-red-600 hover:text-red-800 text-sm">
                                    查看详情 <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-red-200">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">主要风险</p>
                                    <p class="text-sm font-medium text-red-700">研发投入下降32.5%</p>
                                </div>
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">次要风险</p>
                                    <p class="text-sm font-medium text-red-700">人员流失率18%</p>
                                </div>
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">预警时间</p>
                                    <p class="text-sm font-medium text-red-700">2024-03-15</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="warning-card risk-high p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                    <i class="fas fa-flask text-red-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">东海XX生物科技股份公司</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>生物医药</span>
                                        <span>•</span>
                                        <span>北仑区</span>
                                        <span>•</span>
                                        <span>成立时间：2019年</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">严重风险</span>
                                    <span class="text-xs text-gray-500">评分: 82/100</span>
                                </div>
                                <button class="text-red-600 hover:text-red-800 text-sm">
                                    查看详情 <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-red-200">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">主要风险</p>
                                    <p class="text-sm font-medium text-red-700">连续3个月零专利申请</p>
                                </div>
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">次要风险</p>
                                    <p class="text-sm font-medium text-red-700">项目进度严重滞后</p>
                                </div>
                                <div class="bg-white/60 p-3 rounded">
                                    <p class="text-xs text-gray-600 mb-1">预警时间</p>
                                    <p class="text-sm font-medium text-red-700">2024-03-12</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中风险企业 -->
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-yellow-900 flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                            中风险企业 (15家)
                        </h3>
                        <button class="text-yellow-600 hover:text-yellow-800 text-sm">
                            查看全部 <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="warning-card risk-medium p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                    <i class="fas fa-microchip text-yellow-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">甬江XX新材料科技有限公司</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>新材料</span>
                                        <span>•</span>
                                        <span>镇海区</span>
                                        <span>•</span>
                                        <span>中等风险</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">中等风险</span>
                                <div class="mt-2">
                                    <button class="text-yellow-600 hover:text-yellow-800 text-sm">查看详情</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3 flex items-center space-x-4 text-sm">
                            <span class="text-yellow-700">研发强度下降15%</span>
                            <span class="text-gray-400">•</span>
                            <span class="text-yellow-700">项目延期2个</span>
                            <span class="text-gray-400">•</span>
                            <span class="text-gray-600">预警时间：2024-03-10</span>
                        </div>
                    </div>

                    <div class="warning-card risk-medium p-4 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-white rounded-lg flex items-center justify-center shadow-sm">
                                    <i class="fas fa-laptop-code text-yellow-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">江北XX数字科技发展公司</h4>
                                    <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                        <span>数字经济</span>
                                        <span>•</span>
                                        <span>江北区</span>
                                        <span>•</span>
                                        <span>中等风险</span>
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">中等风险</span>
                                <div class="mt-2">
                                    <button class="text-yellow-600 hover:text-yellow-800 text-sm">查看详情</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-3 flex items-center space-x-4 text-sm">
                            <span class="text-yellow-700">核心人员离职3人</span>
                            <span class="text-gray-400">•</span>
                            <span class="text-yellow-700">营收增长放缓</span>
                            <span class="text-gray-400">•</span>
                            <span class="text-gray-600">预警时间：2024-03-08</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 摘牌风险企业 -->
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-blue-900 flex items-center">
                            <i class="fas fa-award text-blue-600 mr-2"></i>
                            潜在摘牌高企预警 (12家)
                        </h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">
                            查看全部 <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="warning-card risk-low p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-semibold text-gray-900">海曙XX科技发展公司</h4>
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">高企摘牌风险</span>
                            </div>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发强度</span>
                                    <span class="text-red-600 font-medium">2.8% (不达标)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发人员比例</span>
                                    <span class="text-yellow-600 font-medium">8.5% (临界)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">知识产权数量</span>
                                    <span class="text-green-600 font-medium">达标</span>
                                </div>
                            </div>
                        </div>

                        <div class="warning-card risk-low p-4 rounded-lg">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-semibold text-gray-900">慈溪XX智能装备公司</h4>
                                <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">高企摘牌风险</span>
                            </div>
                            <div class="space-y-2 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发强度</span>
                                    <span class="text-yellow-600 font-medium">3.2% (临界)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发人员比例</span>
                                    <span class="text-red-600 font-medium">8.1% (不达标)</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">知识产权数量</span>
                                    <span class="text-green-600 font-medium">达标</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 沉默企业 -->
            <div class="bg-white rounded-xl card-shadow">
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-sleep text-gray-600 mr-2"></i>
                            沉默企业 (42家)
                        </h3>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600">近5年未办理科技局业务</span>
                            <button class="text-gray-600 hover:text-gray-800 text-sm">
                                查看全部 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">余姚XX精密制造</h4>
                            <div class="space-y-1 text-sm text-gray-600">
                                <p>最后业务：2019年项目申报</p>
                                <p>沉默时长：5年2个月</p>
                                <p>企业状态：正常经营</p>
                            </div>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">奉化XX电子科技</h4>
                            <div class="space-y-1 text-sm text-gray-600">
                                <p>最后业务：2020年认定申请</p>
                                <p>沉默时长：4年1个月</p>
                                <p>企业状态：正常经营</p>
                            </div>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <h4 class="font-semibold text-gray-900 mb-2">宁海XX新材料</h4>
                            <div class="space-y-1 text-sm text-gray-600">
                                <p>最后业务：2019年补贴申请</p>
                                <p>沉默时长：5年3个月</p>
                                <p>企业状态：待核实</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI预警分析 -->
        <div class="mt-8 bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl card-shadow p-6 border border-purple-100">
            <div class="flex items-center space-x-3 mb-6">
                <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-brain text-purple-600"></i>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-purple-900">AI预警分析</h3>
                    <p class="text-sm text-purple-700">基于多维数据的智能风险识别与预测</p>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-purple-900">风险趋势预测</span>
                        <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">AI分析</span>
                    </div>
                    <p class="text-sm text-purple-800 leading-relaxed mb-3">
                        预计下月新增中风险企业3-5家，主要集中在制造业领域。建议提前介入风险防控。
                    </p>
                    <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                        查看预测详情 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <span class="text-sm font-medium text-purple-900">干预建议</span>
                        <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">建议</span>
                    </div>
                    <p class="text-sm text-purple-800 leading-relaxed mb-3">
                        对高风险企业建议实施"一企一策"精准帮扶，重点关注研发投入和人才稳定性。
                    </p>
                    <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                        查看帮扶方案 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 预警交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 地图点位点击事件
            const mapPoints = document.querySelectorAll('.absolute.top-1\\/4, .absolute.top-1\\/2, .absolute.top-1\\/3, .absolute.top-2\\/3, .absolute.bottom-1\\/4, .absolute.bottom-1\\/3, .absolute.bottom-1\\/2');
            mapPoints.forEach(point => {
                if (point.classList.contains('w-4')) { // 高风险点位
                    point.addEventListener('click', function() {
                        showRiskDetail('high');
                    });
                } else if (point.classList.contains('w-3')) { // 中风险点位
                    point.addEventListener('click', function() {
                        showRiskDetail('medium');
                    });
                } else { // 低风险点位
                    point.addEventListener('click', function() {
                        showRiskDetail('low');
                    });
                }
                
                // 添加悬停效果
                point.style.cursor = 'pointer';
                point.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2)';
                });
                point.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 预警卡片点击效果
            const warningCards = document.querySelectorAll('.warning-card');
            warningCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击效果
                    this.style.transform = 'translateY(-2px)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
        });

        function showRiskDetail(riskLevel) {
            const riskTypes = {
                'high': '高风险企业详情',
                'medium': '中风险企业详情',
                'low': '低风险企业详情'
            };
            
            alert(`显示${riskTypes[riskLevel]}`);
            // 实际实现中这里会打开详情模态框或跳转到详情页面
        }

        // 实时预警检查（模拟）
        function checkNewAlerts() {
            const alertCount = document.querySelector('.alert-blink');
            if (alertCount) {
                // 模拟新预警
                const currentCount = parseInt(alertCount.textContent.match(/\d+/)[0]);
                if (Math.random() > 0.95) { // 5%概率有新预警
                    alertCount.textContent = `${currentCount + 1}个预警`;
                    console.log('新预警产生');
                }
            }
        }

        // 每30秒检查一次新预警
        setInterval(checkNewAlerts, 30000);

        // 预警设置
        function openWarningSettings() {
            console.log('打开预警设置');
            // 实际实现中会打开设置模态框
        }
    </script>
</body>
</html> 