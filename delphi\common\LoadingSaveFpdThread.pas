unit LoadingSaveFpdThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingSaveFpdFrm;

type
  TThreadSaveFpdModel = class(TThread)
  private
    FLoadingSaveFpdForm: TLoadingSaveFpdForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingSaveFpdForm: TLoadingSaveFpdForm read FLoadingSaveFpdForm
      write FLoadingSaveFpdForm;

  end;

var
  LoadingSaveFpdForm: TLoadingSaveFpdForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadSaveFpdModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadSaveFpdModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
  // LoadingForm.RxGIFAnimator1.Animate := true;
  // LoadingForm.Update;
end;

procedure TThreadSaveFpdModel.DoTerminate;
begin
  LoadingSaveFpdForm.Close;
end;

end.
