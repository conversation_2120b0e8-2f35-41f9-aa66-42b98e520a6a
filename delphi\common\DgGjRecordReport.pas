unit DgGjRecordReport;

interface

uses
  Classes;

type
  TDgGjRecordReport = class
  private
    FDgccsjgzreportksid: integer;
    FDgccsjgzid: integer;
    FDdh: string;
    FKh: string;
    FKsbm: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FDds: integer;
    FScs: integer;
    FODP_Date: string;
    FGzrqq: string;
    FGzrqz: string;
    FEmployee_Id: string;
    FEmployee_Name: string;
    FJhdj: double;
    FXs: double;
    FJsj: double;
    FCjgj: double;
    FGjhj: double;
    FJhGjhj: double;
    FRecordType: string;
    FGx: string;
    FGzze: double;
    FXwGzze: double;
    FGj: double;
    FXwJhdj: double;
    FXwCjgj: double;
    FXwGjhj: double;
    FJsrq: string;
    FEmployee_Id_1: integer;
    FEmployee_Id_2: integer;
    FEmployee_Id_3: integer;
    FEmployee_Id_4: integer;
    FEmployee_Id_5: integer;
    FEmployee_Id_6: integer;
    FEmployee_Id_7: integer;
    FEmployee_Id_8: integer;
    FEmployee_Id_9: integer;
    FEmployee_Id_10: integer;
    FEmployee_Id_11: integer;
    FEmployee_Id_12: integer;
    FEmployee_Id_13: integer;
    FEmployee_Id_14: integer;
    FEmployee_Id_15: integer;
    FEmployee_Id_16: integer;
    FEmployee_Id_17: integer;
    FEmployee_Id_18: integer;
    FEmployee_Id_19: integer;
    FEmployee_Id_20: integer;
    FEmployee_Id_21: integer;
    FEmployee_Id_22: integer;
    FEmployee_Id_23: integer;
    FEmployee_Id_24: integer;
    FEmployee_Id_25: integer;
    FEmployee_Id_26: integer;
    FEmployee_Id_27: integer;
    FEmployee_Id_28: integer;
    FEmployee_Id_29: integer;
    FEmployee_Id_30: integer;
    FEmployee_Id_31: integer;
    FEmployee_Id_32: integer;
    FEmployee_Id_33: integer;
    FEmployee_Id_34: integer;
    FEmployee_Id_35: integer;
    FEmployee_Id_36: integer;
    FEmployee_Id_37: integer;
    FEmployee_Id_38: integer;
    FEmployee_Id_39: integer;
    FEmployee_Id_40: integer;
    FEmployee_Id_41: integer;
    FEmployee_Id_42: integer;
    FEmployee_Id_43: integer;
    FEmployee_Id_44: integer;
    FEmployee_Id_45: integer;
    FEmployee_Id_46: integer;
    FEmployee_Id_47: integer;
    FEmployee_Id_48: integer;
    FEmployee_Id_49: integer;
    FEmployee_Id_50: integer;
    FEmployee_Id_0: integer;
  public
    property Dgccsjgzreportksid: integer read FDgccsjgzreportksid
      write FDgccsjgzreportksid;
    property Dgccsjgzid: integer read FDgccsjgzid write FDgccsjgzid;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Ksbm: string read FKsbm write FKsbm;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Dds: integer read FDds write FDds;
    property Scs: integer read FScs write FScs;
    property ODP_Date: string read FODP_Date write FODP_Date;
    property Gzrqq: string read FGzrqq write FGzrqq;
    property Gzrqz: string read FGzrqz write FGzrqz;
    property Employee_Id: string read FEmployee_Id write FEmployee_Id;
    property Employee_Name: string read FEmployee_Name write FEmployee_Name;
    property Jhdj: double read FJhdj write FJhdj;
    property Xs: double read FXs write FXs;
    property Jsj: double read FJsj write FJsj;
    property Cjgj: double read FCjgj write FCjgj;
    property Gjhj: double read FGjhj write FGjhj;
    property JhGjhj: double read FJhGjhj write FJhGjhj;
    property RecordType: string read FRecordType write FRecordType;
    property Gx: string read FGx write FGx;
    property Gzze: double read FGzze write FGzze;
    property XwGzze: double read FXwGzze write FXwGzze;
    property Gj: double read FGj write FGj;
    property XwJhdj: double read FXwJhdj write FXwJhdj;
    property XwCjgj: double read FXwCjgj write FXwCjgj;
    property XwGjhj: double read FXwGjhj write FXwGjhj;
    property Jsrq: string read FJsrq write FJsrq;
    property Employee_Id_1: integer read FEmployee_Id_1 write FEmployee_Id_1;
    property Employee_Id_2: integer read FEmployee_Id_2 write FEmployee_Id_2;
    property Employee_Id_3: integer read FEmployee_Id_3 write FEmployee_Id_3;
    property Employee_Id_4: integer read FEmployee_Id_4 write FEmployee_Id_4;
    property Employee_Id_5: integer read FEmployee_Id_5 write FEmployee_Id_5;
    property Employee_Id_6: integer read FEmployee_Id_6 write FEmployee_Id_6;
    property Employee_Id_7: integer read FEmployee_Id_7 write FEmployee_Id_7;
    property Employee_Id_8: integer read FEmployee_Id_8 write FEmployee_Id_8;
    property Employee_Id_9: integer read FEmployee_Id_9 write FEmployee_Id_9;
    property Employee_Id_10: integer read FEmployee_Id_10 write FEmployee_Id_10;
    property Employee_Id_11: integer read FEmployee_Id_11 write FEmployee_Id_11;
    property Employee_Id_12: integer read FEmployee_Id_12 write FEmployee_Id_12;
    property Employee_Id_13: integer read FEmployee_Id_13 write FEmployee_Id_13;
    property Employee_Id_14: integer read FEmployee_Id_14 write FEmployee_Id_14;
    property Employee_Id_15: integer read FEmployee_Id_15 write FEmployee_Id_15;
    property Employee_Id_16: integer read FEmployee_Id_16 write FEmployee_Id_16;
    property Employee_Id_17: integer read FEmployee_Id_17 write FEmployee_Id_17;
    property Employee_Id_18: integer read FEmployee_Id_18 write FEmployee_Id_18;
    property Employee_Id_19: integer read FEmployee_Id_19 write FEmployee_Id_19;
    property Employee_Id_20: integer read FEmployee_Id_20 write FEmployee_Id_20;
    property Employee_Id_21: integer read FEmployee_Id_21 write FEmployee_Id_21;
    property Employee_Id_22: integer read FEmployee_Id_22 write FEmployee_Id_22;
    property Employee_Id_23: integer read FEmployee_Id_23 write FEmployee_Id_23;
    property Employee_Id_24: integer read FEmployee_Id_24 write FEmployee_Id_24;
    property Employee_Id_25: integer read FEmployee_Id_25 write FEmployee_Id_25;
    property Employee_Id_26: integer read FEmployee_Id_26 write FEmployee_Id_26;
    property Employee_Id_27: integer read FEmployee_Id_27 write FEmployee_Id_27;
    property Employee_Id_28: integer read FEmployee_Id_28 write FEmployee_Id_28;
    property Employee_Id_29: integer read FEmployee_Id_29 write FEmployee_Id_29;
    property Employee_Id_30: integer read FEmployee_Id_30 write FEmployee_Id_30;
    property Employee_Id_31: integer read FEmployee_Id_31 write FEmployee_Id_31;
    property Employee_Id_32: integer read FEmployee_Id_32 write FEmployee_Id_32;
    property Employee_Id_33: integer read FEmployee_Id_33 write FEmployee_Id_33;
    property Employee_Id_34: integer read FEmployee_Id_34 write FEmployee_Id_34;
    property Employee_Id_35: integer read FEmployee_Id_35 write FEmployee_Id_35;
    property Employee_Id_36: integer read FEmployee_Id_36 write FEmployee_Id_36;
    property Employee_Id_37: integer read FEmployee_Id_37 write FEmployee_Id_37;
    property Employee_Id_38: integer read FEmployee_Id_38 write FEmployee_Id_38;
    property Employee_Id_39: integer read FEmployee_Id_39 write FEmployee_Id_39;
    property Employee_Id_40: integer read FEmployee_Id_40 write FEmployee_Id_40;
    property Employee_Id_41: integer read FEmployee_Id_41 write FEmployee_Id_41;
    property Employee_Id_42: integer read FEmployee_Id_42 write FEmployee_Id_42;
    property Employee_Id_43: integer read FEmployee_Id_43 write FEmployee_Id_43;
    property Employee_Id_44: integer read FEmployee_Id_44 write FEmployee_Id_44;
    property Employee_Id_45: integer read FEmployee_Id_45 write FEmployee_Id_45;
    property Employee_Id_46: integer read FEmployee_Id_46 write FEmployee_Id_46;
    property Employee_Id_47: integer read FEmployee_Id_47 write FEmployee_Id_47;
    property Employee_Id_48: integer read FEmployee_Id_48 write FEmployee_Id_48;
    property Employee_Id_49: integer read FEmployee_Id_49 write FEmployee_Id_49;
    property Employee_Id_50: integer read FEmployee_Id_50 write FEmployee_Id_50;
    property Employee_Id_0: integer read FEmployee_Id_0 write FEmployee_Id_0;
  end;

implementation

end.
