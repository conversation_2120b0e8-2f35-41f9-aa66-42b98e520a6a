unit CcCkFlow;

interface

uses
  Classes;

type
  TCcCkFlow = class
  private
    FCkflowid: integer;
    FCkflowtypenum: integer;
    FYwlxnum: integer;
    FGyscd: string;
    FPmtype: string;
    FPm: string;
    FGg: string;
    FLb: string;
    FKz: string;
    FYs: string;
    FSh: string;
    FGh: string;
    FCknamein: string;
    FCknameinamount: double;
    FCknameout: string;
    FCknameoutamount: double;
    FNote: string;
    FRecordtype: integer;
    FCkdate: string;
    FCkuser: string;
    Fdzdj: double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Ckflowid: integer read FCkflowid write FCkflowid;
    property Ckflowtypenum: integer read FCkflowtypenum write FCkflowtypenum;
    property Ywlxnum: integer read FYwlxnum write FYwlxnum;
    property Gyscd: string read FGyscd write FGyscd;
    property Pmtype: string read FPmtype write FPmtype;
    property Pm: string read FPm write FPm;
    property Gg: string read FGg write FGg;
    property Lb: string read FLb write FLb;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Sh: string read FSh write FSh;
    property Gh: string read FGh write FGh;
    property Cknamein: string read FCknamein write FCknamein;
    property Cknameinamount: double read FCknameinamount write FCknameinamount;
    property Cknameout: string read FCknameout write FCknameout;
    property Cknameoutamount: double read FCknameoutamount
      write FCknameoutamount;
    property Note: string read FNote write FNote;
    property Recordtype: integer read FRecordtype write FRecordtype;
    property Ckdate: string read FCkdate write FCkdate;
    property Ckuser: string read FCkuser write FCkuser;
    property dzdj: double read Fdzdj write Fdzdj;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
