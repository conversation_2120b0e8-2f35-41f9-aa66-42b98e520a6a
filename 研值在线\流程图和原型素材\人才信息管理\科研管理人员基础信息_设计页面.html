<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员基础信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研管理人员基础信息</h1>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                条件筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">姓名</label>
                    <input type="text" placeholder="请输入姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">学历</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="doctor">博士</option>
                        <option value="master">硕士</option>
                        <option value="bachelor">本科</option>
                        <option value="college">大专</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年龄区间</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="最小" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" placeholder="最大" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">工作单位</label>
                    <input type="text" placeholder="请输入工作单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">出生年月</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="date" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">毕业院校</label>
                    <input type="text" placeholder="请输入毕业院校" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">专业特长</label>
                    <input type="text" placeholder="请输入专业特长" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术职称</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="senior">高级职称</option>
                        <option value="middle">中级职称</option>
                        <option value="junior">初级职称</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">管理人员类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="enterprise">科技企业法人</option>
                        <option value="platform">科创平台负责人</option>
                        <option value="institute">科研机构管理者</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 管理人员列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">科研管理人员列表</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        批量导出
                    </button>
                    <button class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        批量移除
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学历</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">毕业院校</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业特长</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术职称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">张明</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">42</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技发展有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高级工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技企业法人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">李华</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">硕士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">35</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技创新研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科创平台负责人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王强</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">本科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">38</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究所</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江工业大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">机械工程</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研机构管理者</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">156</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科研管理人员详情</h3>
                    <button onclick="hideDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                            <p class="text-sm text-gray-900">张明</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">性别</label>
                            <p class="text-sm text-gray-900">男</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">出生年月</label>
                            <p class="text-sm text-gray-900">1981-05-12</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">学历</label>
                            <p class="text-sm text-gray-900">博士</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">毕业院校</label>
                            <p class="text-sm text-gray-900">浙江大学</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">专业特长</label>
                            <p class="text-sm text-gray-900">人工智能</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">工作单位</label>
                            <p class="text-sm text-gray-900">宁波市科技发展有限公司</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">职务</label>
                            <p class="text-sm text-gray-900">总经理</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">技术职称</label>
                            <p class="text-sm text-gray-900">高级工程师</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理人员类型</label>
                            <p class="text-sm text-gray-900">科技企业法人</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <p class="text-sm text-gray-900">138****8888</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                            <p class="text-sm text-gray-900"><EMAIL></p>
                        </div>
                    </div>
                </div>
                <div class="mt-6">
                    <h4 class="text-md font-medium text-gray-700 mb-2">关联科研项目</h4>
                    <div class="bg-gray-50 p-3 rounded-md">
                        <p class="text-sm text-gray-900">1. 宁波市人工智能关键技术研究与应用</p>
                        <p class="text-sm text-gray-900">2. 智能制造系统开发与产业化</p>
                    </div>
                </div>
                <div class="mt-4">
                    <h4 class="text-md font-medium text-gray-700 mb-2">关联科研平台</h4>
                    <div class="bg-gray-50 p-3 rounded-md">
                        <p class="text-sm text-gray-900">宁波市人工智能创新中心</p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="hideDetail()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        编辑信息
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 移除确认弹窗 -->
    <div id="removeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">确认移除</h3>
                    <button onclick="hideRemoveModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <p class="text-sm text-gray-600 mb-4">您确定要移除该科研管理人员吗？此操作不可恢复。</p>
                <div>
                    <label for="removeReason" class="block text-sm font-medium text-gray-700 mb-1">移除原因</label>
                    <textarea id="removeReason" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请填写移除原因"></textarea>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="hideRemoveModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        确认移除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示详情弹窗
        function showDetail() {
            document.getElementById('detailModal').classList.remove('hidden');
        }

        // 隐藏详情弹窗
        function hideDetail() {
            document.getElementById('detailModal').classList.add('hidden');
        }

        // 显示移除确认弹窗
        function showRemoveModal() {
            document.getElementById('removeModal').classList.remove('hidden');
        }

        // 隐藏移除确认弹窗
        function hideRemoveModal() {
            document.getElementById('removeModal').classList.add('hidden');
        }

        // 点击弹窗外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideDetail();
            }
        });

        document.getElementById('removeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRemoveModal();
            }
        });

        // 表格中的移除按钮点击事件
        document.querySelectorAll('button.text-red-600').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                showRemoveModal();
            });
        });
    </script>
</body>
</html>