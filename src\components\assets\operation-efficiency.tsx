'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Clock, 
  CheckCircle2Icon,
  AlertCircle,
  Users,
  Zap,
  Target,
  TrendingUp
} from "lucide-react"

const efficiencyData = {
  responseTime: {
    current: 5.2,
    target: 10,
    unit: "分钟",
    trend: -12.5,
    status: "good"
  },
  resolveRate: {
    current: 98.6,
    target: 95,
    unit: "%",
    trend: 2.3,
    status: "good"
  },
  satisfaction: {
    current: 96.8,
    target: 90,
    unit: "%",
    trend: 1.5,
    status: "good"
  },
  teamMetrics: [
    {
      team: "平台组",
      efficiency: 98.5,
      workload: 386,
      completion: 99.2
    },
    {
      team: "应用组",
      efficiency: 97.2,
      workload: 425,
      completion: 98.5
    },
    {
      team: "网络组",
      efficiency: 96.8,
      workload: 245,
      completion: 97.8
    },
    {
      team: "安全组",
      efficiency: 98.9,
      workload: 230,
      completion: 99.5
    }
  ]
}

export function OperationEfficiency() {
  return (
    <Card className="border-[#E5E9EF]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>效能分析</CardTitle>
            <p className="text-sm text-gray-500 mt-1">运维效能指标监控</p>
          </div>
          <Badge variant="outline" className="bg-green-50 text-green-600">
            <Target className="h-3 w-3 mr-1" />
            整体达标
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 核心指标 */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">平均响应时间</span>
              </div>
              <div className="text-2xl font-semibold text-blue-600">
                {efficiencyData.responseTime.current}
                <span className="text-sm ml-1">分钟</span>
              </div>
              <div className="flex items-center gap-1 mt-2 text-xs text-blue-600">
                <TrendingUp className="h-3 w-3" />
                较上期提升 {Math.abs(efficiencyData.responseTime.trend)}%
              </div>
            </div>

            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle2Icon className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">问题解决率</span>
              </div>
              <div className="text-2xl font-semibold text-green-600">
                {efficiencyData.resolveRate.current}%
              </div>
              <div className="flex items-center gap-1 mt-2 text-xs text-green-600">
                <TrendingUp className="h-3 w-3" />
                较上期提升 {efficiencyData.resolveRate.trend}%
              </div>
            </div>

            <div className="p-4 bg-purple-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">服务满意度</span>
              </div>
              <div className="text-2xl font-semibold text-purple-600">
                {efficiencyData.satisfaction.current}%
              </div>
              <div className="flex items-center gap-1 mt-2 text-xs text-purple-600">
                <TrendingUp className="h-3 w-3" />
                较上期提升 {efficiencyData.satisfaction.trend}%
              </div>
            </div>
          </div>

          {/* 团队效能 */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4 text-gray-400" />
              团队效能分析
            </h3>
            <div className="space-y-4">
              {efficiencyData.teamMetrics.map((team, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{team.team}</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-600">
                        工作量: {team.workload}
                      </Badge>
                    </div>
                    <span className="text-sm text-gray-500">
                      完成率: {team.completion}%
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-500">效能指数</span>
                      <span className="font-medium">{team.efficiency}%</span>
                    </div>
                    <Progress value={team.efficiency} className="h-2" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 