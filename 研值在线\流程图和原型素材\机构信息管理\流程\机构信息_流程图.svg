<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">机构信息管理流程图</text>

  <!-- 阶段一：请求触发与数据获取 -->
  <text x="700" y="80" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：请求触发与数据获取</text>
  
  <!-- 节点1: 用户请求 -->
  <g transform="translate(100, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户请求</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">详情查看或检索进入</text>
  </g>

  <!-- 节点2: 缓存检索 -->
  <g transform="translate(400, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存检索</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">本地缓存数据查找</text>
  </g>

  <!-- 节点3: 数据集成 -->
  <g transform="translate(700, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据集成服务</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">科技大脑+本项目数据</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 135 Q 350 135 400 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 135 Q 650 135 700 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="650" y="125" font-size="11" fill="#666">缓存缺失</text>

  <!-- 阶段二：数据校验与处理 -->
  <text x="700" y="230" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据校验与处理</text>

  <!-- 节点4: 字段映射校验 -->
  <g transform="translate(200, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段映射校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">完整性与格式校验</text>
  </g>

  <!-- 节点5: 数据聚合 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据聚合</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">层级结构、统计指标</text>
  </g>

  <!-- 节点6: 展示模型 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">展示数据模型</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">荣誉资质、经营数据</text>
  </g>

  <!-- 连接线从数据集成到字段映射校验 -->
  <path d="M 810 170 C 810 200, 300 220, 300 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 285 Q 450 285 500 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端渲染与日志记录 -->
  <text x="700" y="380" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端渲染与日志记录</text>

  <!-- 节点7: 前端渲染 -->
  <g transform="translate(400, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端渲染</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">页面数据展示</text>
  </g>

  <!-- 节点8: 日志记录 -->
  <g transform="translate(700, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志记录</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">访问轨迹、性能指标</text>
  </g>

  <!-- 连接线从展示模型到前端渲染和日志记录 -->
  <path d="M 900 320 C 900 350, 500 370, 500 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 320 Q 900 360 800 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据变更与审批流程 -->
  <text x="700" y="530" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据变更与审批流程</text>

  <!-- 节点9: 编辑操作 -->
  <g transform="translate(100, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑操作</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">字段合法性校验</text>
  </g>

  <!-- 节点10: 临时版本 -->
  <g transform="translate(350, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">临时版本</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">审批队列提交</text>
  </g>

  <!-- 节点11: 正式落库 -->
  <g transform="translate(600, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">正式落库</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">审批通过后</text>
  </g>

  <!-- 节点12: 风险评估 -->
  <g transform="translate(850, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">风险评估</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">风险标签更新</text>
  </g>

  <!-- 连接线 9 -> 10 -> 11 -> 12 -->
  <path d="M 280 585 Q 315 585 350 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 530 585 Q 565 585 600 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 585 Q 815 585 850 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="815" y="575" font-size="11" fill="#666">涉及风险字段</text>

  <!-- 阶段五：通知与同步 -->
  <text x="700" y="680" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段五：通知与同步</text>

  <!-- 节点13: 指标重计算 -->
  <g transform="translate(250, 700)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">指标重计算</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">层级关系同步</text>
  </g>

  <!-- 节点14: 消息通知 -->
  <g transform="translate(500, 700)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息通知</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">维护人员、订阅者</text>
  </g>

  <!-- 节点15: 页面更新 -->
  <g transform="translate(750, 700)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面更新</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">风险状态展示</text>
  </g>

  <!-- 连接线从正式落库到指标重计算和消息通知 -->
  <path d="M 690 620 C 690 650, 340 670, 340 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 690 620 C 690 650, 590 670, 590 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线从风险评估到页面更新 -->
  <path d="M 940 620 C 940 650, 840 670, 840 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从前端渲染到编辑操作 -->
  <path d="M 400 435 C 350 450, 200 500, 190 550" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="280" y="490" font-size="11" fill="#666">用户编辑</text>

  <!-- 反馈循环：从页面更新回到前端渲染 -->
  <path d="M 750 735 C 650 750, 550 750, 500 470" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="620" y="760" font-size="11" fill="#666">状态更新循环</text>

</svg>