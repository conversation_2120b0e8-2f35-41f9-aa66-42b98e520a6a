<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业储备库 - 企业检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'blue-50': '#EFF6FF',
                        'blue-100': '#DBEAFE',
                        'blue-500': '#3B82F6',
                        'blue-600': '#2563EB'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F1F5F9 0%, #EFF6FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
        }
        .tab-active {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
            color: white;
        }
        .enterprise-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        .enterprise-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
            border-left-color: #3B82F6;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-blue-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-database text-blue-600"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">企业储备库</h1>
                        <p class="text-sm text-gray-600">区域内已分类储备的重点企业名单</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        添加企业
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- 统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-seedling text-green-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">种子</span>
                </div>
                <p class="text-2xl font-bold text-gray-900">186</p>
                <p class="text-sm text-gray-600">种子企业</p>
                <div class="mt-2 flex items-center text-xs">
                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                    <span class="text-green-600">+12.5%</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-microscope text-blue-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">科小</span>
                </div>
                <p class="text-2xl font-bold text-gray-900">394</p>
                <p class="text-sm text-gray-600">科技型中小企业</p>
                <div class="mt-2 flex items-center text-xs">
                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                    <span class="text-green-600">+8.3%</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-award text-purple-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">高企</span>
                </div>
                <p class="text-2xl font-bold text-gray-900">892</p>
                <p class="text-sm text-gray-600">高新技术企业</p>
                <div class="mt-2 flex items-center text-xs">
                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                    <span class="text-green-600">+15.2%</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-running text-orange-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded-full">瞪羚</span>
                </div>
                <p class="text-2xl font-bold text-gray-900">156</p>
                <p class="text-sm text-gray-600">瞪羚之星企业</p>
                <div class="mt-2 flex items-center text-xs">
                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                    <span class="text-green-600">+23.1%</span>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-3">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-100 to-red-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-crown text-red-600 text-xl"></i>
                    </div>
                    <span class="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">领军</span>
                </div>
                <p class="text-2xl font-bold text-gray-900">89</p>
                <p class="text-sm text-gray-600">科技领军企业</p>
                <div class="mt-2 flex items-center text-xs">
                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>
                    <span class="text-green-600">+6.8%</span>
                </div>
            </div>
        </div>

        <!-- Tab导航和筛选 -->
        <div class="bg-white rounded-xl card-shadow mb-6">
            <div class="flex items-center justify-between p-6 border-b border-gray-100">
                <div class="flex items-center space-x-1">
                    <button class="tab-btn tab-active px-4 py-2 rounded-lg transition-all text-sm font-medium" data-tab="all">
                        全部企业 (1,717)
                    </button>
                    <button class="tab-btn px-4 py-2 rounded-lg transition-all text-sm font-medium text-gray-600 hover:text-gray-900" data-tab="seed">
                        种子企业 (186)
                    </button>
                    <button class="tab-btn px-4 py-2 rounded-lg transition-all text-sm font-medium text-gray-600 hover:text-gray-900" data-tab="sme">
                        科技型中小企业 (394)
                    </button>
                    <button class="tab-btn px-4 py-2 rounded-lg transition-all text-sm font-medium text-gray-600 hover:text-gray-900" data-tab="hitech">
                        高新技术企业 (892)
                    </button>
                    <button class="tab-btn px-4 py-2 rounded-lg transition-all text-sm font-medium text-gray-600 hover:text-gray-900" data-tab="gazelle">
                        瞪羚之星 (156)
                    </button>
                    <button class="tab-btn px-4 py-2 rounded-lg transition-all text-sm font-medium text-gray-600 hover:text-gray-900" data-tab="leader">
                        科技领军 (89)
                    </button>
                </div>
                
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <input type="text" placeholder="搜索企业名称..." 
                               class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-64">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <select class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>全部行业</option>
                        <option>智能制造</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>数字经济</option>
                    </select>
                    <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-filter mr-2"></i>
                        高级筛选
                    </button>
                </div>
            </div>
        </div>

        <!-- 企业列表 -->
        <div class="space-y-4" id="enterprise-list">
            <!-- 企业卡片1 -->
            <div class="enterprise-card bg-white rounded-xl card-shadow p-6 cursor-pointer" onclick="showEnterpriseDetail('company1')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=64&h=64&fit=crop&crop=entropy" 
                                 alt="企业logo" class="w-12 h-12 rounded-lg">
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">宁波智能制造科技有限公司</h3>
                                <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">高新技术企业</span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">科技型中小企业</span>
                            </div>
                            <div class="flex items-center space-x-6 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <i class="fas fa-industry mr-2"></i>
                                    <span>智能制造</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <span>鄞州区</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>成立时间：2018年</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <span>员工：234人</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <p class="text-lg font-bold text-blue-600">2.68亿</p>
                            <p class="text-xs text-gray-600">营业收入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-green-600">8,650万</p>
                            <p class="text-xs text-gray-600">研发投入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-purple-600">32.3%</p>
                            <p class="text-xs text-gray-600">研发强度</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-orange-600">156项</p>
                            <p class="text-xs text-gray-600">专利数量</p>
                        </div>
                        <button class="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <!-- 企业亮点 -->
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-trophy text-yellow-500"></i>
                                <span class="text-sm text-gray-700">国家级专精特新小巨人</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-fire text-red-500"></i>
                                <span class="text-sm text-gray-700">研发费用增长 +45.6%</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-handshake text-blue-500"></i>
                                <span class="text-sm text-gray-700">与浙大合作研发</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                            <span class="text-sm text-gray-600">活跃状态</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业卡片2 -->
            <div class="enterprise-card bg-white rounded-xl card-shadow p-6 cursor-pointer" onclick="showEnterpriseDetail('company2')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=64&h=64&fit=crop&crop=entropy" 
                                 alt="企业logo" class="w-12 h-12 rounded-lg">
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">东海生物医药技术股份有限公司</h3>
                                <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">种子企业</span>
                                <span class="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">瞪羚之星</span>
                            </div>
                            <div class="flex items-center space-x-6 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <i class="fas fa-pills mr-2"></i>
                                    <span>生物医药</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <span>北仑区</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>成立时间：2021年</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <span>员工：89人</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <p class="text-lg font-bold text-blue-600">8,650万</p>
                            <p class="text-xs text-gray-600">营业收入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-green-600">3,240万</p>
                            <p class="text-xs text-gray-600">研发投入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-purple-600">37.4%</p>
                            <p class="text-xs text-gray-600">研发强度</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-orange-600">89项</p>
                            <p class="text-xs text-gray-600">专利数量</p>
                        </div>
                        <button class="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-rocket text-purple-500"></i>
                                <span class="text-sm text-gray-700">潜力种子企业</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-chart-line text-green-500"></i>
                                <span class="text-sm text-gray-700">营收增长 +128.3%</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-graduation-cap text-blue-500"></i>
                                <span class="text-sm text-gray-700">博士团队12人</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-orange-500 rounded-full"></span>
                            <span class="text-sm text-gray-600">快速成长</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 企业卡片3 -->
            <div class="enterprise-card bg-white rounded-xl card-shadow p-6 cursor-pointer" onclick="showEnterpriseDetail('company3')">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=64&h=64&fit=crop&crop=entropy" 
                                 alt="企业logo" class="w-12 h-12 rounded-lg">
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">甬江新材料科技集团股份有限公司</h3>
                                <span class="px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">科技领军企业</span>
                                <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">高新技术企业</span>
                            </div>
                            <div class="flex items-center space-x-6 text-sm text-gray-600">
                                <div class="flex items-center">
                                    <i class="fas fa-atom mr-2"></i>
                                    <span>新材料</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-map-marker-alt mr-2"></i>
                                    <span>镇海区</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-calendar mr-2"></i>
                                    <span>成立时间：2015年</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-2"></i>
                                    <span>员工：856人</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center space-x-6">
                        <div class="text-center">
                            <p class="text-lg font-bold text-blue-600">15.8亿</p>
                            <p class="text-xs text-gray-600">营业收入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-green-600">2.4亿</p>
                            <p class="text-xs text-gray-600">研发投入</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-purple-600">15.2%</p>
                            <p class="text-xs text-gray-600">研发强度</p>
                        </div>
                        <div class="text-center">
                            <p class="text-lg font-bold text-orange-600">342项</p>
                            <p class="text-xs text-gray-600">专利数量</p>
                        </div>
                        <button class="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-crown text-yellow-500"></i>
                                <span class="text-sm text-gray-700">行业领军企业</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-globe text-blue-500"></i>
                                <span class="text-sm text-gray-700">国际合作项目5个</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-medal text-purple-500"></i>
                                <span class="text-sm text-gray-700">国家科技进步奖</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            <span class="text-sm text-gray-600">稳定发展</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-8">
            <div class="text-sm text-gray-600">
                显示第 1-20 条，共 1,717 条记录
            </div>
            <div class="flex items-center space-x-2">
                <button class="p-2 text-gray-400 hover:text-gray-600 rounded border border-gray-300 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="px-3 py-2 bg-blue-500 text-white rounded">1</button>
                <button class="px-3 py-2 text-gray-600 hover:text-gray-900 rounded">2</button>
                <button class="px-3 py-2 text-gray-600 hover:text-gray-900 rounded">3</button>
                <span class="px-3 py-2 text-gray-400">...</span>
                <button class="px-3 py-2 text-gray-600 hover:text-gray-900 rounded">86</button>
                <button class="p-2 text-gray-400 hover:text-gray-600 rounded border border-gray-300 hover:border-gray-400 transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </main>

    <!-- 企业详情模态框 -->
    <div id="enterprise-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-gray-900">企业详情</h2>
                    <button onclick="closeModal()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6" id="modal-content">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <script>
        // Tab切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            
            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 移除所有active类
                    tabBtns.forEach(b => {
                        b.classList.remove('tab-active');
                        b.classList.add('text-gray-600', 'hover:text-gray-900');
                    });
                    
                    // 添加active类到当前按钮
                    this.classList.add('tab-active');
                    this.classList.remove('text-gray-600', 'hover:text-gray-900');
                    
                    // 这里可以添加根据tab类型筛选企业列表的逻辑
                    filterByType(this.getAttribute('data-tab'));
                });
            });
        });

        // 根据类型筛选企业
        function filterByType(type) {
            console.log('筛选企业类型:', type);
            // 实际实现中这里会调用API重新获取数据
        }

        // 显示企业详情
        function showEnterpriseDetail(companyId) {
            const modal = document.getElementById('enterprise-modal');
            const content = document.getElementById('modal-content');
            
            // 模拟企业详情数据
            const mockData = {
                company1: {
                    name: '宁波智能制造科技有限公司',
                    industry: '智能制造',
                    location: '鄞州区',
                    established: '2018年',
                    employees: '234人',
                    revenue: '2.68亿元',
                    rdInvestment: '8,650万元',
                    rdIntensity: '32.3%',
                    patents: '156项',
                    description: '专注于工业机器人和自动化设备的研发制造，是国家级专精特新小巨人企业。'
                }
            };
            
            const data = mockData[companyId] || mockData.company1;
            
            content.innerHTML = `
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">企业名称</span>
                                    <span class="font-medium">${data.name}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">所属行业</span>
                                    <span class="font-medium">${data.industry}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">所在区域</span>
                                    <span class="font-medium">${data.location}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">成立时间</span>
                                    <span class="font-medium">${data.established}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">员工人数</span>
                                    <span class="font-medium">${data.employees}</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">经营指标</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">营业收入</span>
                                    <span class="font-medium text-blue-600">${data.revenue}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发投入</span>
                                    <span class="font-medium text-green-600">${data.rdInvestment}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发强度</span>
                                    <span class="font-medium text-purple-600">${data.rdIntensity}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">专利数量</span>
                                    <span class="font-medium text-orange-600">${data.patents}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">企业描述</h3>
                        <p class="text-gray-700 leading-relaxed">${data.description}</p>
                    </div>
                    <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                        <button class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            编辑企业信息
                        </button>
                        <button onclick="closeModal()" class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            
            modal.classList.remove('hidden');
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('enterprise-modal').classList.add('hidden');
        }

        // 点击模态框背景关闭
        document.getElementById('enterprise-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html> 