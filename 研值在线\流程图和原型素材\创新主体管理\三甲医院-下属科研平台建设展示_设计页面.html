<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-下属科研平台建设展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">下属科研平台建设展示</h1>
            <p class="mt-2 text-gray-600">集中呈现重点实验室、研究中心等多类型科研平台的数量、领域分布与运行状态</p>
        </div>

        <!-- 卡片概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="openPlatformList('重点实验室')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">重点实验室</p>
                        <p class="text-2xl font-bold text-blue-600">28</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm ml-1">12%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="openPlatformList('研究中心')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">研究中心</p>
                        <p class="text-2xl font-bold text-blue-600">42</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm ml-1">8%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="openPlatformList('共享服务平台')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">共享服务平台</p>
                        <p class="text-2xl font-bold text-blue-600">15</p>
                    </div>
                    <div class="text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                        <span class="text-sm ml-1">5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="openPlatformList('工程实验室')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">工程实验室</p>
                        <p class="text-2xl font-bold text-blue-600">23</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm ml-1">15%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer" onclick="openPlatformList('工程技术研究中心')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">工程技术研究中心</p>
                        <p class="text-2xl font-bold text-blue-600">19</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                        <span class="text-sm ml-1">10%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">平台类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            重点实验室
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            研究中心
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            共享服务平台
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="medical">生物医学</option>
                        <option value="material">材料科学</option>
                        <option value="environment">环境科学</option>
                        <option value="chemistry">化学分析</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">主管部门</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部部门</option>
                        <option value="ministry">科技部</option>
                        <option value="province">省科技厅</option>
                        <option value="city">市科技局</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">认定级别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="hospital">院级</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">运行状态</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            运行中
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            建设中
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            已停用
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入平台名称或负责人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 科研平台列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">科研平台列表</h2>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                            导出
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">依托单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">运行状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市生物医学重点实验室</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点实验室</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openPlatformDetail('1')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                <button class="text-indigo-600 hover:text-indigo-900">平台资源</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市材料科学研究中心</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">材料科学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第二医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openPlatformDetail('2')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                <button class="text-indigo-600 hover:text-indigo-900">平台资源</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市医疗资源共享服务平台</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">共享服务平台</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市医疗中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王主任</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">建设中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openPlatformDetail('3')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                <button class="text-indigo-600 hover:text-indigo-900">平台资源</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">平台类型分布</h3>
                    <div class="h-80">
                        <canvas id="platformTypeChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">技术领域分布</h3>
                    <div class="h-80">
                        <canvas id="techFieldChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">认定级别分布</h3>
                    <div class="h-80">
                        <canvas id="approvalLevelChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">年度开放共享服务次数</h3>
                    <div class="h-80">
                        <canvas id="serviceTimesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 平台详情侧栏 -->
    <div id="platformDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">宁波市生物医学重点实验室</h3>
                <button onclick="closeModal('platformDetailModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="flex mb-6">
                <div class="w-32 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                </div>
                <div class="ml-6 flex-1">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">平台类型</p>
                            <p class="text-sm font-medium text-gray-900">重点实验室</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">技术领域</p>
                            <p class="text-sm font-medium text-gray-900">生物医学</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">认定级别</p>
                            <p class="text-sm font-medium text-gray-900">省级</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">依托单位</p>
                            <p class="text-sm font-medium text-gray-900">宁波市第一医院</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">负责人</p>
                            <p class="text-sm font-medium text-gray-900">张教授</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">运行状态</p>
                            <p class="text-sm font-medium text-gray-900">运行中</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8">
                    <button class="border-b-2 border-blue-500 text-blue-600 px-1 py-4 text-sm font-medium">平台简介</button>
                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">主要仪器设备</button>
                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">在研项目</button>
                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">开放共享记录</button>
                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">合作成果</button>
                </nav>
            </div>
            
            <div class="py-6">
                <h4 class="text-lg font-medium text-gray-900 mb-4">平台简介</h4>
                <div class="prose max-w-none text-gray-600">
                    <p>宁波市生物医学重点实验室成立于2015年，依托于宁波市第一医院，是宁波市重点建设的生物医学研究平台。实验室聚焦重大疾病发病机制、新型诊疗技术和转化医学研究，拥有先进的实验设备和专业的研究团队。</p>
                    <p>实验室现有研究人员45人，其中高级职称15人，博士研究生导师8人。实验室面积2000平方米，设备原值超过5000万元，包括流式细胞仪、激光共聚焦显微镜、蛋白质组学分析系统等大型仪器设备。</p>
                    <p>近年来，实验室承担国家级项目12项，省部级项目25项，发表SCI论文80余篇，获授权发明专利15项，获省部级科技奖励5项。实验室与国内外多家高校和研究机构建立了长期合作关系。</p>
                </div>
            </div>
            
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-between">
                    <div class="text-sm text-gray-500">
                        最后更新时间：2024-01-15
                    </div>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                            导出PDF
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                            </svg>
                            分享链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function openPlatformList(type) {
            alert('将显示' + type + '列表');
        }

        function openPlatformDetail(id) {
            openModal('platformDetailModal');
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化平台类型分布图表
            const platformTypeCtx = document.getElementById('platformTypeChart').getContext('2d');
            new Chart(platformTypeCtx, {
                type: 'bar',
                data: {
                    labels: ['重点实验室', '研究中心', '共享服务平台', '工程实验室', '工程技术研究中心'],
                    datasets: [{
                        label: '数量',
                        data: [28, 42, 15, 23, 19],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化技术领域分布图表
            const techFieldCtx = document.getElementById('techFieldChart').getContext('2d');
            new Chart(techFieldCtx, {
                type: 'doughnut',
                data: {
                    labels: ['生物医学', '材料科学', '环境科学', '化学分析', '物理研究'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // 初始化认定级别分布图表
            const approvalLevelCtx = document.getElementById('approvalLevelChart').getContext('2d');
            new Chart(approvalLevelCtx, {
                type: 'pie',
                data: {
                    labels: ['国家级', '省级', '市级', '院级'],
                    datasets: [{
                        data: [8, 32, 45, 15],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });

            // 初始化年度开放共享服务次数图表
            const serviceTimesCtx = document.getElementById('serviceTimesChart').getContext('2d');
            new Chart(serviceTimesCtx, {
                type: 'line',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        label: '服务次数',
                        data: [1200, 1800, 2500],
                        borderColor: 'rgba(59, 130, 246, 0.8)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
        });
    </script>
</body>
</html>