<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">高等院校信息管理流程图</text>

  <!-- 阶段一：信息提交与初始校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：信息提交与初始校验</text>
  
  <!-- 节点1: 用户提交院校信息 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交院校信息</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">新增或批量导入</text>
  </g>

  <!-- 节点2: 必填项校验与唯一性比对 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验与比对</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">必填项校验与唯一性比对</text>
  </g>

  <!-- 节点3: 生成初始档案 -->
  <g transform="translate(600, 370)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成初始档案</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标记为"待完善"</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 320 Q 700 345 700 370" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：自动补全与任务推送 -->
  <text x="700" y="490" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：自动补全与任务推送</text>

  <!-- 节点4: 外部共享目录补全 -->
  <g transform="translate(300, 540)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部共享目录补全</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动补全可获取字段</text>
  </g>

  <!-- 节点5: 生成待维护任务 -->
  <g transform="translate(900, 540)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成待维护任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推送至数据责任人</text>
  </g>

  <!-- 连接线 初始档案 -> 外部补全 -->
  <path d="M 650 440 C 550 470, 450 500, 400 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 初始档案 -> 待维护任务 -->
  <path d="M 750 440 C 850 470, 950 500, 1000 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：信息完善与状态更新 -->
  <text x="700" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：信息完善与状态更新</text>

  <!-- 节点6: 责任人信息补录 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">责任人信息补录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">在待办中心完成补录</text>
  </g>

  <!-- 节点7: 状态更新与统计 -->
  <g transform="translate(600, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新与统计</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新为"已完善"并计算评分</text>
  </g>

  <!-- 节点8: 关联管理 -->
  <g transform="translate(1000, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">建立或解除关系映射</text>
  </g>

  <!-- 连接线 待维护任务 -> 责任人补录 -->
  <path d="M 950 610 C 800 650, 500 680, 300 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 责任人补录 -> 状态更新 -->
  <path d="M 400 755 C 450 755, 550 755, 600 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 状态更新 -> 关联管理 -->
  <path d="M 800 755 C 850 755, 900 755, 1000 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：删除操作与数据治理 -->
  <text x="700" y="850" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：删除操作与数据治理</text>
  
  <!-- 节点9: 删除操作检查 -->
  <g transform="translate(200, 900)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除操作检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">检查关键关联依赖</text>
  </g>

  <!-- 节点10: 数据治理扫描 -->
  <g transform="translate(600, 900)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据治理扫描</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">定期扫描完整度与过期数据</text>
  </g>

  <!-- 节点11: 质量提升清单 -->
  <g transform="translate(1000, 900)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量提升清单</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成清单并派发责任人</text>
  </g>

  <!-- 连接线 关联管理 -> 删除检查 -->
  <path d="M 1000 790 C 800 830, 500 870, 300 900" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据治理扫描 -> 质量提升清单 -->
  <path d="M 800 935 C 850 935, 900 935, 1000 935" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：质量提升清单 -> 待维护任务 -->
  <path d="M 1100 900 C 1200 800, 1200 600, 1050 575" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="750" text-anchor="middle" font-size="12" fill="#555">闭环治理</text>

  <!-- 监控循环：数据治理扫描 -> 状态更新 -->
  <path d="M 650 900 C 650 850, 650 800, 650 790" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="580" y="845" text-anchor="middle" font-size="12" fill="#555">持续监控</text>

</svg>