object CC_CkMlReportTotalFrame: TCC_CkMlReportTotalFrame
  Left = 0
  Top = 0
  Width = 965
  Height = 600
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object MainPanel: TRzPanel
    Left = 0
    Top = 40
    Width = 965
    Height = 560
    Align = alClient
    BorderOuter = fsNone
    BorderColor = clWhite
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    ExplicitWidth = 941
    object RzPanel2: TRzPanel
      Left = 0
      Top = 0
      Width = 965
      Height = 560
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      ExplicitWidth = 941
      object LeftPanel: TRzPanel
        Left = 0
        Top = 0
        Width = 965
        Height = 560
        Align = alClient
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
        ExplicitWidth = 941
        object KsAdvStringGrid: TAdvStringGrid
          Left = 0
          Top = 0
          Width = 965
          Height = 560
          Cursor = crDefault
          Align = alClient
          BevelInner = bvNone
          BevelOuter = bvNone
          ColCount = 20
          Ctl3D = True
          DefaultRowHeight = 24
          DoubleBuffered = False
          DrawingStyle = gdsClassic
          FixedCols = 0
          FixedRows = 2
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing]
          ParentCtl3D = False
          ParentDoubleBuffered = False
          ParentFont = False
          ScrollBars = ssVertical
          TabOrder = 0
          OnFixedCellClick = KsAdvStringGridFixedCellClick
          OnMouseMove = KsAdvStringGridMouseMove
          GridLineColor = 15855083
          GridFixedLineColor = 13745060
          HoverRowCells = [hcNormal, hcSelected]
          OnAnchorClick = KsAdvStringGridAnchorClick
          OnGetFloatFormat = KsAdvStringGridGetFloatFormat
          HighlightColor = clNone
          ActiveCellFont.Charset = DEFAULT_CHARSET
          ActiveCellFont.Color = clWindowText
          ActiveCellFont.Height = -12
          ActiveCellFont.Name = #24494#36719#38597#40657
          ActiveCellFont.Style = [fsBold]
          ActiveCellColor = 10344697
          ActiveCellColorTo = 6210033
          ControlLook.FixedGradientFrom = 16513526
          ControlLook.FixedGradientTo = 15260626
          ControlLook.FixedGradientHoverFrom = 15000287
          ControlLook.FixedGradientHoverTo = 14406605
          ControlLook.FixedGradientHoverMirrorFrom = 14406605
          ControlLook.FixedGradientHoverMirrorTo = 13813180
          ControlLook.FixedGradientHoverBorder = 12033927
          ControlLook.FixedGradientDownFrom = 14991773
          ControlLook.FixedGradientDownTo = 14991773
          ControlLook.FixedGradientDownMirrorFrom = 14991773
          ControlLook.FixedGradientDownMirrorTo = 14991773
          ControlLook.FixedGradientDownBorder = 14991773
          ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
          ControlLook.DropDownHeader.Font.Color = clWindowText
          ControlLook.DropDownHeader.Font.Height = -11
          ControlLook.DropDownHeader.Font.Name = 'Tahoma'
          ControlLook.DropDownHeader.Font.Style = []
          ControlLook.DropDownHeader.Visible = True
          ControlLook.DropDownHeader.Buttons = <>
          ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
          ControlLook.DropDownFooter.Font.Color = clWindowText
          ControlLook.DropDownFooter.Font.Height = -11
          ControlLook.DropDownFooter.Font.Name = 'Tahoma'
          ControlLook.DropDownFooter.Font.Style = []
          ControlLook.DropDownFooter.Visible = True
          ControlLook.DropDownFooter.Buttons = <>
          Filter = <>
          FilterDropDown.ColumnWidth = True
          FilterDropDown.Font.Charset = DEFAULT_CHARSET
          FilterDropDown.Font.Color = clWindowText
          FilterDropDown.Font.Height = -12
          FilterDropDown.Font.Name = #24494#36719#38597#40657
          FilterDropDown.Font.Style = []
          FilterDropDown.GlyphActive.Data = {
            36050000424D3605000000000000360400002800000010000000100000000100
            08000000000000010000530B0000530B00000001000000010000104A10001063
            100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
            63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
            8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
            1414020214141414141414141414141414030902141414141414141414141414
            030D090214141414141414141414141403130902141414141414141414141414
            0313090214141414141414141414141403130902141414141414141414141414
            0313090214141414141414141414141403130902141414141414141414141403
            130D09000214141414141414141403130D0D0501000214141414141414031311
            0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
            0806090909040100021403030303030303030303030303030303141414141414
            1414141414141414141414141414141414141414141414141414}
          FilterDropDown.Height = 200
          FilterDropDown.TextChecked = 'Checked'
          FilterDropDown.TextUnChecked = 'Unchecked'
          FilterDropDown.Width = 200
          FilterDropDownClear = #20840#37096
          FilterDropDownCheck = True
          FilterEdit.TypeNames.Strings = (
            'Starts with'
            'Ends with'
            'Contains'
            'Not contains'
            'Equal'
            'Not equal'
            'Clear')
          FixedFooters = 1
          FixedColWidth = 35
          FixedRowHeight = 24
          FixedRowAlways = True
          FixedFont.Charset = DEFAULT_CHARSET
          FixedFont.Color = clBlack
          FixedFont.Height = -12
          FixedFont.Name = #24494#36719#38597#40657
          FixedFont.Style = []
          FloatFormat = '%.2f'
          FloatingFooter.Visible = True
          GridImages = PngImageList1
          HoverButtons.Buttons = <>
          HoverButtons.Position = hbLeftFromColumnLeft
          HoverFixedCells = hfFixedRows
          HTMLSettings.ImageFolder = 'images'
          HTMLSettings.ImageBaseName = 'img'
          Look = glOffice2007
          PrintSettings.DateFormat = 'dd/mm/yyyy'
          PrintSettings.Font.Charset = DEFAULT_CHARSET
          PrintSettings.Font.Color = clWindowText
          PrintSettings.Font.Height = -11
          PrintSettings.Font.Name = 'Tahoma'
          PrintSettings.Font.Style = []
          PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
          PrintSettings.FixedFont.Color = clWindowText
          PrintSettings.FixedFont.Height = -11
          PrintSettings.FixedFont.Name = 'Tahoma'
          PrintSettings.FixedFont.Style = []
          PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
          PrintSettings.HeaderFont.Color = clWindowText
          PrintSettings.HeaderFont.Height = -11
          PrintSettings.HeaderFont.Name = 'Tahoma'
          PrintSettings.HeaderFont.Style = []
          PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
          PrintSettings.FooterFont.Color = clWindowText
          PrintSettings.FooterFont.Height = -11
          PrintSettings.FooterFont.Name = 'Tahoma'
          PrintSettings.FooterFont.Style = []
          PrintSettings.PageNumSep = '/'
          SearchFooter.Color = 16513526
          SearchFooter.ColorTo = clNone
          SearchFooter.FindNextCaption = 'Find &next'
          SearchFooter.FindPrevCaption = 'Find &previous'
          SearchFooter.Font.Charset = DEFAULT_CHARSET
          SearchFooter.Font.Color = clWindowText
          SearchFooter.Font.Height = -11
          SearchFooter.Font.Name = 'Tahoma'
          SearchFooter.Font.Style = []
          SearchFooter.HighLightCaption = 'Highlight'
          SearchFooter.HintClose = 'Close'
          SearchFooter.HintFindNext = 'Find next occurrence'
          SearchFooter.HintFindPrev = 'Find previous occurrence'
          SearchFooter.HintHighlight = 'Highlight occurrences'
          SearchFooter.MatchCaseCaption = 'Match case'
          SelectionColor = 6210033
          SortSettings.DefaultFormat = ssAutomatic
          URLUnderlineOnHover = True
          UseInternalHintClass = False
          VAlignment = vtaCenter
          Version = '8.1.3.0'
          WordWrap = False
          ExplicitWidth = 941
          ColWidths = (
            35
            51
            61
            59
            51
            54
            50
            51
            58
            54
            64
            64
            64
            64
            64
            64
            64
            64
            64
            64)
        end
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 965
    Height = 40
    Align = alTop
    BorderOuter = fsNone
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    GradientColorStyle = gcsCustom
    ParentFont = False
    TabOrder = 1
    ExplicitWidth = 941
    object RzPanel4: TRzPanel
      Left = 0
      Top = 0
      Width = 1100
      Height = 40
      Align = alLeft
      BorderOuter = fsNone
      BorderColor = 14671839
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object RzLabel1: TRzLabel
        Left = 665
        Top = 21
        Width = 84
        Height = 16
        Caption = '16 '#24180' 11 '#26376' 26 '#26085
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        Transparent = True
        Visible = False
      end
      object RzLabel4: TRzLabel
        Left = 225
        Top = 8
        Width = 76
        Height = 26
        Caption = #27969#27700#35814#21333
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
    end
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 1
    OnTimer = Timer1Timer
    Left = 640
    Top = 136
  end
  object PngImageList1: TPngImageList
    PngImages = <
      item
        Background = clWindow
        Name = 'filter'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          61000000854944415478DA6364A010300E230316CE9FBFFAFCF9F321C4683234
          345C139F98188A62C0F4E9D3C56E5EBFFE921803D43535C53333335F617861C1
          DCB961172E5E5C894FB3BEA161586262E26A9C6180CF2BC84EC769003EAF203B
          1DA701B8BC82EE74BC06A07B059BD3091A80EC156C4E276800CC2BFF9998FE63
          733A5106100306DE0000FDD14611086D579B0000000049454E44AE426082}
      end
      item
        Background = clWindow
        Name = 'filterdown'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          610000001974455874536F6674776172650041646F626520496D616765526561
          647971C9653C000002834944415478DAA5925F6852511CC77FF77AF57A9D7F33
          1B9A10C1A897D120073E864205494ECAA4A78D609B5B4F238A5583465039FAF3
          B0070B31A88788A811AC6D3E16582CC4EE8314D1838E25D80AFC537AFD37B5DB
          EF5E6D69592F1DF871EE39F77C3FE77BBEE7103CCFC3FF344200B85CAEA38944
          C2188FC7A152A908F314968E244935C330A0542AEB6AB5BAAAD56AABD85770BC
          8EFF16DB01E3C1603050AFD74540B95C068EAB423A5D029294824AA5028D4623
          808061E4E0F57A4E23E04E07001D80DFEF0F140A0514735845C86639A02819E0
          AEA0D3E94488CF77690A419B1D0087C301C964723C140A0532990C0AB3E86413
          36363890C918D0EBF560301840ABD5C0E4E4C9298542314F51D4AF0CAC562B94
          4AA53ECCC01E894402F97C1EC71548A5BE815CAE84DEDE1D28D6C1C484E73CBA
          599448241F3A426C0100017D98813D1A8D0638AE84AED2D0D3A30393C9082323
          CE590C710101EF11007F030821F6E1FCA1D5D5D7FEB5B5CF787E038C8D1DBF86
          C247643FFDF6CB010EBE1A8B00C2F517BF770508E0BD58EE959557573C9EC3D7
          31B487D43E26961A4521817F84A7D30E309BCD50ABD52097CB897DABCDFC04D0
          343D9DBD4DA29808E0BC058B4580770B401044B747B605A83BA4D355079E5B42
          BC193D72CA7237748F85063F0875FE9F808B08B88A80B9E23CBD0DAD0B3BC309
          A7DBF2E4D9022BAEE081FD03D0F67D7679F9E50DB7FBE06CF926EDB41DB35B7E
          DFE1C5D3E76C5707F8CA84EECCD252F896CB65BB509B6376E36E22A0DF336079
          F738D67440743A102E5785C54033EBE17038E6B3D9F6CF341A8DFBA2E0B23264
          1ADE35F0E9C14716A4C42050444706522C23D6F61660A87922228A6B52226048
          7A0E57EC417114C55ED849C30F2F303B69DD97E65D0000000049454E44AE4260
          82}
      end>
    Left = 972
    Top = 572
    Bitmap = {}
  end
end
