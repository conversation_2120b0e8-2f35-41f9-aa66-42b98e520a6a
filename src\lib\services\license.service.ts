import { PrismaClient } from '@prisma/client'
import { customA<PERSON><PERSON>bet } from 'nanoid'

const prisma = new PrismaClient()

export class LicenseService {
  private static generateLicenseKey() {
    const nanoid = customAlphabet('23456789ABCDEFGHJKLMNPQRSTUVWXYZ', 12)
    return nanoid()
  }

  static async createLicense() {
    const licenseKey = this.generateLicenseKey()
    return await prisma.license.create({
      data: {
        licenseKey
      }
    })
  }

  static async activateLicense(licenseKey: string, machineId: string) {
    const license = await prisma.license.findUnique({
      where: { licenseKey }
    })
  
    if (!license) {
      throw new Error('Invalid license key')
    }
  
    if (license.status === 'ACTIVATED') {
      throw new Error('License already activated')
    }
  
    // 设置激活时间和过期时间（默认30天）
    const activatedAt = new Date()
    const expiresAt = new Date(activatedAt)
    expiresAt.setDate(expiresAt.getDate() + 30)
  
    const updatedLicense = await prisma.license.update({
      where: { id: license.id },
      data: {
        machineId,
        status: 'ACTIVATED',
        activatedAt,
        expiresAt,
        lastVerified: new Date()
      }
    })
  
    await prisma.activationLog.create({
      data: {
        licenseId: license.id,
        machineId,
        status: 'ACTIVATED',
        message: 'License activated successfully'
      }
    })
  
    return updatedLicense
  }
  
  // 修改验证方法，增加时间检查
  static async verifyLicense(licenseKey: string, machineId: string) {
    const license = await prisma.license.findUnique({
      where: { licenseKey }
    })
  
    if (!license) return false
    
    // 检查状态
    if (license.status === 'REVOKED') return false
    
    // 检查机器码
    if (license.machineId !== machineId) return false
    
    // 检查是否过期
    if (license.expiresAt && new Date() > license.expiresAt) {
      return false
    }
  
    // 更新最后验证时间
    await prisma.license.update({
      where: { id: license.id },
      data: { lastVerified: new Date() }
    })
  
    return true
  }
  
  // 添加延期方法
  static async extendLicense(licenseKey: string, days: number) {
    const license = await prisma.license.findUnique({
      where: { licenseKey }
    })
  
    if (!license) {
      throw new Error('Invalid license key')
    }
  
    const currentExpiry = license.expiresAt || new Date()
    const newExpiry = new Date(currentExpiry)
    newExpiry.setDate(newExpiry.getDate() + days)
  
    return await prisma.license.update({
      where: { id: license.id },
      data: {
        expiresAt: newExpiry
      }
    })
  }
}