unit BaseDataMainFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  Vcl.StdCtrls, RzLabel, GDIPPictureContainer,
  GDIPCustomItem, GDIPTextItem, GDIPImageTextItem, CustomItemsContainer,
  AdvVerticalPolyList, CC_JcdmFrm, CC_ColorFrm, CC_PmGgFrm, CC_XmLbFrm,
  CC_XmLbDwFrm, CC_BankModelConfigFrm, CC_ScYjxFrm, CC_ScYjxPzFrm, RzGroupBar,
  RzSplit, CC_KsbmCjTypeFrm, CC_ScXxrFrm, CC_ScSlxsFrm, CC_JcdmCjFrm,
  CC_BmCjFrm, CommonUtil, CC_CnXmLbDwFrm, CC_CnXmLbJbrFrm, CC_SzbPmGgFrm,
  AdvGlowButton, BaseDataFrm, TableGridConfigFrm;

type
  TBaseDataMainForm = class(TForm)
    RzPanel2: TRzPanel;
    RzPanel_Admin: TRzPanel;
    Btn_1: TAdvGlowButton;
    Btn_2: TAdvGlowButton;
    Timer1: TTimer;
    procedure Btn_1Click(Sender: TObject);
    procedure Btn_2Click(Sender: TObject);
    procedure Timer1Timer(Sender: TObject);
  private
    { Private declarations }
  public
    FBaseDataFrame: TBaseDataFrame;
    FTableGridConfigFrame: TTableGridConfigFrame;
    { Public declarations }
    procedure Init(InitType: integer);
    procedure TabShowByType(showtype: integer);
  end;

var
  BaseDataMainForm: TBaseDataMainForm;
  Cap: string;
  G_InitType: integer;

implementation

{$R *.dfm}

procedure TBaseDataMainForm.Btn_1Click(Sender: TObject);
begin

  TabShowByType(1);

  if (FBaseDataFrame <> nil) then
  begin
    FBaseDataFrame.Free;
    FBaseDataFrame := nil;
  end;

  if (FTableGridConfigFrame <> nil) then
  begin
    FTableGridConfigFrame.Free;
    FTableGridConfigFrame := nil;
  end;

  if (FBaseDataFrame = nil) then
  begin
    FBaseDataFrame := TBaseDataFrame.Create(self);
    FBaseDataFrame.Parent := self.RzPanel2;
    FBaseDataFrame.Align := alClient;
    FBaseDataFrame.Init(G_InitType);
  end;
end;

procedure TBaseDataMainForm.Btn_2Click(Sender: TObject);
begin
  TabShowByType(2);

  if (FBaseDataFrame <> nil) then
  begin
    FBaseDataFrame.Free;
    FBaseDataFrame := nil;
  end;

  if (FTableGridConfigFrame <> nil) then
  begin
    FTableGridConfigFrame.Free;
    FTableGridConfigFrame := nil;
  end;

  if (FTableGridConfigFrame = nil) then
  begin
    FTableGridConfigFrame := TTableGridConfigFrame.Create(self);
    FTableGridConfigFrame.Parent := self.RzPanel2;
    FTableGridConfigFrame.Align := alClient;
    FTableGridConfigFrame.Init(G_InitType);
  end;
end;

procedure TBaseDataMainForm.Init(InitType: integer);
begin
  G_InitType := InitType;
  Timer1.Enabled := true;

  self.Btn_1.Visible := false;
  if (InitType = 5) or (InitType = 7) then
  begin
    self.Btn_1.Visible := false;
  end
  else
  begin
    self.Btn_1.Visible := true;
  end;

  if (InitType = 6) or (InitType = 8) or (InitType = 9) or (InitType = 10) or
    (InitType = 11) or (InitType = 12) or (InitType = 13) then
  begin
    self.Btn_2.Visible := false;
  end
  else
  begin
    self.Btn_2.Visible := true;
  end;

end;

procedure TBaseDataMainForm.TabShowByType(showtype: integer);
begin
  self.Btn_1.Font.Color := $00FF9933;
  self.Btn_2.Font.Color := $00FF9933;

  self.Btn_1.Appearance.Color := $00F5D8CA;
  self.Btn_2.Appearance.Color := $00F5D8CA;

  self.Btn_1.Font.Style := [fsBold];
  self.Btn_2.Font.Style := [fsBold];

  Btn_1.Transparent := true;
  Btn_2.Transparent := true;

  self.Btn_1.Refresh;
  self.Btn_2.Refresh;

  if showtype = 1 then
  begin
    Btn_1.Transparent := false;
    self.Btn_1.Font.Color := clwhite;
    self.Btn_1.Font.Style := [fsBold];
    self.Btn_1.Appearance.BorderColor := clblack;
    self.Btn_1.Appearance.ColorHot := TabButtonColor;
    self.Btn_1.Appearance.ColorMirrorHot := TabButtonColor;
    self.Btn_1.Appearance.ColorHotTo := TabButtonColor;
    self.Btn_1.Appearance.ColorMirrorHotTo := TabButtonColor;
    self.Btn_1.Appearance.Color := TabButtonColor;
    self.Btn_1.Appearance.ColorMirror := TabButtonColor;
    self.Btn_1.Appearance.ColorTo := TabButtonColor;
    self.Btn_1.Appearance.ColorMirrorTo := TabButtonColor;

  end;

  if showtype = 2 then
  begin

    Btn_2.Transparent := false;
    self.Btn_2.Font.Color := clwhite;
    self.Btn_2.Font.Style := [fsBold];
    self.Btn_2.Appearance.BorderColor := clblack;
    self.Btn_2.Appearance.ColorHot := TabButtonColor;
    self.Btn_2.Appearance.ColorMirrorHot := TabButtonColor;
    self.Btn_2.Appearance.ColorHotTo := TabButtonColor;
    self.Btn_2.Appearance.ColorMirrorHotTo := TabButtonColor;
    self.Btn_2.Appearance.Color := TabButtonColor;
    self.Btn_2.Appearance.ColorMirror := TabButtonColor;
    self.Btn_2.Appearance.ColorTo := TabButtonColor;
    self.Btn_2.Appearance.ColorMirrorTo := TabButtonColor;

  end;
end;

procedure TBaseDataMainForm.Timer1Timer(Sender: TObject);
begin
  Timer1.Enabled := false;
  if (G_InitType = 6) or (G_InitType = 8) or (G_InitType = 9) or
    (G_InitType = 10) or (G_InitType = 11) or (G_InitType = 12) or
    (G_InitType = 13) then
  begin
    self.Btn_1.Click;
  end
  else
  begin
    self.Btn_2.Click;
  end;

end;

end.
