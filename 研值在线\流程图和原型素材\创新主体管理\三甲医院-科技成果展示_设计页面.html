<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院科技成果展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">三甲医院科技成果展示</h1>
            <p class="text-gray-600">全面展示医院知识产权与成果转化情况，助力科研管理与决策</p>
        </div>

        <!-- 卡片概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- 医院1卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="absolute top-4 right-4 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span class="text-blue-600 font-bold">一</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">第一医院</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">专利申请量</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                            12.5%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">专利授权量</p>
                        <p class="text-2xl font-bold text-gray-900">89</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                            8.3%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">累计拥有量</p>
                        <p class="text-2xl font-bold text-gray-900">324</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">有效专利</p>
                        <p class="text-2xl font-bold text-gray-900">287</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化次数</p>
                        <p class="text-2xl font-bold text-gray-900">23</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化金额(万)</p>
                        <p class="text-2xl font-bold text-gray-900">156.8</p>
                    </div>
                </div>
            </div>

            <!-- 医院2卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="absolute top-4 right-4 w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <span class="text-green-600 font-bold">二</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">第二医院</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">专利申请量</p>
                        <p class="text-2xl font-bold text-gray-900">132</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                            9.8%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">专利授权量</p>
                        <p class="text-2xl font-bold text-gray-900">76</p>
                        <p class="text-xs text-red-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                            2.1%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">累计拥有量</p>
                        <p class="text-2xl font-bold text-gray-900">287</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">有效专利</p>
                        <p class="text-2xl font-bold text-gray-900">245</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化次数</p>
                        <p class="text-2xl font-bold text-gray-900">18</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化金额(万)</p>
                        <p class="text-2xl font-bold text-gray-900">98.5</p>
                    </div>
                </div>
            </div>

            <!-- 医院3卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 relative">
                <div class="absolute top-4 right-4 w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <span class="text-purple-600 font-bold">三</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">第三医院</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500">专利申请量</p>
                        <p class="text-2xl font-bold text-gray-900">187</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                            15.2%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">专利授权量</p>
                        <p class="text-2xl font-bold text-gray-900">102</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
                            </svg>
                            10.7%
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">累计拥有量</p>
                        <p class="text-2xl font-bold text-gray-900">356</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">有效专利</p>
                        <p class="text-2xl font-bold text-gray-900">312</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化次数</p>
                        <p class="text-2xl font-bold text-gray-900">31</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">转化金额(万)</p>
                        <p class="text-2xl font-bold text-gray-900">215.3</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属医院</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部医院</option>
                        <option value="hospital1">第一医院</option>
                        <option value="hospital2">第二医院</option>
                        <option value="hospital3">第三医院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="medical">医疗技术</option>
                        <option value="biotech">生物技术</option>
                        <option value="pharmacy">药学</option>
                        <option value="equipment">医疗设备</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">知识产权类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="patent">发明专利</option>
                        <option value="utility">实用新型</option>
                        <option value="design">外观设计</option>
                        <option value="software">软件著作权</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申请年份</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="开始年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center">至</span>
                        <input type="number" placeholder="结束年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">转化类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="transfer">转让</option>
                        <option value="license">许可</option>
                        <option value="pledge">质押</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入成果名称或编号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 知识产权列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">知识产权列表</h2>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            新增成果
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属医院</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称/标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号/登记号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">发明专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第一医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种新型心血管支架及其制备方法</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗技术</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已转让</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">实用新型</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第二医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种可调节式骨科牵引装置</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202320123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗设备</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">已许可</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">软件著作权</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第三医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能影像诊断辅助系统V1.0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023SR1234567</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物技术</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">已质押</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 156 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">年度申请与授权量对比</h3>
                    <div class="h-80">
                        <canvas id="applicationChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">技术领域分布</h3>
                    <div class="h-80">
                        <canvas id="fieldDistributionChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">成果类型分布</h3>
                    <div class="h-80">
                        <canvas id="typeDistributionChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-3">转化金额趋势</h3>
                    <div class="h-80">
                        <canvas id="transferChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科技成果详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 左侧基础信息 -->
                    <div class="lg:col-span-1 bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-900 mb-4 border-b pb-2">基础信息</h4>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm text-gray-500">成果名称</p>
                                <p class="text-sm font-medium text-gray-900">一种新型心血管支架及其制备方法</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">成果类型</p>
                                <p class="text-sm font-medium text-gray-900">发明专利</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请号/登记号</p>
                                <p class="text-sm font-medium text-gray-900">CN202310123456.7</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">所属医院</p>
                                <p class="text-sm font-medium text-gray-900">第一医院</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">技术领域</p>
                                <p class="text-sm font-medium text-gray-900">医疗技术</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请日期</p>
                                <p class="text-sm font-medium text-gray-900">2023-01-15</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">授权日期</p>
                                <p class="text-sm font-medium text-gray-900">2023-09-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">当前状态</p>
                                <p class="text-sm font-medium text-gray-900">有效</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">转化状态</p>
                                <p class="text-sm font-medium text-gray-900">已转让</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 中间主要内容 -->
                    <div class="lg:col-span-2">
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button class="border-b-2 border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 text-sm font-medium">申请流程</button>
                                <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">授权文件</button>
                                <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">转化信息</button>
                                <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 text-sm font-medium">年费记录</button>
                            </nav>
                        </div>
                        
                        <div class="mt-4">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">申请流程时间轴</h4>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">申请提交</p>
                                        <p class="text-sm text-gray-500">2023-01-15</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">初审通过</p>
                                        <p class="text-sm text-gray-500">2023-03-20</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">实质审查</p>
                                        <p class="text-sm text-gray-500">2023-05-15</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">授权公告</p>
                                        <p class="text-sm text-gray-500">2023-09-20</p>
                                    </div>
                                </div>
                            </div>
                            
                            <h4 class="text-md font-semibold text-gray-900 mt-6 mb-2">技术摘要</h4>
                            <p class="text-sm text-gray-700">
                                本发明公开了一种新型心血管支架及其制备方法，该支架采用特殊合金材料制成，具有优异的生物相容性和机械性能。支架表面经过纳米级处理，可有效减少血栓形成和再狭窄风险。制备方法包括材料预处理、激光切割成型、表面处理等步骤，工艺简单，成本低廉，适合大规模生产。
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        打印
                    </button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下载PDF
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        function openDetailModal() {
            openModal('detailModal');
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化申请与授权量图表
            const applicationCtx = document.getElementById('applicationChart').getContext('2d');
            new Chart(applicationCtx, {
                type: 'bar',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '申请量',
                            data: [120, 145, 168, 187],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        },
                        {
                            label: '授权量',
                            data: [85, 98, 112, 102],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化技术领域分布图表
            const fieldCtx = document.getElementById('fieldDistributionChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'pie',
                data: {
                    labels: ['医疗技术', '生物技术', '药学', '医疗设备', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 初始化成果类型分布图表
            const typeCtx = document.getElementById('typeDistributionChart').getContext('2d');
            new Chart(typeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['发明专利', '实用新型', '外观设计', '软件著作权'],
                    datasets: [{
                        data: [65, 20, 5, 10],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // 初始化转化金额趋势图表
            const transferCtx = document.getElementById('transferChart').getContext('2d');
            new Chart(transferCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '转化金额(万元)',
                        data: [85, 98, 120, 215],
                        borderColor: 'rgba(139, 92, 246, 0.8)',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 为所有弹窗绑定"点击外部关闭"事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
        });
    </script>
</body>
</html>