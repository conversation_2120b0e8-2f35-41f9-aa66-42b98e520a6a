<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">专家基础信息管理业务流程</text>

  <!-- 阶段一：系统初始化与数据展示 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与数据展示</text>
  
  <!-- 节点1: 系统加载 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动加载所有专家基础信息</text>
  </g>

  <!-- 阶段二：查询筛选与交互操作 -->
  <text x="600" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询筛选与交互操作</text>

  <!-- 节点2: 条件筛选 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定查询条件实时刷新</text>
  </g>

  <!-- 节点3: 详情钻取 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">弹窗展示专家详细信息</text>
  </g>

  <!-- 节点4: 标签绑定 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标签绑定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标签管理与关系维护</text>
  </g>

  <!-- 连接线 系统加载 -> 筛选/详情/标签 -->
  <path d="M 550 200 C 450 250, 350 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 200 Q 600 260 600 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 200 C 750 250, 850 280, 900 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据导出与系统管理 -->
  <text x="600" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据导出与系统管理</text>

  <!-- 节点5: 数据导出 -->
  <g transform="translate(350, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成标准化数据文件</text>
  </g>

  <!-- 节点6: 系统维护 -->
  <g transform="translate(650, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统维护</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据同步与关系管理</text>
  </g>

  <!-- 连接线 筛选/详情/标签 -> 导出/维护 -->
  <path d="M 300 390 C 350 450, 400 480, 450 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 390 C 650 450, 700 480, 750 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 390 C 850 450, 800 480, 750 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：完整业务闭环 -->
  <text x="600" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：完整业务闭环</text>
  
  <!-- 节点7: 业务闭环 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
      <rect width="400" height="60" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专家信息管理完整业务闭环</text>
      <text x="200" y="50" text-anchor="middle" font-size="12" fill="#555">信息展示 → 条件筛选 → 详情查看 → 标签管理 → 数据导出</text>
  </g>

  <!-- 连接线 导出/维护 -> 业务闭环 -->
  <path d="M 450 590 C 500 650, 550 680, 550 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 590 C 700 650, 650 680, 650 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：业务闭环 -> 系统加载 -->
  <path d="M 400 750 C 200 750, 100 400, 100 200 C 100 150, 200 130, 500 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="770" text-anchor="middle" font-size="11" fill="#666">持续优化反馈</text>

</svg>