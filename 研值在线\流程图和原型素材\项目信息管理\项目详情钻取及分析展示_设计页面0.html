<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目详情钻取及分析展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">项目详情钻取及分析展示</h1>
            <p class="text-gray-600">全面展示项目基础信息、团队组成、产出成果及多维分析视图</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 项目基础信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            宁波市智慧港口AI调度系统研发项目
                        </h2>
                        <div class="flex space-x-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">进行中</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">重点研发</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="text-sm text-gray-500 mb-1">项目编号</div>
                            <div class="text-sm font-medium text-gray-900">NB2023-RD-015</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 mb-1">承担单位</div>
                            <div class="text-sm font-medium text-gray-900">宁波港智能科技有限公司</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 mb-1">起止日期</div>
                            <div class="text-sm font-medium text-gray-900">2023-03-01 至 2024-12-31</div>
                        </div>
                        <div>
                            <div class="text-sm text-gray-500 mb-1">预算经费</div>
                            <div class="text-sm font-medium text-gray-900">¥2,850,000.00</div>
                        </div>
                        <div class="md:col-span-2">
                            <div class="text-sm text-gray-500 mb-1">项目内容</div>
                            <div class="text-sm text-gray-700">
                                本项目旨在研发基于人工智能的港口调度系统，通过机器学习算法优化集装箱装卸流程，提升宁波港作业效率30%以上。项目包含智能调度算法开发、硬件设备改造、系统集成测试等关键任务。
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <div class="text-sm text-gray-500 mb-2">项目附件</div>
                        <div class="flex flex-wrap gap-2">
                            <button class="flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                立项书.pdf
                            </button>
                            <button class="flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                技术合同.docx
                            </button>
                            <button class="flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                中期报告.pdf
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 项目团队与合作伙伴卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            项目团队与合作伙伴
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                            查看历史团队
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <!-- 核心团队 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">核心团队</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                <div class="border border-gray-200 rounded-md p-3 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">张伟</p>
                                            <p class="text-xs text-gray-500">项目负责人 / 算法专家</p>
                                            <p class="text-xs text-gray-500 mt-1">宁波大学计算机科学与技术博士</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-md p-3 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">王芳</p>
                                            <p class="text-xs text-gray-500">硬件工程师</p>
                                            <p class="text-xs text-gray-500 mt-1">宁波工程学院自动化专业硕士</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-md p-3 hover:bg-gray-50">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">李强</p>
                                            <p class="text-xs text-gray-500">软件工程师</p>
                                            <p class="text-xs text-gray-500 mt-1">浙江大学软件工程硕士</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 合作单位 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">合作单位</h3>
                            <div class="space-y-2">
                                <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">宁波大学智能系统研究所</p>
                                        <p class="text-xs text-gray-500">算法支持 / 技术咨询</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">宁波港务集团有限公司</p>
                                        <p class="text-xs text-gray-500">业务支持 / 测试环境</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目成果卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            项目成果
                        </h2>
                        <div class="flex space-x-2">
                            <select class="text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="all">全部成果类型</option>
                                <option value="paper">论文</option>
                                <option value="patent">专利</option>
                                <option value="product">产品</option>
                                <option value="standard">标准</option>
                                <option value="service">服务</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <!-- 论文 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                论文 (3篇)
                            </h3>
                            <div class="space-y-2">
                                <div class="p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-800">基于深度学习的港口集装箱调度算法研究</a>
                                    <div class="flex flex-wrap items-center text-xs text-gray-500 mt-1">
                                        <span class="mr-2">张伟, 王芳, 李强</span>
                                        <span class="mr-2">《自动化学报》2023年第8期</span>
                                        <span>SCI收录</span>
                                    </div>
                                </div>
                                <div class="p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-800">智能港口系统中的多目标优化问题探讨</a>
                                    <div class="flex flex-wrap items-center text-xs text-gray-500 mt-1">
                                        <span class="mr-2">张伟</span>
                                        <span class="mr-2">《计算机应用研究》2023年第5期</span>
                                        <span>EI收录</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 专利 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                专利 (2项)
                            </h3>
                            <div class="space-y-2">
                                <div class="p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-800">一种基于机器学习的港口集装箱智能调度系统</a>
                                    <div class="flex flex-wrap items-center text-xs text-gray-500 mt-1">
                                        <span class="mr-2">发明专利</span>
                                        <span class="mr-2">申请号: CN202310123456.7</span>
                                        <span>2023-06-15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 产品 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <svg class="w-4 h-4 mr-1 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                产品 (1个)
                            </h3>
                            <div class="p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                <a href="#" class="text-sm font-medium text-blue-600 hover:text-blue-800">港口智能调度系统V1.0</a>
                                <div class="flex flex-wrap items-center text-xs text-gray-500 mt-1">
                                    <span class="mr-2">软件产品</span>
                                    <span class="mr-2">登记号: 浙DGY-2023-0123</span>
                                    <span>2023-09-01</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧分析区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 项目分析卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        项目分析
                    </h2>
                    
                    <div class="space-y-6">
                        <!-- 项目类型分布 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">项目类型分布</h3>
                            <div class="h-40">
                                <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                                    <span>重点研发</span>
                                    <span>45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between text-xs text-gray-500 mt-3 mb-1">
                                    <span>技术创新</span>
                                    <span>30%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 30%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between text-xs text-gray-500 mt-3 mb-1">
                                    <span>成果转化</span>
                                    <span>15%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 15%"></div>
                                </div>
                                
                                <div class="flex items-center justify-between text-xs text-gray-500 mt-3 mb-1">
                                    <span>其他</span>
                                    <span>10%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-red-500 h-2 rounded-full" style="width: 10%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 经费结构 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">经费结构 (万元)</h3>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-500">财政拨款</span>
                                <span class="text-xs font-medium">150.0</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 53%"></div>
                            </div>
                            
                            <div class="flex items-center justify-between mt-3 mb-2">
                                <span class="text-xs text-gray-500">单位自筹</span>
                                <span class="text-xs font-medium">100.0</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 35%"></div>
                            </div>
                            
                            <div class="flex items-center justify-between mt-3 mb-2">
                                <span class="text-xs text-gray-500">企业投资</span>
                                <span class="text-xs font-medium">35.0</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 12%"></div>
                            </div>
                        </div>
                        
                        <!-- 项目进展 -->
                        <div>
                            <h3 class="text-sm font-medium text-gray-700 mb-2">项目进展</h3>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-blue-600"></div>
                                    <div class="ml-2 text-xs text-gray-700">立项阶段 (2023-03)</div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-blue-600"></div>
                                    <div class="ml-2 text-xs text-gray-700">需求分析 (2023-04)</div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-blue-600"></div>
                                    <div class="ml-2 text-xs text-gray-700">算法开发 (2023-06)</div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-green-500"></div>
                                    <div class="ml-2 text-xs text-gray-700">系统集成 (当前)</div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-gray-300"></div>
                                    <div class="ml-2 text-xs text-gray-500">测试验证 (2024-06)</div>
                                </div>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-2 w-2 rounded-full bg-gray-300"></div>
                                    <div class="ml-2 text-xs text-gray-500">验收结题 (2024-12)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        操作
                    </h2>
                    
                    <div class="space-y-3">
                        <button class="w-full flex items-center justify-between px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                            <span>切换项目</span>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        
                        <button class="w-full flex items-center justify-between px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                            <span>导出分析图表</span>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                        </button>
                        
                        <button class="w-full flex items-center justify-between px-4 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">
                            <span>生成项目报告</span>
                            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>