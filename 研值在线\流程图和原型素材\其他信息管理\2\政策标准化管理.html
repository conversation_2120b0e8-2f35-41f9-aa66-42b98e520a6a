<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策标准化管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .drag-over {
            background-color: #dbeafe;
            border: 2px dashed #3b82f6;
        }
        .mapping-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(to right, #3b82f6, #10b981);
            z-index: 10;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">政策标准化管理</h1>
                    <p class="mt-2 text-sm text-gray-600">对原始政策文本进行字段映射、数据清洗与结构化拆解，转换为统一数据结构</p>
                </div>
                <div class="flex space-x-3">
                    <!-- 版本管理按钮 -->
                    <button id="versionBtn" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        版本管理
                    </button>
                    <!-- 批量导出按钮 -->
                    <button id="exportBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        批量导出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- 政策名称搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">政策名称</label>
                    <input type="text" placeholder="请输入政策名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                </div>
                <!-- 来源渠道 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">来源渠道</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                        <option value="">全部渠道</option>
                        <option value="gov">政府官网</option>
                        <option value="news">新闻媒体</option>
                        <option value="manual">手动录入</option>
                        <option value="api">API接口</option>
                    </select>
                </div>
                <!-- 标准化状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">标准化状态</label>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 border border-blue-300">未标准化</button>
                        <button class="px-3 py-1 text-sm rounded-full bg-white text-gray-700 border border-gray-300">标准化中</button>
                        <button class="px-3 py-1 text-sm rounded-full bg-white text-gray-700 border border-gray-300">已完成</button>
                    </div>
                </div>
                <!-- 创建时间 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">创建时间</label>
                    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                        <input type="date" class="flex-1 min-w-0 px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                        <span class="text-gray-500 self-center text-center sm:text-left">至</span>
                        <input type="date" class="flex-1 min-w-0 px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                    </div>
                </div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">审核状态</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">待审核</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">审核通过</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">审核驳回</span>
                        </label>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">重置</button>
                    <button class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">查询</button>
                </div>
            </div>
        </div>

        <!-- 标准化工作台区 -->
        <div class="bg-white rounded-lg shadow-md mb-8" style="height: calc(100vh - 250px); min-height: 700px;">
            <div class="p-6 border-b">
                <h2 class="text-lg font-semibold text-gray-900">标准化工作台</h2>
                <p class="text-sm text-gray-600 mt-1">当前处理：《关于支持中小企业发展的若干政策措施》</p>
            </div>
            <div class="flex" style="height: calc(100% - 100px);">
                <!-- 左栏：字段映射树 -->
                <div class="w-1/3 border-r p-6" style="max-height: 100%;">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-900">字段映射树</h3>
                        <span class="text-sm text-blue-600">完整度: 85%</span>
                    </div>
                    <!-- 内部滚动框 -->
                    <div class="border rounded-lg bg-gray-50 p-4 overflow-y-auto" style="height: calc(100% - 60px);">
                        <div class="space-y-3">
                        <!-- 标准字段项 -->
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">政策标题</span>
                                <span class="text-xs text-green-600">已映射</span>
                            </div>
                            <div class="mt-2 p-2 bg-white rounded border-dashed border-2 border-gray-300 min-h-[40px] drop-zone" data-field="title">
                                <div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded inline-block">原始字段: policy_name</div>
                            </div>
                        </div>
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">发布机构</span>
                                <span class="text-xs text-green-600">已映射</span>
                            </div>
                            <div class="mt-2 p-2 bg-white rounded border-dashed border-2 border-gray-300 min-h-[40px] drop-zone" data-field="publisher">
                                <div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded inline-block">原始字段: department</div>
                            </div>
                        </div>
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">政策内容</span>
                                <span class="text-xs text-yellow-600">未映射</span>
                            </div>
                            <div class="mt-2 p-2 bg-white rounded border-dashed border-2 border-gray-300 min-h-[40px] drop-zone" data-field="content">
                                <span class="text-xs text-gray-400">拖拽原始字段到此处</span>
                            </div>
                        </div>
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">生效时间</span>
                                <span class="text-xs text-green-600">已映射</span>
                            </div>
                            <div class="mt-2 p-2 bg-white rounded border-dashed border-2 border-gray-300 min-h-[40px] drop-zone" data-field="effective_date">
                                <div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded inline-block">原始字段: start_date</div>
                            </div>
                        </div>
                        <div class="border rounded-lg p-3 bg-gray-50">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-gray-900">适用对象</span>
                                <span class="text-xs text-yellow-600">未映射</span>
                            </div>
                            <div class="mt-2 p-2 bg-white rounded border-dashed border-2 border-gray-300 min-h-[40px] drop-zone" data-field="target">
                                <span class="text-xs text-gray-400">拖拽原始字段到此处</span>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>

                <!-- 右栏：原始政策全文与结构化预览 -->
                <div class="flex-1 flex flex-col" style="max-height: 100%;">
                    <!-- 选项卡 -->
                    <div class="border-b flex-shrink-0">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button id="originalTab" class="border-transparent text-blue-600 border-b-2 border-blue-600 py-4 px-1 text-sm font-medium">原始全文</button>
                            <button id="previewTab" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-4 px-1 text-sm font-medium">结构化预览</button>
                        </nav>
                    </div>
                    
                    <!-- 原始全文内容 -->
                    <div id="originalContent" class="flex-1 p-6" style="max-height: calc(100% - 60px);">
                        <!-- 内部滚动框 -->
                        <div class="border rounded-lg bg-gray-50 p-4 overflow-y-auto h-full">
                            <div class="prose max-w-none">
                            <h3 class="text-lg font-semibold mb-4">关于支持中小企业发展的若干政策措施</h3>
                            <div class="space-y-4 text-sm leading-relaxed">
                                <p class="draggable-field cursor-pointer hover:bg-yellow-100 p-1 rounded transition-colors" data-field="policy_name" onclick="selectText(this)">为深入贯彻落实党中央、国务院关于支持中小企业发展的决策部署，进一步优化中小企业发展环境，激发中小企业创新活力，现制定以下政策措施。</p>
                                <p class="draggable-field cursor-pointer hover:bg-yellow-100 p-1 rounded transition-colors" data-field="department" onclick="selectText(this)">发布机构：市工业和信息化局</p>
                                <p class="draggable-field cursor-pointer hover:bg-yellow-100 p-1 rounded transition-colors" data-field="start_date" onclick="selectText(this)">生效时间：2024年1月1日</p>
                                <p class="draggable-field cursor-pointer hover:bg-yellow-100 p-1 rounded transition-colors" data-field="policy_content" onclick="selectText(this)">一、加大财政支持力度。设立中小企业发展专项资金，年度预算不少于5000万元，重点支持中小企业技术创新、数字化转型、绿色发展等。对符合条件的中小企业给予最高100万元的资金补助。</p>
                                <p class="draggable-field cursor-pointer hover:bg-yellow-100 p-1 rounded transition-colors" data-field="target_audience" onclick="selectText(this)">二、适用范围。本政策适用于在本市注册的中小企业，包括小型企业和微型企业。企业规模划分按照国家统计局相关标准执行。</p>
                                <p class="selectable-text cursor-text hover:bg-blue-50 p-1 rounded transition-colors" onclick="selectText(this)">三、优化融资环境。建立中小企业融资担保体系，政府性融资担保机构对中小企业融资担保费率不超过1%。鼓励银行业金融机构加大对中小企业信贷投放，对首贷户给予利率优惠。</p>
                                <p class="selectable-text cursor-text hover:bg-blue-50 p-1 rounded transition-colors" onclick="selectText(this)">四、强化服务保障。建设中小企业公共服务平台，为中小企业提供政策咨询、技术支持、人才培训等服务。简化行政审批流程，推行"一网通办"，提高办事效率。</p>
                            </div>
                            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                                <h4 class="text-sm font-medium text-blue-900 mb-2">文本选取提示</h4>
                                <p class="text-xs text-blue-700">• 点击高亮的段落可直接拖拽到左侧字段映射区</p>
                                <p class="text-xs text-blue-700">• 选中任意文本片段后，可通过右键菜单绑定到标准字段</p>
                                <p class="text-xs text-blue-700">• 已映射的字段会在结构化预览中实时显示</p>
                            </div>
                        </div>
                        </div>
                    </div>
                    
                    <!-- 结构化预览内容 -->
                    <div id="previewContent" class="flex-1 p-6 hidden" style="max-height: calc(100% - 60px);">
                        <!-- 内部滚动框 -->
                        <div class="border rounded-lg bg-gray-50 p-4 overflow-y-auto h-full">
                            <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <h3 class="text-lg font-semibold text-gray-900">JSON 结构预览</h3>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">格式化</button>
                                    <button class="px-3 py-1 text-xs bg-gray-100 text-gray-800 rounded-full">复制</button>
                                </div>
                            </div>
                            <div class="bg-gray-900 rounded-lg p-4 text-sm font-mono text-green-400 overflow-x-auto">
                                <pre id="jsonPreview">{
  "policy_id": "POL_2024_001",
  "title": "关于支持中小企业发展的若干政策措施",
  "publisher": "市工业和信息化局",
  "effective_date": "2024-01-01",
  "content": null,
  "target": null,
  "status": "draft",
  "mapping_progress": "60%",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T14:25:00Z",
  "validation_status": {
    "field_completeness": true,
    "naming_convention": false,
    "hierarchy_consistency": true,
    "reference_integrity": false
  },
  "mapped_fields": {
    "title": {
      "source_field": "policy_name",
      "confidence": 0.95,
      "mapping_type": "direct"
    },
    "publisher": {
      "source_field": "department",
      "confidence": 0.98,
      "mapping_type": "direct"
    },
    "effective_date": {
      "source_field": "start_date",
      "confidence": 0.92,
      "mapping_type": "format_conversion"
    }
  },
  "unmapped_fields": [
    "content",
    "target"
  ]
}</pre>
                            </div>
                            <div class="grid grid-cols-2 gap-4 mt-6">
                                <div class="bg-green-50 p-4 rounded-lg">
                                    <h4 class="text-sm font-medium text-green-900 mb-2">已映射字段 (3)</h4>
                                    <div class="space-y-2">
                                        <div class="flex justify-between text-xs">
                                            <span class="text-green-700">政策标题</span>
                                            <span class="text-green-600">95% 置信度</span>
                                        </div>
                                        <div class="flex justify-between text-xs">
                                            <span class="text-green-700">发布机构</span>
                                            <span class="text-green-600">98% 置信度</span>
                                        </div>
                                        <div class="flex justify-between text-xs">
                                            <span class="text-green-700">生效时间</span>
                                            <span class="text-green-600">92% 置信度</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <h4 class="text-sm font-medium text-yellow-900 mb-2">待映射字段 (2)</h4>
                                    <div class="space-y-2">
                                        <div class="text-xs text-yellow-700">政策内容</div>
                                        <div class="text-xs text-yellow-700">适用对象</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 规则校验区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b cursor-pointer" onclick="toggleValidation()">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">规则校验</h2>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-yellow-600">发现 2 个建议</span>
                        <svg class="w-5 h-5 text-gray-400 transform transition-transform" id="validationIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="p-6" id="validationContent">
                <div class="space-y-4">
                    <!-- 数据质量校验项目 -->
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">数据格式校验</h4>
                            <p class="text-sm text-gray-600">日期格式、数值范围等符合标准</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">命名规范校验</h4>
                            <p class="text-sm text-gray-600">字段命名不符合驼峰命名规范，建议调整为 policyContent</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">数据类型校验</h4>
                            <p class="text-sm text-gray-600">所有字段数据类型匹配正确</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">业务逻辑校验</h4>
                            <p class="text-sm text-gray-600">生效时间应早于失效时间，建议补充失效时间字段</p>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">完整性建议</h4>
                            <p class="text-sm text-gray-600">建议补充政策文号、相关法规引用等扩展字段</p>
                        </div>
                    </div>
                </div>
                <div class="mt-6 flex space-x-3">
                    <button class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">保存映射</button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">重新校验</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本管理弹窗 -->
    <div id="versionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">版本管理</h3>
                <button onclick="closeVersionModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">校验状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更摘要</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-15 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">通过</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">完善适用对象字段映射</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">对比差异</button>
                                <button class="text-green-600 hover:text-green-900">设为当前</button>
                                <button class="text-red-600 hover:text-red-900">删除版本</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.2</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-14 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">调整政策内容字段结构</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">对比差异</button>
                                <button class="text-green-600 hover:text-green-900">设为当前</button>
                                <button class="text-red-600 hover:text-red-900">删除版本</button>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">v1.1</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王五</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-13 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">通过</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">初始字段映射配置</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">对比差异</button>
                                <button class="text-green-600 hover:text-green-900">设为当前</button>
                                <button class="text-red-600 hover:text-red-900">删除版本</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 批量导出弹窗 -->
    <div id="exportModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">批量导出</h3>
                <button onclick="closeExportModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">导出类型</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="exportType" value="structured" class="text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">结构化数据 (JSON)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="exportType" value="report" class="text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">校验报告 (Excel)</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="exportType" value="mapping" class="text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">映射配置 (XML)</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">导出范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none">
                        <option value="current">当前筛选结果</option>
                        <option value="all">全部已标准化政策</option>
                        <option value="selected">选中的政策条目</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">文件名称</label>
                    <input type="text" value="政策标准化数据_20240115" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button onclick="closeExportModal()" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">取消</button>
                    <button class="px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">开始导出</button>
                </div>
            </div>
            <!-- 导出历史 -->
            <div class="mt-6 border-t pt-4">
                <h4 class="text-sm font-medium text-gray-900 mb-3">导出历史</h4>
                <div class="space-y-2 max-h-32 overflow-y-auto">
                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div>
                            <span class="text-sm text-gray-900">政策标准化数据_20240114.json</span>
                            <span class="text-xs text-gray-500 ml-2">2024-01-14 15:30</span>
                        </div>
                        <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div>
                            <span class="text-sm text-gray-900">校验报告_20240113.xlsx</span>
                            <span class="text-xs text-gray-500 ml-2">2024-01-13 10:15</span>
                        </div>
                        <button class="text-blue-600 hover:text-blue-900 text-sm">下载</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 版本管理弹窗
        document.getElementById('versionBtn').addEventListener('click', function() {
            document.getElementById('versionModal').classList.remove('hidden');
        });

        function closeVersionModal() {
            document.getElementById('versionModal').classList.add('hidden');
        }

        // 批量导出弹窗
        document.getElementById('exportBtn').addEventListener('click', function() {
            document.getElementById('exportModal').classList.remove('hidden');
        });

        function closeExportModal() {
            document.getElementById('exportModal').classList.add('hidden');
        }

        // 规则校验折叠
        function toggleValidation() {
            const content = document.getElementById('validationContent');
            const icon = document.getElementById('validationIcon');
            
            if (content.style.display === 'none') {
                content.style.display = 'block';
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.style.display = 'none';
                icon.style.transform = 'rotate(-90deg)';
            }
        }

        // 拖拽功能
        let draggedElement = null;

        // 为可拖拽字段添加事件监听
        document.querySelectorAll('.draggable-field').forEach(field => {
            field.addEventListener('dragstart', function(e) {
                draggedElement = this;
                this.style.opacity = '0.5';
            });

            field.addEventListener('dragend', function(e) {
                this.style.opacity = '1';
                draggedElement = null;
            });

            field.setAttribute('draggable', 'true');
        });

        // 为放置区域添加事件监听
        document.querySelectorAll('.drop-zone').forEach(zone => {
            zone.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('drag-over');
            });

            zone.addEventListener('dragleave', function(e) {
                this.classList.remove('drag-over');
            });

            zone.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                
                if (draggedElement) {
                    const fieldName = draggedElement.getAttribute('data-field');
                    const fieldText = draggedElement.textContent.substring(0, 50) + '...';
                    
                    this.innerHTML = `<div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded inline-block">原始字段: ${fieldName}</div>`;
                    
                    // 更新映射状态
                    const parentCard = this.closest('.border');
                    const statusSpan = parentCard.querySelector('.text-xs');
                    statusSpan.textContent = '已映射';
                    statusSpan.className = 'text-xs text-green-600';
                }
            });
        });

        // 状态按钮切换
        document.querySelectorAll('.bg-blue-100, .bg-white').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有按钮的激活状态
                this.parentElement.querySelectorAll('button').forEach(btn => {
                    btn.className = 'px-3 py-1 text-sm rounded-full bg-white text-gray-700 border border-gray-300';
                });
                
                // 激活当前按钮
                this.className = 'px-3 py-1 text-sm rounded-full bg-blue-100 text-blue-800 border border-blue-300';
            });
        });

        // 选项卡切换
        document.getElementById('originalTab').addEventListener('click', function() {
            // 切换选项卡样式
            this.className = 'border-transparent text-blue-600 border-b-2 border-blue-600 py-4 px-1 text-sm font-medium';
            document.getElementById('previewTab').className = 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-4 px-1 text-sm font-medium';
            
            // 切换内容显示
            document.getElementById('originalContent').classList.remove('hidden');
            document.getElementById('previewContent').classList.add('hidden');
        });
        
        document.getElementById('previewTab').addEventListener('click', function() {
            // 切换选项卡样式
            this.className = 'border-transparent text-blue-600 border-b-2 border-blue-600 py-4 px-1 text-sm font-medium';
            document.getElementById('originalTab').className = 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-4 px-1 text-sm font-medium';
            
            // 切换内容显示
            document.getElementById('originalContent').classList.add('hidden');
            document.getElementById('previewContent').classList.remove('hidden');
            
            // 更新JSON预览
            updateJsonPreview();
        });
        
        // 文本选取功能
        function selectText(element) {
            // 高亮选中的文本
            document.querySelectorAll('.selected-text').forEach(el => {
                el.classList.remove('selected-text', 'bg-blue-200');
            });
            
            element.classList.add('selected-text', 'bg-blue-200');
            
            // 显示绑定提示
            showBindingTooltip(element);
        }
        
        // 显示绑定提示
        function showBindingTooltip(element) {
            // 移除现有提示
            const existingTooltip = document.querySelector('.binding-tooltip');
            if (existingTooltip) {
                existingTooltip.remove();
            }
            
            // 创建新提示
            const tooltip = document.createElement('div');
            tooltip.className = 'binding-tooltip absolute bg-white border border-gray-300 rounded-lg shadow-lg p-3 z-50';
            tooltip.innerHTML = `
                <div class="text-sm font-medium text-gray-900 mb-2">绑定到标准字段</div>
                <div class="space-y-1">
                    <button class="block w-full text-left px-2 py-1 text-xs hover:bg-blue-50 rounded" onclick="bindToField('content')">政策内容</button>
                    <button class="block w-full text-left px-2 py-1 text-xs hover:bg-blue-50 rounded" onclick="bindToField('target')">适用对象</button>
                    <button class="block w-full text-left px-2 py-1 text-xs hover:bg-blue-50 rounded" onclick="bindToField('custom')">自定义字段</button>
                </div>
            `;
            
            // 定位提示框
            const rect = element.getBoundingClientRect();
            tooltip.style.left = (rect.right + 10) + 'px';
            tooltip.style.top = rect.top + 'px';
            
            document.body.appendChild(tooltip);
            
            // 3秒后自动隐藏
            setTimeout(() => {
                if (tooltip.parentNode) {
                    tooltip.remove();
                }
            }, 3000);
        }
        
        // 绑定到字段
        function bindToField(fieldType) {
            const selectedElement = document.querySelector('.selected-text');
            if (!selectedElement) return;
            
            const text = selectedElement.textContent;
            const dropZone = document.querySelector(`[data-field="${fieldType}"]`);
            
            if (dropZone) {
                dropZone.innerHTML = `<div class="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded inline-block">已绑定: ${text.substring(0, 30)}...</div>`;
                
                // 更新映射状态
                const parentCard = dropZone.closest('.border');
                const statusSpan = parentCard.querySelector('.text-xs');
                statusSpan.textContent = '已映射';
                statusSpan.className = 'text-xs text-green-600';
                
                // 更新完整度
                updateMappingProgress();
            }
            
            // 移除选中状态和提示
            selectedElement.classList.remove('selected-text', 'bg-blue-200');
            const tooltip = document.querySelector('.binding-tooltip');
            if (tooltip) tooltip.remove();
        }
        
        // 更新映射完整度
        function updateMappingProgress() {
            const totalFields = document.querySelectorAll('.drop-zone').length;
            const mappedFields = document.querySelectorAll('.text-green-600').length;
            const progress = Math.round((mappedFields / totalFields) * 100);
            
            const progressElement = document.querySelector('.text-blue-600');
            if (progressElement && progressElement.textContent.includes('完整度')) {
                progressElement.textContent = `完整度: ${progress}%`;
            }
        }
        
        // 更新JSON预览
        function updateJsonPreview() {
            const mappedData = {
                policy_id: "POL_2024_001",
                title: "关于支持中小企业发展的若干政策措施",
                publisher: "市工业和信息化局",
                effective_date: "2024-01-01",
                content: null,
                target: null,
                status: "draft",
                mapping_progress: "60%",
                created_at: "2024-01-15T10:30:00Z",
                updated_at: new Date().toISOString(),
                validation_status: {
                    field_completeness: true,
                    naming_convention: false,
                    hierarchy_consistency: true,
                    reference_integrity: false
                },
                mapped_fields: {},
                unmapped_fields: []
            };
            
            // 检查已映射字段
            document.querySelectorAll('.drop-zone').forEach(zone => {
                const fieldName = zone.getAttribute('data-field');
                const isMapping = zone.querySelector('.text-blue-600');
                
                if (isMapping) {
                    mappedData.mapped_fields[fieldName] = {
                        source_field: fieldName,
                        confidence: 0.95,
                        mapping_type: "direct"
                    };
                } else {
                    mappedData.unmapped_fields.push(fieldName);
                }
            });
            
            // 更新JSON显示
            const jsonPreview = document.getElementById('jsonPreview');
            if (jsonPreview) {
                jsonPreview.textContent = JSON.stringify(mappedData, null, 2);
            }
        }

        // 点击外部关闭弹窗
        window.addEventListener('click', function(e) {
            if (e.target.classList.contains('bg-opacity-50')) {
                closeVersionModal();
                closeExportModal();
            }
        });
    </script>
</body>
</html>