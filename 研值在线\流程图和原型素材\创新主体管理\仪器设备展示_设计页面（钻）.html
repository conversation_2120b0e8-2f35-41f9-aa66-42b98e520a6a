<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器设备展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">仪器设备展示</h1>
            <p class="text-gray-600">集中管理科研仪器设备信息，提供设备分类、使用情况及共享服务数据</p>
        </div>

        <!-- 统计卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <!-- 自有设备卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">自有设备总数</p>
                        <p class="text-2xl font-bold text-blue-600 mt-2">286</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-gray-500">
                    <span class="text-green-500 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        12.5%
                    </span>
                    <span class="ml-2">同比上月</span>
                </div>
            </div>

            <!-- 租借设备卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">租借设备总数</p>
                        <p class="text-2xl font-bold text-indigo-600 mt-2">124</p>
                    </div>
                    <div class="bg-indigo-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-gray-500">
                    <span class="text-green-500 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        8.3%
                    </span>
                    <span class="ml-2">同比上月</span>
                </div>
            </div>

            <!-- 合作设备卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">合作共享设备</p>
                        <p class="text-2xl font-bold text-purple-600 mt-2">78</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-gray-500">
                    <span class="text-red-500 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                        </svg>
                        2.1%
                    </span>
                    <span class="ml-2">同比上月</span>
                </div>
            </div>

            <!-- 使用时长卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">近一年使用时长</p>
                        <p class="text-2xl font-bold text-green-600 mt-2">3,568</p>
                        <p class="text-sm text-gray-500 mt-1">小时</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-4 flex items-center text-sm text-gray-500">
                    <span class="text-green-500 flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        15.7%
                    </span>
                    <span class="ml-2">同比上月</span>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                条件筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- 设备来源 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设备来源</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            自有
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            租借
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            合作共享
                        </label>
                    </div>
                </div>

                <!-- 设备分类 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设备分类</label>
                    <div class="relative">
                        <button id="categoryButton" onclick="toggleCategoryTree()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                            <span id="selectedCategory">请选择设备分类</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="categoryTree" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                            <ul class="space-y-2">
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            分析仪器
                                        </label>
                                    </div>
                                    <ul class="pl-6 mt-2 space-y-2 hidden">
                                        <li>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                光谱仪器
                                            </label>
                                        </li>
                                        <li>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                                色谱仪器
                                            </label>
                                        </li>
                                    </ul>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            实验设备
                                        </label>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            检测设备
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 技术领域 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <div class="relative">
                        <button id="techButton" onclick="toggleTechTree()" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-left flex justify-between items-center">
                            <span id="selectedTech">请选择技术领域</span>
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="techTree" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden max-h-60 overflow-y-auto">
                            <ul class="space-y-2">
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            新材料
                                        </label>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            智能制造
                                        </label>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-gray-500 cursor-pointer" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                        <label class="ml-2 flex items-center">
                                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                            生物医药
                                        </label>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 购置年份 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">购置年份</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" placeholder="起始年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="2000" max="2024">
                        <span class="text-gray-500">至</span>
                        <input type="number" placeholder="结束年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="2000" max="2024">
                    </div>
                </div>

                <!-- 设备状态 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设备状态</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            在用
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            停用
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            维护中
                        </label>
                    </div>
                </div>

                <!-- 关键字搜索 -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入设备名称或型号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex space-x-3 mt-4">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置
                </button>
            </div>
        </div>

        <!-- 设备列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        设备列表
                    </h2>
                    <div class="flex space-x-2">
                        <div class="relative" id="exportDropdown">
                            <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4 4V4"></path>
                                </svg>
                                导出
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                </div>
                            </div>
                        </div>
                        <button onclick="openImportModal()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导入
                        </button>
                        <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增设备
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备图片</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">型号规格</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">来源</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- 设备1 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">高效液相色谱仪</div>
                                <div class="text-sm text-gray-500">宁波市XX科学仪器有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">HPLC-2024</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">分析仪器/色谱仪器</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">自有</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('dev1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="editDevice('dev1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="toggleStatus('dev1')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                </div>
                            </td>
                        </tr>

                        <!-- 设备2 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">原子吸收光谱仪</div>
                                <div class="text-sm text-gray-500">宁波XX检测技术有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">AAS-3000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">分析仪器/光谱仪器</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800">租借</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">在用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('dev2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="editDevice('dev2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="toggleStatus('dev2')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                </div>
                            </td>
                        </tr>

                        <!-- 设备3 -->
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">电子显微镜</div>
                                <div class="text-sm text-gray-500">宁波XX大学材料学院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">EM-5000</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">检测设备</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">合作共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">维护中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('dev3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    <button onclick="editDevice('dev3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="toggleStatus('dev3')" class="text-green-600 hover:text-green-900">启用</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">286</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 可视化分析区 -->
        <div class="bg-white rounded-lg shadow-md mt-6 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                设备分析
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 分类分布图 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">设备分类分布</h3>
                    <div class="h-64 flex items-center justify-center">
                        <svg class="w-full h-full" viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#3b82f6" stroke-width="20" stroke-dasharray="40 60" stroke-dashoffset="0"></circle>
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#6366f1" stroke-width="20" stroke-dasharray="30 70" stroke-dashoffset="-40"></circle>
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#8b5cf6" stroke-width="20" stroke-dasharray="20 80" stroke-dashoffset="-70"></circle>
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#a855f7" stroke-width="20" stroke-dasharray="10 90" stroke-dashoffset="-90"></circle>
                            <text x="50" y="50" text-anchor="middle" dominant-baseline="middle" font-size="10" fill="#4b5563">分类分布图</text>
                        </svg>
                    </div>
                    <div class="flex justify-center space-x-4 mt-4">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-blue-500 mr-1"></div>
                            <span class="text-xs text-gray-600">分析仪器</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-indigo-500 mr-1"></div>
                            <span class="text-xs text-gray-600">实验设备</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-purple-500 mr-1"></div>
                            <span class="text-xs text-gray-600">检测设备</span>
                        </div>
                    </div>
                </div>

                <!-- 来源分布图 -->
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">设备来源分布</h3>
                    <div class="h-64 flex items-center justify-center">
                        <svg class="w-full h-full" viewBox="0 0 100 100">
                            <rect x="10" y="60" width="20" height="40" fill="#3b82f6"></rect>
                            <rect x="40" y="30" width="20" height="70" fill="#6366f1"></rect>
                            <rect x="70" y="50" width="20" height="50" fill="#8b5cf6"></rect>
                            <text x="20" y="55" text-anchor="middle" font-size="6" fill="#4b5563">自有</text>
                            <text x="50" y="25" text-anchor="middle" font-size="6" fill="#4b5563">租借</text>
                            <text x="80" y="45" text-anchor="middle" font-size="6" fill="#4b5563">共享</text>
                            <text x="50" y="95" text-anchor="middle" font-size="8" fill="#4b5563">来源分布图</text>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详情侧栏 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full max-w-md bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <!-- 侧栏头部 -->
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">设备详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- 侧栏内容 -->
            <div class="flex-1 overflow-y-auto p-6">
                <!-- 设备图片 -->
                <div class="w-full h-48 bg-gray-200 rounded-md mb-4 flex items-center justify-center">
                    <svg class="w-24 h-24 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                    </svg>
                </div>

                <!-- 基本信息 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">基本信息</h4>
                    <div class="space-y-2">
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">设备名称</span>
                            <span class="text-sm text-gray-900">高效液相色谱仪</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">型号规格</span>
                            <span class="text-sm text-gray-900">HPLC-2024</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">生产厂商</span>
                            <span class="text-sm text-gray-900">宁波市XX科学仪器有限公司</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">设备分类</span>
                            <span class="text-sm text-gray-900">分析仪器/色谱仪器</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">来源方式</span>
                            <span class="text-sm text-gray-900">自有</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">购置日期</span>
                            <span class="text-sm text-gray-900">2023-05-15</span>
                        </div>
                        <div class="flex">
                            <span class="w-24 text-sm text-gray-500">设备状态</span>
                            <span class="text-sm text-gray-900">在用</span>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="switchTab('tech')" id="techTab" class="border-b-2 border-blue-500 text-blue-600 px-1 py-2 text-sm font-medium">技术指标</button>
                        <button onclick="switchTab('service')" id="serviceTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">服务内容</button>
                        <button onclick="switchTab('record')" id="recordTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">使用记录</button>
                    </nav>
                </div>

                <!-- 技术指标内容 -->
                <div id="techContent" class="tab-content">
                    <div class="prose prose-sm max-w-none">
                        <h5>主要技术参数</h5>
                        <ul>
                            <li>波长范围：190-800nm</li>
                            <li>波长准确度：±1nm</li>
                            <li>波长重复性：≤0.1nm</li>
                            <li>基线噪声：≤±0.5×10-5AU</li>
                            <li>基线漂移：≤1×10-4AU/h</li>
                            <li>流量范围：0.001-10.000mL/min</li>
                            <li>流量精度：≤0.1%RSD</li>
                        </ul>
                        <h5 class="mt-4">应用范围</h5>
                        <p>适用于医药、食品、环境、化工等领域中化合物的定性定量分析。</p>
                    </div>
                </div>

                <!-- 服务内容 (默认隐藏) -->
                <div id="serviceContent" class="tab-content hidden">
                    <div class="prose prose-sm max-w-none">
                        <h5>服务项目</h5>
                        <ul>
                            <li>样品测试服务</li>
                            <li>方法开发与验证</li>
                            <li>技术咨询与培训</li>
                            <li>仪器租赁</li>
                        </ul>
                        <h5 class="mt-4">收费标准</h5>
                        <table>
                            <thead>
                                <tr>
                                    <th>服务项目</th>
                                    <th>校内(元/样)</th>
                                    <th>校外(元/样)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>常规测试</td>
                                    <td>200</td>
                                    <td>400</td>
                                </tr>
                                <tr>
                                    <td>方法开发</td>
                                    <td>800</td>
                                    <td>1500</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 使用记录 (默认隐藏) -->
                <div id="recordContent" class="tab-content hidden">
                    <div class="space-y-4">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-2">近期使用记录</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="flex justify-between text-xs text-gray-500">
                                    <span>2024-03-15</span>
                                    <span>宁波XX制药有限公司</span>
                                    <span>4小时</span>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md mt-2">
                                <div class="flex justify-between text-xs text-gray-500">
                                    <span>2024-03-10</span>
                                    <span>宁波XX大学化学系</span>
                                    <span>2小时</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-2">维护记录</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="flex justify-between text-xs text-gray-500">
                                    <span>2024-02-28</span>
                                    <span>定期维护</span>
                                    <span>张伟</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 侧栏底部 -->
            <div class="p-4 border-t border-gray-200 bg-gray-50 flex justify-between">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出PDF
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                    </svg>
                    分享链接
                </button>
            </div>
        </div>
    </div>

    <!-- 新增设备模态框 -->
    <div id="addModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">新增设备</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">设备名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入设备名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">型号规格 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入型号规格" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">生产厂商 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入生产厂商" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">设备分类 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择设备分类</option>
                                    <option value="analysis">分析仪器</option>
                                    <option value="experiment">实验设备</option>
                                    <option value="test">检测设备</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">来源方式 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择来源方式</option>
                                    <option value="own">自有</option>
                                    <option value="rent">租借</option>
                                    <option value="share">合作共享</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">购置日期 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 设备图片上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">设备图片</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-4">
                                    <label for="device-image" class="cursor-pointer">
                                        <span class="block text-sm font-medium text-gray-700">点击上传图片</span>
                                        <span class="block text-xs text-gray-500">支持 JPG, PNG 格式，最大2MB</span>
                                    </label>
                                    <input id="device-image" type="file" class="sr-only" accept="image/jpeg,image/png">
                                </div>
                            </div>
                        </div>

                        <!-- 技术指标 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">技术指标</label>
                            <textarea rows="4" placeholder="请输入设备技术指标" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeAddModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入设备</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                下载导入模板
                            </a>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="block text-sm font-medium text-gray-700">点击上传文件</span>
                                    <span class="block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                </label>
                                <input id="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div id="uploadProgress" class="hidden">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span class="text-sm font-medium text-gray-700">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                    <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                        <li>第3行：设备名称为必填项</li>
                                        <li>第5行：购置日期格式不正确</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeImportModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        取消
                    </button>
                    <button id="importButton" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设备分类树形选择器
        function toggleCategoryTree() {
            const tree = document.getElementById('categoryTree');
            tree.classList.toggle('hidden');
        }
        
        // 技术领域树形选择器
        function toggleTechTree() {
            const tree = document.getElementById('techTree');
            tree.classList.toggle('hidden');
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 查看设备详情
        function viewDetail(deviceId) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
            console.log('查看设备:', deviceId);
        }
        
        // 关闭详情侧栏
        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }
        
        // 编辑设备
        function editDevice(deviceId) {
            openAddModal();
            console.log('编辑设备:', deviceId);
        }
        
        // 切换设备状态
        function toggleStatus(deviceId) {
            if (confirm('确定要更改此设备的状态吗？')) {
                console.log('切换设备状态:', deviceId);
            }
        }
        
        // 打开新增设备模态框
        function openAddModal() {
            document.getElementById('addModal').classList.remove('hidden');
        }
        
        // 关闭新增设备模态框
        function closeAddModal() {
            document.getElementById('addModal').classList.add('hidden');
        }
        
        // 打开批量导入模态框
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
        }
        
        // 关闭批量导入模态框
        function closeImportModal() {
            document.getElementById('importModal').classList.add('hidden');
        }
        
        // 切换标签页
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // 取消所有标签的激活状态
            document.querySelectorAll('[id$="Tab"]').forEach(tab => {
                tab.classList.remove('border-blue-500', 'text-blue-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的内容
            document.getElementById(tabName + 'Content').classList.remove('hidden');
            
            // 激活选中的标签
            document.getElementById(tabName + 'Tab').classList.remove('border-transparent', 'text-gray-500');
            document.getElementById(tabName + 'Tab').classList.add('border-blue-500', 'text-blue-600');
        }
        
        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const progressBar = document.querySelector('#uploadProgress .bg-blue-600');
                const progressText = document.querySelector('#uploadProgress span:last-child');
                const importButton = document.getElementById('importButton');
                
                document.getElementById('uploadProgress').classList.remove('hidden');
                importButton.disabled = true;
                importButton.classList.add('opacity-50', 'cursor-not-allowed');
                
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    progressBar.style.width = progress + '%';
                    progressText.textContent = progress + '%';
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('validationResult').classList.remove('hidden');
                            importButton.disabled = false;
                            importButton.classList.remove('opacity-50', 'cursor-not-allowed');
                        }, 500);
                    }
                }, 100);
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 设备分类树形选择器
            const categoryButton = document.getElementById('categoryButton');
            const categoryTree = document.getElementById('categoryTree');
            if (!categoryButton.contains(e.target) && !categoryTree.contains(e.target) && !categoryTree.classList.contains('hidden')) {
                categoryTree.classList.add('hidden');
            }
            
            // 技术领域树形选择器
            const techButton = document.getElementById('techButton');
            const techTree = document.getElementById('techTree');
            if (!techButton.contains(e.target) && !techTree.contains(e.target) && !techTree.classList.contains('hidden')) {
                techTree.classList.add('hidden');
            }
            
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
            
            // 点击模态框外部关闭
            const addModal = document.getElementById('addModal');
            if (addModal && !addModal.contains(e.target) && e.target === addModal) {
                closeAddModal();
            }
            
            const importModal = document.getElementById('importModal');
            if (importModal && !importModal.contains(e.target) && e.target === importModal) {
                closeImportModal();
            }
        });
    </script>
</body>
</html>