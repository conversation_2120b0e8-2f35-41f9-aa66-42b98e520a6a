<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">创新主体科研码数据处理流程</text>

  <!-- 阶段一：多方数据汇集 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：多方数据汇集 (Multi-source Data Collection)</text>
  
  <!-- 节点1: 科技人才数据 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科技人才数据</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">研究人员、专家库</text>
  </g>

  <!-- 节点2: 项目数据 -->
  <g transform="translate(320, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目数据</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">科研项目、资助信息</text>
  </g>

  <!-- 节点3: 设备数据 -->
  <g transform="translate(540, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">仪器设备数据</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">实验设备、平台资源</text>
  </g>

  <!-- 节点4: 成果数据 -->
  <g transform="translate(760, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科技成果数据</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">论文、专利、技术</text>
  </g>

  <!-- 节点5: 外部数据源 -->
  <g transform="translate(980, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部数据源</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">政府、机构、企业</text>
  </g>

  <!-- 阶段二：数据仓库资源迭代升级 -->
  <text x="700" y="290" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据仓库资源迭代升级 (Data Warehouse Iteration)</text>

  <!-- 节点6: 数据仓库 -->
  <g transform="translate(300, 330)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="35" text-anchor="middle" font-size="16" font-weight="600" fill="#333">现有数据仓库</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">历史数据积累与存储</text>
  </g>

  <!-- 节点7: 数据处理引擎 -->
  <g transform="translate(650, 330)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="35" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能处理引擎</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">清洗、整合、标准化</text>
  </g>

  <!-- 连接线 阶段一 -> 数据仓库 -->
  <path d="M 190 200 C 190 250, 350 280, 400 330" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 410 200 C 410 250, 420 280, 425 330" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 630 200 C 630 250, 500 280, 450 330" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 200 C 850 250, 600 280, 500 330" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1070 200 C 1070 250, 700 280, 550 330" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据仓库 -> 处理引擎 -->
  <path d="M 550 370 Q 600 370 650 370" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：要素数据形成 -->
  <text x="700" y="500" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：要素数据形成 (Core Data Elements Generation)</text>

  <!-- 节点8: 科研码核心要素 -->
  <g transform="translate(450, 540)" filter="url(#soft-shadow)">
    <rect width="400" height="100" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">创新主体科研码核心要素</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">
      <tspan dx="-120">科技人才</tspan>
      <tspan dx="40">项目信息</tspan>
      <tspan dx="40">仪器设备</tspan>
      <tspan dx="40">科技成果</tspan>
    </text>
    <text x="200" y="80" text-anchor="middle" font-size="12" fill="#555">结构化、关联化的要素数据集合</text>
  </g>

  <!-- 连接线 处理引擎 -> 核心要素 -->
  <path d="M 775 410 C 775 450, 700 500, 650 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：最终成果 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：最终成果 (Final Output)</text>
  
  <!-- 节点9: 场景化规范化要素数据 -->
  <g transform="translate(350, 760)" filter="url(#soft-shadow)">
      <rect width="500" height="90" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="250" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">场景化、规范化的要素数据</text>
      <text x="250" y="55" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-150">标准化数据格式</tspan>
        <tspan dx="60">业务场景适配</tspan>
        <tspan dx="60">质量保障体系</tspan>
      </text>
      <text x="250" y="80" text-anchor="middle" font-size="12" fill="#555">支持科研管理、决策分析、服务创新等多元化应用场景</text>
  </g>

  <!-- 连接线 核心要素 -> 最终成果 -->
  <path d="M 650 640 C 650 680, 600 720, 600 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据流向标注 -->
  <text x="250" y="240" text-anchor="middle" font-size="11" fill="#666">数据汇集</text>
  <text x="575" y="380" text-anchor="middle" font-size="11" fill="#666">迭代升级</text>
  <text x="720" y="480" text-anchor="middle" font-size="11" fill="#666">要素提取</text>
  <text x="620" y="700" text-anchor="middle" font-size="11" fill="#666">场景化应用</text>

</svg> 