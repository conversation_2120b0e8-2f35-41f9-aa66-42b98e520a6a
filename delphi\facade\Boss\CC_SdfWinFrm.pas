unit CC_SdfWinFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  CC_SdfReportFrm;

type
  TCC_SdfWinForm = class(TForm)
    RzPanel1: TRzPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_SdfReportFrame: TCC_SdfReportFrame;
  end;

var
  CC_SdfWinForm: TCC_SdfWinForm;

implementation

{$R *.dfm}

procedure TCC_SdfWinForm.FormShow(Sender: TObject);
begin
  if (FCC_SdfReportFrame <> nil) then
  begin
    FCC_SdfReportFrame.Free;
    FCC_SdfReportFrame := nil;
  end;

  if (FCC_SdfReportFrame = nil) then
  begin
    FCC_SdfReportFrame := TCC_SdfReportFrame.Create(self);
    FCC_SdfReportFrame.Parent := self.RzPanel1;
    FCC_SdfReportFrame.Align := alClient;
    FCC_SdfReportFrame.Init();
  end;
end;

end.
