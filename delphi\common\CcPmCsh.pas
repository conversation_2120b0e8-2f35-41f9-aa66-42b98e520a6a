unit CcPmCsh;

interface
uses
  Classes;

type
  TCcPmCsh = class
  private
    FPmchsid: integer;
    FPmdzid: integer;
    FNd: integer;
    FKsbm: string;
    FPb_sb_a: Double;
    FPb_zzf_a: Double;
    FPb_zzsh_a: Double;
    FPb_rsf_a: Double;
    FPb_sb_b: Double;
    PFb_zzf_b: Double;
    FPb_zzsh_b: Double;
    FPb_rsf_b: Double;
    FPb_sb_c: Double;
    FPb_zzf_c: Double;
    FPb_zzsh_c: Double;
    FPb_rsf_c: Double;
    FPb_sb_d: Double;
    FPb_zzf_d: Double;
    FPb_zzsh_d: Double;
    FPb_rsf_d: Double;
    FPb_sb_e: Double;
    FPb_zzf_e: Double;
    FPb_zzsh_e: Double;
    FPb_rsf_e: Double;
    FHjl_pbj: Double;
    FHjl_hjjgf: Double;
    FRzd_dj_a: Double;
    FRzd_dj_b: Double;
    FGtb_pbj: Double;
    FLw_pbj: Double;
    FSbdj: Double;
    FDpdj: Double;
    FNkdj: Double;
    FNhcdj: Double;
    FXdj: Double;
    FLldj: Double;
    FJddj: Double;
    FTxmdj: Double;
    FCzdj: Double;
    FCbdj: Double;
    FZxdj: Double;
    FFgddj: Double;
    FQtdj_a: Double;
    FQtdj_b: Double;
    FGj: Double;
    FMjdj: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Pmchsid: integer read FPmchsid write FPmchsid;
    property Pmdzid: integer read FPmdzid write FPmdzid;
    property Nd: integer read FNd write FNd;
    property Ksbm: string read FKsbm write FKsbm;
    property Pb_sb_a: double read FPb_sb_a write FPb_sb_a;
    property Pb_zzf_a: double read FPb_zzf_a write FPb_zzf_a;
    property Pb_zzsh_a: double read FPb_zzsh_a write FPb_zzsh_a;
    property Pb_rsf_a: double read FPb_rsf_a write FPb_rsf_a;
    property Pb_sb_b: double read FPb_sb_b write FPb_sb_b;
    property Pb_zzf_b: double read PFb_zzf_b write PFb_zzf_b;
    property Pb_zzsh_b: double read FPb_zzsh_b write FPb_zzsh_b;
    property Pb_rsf_b: double read FPb_rsf_b write FPb_rsf_b;
    property Pb_sb_c: double read FPb_sb_c write FPb_sb_c;
    property Pb_zzf_c: double read FPb_zzf_c write FPb_zzf_c;
    property Pb_zzsh_c: double read FPb_zzsh_c write FPb_zzsh_c;
    property Pb_rsf_c: double read FPb_rsf_c write FPb_rsf_c;
    property Pb_sb_d: double read FPb_sb_d write FPb_sb_d;
    property Pb_zzf_d: double read FPb_zzf_d write FPb_zzf_d;
    property Pb_zzsh_d: double read FPb_zzsh_d write FPb_zzsh_d;
    property Pb_rsf_d: double read FPb_rsf_d write FPb_rsf_d;
    property Pb_sb_e: double read FPb_sb_e write FPb_sb_e;
    property Pb_zzf_e: double read FPb_zzf_e write FPb_zzf_e;
    property Pb_zzsh_e: double read FPb_zzsh_e write FPb_zzsh_e;
    property Pb_rsf_e: double read FPb_rsf_e write FPb_rsf_e;
    property Hjl_pbj: double read FHjl_pbj write FHjl_pbj;
    property Hjl_hjjgf: double read FHjl_hjjgf write FHjl_hjjgf;
    property Rzd_dj_a: double read FRzd_dj_a write FRzd_dj_a;
    property Rzd_dj_b: double read FRzd_dj_b write FRzd_dj_b;
    property Gtb_pbj: double read FGtb_pbj write FGtb_pbj;
    property Lw_pbj: double read FLw_pbj write FLw_pbj;
    property Sbdj: double read FSbdj write FSbdj;
    property Dpdj: double read FDpdj write FDpdj;
    property Nkdj: double read FNkdj write FNkdj;
    property Nhcdj: double read FNhcdj write FNhcdj;
    property Xdj: double read FXdj write FXdj;
    property Lldj: double read FLldj write FLldj;
    property Jddj: double read FJddj write FJddj;
    property Txmdj: double read FTxmdj write FTxmdj;
    property Czdj: double read FCzdj write FCzdj;
    property Cbdj: double read FCbdj write FCbdj;
    property Zxdj: double read FZxdj write FZxdj;
    property Fgddj: double read FFgddj write FFgddj;
    property Qtdj_a: double read FQtdj_a write FQtdj_a;
    property Qtdj_b: double read FQtdj_b write FQtdj_b;
    property Gj: double read FGj write FGj;
    property Mjdj: double read FMjdj write FMjdj;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.

