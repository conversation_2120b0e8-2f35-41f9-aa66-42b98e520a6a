<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甬知AI智能问答 - 知识产权专业咨询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        
        /* 打字机效果 */
        .typing-animation::after {
            content: '|';
            animation: blink 1s linear infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }
        
        /* 消息动画 */
        .message-slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 引用链接样式 */
        .reference-link {
            border-left: 3px solid #3b82f6;
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen">
    <!-- 主容器 -->
    <div class="max-w-2xl mx-auto h-screen flex flex-col bg-white shadow-xl">
        <!-- 顶部导航栏 -->
        <header class="bg-gradient-to-r from-blue-600 to-indigo-600 px-4 py-3 shadow-md">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <!-- AI机器人图标 -->
                    <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-white font-semibold text-lg">甬知AI</h1>
                        <p class="text-blue-100 text-xs">知识产权智能咨询助手</p>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <!-- 在线状态 -->
                    <div class="flex items-center space-x-1">
                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span class="text-blue-100 text-xs">在线</span>
                    </div>
                    <!-- 更多选项 -->
                    <button onclick="toggleMenu()" class="text-white p-1 hover:bg-blue-500 rounded">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </header>

        <!-- 聊天消息区域 -->
        <div id="chatContainer" class="flex-1 overflow-y-auto custom-scrollbar px-4 py-4 space-y-4">
            <!-- 欢迎消息 -->
            <div class="message-slide-in">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                        <p class="text-gray-800 text-sm">您好！欢迎使用甬知AI智能问答系统。我可以为您提供知识产权政策、申请流程、常见问题等专业咨询服务。</p>
                        <div class="mt-2 text-xs text-gray-500">09:30</div>
                    </div>
                </div>
            </div>

            <!-- 使用说明卡片 -->
            <div class="message-slide-in">
                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4 mx-8">
                    <div class="flex items-center mb-2">
                        <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="font-medium text-blue-800">使用说明</h3>
                    </div>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 您可以咨询专利、商标、版权等知识产权问题</li>
                        <li>• 系统将提供权威的政策解读和流程指导</li>
                        <li>• 每个回答都会标注信息来源，确保可追溯</li>
                    </ul>
                </div>
            </div>

            <!-- 示例问题 -->
            <div class="message-slide-in">
                <div class="mx-8">
                    <p class="text-center text-gray-500 text-xs mb-3">您可以尝试以下问题：</p>
                    <div class="grid grid-cols-1 gap-2">
                        <button onclick="askQuestion('如何申请发明专利？')" class="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm text-left hover:bg-blue-50 hover:border-blue-200 transition-colors">
                            如何申请发明专利？
                        </button>
                        <button onclick="askQuestion('商标注册需要多长时间？')" class="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm text-left hover:bg-blue-50 hover:border-blue-200 transition-colors">
                            商标注册需要多长时间？
                        </button>
                        <button onclick="askQuestion('软件著作权登记流程是什么？')" class="bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm text-left hover:bg-blue-50 hover:border-blue-200 transition-colors">
                            软件著作权登记流程是什么？
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="border-t border-gray-200 bg-white px-4 py-3">
            <div class="flex items-end space-x-3">
                <div class="flex-1">
                    <textarea
                        id="messageInput"
                        placeholder="请输入您的问题..."
                        rows="1"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        onkeydown="handleKeyDown(event)"
                        oninput="autoResize(this)"
                    ></textarea>
                </div>
                <button
                    id="sendButton"
                    onclick="sendMessage()"
                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-4 py-2 transition-colors flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
            <div class="flex items-center justify-between mt-2">
                <p class="text-xs text-gray-500">AI智能问答 • 实时响应</p>
                <div class="flex items-center space-x-2 text-xs text-gray-500">
                    <span id="sessionInfo">会话剩余：10条</span>
                    <span>•</span>
                    <span id="timeRemaining">29:45</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 菜单弹窗 -->
    <div id="menuOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" onclick="toggleMenu()">
        <div class="absolute top-16 right-4 bg-white rounded-lg shadow-xl py-2 w-48" onclick="event.stopPropagation()">
            <button onclick="clearChat()" class="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                清空对话
            </button>
            <button onclick="contactSupport()" class="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                人工客服
            </button>
            <button onclick="showFeedback()" class="w-full text-left px-4 py-2 text-sm hover:bg-gray-100 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                </svg>
                意见反馈
            </button>
        </div>
    </div>

    <!-- 异常提示弹窗 -->
    <div id="errorModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 mx-4 max-w-sm w-full shadow-xl">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900">服务异常</h3>
            </div>
            <p class="text-gray-600 text-sm mb-4">很抱歉，AI服务暂时不可用。您可以尝试稍后再试或联系人工客服获得帮助。</p>
            <div class="flex space-x-3">
                <button onclick="hideErrorModal()" class="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded text-sm transition-colors">
                    稍后再试
                </button>
                <button onclick="contactSupport()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded text-sm transition-colors">
                    人工客服
                </button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let messageCount = 0;
        let maxMessages = 10;
        let sessionTimeout;
        let timeRemaining = 30 * 60; // 30分钟
        let errorCount = 0;
        let isTyping = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSession();
            startSessionTimer();
        });

        // 初始化会话
        function initializeSession() {
            // 模拟OAuth静默授权
            console.log('正在进行OAuth静默授权...');
            
            // 模拟获取用户openid
            const urlParams = new URLSearchParams(window.location.search);
            const openid = urlParams.get('openid') || 'mock_openid_' + Date.now();
            
            // 更新会话信息
            updateSessionInfo();
        }

        // 更新会话信息
        function updateSessionInfo() {
            document.getElementById('sessionInfo').textContent = `会话剩余：${maxMessages - messageCount}条`;
        }

        // 会话计时器
        function startSessionTimer() {
            const timer = setInterval(() => {
                timeRemaining--;
                const minutes = Math.floor(timeRemaining / 60);
                const seconds = timeRemaining % 60;
                document.getElementById('timeRemaining').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeRemaining <= 0) {
                    clearInterval(timer);
                    archiveSession();
                }
            }, 1000);
        }

        // 自动调整输入框高度
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
        }

        // 处理键盘事件
        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message || isTyping) return;
            
            if (messageCount >= maxMessages) {
                showMessage('系统提示', '本次会话已达到消息上限，请重新开始会话。', 'system');
                return;
            }

            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            input.style.height = 'auto';
            
            messageCount++;
            updateSessionInfo();
            
            // 模拟AI回复
            simulateAIResponse(message);
        }

        // 快速提问
        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        // 添加消息到聊天界面
        function addMessage(content, type, references = []) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-slide-in';
            
            const currentTime = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3 justify-end">
                        <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                            <p class="text-sm">${content}</p>
                            <div class="mt-2 text-xs text-blue-100">${currentTime}</div>
                        </div>
                        <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                `;
            } else if (type === 'system') {
                messageDiv.innerHTML = `
                    <div class="mx-8">
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
                            <p class="text-sm text-yellow-800">${content}</p>
                        </div>
                    </div>
                `;
            } else {
                let referencesHtml = '';
                if (references.length > 0) {
                    referencesHtml = `
                        <div class="mt-3 space-y-2">
                            <p class="text-xs font-medium text-gray-600">参考来源：</p>
                            ${references.map(ref => `
                                <div class="reference-link p-2 rounded text-xs">
                                    <div class="font-medium text-blue-800">${ref.title}</div>
                                    <div class="text-gray-600 mt-1">${ref.description}</div>
                                    <div class="text-gray-500 mt-1">来源：${ref.source}</div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }

                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                            <p class="text-gray-800 text-sm">${content}</p>
                            ${referencesHtml}
                            <div class="mt-2 text-xs text-gray-500">${currentTime}</div>
                        </div>
                    </div>
                `;
            }
            
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // 模拟AI回复
        function simulateAIResponse(userMessage) {
            isTyping = true;
            document.getElementById('sendButton').disabled = true;
            
            // 模拟网络延迟
            const delay = Math.random() * 1000 + 500;
            
            setTimeout(() => {
                // 检查是否超时（模拟4秒超时）
                if (Math.random() < 0.1) { // 10%概率超时
                    handleTimeout();
                    return;
                }
                
                // 检查连续错误
                if (Math.random() < 0.05) { // 5%概率出错
                    handleError();
                    return;
                }
                
                // 正常回复
                let response = '';
                let references = [];
                
                if (userMessage.includes('专利')) {
                    response = '发明专利申请流程如下：\n\n1. 准备申请材料（请求书、说明书、权利要求书等）\n2. 向国家知识产权局提交申请\n3. 形式审查（约1-3个月）\n4. 公布（申请日起18个月）\n5. 实质审查（约1-2年）\n6. 授权公告\n\n整个流程通常需要2-3年时间。';
                    references = [
                        {
                            title: '《专利法》第二十六条',
                            description: '申请发明或者实用新型专利的，应当提交请求书、说明书及其摘要和权利要求书等文件。',
                            source: '全国人大常委会'
                        },
                        {
                            title: '《专利法实施细则》第四十条',
                            description: '说明书应当对发明或者实用新型作出清楚、完整的说明。',
                            source: '国务院'
                        }
                    ];
                } else if (userMessage.includes('商标')) {
                    response = '商标注册申请流程：\n\n1. 商标查询（建议进行）\n2. 准备申请材料\n3. 提交申请（网上或现场）\n4. 形式审查（约1个月）\n5. 实质审查（约6-8个月）\n6. 公告期（3个月）\n7. 核准注册\n\n总计约9-12个月完成注册。';
                    references = [
                        {
                            title: '《商标法》第二十二条',
                            description: '商标注册申请人应当按规定的商品分类表填报使用商标的商品类别和商品名称。',
                            source: '全国人大常委会'
                        }
                    ];
                } else if (userMessage.includes('著作权') || userMessage.includes('软件')) {
                    response = '软件著作权登记流程：\n\n1. 准备申请材料（申请表、源代码、说明书等）\n2. 向中国版权保护中心提交申请\n3. 形式审查（约5-10个工作日）\n4. 获得登记证书\n\n软件著作权登记不是强制性的，但建议进行登记以获得更好的法律保护。';
                    references = [
                        {
                            title: '《计算机软件保护条例》第七条',
                            description: '软件著作权人可以向国务院著作权行政管理部门申请登记。',
                            source: '国务院'
                        }
                    ];
                } else {
                    response = '感谢您的咨询。我是专门为知识产权问题提供服务的AI助手。\n\n我可以帮助您了解：\n• 专利申请流程和要求\n• 商标注册相关问题\n• 著作权保护和登记\n• 知识产权政策解读\n• 申请费用和时间安排\n\n请告诉我您具体想了解哪方面的问题？';
                }
                
                addMessage(response, 'assistant', references);
                
                // 重置错误计数
                errorCount = 0;
                isTyping = false;
                document.getElementById('sendButton').disabled = false;
                
            }, delay);
        }

        // 处理超时
        function handleTimeout() {
            addMessage('很抱歉，AI服务响应超时。请稍后再试或通过人工客服获得帮助。', 'assistant');
            isTyping = false;
            document.getElementById('sendButton').disabled = false;
        }

        // 处理错误
        function handleError() {
            errorCount++;
            
            if (errorCount >= 3) {
                showErrorModal();
            } else {
                addMessage('抱歉，处理您的请求时出现问题，请重新尝试。', 'assistant');
            }
            
            isTyping = false;
            document.getElementById('sendButton').disabled = false;
        }

        // 显示错误弹窗
        function showErrorModal() {
            document.getElementById('errorModal').classList.remove('hidden');
        }

        // 隐藏错误弹窗
        function hideErrorModal() {
            document.getElementById('errorModal').classList.add('hidden');
        }

        // 切换菜单
        function toggleMenu() {
            const overlay = document.getElementById('menuOverlay');
            overlay.classList.toggle('hidden');
        }

        // 清空对话
        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            // 保留欢迎消息和使用说明
            const messages = chatContainer.children;
            while (messages.length > 3) {
                chatContainer.removeChild(messages[messages.length - 1]);
            }
            
            messageCount = 0;
            errorCount = 0;
            updateSessionInfo();
            toggleMenu();
        }

        // 联系人工客服
        function contactSupport() {
            // 模拟跳转到人工客服
            alert('正在为您转接人工客服，请稍候...');
            toggleMenu();
        }

        // 显示反馈
        function showFeedback() {
            const feedback = prompt('请输入您的意见或建议：');
            if (feedback) {
                alert('感谢您的反馈，我们会认真考虑您的建议！');
            }
            toggleMenu();
        }

        // 归档会话
        function archiveSession() {
            addMessage('本次会话已超时，正在归档会话记录...', 'system');
            setTimeout(() => {
                addMessage('会话已归档。点击下方按钮可以开始新的对话。', 'system');
            }, 1000);
        }

        // 显示消息（通用方法）
        function showMessage(title, content, type = 'assistant') {
            addMessage(content, type);
        }
    </script>
</body>
</html>