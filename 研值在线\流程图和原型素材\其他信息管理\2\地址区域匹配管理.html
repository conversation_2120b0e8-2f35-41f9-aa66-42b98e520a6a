<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址区域匹配管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">地址区域匹配管理</h1>
            <p class="text-gray-600">管理地址标准化与空间归属体系的匹配规则，支持版本化发布和可视化维护</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">匹配区域</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部区域</option>
                                <option value="zhejiang">浙江省</option>
                                <option value="ningbo">宁波市</option>
                                <option value="haishu">海曙区</option>
                                <option value="jiangbei">江北区</option>
                                <option value="beilun">北仑区</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键词搜索</label>
                            <input type="text" placeholder="输入关键词模糊搜索" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">有效状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="active">有效</option>
                                <option value="inactive">无效</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">版本生效时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            搜索
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 规则列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                匹配规则列表
                            </h2>
                            <button onclick="openRuleModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新增规则
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配区域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">核心关键词</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">公式摘要</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前版本</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRule('rule1')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市海曙区</div>
                                        <div class="text-sm text-gray-500">浙江省 > 宁波市 > 海曙区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-wrap gap-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">中山路</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">天一广场</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">月湖</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">包含(中山路|天一广场|月湖) AND 海曙</div>
                                        <div class="text-sm text-gray-500">权重: 0.8</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">v2.1.3</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">有效</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRule('rule1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRule('rule1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="copyRule('rule1')" class="text-green-600 hover:text-green-900">复制</button>
                                            <button onclick="deleteRule('rule1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRule('rule2')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市江北区</div>
                                        <div class="text-sm text-gray-500">浙江省 > 宁波市 > 江北区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-wrap gap-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">老外滩</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">来福士</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">包含(老外滩|来福士) AND 江北</div>
                                        <div class="text-sm text-gray-500">权重: 0.9</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">v1.8.2</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">有效</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10 09:15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRule('rule2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRule('rule2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="copyRule('rule2')" class="text-green-600 hover:text-green-900">复制</button>
                                            <button onclick="deleteRule('rule2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRule('rule3')">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波高新区</div>
                                        <div class="text-sm text-gray-500">浙江省 > 宁波市 > 高新区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex flex-wrap gap-1">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">研发园</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">科技城</span>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">创新中心</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">包含(研发园|科技城|创新中心) AND 高新</div>
                                        <div class="text-sm text-gray-500">权重: 0.85</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">v0.9.1</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">无效</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-28 16:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRule('rule3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRule('rule3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="copyRule('rule3')" class="text-green-600 hover:text-green-900">复制</button>
                                            <button onclick="deleteRule('rule3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">3</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 测试与模拟区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                测试与模拟
                            </h2>
                            <button onclick="toggleTestPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="testPanelToggle">展开</span>
                            </button>
                        </div>
                        <div id="testPanel" class="hidden space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">测试地址</label>
                                    <input type="text" placeholder="输入要测试的地址，如：宁波市海曙区中山西路138号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">测试版本</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="current">当前版本</option>
                                        <option value="v2.1.3">v2.1.3</option>
                                        <option value="v1.8.2">v1.8.2</option>
                                        <option value="v0.9.1">v0.9.1</option>
                                    </select>
                                </div>
                            </div>
                            <div class="flex space-x-3">
                                <button onclick="runSimulation()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    运行模拟
                                </button>
                                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    清空结果
                                </button>
                            </div>
                            <div id="simulationResult" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">匹配结果</h3>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-white rounded border">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">宁波市海曙区</div>
                                            <div class="text-xs text-gray-500">命中公式: 包含(中山路|天一广场|月湖) AND 海曙</div>
                                        </div>
                                        <div class="text-right">
                                            <div class="text-sm font-medium text-green-600">95%</div>
                                            <div class="text-xs text-gray-500">匹配度</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧版本管理区 -->
            <div class="xl:col-span-1">
                <div class="bg-white rounded-lg shadow-md h-full">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            版本管理
                        </h2>
                        <p class="text-sm text-gray-500 mt-1">选择规则查看版本历史</p>
                    </div>
                    <div class="p-6">
                        <div id="versionList" class="space-y-3">
                            <div class="text-sm text-gray-500 text-center py-8">
                                <svg class="w-12 h-12 mx-auto text-gray-300 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                请选择一个规则查看版本历史
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则编辑弹窗 -->
    <div id="ruleModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">规则编辑</h3>
                    <button onclick="closeRuleModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">匹配区域</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择区域</option>
                                    <option value="zhejiang">浙江省</option>
                                    <option value="ningbo">宁波市</option>
                                    <option value="haishu">海曙区</option>
                                    <option value="jiangbei">江北区</option>
                                    <option value="beilun">北仑区</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">有效状态</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="active">有效</option>
                                    <option value="inactive">无效</option>
                                </select>
                            </div>
                        </div>

                        <!-- 关键词设置 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">核心关键词</label>
                            <div class="space-y-2">
                                <input type="text" placeholder="输入关键词，按回车添加" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div class="flex flex-wrap gap-2">
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                        中山路
                                        <button class="ml-2 text-blue-600 hover:text-blue-800">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800">
                                        天一广场
                                        <button class="ml-2 text-blue-600 hover:text-blue-800">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- 公式编辑器 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">匹配公式</label>
                            <div class="border border-gray-300 rounded-md">
                                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300 flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <button class="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50">AND</button>
                                        <button class="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50">OR</button>
                                        <button class="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50">NOT</button>
                                        <button class="px-2 py-1 text-xs bg-white border border-gray-300 rounded hover:bg-gray-50">()</button>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs text-gray-500">语法检查</span>
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                    </div>
                                </div>
                                <textarea rows="4" placeholder="包含(中山路|天一广场|月湖) AND 海曙" class="w-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm resize-none"></textarea>
                            </div>
                            <div class="mt-2 text-xs text-gray-500">
                                支持正则表达式和逻辑运算符，使用 | 表示或，& 表示且，! 表示非
                            </div>
                        </div>

                        <!-- 权重设置 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">匹配权重</label>
                                <input type="range" min="0" max="1" step="0.1" value="0.8" class="w-full">
                                <div class="flex justify-between text-xs text-gray-500 mt-1">
                                    <span>0.0</span>
                                    <span>0.8</span>
                                    <span>1.0</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">版本生效时间</label>
                                <input type="datetime-local" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeRuleModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 规则编辑弹窗
        function openRuleModal() {
            document.getElementById('ruleModal').classList.remove('hidden');
        }
        
        function closeRuleModal() {
            document.getElementById('ruleModal').classList.add('hidden');
        }
        
        // 选择规则显示版本历史
        function selectRule(ruleId) {
            const versionList = document.getElementById('versionList');
            
            const versions = {
                'rule1': [
                    { version: 'v2.1.3', status: '当前版本', date: '2024-01-15', author: '张三', active: true },
                    { version: 'v2.1.2', status: '历史版本', date: '2024-01-10', author: '李四', active: false },
                    { version: 'v2.0.1', status: '历史版本', date: '2023-12-20', author: '王五', active: false }
                ],
                'rule2': [
                    { version: 'v1.8.2', status: '当前版本', date: '2024-01-10', author: '李四', active: true },
                    { version: 'v1.8.1', status: '历史版本', date: '2023-12-15', author: '张三', active: false }
                ],
                'rule3': [
                    { version: 'v0.9.1', status: '当前版本', date: '2023-12-28', author: '王五', active: true },
                    { version: 'v0.8.5', status: '历史版本', date: '2023-11-20', author: '赵六', active: false }
                ]
            };
            
            const ruleVersions = versions[ruleId] || [];
            
            versionList.innerHTML = ruleVersions.map(version => `
                <div class="border border-gray-200 rounded-lg p-3 ${version.active ? 'bg-blue-50 border-blue-200' : 'bg-white'}">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-900">${version.version}</span>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            version.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }">
                            ${version.status}
                        </span>
                    </div>
                    <div class="text-xs text-gray-500 mb-2">
                        <div>发布时间: ${version.date}</div>
                        <div>发布人: ${version.author}</div>
                    </div>
                    <div class="flex space-x-2">
                        ${!version.active ? `
                            <button onclick="setCurrentVersion('${ruleId}', '${version.version}')" class="text-xs text-blue-600 hover:text-blue-800">设为当前</button>
                        ` : ''}
                        <button onclick="compareVersion('${ruleId}', '${version.version}')" class="text-xs text-indigo-600 hover:text-indigo-800">对比差异</button>
                        ${!version.active ? `
                            <button onclick="rollbackVersion('${ruleId}', '${version.version}')" class="text-xs text-orange-600 hover:text-orange-800">回滚</button>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }
        
        // 测试面板切换
        function toggleTestPanel() {
            const panel = document.getElementById('testPanel');
            const toggle = document.getElementById('testPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 运行模拟
        function runSimulation() {
            const result = document.getElementById('simulationResult');
            result.classList.remove('hidden');
        }
        
        // 其他功能函数
        function viewRule(ruleId) {
            console.log('查看规则:', ruleId);
        }
        
        function editRule(ruleId) {
            openRuleModal();
            console.log('编辑规则:', ruleId);
        }
        
        function copyRule(ruleId) {
            console.log('复制规则:', ruleId);
        }
        
        function deleteRule(ruleId) {
            if (confirm('确定要删除这个规则吗？')) {
                console.log('删除规则:', ruleId);
            }
        }
        
        function setCurrentVersion(ruleId, version) {
            console.log('设为当前版本:', ruleId, version);
        }
        
        function compareVersion(ruleId, version) {
            console.log('对比版本差异:', ruleId, version);
        }
        
        function rollbackVersion(ruleId, version) {
            if (confirm('确定要回滚到此版本吗？')) {
                console.log('回滚版本:', ruleId, version);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('ruleModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRuleModal();
            }
        });
    </script>
</body>
</html>