object DataModule1: TDataModule1
  OldCreateOrder = False
  OnCreate = DataModuleCreate
  OnDestroy = DataModuleDestroy
  Height = 325
  Width = 402
  object UniDataSource1: TUniDataSource
    DataSet = UniQuery1
    Left = 104
    Top = 96
  end
  object UniQuery1: TUniQuery
    Connection = UniConnection1
    SQL.Strings = (
      'select * from plus_customer')
    Left = 40
    Top = 96
  end
  object MySQLUniProvider1: TMySQLUniProvider
    Left = 104
    Top = 32
  end
  object UniConnection1: TUniConnection
    ProviderName = 'MySQL'
    Port = 3306
    SpecificOptions.Strings = (
      'MySQL.Charset=GBK')
    Left = 40
    Top = 32
    EncryptedPassword = ''
  end
  object UniTransaction1: TUniTransaction
    DefaultConnection = UniConnection1
    Left = 168
    Top = 32
  end
end
