"use client";

import { useState } from "react";
import { 
  Card, 
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle 
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Edit,
  Phone,
  Mail,
  Building,
  MapPin,
  Globe,
  FileText,
  User,
  DollarSign,
  Calendar,
  Clock,
  BarChart3,
  ShoppingCart,
  CreditCard,
  AlertCircle,
  CheckCircle2,
  BrainCircuit,
  Sparkles,
  LightbulbIcon,
  ThumbsUp,
  ThumbsDown,
  ArrowRight,
  TrendingUp,
  Users,
  HeartHandshake,
  PiggyBank,
  BadgeDollarSign,
  Award,
  Timer,
  AlertTriangle,
  ArrowRightCircle,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";

// 模拟客户数据
const customerData = {
  id: "CUS-2023-001",
  name: "北京科技创新有限公司",
  type: "企业",
  industry: "信息技术",
  size: "中型企业 (200-500人)",
  status: "活跃",
  level: "关键客户",
  creditLimit: 500000,
  remainingCredit: 320000,
  contactInfo: {
    contact: "张志远",
    title: "CIO",
    phone: "010-********",
    mobile: "***********",
    email: "<EMAIL>",
    address: "北京市海淀区中关村软件园",
    website: "www.bjtech-innovation.com"
  },
  financialInfo: {
    totalPurchase: 2580000,
    outstandingInvoices: 180000,
    overdueDays: 0,
    paymentTimeliness: 98,
    avgDaysToPayment: 12
  },
  relationshipInfo: {
    since: "2020-05-15",
    lastPurchaseDate: "2023-09-28",
    purchaseFrequency: "每季度",
    productCategories: ["ERP系统", "数据分析服务", "云存储方案"],
    lastContactDate: "2023-10-05",
    nextFollowUpDate: "2023-10-20",
    assignedTo: "李明 (销售经理)"
  },
  purchaseHistory: [
    {
      id: "ORD-2023-128",
      date: "2023-09-28",
      amount: 580000,
      status: "已完成",
      items: ["ERP系统升级", "定制开发", "用户培训"]
    }
  ],
  opportunities: [
    {
      id: "OPP-2023-045",
      title: "数据中台建设",
      estimatedValue: 1200000,
      probability: 75,
      expectedCloseDate: "2023-12-15",
      stage: "提案阶段"
    },
    {
      id: "OPP-2024-003",
      title: "移动应用开发",
      estimatedValue: 680000,
      probability: 50,
      expectedCloseDate: "2024-02-28",
      stage: "需求调研"
    }
  ],
  // AI分析数据
  aiAnalysis: {
    customerValue: {
      score: 92,
      category: "高价值客户",
      trend: "上升",
      insights: [
        "过去12个月内购买频率高于同行业客户平均水平68%",
        "客户采购金额持续增长，年增长率达到32%",
        "付款及时性98%，远高于平均水平(85%)"
      ]
    },
    riskAssessment: {
      score: 12,
      level: "低风险",
      insights: [
        "财务状况稳健，从未有过逾期付款",
        "业务规模稳定增长，未发现收缩迹象",
        "多条业务线采购，依赖度分散"
      ]
    },
    growthPotential: {
      score: 87,
      insights: [
        "数据分析需求增长迅速，可进一步拓展高级分析服务",
        "客户正在规划新的数字化转型项目",
        "移动应用需求尚未完全满足，存在新业务机会"
      ],
      recommendations: [
        {
          title: "提高信用额度至800,000元",
          reasoning: "基于客户优良的付款记录和增长的采购需求",
          impact: "高",
          confidenceScore: 94
        },
        {
          title: "推荐数据中台高级版方案",
          reasoning: "根据客户数据量增长和分析需求升级判断",
          impact: "中",
          confidenceScore: 88
        },
        {
          title: "主动提供移动应用解决方案",
          reasoning: "基于客户最近查询和行业数字化趋势",
          impact: "中",
          confidenceScore: 85
        },
        {
          title: "安排季度业务回顾会议",
          reasoning: "加强关系维护，及时发现新需求",
          impact: "中",
          confidenceScore: 91
        }
      ]
    },
    similarCustomers: [
      {
        name: "上海金融科技服务集团",
        similarity: 87,
        keyAttributes: ["信息技术行业", "数据分析需求", "多业务线合作"]
      },
      {
        name: "广州新零售科技有限公司",
        similarity: 81,
        keyAttributes: ["数字化转型阶段", "ERP系统用户", "增长迅速"]
      }
    ],
    aiConfidence: 93,
    lastUpdated: "2023-10-12 08:30"
  }
};

export default function CustomerProfilePage() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showCompleteAIProfile, setShowCompleteAIProfile] = useState(false);
  
  // 获取标签颜色
  const getCustomerLevelColor = (level: string) => {
    switch(level) {
      case "关键客户": return "bg-yellow-500";
      case "战略客户": return "bg-purple-500";
      case "核心客户": return "bg-blue-500";
      case "普通客户": return "bg-gray-500";
      default: return "bg-gray-500";
    }
  };
  
  // 计算客户价值评分对应的颜色
  const getScoreColor = (score: number) => {
    if (score >= 85) return "text-green-600";
    if (score >= 70) return "text-blue-600";
    if (score >= 50) return "text-yellow-600";
    return "text-red-600";
  };
  
  // 处理应用AI建议
  const handleApplyRecommendation = (recommendation: any) => {
    // 实际应用中这里应该调用API进行数据更新
    // 这里只是示例，会显示一个提示
    alert(`即将应用建议: ${recommendation.title}\n\n这在实际系统中会触发相应的业务流程变更。`);
  };
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* 页面头部 */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            {customerData.name}
            <Badge className={`${getCustomerLevelColor(customerData.level)} text-white`}>
              {customerData.level}
            </Badge>
          </h1>
          <p className="text-muted-foreground mt-1">
            {customerData.industry} · {customerData.size} · 客户ID: {customerData.id}
          </p>
        </div>
        <Button className="bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200">
          <Edit className="h-4 w-4 mr-2" />
          编辑客户
        </Button>
      </div>
      
      {/* 客户概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-sm text-muted-foreground">总采购金额</h3>
              <DollarSign className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-2xl font-bold">¥ {customerData.financialInfo.totalPurchase.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground mt-1">较上年增长32%</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-sm text-muted-foreground">未付账款</h3>
              <FileText className="h-4 w-4 text-amber-500" />
            </div>
            <p className="text-2xl font-bold">¥ {customerData.financialInfo.outstandingInvoices.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground mt-1">
              {customerData.financialInfo.overdueDays > 0 
                ? `逾期 ${customerData.financialInfo.overdueDays} 天` 
                : "未有逾期"}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-sm text-muted-foreground">信用额度</h3>
              <CreditCard className="h-4 w-4 text-blue-500" />
            </div>
            <p className="text-2xl font-bold">¥ {customerData.creditLimit.toLocaleString()}</p>
            <p className="text-xs text-muted-foreground mt-1">
              剩余: ¥ {customerData.remainingCredit.toLocaleString()}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-center mb-3">
              <h3 className="font-medium text-sm text-muted-foreground">付款准时率</h3>
              <Clock className="h-4 w-4 text-indigo-500" />
            </div>
            <p className="text-2xl font-bold">{customerData.financialInfo.paymentTimeliness}%</p>
            <p className="text-xs text-muted-foreground mt-1">
              平均付款天数: {customerData.financialInfo.avgDaysToPayment}天
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左侧和中间内容区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 标签页 */}
          <Card>
            <CardHeader className="pb-3">
              <Tabs defaultValue="overview" onValueChange={setActiveTab}>
                <TabsList>
                  <TabsTrigger value="overview">概览</TabsTrigger>
                  <TabsTrigger value="orders">订单历史</TabsTrigger>
                  <TabsTrigger value="opportunities">商机</TabsTrigger>
                  <TabsTrigger value="interactions">互动记录</TabsTrigger>
                </TabsList>
              </Tabs>
            </CardHeader>
            <CardContent>
              {/* 概览标签内容 */}
              {activeTab === "overview" && (
                <div className="space-y-6">
                  {/* 联系信息 */}
                  <div>
                    <h3 className="font-semibold text-lg mb-4">联系信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">主要联系人:</span>
                          <span className="text-sm">{customerData.contactInfo.contact}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-normal">
                            {customerData.contactInfo.title}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{customerData.contactInfo.phone}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{customerData.contactInfo.mobile}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{customerData.contactInfo.email}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{customerData.contactInfo.address}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Globe className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">{customerData.contactInfo.website}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  {/* 关系信息 */}
                  <div>
                    <h3 className="font-semibold text-lg mb-4">关系信息</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">客户自:</span>
                          <span className="text-sm">{customerData.relationshipInfo.since}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">最近购买:</span>
                          <span className="text-sm">{customerData.relationshipInfo.lastPurchaseDate}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">购买频率:</span>
                          <span className="text-sm">{customerData.relationshipInfo.purchaseFrequency}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">最近联系:</span>
                          <span className="text-sm">{customerData.relationshipInfo.lastContactDate}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">下次跟进:</span>
                          <span className="text-sm">{customerData.relationshipInfo.nextFollowUpDate}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">客户经理:</span>
                          <span className="text-sm">{customerData.relationshipInfo.assignedTo}</span>
                        </div>
                      </div>
                    </div>
                    <div className="mt-4">
                      <div className="flex items-center gap-2 mb-2">
                        <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">产品类别:</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {customerData.relationshipInfo.productCategories.map((category, index) => (
                          <Badge key={index} variant="secondary">
                            {category}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              {/* 订单历史标签内容 */}
              {activeTab === "orders" && (
                <div className="space-y-4">
                  <h3 className="font-semibold text-lg">订单历史</h3>
                  <div className="border rounded-md">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>订单号</TableHead>
                          <TableHead>日期</TableHead>
                          <TableHead>产品/服务</TableHead>
                          <TableHead className="text-right">金额 (¥)</TableHead>
                          <TableHead>状态</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {customerData.purchaseHistory.map((order) => (
                          <TableRow key={order.id}>
                            <TableCell className="font-medium">{order.id}</TableCell>
                            <TableCell>{order.date}</TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {order.items.map((item, idx) => (
                                  <Badge key={idx} variant="outline" className="font-normal">
                                    {item}
                                  </Badge>
                                ))}
                              </div>
                            </TableCell>
                            <TableCell className="text-right">{order.amount.toLocaleString()}</TableCell>
                            <TableCell>
                              <Badge variant={order.status === "已完成" ? "success" : "default"}>
                                {order.status}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              )}
              
              {/* 商机标签内容 */}
              {activeTab === "opportunities" && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold text-lg">潜在商机</h3>
                    <Button size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200">
                      <Plus className="h-4 w-4 mr-1" />
                      新建商机
                    </Button>
                  </div>
                  
                  <div className="space-y-4">
                    {customerData.opportunities.map((opportunity) => (
                      <Card key={opportunity.id}>
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-semibold">{opportunity.title}</h4>
                              <p className="text-sm text-muted-foreground">{opportunity.id} · 预期: {opportunity.expectedCloseDate}</p>
                            </div>
                            <Badge variant={opportunity.probability >= 70 ? "success" : "outline"}>
                              成功率: {opportunity.probability}%
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                            <div>
                              <p className="text-sm text-muted-foreground mb-1">预估价值</p>
                              <p className="font-semibold">¥ {opportunity.estimatedValue.toLocaleString()}</p>
                            </div>
                            <div>
                              <p className="text-sm text-muted-foreground mb-1">阶段</p>
                              <p className="font-semibold">{opportunity.stage}</p>
                            </div>
                            <div className="flex justify-end items-center">
                              <Button size="sm" className="bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200">
                                查看详情
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
              
              {/* 互动记录标签内容 */}
              {activeTab === "interactions" && (
                <div className="text-center p-10 text-muted-foreground">
                  <p>互动记录功能开发中...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        {/* AI客户画像侧栏 */}
        <div className="space-y-6">
          {/* AI客户价值评估卡片 */}
          <Card className="bg-blue-50 border-blue-200">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center gap-2 text-blue-700">
                  <BrainCircuit className="h-5 w-5 text-blue-500" />
                  AI客户画像
                </CardTitle>
                <Badge variant="outline" className="text-blue-700 border-blue-200">
                  更新于: {customerData.aiAnalysis.lastUpdated}
                </Badge>
              </div>
              <CardDescription className="text-blue-600">
                基于历史交易数据和行业模式的智能分析
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {/* 客户价值评分 */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-blue-700">客户价值评分</h3>
                  <div className="flex items-center gap-1">
                    <span className={`text-xl font-bold ${getScoreColor(customerData.aiAnalysis.customerValue.score)}`}>
                      {customerData.aiAnalysis.customerValue.score}
                    </span>
                    <span className="text-sm text-blue-600">/100</span>
                  </div>
                </div>
                
                <Progress 
                  value={customerData.aiAnalysis.customerValue.score} 
                  className="h-2 mb-2"
                />
                
                <div className="flex items-center gap-2 text-blue-700 mb-3">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700 border-none">
                    {customerData.aiAnalysis.customerValue.category}
                  </Badge>
                  <div className="flex items-center text-xs gap-1">
                    <TrendingUp className="h-3.5 w-3.5" />
                    <span>趋势: {customerData.aiAnalysis.customerValue.trend}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  {customerData.aiAnalysis.customerValue.insights.map((insight, idx) => (
                    <div key={idx} className="flex items-start gap-2 text-sm text-blue-700">
                      <CheckCircle2 className="h-4 w-4 text-blue-500 mt-0.5 shrink-0" />
                      <span>{insight}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator className="bg-blue-200" />
              
              {/* 风险评估 */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-semibold text-blue-700">风险评估</h3>
                  <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                    {customerData.aiAnalysis.riskAssessment.level}
                  </Badge>
                </div>
                
                <div className="space-y-2">
                  {customerData.aiAnalysis.riskAssessment.insights.map((insight, idx) => (
                    <div key={idx} className="flex items-start gap-2 text-sm text-blue-700">
                      <CheckCircle2 className="h-4 w-4 text-blue-500 mt-0.5 shrink-0" />
                      <span>{insight}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator className="bg-blue-200" />
              
              {/* 增长潜力与建议 */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-semibold text-blue-700">增长潜力</h3>
                  <div className="flex items-center gap-1">
                    <span className="text-xl font-bold text-blue-700">
                      {customerData.aiAnalysis.growthPotential.score}
                    </span>
                    <span className="text-sm text-blue-600">/100</span>
                  </div>
                </div>
                
                <div className="space-y-2 mb-4">
                  {customerData.aiAnalysis.growthPotential.insights.map((insight, idx) => (
                    <div key={idx} className="flex items-start gap-2 text-sm text-blue-700">
                      <LightbulbIcon className="h-4 w-4 text-amber-500 mt-0.5 shrink-0" />
                      <span>{insight}</span>
                    </div>
                  ))}
                </div>
                
                <div className="mb-3">
                  <h4 className="font-semibold text-sm text-blue-700 mb-2">AI推荐行动</h4>
                  <div className="space-y-3">
                    {customerData.aiAnalysis.growthPotential.recommendations.slice(0, showCompleteAIProfile ? undefined : 2).map((rec, idx) => (
                      <div key={idx} className="bg-white rounded-md p-3 border border-blue-100">
                        <div className="flex items-start gap-2">
                          <div className="bg-blue-100 rounded-full p-1 mt-0.5 shrink-0">
                            {idx === 0 ? (
                              <PiggyBank className="h-4 w-4 text-blue-600" />
                            ) : idx === 1 ? (
                              <BadgeDollarSign className="h-4 w-4 text-blue-600" />
                            ) : idx === 2 ? (
                              <HeartHandshake className="h-4 w-4 text-blue-600" />
                            ) : (
                              <Users className="h-4 w-4 text-blue-600" />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <h5 className="font-semibold text-sm">{rec.title}</h5>
                              <Badge variant="outline" className="text-xs text-blue-700 border-blue-200">
                                影响: {rec.impact}
                              </Badge>
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              {rec.reasoning}
                            </p>
                            
                            <div className="flex justify-between items-center mt-2">
                              <div className="flex items-center gap-1 text-xs text-blue-600">
                                <Sparkles className="h-3.5 w-3.5" />
                                <span>置信度: {rec.confidenceScore}%</span>
                              </div>
                              <Button 
                                size="sm" 
                                className="h-7 text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                onClick={() => handleApplyRecommendation(rec)}
                              >
                                应用建议
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {!showCompleteAIProfile && customerData.aiAnalysis.growthPotential.recommendations.length > 2 && (
                    <Button 
                      variant="ghost" 
                      className="w-full mt-2 text-blue-600 hover:bg-blue-50"
                      onClick={() => setShowCompleteAIProfile(true)}
                    >
                      <ArrowRight className="h-4 w-4 mr-1" />
                      查看全部 {customerData.aiAnalysis.growthPotential.recommendations.length} 条建议
                    </Button>
                  )}
                </div>
              </div>
              
              <Separator className="bg-blue-200" />
              
              {/* 相似客户 */}
              {showCompleteAIProfile && (
                <>
                  <div>
                    <h3 className="font-semibold text-blue-700 mb-3">相似客户</h3>
                    <div className="space-y-3">
                      {customerData.aiAnalysis.similarCustomers.map((customer, idx) => (
                        <div key={idx} className="flex justify-between items-center bg-white p-2 rounded-md border border-blue-100">
                          <div>
                            <h5 className="font-semibold text-sm">{customer.name}</h5>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {customer.keyAttributes.map((attr, i) => (
                                <Badge key={i} variant="outline" className="text-xs font-normal">
                                  {attr}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="flex items-center text-sm text-blue-600 font-medium">
                            {customer.similarity}% 匹配
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <Separator className="bg-blue-200" />
                </>
              )}
              
              {/* 反馈按钮 */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-1 text-blue-600">
                  <Sparkles className="h-4 w-4" />
                  <span>AI置信度: {customerData.aiAnalysis.aiConfidence}%</span>
                </div>
                <div className="flex items-center gap-2 text-gray-500">
                  <span>分析有用吗?</span>
                  <div className="flex gap-1">
                    <Button variant="ghost" size="icon" className="h-7 w-7 rounded-full bg-blue-50 hover:bg-blue-100">
                      <ThumbsUp className="h-3.5 w-3.5 text-blue-600" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-7 w-7 rounded-full bg-gray-50 hover:bg-gray-100">
                      <ThumbsDown className="h-3.5 w-3.5 text-gray-400" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* 快捷操作卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">快捷操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200">
                <FileText className="h-4 w-4 mr-2" />
                创建新报价
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                安排跟进
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Mail className="h-4 w-4 mr-2" />
                发送邮件
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Building className="h-4 w-4 mr-2" />
                编辑公司信息
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 