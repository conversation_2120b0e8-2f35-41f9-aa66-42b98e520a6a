unit CcJcdmtype;

interface
uses
  Classes;

type
  TCcJcdmtype = class
  private
    FJcdmtypeid: integer;
    FJcdmtypename: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Jcdmtypeid: integer read FJcdmtypeid write FJcdmtypeid;
    property Jcdmtypename: string read FJcdmtypename write FJcdmtypename;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketU<PERSON>;
    property State: string read FState write FState;
  end;

implementation

end.

