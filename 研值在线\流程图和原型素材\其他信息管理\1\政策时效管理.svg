<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策时效管理流程</text>

  <!-- 阶段一：时效设置与校验 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：时效设置与校验</text>
  
  <!-- 节点1: 时效设置 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">时效设置</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(设置或调整政策时效)</text>
  </g>

  <!-- 节点2: 时效校验 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">时效校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(生效时间≥发布日期, 失效时间≥生效时间)</text>
  </g>

  <!-- 节点3: 写入时效 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入时效字段</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(记录变更日志)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 L 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 165 L 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：状态自动流转 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：状态自动流转</text>

  <!-- 节点4: 未生效检查 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">未生效政策检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(每日定时任务)</text>
  </g>

  <!-- 节点5: 状态切换 -->
  <g transform="translate(500, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态切换为"生效中"</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(推送通知至各业务模块)</text>
  </g>

  <!-- 节点6: 生效中检查 -->
  <g transform="translate(800, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生效中政策检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(每日扫描)</text>
  </g>
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 400 335 L 500 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 335 L 800 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点7: 到期提醒 -->
  <g transform="translate(650, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">到期提醒</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(预警阈值内即将到期)</text>
  </g>

  <!-- 节点8: 状态更新 -->
  <g transform="translate(950, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新为"已失效"</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(失效时间达到后)</text>
  </g>

  <!-- 连接线 6 -> 7 -->
  <path d="M 900 370 C 900 395, 800 395, 750 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6 -> 8 -->
  <path d="M 900 370 C 900 395, 1000 395, 1050 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点9: 风险报告 -->
  <g transform="translate(950, 540)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">引用检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(生成风险报告)</text>
  </g>

  <!-- 连接线 8 -> 9 -->
  <path d="M 1050 490 L 1050 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：批量处理与审计 -->
  <text x="300" y="450" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：批量处理与审计</text>

  <!-- 节点10: 批量导入 -->
  <g transform="translate(50, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(时效调整)</text>
  </g>

  <!-- 节点11: 逐行校验 -->
  <g transform="translate(300, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">逐行校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(保存合规记录至"待审核"队列)</text>
  </g>

  <!-- 节点12: 管理员审核 -->
  <g transform="translate(300, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(审核通过后统一生效)</text>
  </g>
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 250 535 L 300 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 11 -> 12 -->
  <path d="M 400 570 L 400 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点13: 审计库 -->
  <g transform="translate(600, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(记录所有时效变更操作及状态流转)</text>
  </g>

  <!-- 节点14: 时效变更报表 -->
  <g transform="translate(600, 740)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">时效变更报表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(定期归档生成, 供合规部门查询)</text>
  </g>

  <!-- 连接线 3 -> 13 (从写入时效到审计库) -->
  <path d="M 900 200 C 900 400, 700 500, 700 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 13 (从状态切换到审计库) -->
  <path d="M 600 370 C 600 450, 650 500, 650 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 8 -> 13 (从状态更新到审计库) -->
  <path d="M 950 455 C 850 500, 750 550, 700 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 12 -> 13 (从管理员审核到审计库) -->
  <path d="M 500 655 L 600 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 13 -> 14 -->
  <path d="M 700 690 L 700 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>