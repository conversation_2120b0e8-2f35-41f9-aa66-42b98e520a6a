<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-医疗队伍建设展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">医疗队伍建设展示</h1>
            <p class="mt-2 text-gray-600">全面展示医院科研与医疗服务人才储备情况</p>
        </div>

        <!-- 卡片概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">在岗研发团队</p>
                        <div class="flex items-end">
                            <p class="text-2xl font-bold text-gray-900 mr-2">28</p>
                            <span class="text-sm text-green-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                </svg>
                                12%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">研发人员总数</p>
                        <div class="flex items-end">
                            <p class="text-2xl font-bold text-gray-900 mr-2">156</p>
                            <span class="text-sm text-green-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                </svg>
                                8%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">注册服务专家</p>
                        <div class="flex items-end">
                            <p class="text-2xl font-bold text-gray-900 mr-2">42</p>
                            <span class="text-sm text-red-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                </svg>
                                3%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">兼职顾问人数</p>
                        <div class="flex items-end">
                            <p class="text-2xl font-bold text-gray-900 mr-2">15</p>
                            <span class="text-sm text-green-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                </svg>
                                5%
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">科室</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部科室</option>
                        <option>心血管内科</option>
                        <option>神经外科</option>
                        <option>肿瘤科</option>
                        <option>儿科</option>
                        <option>妇产科</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">职称等级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部职称</option>
                        <option>主任医师</option>
                        <option>副主任医师</option>
                        <option>主治医师</option>
                        <option>住院医师</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">专家类别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类别</option>
                        <option>临床专家</option>
                        <option>科研专家</option>
                        <option>教学专家</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">关键字搜索</label>
                    <input type="text" placeholder="输入姓名或专业" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 队伍列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">队伍列表</h2>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            新增团队
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">队伍/成员名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属科室</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务/职称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规模/服务时长</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主要研究方向</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?team" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">心血管疾病研究团队</div>
                                        <div class="text-sm text-gray-500">团队</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">心血管内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">主任医师领衔</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">冠心病介入治疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('team')" class="text-blue-600 hover:text-blue-900 mr-3">查看团队</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?doctor" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张明华</div>
                                        <div class="text-sm text-gray-500">神经外科</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">神经外科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">主任医师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">320小时</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">脑肿瘤微创治疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('member')" class="text-blue-600 hover:text-blue-900 mr-3">查看成员</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?team" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">肿瘤精准治疗团队</div>
                                        <div class="text-sm text-gray-500">团队</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">肿瘤科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">副主任医师领衔</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6人</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">肿瘤免疫治疗</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('team')" class="text-blue-600 hover:text-blue-900 mr-3">查看团队</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?doctor" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李雪梅</div>
                                        <div class="text-sm text-gray-500">儿科</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">儿科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">副主任医师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">280小时</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">儿童呼吸系统疾病</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal('member')" class="text-blue-600 hover:text-blue-900 mr-3">查看成员</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-4 条，共 28 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-6">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">各科室研发团队分布</h3>
                    <div class="h-80">
                        <canvas id="deptChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">职称等级分布</h3>
                    <div class="h-80">
                        <canvas id="titleChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">专家服务时长趋势</h3>
                    <div class="h-80">
                        <canvas id="serviceChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-4">顾问类别占比</h3>
                    <div class="h-80">
                        <canvas id="advisorChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情侧栏 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex justify-end h-full">
            <div class="bg-white w-full max-w-2xl h-full overflow-y-auto">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">团队详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 h-20 w-20">
                            <img class="h-20 w-20 rounded-full" src="https://source.unsplash.com/random/200x200/?team" alt="">
                        </div>
                        <div class="ml-4">
                            <h4 class="text-xl font-bold text-gray-900" id="detailName">心血管疾病研究团队</h4>
                            <p class="text-sm text-gray-500" id="detailDept">心血管内科</p>
                        </div>
                    </div>
                    
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button id="tab1" class="border-b-2 border-blue-500 text-blue-600 px-1 py-4 text-sm font-medium">团队简介</button>
                            <button id="tab2" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">成员构成</button>
                            <button id="tab3" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">在研项目</button>
                            <button id="tab4" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">合作案例</button>
                        </nav>
                    </div>
                    
                    <div id="tabContent1" class="py-4">
                        <h5 class="text-md font-medium text-gray-900 mb-2">团队介绍</h5>
                        <p class="text-sm text-gray-600 mb-4">心血管疾病研究团队由8名专业医师组成，专注于冠心病介入治疗领域的研究与临床实践。团队拥有丰富的临床经验和科研成果，在国内外期刊发表论文30余篇。</p>
                        
                        <h5 class="text-md font-medium text-gray-900 mb-2">研究方向</h5>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">冠心病介入治疗</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">心血管影像学</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">心脏康复</span>
                        </div>
                        
                        <h5 class="text-md font-medium text-gray-900 mb-2">科研成果</h5>
                        <ul class="list-disc pl-5 text-sm text-gray-600 space-y-1">
                            <li>2023年发表SCI论文5篇</li>
                            <li>获得国家自然科学基金项目1项</li>
                            <li>省级科技进步二等奖1项</li>
                        </ul>
                    </div>
                    
                    <div id="tabContent2" class="py-4 hidden">
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?doctor" alt="">
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">王建国</p>
                                    <p class="text-sm text-gray-500">主任医师/团队负责人</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?doctor" alt="">
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">李红梅</p>
                                    <p class="text-sm text-gray-500">副主任医师</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?doctor" alt="">
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">张伟</p>
                                    <p class="text-sm text-gray-500">主治医师</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 mt-6">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出PDF
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                            </svg>
                            分享链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openDetailModal(type) {
            const modal = document.getElementById('detailModal');
            const modalTitle = document.getElementById('modalTitle');
            const detailName = document.getElementById('detailName');
            const detailDept = document.getElementById('detailDept');
            
            if (type === 'team') {
                modalTitle.textContent = '团队详情';
                detailName.textContent = '心血管疾病研究团队';
                detailDept.textContent = '心血管内科';
            } else {
                modalTitle.textContent = '成员详情';
                detailName.textContent = '张明华';
                detailDept.textContent = '神经外科';
            }
            
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            const deptCtx = document.getElementById('deptChart').getContext('2d');
            new Chart(deptCtx, {
                type: 'bar',
                data: {
                    labels: ['心血管内科', '神经外科', '肿瘤科', '儿科', '妇产科'],
                    datasets: [{
                        label: '团队数量',
                        data: [5, 3, 4, 2, 3],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
            
            const titleCtx = document.getElementById('titleChart').getContext('2d');
            new Chart(titleCtx, {
                type: 'pie',
                data: {
                    labels: ['主任医师', '副主任医师', '主治医师', '住院医师'],
                    datasets: [{
                        data: [15, 25, 40, 20],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            const serviceCtx = document.getElementById('serviceChart').getContext('2d');
            new Chart(serviceCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '服务时长(小时)',
                        data: [320, 280, 350, 400, 380, 420],
                        borderColor: 'rgba(59, 130, 246, 0.8)',
                        tension: 0.4,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            const advisorCtx = document.getElementById('advisorChart').getContext('2d');
            new Chart(advisorCtx, {
                type: 'doughnut',
                data: {
                    labels: ['临床顾问', '科研顾问', '管理顾问'],
                    datasets: [{
                        data: [8, 5, 2],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // 标签页切换
            const tabs = ['tab1', 'tab2', 'tab3', 'tab4'];
            tabs.forEach(tabId => {
                document.getElementById(tabId).addEventListener('click', function() {
                    // 重置所有标签和内容
                    tabs.forEach(id => {
                        document.getElementById(id).classList.remove('border-blue-500', 'text-blue-600');
                        document.getElementById(id).classList.add('border-transparent', 'text-gray-500');
                        document.getElementById('tabContent' + id.slice(3)).classList.add('hidden');
                    });
                    
                    // 激活当前标签
                    this.classList.add('border-blue-500', 'text-blue-600');
                    this.classList.remove('border-transparent', 'text-gray-500');
                    document.getElementById('tabContent' + this.id.slice(3)).classList.remove('hidden');
                });
            });
            
            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });
            
            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>