<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员成果管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研管理人员成果管理</h1>

        <!-- 查询筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">查询条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">科研人员</label>
                    <input type="text" placeholder="姓名或工号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部单位</option>
                        <option value="ningbo-university">宁波大学</option>
                        <option value="ningbo-polytechnic">宁波职业技术学院</option>
                        <option value="ningbo-research">宁波市科研院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="paper">论文</option>
                        <option value="patent">专利</option>
                        <option value="project">项目</option>
                        <option value="award">获奖</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                    <input type="text" placeholder="输入关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <input type="text" placeholder="输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果年度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部年度</option>
                        <option value="2024">2024年</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">成果状态</label>
                    <div class="flex space-x-2 mt-2">
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">已关联</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">待审核</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 成果管理区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">科研成果列表</h2>
                <div class="flex space-x-3">
                    <button onclick="openExportDialog()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出
                    </button>
                    <button onclick="openAddDialog()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        关联成果
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属项目</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发表/获得时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">基于深度学习的宁波市智能制造产业分析</div>
                                <div class="text-sm text-gray-500">宁波大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">论文</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造发展研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已关联</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="editItem('1')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="removeItem('1')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">一种智能制造的自动化控制系统</div>
                                <div class="text-sm text-gray-500">宁波职业技术学院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造关键技术研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已关联</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="editItem('2')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="removeItem('2')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新材料产业发展规划研究</div>
                                <div class="text-sm text-gray-500">宁波市科研院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市新材料产业研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-05</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="editItem('3')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="removeItem('3')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">浙江省科技进步奖</div>
                                <div class="text-sm text-gray-500">宁波大学</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">获奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造关键技术研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已关联</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button onclick="editItem('4')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="removeItem('4')" class="text-red-600 hover:text-red-900">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联成果弹窗 -->
    <div id="addDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">关联科研成果</h3>
                    <button onclick="closeAddDialog()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="flex space-x-3">
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="paper">论文</option>
                                <option value="patent">专利</option>
                                <option value="project">项目</option>
                                <option value="award">获奖</option>
                            </select>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">成果名称</label>
                            <input type="text" placeholder="输入关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div class="flex items-end">
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                搜索
                            </button>
                        </div>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h4 class="text-sm font-medium text-gray-700">搜索结果</h4>
                            <span class="text-xs text-gray-500">共找到 12 条记录</span>
                        </div>
                        <div class="space-y-3 max-h-60 overflow-y-auto">
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">宁波市智能制造产业现状分析</div>
                                    <div class="text-xs text-gray-500">论文 | 2023-04-10</div>
                                </div>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">关联</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">智能制造自动化控制系统V1.0</div>
                                    <div class="text-xs text-gray-500">专利 | 2023-07-15</div>
                                </div>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">关联</button>
                            </div>
                            <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">宁波市新材料产业发展规划</div>
                                    <div class="text-xs text-gray-500">项目 | 2023-09-20</div>
                                </div>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">关联</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddDialog()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        确认关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 成果详情弹窗 -->
    <div id="detailDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科研成果详情</h3>
                    <button onclick="closeDetailDialog()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">基本信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="text-gray-600 w-24">成果名称：</span>
                                <span class="text-gray-900 flex-1">基于深度学习的宁波市智能制造产业分析</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">成果类型：</span>
                                <span class="text-gray-900 flex-1">论文</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">所属项目：</span>
                                <span class="text-gray-900 flex-1">宁波市智能制造发展研究</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">发表时间：</span>
                                <span class="text-gray-900 flex-1">2023-05-15</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">状态：</span>
                                <span class="text-gray-900 flex-1">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已关联</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-800 mb-3">关联信息</h4>
                        <div class="space-y-3 text-sm">
                            <div class="flex">
                                <span class="text-gray-600 w-24">科研人员：</span>
                                <span class="text-gray-900 flex-1">张研究员</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">所属单位：</span>
                                <span class="text-gray-900 flex-1">宁波大学</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">关联时间：</span>
                                <span class="text-gray-900 flex-1">2023-06-10</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-600 w-24">关联人：</span>
                                <span class="text-gray-900 flex-1">李管理员</span>
                            </div>
                        </div>
                    </div>
                    <div class="md:col-span-2">
                        <h4 class="text-md font-medium text-gray-800 mb-3">成果简介</h4>
                        <div class="bg-gray-50 p-4 rounded-md text-sm text-gray-700">
                            <p>本研究基于深度学习技术，对宁波市智能制造产业发展现状进行了全面分析。通过构建多维度评价指标体系，结合企业调研数据，建立了智能制造产业发展水平评估模型。研究结果表明，宁波市智能制造产业在技术创新能力、产业集聚度等方面具有明显优势，但在高端人才引进、产业链协同等方面仍需加强。本研究为宁波市智能制造产业政策制定提供了数据支持和决策参考。</p>
                        </div>
                    </div>
                    <div class="md:col-span-2">
                        <h4 class="text-md font-medium text-gray-800 mb-3">附件材料</h4>
                        <div class="space-y-2">
                            <div class="flex items-center p-3 border border-gray-200 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm text-gray-700 flex-1">论文全文.pdf</span>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                            </div>
                            <div class="flex items-center p-3 border border-gray-200 rounded-md">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm text-gray-700 flex-1">数据分析报告.xlsx</span>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeDetailDialog()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导出设置弹窗 -->
    <div id="exportDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">导出设置</h3>
                    <button onclick="closeExportDialog()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出格式</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportFormat" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">Excel (.xlsx)</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="exportFormat" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">PDF (.pdf)</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">导出字段</label>
                        <div class="space-y-2">
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">成果名称</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">成果类型</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                <span class="ml-2 text-sm text-gray-700">所属项目</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">发表/获得时间</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">状态</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeExportDialog()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        确认导出
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 关联成果弹窗控制
        function openAddDialog() {
            document.getElementById('addDialog').classList.remove('hidden');
        }
        function closeAddDialog() {
            document.getElementById('addDialog').classList.add('hidden');
        }

        // 成果详情弹窗控制
        function showDetail(id) {
            document.getElementById('detailDialog').classList.remove('hidden');
        }
        function closeDetailDialog() {
            document.getElementById('detailDialog').classList.add('hidden');
        }

        // 导出设置弹窗控制
        function openExportDialog() {
            document.getElementById('exportDialog').classList.remove('hidden');
        }
        function closeExportDialog() {
            document.getElementById('exportDialog').classList.add('hidden');
        }

        // 编辑项目
        function editItem(id) {
            alert('编辑项目 ID: ' + id);
        }

        // 移除项目
        function removeItem(id) {
            if (confirm('确定要移除这个成果关联吗？')) {
                alert('移除项目 ID: ' + id);
            }
        }

        // 点击弹窗外部关闭
        document.getElementById('addDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddDialog();
            }
        });
        document.getElementById('detailDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailDialog();
            }
        });
        document.getElementById('exportDialog').addEventListener('click', function(e) {
            if (e.target === this) {
                closeExportDialog();
            }
        });
    </script>
</body>
</html>