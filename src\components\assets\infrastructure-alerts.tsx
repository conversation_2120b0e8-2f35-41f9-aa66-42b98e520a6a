'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  AlertTriangle, 
  AlertCircle,
  Clock,
  ArrowDown,
  ArrowUpIcon,
  ArrowDownIcon,
  Bell,
  CheckCircle2Icon,
  XCircleIcon
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

// 模拟24小时告警数据
const alertTrendData = [
  { hour: '00', count: 3 },
  { hour: '01', count: 2 },
  { hour: '02', count: 1 },
  { hour: '03', count: 2 },
  { hour: '04', count: 4 },
  { hour: '05', count: 3 },
  { hour: '06', count: 2 },
  { hour: '07', count: 5 },
  { hour: '08', count: 8 },
  { hour: '09', count: 6 },
  { hour: '10', count: 4 },
  { hour: '11', count: 3 },
  { hour: '12', count: 4 },
  { hour: '13', count: 5 },
  { hour: '14', count: 3 },
  { hour: '15', count: 2 },
  { hour: '16', count: 4 },
  { hour: '17', count: 3 },
  { hour: '18', count: 2 },
  { hour: '19', count: 1 },
  { hour: '20', count: 2 },
  { hour: '21', count: 1 },
  { hour: '22', count: 2 },
  { hour: '23', count: 1 },
]

export function InfrastructureAlerts() {
  // 获取最大告警数用于计算高度比例
  const maxCount = Math.max(...alertTrendData.map(d => d.count))

  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-4">
          <CardTitle>告警中心</CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-red-50 text-red-600">
              <AlertCircle className="h-3 w-3 mr-1" />
              严重: 2
            </Badge>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-600">
              <AlertTriangle className="h-3 w-3 mr-1" />
              警告: 12
            </Badge>
          </div>
        </div>
        <Button variant="outline" size="sm">
          <Bell className="h-4 w-4 mr-2" />
          通知设置
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 告警趋势 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">24小时告警趋势</span>
              </div>
              <div className="flex items-center gap-2">
                <ArrowDown className="h-3 w-3 text-green-500" />
                <span className="text-sm font-medium text-green-600">-15%</span>
              </div>
            </div>
            
            {/* 趋势图表区域 */}
            <div className="relative h-[60px] bg-white rounded-lg p-2">
              <div className="absolute inset-0 flex items-end justify-between px-2">
                {alertTrendData.map((data, index) => (
                  <div
                    key={data.hour}
                    className="relative flex-1 mx-[1px]"
                  >
                    <div
                      className="absolute bottom-0 w-full bg-blue-400 rounded-t"
                      style={{
                        height: `${(data.count / maxCount) * 40}px`,
                        opacity: data.count / maxCount * 0.8 + 0.2
                      }}
                    />
                  </div>
                ))}
              </div>
              
              {/* 网格线 */}
              <div className="absolute inset-0">
                <div className="border-b border-gray-100" style={{ top: '25%' }} />
                <div className="border-b border-gray-100" style={{ top: '50%' }} />
                <div className="border-b border-gray-100" style={{ top: '75%' }} />
              </div>
            </div>

            {/* 时间刻度 */}
            <div className="flex items-center justify-between mt-2 text-xs text-gray-500 px-2">
              <span>00:00</span>
              <span>06:00</span>
              <span>12:00</span>
              <span>18:00</span>
              <span>24:00</span>
            </div>
          </div>

          {/* 最新告警列表 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">最新告警</span>
              <Button variant="link" size="sm" className="text-blue-500">
                查看全部
              </Button>
            </div>
            
            {/* 告警项目 */}
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <div className="font-medium text-red-600">存储系统容量告警</div>
                  <div className="text-sm text-gray-500">主存储系统可用空间低于10%</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">5分钟前</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="font-medium text-yellow-600">服务器CPU使用率过高</div>
                  <div className="text-sm text-gray-500">Web服务器群CPU使用率超过85%</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">18分钟前</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle2Icon className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium text-green-600">网络延迟告警已解除</div>
                  <div className="text-sm text-gray-500">核心交换机网络延迟恢复正常</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">32分钟前</div>
            </div>

            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <XCircleIcon className="h-5 w-5 text-red-500" />
                <div>
                  <div className="font-medium text-red-600">备份任务失败</div>
                  <div className="text-sm text-gray-500">数据库每日备份任务执行失败</div>
                </div>
              </div>
              <div className="text-sm text-gray-500">1小时前</div>
            </div>
          </div>

          {/* 告警统计 */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-3 bg-gray-50 rounded-lg text-center">
              <div className="text-sm text-gray-500">今日告警</div>
              <div className="text-xl font-semibold mt-1">24</div>
              <div className="text-xs text-red-500 mt-1">
                <ArrowUpIcon className="h-3 w-3 inline" />
                +8.5%
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg text-center">
              <div className="text-sm text-gray-500">待处理</div>
              <div className="text-xl font-semibold mt-1">14</div>
              <div className="text-xs text-yellow-500 mt-1">
                需要关注
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg text-center">
              <div className="text-sm text-gray-500">已解决</div>
              <div className="text-xl font-semibold mt-1">10</div>
              <div className="text-xs text-green-500 mt-1">
                <ArrowUpIcon className="h-3 w-3 inline" />
                +25%
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 