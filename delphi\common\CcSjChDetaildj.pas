unit CcSjChDetaildj;

interface

uses
  Classes;

type
  TCcSjChDetaildj = class
  private
    FSjchdetaildjid: Integer;
    FDdDetailid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FKsbm: string;
    FSjh: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FCm: string;
    FDds: Integer;
    FMjdj: double;
    FRmbdj: double;

    FZxdMs: string;
    FZxdYwMs: string;
    FZxdTxm: string;
    FZxdKsbm: string;

    FKsbh: string;
    FZxbh: string;
    FKsbj: string;
    FKslb: string;
    FYwYs: string;

    FChs_a: Integer;
    FChrq_a: string;
    FZxsl_a: Integer;
    FZxxjs_a: Integer;
    FZxbegin_a: Integer;
    FZxend_a: Integer;

    FShaocs_a: Integer;
    FShaocbz_a: string;
    FDuanzxs_a: Integer;
    FHxxh_a: Integer;
    FHxjs_a: Integer;
    FHth_a: string;
    FTdh_a: string;

    FChs_b: Integer;
    FChrq_b: string;
    FZxsl_b: Integer;
    FZxxjs_b: Integer;
    FZxbegin_b: Integer;
    FZxend_b: Integer;

    FHx_1: string;
    FHx_2: string;
    FHx_3: string;
    FHx_4: string;
    FHx_5: string;
    FHx_6: string;

    FShaocs_b: Integer;
    FShaocbz_b: string;
    FDuanzxs_b: Integer;
    FHxxh_b: Integer;
    FHxjs_b: Integer;
    FHth_b: string;
    FTdh_b: string;

    FChs_c: Integer;
    FChrq_c: string;
    FZxsl_c: Integer;
    FZxxjs_c: Integer;
    FZxbegin_c: Integer;
    FZxend_c: Integer;

    FShaocs_c: Integer;
    FShaocbz_c: string;
    FDuanzxs_c: Integer;
    FHxxh_c: Integer;
    FHxjs_c: Integer;
    FHth_c: string;
    FTdh_c: string;

    FChs_d: Integer;
    FChrq_d: string;
    FZxsl_d: Integer;
    FZxxjs_d: Integer;
    FZxbegin_d: Integer;
    FZxend_d: Integer;

    FShaocs_d: Integer;
    FShaocbz_d: string;
    FDuanzxs_d: Integer;
    FHxxh_d: Integer;
    FHxjs_d: Integer;
    FHth_d: string;
    FTdh_d: string;

    FChs_e: Integer;
    FChrq_e: string;
    FZxsl_e: Integer;
    FZxxjs_e: Integer;
    FZxbegin_e: Integer;
    FZxend_e: Integer;

    FShaocs_e: Integer;
    FShaocbz_e: string;
    FDuanzxs_e: Integer;
    FHxxh_e: Integer;
    FHxjs_e: Integer;
    FHth_e: string;
    FTdh_e: string;

    FChs_f: Integer;
    FChrq_f: string;
    FZxsl_f: Integer;
    FZxxjs_f: Integer;
    FZxbegin_f: Integer;
    FZxend_f: Integer;

    FShaocs_f: Integer;
    FShaocbz_f: string;
    FDuanzxs_f: Integer;
    FHxxh_f: Integer;
    FHxjs_f: Integer;
    FHth_f: string;
    FTdh_f: string;

    FPort: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FZxsl_a_1: Integer;
    FZxxjs_a_1: Integer;
    FZxbegin_a_1: Integer;
    FZxend_a_1: Integer;

    FZxsl_a_2: Integer;
    FZxxjs_a_2: Integer;
    FZxbegin_a_2: Integer;
    FZxend_a_2: Integer;

    FZxsl_a_3: Integer;
    FZxxjs_a_3: Integer;
    FZxbegin_a_3: Integer;
    FZxend_a_3: Integer;

    FZxsl_b_1: Integer;
    FZxxjs_b_1: Integer;
    FZxbegin_b_1: Integer;
    FZxend_b_1: Integer;

    FZxsl_b_2: Integer;
    FZxxjs_b_2: Integer;
    FZxbegin_b_2: Integer;
    FZxend_b_2: Integer;

    FZxsl_b_3: Integer;
    FZxxjs_b_3: Integer;
    FZxbegin_b_3: Integer;
    FZxend_b_3: Integer;

    FZxsl_c_1: Integer;
    FZxxjs_c_1: Integer;
    FZxbegin_c_1: Integer;
    FZxend_c_1: Integer;

    FZxsl_c_2: Integer;
    FZxxjs_c_2: Integer;
    FZxbegin_c_2: Integer;
    FZxend_c_2: Integer;

    FZxsl_c_3: Integer;
    FZxxjs_c_3: Integer;
    FZxbegin_c_3: Integer;
    FZxend_c_3: Integer;

    FZxsl_d_1: Integer;
    FZxxjs_d_1: Integer;
    FZxbegin_d_1: Integer;
    FZxend_d_1: Integer;

    FZxsl_d_2: Integer;
    FZxxjs_d_2: Integer;
    FZxbegin_d_2: Integer;
    FZxend_d_2: Integer;

    FZxsl_d_3: Integer;
    FZxxjs_d_3: Integer;
    FZxbegin_d_3: Integer;
    FZxend_d_3: Integer;

    FZxsl_e_1: Integer;
    FZxxjs_e_1: Integer;
    FZxbegin_e_1: Integer;
    FZxend_e_1: Integer;

    FZxsl_e_2: Integer;
    FZxxjs_e_2: Integer;
    FZxbegin_e_2: Integer;
    FZxend_e_2: Integer;

    FZxsl_e_3: Integer;
    FZxxjs_e_3: Integer;
    FZxbegin_e_3: Integer;
    FZxend_e_3: Integer;

    FZxsl_f_1: Integer;
    FZxxjs_f_1: Integer;
    FZxbegin_f_1: Integer;
    FZxend_f_1: Integer;

    FZxsl_f_2: Integer;
    FZxxjs_f_2: Integer;
    FZxbegin_f_2: Integer;
    FZxend_f_2: Integer;

    FZxsl_f_3: Integer;
    FZxxjs_f_3: Integer;
    FZxbegin_f_3: Integer;
    FZxend_f_3: Integer;

    FTm: string;
    FLineNumber: string;
  public
    property Sjchdetaildjid: Integer read FSjchdetaildjid write FSjchdetaildjid;
    property DdDetailid: Integer read FDdDetailid write FDdDetailid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Cm: string read FCm write FCm;

    property Dds: Integer read FDds write FDds;

    property Mjdj: double read FMjdj write FMjdj;
    property Rmbdj: double read FRmbdj write FRmbdj;

    property ZxdMs: string read FZxdMs write FZxdMs;
    property ZxdYwMs: string read FZxdYwMs write FZxdYwMs;

    property ZxdTxm: string read FZxdTxm write FZxdTxm;
    property ZxdKsbm: string read FZxdKsbm write FZxdKsbm;

    property Ksbh: string read FKsbh write FKsbh;
    property Zxbh: string read FZxbh write FZxbh;
    property Ksbj: string read FKsbj write FKsbj;
    property Kslb: string read FKslb write FKslb;
    property YwYs: string read FYwYs write FYwYs;

    property Chs_a: Integer read FChs_a write FChs_a;
    property Chrq_a: string read FChrq_a write FChrq_a;
    property Zxsl_a: Integer read FZxsl_a write FZxsl_a;
    property Zxxjs_a: Integer read FZxxjs_a write FZxxjs_a;
    property Zxbegin_a: Integer read FZxbegin_a write FZxbegin_a;
    property Zxend_a: Integer read FZxend_a write FZxend_a;

    property Shaocs_a: Integer read FShaocs_a write FShaocs_a;
    property Shaocbz_a: string read FShaocbz_a write FShaocbz_a;
    property Duanzxs_a: Integer read FDuanzxs_a write FDuanzxs_a;
    property Hxxh_a: Integer read FHxxh_a write FHxxh_a;
    property Hxjs_a: Integer read FHxjs_a write FHxjs_a;
    property Hth_a: string read FHth_a write FHth_a;
    property Tdh_a: string read FTdh_a write FTdh_a;

    property Chs_b: Integer read FChs_b write FChs_b;
    property Chrq_b: string read FChrq_b write FChrq_b;
    property Zxsl_b: Integer read FZxsl_b write FZxsl_b;
    property Zxxjs_b: Integer read FZxxjs_b write FZxxjs_b;
    property Zxbegin_b: Integer read FZxbegin_b write FZxbegin_b;
    property Zxend_b: Integer read FZxend_b write FZxend_b;

    property Shaocs_b: Integer read FShaocs_b write FShaocs_b;
    property Shaocbz_b: string read FShaocbz_b write FShaocbz_b;
    property Duanzxs_b: Integer read FDuanzxs_b write FDuanzxs_b;
    property Hxxh_b: Integer read FHxxh_b write FHxxh_b;
    property Hxjs_b: Integer read FHxjs_b write FHxjs_b;
    property Hth_b: string read FHth_b write FHth_b;
    property Tdh_b: string read FTdh_b write FTdh_b;

    property Chs_c: Integer read FChs_c write FChs_c;
    property Chrq_c: string read FChrq_c write FChrq_c;
    property Zxsl_c: Integer read FZxsl_c write FZxsl_c;
    property Zxxjs_c: Integer read FZxxjs_c write FZxxjs_c;
    property Zxbegin_c: Integer read FZxbegin_c write FZxbegin_c;
    property Zxend_c: Integer read FZxend_c write FZxend_c;

    property Shaocs_c: Integer read FShaocs_c write FShaocs_c;
    property Shaocbz_c: string read FShaocbz_c write FShaocbz_c;
    property Duanzxs_c: Integer read FDuanzxs_c write FDuanzxs_c;
    property Hxxh_c: Integer read FHxxh_c write FHxxh_c;
    property Hxjs_c: Integer read FHxjs_c write FHxjs_c;
    property Hth_c: string read FHth_c write FHth_c;
    property Tdh_c: string read FTdh_c write FTdh_c;

    property Chs_d: Integer read FChs_d write FChs_d;
    property Chrq_d: string read FChrq_d write FChrq_d;
    property Zxsl_d: Integer read FZxsl_d write FZxsl_d;
    property Zxxjs_d: Integer read FZxxjs_d write FZxxjs_d;
    property Zxbegin_d: Integer read FZxbegin_d write FZxbegin_d;
    property Zxend_d: Integer read FZxend_d write FZxend_d;

    property Shaocs_d: Integer read FShaocs_d write FShaocs_d;
    property Shaocbz_d: string read FShaocbz_d write FShaocbz_d;
    property Duanzxs_d: Integer read FDuanzxs_d write FDuanzxs_d;
    property Hxxh_d: Integer read FHxxh_d write FHxxh_d;
    property Hxjs_d: Integer read FHxjs_d write FHxjs_d;
    property Hth_d: string read FHth_d write FHth_d;
    property Tdh_d: string read FTdh_d write FTdh_d;

    property Chs_e: Integer read FChs_e write FChs_e;
    property Chrq_e: string read FChrq_e write FChrq_e;
    property Zxsl_e: Integer read FZxsl_e write FZxsl_e;
    property Zxxjs_e: Integer read FZxxjs_e write FZxxjs_e;
    property Zxbegin_e: Integer read FZxbegin_e write FZxbegin_e;
    property Zxend_e: Integer read FZxend_e write FZxend_e;

    property Shaocs_e: Integer read FShaocs_e write FShaocs_e;
    property Shaocbz_e: string read FShaocbz_e write FShaocbz_e;
    property Duanzxs_e: Integer read FDuanzxs_e write FDuanzxs_e;
    property Hxxh_e: Integer read FHxxh_e write FHxxh_e;
    property Hxjs_e: Integer read FHxjs_e write FHxjs_e;
    property Hth_e: string read FHth_e write FHth_e;
    property Tdh_e: string read FTdh_e write FTdh_e;

    property Chs_f: Integer read FChs_f write FChs_f;
    property Chrq_f: string read FChrq_f write FChrq_f;
    property Zxsl_f: Integer read FZxsl_f write FZxsl_f;
    property Zxxjs_f: Integer read FZxxjs_f write FZxxjs_f;
    property Zxbegin_f: Integer read FZxbegin_f write FZxbegin_f;
    property Zxend_f: Integer read FZxend_f write FZxend_f;

    property Shaocs_f: Integer read FShaocs_f write FShaocs_f;
    property Shaocbz_f: string read FShaocbz_f write FShaocbz_f;
    property Duanzxs_f: Integer read FDuanzxs_f write FDuanzxs_f;
    property Hxxh_f: Integer read FHxxh_f write FHxxh_f;
    property Hxjs_f: Integer read FHxjs_f write FHxjs_f;
    property Hth_f: string read FHth_f write FHth_f;
    property Tdh_f: string read FTdh_f write FTdh_f;

    property Port: string read FPort write FPort;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

    property Zxsl_a_1: Integer read FZxsl_a_1 write FZxsl_a_1;
    property Zxxjs_a_1: Integer read FZxxjs_a_1 write FZxxjs_a_1;
    property Zxbegin_a_1: Integer read FZxbegin_a_1 write FZxbegin_a_1;
    property Zxend_a_1: Integer read FZxend_a_1 write FZxend_a_1;

    property Zxsl_a_2: Integer read FZxsl_a_2 write FZxsl_a_2;
    property Zxxjs_a_2: Integer read FZxxjs_a_2 write FZxxjs_a_2;
    property Zxbegin_a_2: Integer read FZxbegin_a_2 write FZxbegin_a_2;
    property Zxend_a_2: Integer read FZxend_a_2 write FZxend_a_2;

    property Zxsl_a_3: Integer read FZxsl_a_3 write FZxsl_a_3;
    property Zxxjs_a_3: Integer read FZxxjs_a_3 write FZxxjs_a_3;
    property Zxbegin_a_3: Integer read FZxbegin_a_3 write FZxbegin_a_3;
    property Zxend_a_3: Integer read FZxend_a_3 write FZxend_a_3;

    property Zxsl_b_1: Integer read FZxsl_b_1 write FZxsl_b_1;
    property Zxxjs_b_1: Integer read FZxxjs_b_1 write FZxxjs_b_1;
    property Zxbegin_b_1: Integer read FZxbegin_b_1 write FZxbegin_b_1;
    property Zxend_b_1: Integer read FZxend_b_1 write FZxend_b_1;

    property Zxsl_b_2: Integer read FZxsl_b_2 write FZxsl_b_2;
    property Zxxjs_b_2: Integer read FZxxjs_b_2 write FZxxjs_b_2;
    property Zxbegin_b_2: Integer read FZxbegin_b_2 write FZxbegin_b_2;
    property Zxend_b_2: Integer read FZxend_b_2 write FZxend_b_2;

    property Zxsl_b_3: Integer read FZxsl_b_3 write FZxsl_b_3;
    property Zxxjs_b_3: Integer read FZxxjs_b_3 write FZxxjs_b_3;
    property Zxbegin_b_3: Integer read FZxbegin_b_3 write FZxbegin_b_3;
    property Zxend_b_3: Integer read FZxend_b_3 write FZxend_b_3;

    property Zxsl_c_1: Integer read FZxsl_c_1 write FZxsl_c_1;
    property Zxxjs_c_1: Integer read FZxxjs_c_1 write FZxxjs_c_1;
    property Zxbegin_c_1: Integer read FZxbegin_c_1 write FZxbegin_c_1;
    property Zxend_c_1: Integer read FZxend_c_1 write FZxend_c_1;

    property Zxsl_c_2: Integer read FZxsl_c_2 write FZxsl_c_2;
    property Zxxjs_c_2: Integer read FZxxjs_c_2 write FZxxjs_c_2;
    property Zxbegin_c_2: Integer read FZxbegin_c_2 write FZxbegin_c_2;
    property Zxend_c_2: Integer read FZxend_c_2 write FZxend_c_2;

    property Zxsl_c_3: Integer read FZxsl_c_3 write FZxsl_c_3;
    property Zxxjs_c_3: Integer read FZxxjs_c_3 write FZxxjs_c_3;
    property Zxbegin_c_3: Integer read FZxbegin_c_3 write FZxbegin_c_3;
    property Zxend_c_3: Integer read FZxend_c_3 write FZxend_c_3;

    property Zxsl_d_1: Integer read FZxsl_d_1 write FZxsl_d_1;
    property Zxxjs_d_1: Integer read FZxxjs_d_1 write FZxxjs_d_1;
    property Zxbegin_d_1: Integer read FZxbegin_d_1 write FZxbegin_d_1;
    property Zxend_d_1: Integer read FZxend_d_1 write FZxend_d_1;

    property Zxsl_d_2: Integer read FZxsl_d_2 write FZxsl_d_2;
    property Zxxjs_d_2: Integer read FZxxjs_d_2 write FZxxjs_d_2;
    property Zxbegin_d_2: Integer read FZxbegin_d_2 write FZxbegin_d_2;
    property Zxend_d_2: Integer read FZxend_d_2 write FZxend_d_2;

    property Zxsl_d_3: Integer read FZxsl_d_3 write FZxsl_d_3;
    property Zxxjs_d_3: Integer read FZxxjs_d_3 write FZxxjs_d_3;
    property Zxbegin_d_3: Integer read FZxbegin_d_3 write FZxbegin_d_3;
    property Zxend_d_3: Integer read FZxend_d_3 write FZxend_d_3;

    property Zxsl_e_1: Integer read FZxsl_e_1 write FZxsl_e_1;
    property Zxxjs_e_1: Integer read FZxxjs_e_1 write FZxxjs_e_1;
    property Zxbegin_e_1: Integer read FZxbegin_e_1 write FZxbegin_e_1;
    property Zxend_e_1: Integer read FZxend_e_1 write FZxend_e_1;

    property Zxsl_e_2: Integer read FZxsl_e_2 write FZxsl_e_2;
    property Zxxjs_e_2: Integer read FZxxjs_e_2 write FZxxjs_e_2;
    property Zxbegin_e_2: Integer read FZxbegin_e_2 write FZxbegin_e_2;
    property Zxend_e_2: Integer read FZxend_e_2 write FZxend_e_2;

    property Zxsl_e_3: Integer read FZxsl_e_3 write FZxsl_e_3;
    property Zxxjs_e_3: Integer read FZxxjs_e_3 write FZxxjs_e_3;
    property Zxbegin_e_3: Integer read FZxbegin_e_3 write FZxbegin_e_3;
    property Zxend_e_3: Integer read FZxend_e_3 write FZxend_e_3;

    property Zxsl_f_1: Integer read FZxsl_f_1 write FZxsl_f_1;
    property Zxxjs_f_1: Integer read FZxxjs_f_1 write FZxxjs_f_1;
    property Zxbegin_f_1: Integer read FZxbegin_f_1 write FZxbegin_f_1;
    property Zxend_f_1: Integer read FZxend_f_1 write FZxend_f_1;

    property Zxsl_f_2: Integer read FZxsl_f_2 write FZxsl_f_2;
    property Zxxjs_f_2: Integer read FZxxjs_f_2 write FZxxjs_f_2;
    property Zxbegin_f_2: Integer read FZxbegin_f_2 write FZxbegin_f_2;
    property Zxend_f_2: Integer read FZxend_f_2 write FZxend_f_2;

    property Zxsl_f_3: Integer read FZxsl_f_3 write FZxsl_f_3;
    property Zxxjs_f_3: Integer read FZxxjs_f_3 write FZxxjs_f_3;
    property Zxbegin_f_3: Integer read FZxbegin_f_3 write FZxbegin_f_3;
    property Zxend_f_3: Integer read FZxend_f_3 write FZxend_f_3;

    property Hx_1: String read FHx_1 write FHx_1;
    property Hx_2: String read FHx_2 write FHx_2;
    property Hx_3: String read FHx_3 write FHx_3;
    property Hx_4: String read FHx_4 write FHx_4;
    property Hx_5: String read FHx_5 write FHx_5;
    property Hx_6: String read FHx_6 write FHx_6;

    property Tm: string read FTm write FTm;
    property LineNumber: string read FLineNumber write FLineNumber;
  end;

implementation

end.
