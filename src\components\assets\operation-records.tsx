'use client'

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  MoreVertical,
  AlertCircle,
  CheckCircle2Icon,
  Clock,
  FileText,
  MessageSquare,
  History,
  UserCircle2,
  Tag,
  Link2
} from "lucide-react"

// 状态配置
const statusConfig = {
  pending: { label: '待处理', class: 'bg-yellow-50 text-yellow-600' },
  processing: { label: '处理中', class: 'bg-blue-50 text-blue-600' },
  completed: { label: '已完成', class: 'bg-green-50 text-green-600' },
  failed: { label: '处理失败', class: 'bg-red-50 text-red-600' }
}

// 优先级配置
const priorityConfig = {
  high: { label: '高', class: 'bg-red-50 text-red-600' },
  medium: { label: '中', class: 'bg-yellow-50 text-yellow-600' },
  low: { label: '低', class: 'bg-blue-50 text-blue-600' }
}

// 模拟运维记录数据
const operationRecords = [
  {
    id: "OPS-2024-0328",
    title: "预算管理系统数据同步延迟",
    type: "故障处理",
    priority: "high",
    status: "completed",
    handler: "张工",
    team: "数据库组",
    createTime: "2024-03-28 09:15:23",
    updateTime: "2024-03-28 09:45:12",
    responseTime: "3分钟",
    duration: "30分钟",
    relatedSystem: "预算管理系统",
  },
  {
    id: "OPS-2024-0327",
    title: "非税收入系统服务器扩容",
    type: "变更管理",
    priority: "medium",
    status: "processing",
    handler: "李工",
    team: "平台组",
    createTime: "2024-03-28 10:00:00",
    updateTime: "2024-03-28 10:25:18",
    responseTime: "5分钟",
    duration: "进行中",
    relatedSystem: "非税收入系统",
  },
  {
    id: "OPS-2024-0326",
    title: "财政一体化平台版本升级",
    type: "发布部署",
    priority: "medium",
    status: "completed",
    handler: "王工",
    team: "应用组",
    createTime: "2024-03-28 08:30:00",
    updateTime: "2024-03-28 09:15:00",
    responseTime: "即时",
    duration: "45分钟",
    relatedSystem: "财政一体化平台",
  },
  {
    id: "OPS-2024-0325",
    title: "国库支付系统负载优化",
    type: "变更管理",
    priority: "low",
    status: "completed",
    handler: "赵工",
    team: "网络组",
    createTime: "2024-03-28 07:45:00",
    updateTime: "2024-03-28 08:15:00",
    responseTime: "8分钟",
    duration: "30分钟",
    relatedSystem: "国库支付系统",
  },
  {
    id: "OPS-2024-0324",
    title: "政府采购系统安全加固",
    type: "发布部署",
    priority: "high",
    status: "completed",
    handler: "刘工",
    team: "安全组",
    createTime: "2024-03-27 16:30:00",
    updateTime: "2024-03-27 17:00:00",
    responseTime: "2分钟",
    duration: "30分钟",
    relatedSystem: "政府采购系统",
  },
  {
    id: "OPS-2024-0323",
    title: "资产管理系统数据备份",
    type: "日常维护",
    priority: "medium",
    status: "completed",
    handler: "陈工",
    team: "运维组",
    createTime: "2024-03-27 15:00:00",
    updateTime: "2024-03-27 15:30:00",
    responseTime: "即时",
    duration: "30分钟",
    relatedSystem: "资产管理系统",
  },
  {
    id: "OPS-2024-0322",
    title: "决算管理系统性能优化",
    type: "变更管理",
    priority: "medium",
    status: "completed",
    handler: "周工",
    team: "应用组",
    createTime: "2024-03-27 14:00:00",
    updateTime: "2024-03-27 14:45:00",
    responseTime: "5分钟",
    duration: "45分钟",
    relatedSystem: "决算管理系统",
  },
  {
    id: "OPS-2024-0321",
    title: "项目支出绩效系统更新",
    type: "发布部署",
    priority: "medium",
    status: "completed",
    handler: "吴工",
    team: "应用组",
    createTime: "2024-03-27 11:00:00",
    updateTime: "2024-03-27 11:40:00",
    responseTime: "即时",
    duration: "40分钟",
    relatedSystem: "项目支出绩效系统",
  },
  {
    id: "OPS-2024-0320",
    title: "预算单位账务系统维护",
    type: "日常维护",
    priority: "low",
    status: "completed",
    handler: "郑工",
    team: "运维组",
    createTime: "2024-03-27 10:00:00",
    updateTime: "2024-03-27 10:30:00",
    responseTime: "即时",
    duration: "30分钟",
    relatedSystem: "预算单位账务系统",
  },
  {
    id: "OPS-2024-0319",
    title: "财政专项资金系统升级",
    type: "发布部署",
    priority: "high",
    status: "processing",
    handler: "孙工",
    team: "应用组",
    createTime: "2024-03-27 09:00:00",
    updateTime: "2024-03-27 09:45:00",
    responseTime: "3分钟",
    duration: "进行中",
    relatedSystem: "财政专项资金系统",
  }
]

export function OperationRecords() {
  return (
    <div>
      <Table>
        <TableHeader>
          <TableRow className="bg-[#F8FAFC] hover:bg-[#F8FAFC]">
            <TableHead className="text-[#666]">工单信息</TableHead>
            <TableHead className="text-[#666]">类型</TableHead>
            <TableHead className="text-[#666]">优先级</TableHead>
            <TableHead className="text-[#666]">状态</TableHead>
            <TableHead className="text-[#666]">处理人</TableHead>
            <TableHead className="text-[#666]">响应时间</TableHead>
            <TableHead className="text-[#666]">处理时长</TableHead>
            <TableHead className="text-[#666]">相关系统</TableHead>
            <TableHead className="text-[#666] text-right">操作</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {operationRecords.map((record) => (
            <TableRow key={record.id} className="hover:bg-[#F0F7FF]">
              <TableCell>
                <div className="space-y-1">
                  <div className="font-medium">{record.title}</div>
                  <div className="text-sm text-gray-500">{record.id}</div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline" className="bg-gray-50">
                  {record.type}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge className={priorityConfig[record.priority].class}>
                  {priorityConfig[record.priority].label}
                </Badge>
              </TableCell>
              <TableCell>
                <Badge className={statusConfig[record.status].class}>
                  {statusConfig[record.status].label}
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-2">
                  <UserCircle2 className="h-4 w-4 text-gray-400" />
                  <div>
                    <div className="font-medium">{record.handler}</div>
                    <div className="text-sm text-gray-500">{record.team}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{record.responseTime}</TableCell>
              <TableCell>{record.duration}</TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Link2 className="h-3 w-3 text-gray-400" />
                  <span className="text-sm">{record.relatedSystem}</span>
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[160px]">
                    <DropdownMenuLabel>工单操作</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <FileText className="h-4 w-4 mr-2" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <MessageSquare className="h-4 w-4 mr-2" />
                      添加备注
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <History className="h-4 w-4 mr-2" />
                      处理记录
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem>
                      <Tag className="h-4 w-4 mr-2" />
                      更新标签
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* 分页控件 */}
      <div className="flex items-center justify-between px-4 py-4 border-t border-[#E5E9EF]">
        <div className="text-sm text-gray-500">
          共 1,286 条记录
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            1
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            2
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            3
          </Button>
          <span className="mx-2">...</span>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            42
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
} 