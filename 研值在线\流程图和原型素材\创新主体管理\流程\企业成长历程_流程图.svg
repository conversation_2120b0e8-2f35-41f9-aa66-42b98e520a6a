<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">企业成长历程数据处理与展示流程</text>

  <!-- 阶段一：数据采集与处理 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据采集与处理</text>
  
  <!-- 节点1: 多源数据拉取 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据拉取</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">国家、省、市认定库</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">企业内部研发机构登记库</text>
  </g>

  <!-- 节点2: 数据清洗与序列化 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据清洗与序列化</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">多源数据清洗、去重</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">时间序列化处理</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 350 170 Q 400 170 450 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计分析与计算 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计分析与计算</text>

  <!-- 节点3: 统计服务聚合计算 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计服务聚合计算</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">认定事件与企业研发投入、知识产权</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">新增平台等关键指标聚合</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 575 210 Q 575 260 650 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点4: 时间轴与阶段视图 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">时间轴与阶段视图</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">根据筛选条件加载数据</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">渲染里程碑节点及指标卡片</text>
  </g>

  <!-- 节点5: 用户交互下钻 -->
  <g transform="translate(550, 490)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互下钻</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">请求认定事件详细信息</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">记录访问日志</text>
  </g>

  <!-- 节点6: 雷达对比视图 -->
  <g transform="translate(900, 490)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">雷达对比视图</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">目标企业与同类均值对比</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">动态更新维度评分</text>
  </g>

  <!-- 连接线 3 -> 4,5,6 -->
  <path d="M 600 390 C 500 430, 400 460, 325 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 390 Q 650 440 675 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 390 C 800 430, 900 460, 1025 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据归档与分析 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据归档与分析</text>
  
  <!-- 节点7: 日志归档与统计分析 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">日志归档与统计分析</text>
      <text x="200" y="55" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">认定数据归档</tspan>
        <tspan dx="40">用户操作日志</tspan>
        <tspan dx="40">报告生成</tspan>
        <tspan dx="40">功能优化</tspan>
      </text>
  </g>

  <!-- 连接线 4,5,6 -> 7 -->
  <path d="M 325 570 C 400 600, 500 640, 550 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 675 570 Q 675 620 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1025 570 C 950 600, 850 640, 850 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <path d="M 500 710 C 200 750, 100 600, 150 390" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="250" y="780" text-anchor="middle" font-size="12" fill="#666">数据反馈优化</text>

</svg>