<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目总览分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">项目总览分析</h1>
        <p class="text-gray-600 mb-6">全市科技项目综合数据可视化分析平台</p>

        <!-- 核心指标区 -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">项目总数</p>
                        <p class="text-2xl font-bold">1,245</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-green-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">在研项目</p>
                        <p class="text-2xl font-bold">856</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">累计经费(万元)</p>
                        <p class="text-2xl font-bold">28,560</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">年度支出(万元)</p>
                        <p class="text-2xl font-bold">12,340</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-indigo-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">承担单位</p>
                        <p class="text-2xl font-bold">342</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center">
                    <div class="bg-pink-100 p-2 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">产学研项目</p>
                        <p class="text-2xl font-bold">567</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option>科技计划项目</option>
                        <option>创新主体自研</option>
                        <option>高新投资项目</option>
                        <option>产学研合作</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">区域分布</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部区域</option>
                        <option>海曙区</option>
                        <option>江北区</option>
                        <option>鄞州区</option>
                        <option>高新区</option>
                        <option>余姚市</option>
                        <option>慈溪市</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option>申报中</option>
                        <option>立项</option>
                        <option>在研</option>
                        <option>结题</option>
                        <option>验收</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 多维度分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 项目类型分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">项目类型分布</h3>
                    <div class="flex space-x-2">
                        <button onclick="switchChartType('typeChart', 'pie')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">饼图</button>
                        <button onclick="switchChartType('typeChart', 'bar')" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">柱状图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="typeChart"></canvas>
                </div>
            </div>

            <!-- 区域分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">区域分布</h3>
                    <div class="flex space-x-2">
                        <button onclick="switchChartType('regionChart', 'doughnut')" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">环形图</button>
                        <button onclick="switchChartType('regionChart', 'bar')" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">柱状图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="regionChart"></canvas>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 经费趋势 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">经费投入趋势</h3>
                <div class="h-80">
                    <canvas id="fundingChart"></canvas>
                </div>
            </div>

            <!-- 承担单位类型 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">承担单位类型</h3>
                <div class="h-80">
                    <canvas id="orgTypeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 项目进展阶段 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">项目进展阶段</h3>
            <div class="h-96">
                <canvas id="progressChart"></canvas>
            </div>
        </div>

        <!-- 项目列表 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目列表</h3>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出
                        </button>
                        <button onclick="openProjectModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            新增项目
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在区域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费(万元)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2024-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能网联汽车关键技术研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技计划项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波吉利研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄞州区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1,200</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showProjectDetail('1')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2024-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新型半导体材料产业化</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新投资项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波中芯集成电路</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2,500</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">立项</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showProjectDetail('2')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2024-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">海洋生物医药研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">产学研合作</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">江北区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">800</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">中期检查</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showProjectDetail('3')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2024-004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">智能家居控制系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">创新主体自研</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波方太厨具</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">慈溪市</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">结题</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showProjectDetail('4')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2024-005</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">港口智能调度系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技计划项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波港集团</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北仑区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1,500</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showProjectDetail('5')" class="text-blue-600 hover:text-blue-900 mr-3">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 1,245 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">项目详情</h3>
                <button onclick="closeModal('projectDetailModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 基本信息 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">项目编号</p>
                                <p class="text-sm font-medium text-gray-900">NB2024-001</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目名称</p>
                                <p class="text-sm font-medium text-gray-900">智能网联汽车关键技术研发</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目类型</p>
                                <p class="text-sm font-medium text-gray-900">科技计划项目</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">承担单位</p>
                                <p class="text-sm font-medium text-gray-900">宁波吉利研究院</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">所在区域</p>
                                <p class="text-sm font-medium text-gray-900">鄞州区</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目状态</p>
                                <p class="text-sm font-medium text-gray-900">在研</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">开始日期</p>
                                <p class="text-sm font-medium text-gray-900">2024-01-15</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">预计结束</p>
                                <p class="text-sm font-medium text-gray-900">2025-12-31</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目简介 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">项目简介</h4>
                        <p class="text-sm text-gray-700">
                            本项目旨在研发智能网联汽车的关键技术，包括车载智能终端、车路协同通信、高精度定位等核心技术，构建智能网联汽车技术体系，推动宁波市汽车产业转型升级。项目由宁波吉利研究院牵头，联合宁波大学、中科院宁波材料所等多家单位共同承担。
                        </p>
                    </div>
                    
                    <!-- 团队成员 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">团队成员</h4>
                        <div class="space-y-4">
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <span class="text-blue-600 font-medium">张</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">张明远</p>
                                    <p class="text-xs text-gray-500">项目负责人/高级工程师</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <span class="text-green-600 font-medium">李</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">李静</p>
                                    <p class="text-xs text-gray-500">技术负责人/研究员</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                                    <span class="text-purple-600 font-medium">王</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">王海涛</p>
                                    <p class="text-xs text-gray-500">软件开发工程师</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 经费与成果 -->
                <div>
                    <!-- 经费结构 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">经费结构</h4>
                        <div class="h-48">
                            <canvas id="projectFundingChart"></canvas>
                        </div>
                        <div class="mt-4 space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">财政拨款</span>
                                <span class="text-sm font-medium">800万元</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">单位自筹</span>
                                <span class="text-sm font-medium">400万元</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 项目成果 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">项目成果</h4>
                        <div class="space-y-3">
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <p class="text-sm text-gray-700">申请发明专利5项</p>
                            </div>
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <p class="text-sm text-gray-700">发表SCI论文3篇</p>
                            </div>
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                <p class="text-sm text-gray-700">开发原型系统1套</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end mt-6">
                <button onclick="closeModal('projectDetailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <!-- 新增项目弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">新增项目</h3>
                <button onclick="closeModal('projectModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">请选择项目类型</option>
                            <option>科技计划项目</option>
                            <option>创新主体自研</option>
                            <option>高新投资项目</option>
                            <option>产学研合作</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">承担单位</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入承担单位">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">所在区域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">请选择区域</option>
                            <option>海曙区</option>
                            <option>江北区</option>
                            <option>鄞州区</option>
                            <option>高新区</option>
                            <option>余姚市</option>
                            <option>慈溪市</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">预计结束</label>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">财政拨款(万元)</label>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入金额">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">单位自筹(万元)</label>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入金额">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目简介</label>
                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目简介"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('projectModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function showProjectDetail(id) {
            openModal('projectDetailModal');
        }

        function openProjectModal() {
            openModal('projectModal');
        }

        // 切换图表类型
        function switchChartType(chartId, type) {
            const chart = window[chartId];
            if (chart) {
                chart.destroy();
                initChart(chartId, type);
            }
        }

        // 初始化所有图表
        document.addEventListener('DOMContentLoaded', function() {
            // 项目类型分布图
            initChart('typeChart', 'pie');
            
            // 区域分布图
            initChart('regionChart', 'doughnut');
            
            // 经费趋势图
            const fundingCtx = document.getElementById('fundingChart').getContext('2d');
            new Chart(fundingCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023', '2024'],
                    datasets: [{
                        label: '经费投入(万元)',
                        data: [8500, 10200, 15600, 18700, 23400],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 承担单位类型图
            const orgTypeCtx = document.getElementById('orgTypeChart').getContext('2d');
            new Chart(orgTypeCtx, {
                type: 'bar',
                data: {
                    labels: ['企业', '高校', '科研院所', '政府机构', '其他'],
                    datasets: [{
                        label: '数量',
                        data: [856, 342, 156, 78, 45],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 项目进展阶段图
            const progressCtx = document.getElementById('progressChart').getContext('2d');
            new Chart(progressCtx, {
                type: 'radar',
                data: {
                    labels: ['申报', '立项', '中期', '结题', '验收', '成果转化'],
                    datasets: [{
                        label: '项目数量',
                        data: [1245, 856, 523, 387, 298, 141],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: '#3B82F6',
                        pointBackgroundColor: '#3B82F6',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
            
            // 项目详情中的经费结构图
            const projectFundingCtx = document.getElementById('projectFundingChart').getContext('2d');
            new Chart(projectFundingCtx, {
                type: 'doughnut',
                data: {
                    labels: ['财政拨款', '单位自筹'],
                    datasets: [{
                        data: [800, 400],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // 点击弹窗外部关闭
            document.getElementById('projectDetailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('projectDetailModal');
                }
            });
            
            document.getElementById('projectModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('projectModal');
                }
            });
            
            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!document.getElementById('projectDetailModal').classList.contains('hidden')) {
                        closeModal('projectDetailModal');
                    }
                    if (!document.getElementById('projectModal').classList.contains('hidden')) {
                        closeModal('projectModal');
                    }
                }
            });
        });
        
        // 初始化图表函数
        function initChart(chartId, type) {
            const ctx = document.getElementById(chartId).getContext('2d');
            let config = {};
            
            if (chartId === 'typeChart') {
                config = {
                    type: type,
                    data: {
                        labels: ['科技计划', '创新自研', '高新投资', '产学研', '其他'],
                        datasets: [{
                            data: [856, 342, 156, 78, 45],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(139, 92, 246, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: type === 'pie' ? 'right' : 'bottom'
                            }
                        }
                    }
                };
            } else if (chartId === 'regionChart') {
                config = {
                    type: type,
                    data: {
                        labels: ['海曙区', '江北区', '鄞州区', '高新区', '余姚市', '慈溪市'],
                        datasets: [{
                            data: [356, 342, 456, 278, 145, 123],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(139, 92, 246, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'right'
                            }
                        }
                    }
                };
            }
            
            window[chartId] = new Chart(ctx, config);
        }
    </script>
</body>
</html>