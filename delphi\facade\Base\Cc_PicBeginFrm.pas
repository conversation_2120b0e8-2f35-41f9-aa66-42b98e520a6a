unit Cc_PicBeginFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, RzLabel, Vcl.ExtCtrls,
  RzPanel, Vcl.Imaging.pngimage;

type
  TPicBeginForm = class(TForm)
    RzPanel1: TRzPanel;
    Timer1: TTimer;
    Image1: TImage;
    RzLabel2: TRzLabel;
    procedure Timer1Timer(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  PicBeginForm: TPicBeginForm;

implementation

{$R *.dfm}

procedure TPicBeginForm.FormShow(Sender: TObject);
begin
  self.Timer1.Enabled := true;
end;

procedure TPicBeginForm.Timer1Timer(Sender: TObject);
begin
  Timer1.Enabled := false;
  sleep(100);
  self.Close;
end;

end.
