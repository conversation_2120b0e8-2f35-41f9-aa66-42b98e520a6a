'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Separator } from "@/components/ui/separator"
import {
  Search,
  Plus,
  FileText,
  FolderPlus,
  Upload,
  Download,
  MoreVertical,
  Calendar,
  Tag,
  User,
  Clock,
  MessageSquare,
  SendHorizonal,
  Edit,
  Copy,
  Trash,
  Brain,
  Filter,
  SortAsc,
  File,
  FileImage,
  Image,
  CheckCircle,
  X,
  Folder,
  UploadCloud,
  FileUp,
  ArrowUpDown,
  ArrowRight,
  FileQuestion,
  FileCog,
} from "lucide-react"

// 模拟分类数据
const categories = [
  {
    id: "1",
    name: "导航运营部部门总结",
    coverImage: "/placeholder-cover-1.jpg",
    description: "导航运营部的工作总结文档",
    documentCount: 15,
    latestUpdate: "2023-12-10",
  }
];

// 模拟文档数据
const documents = [
  {
    id: "doc1",
    name: "2025年04月导航运营部工作总结（张明）.docx",
    size: "1.5MB",
    uploadTime: "2025-04-29 15:30",
    tags: [
      { type: "date", value: "2025年04月" },
      { type: "content", value: "工作总结" },
      { type: "person", value: "张明" }
    ],
    categoryId: "1"
  }
];

// 模拟对话历史
const chatHistory = [
  { role: "assistant", content: "你好，我是文档处理智能助手，请问有什么可以帮助您？" },
];

export default function DocumentManagementPage() {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [isNewCategoryDialogOpen, setIsNewCategoryDialogOpen] = useState(false);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isEditTagDialogOpen, setIsEditTagDialogOpen] = useState(false);
  const [currentDocument, setCurrentDocument] = useState<any>(null);
  const [documentTags, setDocumentTags] = useState<{type: string, value: string}[]>([]);
  const [newTagType, setNewTagType] = useState<string>("date");
  const [newTagValue, setNewTagValue] = useState<string>("");
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [userMessage, setUserMessage] = useState('');
  const [messages, setMessages] = useState(chatHistory);
  const [generatedContent, setGeneratedContent] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [activeTab, setActiveTab] = useState("chat");
  const [showTagSaveSuccess, setShowTagSaveSuccess] = useState(false);
  const [editingMessageIndex, setEditingMessageIndex] = useState<number | null>(null);
  const [editingMessageContent, setEditingMessageContent] = useState("");
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 过滤当前分类的文档
  const filteredDocuments = documents.filter(doc => {
    // 如果有选中的分类，只显示该分类下的文档
    if (selectedCategory && doc.categoryId !== selectedCategory) {
      return false;
    }
    
    // 搜索过滤
    if (searchQuery && !doc.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      const tagMatch = doc.tags.some(tag => 
        tag.value.toLowerCase().includes(searchQuery.toLowerCase())
      );
      if (!tagMatch) return false;
    }
    
    return true;
  });
  
  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      // 这里模拟上传处理，实际实现需要发送到后端
      console.log(`正在上传 ${event.target.files.length} 个文件`);
      setIsUploadDialogOpen(false);
    }
  };
  
  // 处理拖放上传
  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      console.log(`通过拖放上传了 ${event.dataTransfer.files.length} 个文件`);
    }
  };
  
  // 触发文件输入点击
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  
  // 处理发送消息
  const handleSendMessage = () => {
    if (!userMessage.trim()) return;
    
    // 添加用户消息到聊天历史
    setMessages([...messages, { role: "user", content: userMessage }]);
    
    // 模拟AI响应
    setTimeout(() => {
      setMessages(prev => [...prev, { 
        role: "assistant", 
        content: "我正在分析您的请求，稍后将为您生成总结文档...\n\n正在查找相关文档和信息..." 
      }]);
      
      // 第一次回复后，再添加处理中的消息
      setTimeout(() => {
        setMessages(prev => [...prev, { 
          role: "assistant", 
          content: "我找到了几份相关文档，正在提取关键信息：\n- 导航运营部2025年4月工作进展记录\n- 导航运营部专利导航工作总结\n- 知识产权公共服务记录表" 
        }]);
        
        // 最后添加完成消息并生成内容
        const content = `导航运营部2025年4月工作总结`;
          
        setGeneratedContent(content);
        
        // 直接在聊天中展示完整内容
        setMessages(prev => [...prev, { 
          role: "assistant", 
          content: "我已生成了导航运营部2025年4月工作总结文档。以下是文档内容：\n\n" + content 
        }]);
        
        // 添加提示信息
        setTimeout(() => {
          setMessages(prev => [...prev, { 
            role: "assistant", 
            content: "您可以在预览标签页查看更好的排版效果，也可以下载或复制文档。需要我修改内容吗？" 
          }]);
        }, 500);
      }, 1500);
    }, 1000);
    
    setUserMessage('');
  };

  // 处理预览切换
  const handlePreviewClick = () => {
    setActiveTab("preview");
  };

  // 打开编辑标签对话框
  const handleEditTagsClick = (doc: any) => {
    setCurrentDocument(doc);
    setDocumentTags([...doc.tags]);
    setIsEditTagDialogOpen(true);
  };

  // 添加新标签
  const handleAddTag = () => {
    if (newTagValue.trim() === "") return;
    
    setDocumentTags([...documentTags, { type: newTagType, value: newTagValue.trim() }]);
    setNewTagValue("");
  };

  // 删除标签
  const handleDeleteTag = (index: number) => {
    const updatedTags = [...documentTags];
    updatedTags.splice(index, 1);
    setDocumentTags(updatedTags);
  };

  // 保存编辑后的标签
  const handleSaveTags = () => {
    // 在实际应用中，这里需要调用API保存到后端
    // 这里我们只更新前端状态
    const updatedDocuments = documents.map(doc => {
      if (doc.id === currentDocument.id) {
        return {
          ...doc,
          tags: documentTags
        };
      }
      return doc;
    });
    
    // 修改这里：我们需要修改全局的文档数据，由于documents是常量，实际应用中应该是API调用
    // 在这个演示中，我们直接修改原始数据以便界面能够更新
    documents.forEach((doc, index) => {
      if (doc.id === currentDocument.id) {
        documents[index].tags = [...documentTags];
      }
    });
    
    // 在真实应用中，这里应该是一个API调用
    console.log("标签已更新:", documentTags);
    
    setIsEditTagDialogOpen(false);
    
    // 显示成功提示
    setShowTagSaveSuccess(true);
    
    // 3秒后自动隐藏提示
    setTimeout(() => {
      setShowTagSaveSuccess(false);
    }, 3000);
  };

  // 处理消息编辑
  const startEditingMessage = (index: number) => {
    const message = messages[index];
    if (message.role === "user") {
      setEditingMessageIndex(index);
      setEditingMessageContent(message.content);
    }
  };

  const cancelEditingMessage = () => {
    setEditingMessageIndex(null);
    setEditingMessageContent("");
  };

  const saveEditedMessage = () => {
    if (editingMessageIndex === null || !editingMessageContent.trim()) return;
    
    // 更新消息
    const updatedMessages = [...messages];
    updatedMessages[editingMessageIndex] = {
      role: "user",
      content: editingMessageContent.trim()
    };
    
    // 移除编辑消息后的所有消息（即AI的回复）
    const newMessages = updatedMessages.slice(0, editingMessageIndex + 1);
    
    setMessages(newMessages);
    setEditingMessageIndex(null);
    
    // 自动重新生成回复
    setTimeout(() => {
      setMessages(prev => [...prev, { 
        role: "assistant", 
        content: "我正在基于您修改后的问题重新分析，请稍候..." 
      }]);
      
      // 模拟重新生成内容的流程
      setTimeout(() => {
        handleSendMessage();
      }, 1000);
    }, 500);
  };

  // 复制消息内容
  const copyMessageContent = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        // 可以加入复制成功的提示
        console.log('文本已复制到剪贴板');
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
  };

  return (
    <div className="flex-1 p-6 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="h-[calc(100vh-120px)] flex flex-col">
        {/* 提示信息 */}
        {showTagSaveSuccess && (
          <div className="fixed top-4 right-4 bg-green-100 border border-green-300 text-green-800 px-4 py-2 rounded-md shadow-md flex items-center z-50">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            标签更新成功！
          </div>
        )}
        
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-semibold text-blue-800 flex items-center">
            <FileText className="mr-2 h-6 w-6 text-blue-500" />
            文档智能助手
          </h1>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1 border-blue-200 text-blue-700"
              onClick={() => setIsNewCategoryDialogOpen(true)}
            >
              <FolderPlus size={16} className="text-blue-500" />
              新建分类
            </Button>
            <Button 
              size="sm" 
              className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white"
              onClick={() => setIsUploadDialogOpen(true)}
            >
              <Upload size={16} />
              上传文档
            </Button>
          </div>
        </div>
        
        <div className="flex gap-4 flex-1">
          {/* 左侧：分类列表区域 */}
          <div className="w-1/5">
            <Card className="h-full shadow-md border-blue-100">
              <CardHeader className="p-4 pb-2 border-b border-blue-100">
                <CardTitle className="text-md text-blue-800 flex items-center">
                  <Folder size={18} className="text-blue-500 mr-2" />
                  文档分类
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <ScrollArea className="h-[calc(100vh-230px)]">
                  <div className="py-2">
                    <div
                      className={`flex items-center px-4 py-2 cursor-pointer hover:bg-blue-50 transition-colors ${
                        selectedCategory === null ? 'bg-blue-100' : ''
                      }`}
                      onClick={() => setSelectedCategory(null)}
                    >
                      <FileText size={16} className="text-blue-500 mr-2" />
                      <span className="text-sm font-medium text-gray-700">所有文档</span>
                      <Badge className="ml-auto bg-blue-100 text-blue-800">{documents.length}</Badge>
                    </div>
                    
                    {categories.map(category => (
                      <div
                        key={category.id}
                        className={`flex items-center px-4 py-2 cursor-pointer hover:bg-blue-50 transition-colors ${
                          selectedCategory === category.id ? 'bg-blue-100' : ''
                        }`}
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        <Folder size={16} className="text-blue-500 mr-2" />
                        <span className="text-sm font-medium text-gray-700">{category.name}</span>
                        <Badge className="ml-auto bg-blue-100 text-blue-800">
                          {documents.filter(doc => doc.categoryId === category.id).length}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
                
                <div className="p-3 border-t border-blue-100">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full flex items-center justify-center text-blue-700 border-blue-200"
                    onClick={() => setIsNewCategoryDialogOpen(true)}
                  >
                    <Plus size={16} className="mr-1" />
                    新建分类
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* 中间：文件管理区域 */}
          <div className="w-2/5">
            <Card className="h-full shadow-md border-blue-100">
              <CardHeader className="p-4 pb-3 border-b border-blue-100">
                <div className="flex justify-between items-center mb-3">
                  <CardTitle className="text-md text-blue-800 flex items-center">
                    <FileText size={18} className="text-blue-500 mr-2" />
                    {selectedCategory 
                      ? categories.find(c => c.id === selectedCategory)?.name 
                      : "所有文档"}
                  </CardTitle>
                  
                  <div className="flex gap-2">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-gray-500"
                    >
                      <Filter size={16} />
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-gray-500"
                    >
                      <SortAsc size={16} />
                    </Button>
                  </div>
                </div>
                
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                  <Input 
                    placeholder="搜索文档..." 
                    className="pl-8 border-blue-200 focus-visible:ring-blue-400"
                    value={searchQuery}
                    onChange={e => setSearchQuery(e.target.value)}
                  />
                </div>
              </CardHeader>
              
              <CardContent className="p-0">
                <ScrollArea className="h-[calc(100vh-280px)]">
                  <div 
                    className="p-4 border-dashed border-2 border-blue-200 rounded-lg mx-4 mt-4 bg-blue-50/50 text-center"
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={handleDrop}
                  >
                    <UploadCloud size={30} className="mx-auto text-blue-400 mb-2" />
                    <p className="text-sm text-blue-600 mb-1">拖放文件到此处上传</p>
                    <p className="text-xs text-gray-500">或</p>
                    <Button 
                      size="sm" 
                      variant="outline"
                      className="mt-2 text-blue-700 border-blue-200"
                      onClick={triggerFileInput}
                    >
                      <FileUp size={14} className="mr-1" />
                      浏览文件
                    </Button>
                    <input 
                      type="file" 
                      ref={fileInputRef} 
                      className="hidden" 
                      multiple 
                      onChange={handleFileUpload}
                    />
                  </div>
                  
                  <div className="px-4 pt-2">
                    <h3 className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                      <ArrowUpDown size={14} className="mr-1" />
                      最新上传
                    </h3>
                    
                    {filteredDocuments.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        <FileQuestion size={40} className="mx-auto text-gray-300 mb-2" />
                        <p>没有找到符合条件的文档</p>
                      </div>
                    ) : (
                      filteredDocuments.map(doc => (
                        <Card key={doc.id} className="mb-3 shadow-sm border-blue-100 overflow-hidden">
                          <CardContent className="p-3">
                            <div className="flex items-start">
                              <div className="mr-3 mt-1">
                                <FileText size={20} className="text-blue-500" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-blue-800 truncate" title={doc.name}>
                                  {doc.name}
                                </h4>
                                <div className="flex items-center text-xs text-gray-500 mt-1">
                                  <Clock size={12} className="mr-1" />
                                  {doc.uploadTime}
                                  <div className="mx-2 h-3 border-r border-gray-300"></div>
                                  <span>{doc.size}</span>
                                </div>
                                <div className="flex flex-wrap gap-1 mt-2">
                                  {doc.tags.map((tag, idx) => (
                                    <Badge 
                                      key={idx} 
                                      className={`text-xs font-normal ${
                                        tag.type === 'date' ? 'bg-blue-100 text-blue-800' : 
                                        tag.type === 'content' ? 'bg-green-100 text-green-800' : 
                                        'bg-purple-100 text-purple-800'
                                      }`}
                                    >
                                      {tag.type === 'date' ? <Calendar size={10} className="mr-1" /> : 
                                       tag.type === 'content' ? <Tag size={10} className="mr-1" /> : 
                                       <User size={10} className="mr-1" />}
                                      {tag.value}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                    <MoreVertical size={16} />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                  <DropdownMenuLabel>操作</DropdownMenuLabel>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem 
                                    className="flex items-center"
                                    onClick={() => handleEditTagsClick(doc)}
                                  >
                                    <Edit size={14} className="mr-2" />
                                    编辑标签
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="flex items-center">
                                    <Download size={14} className="mr-2" />
                                    下载
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="flex items-center text-red-600">
                                    <Trash size={14} className="mr-2" />
                                    删除
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </ScrollArea>
                
                <div className="p-3 border-t border-blue-100 flex justify-between items-center">
                  <div className="text-xs text-gray-500">
                    共 {filteredDocuments.length} 个文档
                  </div>
                  <Button 
                    size="sm" 
                    className="flex items-center gap-1 bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={() => setIsUploadDialogOpen(true)}
                  >
                    <Upload size={14} />
                    上传文档
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* 右侧：智能问答与生成区域 */}
          <div className="w-2/5">
            <Card className="h-full shadow-md border-blue-100">
              <CardHeader className="p-4 pb-2 border-b border-blue-100">
                <Tabs defaultValue="chat" value={activeTab} onValueChange={setActiveTab} className="w-full">
                  <div className="flex justify-between items-center mb-2">
                    <TabsList>
                      <TabsTrigger value="chat" className="text-sm">问答</TabsTrigger>
                      <TabsTrigger value="preview" className="text-sm">预览</TabsTrigger>
                      <TabsTrigger value="history" className="text-sm">历史</TabsTrigger>
                    </TabsList>
                    
                    {generatedContent && (
                      <div className="flex gap-1">
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0 text-blue-700"
                          title="复制内容"
                        >
                          <Copy size={14} />
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline" 
                          className="h-8 w-8 p-0 text-blue-700"
                          title="下载Word文档"
                        >
                          <Download size={14} />
                        </Button>
                      </div>
                    )}
                  </div>
                </Tabs>
              </CardHeader>
              
              <CardContent className="p-0 flex-1 flex flex-col">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                  <TabsContent value="chat" className="flex-1 flex flex-col p-0 m-0">
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-4">
                        {messages.map((message, index) => (
                          <div 
                            key={index} 
                            className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            {editingMessageIndex === index ? (
                              <div className="max-w-[80%] bg-blue-50 rounded-lg p-3 border border-blue-200">
                                <textarea
                                  className="w-full p-2 rounded border border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
                                  value={editingMessageContent}
                                  onChange={(e) => setEditingMessageContent(e.target.value)}
                                  rows={3}
                                />
                                <div className="flex justify-end gap-2 mt-2">
                                  <Button size="sm" variant="ghost" onClick={cancelEditingMessage}>
                                    取消
                                  </Button>
                                  <Button size="sm" className="bg-blue-500 text-white" onClick={saveEditedMessage}>
                                    保存并重新生成
                                  </Button>
                                </div>
                              </div>
                            ) : (
                              <div className="flex flex-col max-w-[80%]">
                                <div 
                                  className={`rounded-lg p-3 ${
                                    message.role === 'user' 
                                      ? 'bg-blue-500 text-white rounded-tr-none' 
                                      : 'bg-white border border-blue-100 text-gray-800 rounded-tl-none'
                                  }`}
                                >
                                  {message.role === 'assistant' && (
                                    <div className="flex items-center mb-1.5">
                                      <Brain size={14} className="mr-1.5 text-blue-500" />
                                      <span className="font-medium text-blue-700 text-sm">AI助手</span>
                                    </div>
                                  )}
                                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                                  
                                  {/* 在生成内容的消息后添加预览按钮 */}
                                  {message.role === 'assistant' && 
                                   (message.content.includes('您可以在预览标签页查看') || 
                                    message.content.includes('需要我修改内容吗')) && 
                                   generatedContent && (
                                    <div className="mt-2 flex justify-end">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="bg-blue-50 text-blue-700 border-blue-200 flex items-center"
                                        onClick={handlePreviewClick}
                                      >
                                        <FileText size={14} className="mr-1" />
                                        预览文档
                                      </Button>
                                    </div>
                                  )}
                                </div>
                                
                                {/* 用户消息下方的操作按钮 */}
                                {message.role === 'user' && (
                                  <div className="flex justify-end gap-1 mt-1">
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-7 px-2 py-1 text-blue-700 bg-blue-50 hover:bg-blue-100 flex items-center"
                                      onClick={() => copyMessageContent(message.content)}
                                      title="复制内容"
                                    >
                                      <Copy size={14} className="mr-1" />
                                      <span className="text-xs"></span>
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      className="h-7 px-2 py-1 text-blue-700 bg-blue-50 hover:bg-blue-100 flex items-center"
                                      onClick={() => startEditingMessage(index)}
                                      title="修改问题"
                                    >
                                      <Edit size={14} className="mr-1" />
                                      <span className="text-xs"></span>
                                    </Button>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                    
                    <div className="p-4 border-t border-blue-100">
                      <div className="flex gap-2">
                        <Input
                          placeholder="输入你的问题，例如：帮我做XX部门的工作总结..."
                          className="flex-1 border-blue-200 focus-visible:ring-blue-400"
                          value={userMessage}
                          onChange={(e) => setUserMessage(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                              e.preventDefault();
                              handleSendMessage();
                            }
                          }}
                        />
                        <Button 
                          className="bg-blue-500 hover:bg-blue-600 text-white"
                          onClick={handleSendMessage}
                          disabled={!userMessage.trim()}
                        >
                          <SendHorizonal size={18} />
                        </Button>
                      </div>
                      <div className="mt-2 text-xs text-gray-500">
                        提示：您可以要求AI帮您整理特定部门、特定时期的工作总结
                      </div>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="preview" className="flex-1 p-0 m-0">
                    {generatedContent ? (
                      <div className="flex flex-col h-full">
                        <div className="p-3 bg-gray-50 border-b border-blue-100 flex justify-between items-center">
                          <h3 className="text-sm font-medium text-blue-800 flex items-center">
                            <FileText size={16} className="mr-2 text-blue-600" />
                            文档预览
                          </h3>
                          <div className="flex gap-2">
                            <Button size="sm" variant="outline" className="h-8 text-blue-700 bg-white">
                              <Copy size={14} className="mr-1" />
                              复制全文
                            </Button>
                            <Button size="sm" className="h-8 bg-blue-500 text-white">
                              <Download size={14} className="mr-1" />
                              下载Word
                            </Button>
                          </div>
                        </div>
                        <ScrollArea className="flex-1 p-6 bg-white">
                          <div className="max-w-3xl mx-auto">
                            <div className="prose prose-blue">
                              <div className="bg-white p-6 shadow-sm border border-gray-100 rounded-lg">
                                <pre className="whitespace-pre-wrap text-gray-800 font-sans leading-relaxed">
                                  {generatedContent}
                                </pre>
                              </div>
                            </div>
                          </div>
                        </ScrollArea>
                      </div>
                    ) : (
                      <div className="flex-1 flex flex-col items-center justify-center p-6 text-center">
                        <FileCog size={60} className="text-gray-300 mb-3" />
                        <h3 className="text-lg font-medium text-gray-700 mb-1">暂无生成内容</h3>
                        <p className="text-gray-500 max-w-md">
                          请在问答区域向AI助手询问生成工作总结，生成后内容将在此处显示
                        </p>
                      </div>
                    )}
                  </TabsContent>
                  
                  <TabsContent value="history" className="flex-1 p-0 m-0">
                    <ScrollArea className="flex-1 p-4">
                      <div className="space-y-3">
                        <div className="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium text-blue-800">导航运营部2025年4月工作总结</h4>
                              <p className="text-sm text-gray-500">生成时间：2025-04-28 15:30</p>
                            </div>
                            <Button size="sm" variant="ghost" className="text-blue-700">
                              <FileText size={14} className="mr-1" />
                              查看
                            </Button>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium text-blue-800">市场部2025年3月销售报告</h4>
                              <p className="text-sm text-gray-500">生成时间：2025-04-02 10:15</p>
                            </div>
                            <Button size="sm" variant="ghost" className="text-blue-700">
                              <FileText size={14} className="mr-1" />
                              查看
                            </Button>
                          </div>
                        </div>
                        <div className="bg-white p-3 rounded-lg border border-blue-100 shadow-sm">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium text-blue-800">财务部2024年度总结</h4>
                              <p className="text-sm text-gray-500">生成时间：2025-01-15 14:20</p>
                            </div>
                            <Button size="sm" variant="ghost" className="text-blue-700">
                              <FileText size={14} className="mr-1" />
                              查看
                            </Button>
                          </div>
                        </div>
                      </div>
                    </ScrollArea>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      
      {/* 新建分类对话框 */}
      <Dialog open={isNewCategoryDialogOpen} onOpenChange={setIsNewCategoryDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>新建文档分类</DialogTitle>
            <DialogDescription>
              创建一个新的文档分类以便更好地组织管理您的文档
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">分类名称</label>
              <Input placeholder="输入分类名称" className="border-blue-200" />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">封面图</label>
              <div className="border-2 border-dashed border-blue-200 rounded-lg p-4 text-center bg-blue-50/50">
                <Image size={30} className="mx-auto text-blue-400 mb-2" />
                <p className="text-sm text-blue-600">点击上传封面图片</p>
                <input type="file" className="hidden" accept="image/*" />
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">描述</label>
              <Textarea 
                placeholder="输入分类描述（可选）" 
                className="border-blue-200 resize-none"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsNewCategoryDialogOpen(false)}
              className="border-blue-200"
            >
              取消
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              创建分类
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 上传文档对话框 */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>上传文档</DialogTitle>
            <DialogDescription>
              选择您要上传的文档，支持.docx、.pdf、.pptx、.xlsx等格式
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div 
              className="border-2 border-dashed border-blue-200 rounded-lg p-6 text-center bg-blue-50/50"
              onDragOver={(e) => e.preventDefault()}
              onDrop={handleDrop}
            >
              <UploadCloud size={40} className="mx-auto text-blue-400 mb-3" />
              <p className="text-blue-600 mb-1">拖放文件到此处上传</p>
              <p className="text-xs text-gray-500 mb-3">或</p>
              <Button 
                variant="outline"
                className="text-blue-700 border-blue-200"
                onClick={triggerFileInput}
              >
                <FileUp size={16} className="mr-1" />
                浏览文件
              </Button>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">选择分类</label>
              <select className="w-full rounded-md border border-blue-200 p-2">
                <option value="">选择文档分类</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsUploadDialogOpen(false)}
              className="border-blue-200"
            >
              取消
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              开始上传
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 添加编辑标签对话框 */}
      <Dialog open={isEditTagDialogOpen} onOpenChange={setIsEditTagDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>编辑文档标签</DialogTitle>
            <DialogDescription>
              {currentDocument && (
                <span className="text-gray-500">为文档 "{currentDocument.name}" 编辑标签</span>
              )}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">现有标签</label>
              <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-md min-h-12 max-h-36 overflow-y-auto">
                {documentTags.length === 0 ? (
                  <p className="text-sm text-gray-400">暂无标签</p>
                ) : (
                  documentTags.map((tag, idx) => (
                    <div 
                      key={idx} 
                      className={`flex items-center gap-1 p-1 px-2 rounded-md ${
                        tag.type === 'date' ? 'bg-blue-100 text-blue-800' : 
                        tag.type === 'content' ? 'bg-green-100 text-green-800' : 
                        'bg-purple-100 text-purple-800'
                      }`}
                    >
                      {tag.type === 'date' ? <Calendar size={14} className="mr-1" /> : 
                       tag.type === 'content' ? <Tag size={14} className="mr-1" /> : 
                       <User size={14} className="mr-1" />}
                      <span className="text-sm">{tag.value}</span>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-5 w-5 p-0 rounded-full hover:bg-gray-200 hover:bg-opacity-50"
                        onClick={() => handleDeleteTag(idx)}
                      >
                        <X size={12} />
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">添加新标签</label>
              <div className="flex gap-2">
                <select 
                  className="p-2 border border-blue-200 rounded-md w-1/3"
                  value={newTagType}
                  onChange={(e) => setNewTagType(e.target.value)}
                >
                  <option value="date">日期</option>
                  <option value="content">内容</option>
                  <option value="person">人员</option>
                </select>
                <div className="flex-1 flex gap-2">
                  <Input 
                    placeholder="输入标签内容..." 
                    className="flex-1 border-blue-200"
                    value={newTagValue}
                    onChange={(e) => setNewTagValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        handleAddTag();
                      }
                    }}
                  />
                  <Button 
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                    onClick={handleAddTag}
                    disabled={newTagValue.trim() === ""}
                  >
                    <Plus size={18} />
                  </Button>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                提示：按 Enter 键快速添加标签
              </p>
            </div>
          </div>
          
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsEditTagDialogOpen(false)}
              className="border-blue-200"
            >
              取消
            </Button>
            <Button 
              className="bg-blue-500 hover:bg-blue-600 text-white"
              onClick={handleSaveTags}
            >
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 