<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下属科创平台数据处理与展示流程</text>

  <!-- 阶段一：数据同步与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与标准化</text>
  
  <!-- 节点1: 多源数据接口 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据接口</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">教育部、科技厅、高校系统</text>
  </g>

  <!-- 节点2: 数据同步 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定期数据同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">科创平台数据获取</text>
  </g>

  <!-- 节点3: 数据标准化 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据标准化处理</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">类型、级别、学科字段去重</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计分析与缓存 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计分析与缓存</text>

  <!-- 节点4: 统计服务 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计服务分析</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">按平台类型与级别统计</text>
  </g>

  <!-- 节点5: 可视化数据生成 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化数据生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">数量、增长率、网络、热力图</text>
  </g>

  <!-- 节点6: 数据缓存 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果缓存</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">供前端调用</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 420 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点7: 页面访问 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">前端加载缓存数据</text>
  </g>

  <!-- 节点8: 视图渲染 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多视图渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">概况、分布、网络、热力图</text>
  </g>

  <!-- 节点9: 筛选条件 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">默认或自定义条件</text>
  </g>

  <!-- 节点10: 下钻查询 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下钻明细查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击柱体或热力单元</text>
  </g>

  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：详情展示与日志记录 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：详情展示与日志记录</text>

  <!-- 节点11: 台账查询 -->
  <g transform="translate(150, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">平台台账查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">返回表格数据</text>
  </g>

  <!-- 节点12: 合作详情 -->
  <g transform="translate(400, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">合作项目详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">侧滑详情页展示</text>
  </g>

  <!-- 节点13: 导出功能 -->
  <g transform="translate(650, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出与分页</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">支持数据导出浏览</text>
  </g>

  <!-- 节点14: 日志记录 -->
  <g transform="translate(900, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">统计分析支持</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -> 14 -->
  <path d="M 350 705 Q 375 705 400 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 705 Q 625 705 650 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 705 Q 875 705 900 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 从数据标准化到统计服务 -->
  <path d="M 860 200 C 860 240, 310 240, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从缓存到页面访问 -->
  <path d="M 910 380 C 910 420, 200 420, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从下钻查询到台账查询 -->
  <path d="M 950 560 C 950 600, 250 600, 250 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <!-- 从筛选条件回到视图渲染 -->
  <path d="M 600 525 C 580 540, 520 540, 450 540 C 450 530, 450 525, 450 525" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="525" y="555" font-size="11" fill="#666">动态刷新</text>

  <!-- 从协同网络节点点击到合作详情 -->
  <path d="M 450 560 C 450 600, 500 600, 500 670" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="460" y="615" font-size="11" fill="#666">节点点击</text>

</svg>