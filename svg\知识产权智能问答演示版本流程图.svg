<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="28" text-anchor="middle" font-weight="600" fill="#333">知识产权智能问答系统架构流程图</text>
  <text x="700" y="70" font-size="16" text-anchor="middle" fill="#555">"一套核心能力、双域部署、统一治理"</text>

  <!-- 阶段一：接入层 -->
  <text x="700" y="120" font-size="20" text-anchor="middle" font-weight="500" fill="#555">接入层 (Presentation Layer)</text>
  
  <!-- H5智能问答页面 -->
  <g transform="translate(200, 140)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">H5 智能问答页面</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">微信菜单 / 公共服务平台 / 内部平台</text>
    <text x="140" y="60" text-anchor="middle" font-size="12" fill="#555">携带 openid 或 userId JWT</text>
  </g>

  <!-- OAuth认证 -->
  <g transform="translate(920, 140)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">OAuth 认证</text>
    <text x="90" y="45" text-anchor="middle" font-size="12" fill="#555">浙政钉 / 微信</text>
    <text x="90" y="60" text-anchor="middle" font-size="12" fill="#555">生成访问 Token</text>
  </g>

  <!-- 阶段二：业务层 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">业务层 (Application Layer)</text>

  <!-- 外网智能问答实例 -->
  <g transform="translate(150, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外网实例</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">部署在互联网区</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">负责公众流量</text>
  </g>

  <!-- 内网智能问答实例 -->
  <g transform="translate(400, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">内网实例</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">部署在内部平台区</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">服务内部人员</text>
  </g>

  <!-- 统一智能体服务 -->
  <g transform="translate(700, 300)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统一智能体服务</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">会话管理 | 内容安全检测 | 模型路由</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">Chat Gateway 统一处理</text>
  </g>

  <!-- 阶段三：AI服务层与知识层 -->
  <text x="350" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">AI 服务层 (Model Layer)</text>
  <text x="1050" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">知识层 (Knowledge Layer)</text>

  <!-- DeepSeek-R1 GPU集群 -->
  <g transform="translate(80, 480)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">DeepSeek-R1 集群</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">内部平台区GPU</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">数据不出域</text>
  </g>

  <!-- 互联网大模型API -->
  <g transform="translate(350, 480)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">大模型 API</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">互联网服务</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">按量计费，高并发</text>
  </g>

  <!-- 智能体平台（双域） -->
  <g transform="translate(850, 480)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#FFE8CC" stroke="#FFB74D" stroke-width="1.5" />
    <text x="140" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">智能体平台（双域各一套）</text>
    <text x="140" y="45" text-anchor="middle" font-size="12" fill="#555">文档切分 | Embedding</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">RAG 检索</text>
  </g>

  <!-- 阶段四：数据治理与安全层 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">数据治理与安全层 (Governance &amp; Security)</text>

  <!-- 四级内容安全 -->
  <g transform="translate(250, 660)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">四级敏感词检测</text>
    <text x="150" y="45" text-anchor="middle" font-size="12" fill="#555">请求 | Prompt | 检索结果 | 最终回复</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">违规内容即时阻断</text>
  </g>

  <!-- 日志与审计 -->
  <g transform="translate(650, 660)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">日志与审计</text>
    <text x="150" y="45" text-anchor="middle" font-size="12" fill="#555">外网：匿名化7天 | 内网：180天溯源</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">统一存储与管理</text>
  </g>

  <!-- 连接线 -->
  <!-- H5页面 -> OAuth -->
  <path d="M 480 175 C 550 175, 650 175, 920 175" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 接入层 -> 业务层 -->
  <path d="M 340 210 C 340 240, 250 270, 250 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1010 210 C 1010 240, 500 270, 500 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 外网/内网实例 -> 统一智能体服务 -->
  <path d="M 350 340 C 450 340, 550 340, 700 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 340 L 700 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 统一智能体服务 -> AI服务层 -->
  <path d="M 750 380 C 650 420, 400 450, 300 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 380 C 750 420, 550 450, 460 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 统一智能体服务 -> 智能体平台 -->
  <path d="M 890 380 C 940 420, 970 450, 990 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- AI服务层 -> 安全层 -->
  <path d="M 300 560 C 350 600, 380 630, 400 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 460 560 C 500 600, 650 630, 700 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 智能体平台 -> 安全层 -->
  <path d="M 990 560 C 900 600, 750 630, 650 660" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 用户流程标注 -->
  <text x="50" y="850" font-size="16" font-weight="600" fill="#333">整体流程：</text>
  <text x="50" y="875" font-size="14" fill="#555">用户 → 接入层 → 智能问答应用 → Chat Gateway → ①路由模型（DeepSeek-R1或API）② 并行拉取向量检索结果 → 四级内容安全 → 统一格式化 → 返回前端</text>

  <!-- 架构特点标注 -->
  <text x="50" y="920" font-size="16" font-weight="600" fill="#333">架构特点：</text>
  <text x="50" y="945" font-size="14" fill="#555">内外双活 | 统一网关 | 集中治理 | 弹性扩容 | 数据可控 | 快速响应</text>

</svg> 