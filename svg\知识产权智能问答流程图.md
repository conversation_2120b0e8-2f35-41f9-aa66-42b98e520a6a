# 任务: 创建一个专业、层次分明且美观的SVG流程图

根据下面的流程描述，生成对应的SVG代码。

## 流程描述
<flow>
本方案采用 “一套核心能力、双域部署、统一治理” 的总体架构，既满足外网高并发与安全隔离要求，又保证内网数据主权与低时延体验。
    接入层（Presentation Layer）
        H5 智能问答页面：微信自定义菜单 / 关键词回复、公共服务平台浮窗、内部平台功能入口均指向同一套 H5 页面代码；首次加载时携带 openid 或 userId JWT。
        浙政钉 / 微信 OAuth：完成用户身份解析并生成最小权限的访问 Token。
    边界层（Access Layer）
        互联网区 WAF + CDN：对外统一入口，提供 DDoS 防护、速率限制、IP 黑白名单。
        DMZ 反向代理（Nginx/Gateway）：仅开放 443 端口，将请求转发至互联网区“智能问答应用”。
    业务层（Application Layer）
        智能问答应用（两套实例）
            外网实例：部署在互联网区，负责公众流量；
            内网实例：部署在内部平台区，服务员工与托底计算。

        两套实例共享 统一 Chat Gateway（逻辑服务），实现：
        1）会话管理与限流；2）内容安全四级检测；3）模型路由策略（外网→大模型 SaaS，内网→DeepSeek-R1）。
    AI 服务层（Model Layer）
        DeepSeek-R1 GPU 集群（内部平台区）：主力推理，保证数据不出域；若安全评估允许，可开放给外网实例使用（需额外防火墙策略）。
        互联网大模型 SaaS：按量计费，应对公众高并发；返回结果经 Gateway 统一过滤。
    知识层（Knowledge Layer）
        智能体平台 / Dify（双域各一套）：负责文档切分、Embedding 与 RAG 检索；文档存储在对象存储（MinIO / OSS），向量落库 PGVector。
        元数据统一标签：source_type、jurisdiction、version，便于跨域同步与精确召回。
    数据治理与安全层（Governance & Security）
        四级敏感词检测：请求、Prompt、向量检索结果、最终回复均过安全策略引擎；违规内容即时阻断。
        日志与审计：外网会话匿名化存储 7 天；内网日志写入 ClickHouse，保留 180 天并支持溯源。
        双域隔离：公网与内网通过零信任网关最小权限通信；任何跨域调用需经 mTLS + 白名单。
    运维与可观测性层（Ops Layer）
        Prometheus + Grafana：统一收集 QPS、P95 延迟、GPU 利用率等指标；异常触发告警。
        CI/CD：前端、应用、模型权重均通过 GitLab CI 自动化发布，确保版本一致性。
整体流程：
用户 → 接入层 → 边界层安全校验 → 智能问答应用 → Chat Gateway →
① 路由模型（DeepSeek-R1 或 SaaS）
② 并行拉取向量检索结果 → 四级内容安全 → 统一格式化 → 返回前端。
通过这种“内外双活、统一网关、集中治理”的架构，既可在公众侧实现弹性扩容，又能在内部场景保持数据可控与快速响应，满足知识产权业务对 准确性、合规性与可维护性 的综合要求。

</flow>

## 设计与风格要求 (关键！)
*   **总体布局**: 采用分阶段、分模块的布局，确保逻辑清晰、层次分明。为主要阶段（如“阶段一”、“阶段二”）创建视觉上的分组。整体布局要留有足够的空白，避免拥挤。
*   **配色方案**:
    *   背景: `white` (#FFFFFF)
    *   主文本/线条颜色: `#333333`
    *   **节点配色 (柔和色系)**:
        *   阶段一/预训练相关: 淡蓝色系 (背景: `#E3F2FD`, 边框: `#90CAF9`)
        *   阶段二/SFT相关: 淡绿色系 (背景: `#E6F4EA`, 边框: `#A5D6A7`)
        *   阶段二/RM与PPO相关: 淡紫色系 (背景: `#F3E8FD`, 边框: `#CE93D8`)
        *   阶段三/最终结果: 淡黄色系 (背景: `#FFFBEB`, 边框: `#FFE082`)
*   **节点/框体样式**:
    *   所有节点均为**圆角矩形** (`rx="8"`, `ry="8"`).
    *   添加柔和的**投影效果**以增加深度感。
    *   **内部文本结构**: 每个节点应包含两部分文字：
        1.  **主标题**: 加粗 (`font-weight="600"`), 字号稍大。
        2.  **描述/副标题**: 普通字重, 字号稍小, 颜色稍浅 (`#555555`)。
*   **连接线与箭头**:
    *   优先使用**平滑的贝塞尔曲线 (`<path>`)** 连接节点，而非生硬的直线 (`<line>`)，以创造流动的视觉效果。
    *   箭头 (`<marker>`) 设计要简洁，与线条颜色一致。
    *   如果连接线有标签，标签文字应放置在路径旁边，清晰可读。
*   **字体**:
    *   使用现代无衬线字体，如 `'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif`。
    *   标题字号约 `16px`, 描述字号约 `12px`。整体标题字号约 `24px`。

## SVG参考示例 (关键！)
请严格遵循以下SVG代码的结构和风格规范来生成你的流程图。这个示例展示了如何实现上述所有设计要求。

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">LLM 训练与微调技术解析</text>

  <!-- 阶段一：基础预训练 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：基础预训练 (Foundational Pre-training)</text>
  
  <!-- 节点1: 语料库 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">海量语料库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(互联网, 书籍, 代码)</text>
  </g>

  <!-- 节点2: 基础模型 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">基础模型 (Base LLM)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">知识渊博, 但未对齐</text>
  </g>
  
  <!-- 连接线 1 -> 2 (直线) -->
  <path d="M 600 200 Q 600 225 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：对齐微调 -->
  <text x="600" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：对齐微调 (Alignment Fine-Tuning)</text>

  <!-- 节点3: SFT模型 -->
  <g transform="translate(250, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">SFT 模型</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">能遵循指令, 价值未对齐</text>
  </g>

  <!-- 节点4: 奖励模型 -->
  <g transform="translate(750, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">奖励模型 (RM)</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">人类偏好的代理</text>
  </g>

  <!-- 连接线 Base -> SFT (曲线) -->
  <path d="M 550 320 C 450 350, 400 380, 350 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 SFT -> RM (曲线) -->
  <path d="M 450 455 C 550 455, 650 455, 750 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="600" y="445" text-anchor="middle" font-size="12" fill="#555">用于生成排序数据</text>

  <!-- 阶段三：最终成果 -->
  <text x="600" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：最终成果</text>
  
  <!-- 节点5: 对齐后的LLM -->
  <g transform="translate(400, 620)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">对齐后的语言模型 (Aligned LLM)</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-100">知识渊博</tspan>
        <tspan dx="40">遵循指令</tspan>
        <tspan dx="40">符合偏好</tspan>
      </text>
  </g>

  <!-- 连接线 SFT/RM -> Aligned LLM (曲线) -->
  <path d="M 350 490 C 350 550, 500 580, 500 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 490 C 850 550, 700 580, 700 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>