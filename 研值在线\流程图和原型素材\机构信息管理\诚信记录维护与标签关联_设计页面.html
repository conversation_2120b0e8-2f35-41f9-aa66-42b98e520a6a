<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诚信记录维护与标签关联</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">诚信记录维护与标签关联</h1>

        <!-- 查询筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                查询筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="subjectName" class="block text-sm font-medium text-gray-700 mb-1">失信主体名称</label>
                    <input type="text" id="subjectName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入主体名称">
                </div>
                <div>
                    <label for="creditLevel" class="block text-sm font-medium text-gray-700 mb-1">信用等级</label>
                    <select id="creditLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="A">A级</option>
                        <option value="B">B级</option>
                        <option value="C">C级</option>
                        <option value="D">D级</option>
                    </select>
                </div>
                <div>
                    <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1">失信时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" id="startDate" class="w-1/2 px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" id="endDate" class="w-1/2 px-2 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label for="punishment" class="block text-sm font-medium text-gray-700 mb-1">惩戒措施</label>
                    <select id="punishment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="1">限制高消费</option>
                        <option value="2">限制投标</option>
                        <option value="3">限制贷款</option>
                        <option value="4">其他</option>
                    </select>
                </div>
                <div>
                    <label for="expiryStatus" class="block text-sm font-medium text-gray-700 mb-1">到期状态</label>
                    <select id="expiryStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="active">未到期</option>
                        <option value="expired">已到期</option>
                    </select>
                </div>
                <div>
                    <label for="tagSearch" class="block text-sm font-medium text-gray-700 mb-1">已关联标签</label>
                    <div class="relative">
                        <input type="text" id="tagSearch" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入标签名称">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div class="flex items-center">
                    <input type="checkbox" id="fuzzySearch" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="fuzzySearch" class="ml-2 block text-sm text-gray-700">模糊查询</label>
                </div>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        重置
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 批量操作工具栏 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6 flex justify-between items-center">
            <div class="flex space-x-3">
                <button onclick="openImportModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    批量导入
                </button>
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    批量导出
                </button>
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    模板下载
                </button>
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    统一更新
                </button>
            </div>
            <button onclick="openAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                新增记录
            </button>
        </div>

        <!-- 数据列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主体名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">信用等级</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失信行为摘要</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失信时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">惩戒措施</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联标签</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">D级</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">未按时支付工程款，涉及金额120万元</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2023-05-12</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">限制高消费</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024-05-12</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">3</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">v1.2</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="deleteRecord('1')" class="text-red-600 hover:text-red-900">删除</button>
                                <button onclick="openTagModal('1')" class="text-green-600 hover:text-green-900">标签管理</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX建筑有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">C级</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">工程质量不达标，存在安全隐患</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2023-08-23</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">限制投标</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024-02-23</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">v1.0</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="deleteRecord('2')" class="text-red-600 hover:text-red-900">删除</button>
                                <button onclick="openTagModal('2')" class="text-green-600 hover:text-green-900">标签管理</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX贸易有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">B级</span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">虚假宣传，误导消费者</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2023-11-15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">限制贷款</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024-05-15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">1</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">v1.1</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailDrawer('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">修改</button>
                                <button onclick="deleteRecord('3')" class="text-red-600 hover:text-red-900">删除</button>
                                <button onclick="openTagModal('3')" class="text-green-600 hover:text-green-900">标签管理</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">24</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增记录模态框 -->
    <div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增诚信记录</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">主体名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入主体名称" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">信用等级 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择信用等级</option>
                                <option value="A">A级</option>
                                <option value="B">B级</option>
                                <option value="C">C级</option>
                                <option value="D">D级</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">失信行为摘要 <span class="text-red-500">*</span></label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入失信行为摘要" required></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">失信时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">惩戒措施 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择惩戒措施</option>
                                <option value="1">限制高消费</option>
                                <option value="2">限制投标</option>
                                <option value="3">限制贷款</option>
                                <option value="4">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">到期时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeAddModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 导入记录模态框 -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批量导入诚信记录</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            下载标准模板
                        </a>
                        <span class="text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                    </div>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                            </label>
                            <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                        </div>
                    </div>
                    <div class="hidden" id="upload-progress">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">上传进度</span>
                            <span class="text-gray-600">75%</span>
                        </div>
                        <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                        </div>
                    </div>
                    <div id="validation-result" class="hidden bg-yellow-50 p-4 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                    <li>第3行：主体名称不能为空</li>
                                    <li>第5行：失信时间格式不正确</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/2 bg-white shadow-lg transform transition-transform duration-300 ease-in-out translate-x-full">
        <div class="h-full flex flex-col">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">诚信记录详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-md font-semibold text-gray-900">基本信息</h4>
                            <button onclick="openEditModal('detail')" class="text-sm text-blue-600 hover:text-blue-800">编辑信息</button>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">主体名称：</span>
                                    <span class="font-medium text-gray-900">宁波市XX科技有限公司</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">信用等级：</span>
                                    <span class="font-medium text-gray-900">D级</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">失信行为摘要：</span>
                                    <span class="font-medium text-gray-900">未按时支付工程款，涉及金额120万元</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">失信时间：</span>
                                    <span class="font-medium text-gray-900">2023-05-12</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">惩戒措施：</span>
                                    <span class="font-medium text-gray-900">限制高消费</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">到期时间：</span>
                                    <span class="font-medium text-gray-900">2024-05-12</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">创建人：</span>
                                    <span class="font-medium text-gray-900">张三</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">创建时间：</span>
                                    <span class="font-medium text-gray-900">2023-05-15 14:30</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">最后更新人：</span>
                                    <span class="font-medium text-gray-900">李四</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">最后更新时间：</span>
                                    <span class="font-medium text-gray-900">2023-06-20 09:15</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="text-gray-500">备注：</span>
                                    <span class="font-medium text-gray-900">该企业已承诺分期偿还欠款</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 标签列表 -->
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <h4 class="text-md font-semibold text-gray-900">标签列表</h4>
                            <button onclick="openTagModal('detail')" class="text-sm text-blue-600 hover:text-blue-800">管理标签</button>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex flex-wrap gap-2">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">欠薪企业</span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">工程纠纷</span>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">重点监管</span>
                            </div>
                        </div>
                    </div>

                    <!-- 版本对比 -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-900 mb-4">版本对比</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center space-x-4 mb-4">
                                <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                    <option>v1.2 (当前版本)</option>
                                    <option>v1.1</option>
                                    <option>v1.0</option>
                                </select>
                                <span class="text-gray-500">与</span>
                                <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                    <option>v1.1</option>
                                    <option>v1.0</option>
                                </select>
                                <button class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                    对比
                                </button>
                            </div>
                            <div class="text-sm">
                                <div class="grid grid-cols-12 gap-2 mb-2 font-medium">
                                    <div class="col-span-4">字段</div>
                                    <div class="col-span-4">v1.2</div>
                                    <div class="col-span-4">v1.1</div>
                                </div>
                                <div class="grid grid-cols-12 gap-2 py-2 border-t border-gray-200">
                                    <div class="col-span-4 text-gray-500">惩戒措施</div>
                                    <div class="col-span-4">限制高消费</div>
                                    <div class="col-span-4">限制投标</div>
                                </div>
                                <div class="grid grid-cols-12 gap-2 py-2 border-t border-gray-200">
                                    <div class="col-span-4 text-gray-500">到期时间</div>
                                    <div class="col-span-4">2024-05-12</div>
                                    <div class="col-span-4">2024-02-12</div>
                                </div>
                                <div class="grid grid-cols-12 gap-2 py-2 border-t border-gray-200">
                                    <div class="col-span-4 text-gray-500">备注</div>
                                    <div class="col-span-4">该企业已承诺分期偿还欠款</div>
                                    <div class="col-span-4">-</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 标签管理模态框 -->
    <div id="tagModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">标签管理</h3>
                    <button onclick="closeTagModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center">
                        <div class="relative w-1/2">
                            <input type="text" placeholder="搜索标签名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                            </div>
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            新增标签
                        </button>
                    </div>
                    <div class="border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标签名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联记录数</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">欠薪企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">23</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">2022-05-12</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">工程纠纷</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">45</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">2022-03-15</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">重点监管</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">12</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">2023-01-20</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">环保违规</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">8</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">2023-04-05</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeTagModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 新增记录模态框
        function openAddModal() {
            document.getElementById('addModal').classList.remove('hidden');
        }
        function closeAddModal() {
            document.getElementById('addModal').classList.add('hidden');
        }

        // 导入记录模态框
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
        }
        function closeImportModal() {
            document.getElementById('importModal').classList.add('hidden');
        }

        // 详情抽屉
        function openDetailDrawer(id) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }
        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        // 标签管理模态框
        function openTagModal(id) {
            document.getElementById('tagModal').classList.remove('hidden');
        }
        function closeTagModal() {
            document.getElementById('tagModal').classList.add('hidden');
        }

        // 编辑记录模态框
        function openEditModal(id) {
            // 这里可以添加根据id加载不同记录的逻辑
            alert('编辑记录功能待实现，记录ID: ' + id);
        }

        // 删除记录
        function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？此操作不可恢复！')) {
                alert('删除记录功能待实现，记录ID: ' + id);
            }
        }

        // 点击模态框外部关闭
        document.getElementById('addModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAddModal();
            }
        });
        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportModal();
            }
        });
        document.getElementById('tagModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTagModal();
            }
        });

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('upload-progress').classList.remove('hidden');
                document.getElementById('validation-result').classList.remove('hidden');
                
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                    }
                }, 200);
            }
        });
    </script>
</body>
</html>