<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校-服务企业情况</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">高等院校-服务企业情况</h1>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('service-count')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">累计服务企业次数</p>
                        <p class="text-2xl font-bold text-gray-900">1,245</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">12%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('cooperation-count')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">产学研合作项目数</p>
                        <p class="text-2xl font-bold text-gray-900">328</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">8%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('project-count')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">横向课题在研数量</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="text-red-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('transaction-amount')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">年度技术交易总额</p>
                        <p class="text-2xl font-bold text-gray-900">¥2,856万</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">23%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer" onclick="openDetailModal('consultant-hours')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">技术顾问总时长</p>
                        <p class="text-2xl font-bold text-gray-900">1,245小时</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">15%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">年份范围</label>
                    <div class="flex space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                            <option>2020</option>
                        </select>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                            <option>2020</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">企业行业</label>
                    <div class="relative">
                        <button onclick="toggleDropdown('industry-dropdown')" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm text-left flex justify-between items-center">
                            <span>选择行业</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div id="industry-dropdown" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 hidden">
                            <div class="p-2 max-h-60 overflow-y-auto">
                                <div class="space-y-1">
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">制造业</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">信息技术</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">生物医药</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">新能源</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">新材料</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                    <div class="relative">
                        <button onclick="toggleDropdown('tech-dropdown')" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm text-left flex justify-between items-center">
                            <span>选择技术领域</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </button>
                        <div id="tech-dropdown" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-300 hidden">
                            <div class="p-2 max-h-60 overflow-y-auto">
                                <div class="space-y-1">
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">人工智能</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">智能制造</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">生物技术</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">新材料</span>
                                    </label>
                                    <label class="flex items-center px-2 py-1 hover:bg-gray-100 rounded">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">新能源</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">合作类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        <option>全部类型</option>
                        <option>产学研合作</option>
                        <option>横向课题</option>
                        <option>技术交易</option>
                        <option>顾问服务</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">关键字搜索</label>
                    <div class="flex">
                        <input type="text" placeholder="输入项目名称、企业名称或关键字" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            搜索
                        </button>
                    </div>
                </div>
                <div class="flex space-x-3 md:col-span-2">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        重置
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 服务记录列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">服务记录列表</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出
                    </button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        新增记录
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目/服务名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作企业</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费/交易额</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能制造生产线优化</div>
                                <div class="text-sm text-gray-500">项目编号: PROJ-2023-001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX科技</div>
                                <div class="text-sm text-gray-500">制造业</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">产学研合作</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023/01-2023/12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥1,200,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('project-detail')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">企业</button>
                                <button class="text-purple-600 hover:text-purple-900">项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新材料研发技术咨询</div>
                                <div class="text-sm text-gray-500">服务编号: SERV-2023-002</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX材料</div>
                                <div class="text-sm text-gray-500">新材料</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">顾问服务</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023/03-2023/09</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥450,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('project-detail')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">企业</button>
                                <button class="text-purple-600 hover:text-purple-900">项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">生物医药检测技术转让</div>
                                <div class="text-sm text-gray-500">交易编号: TRAN-2023-003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX医药</div>
                                <div class="text-sm text-gray-500">生物医药</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">技术交易</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023/05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥800,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物技术</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('project-detail')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">企业</button>
                                <button class="text-purple-600 hover:text-purple-900">项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新能源电池研发项目</div>
                                <div class="text-sm text-gray-500">项目编号: PROJ-2023-004</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX能源</div>
                                <div class="text-sm text-gray-500">新能源</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">横向课题</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023/02-2023/11</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥1,500,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('project-detail')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">企业</button>
                                <button class="text-purple-600 hover:text-purple-900">项目</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">124</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">年度服务企业次数</h3>
                    <div class="h-64">
                        <canvas id="serviceChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">横向课题资金分布</h3>
                    <div class="h-64">
                        <canvas id="fundingChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">技术交易额趋势</h3>
                    <div class="h-64">
                        <canvas id="transactionChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-700 mb-2">技术领域服务占比</h3>
                    <div class="h-64">
                        <canvas id="techChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情侧栏 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto hidden z-50">
        <div class="flex justify-end min-h-screen">
            <div class="bg-white w-full max-w-2xl shadow-xl">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto" style="max-height: calc(100vh - 100px);">
                    <div class="space-y-6">
                        <!-- 基础信息 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">项目名称：</span>
                                    <span class="font-medium text-gray-900">智能制造生产线优化</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目编号：</span>
                                    <span class="font-medium text-gray-900">PROJ-2023-001</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">合作企业：</span>
                                    <span class="font-medium text-gray-900">宁波市XX科技</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">合作类型：</span>
                                    <span class="font-medium text-gray-900">产学研合作</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">起止时间：</span>
                                    <span class="font-medium text-gray-900">2023/01-2023/12</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目经费：</span>
                                    <span class="font-medium text-gray-900">¥1,200,000</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">技术领域：</span>
                                    <span class="font-medium text-gray-900">智能制造</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">负责人：</span>
                                    <span class="font-medium text-gray-900">张教授</span>
                                </div>
                            </div>
                        </div>

                        <!-- 标签页导航 -->
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button id="progressTab" class="border-b-2 border-blue-500 text-blue-600 px-1 py-2 text-sm font-medium">项目进度</button>
                                <button id="fundingTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">经费使用</button>
                                <button id="outputTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">成果产出</button>
                                <button id="feedbackTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">企业反馈</button>
                                <button id="attachmentTab" class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-2 text-sm font-medium">合同附件</button>
                            </nav>
                        </div>

                        <!-- 项目进度内容 -->
                        <div id="progressContent" class="space-y-4">
                            <div>
                                <h5 class="text-sm font-medium text-gray-700 mb-2">当前阶段</h5>
                                <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-blue-100 rounded-full p-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-blue-800">技术实施阶段</p>
                                            <p class="text-sm text-blue-700">预计2023年10月完成</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h5 class="text-sm font-medium text-gray-700 mb-2">进度时间线</h5>
                                <div class="space-y-4">
                                    <div class="relative">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 bg-blue-500 rounded-full h-6 w-6 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">项目启动</p>
                                                <p class="text-sm text-gray-500">2023年1月15日</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 bg-blue-500 rounded-full h-6 w-6 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">需求分析完成</p>
                                                <p class="text-sm text-gray-500">2023年3月20日</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 bg-blue-500 rounded-full h-6 w-6 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">方案设计完成</p>
                                                <p class="text-sm text-gray-500">2023年5月10日</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 bg-blue-500 rounded-full h-6 w-6 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">技术实施阶段</p>
                                                <p class="text-sm text-gray-500">进行中，预计2023年10月完成</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="relative">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0 bg-gray-300 rounded-full h-6 w-6 flex items-center justify-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <p class="text-sm font-medium text-gray-900">项目验收</p>
                                                <p class="text-sm text-gray-500">预计2023年12月</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button onclick="closeDetailModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        关闭
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        下载合同
                    </button>
                    <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-sm">
                        导出PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(modalId) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            dropdown.classList.toggle('hidden');
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有图表
            const serviceCtx = document.getElementById('serviceChart').getContext('2d');
            new Chart(serviceCtx, {
                type: 'bar',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '服务企业次数',
                        data: [856, 942, 1120, 1245],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            const fundingCtx = document.getElementById('fundingChart').getContext('2d');
            new Chart(fundingCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '横向课题资金',
                        data: [120, 190, 130, 150, 210, 190, 250, 220, 300, 280, 320, 350],
                        borderColor: 'rgba(124, 58, 237, 1)',
                        backgroundColor: 'rgba(124, 58, 237, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            const transactionCtx = document.getElementById('transactionChart').getContext('2d');
            new Chart(transactionCtx, {
                type: 'line',
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        label: '技术交易额',
                        data: [450, 600, 800, 1000],
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            const techCtx = document.getElementById('techChart').getContext('2d');
            new Chart(techCtx, {
                type: 'doughnut',
                data: {
                    labels: ['智能制造', '新材料', '生物技术', '新能源', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // 标签页切换
            const tabs = ['progressTab', 'fundingTab', 'outputTab', 'feedbackTab', 'attachmentTab'];
            const contents = ['progressContent', 'fundingContent', 'outputContent', 'feedbackContent', 'attachmentContent'];
            
            tabs.forEach((tabId, index) => {
                const tab = document.getElementById(tabId);
                if (tab) {
                    tab.addEventListener('click', function() {
                        // 重置所有标签和内容
                        tabs.forEach(t => {
                            const tTab = document.getElementById(t);
                            tTab.classList.remove('border-blue-500', 'text-blue-600');
                            tTab.classList.add('border-transparent', 'text-gray-500');
                        });
                        
                        contents.forEach(c => {
                            const cContent = document.getElementById(c);
                            if (cContent) cContent.classList.add('hidden');
                        });
                        
                        // 激活当前标签
                        this.classList.remove('border-transparent', 'text-gray-500');
                        this.classList.add('border-blue-500', 'text-blue-600');
                        
                        // 显示对应内容
                        const content = document.getElementById(contents[index]);
                        if (content) content.classList.remove('hidden');
                    });
                }
            });
        });
    </script>
</body>
</html>