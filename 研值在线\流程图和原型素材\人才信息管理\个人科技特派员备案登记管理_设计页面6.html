<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人科技特派员备案登记管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6',
                        light: '#f3f4f6',
                        dark: '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">个人科技特派员备案登记管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-4/5 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-3 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-1">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-0.5">姓名</label>
                            <input type="text" id="name" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="workUnit" class="block text-sm font-medium text-gray-700 mb-0.5">工作单位</label>
                            <input type="text" id="workUnit" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                        </div>
                        <div>
                            <label for="specialty" class="block text-sm font-medium text-gray-700 mb-0.5">专业特长</label>
                            <input type="text" id="specialty" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专业特长">
                        </div>
                        <div>
                            <label for="batch" class="block text-sm font-medium text-gray-700 mb-0.5">申报批次</label>
                            <select id="batch" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部批次</option>
                                <option value="2024-01">2024年第一批</option>
                                <option value="2024-02">2024年第二批</option>
                                <option value="2023-01">2023年第一批</option>
                                <option value="2023-02">2023年第二批</option>
                            </select>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-0.5">当前状态</label>
                            <select id="status" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="draft">草稿</option>
                                <option value="pending">待审核</option>
                                <option value="approved-town">乡镇已审核</option>
                                <option value="approved-unit">派出单位已审核</option>
                                <option value="approved-county">区县已终审</option>
                                <option value="rejected">已驳回</option>
                                <option value="archived">已归档</option>
                            </select>
                        </div>
                        <div>
                            <label for="applyDate" class="block text-sm font-medium text-gray-700 mb-0.5">申报日期</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    <div class="mt-1.5 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3 flex-shrink-0">
                    <!-- 备案登记列表区 -->
                    <div class="w-full flex-1 flex flex-col flex-shrink-0">
                        <!-- 列表标题区 -->
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-800">备案登记列表</h2>
                            <div class="flex space-x-3">
                                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    导出数据
                                </button>
                                <button onclick="openModal('addRecordModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    新增备案
                                </button>
                            </div>
                        </div>
                        
                        <!-- 批次切换标签 -->
                        <div class="px-4 py-2 border-b border-gray-200 flex flex-wrap gap-2">
                            <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-medium">全部批次</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">2024年第一批</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">2024年第二批</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">2023年第一批</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-800 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors">2023年第二批</button>
                        </div>
                        
                        <!-- 列表内容区 -->
                        <div class="flex-1 overflow-auto flex-shrink-0">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作单位</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专业特长</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">结对服务单位</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报批次</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">张明</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市农业科学研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">水稻种植技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">慈溪市水稻种植合作社</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">李华</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市林业科学研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">果树栽培技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">余姚市杨梅种植基地</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-18</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">区县已终审</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button class="text-green-600 hover:text-green-900 transition-colors">归档</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">王强</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">水产养殖技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">象山县海水养殖合作社</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">派出单位已审核</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button onclick="openModal('approveModal')" class="text-green-600 hover:text-green-900 transition-colors">审核</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">赵静</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市农业技术推广总站</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">蔬菜种植技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄞州区蔬菜种植基地</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-22</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">乡镇已审核</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button onclick="openModal('approveModal')" class="text-green-600 hover:text-green-900 transition-colors">审核</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">陈杰</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市畜牧兽医局</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">畜牧养殖技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁海县生猪养殖合作社</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-25</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">待审核</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button onclick="openModal('approveModal')" class="text-green-600 hover:text-green-900 transition-colors">审核</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">刘芳</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">女</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市农村能源技术推广中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">奉化区生态农业基地</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已驳回</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button onclick="openModal('addRecordModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">7</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">周伟</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">男</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市农产品质量安全检测中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农产品检测技术</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">北仑区农产品合作社</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024年第一批</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-01</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">草稿</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('viewRecordModal')" class="text-blue-600 hover:text-blue-900 transition-colors">查看</button>
                                            <button onclick="openModal('addRecordModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="submitForReview()" class="text-blue-600 hover:text-blue-900 transition-colors">提交审核</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页区域 -->
                        <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">7</span> 条，共 <span class="font-medium">42</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 transition-colors">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 transition-colors">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 transition-colors">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 transition-colors">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="w-full lg:w-1/5 lg:min-w-[280px] space-y-4">
                <!-- 备案统计 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">备案统计</h3>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">总备案数</span>
                                <span class="text-xl font-bold text-blue-600">156</span>
                            </div>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">已归档</span>
                                <span class="text-xl font-bold text-green-600">98</span>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">审核中</span>
                                <span class="text-xl font-bold text-yellow-600">32</span>
                            </div>
                        </div>
                        <div class="bg-red-50 p-3 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">已驳回</span>
                                <span class="text-xl font-bold text-red-600">12</span>
                            </div>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="text-sm text-gray-600">草稿</span>
                                <span class="text-xl font-bold text-gray-600">14</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">备案进度</h4>
                        <div class="h-24">
                            <canvas id="progressChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 批次管理 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-800">批次管理</h3>
                        <button onclick="openModal('addBatchModal')" class="text-blue-600 hover:text-blue-800 focus:outline-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </button>
                    </div>
                    <div class="divide-y divide-gray-200">
                        <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer flex justify-between items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">2024年第一批</div>
                                <div class="text-xs text-gray-500">2024-01-01至2024-03-31</div>
                            </div>
                            <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                        </div>
                        <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer flex justify-between items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">2024年第二批</div>
                                <div class="text-xs text-gray-500">2024-04-01至2024-06-30</div>
                            </div>
                            <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">未开始</span>
                        </div>
                        <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer flex justify-between items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">2023年第一批</div>
                                <div class="text-xs text-gray-500">2023-01-01至2023-06-30</div>
                            </div>
                            <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">已结束</span>
                        </div>
                        <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer flex justify-between items-center">
                            <div>
                                <div class="text-sm font-medium text-gray-900">2023年第二批</div>
                                <div class="text-xs text-gray-500">2023-07-01至2023-12-31</div>
                            </div>
                            <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">已结束</span>
                        </div>
                    </div>
                    <div class="p-3 border-t border-gray-200">
                        <button class="w-full text-sm text-blue-600 hover:text-blue-800 flex items-center justify-center">
                            查看所有批次
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">快速操作</h3>
                    <div class="space-y-3">
                        <button onclick="openModal('batchImportModal')" class="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            批量导入备案
                        </button>
                        <button class="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            批量归档
                        </button>
                        <button class="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            审核记录查询
                        </button>
                        <button class="w-full flex items-center justify-start px-3 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出年度报表
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑备案弹窗 -->
    <div id="addRecordModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增个人科技特派员备案</h3>
                    <button onclick="closeModal('addRecordModal')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <!-- 表单标签页 -->
                <div class="border-b border-gray-200 mb-4">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button class="border-blue-500 text-blue-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">基本信息</button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">服务信息</button>
                        <button class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">附件材料</button>
                    </nav>
                </div>
                
                <form class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="recordName" class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="recordName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="recordGender" class="block text-sm font-medium text-gray-700 mb-1">性别 <span class="text-red-500">*</span></label>
                            <select id="recordGender" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择性别</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                            </select>
                        </div>
                        <div>
                            <label for="recordBirth" class="block text-sm font-medium text-gray-700 mb-1">出生年月 <span class="text-red-500">*</span></label>
                            <input type="date" id="recordBirth" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="recordNation" class="block text-sm font-medium text-gray-700 mb-1">民族</label>
                            <input type="text" id="recordNation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入民族" value="汉族">
                        </div>
                        <div>
                            <label for="recordWorkUnit" class="block text-sm font-medium text-gray-700 mb-1">工作单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="recordWorkUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                        </div>
                        <div>
                            <label for="recordTitle" class="block text-sm font-medium text-gray-700 mb-1">职称</label>
                            <select id="recordTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择职称</option>
                                <option value="researcher">研究员</option>
                                <option value="associate-researcher">副研究员</option>
                                <option value="assistant-researcher">助理研究员</option>
                                <option value="research-assistant">研究实习员</option>
                                <option value="professor">教授</option>
                                <option value="associate-professor">副教授</option>
                                <option value="lecturer">讲师</option>
                            </select>
                        </div>
                        <div>
                            <label for="recordSpecialty" class="block text-sm font-medium text-gray-700 mb-1">专业特长 <span class="text-red-500">*</span></label>
                            <input type="text" id="recordSpecialty" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专业特长">
                        </div>
                        <div>
                            <label for="recordEducation" class="block text-sm font-medium text-gray-700 mb-1">学历</label>
                            <select id="recordEducation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择学历</option>
                                <option value="doctor">博士研究生</option>
                                <option value="master">硕士研究生</option>
                                <option value="bachelor">大学本科</option>
                                <option value="college">大学专科</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div>
                            <label for="recordPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                            <input type="tel" id="recordPhone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入联系电话">
                        </div>
                        <div>
                            <label for="recordEmail" class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                            <input type="email" id="recordEmail" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入电子邮箱">
                        </div>
                        <div>
                            <label for="recordBatch" class="block text-sm font-medium text-gray-700 mb-1">申报批次 <span class="text-red-500">*</span></label>
                            <select id="recordBatch" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择申报批次</option>
                                <option value="2024-01">2024年第一批</option>
                                <option value="2024-02">2024年第二批</option>
                            </select>
                        </div>
                        <div>
                            <label for="recordServiceUnit" class="block text-sm font-medium text-gray-700 mb-1">结对服务单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="recordServiceUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入结对服务单位">
                        </div>
                    </div>
                    
                    <div>
                        <label for="recordServiceContent" class="block text-sm font-medium text-gray-700 mb-1">科技服务内容 <span class="text-red-500">*</span></label>
                        <textarea id="recordServiceContent" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述科技服务内容"></textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="recordStartTime" class="block text-sm font-medium text-gray-700 mb-1">服务开始时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="recordStartTime" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="recordEndTime" class="block text-sm font-medium text-gray-700 mb-1">服务结束时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="recordEndTime" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    
                    <div>
                        <label for="recordExpectedGoal" class="block text-sm font-medium text-gray-700 mb-1">预期目标与任务</label>
                        <textarea id="recordExpectedGoal" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述预期目标与任务"></textarea>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" onclick="closeModal('addRecordModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            取消
                        </button>
                        <button type="button" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            保存草稿
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            提交审核
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 查看备案详情弹窗 -->
    <div id="viewRecordModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">个人科技特派员备案详情</h3>
                    <button onclick="closeModal('viewRecordModal')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <!-- 备案状态 -->
                <div class="bg-blue-50 p-3 rounded-lg mb-6">
                    <div class="flex justify-between items-center">
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">张明 - 宁波市农业科学研究院</h4>
                            <p class="text-xs text-gray-500">备案编号: KTPY-20240115001</p>
                        </div>
                        <span class="px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                    </div>
                </div>
                
                <!-- 审核进度 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">审核进度</h4>
                    <div class="relative">
                        <!-- 进度线 -->
                        <div class="absolute left-3 top-0 bottom-0 w-0.5 bg-green-200"></div>
                        
                        <!-- 进度节点 -->
                        <div class="relative pl-10 pb-6">
                            <div class="absolute left-0 w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h5 class="text-sm font-medium text-gray-900">提交申请</h5>
                            <p class="text-xs text-gray-500">2024-01-15 09:30</p>
                            <p class="text-xs text-gray-700 mt-1">申请人：张明</p>
                        </div>
                        
                        <div class="relative pl-10 pb-6">
                            <div class="absolute left-0 w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h5 class="text-sm font-medium text-gray-900">乡镇审核通过</h5>
                            <p class="text-xs text-gray-500">2024-01-16 14:20</p>
                            <p class="text-xs text-gray-700 mt-1">审核人：王乡镇</p>
                            <p class="text-xs text-gray-600 mt-1">审核意见：同意推荐</p>
                        </div>
                        
                        <div class="relative pl-10 pb-6">
                            <div class="absolute left-0 w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h5 class="text-sm font-medium text-gray-900">派出单位审核通过</h5>
                            <p class="text-xs text-gray-500">2024-01-18 10:15</p>
                            <p class="text-xs text-gray-700 mt-1">审核人：李单位</p>
                            <p class="text-xs text-gray-600 mt-1">审核意见：同意推荐</p>
                        </div>
                        
                        <div class="relative pl-10">
                            <div class="absolute left-0 w-6 h-6 rounded-full bg-green-500 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            </div>
                            <h5 class="text-sm font-medium text-gray-900">区县科技局终审通过并归档</h5>
                            <p class="text-xs text-gray-500">2024-01-20 16:40</p>
                            <p class="text-xs text-gray-700 mt-1">审核人：赵科技</p>
                            <p class="text-xs text-gray-600 mt-1">审核意见：同意备案，已归档</p>
                        </div>
                    </div>
                </div>
                
                <!-- 基本信息 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">基本信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">姓名：</span>
                            <span class="font-medium text-gray-900">张明</span>
                        </div>
                        <div>
                            <span class="text-gray-500">性别：</span>
                            <span class="font-medium text-gray-900">男</span>
                        </div>
                        <div>
                            <span class="text-gray-500">出生年月：</span>
                            <span class="font-medium text-gray-900">1985-06-15</span>
                        </div>
                        <div>
                            <span class="text-gray-500">民族：</span>
                            <span class="font-medium text-gray-900">汉族</span>
                        </div>
                        <div>
                            <span class="text-gray-500">工作单位：</span>
                            <span class="font-medium text-gray-900">宁波市农业科学研究院</span>
                        </div>
                        <div>
                            <span class="text-gray-500">职称：</span>
                            <span class="font-medium text-gray-900">副研究员</span>
                        </div>
                        <div>
                            <span class="text-gray-500">专业特长：</span>
                            <span class="font-medium text-gray-900">水稻种植技术</span>
                        </div>
                        <div>
                            <span class="text-gray-500">学历：</span>
                            <span class="font-medium text-gray-900">博士研究生</span>
                        </div>
                        <div>
                            <span class="text-gray-500">联系电话：</span>
                            <span class="font-medium text-gray-900">13800138000</span>
                        </div>
                        <div>
                            <span class="text-gray-500">电子邮箱：</span>
                            <span class="font-medium text-gray-900"><EMAIL></span>
                        </div>
                    </div>
                </div>
                
                <!-- 服务信息 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">服务信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-4">
                        <div>
                            <span class="text-gray-500">申报批次：</span>
                            <span class="font-medium text-gray-900">2024年第一批</span>
                        </div>
                        <div>
                            <span class="text-gray-500">结对服务单位：</span>
                            <span class="font-medium text-gray-900">慈溪市水稻种植合作社</span>
                        </div>
                        <div>
                            <span class="text-gray-500">服务开始时间：</span>
                            <span class="font-medium text-gray-900">2024-03-01</span>
                        </div>
                        <div>
                            <span class="text-gray-500">服务结束时间：</span>
                            <span class="font-medium text-gray-900">2024-12-31</span>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-1">科技服务内容：</h5>
                        <div class="p-3 bg-gray-50 rounded-md text-sm text-gray-700">
                            1. 提供水稻优质高产栽培技术指导，包括品种选择、育苗技术、田间管理等方面的培训与现场指导。<br>
                            2. 开展水稻病虫害绿色防控技术研究与推广，减少化学农药使用量。<br>
                            3. 引进水稻新品种进行试验示范，筛选适合本地种植的优质高产品种。<br>
                            4. 指导合作社开展水稻标准化生产，提升产品质量安全水平。<br>
                            5. 协助合作社对接市场，提高水稻产品附加值。
                        </div>
                    </div>
                    
                    <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-1">预期目标与任务：</h5>
                        <div class="p-3 bg-gray-50 rounded-md text-sm text-gray-700">
                            1. 培训合作社技术骨干20人次，提高合作社整体种植技术水平。<br>
                            2. 示范推广水稻优质品种2-3个，病虫害绿色防控技术覆盖率达到80%以上。<br>
                            3. 实现水稻平均亩产提高10%，农药使用量减少15%。<br>
                            4. 帮助合作社获得绿色食品认证1-2项。<br>
                            5. 建立水稻标准化生产技术规程1套。
                        </div>
                    </div>
                </div>
                
                <!-- 附件材料 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">附件材料</h4>
                    <div class="space-y-2">
                        <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                            </svg>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">个人简历.docx</p>
                                <p class="text-xs text-gray-500">2024-01-15 上传 · 2.4MB</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                            </svg>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">职称证书.pdf</p>
                                <p class="text-xs text-gray-500">2024-01-15 上传 · 1.8MB</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                            </svg>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">服务协议.pdf</p>
                                <p class="text-xs text-gray-500">2024-01-16 上传 · 3.2MB</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4 border-t">
                    <button type="button" onclick="closeModal('viewRecordModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        关闭
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-60