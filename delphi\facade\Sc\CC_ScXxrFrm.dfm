object CC_ScXxrFrame: TCC_ScXxrFrame
  Left = 0
  Top = 0
  Width = 900
  Height = 718
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object RzPanel2: TRzPanel
    Left = 0
    Top = 41
    Width = 900
    Height = 677
    Align = alClient
    BorderOuter = fsNone
    Color = clWhite
    TabOrder = 0
    ExplicitWidth = 940
    object AdvSmoothCalendarGroup1: TAdvSmoothCalendarGroup
      Left = 0
      Top = 0
      Width = 900
      Height = 660
      Header.Fill.Color = clWhite
      Header.Fill.ColorTo = clNone
      Header.Fill.ColorMirror = clNone
      Header.Fill.ColorMirrorTo = clNone
      Header.Fill.GradientType = gtVertical
      Header.Fill.GradientMirrorType = gtSolid
      Header.Fill.BorderColor = clNone
      Header.Fill.Rounding = 0
      Header.Fill.ShadowOffset = 0
      Header.Fill.Glow = gmNone
      Header.UpDownVisible = False
      Header.ArrowColor = clBlack
      Header.ArrowsVisible = False
      Header.Height = 30
      Header.Font.Charset = DEFAULT_CHARSET
      Header.Font.Color = clRed
      Header.Font.Height = -13
      Header.Font.Name = #24494#36719#38597#40657
      Header.Font.Style = []
      Footer.Fill.Color = clWhite
      Footer.Fill.ColorTo = clNone
      Footer.Fill.ColorMirror = clNone
      Footer.Fill.ColorMirrorTo = clNone
      Footer.Fill.GradientType = gtVertical
      Footer.Fill.GradientMirrorType = gtSolid
      Footer.Fill.BorderColor = 14474202
      Footer.Fill.Rounding = 0
      Footer.Fill.ShadowOffset = 0
      Footer.Fill.Glow = gmNone
      Footer.Font.Charset = DEFAULT_CHARSET
      Footer.Font.Color = clBlack
      Footer.Font.Height = -11
      Footer.Font.Name = 'Tahoma'
      Footer.Font.Style = []
      Footer.Visible = False
      ArrowLeft = False
      ArrowRight = False
      OnSelectDate = AdvSmoothCalendarGroup1SelectDate
      OnDateFill = AdvSmoothCalendarGroup1DateFill
      Fill.Color = clWhite
      Fill.ColorTo = clNone
      Fill.ColorMirror = clNone
      Fill.ColorMirrorTo = clNone
      Fill.GradientType = gtVertical
      Fill.GradientMirrorType = gtSolid
      Fill.BorderColor = 14474202
      Fill.Rounding = 0
      Fill.ShadowOffset = 0
      Fill.Glow = gmNone
      Month = 10
      Year = 2012
      Rows = 4
      TabOrder = 0
      ShowHint = True
      Version = '1.2.0.1'
      DateAppearance.DateFont.Charset = DEFAULT_CHARSET
      DateAppearance.DateFont.Color = clBlack
      DateAppearance.DateFont.Height = -11
      DateAppearance.DateFont.Name = #24494#36719#38597#40657
      DateAppearance.DateFont.Style = []
      DateAppearance.DateFill.Color = clWhite
      DateAppearance.DateFill.ColorTo = clNone
      DateAppearance.DateFill.ColorMirror = clNone
      DateAppearance.DateFill.ColorMirrorTo = clNone
      DateAppearance.DateFill.GradientType = gtVertical
      DateAppearance.DateFill.GradientMirrorType = gtVertical
      DateAppearance.DateFill.BorderColor = clNone
      DateAppearance.DateFill.Rounding = 0
      DateAppearance.DateFill.ShadowOffset = 0
      DateAppearance.DateFill.Glow = gmNone
      DateAppearance.DayOfWeekFont.Charset = DEFAULT_CHARSET
      DateAppearance.DayOfWeekFont.Color = clBlack
      DateAppearance.DayOfWeekFont.Height = -11
      DateAppearance.DayOfWeekFont.Name = #24494#36719#38597#40657
      DateAppearance.DayOfWeekFont.Style = []
      DateAppearance.DayOfWeekFill.Color = clWhite
      DateAppearance.DayOfWeekFill.ColorTo = clNone
      DateAppearance.DayOfWeekFill.ColorMirror = clNone
      DateAppearance.DayOfWeekFill.ColorMirrorTo = clNone
      DateAppearance.DayOfWeekFill.GradientType = gtVertical
      DateAppearance.DayOfWeekFill.GradientMirrorType = gtSolid
      DateAppearance.DayOfWeekFill.BorderColor = 14474202
      DateAppearance.DayOfWeekFill.BorderWidth = 0
      DateAppearance.DayOfWeekFill.Rounding = 0
      DateAppearance.DayOfWeekFill.ShadowOffset = 0
      DateAppearance.DayOfWeekFill.Glow = gmNone
      DateAppearance.SelectedDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.SelectedDateFont.Color = clBlack
      DateAppearance.SelectedDateFont.Height = -11
      DateAppearance.SelectedDateFont.Name = 'Tahoma'
      DateAppearance.SelectedDateFont.Style = []
      DateAppearance.SelectedDateFill.Color = clNone
      DateAppearance.SelectedDateFill.ColorTo = clNone
      DateAppearance.SelectedDateFill.ColorMirror = clNone
      DateAppearance.SelectedDateFill.ColorMirrorTo = clNone
      DateAppearance.SelectedDateFill.GradientType = gtVertical
      DateAppearance.SelectedDateFill.GradientMirrorType = gtVertical
      DateAppearance.SelectedDateFill.BorderColor = 14983778
      DateAppearance.SelectedDateFill.Rounding = 0
      DateAppearance.SelectedDateFill.ShadowOffset = 0
      DateAppearance.SelectedDateFill.Glow = gmNone
      DateAppearance.CurrentDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.CurrentDateFont.Color = clWhite
      DateAppearance.CurrentDateFont.Height = -11
      DateAppearance.CurrentDateFont.Name = 'Tahoma'
      DateAppearance.CurrentDateFont.Style = []
      DateAppearance.CurrentDateFill.Color = clBlue
      DateAppearance.CurrentDateFill.ColorTo = clNone
      DateAppearance.CurrentDateFill.ColorMirror = clNone
      DateAppearance.CurrentDateFill.ColorMirrorTo = clNone
      DateAppearance.CurrentDateFill.GradientType = gtVertical
      DateAppearance.CurrentDateFill.GradientMirrorType = gtVertical
      DateAppearance.CurrentDateFill.BorderColor = 14327846
      DateAppearance.CurrentDateFill.Rounding = 0
      DateAppearance.CurrentDateFill.ShadowOffset = 0
      DateAppearance.CurrentDateFill.Glow = gmNone
      DateAppearance.WeekendFill.Color = clWhite
      DateAppearance.WeekendFill.ColorTo = clNone
      DateAppearance.WeekendFill.ColorMirror = clNone
      DateAppearance.WeekendFill.ColorMirrorTo = clNone
      DateAppearance.WeekendFill.GradientType = gtVertical
      DateAppearance.WeekendFill.GradientMirrorType = gtVertical
      DateAppearance.WeekendFill.BorderColor = clNone
      DateAppearance.WeekendFill.Rounding = 0
      DateAppearance.WeekendFill.ShadowOffset = 0
      DateAppearance.WeekendFill.Glow = gmNone
      DateAppearance.WeekendFont.Charset = DEFAULT_CHARSET
      DateAppearance.WeekendFont.Color = clBlack
      DateAppearance.WeekendFont.Height = -11
      DateAppearance.WeekendFont.Name = 'Tahoma'
      DateAppearance.WeekendFont.Style = []
      DateAppearance.HoverDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.HoverDateFont.Color = 5978398
      DateAppearance.HoverDateFont.Height = -11
      DateAppearance.HoverDateFont.Name = 'Tahoma'
      DateAppearance.HoverDateFont.Style = []
      DateAppearance.HoverDateFill.Color = 16248808
      DateAppearance.HoverDateFill.ColorTo = clNone
      DateAppearance.HoverDateFill.ColorMirror = clNone
      DateAppearance.HoverDateFill.ColorMirrorTo = clNone
      DateAppearance.HoverDateFill.GradientType = gtVertical
      DateAppearance.HoverDateFill.GradientMirrorType = gtVertical
      DateAppearance.HoverDateFill.BorderColor = 16371364
      DateAppearance.HoverDateFill.Rounding = 0
      DateAppearance.HoverDateFill.ShadowOffset = 0
      DateAppearance.HoverDateFill.Glow = gmNone
      DateAppearance.MonthDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.MonthDateFont.Color = clBlack
      DateAppearance.MonthDateFont.Height = -12
      DateAppearance.MonthDateFont.Name = #24494#36719#38597#40657
      DateAppearance.MonthDateFont.Style = []
      DateAppearance.YearDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.YearDateFont.Color = clBlack
      DateAppearance.YearDateFont.Height = -12
      DateAppearance.YearDateFont.Name = #24494#36719#38597#40657
      DateAppearance.YearDateFont.Style = []
      DateAppearance.WeekNumbers.Visible = True
      DateAppearance.WeekNumbers.Font.Charset = DEFAULT_CHARSET
      DateAppearance.WeekNumbers.Font.Color = clWhite
      DateAppearance.WeekNumbers.Font.Height = -11
      DateAppearance.WeekNumbers.Font.Name = 'Tahoma'
      DateAppearance.WeekNumbers.Font.Style = []
      DateAppearance.WeekNumbers.Fill.Color = clWhite
      DateAppearance.WeekNumbers.Fill.ColorTo = clNone
      DateAppearance.WeekNumbers.Fill.ColorMirror = clNone
      DateAppearance.WeekNumbers.Fill.ColorMirrorTo = clNone
      DateAppearance.WeekNumbers.Fill.GradientType = gtVertical
      DateAppearance.WeekNumbers.Fill.GradientMirrorType = gtSolid
      DateAppearance.WeekNumbers.Fill.BorderColor = 14474202
      DateAppearance.WeekNumbers.Fill.Rounding = 0
      DateAppearance.WeekNumbers.Fill.ShadowOffset = 0
      DateAppearance.WeekNumbers.Fill.Glow = gmNone
      DateAppearance.WeekNumbers.Width = 15
      DateAppearance.DisabledDateFont.Charset = DEFAULT_CHARSET
      DateAppearance.DisabledDateFont.Color = clWhite
      DateAppearance.DisabledDateFont.Height = -11
      DateAppearance.DisabledDateFont.Name = 'Tahoma'
      DateAppearance.DisabledDateFont.Style = []
      DateAppearance.DisabledDateFill.Color = 15921906
      DateAppearance.DisabledDateFill.ColorTo = clNone
      DateAppearance.DisabledDateFill.ColorMirror = clNone
      DateAppearance.DisabledDateFill.ColorMirrorTo = clNone
      DateAppearance.DisabledDateFill.GradientType = gtVertical
      DateAppearance.DisabledDateFill.GradientMirrorType = gtVertical
      DateAppearance.DisabledDateFill.BorderColor = clNone
      DateAppearance.DisabledDateFill.Rounding = 0
      DateAppearance.DisabledDateFill.ShadowOffset = 0
      DateAppearance.DisabledDateFill.Glow = gmNone
      DateAppearance.DateBeforeFill.Color = clNone
      DateAppearance.DateBeforeFill.ColorTo = clNone
      DateAppearance.DateBeforeFill.ColorMirror = clNone
      DateAppearance.DateBeforeFill.ColorMirrorTo = clNone
      DateAppearance.DateBeforeFill.GradientType = gtVertical
      DateAppearance.DateBeforeFill.GradientMirrorType = gtSolid
      DateAppearance.DateBeforeFill.BorderColor = clNone
      DateAppearance.DateBeforeFill.Rounding = 0
      DateAppearance.DateBeforeFill.ShadowOffset = 0
      DateAppearance.DateBeforeFill.Glow = gmNone
      DateAppearance.DateAfterFill.Color = clNone
      DateAppearance.DateAfterFill.ColorTo = clNone
      DateAppearance.DateAfterFill.ColorMirror = clNone
      DateAppearance.DateAfterFill.ColorMirrorTo = clNone
      DateAppearance.DateAfterFill.GradientType = gtVertical
      DateAppearance.DateAfterFill.GradientMirrorType = gtSolid
      DateAppearance.DateAfterFill.BorderColor = clNone
      DateAppearance.DateAfterFill.Rounding = 0
      DateAppearance.DateAfterFill.ShadowOffset = 0
      DateAppearance.DateAfterFill.Glow = gmNone
      DateAppearance.DateBeforeFont.Charset = DEFAULT_CHARSET
      DateAppearance.DateBeforeFont.Color = clSilver
      DateAppearance.DateBeforeFont.Height = -11
      DateAppearance.DateBeforeFont.Name = 'Tahoma'
      DateAppearance.DateBeforeFont.Style = []
      DateAppearance.DateAfterFont.Charset = DEFAULT_CHARSET
      DateAppearance.DateAfterFont.Color = clSilver
      DateAppearance.DateAfterFont.Height = -11
      DateAppearance.DateAfterFont.Name = 'Tahoma'
      DateAppearance.DateAfterFont.Style = []
      StatusAppearance.Fill.Color = clRed
      StatusAppearance.Fill.ColorMirror = clNone
      StatusAppearance.Fill.ColorMirrorTo = clNone
      StatusAppearance.Fill.GradientType = gtSolid
      StatusAppearance.Fill.GradientMirrorType = gtSolid
      StatusAppearance.Fill.BorderColor = clGray
      StatusAppearance.Fill.Rounding = 0
      StatusAppearance.Fill.ShadowOffset = 0
      StatusAppearance.Fill.Glow = gmNone
      StatusAppearance.Font.Charset = DEFAULT_CHARSET
      StatusAppearance.Font.Color = clWhite
      StatusAppearance.Font.Height = -11
      StatusAppearance.Font.Name = 'Tahoma'
      StatusAppearance.Font.Style = []
      StatusAppearance.Glow = False
      Align = alCustom
      TMSStyle = 20
    end
  end
  object RzPanel3: TRzPanel
    Left = 0
    Top = 0
    Width = 900
    Height = 41
    Align = alTop
    BorderOuter = fsFlat
    Color = clWhite
    TabOrder = 1
    ExplicitWidth = 940
    object RzPanel4: TRzPanel
      Left = 1
      Top = 1
      Width = 898
      Height = 39
      Align = alTop
      BorderOuter = fsFlat
      Color = clWhite
      TabOrder = 0
      ExplicitWidth = 940
      object RzLabel4: TRzLabel
        Left = 24
        Top = 8
        Width = 57
        Height = 26
        Caption = #20241#24687#26085
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel5: TRzLabel
        Left = 95
        Top = 10
        Width = 30
        Height = 19
        Caption = #32534#21046
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel14: TRzLabel
        Left = 441
        Top = 7
        Width = 240
        Height = 19
        Caption = #26085#26399#21333#20987#20026#35774#32622#65292#20877#27425#21333#20987#20026#21462#28040#12290
        Font.Charset = DEFAULT_CHARSET
        Font.Color = 81119
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
      end
      object RzLabel1: TRzLabel
        Left = 209
        Top = 10
        Width = 75
        Height = 19
        Caption = #24180#20221#36873#25321#65306
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object Btn_Modify: TAdvGlowButton
        Left = 704
        Top = 6
        Width = 82
        Height = 28
        Caption = #20445'  '#23384
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -16
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Spacing = 10
        TabOrder = 0
        OnClick = Btn_ModifyClick
        Appearance.BorderColor = 12631218
        Appearance.BorderColorHot = 10079963
        Appearance.BorderColorDown = 4548219
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 7915518
        Appearance.ColorCheckedTo = 11918331
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 12631218
        Appearance.ColorDownTo = 12631218
        Appearance.ColorHot = 12631218
        Appearance.ColorHotTo = 12631218
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 12631218
        Appearance.ColorMirrorHotTo = 12631218
        Appearance.ColorMirrorDown = 12631218
        Appearance.ColorMirrorDownTo = 12631218
        Appearance.ColorMirrorChecked = 10480637
        Appearance.ColorMirrorCheckedTo = 5682430
        Appearance.ColorMirrorDisabled = 11974326
        Appearance.ColorMirrorDisabledTo = 15921906
        Appearance.GradientHot = ggVertical
        Appearance.GradientMirrorHot = ggVertical
        Appearance.GradientDown = ggVertical
        Appearance.GradientMirrorDown = ggVertical
        Appearance.GradientChecked = ggVertical
        Appearance.SystemFont = False
        Appearance.TextColorDown = clWhite
        Appearance.TextColorHot = clWhite
      end
      object ComboBox_Year: TRzComboBox
        Left = 290
        Top = 6
        Width = 145
        Height = 25
        TabOrder = 1
        OnChange = ComboBox_YearChange
        Items.Strings = (
          '2024'
          '2025')
      end
    end
  end
end
