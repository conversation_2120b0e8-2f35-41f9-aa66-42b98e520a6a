unit CcYsPbdj;

interface

uses
  Classes;

type
  TCcYsPbdj = class
  private
    FYspbdjid: Integer;
    FYsid: Integer;

    FBjbh: string;
    FDdh: string;
    FKh: string;
    FBjrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FSl: Integer;
    FIsRelate: string;
    FDds: Integer;
    FXdrq: string;
    FChrq1: string;
    FChrq2: string;
    FChmdd: string;
    FDds_c1: Integer;
    FDds_c2: Integer;
    FDds_c3: Integer;
    FDds_c4: Integer;
    FDds_c5: Integer;
    FDds_c6: Integer;
    FOrderDds_c1: Integer;
    FOrderDds_c2: Integer;
    FOrderDds_c3: Integer;
    FOrderDds_c4: Integer;
    FOrderDds_c5: Integer;
    FOrderDds_c6: Integer;

    FPbpmid_a: string;
    FPbggid_a: string;
    FSj_a: Double;
    FJyl_a: Double;
    FSb_a: Double;
    FZzf_a: Double;
    FZzsh_a: Double;
    FSgf_a: Double;
    FSgsh_a: Double;
    FRsf_a: Double;
    FRsfsh_a: Double;
    FYhsx_a: Double;
    FYhsxi_a: Double;
    FPbcb_a: Double;
    FBz_a: string;

    FPbpmid_b: string;
    FPbggid_b: string;
    FSj_b: Double;
    FJyl_b: Double;
    FSb_b: Double;
    FZzf_b: Double;
    FZzsh_b: Double;
    FSgf_b: Double;
    FSgsh_b: Double;
    FRsf_b: Double;
    FRsfsh_b: Double;
    FYhsx_b: Double;
    FYhsxi_b: Double;
    FPbcb_b: Double;
    FBz_b: string;

    FPbpmid_c: string;
    FPbggid_c: string;
    FSj_c: Double;
    FJyl_c: Double;
    FSb_c: Double;
    FZzf_c: Double;
    FZzsh_c: Double;
    FSgf_c: Double;
    FSgsh_c: Double;
    FRsf_c: Double;
    FRsfsh_c: Double;
    FYhsx_c: Double;
    FYhsxi_c: Double;
    FPbcb_c: Double;
    FBz_c: string;

    FPbpmid_d: string;
    FPbggid_d: string;
    FSj_d: Double;
    FJyl_d: Double;
    FSb_d: Double;
    FZzf_d: Double;
    FZzsh_d: Double;
    FSgf_d: Double;
    FSgsh_d: Double;
    FRsf_d: Double;
    FRsfsh_d: Double;
    FYhsx_d: Double;
    FYhsxi_d: Double;
    FPbcb_d: Double;
    FBz_d: string;

    FPbpmid_e: string;
    FPbggid_e: string;
    FSj_e: Double;
    FJyl_e: Double;
    FSb_e: Double;
    FZzf_e: Double;
    FZzsh_e: Double;
    FSgf_e: Double;
    FSgsh_e: Double;
    FRsf_e: Double;
    FRsfsh_e: Double;
    FYhsx_e: Double;
    FYhsxi_e: Double;
    FPbcb_e: Double;
    FBz_e: string;

    FDycb: Double;
    FDyjs: Double;

    FPbqtdj_a1: Double;
    FPbqtsh_a1: Double;
    FPbqtje_a1: Double;
    FPbqtdj_a2: Double;
    FPbqtsh_a2: Double;
    FPbqtje_a2: Double;

    FPbqtdj_b1: Double;
    FPbqtsh_b1: Double;
    FPbqtje_b1: Double;
    FPbqtdj_b2: Double;
    FPbqtsh_b2: Double;
    FPbqtje_b2: Double;

    FPbqtdj_c1: Double;
    FPbqtsh_c1: Double;
    FPbqtje_c1: Double;
    FPbqtdj_c2: Double;
    FPbqtsh_c2: Double;
    FPbqtje_c2: Double;

    FPbqtdj_d1: Double;
    FPbqtsh_d1: Double;
    FPbqtje_d1: Double;
    FPbqtdj_d2: Double;
    FPbqtsh_d2: Double;
    FPbqtje_d2: Double;

    FPbqtdj_a3: Double;
    FPbqtsh_a3: Double;
    FPbqtje_a3: Double;
    FPbqtdj_b3: Double;
    FPbqtsh_b3: Double;
    FPbqtje_b3: Double;
    FPbqtdj_c3: Double;
    FPbqtsh_c3: Double;
    FPbqtje_c3: Double;
    FPbqtdj_d3: Double;
    FPbqtsh_d3: Double;
    FPbqtje_d3: Double;

    FPbzcb: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FZzjgrmb: Double;
    FZzjgmj: Double;
    FRmb_Rmbj: Double;
    FMj_Mjj: Double;
    FAuditState: string;
  public
    property Yspbdjid: Integer read FYspbdjid write FYspbdjid;
    property Ysid: Integer read FYsid write FYsid;

    property Bjbh: string read FBjbh write FBjbh;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Bjrq: string read FBjrq write FBjrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Sl: Integer read FSl write FSl;
    property IsRelate: string read FIsRelate write FIsRelate;
    property Dds: Integer read FDds write FDds;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq1: string read FChrq1 write FChrq1;
    property Chrq2: string read FChrq2 write FChrq2;
    property Chmdd: string read FChmdd write FChmdd;

    property Dds_c1: Integer read FDds_c1 write FDds_c1;
    property Dds_c2: Integer read FDds_c2 write FDds_c2;
    property Dds_c3: Integer read FDds_c3 write FDds_c3;
    property Dds_c4: Integer read FDds_c4 write FDds_c4;
    property Dds_c5: Integer read FDds_c5 write FDds_c5;
    property Dds_c6: Integer read FDds_c6 write FDds_c6;
    property OrderDds_c1: Integer read FOrderDds_c1 write FOrderDds_c1;
    property OrderDds_c2: Integer read FOrderDds_c2 write FOrderDds_c2;
    property OrderDds_c3: Integer read FOrderDds_c3 write FOrderDds_c3;
    property OrderDds_c4: Integer read FOrderDds_c4 write FOrderDds_c4;
    property OrderDds_c5: Integer read FOrderDds_c5 write FOrderDds_c5;
    property OrderDds_c6: Integer read FOrderDds_c6 write FOrderDds_c6;

    property Pbpmid_a: string read FPbpmid_a write FPbpmid_a;
    property Pbggid_a: string read FPbggid_a write FPbggid_a;
    property Sj_a: Double read FSj_a write FSj_a;
    property Jyl_a: Double read FJyl_a write FJyl_a;
    property Sb_a: Double read FSb_a write FSb_a;
    property Zzf_a: Double read FZzf_a write FZzf_a;
    property Zzsh_a: Double read FZzsh_a write FZzsh_a;
    property Sgf_a: Double read FSgf_a write FSgf_a;
    property Sgsh_a: Double read FSgsh_a write FSgsh_a;
    property Rsf_a: Double read FRsf_a write FRsf_a;
    property Rsfsh_a: Double read FRsfsh_a write FRsfsh_a;
    property Yhsx_a: Double read FYhsx_a write FYhsx_a;
    property Yhsxi_a: Double read FYhsxi_a write FYhsxi_a;
    property Pbcb_a: Double read FPbcb_a write FPbcb_a;
    property Bz_a: string read FBz_a write FBz_a;

    property Pbpmid_b: string read FPbpmid_b write FPbpmid_b;
    property Pbggid_b: string read FPbggid_b write FPbggid_b;
    property Sj_b: Double read FSj_b write FSj_b;
    property Jyl_b: Double read FJyl_b write FJyl_b;
    property Sb_b: Double read FSb_b write FSb_b;
    property Zzf_b: Double read FZzf_b write FZzf_b;
    property Zzsh_b: Double read FZzsh_b write FZzsh_b;
    property Sgf_b: Double read FSgf_b write FSgf_b;
    property Sgsh_b: Double read FSgsh_b write FSgsh_b;
    property Rsf_b: Double read FRsf_b write FRsf_b;
    property Rsfsh_b: Double read FRsfsh_b write FRsfsh_b;
    property Yhsx_b: Double read FYhsx_b write FYhsx_b;
    property Yhsxi_b: Double read FYhsxi_b write FYhsxi_b;
    property Pbcb_b: Double read FPbcb_b write FPbcb_b;
    property Bz_b: string read FBz_b write FBz_b;

    property Pbpmid_c: string read FPbpmid_c write FPbpmid_c;
    property Pbggid_c: string read FPbggid_c write FPbggid_c;
    property Sj_c: Double read FSj_c write FSj_c;
    property Jyl_c: Double read FJyl_c write FJyl_c;
    property Sb_c: Double read FSb_c write FSb_c;
    property Zzf_c: Double read FZzf_c write FZzf_c;
    property Zzsh_c: Double read FZzsh_c write FZzsh_c;
    property Sgf_c: Double read FSgf_c write FSgf_c;
    property Sgsh_c: Double read FSgsh_c write FSgsh_c;
    property Rsf_c: Double read FRsf_c write FRsf_c;
    property Rsfsh_c: Double read FRsfsh_c write FRsfsh_c;
    property Yhsx_c: Double read FYhsx_c write FYhsx_c;
    property Yhsxi_c: Double read FYhsxi_c write FYhsxi_c;
    property Pbcb_c: Double read FPbcb_c write FPbcb_c;
    property Bz_c: string read FBz_c write FBz_c;

    property Pbpmid_d: string read FPbpmid_d write FPbpmid_d;
    property Pbggid_d: string read FPbggid_d write FPbggid_d;
    property Sj_d: Double read FSj_d write FSj_d;
    property Jyl_d: Double read FJyl_d write FJyl_d;
    property Sb_d: Double read FSb_d write FSb_d;
    property Zzf_d: Double read FZzf_d write FZzf_d;
    property Zzsh_d: Double read FZzsh_d write FZzsh_d;
    property Sgf_d: Double read FSgf_d write FSgf_d;
    property Sgsh_d: Double read FSgsh_d write FSgsh_d;
    property Rsf_d: Double read FRsf_d write FRsf_d;
    property Rsfsh_d: Double read FRsfsh_d write FRsfsh_d;
    property Yhsx_d: Double read FYhsx_d write FYhsx_d;
    property Yhsxi_d: Double read FYhsxi_d write FYhsxi_d;
    property Pbcb_d: Double read FPbcb_d write FPbcb_d;
    property Bz_d: string read FBz_d write FBz_d;

    property Pbpmid_e: string read FPbpmid_e write FPbpmid_e;
    property Pbggid_e: string read FPbggid_e write FPbggid_e;
    property Sj_e: Double read FSj_e write FSj_e;
    property Jyl_e: Double read FJyl_e write FJyl_e;
    property Sb_e: Double read FSb_e write FSb_e;
    property Zzf_e: Double read FZzf_e write FZzf_e;
    property Zzsh_e: Double read FZzsh_e write FZzsh_e;
    property Sgf_e: Double read FSgf_e write FSgf_e;
    property Sgsh_e: Double read FSgsh_e write FSgsh_e;
    property Rsf_e: Double read FRsf_e write FRsf_e;
    property Rsfsh_e: Double read FRsfsh_e write FRsfsh_e;
    property Yhsx_e: Double read FYhsx_e write FYhsx_e;
    property Yhsxi_e: Double read FYhsxi_e write FYhsxi_e;
    property Pbcb_e: Double read FPbcb_e write FPbcb_e;
    property Bz_e: string read FBz_e write FBz_e;

    property Dycb: Double read FDycb write FDycb;
    property Dyjs: Double read FDyjs write FDyjs;

    property Pbqtdj_a1: Double read FPbqtdj_a1 write FPbqtdj_a1;
    property Pbqtsh_a1: Double read FPbqtsh_a1 write FPbqtsh_a1;
    property Pbqtje_a1: Double read FPbqtje_a1 write FPbqtje_a1;
    property Pbqtdj_a2: Double read FPbqtdj_a2 write FPbqtdj_a2;
    property Pbqtsh_a2: Double read FPbqtsh_a2 write FPbqtsh_a2;
    property Pbqtje_a2: Double read FPbqtje_a2 write FPbqtje_a2;

    property Pbqtdj_b1: Double read FPbqtdj_b1 write FPbqtdj_b1;
    property Pbqtsh_b1: Double read FPbqtsh_b1 write FPbqtsh_b1;
    property Pbqtje_b1: Double read FPbqtje_b1 write FPbqtje_b1;
    property Pbqtdj_b2: Double read FPbqtdj_b2 write FPbqtdj_b2;
    property Pbqtsh_b2: Double read FPbqtsh_b2 write FPbqtsh_b2;
    property Pbqtje_b2: Double read FPbqtje_b2 write FPbqtje_b2;

    property Pbqtdj_c1: Double read FPbqtdj_c1 write FPbqtdj_c1;
    property Pbqtsh_c1: Double read FPbqtsh_c1 write FPbqtsh_c1;
    property Pbqtje_c1: Double read FPbqtje_c1 write FPbqtje_c1;
    property Pbqtdj_c2: Double read FPbqtdj_c2 write FPbqtdj_c2;
    property Pbqtsh_c2: Double read FPbqtsh_c2 write FPbqtsh_c2;
    property Pbqtje_c2: Double read FPbqtje_c2 write FPbqtje_c2;

    property Pbqtdj_d1: Double read FPbqtdj_d1 write FPbqtdj_d1;
    property Pbqtsh_d1: Double read FPbqtsh_d1 write FPbqtsh_d1;
    property Pbqtje_d1: Double read FPbqtje_d1 write FPbqtje_d1;
    property Pbqtdj_d2: Double read FPbqtdj_d2 write FPbqtdj_d2;
    property Pbqtsh_d2: Double read FPbqtsh_d2 write FPbqtsh_d2;
    property Pbqtje_d2: Double read FPbqtje_d2 write FPbqtje_d2;

    property Pbqtdj_a3: Double read FPbqtdj_a3 write FPbqtdj_a3;
    property Pbqtsh_a3: Double read FPbqtsh_a3 write FPbqtsh_a3;
    property Pbqtje_a3: Double read FPbqtje_a3 write FPbqtje_a3;
    property Pbqtdj_b3: Double read FPbqtdj_b3 write FPbqtdj_b3;
    property Pbqtsh_b3: Double read FPbqtsh_b3 write FPbqtsh_b3;
    property Pbqtje_b3: Double read FPbqtje_b3 write FPbqtje_b3;
    property Pbqtdj_c3: Double read FPbqtdj_c3 write FPbqtdj_c3;
    property Pbqtsh_c3: Double read FPbqtsh_c3 write FPbqtsh_c3;
    property Pbqtje_c3: Double read FPbqtje_c3 write FPbqtje_c3;
    property Pbqtdj_d3: Double read FPbqtdj_d3 write FPbqtdj_d3;
    property Pbqtsh_d3: Double read FPbqtsh_d3 write FPbqtsh_d3;
    property Pbqtje_d3: Double read FPbqtje_d3 write FPbqtje_d3;

    property Pbzcb: Double read FPbzcb write FPbzcb;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

    property Zzjgrmb: Double read FZzjgrmb write FZzjgrmb;
    property Zzjgmj: Double read FZzjgmj write FZzjgmj;
    property Rmb_Rmbj: Double read FRmb_Rmbj write FRmb_Rmbj;
    property Mj_Mjj: Double read FMj_Mjj write FMj_Mjj;
    property AuditState: string read FAuditState write FAuditState;

  end;

implementation

end.
