<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构详细信息分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 顶部概览区 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-6 border-l-4 border-blue-500">
            <div class="p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">宁波市智能科技研究院</h1>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-sm text-gray-600">统一信用代码: 91330201MA2G9U1234</span>
                            <span class="text-sm text-gray-600">成立日期: 2018-05-15</span>
                            <span class="text-sm text-gray-600">人员规模: 256人</span>
                            <span class="px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">低风险</span>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            编辑信息
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出报告
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 基本信息统计区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">基础信息</h2>
                    <div class="text-xs text-gray-500">更新于: 2024-01-15</div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm text-gray-500 mb-1">机构地址</p>
                        <p class="text-sm font-medium">宁波市高新区聚贤路123号</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">法定代表人</p>
                        <p class="text-sm font-medium">张明远</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">联系人</p>
                        <p class="text-sm font-medium">李科技 (0574-88123456)</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">注册资本</p>
                        <p class="text-sm font-medium">5000万元人民币</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">机构类型</p>
                        <p class="text-sm font-medium">高新技术企业/省级研发中心</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 mb-1">主营业务</p>
                        <p class="text-sm font-medium">人工智能、智能制造、工业互联网</p>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-100">
                    <p class="text-sm text-gray-500 mb-2">技术热词</p>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">机器学习</span>
                        <span class="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">计算机视觉</span>
                        <span class="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">工业机器人</span>
                        <span class="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">物联网</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-800">数据完整度</h2>
                    <div class="w-16 h-16">
                        <canvas id="completenessChart"></canvas>
                    </div>
                </div>
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">基本信息</span>
                            <span class="font-medium">92%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 92%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">财务数据</span>
                            <span class="font-medium">85%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">研发数据</span>
                            <span class="font-medium">78%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 78%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span class="text-gray-600">资质证书</span>
                            <span class="font-medium">95%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 95%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 层级结构分析区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-800">层级结构分析</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">全部展开</button>
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md">仅显示一级</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2 bg-gray-50 rounded-lg p-4 h-96 flex items-center justify-center">
                    <div class="text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                        </svg>
                        <p class="mt-2 text-sm text-gray-500">交互式树形图将在此处展示</p>
                    </div>
                </div>
                <div class="bg-gray-50 rounded-lg p-4">
                    <h3 class="text-md font-medium text-gray-800 mb-4">节点详情</h3>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-500 mb-1">节点名称</p>
                            <p class="text-sm font-medium">智能机器人研发中心</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">负责人</p>
                            <p class="text-sm font-medium">王创新</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">团队成员</p>
                            <p class="text-sm font-medium">32人</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">成立时间</p>
                            <p class="text-sm font-medium">2020-08-12</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500 mb-1">主要方向</p>
                            <div class="flex flex-wrap gap-1 mt-1">
                                <span class="px-2 py-0.5 bg-blue-50 text-blue-700 text-xs rounded-full">工业机器人</span>
                                <span class="px-2 py-0.5 bg-blue-50 text-blue-700 text-xs rounded-full">服务机器人</span>
                                <span class="px-2 py-0.5 bg-blue-50 text-blue-700 text-xs rounded-full">运动控制</span>
                            </div>
                        </div>
                        <button class="w-full mt-4 px-3 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                            查看子层级
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 荣誉与资质评估区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-800">荣誉与资质评估</h2>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                        <option>全部状态</option>
                        <option>有效期内</option>
                        <option>即将到期</option>
                        <option>已过期</option>
                    </select>
                    <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                        <option>全部等级</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                    </select>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-blue-800">证书总数</span>
                        <span class="text-2xl font-bold text-blue-800">28</span>
                    </div>
                    <div class="text-xs text-blue-700">较去年增加3项</div>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-green-800">有效证书</span>
                        <span class="text-2xl font-bold text-green-800">24</span>
                    </div>
                    <div class="text-xs text-green-700">4项即将到期</div>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-purple-800">高价值证书</span>
                        <span class="text-2xl font-bold text-purple-800">9</span>
                    </div>
                    <div class="text-xs text-purple-700">国家级8项，省级1项</div>
                </div>
            </div>
            <div class="h-64">
                <canvas id="certificateChart"></canvas>
            </div>
        </div>

        <!-- 经营与财务状况区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-800">经营与财务状况</h2>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                        <span class="text-sm">营业收入</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-sm">净利润</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                        <span class="text-sm">纳税额</span>
                    </div>
                </div>
            </div>
            <div class="h-80 mb-6">
                <canvas id="financeChart"></canvas>
            </div>
            <div class="bg-gray-50 rounded-lg p-4">
                <h3 class="text-md font-medium text-gray-800 mb-3">经营摘要</h3>
                <p class="text-sm text-gray-700">2023年营业收入预计达到2.8亿元，同比增长15%；净利润约4200万元，同比增长8%；研发投入占比12.5%，高于行业平均水平。资产负债率维持在35%的健康水平，现金流充裕。</p>
            </div>
        </div>

        <!-- 研发投入与产出分析区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-800">研发投入与产出分析</h2>
                <div class="text-sm">
                    <span class="text-gray-600">研发强度: </span>
                    <span class="font-medium text-blue-600">12.5%</span>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="h-64">
                    <canvas id="rdInputChart"></canvas>
                </div>
                <div class="h-64">
                    <canvas id="rdOutputChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 数据质量与预警区 -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-800">数据质量与预警</h2>
                <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                    全部标记为已读
                </button>
            </div>
            <div class="space-y-4">
                <div class="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="flex items-center mb-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <span class="text-sm font-medium text-yellow-800">2022年第四季度财务数据异常</span>
                            </div>
                            <p class="text-xs text-yellow-700 ml-6">营业收入环比下降25%，但净利润增长12%，需核实数据准确性</p>
                        </div>
                        <button class="text-xs text-blue-600 hover:text-blue-800">去修复</button>
                    </div>
                </div>
                <div class="p-4 border border-red-200 bg-red-50 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="flex items-center mb-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <span class="text-sm font-medium text-red-800">3项资质证书即将到期</span>
                            </div>
                            <p class="text-xs text-red-700 ml-6">高新技术企业证书等3项重要资质将在60天内到期</p>
                        </div>
                        <button class="text-xs text-blue-600 hover:text-blue-800">去处理</button>
                    </div>
                </div>
                <div class="p-4 border border-gray-200 bg-gray-50 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="flex items-center mb-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-800">5个研发项目信息不完整</span>
                            </div>
                            <p class="text-xs text-gray-700 ml-6">缺少项目负责人、预算或预期成果等关键信息</p>
                        </div>
                        <button class="text-xs text-blue-600 hover:text-blue-800">去补充</button>
                    </div>
                </div>
            </div>
            <div class="mt-6 pt-4 border-t border-gray-200">
                <h3 class="text-sm font-medium text-gray-800 mb-2">统计口径说明</h3>
                <ul class="text-xs text-gray-600 space-y-1 list-disc list-inside">
                    <li>财务数据来源于企业年度审计报告</li>
                    <li>研发投入包含人员费用、设备折旧和直接材料费用</li>
                    <li>专利数据包含发明专利、实用新型和外观设计</li>
                    <li>资质证书状态以发证机关公示信息为准</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 数据完整度图表
            const completenessCtx = document.getElementById('completenessChart').getContext('2d');
            new Chart(completenessCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [92, 8],
                        backgroundColor: ['#3b82f6', '#e5e7eb'],
                        borderWidth: 0
                    }]
                },
                options: {
                    cutout: '70%',
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    }
                }
            });

            // 证书类型图表
            const certificateCtx = document.getElementById('certificateChart').getContext('2d');
            new Chart(certificateCtx, {
                type: 'bar',
                data: {
                    labels: ['质量体系', '专利证书', '资质认证', '荣誉奖项', '其他'],
                    datasets: [{
                        label: '证书数量',
                        data: [8, 6, 5, 7, 2],
                        backgroundColor: '#3b82f6'
                    }, {
                        label: '即将到期',
                        data: [1, 0, 2, 1, 0],
                        backgroundColor: '#f59e0b'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 财务数据图表
            const financeCtx = document.getElementById('financeChart').getContext('2d');
            new Chart(financeCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '营业收入(亿元)',
                        data: [1.2, 1.5, 1.8, 2.4, 2.8],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '净利润(千万元)',
                        data: [18, 22, 30, 39, 42],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '纳税额(千万元)',
                        data: [3.2, 3.8, 4.5, 5.1, 5.6],
                        borderColor: '#8b5cf6',
                        backgroundColor: 'rgba(139, 92, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 研发投入图表
            const rdInputCtx = document.getElementById('rdInputChart').getContext('2d');
            new Chart(rdInputCtx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '研发费用(百万元)',
                        data: [12, 15, 18, 24, 35],
                        backgroundColor: '#3b82f6'
                    }, {
                        label: '研发人员(人)',
                        data: [45, 56, 68, 82, 102],
                        backgroundColor: '#8b5cf6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 研发产出图表
            const rdOutputCtx = document.getElementById('rdOutputChart').getContext('2d');
            new Chart(rdOutputCtx, {
                type: 'bubble',
                data: {
                    datasets: [{
                        label: '2019',
                        data: [{x: 12, y: 8, r: 8}],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }, {
                        label: '2020',
                        data: [{x: 15, y: 12, r: 12}],
                        backgroundColor: 'rgba(16, 185, 129, 0.7)'
                    }, {
                        label: '2021',
                        data: [{x: 18, y: 15, r: 15}],
                        backgroundColor: 'rgba(245, 158, 11, 0.7)'
                    }, {
                        label: '2022',
                        data: [{x: 24, y: 22, r: 22}],
                        backgroundColor: 'rgba(239, 68, 68, 0.7)'
                    }, {
                        label: '2023',
                        data: [{x: 35, y: 28, r: 28}],
                        backgroundColor: 'rgba(139, 92, 246, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `研发投入: ${context.raw.x}百万元 | 专利授权: ${context.raw.y}项`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: { display: true, text: '研发投入(百万元)' },
                            beginAtZero: true
                        },
                        y: {
                            title: { display: true, text: '专利授权(项)' },
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>