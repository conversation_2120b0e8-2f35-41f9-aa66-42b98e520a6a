unit CC_BjAddGuideFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, AdvGlowButton, Vcl.ExtCtrls, RzPanel,
  Vcl.Std<PERSON>trls, RzLabel, CommonUtil;

type
  TCC_BjAddGuideForm = class(TForm)
    RzPanel1: TRzPanel;
    Btn_Type1: TAdvGlowButton;
    Btn_Type2: TAdvGlowButton;
    Btn_Type3: TAdvGlowButton;
    RzLabel7: TRzLabel;
    Btn_Type4: TAdvGlowButton;
    procedure Btn_Type1Click(Sender: TObject);
    procedure Btn_Type2Click(Sender: TObject);
    procedure Btn_Type3Click(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure Btn_Type4Click(Sender: TObject);
    procedure FormClose(Sender: TObject; var Action: TCloseAction);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  CC_BjAddGuideForm: TCC_BjAddGuideForm;

implementation

{$R *.dfm}

uses IndexFrm;

procedure TCC_BjAddGuideForm.Btn_Type1Click(Sender: TObject);
begin
  SetEditKsbmGuideFlag(True);
  self.Close;
  IndexForm.FCC_BjFrame.Timer6.Enabled := True;
end;

procedure TCC_BjAddGuideForm.Btn_Type2Click(Sender: TObject);
begin
  SetEditKsbmGuideFlag(True);
  self.Close;
  IndexForm.FCC_BjFrame.Timer7.Enabled := True;
end;

procedure TCC_BjAddGuideForm.Btn_Type3Click(Sender: TObject);
begin
  SetEditKsbmGuideFlag(True);
  self.Close;
  IndexForm.FCC_BjFrame.Timer8.Enabled := True;
end;

procedure TCC_BjAddGuideForm.Btn_Type4Click(Sender: TObject);
begin
  SetEditKsbmGuideFlag(True);
  self.Close;
  IndexForm.FCC_BjFrame.Timer9.Enabled := True;
end;

procedure TCC_BjAddGuideForm.FormClose(Sender: TObject;
  var Action: TCloseAction);
begin
  if GetEditKsbmGuideFlag() = false then
  begin
    IndexForm.TabShowByTypeBj(1);
  end;

end;

procedure TCC_BjAddGuideForm.FormShow(Sender: TObject);
begin
  SetEditKsbmGuideFlag(false);
  SetEditKsbmFlag(false);

  self.Btn_Type1.Font.Style := [fsBold];
  self.Btn_Type2.Font.Style := [fsBold];
  self.Btn_Type3.Font.Style := [fsBold];
  self.Btn_Type4.Font.Style := [fsBold];

  self.Btn_Type1.Font.Color := clNavy;
  self.Btn_Type1.Appearance.BorderColor := clgray;
  self.Btn_Type1.Appearance.ColorHot := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type1.Appearance.ColorHotTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type1.Appearance.Color := clwhite;
  self.Btn_Type1.Appearance.ColorTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirror := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type2.Font.Color := clNavy;
  self.Btn_Type2.Appearance.BorderColor := clgray;
  self.Btn_Type2.Appearance.ColorHot := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type2.Appearance.ColorHotTo := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type2.Appearance.Color := clwhite;
  self.Btn_Type2.Appearance.ColorTo := clwhite;
  self.Btn_Type2.Appearance.ColorMirror := clwhite;
  self.Btn_Type2.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type3.Font.Color := clNavy;
  self.Btn_Type3.Appearance.BorderColor := clgray;
  self.Btn_Type3.Appearance.ColorHot := clwhite;
  self.Btn_Type3.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type3.Appearance.ColorHotTo := clwhite;
  self.Btn_Type3.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type3.Appearance.Color := clwhite;
  self.Btn_Type3.Appearance.ColorTo := clwhite;
  self.Btn_Type3.Appearance.ColorMirror := clwhite;
  self.Btn_Type3.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type4.Font.Color := clNavy;
  self.Btn_Type4.Appearance.BorderColor := clgray;
  self.Btn_Type4.Appearance.ColorHot := clwhite;
  self.Btn_Type4.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type4.Appearance.ColorHotTo := clwhite;
  self.Btn_Type4.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type4.Appearance.Color := clwhite;
  self.Btn_Type4.Appearance.ColorTo := clwhite;
  self.Btn_Type4.Appearance.ColorMirror := clwhite;
  self.Btn_Type4.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type1.Refresh;
  self.Btn_Type2.Refresh;
  self.Btn_Type3.Refresh;
  self.Btn_Type4.Refresh;

  self.Btn_Type1.Font.Color := clNavy;
  self.Btn_Type1.Font.Style := [fsBold];
  self.Btn_Type1.Appearance.BorderColor := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorHot := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorMirrorHot := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorHotTo := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorMirrorHotTo := $00F4E3CF;
  self.Btn_Type1.Appearance.Color := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorMirror := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorTo := $00F4E3CF;
  self.Btn_Type1.Appearance.ColorMirrorTo := $00F4E3CF;

  self.Btn_Type2.Font.Color := clNavy;
  self.Btn_Type2.Font.Style := [fsBold];
  self.Btn_Type2.Appearance.BorderColor := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorHot := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorMirrorHot := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorHotTo := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorMirrorHotTo := $00F4E3CF;
  self.Btn_Type2.Appearance.Color := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorMirror := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorTo := $00F4E3CF;
  self.Btn_Type2.Appearance.ColorMirrorTo := $00F4E3CF;

  self.Btn_Type3.Font.Color := clNavy;
  self.Btn_Type3.Font.Style := [fsBold];
  self.Btn_Type3.Appearance.BorderColor := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorHot := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorMirrorHot := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorHotTo := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorMirrorHotTo := $00F4E3CF;
  self.Btn_Type3.Appearance.Color := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorMirror := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorTo := $00F4E3CF;
  self.Btn_Type3.Appearance.ColorMirrorTo := $00F4E3CF;

  self.Btn_Type4.Font.Color := clNavy;
  self.Btn_Type4.Font.Style := [fsBold];
  self.Btn_Type4.Appearance.BorderColor := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorHot := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorMirrorHot := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorHotTo := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorMirrorHotTo := $00F4E3CF;
  self.Btn_Type4.Appearance.Color := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorMirror := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorTo := $00F4E3CF;
  self.Btn_Type4.Appearance.ColorMirrorTo := $00F4E3CF;

end;

end.
