<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">机构综合分析流程图</text>

  <!-- 阶段一：数据触发与获取 -->
  <text x="700" y="80" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据触发与获取</text>
  
  <!-- 节点1: 用户触发 -->
  <g transform="translate(100, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户触发</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">页面进入或筛选调整</text>
  </g>

  <!-- 节点2: 数据拉取 -->
  <g transform="translate(400, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">综合数据拉取</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">机构、行业、区域、载体表</text>
  </g>

  <!-- 节点3: 数据合并 -->
  <g transform="translate(700, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据合并</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">科技大脑+增量数据</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 135 Q 350 135 400 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 620 135 Q 660 135 700 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据校验与处理 -->
  <text x="700" y="230" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据校验与处理</text>

  <!-- 节点4: 完整性校验 -->
  <g transform="translate(200, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">完整性校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">口径校验、质量队列</text>
  </g>

  <!-- 节点5: 聚合统计 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">聚合统计</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">多维度指标计算</text>
  </g>

  <!-- 节点6: 展示模型 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">展示模型</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">比例、数量、增幅数据</text>
  </g>

  <!-- 连接线从数据合并到完整性校验 -->
  <path d="M 810 170 C 810 200, 300 220, 300 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 285 Q 450 285 500 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：可视化渲染与交互 -->
  <text x="700" y="380" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：可视化渲染与交互</text>

  <!-- 节点7: 前端渲染 -->
  <g transform="translate(300, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端渲染</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">环形图、柱状图等</text>
  </g>

  <!-- 节点8: 交互事件 -->
  <g transform="translate(600, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">交互事件</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">钻取、筛选监听</text>
  </g>

  <!-- 连接线从展示模型到前端渲染 -->
  <path d="M 900 320 C 900 350, 400 370, 400 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 7 -> 8 -->
  <path d="M 500 435 Q 550 435 600 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：报告导出与日志记录 -->
  <text x="700" y="530" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：报告导出与日志记录</text>

  <!-- 节点9: 报告生成 -->
  <g transform="translate(200, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">报告生成</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">PDF文件、下载队列</text>
  </g>

  <!-- 节点10: 访问日志 -->
  <g transform="translate(500, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">访问日志</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">用户行为记录</text>
  </g>

  <!-- 节点11: 监控服务 -->
  <g transform="translate(800, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">监控服务</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">性能优化、使用统计</text>
  </g>

  <!-- 连接线从交互事件到报告生成 -->
  <path d="M 700 470 C 700 500, 300 520, 300 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="500" y="495" font-size="11" fill="#666">导出请求</text>

  <!-- 连接线从交互事件到访问日志和监控服务 -->
  <path d="M 700 470 Q 700 510 600 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 470 C 700 500, 900 520, 900 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：质量修复与反馈循环 -->
  <text x="700" y="680" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段五：质量修复与反馈循环</text>

  <!-- 节点12: 质量修复 -->
  <g transform="translate(400, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量修复</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">缺失冲突数据处理</text>
  </g>

  <!-- 节点13: 筛选优化 -->
  <g transform="translate(700, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选优化</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">基于监控数据调整</text>
  </g>

  <!-- 连接线从完整性校验到质量修复 -->
  <path d="M 300 320 C 300 500, 500 650, 500 700" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="510" font-size="11" fill="#666">质量问题</text>

  <!-- 连接线从监控服务到筛选优化 -->
  <path d="M 900 620 Q 900 660 800 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从质量修复回到数据拉取 -->
  <path d="M 400 735 C 300 750, 200 750, 100 200, 100 150, 400 120" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="760" font-size="11" fill="#666">数据修复循环</text>

  <!-- 反馈循环：从筛选优化回到用户触发 -->
  <path d="M 700 735 C 600 750, 500 750, 200 750, 50 750, 50 200, 50 150, 100 120" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="780" font-size="11" fill="#666">优化反馈循环</text>

</svg>