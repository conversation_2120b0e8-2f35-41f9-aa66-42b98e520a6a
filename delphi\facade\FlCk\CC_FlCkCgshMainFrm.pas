unit CC_FlCkCgshMainFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  Vcl.Grids, AdvObj, BaseGrid, AdvGrid, DBAdvGrid, DMUtil,
  Vcl.ImgList, RzButton, Vcl.<PERSON>d<PERSON>, Vcl.Mask, RzEdit,
  RzLabel, Txm, RzCmboBx, VclTee.TeeGDIPlus, VclTee.TeEngine, VclTee.TeeTools,
  VclTee.TeePageNumTool, VclTee.Series, VclTee.TeeDoubleHorizBar,
  VclTee.TeeProcs, VclTee.Chart, VclTee.TeeURL, VclTee.TeeSeriesTextEd,
  VclTee.TeeExcelSource, RzBckgnd, Vcl.Imaging.pngimage, AdvGlowButton, CcXjll,
  BankModelConfig, RzCommon, CommonUtil, CC_ScConPlanFrm, RzTabs, CcSc, XxDd,
  CC_RkConFrm, CC_FlCkCgsh1MainFrm;

type
  TCC_FlCkCgshMainFrame = class(TFrame)
    MainPanel: TRzPanel;
    TopPanel: TRzPanel;
    TopHeadPanel: TRzPanel;
    TopBluePanel: TRzPanel;
    RightPanel: TRzPanel;
    RzPageControl1: TRzPageControl;
    TabSheet2: TRzTabSheet;
    RzPanel1: TRzPanel;
    TopButtonPanel: TRzPanel;
    Btn_Type1: TAdvGlowButton;
    TabSheet1: TRzTabSheet;
    RzPanel2: TRzPanel;
    TabSheet3: TRzTabSheet;
    RzPanel3: TRzPanel;
    procedure Btn_Type1Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_FlCkCgsh1MainFrame: TCC_FlCkCgsh1MainFrame;
    procedure Init();
    procedure TabShowByType(showtype: integer);
  end;

var

  UpdateFlag: integer;
  UpdateID: string;
  DMUtilSelect: TDataModule1;
  G_Sztype: integer;

implementation

uses IndexFrm;

{$R *.dfm}

procedure TCC_FlCkCgshMainFrame.TabShowByType(showtype: integer);
begin

  self.Btn_Type1.Font.Style := [fsBold];

  self.Btn_Type1.Font.Color := clNavy;
  self.Btn_Type1.Appearance.BorderColor := clgray;
  self.Btn_Type1.Appearance.ColorHot := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHot := clwhite;
  self.Btn_Type1.Appearance.ColorHotTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorHotTo := clwhite;
  self.Btn_Type1.Appearance.Color := clwhite;
  self.Btn_Type1.Appearance.ColorTo := clwhite;
  self.Btn_Type1.Appearance.ColorMirror := clwhite;
  self.Btn_Type1.Appearance.ColorMirrorTo := clwhite;

  self.Btn_Type1.Refresh;

  self.Btn_Type1.Enabled := false;

  if showtype = 1 then
  begin
    self.Btn_Type1.Font.Color := clwhite;
    self.Btn_Type1.Font.Style := [fsBold];
    self.Btn_Type1.Appearance.BorderColor := TabButtonColor;
    self.Btn_Type1.Appearance.ColorHot := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorHot := TabButtonColor;
    self.Btn_Type1.Appearance.ColorHotTo := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorHotTo := TabButtonColor;
    self.Btn_Type1.Appearance.Color := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirror := TabButtonColor;
    self.Btn_Type1.Appearance.ColorTo := TabButtonColor;
    self.Btn_Type1.Appearance.ColorMirrorTo := TabButtonColor;
  end;

end;

procedure TCC_FlCkCgshMainFrame.Btn_Type1Click(Sender: TObject);
begin

  TabShowByType(1);

  if (FCC_FlCkCgsh1MainFrame <> nil) then
  begin
    FCC_FlCkCgsh1MainFrame.Free;
    FCC_FlCkCgsh1MainFrame := nil;
  end;

  if (FCC_FlCkCgsh1MainFrame = nil) then
  begin
    FCC_FlCkCgsh1MainFrame := TCC_FlCkCgsh1MainFrame.Create(Application);
    FCC_FlCkCgsh1MainFrame.Parent := self.RzPanel2;
    FCC_FlCkCgsh1MainFrame.Align := alClient;
    FCC_FlCkCgsh1MainFrame.Init(6, 1);
  end;

  self.Btn_Type1.Enabled := true;
end;

procedure TCC_FlCkCgshMainFrame.Init();
begin

  TabShowByType(1);

  if (FCC_FlCkCgsh1MainFrame = nil) then
  begin
    FCC_FlCkCgsh1MainFrame := TCC_FlCkCgsh1MainFrame.Create(Application);
    FCC_FlCkCgsh1MainFrame.Parent := self.RzPanel2;
    FCC_FlCkCgsh1MainFrame.Align := alClient;
    FCC_FlCkCgsh1MainFrame.Init(6, 1);
  end;

  self.Btn_Type1.Enabled := true;
end;

end.
