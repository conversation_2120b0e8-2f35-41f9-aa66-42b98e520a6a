<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校科技人才展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">高等院校科技人才展示</h1>
            <p class="text-gray-600">集中呈现师资队伍规模、学科分布与科研能力，为人才管理与资源配置提供数据支撑</p>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('teacher')">
                <div class="flex items-center">
                    <div class="bg-blue-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">教师总数</p>
                        <p class="text-xl font-bold text-gray-900">1,245</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            12.5% 同比
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('talent')">
                <div class="flex items-center">
                    <div class="bg-purple-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">高层次人才</p>
                        <p class="text-xl font-bold text-gray-900">156</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            8.3% 同比
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('professor')">
                <div class="flex items-center">
                    <div class="bg-green-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">教授人数</p>
                        <p class="text-xl font-bold text-gray-900">287</p>
                        <p class="text-xs text-red-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            2.1% 同比
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('project')">
                <div class="flex items-center">
                    <div class="bg-yellow-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">在研项目</p>
                        <p class="text-xl font-bold text-gray-900">856</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            15.7% 同比
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('cooperation')">
                <div class="flex items-center">
                    <div class="bg-indigo-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">企业合作项目</p>
                        <p class="text-xl font-bold text-gray-900">324</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            23.8% 同比
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-4 cursor-pointer hover:shadow-lg transition-shadow" onclick="openDetailModal('postdoc')">
                <div class="flex items-center">
                    <div class="bg-pink-100 p-3 rounded-full mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">博士后人数</p>
                        <p class="text-xl font-bold text-gray-900">89</p>
                        <p class="text-xs text-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                            </svg>
                            7.2% 同比
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                条件筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">学科领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部学科</option>
                        <option value="engineering">工学</option>
                        <option value="science">理学</option>
                        <option value="medicine">医学</option>
                        <option value="agriculture">农学</option>
                        <option value="management">管理学</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">职称等级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部职称</option>
                        <option value="professor">教授</option>
                        <option value="associate">副教授</option>
                        <option value="lecturer">讲师</option>
                        <option value="assistant">助教</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">学历层级</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部学历</option>
                        <option value="doctor">博士</option>
                        <option value="master">硕士</option>
                        <option value="bachelor">本科</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">人才类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="academician">院士</option>
                        <option value="expert">专家</option>
                        <option value="scholar">学者</option>
                        <option value="youth">青年人才</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属学院</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部学院</option>
                        <option value="computer">计算机学院</option>
                        <option value="mechanical">机械学院</option>
                        <option value="material">材料学院</option>
                        <option value="electronic">电子学院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入姓名或研究领域" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目经费区间</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="最小值" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" placeholder="最大值" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">教学评价</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部评价</option>
                        <option value="excellent">优秀</option>
                        <option value="good">良好</option>
                        <option value="medium">中等</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 人才列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                        人才列表
                    </h2>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            新增人才
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最高学历</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人才类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属学院</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在研项目</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张教授</div>
                                        <div class="text-sm text-gray-500">计算机学院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">计算机科学与技术</div>
                                <div class="text-sm text-gray-500">人工智能</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    教授
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                博士
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">
                                    专家
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                计算机学院
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                12
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button onclick="openProfileModal('1')" class="text-blue-600 hover:text-blue-900">查看画像</button>
                                <button onclick="openProjectModal('1')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李教授</div>
                                        <div class="text-sm text-gray-500">机械学院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">机械工程</div>
                                <div class="text-sm text-gray-500">智能制造</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    教授
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                博士
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                    院士
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                机械学院
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                8
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button onclick="openProfileModal('2')" class="text-blue-600 hover:text-blue-900">查看画像</button>
                                <button onclick="openProjectModal('2')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">王副教授</div>
                                        <div class="text-sm text-gray-500">材料学院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">材料科学与工程</div>
                                <div class="text-sm text-gray-500">纳米材料</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                    副教授
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                博士
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                    青年人才
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                材料学院
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                6
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button onclick="openProfileModal('3')" class="text-blue-600 hover:text-blue-900">查看画像</button>
                                <button onclick="openProjectModal('3')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">赵讲师</div>
                                        <div class="text-sm text-gray-500">电子学院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">电子工程</div>
                                <div class="text-sm text-gray-500">集成电路</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    讲师
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                硕士
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    普通
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                电子学院
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                3
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button onclick="openProfileModal('4')" class="text-blue-600 hover:text-blue-900">查看画像</button>
                                <button onclick="openProjectModal('4')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">刘助教</div>
                                        <div class="text-sm text-gray-500">计算机学院</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">计算机科学与技术</div>
                                <div class="text-sm text-gray-500">软件工程</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    助教
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                硕士
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                    普通
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                计算机学院
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                1
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                                <button onclick="openProfileModal('5')" class="text-blue-600 hover:text-blue-900">查看画像</button>
                                <button onclick="openProjectModal('5')" class="text-indigo-600 hover:text-indigo-900">下钻项目</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">1,245</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 学科领域分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">学科领域分布</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">柱状图</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">饼图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="subjectChart"></canvas>
                </div>
            </div>
            
            <!-- 职称等级分布 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">职称等级分布</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">堆积图</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">雷达图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="titleChart"></canvas>
                </div>
            </div>
            
            <!-- 年度项目承载 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">年度项目承载</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">折线图</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">面积图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="projectChart"></canvas>
                </div>
            </div>
            
            <!-- 高层次人才类型 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">高层次人才类型</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">饼图</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">环形图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="talentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 人才画像侧栏 -->
    <div id="profileModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex justify-end h-full">
            <div class="bg-white w-full max-w-2xl h-full overflow-y-auto">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">人才画像</h3>
                    <button onclick="closeProfileModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <!-- 基础信息 -->
                    <div class="flex items-start mb-6">
                        <div class="flex-shrink-0 mr-4">
                            <img class="h-20 w-20 rounded-full" src="https://source.unsplash.com/random/200x200/?portrait" alt="">
                        </div>
                        <div>
                            <h4 class="text-xl font-bold text-gray-900">张教授</h4>
                            <p class="text-sm text-gray-500">计算机学院 | 教授 | 博士</p>
                            <div class="flex flex-wrap gap-2 mt-2">
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">专家</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">博士生导师</span>
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">学科带头人</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 标签页导航 -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-8">
                            <button class="border-b-2 border-blue-500 text-blue-600 px-4 py-3 text-sm font-medium">教育经历</button>
                            <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">科研项目</button>
                            <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">论文专著</button>
                            <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">专利成果</button>
                            <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">产学研合作</button>
                        </nav>
                    </div>
                    
                    <!-- 教育经历内容 -->
                    <div>
                        <h5 class="text-md font-semibold text-gray-900 mb-4">教育经历</h5>
                        <div class="space-y-4">
                            <div class="border-l-4 border-blue-500 pl-4 py-1">
                                <p class="text-sm font-medium text-gray-900">清华大学</p>
                                <p class="text-xs text-gray-500">计算机科学与技术 | 博士 | 2005-2010</p>
                            </div>
                            <div class="border-l-4 border-blue-500 pl-4 py-1">
                                <p class="text-sm font-medium text-gray-900">浙江大学</p>
                                <p class="text-xs text-gray-500">计算机科学与技术 | 硕士 | 2002-2005</p>
                            </div>
                            <div class="border-l-4 border-blue-500 pl-4 py-1">
                                <p class="text-sm font-medium text-gray-900">浙江大学</p>
                                <p class="text-xs text-gray-500">计算机科学与技术 | 本科 | 1998-2002</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 职称晋升轨迹 -->
                    <div class="mt-8">
                        <h5 class="text-md font-semibold text-gray-900 mb-4">职称晋升轨迹</h5>
                        <div class="relative">
                            <div class="absolute left-4 top-0 h-full w-0.5 bg-gray-200"></div>
                            <div class="space-y-6">
                                <div class="relative pl-8">
                                    <div class="absolute left-0 top-0 h-2 w-2 rounded-full bg-blue-500 mt-1"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">教授</p>
                                        <p class="text-xs text-gray-500">2015年至今 | 宁波大学</p>
                                    </div>
                                </div>
                                <div class="relative pl-8">
                                    <div class="absolute left-0 top-0 h-2 w-2 rounded-full bg-blue-500 mt-1"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">副教授</p>
                                        <p class="text-xs text-gray-500">2010-2015 | 宁波大学</p>
                                    </div>
                                </div>
                                <div class="relative pl-8">
                                    <div class="absolute left-0 top-0 h-2 w-2 rounded-full bg-blue-500 mt-1"></div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">讲师</p>
                                        <p class="text-xs text-gray-500">2005-2010 | 宁波大学</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 底部操作按钮 -->
                    <div class="mt-8 flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            下载个人简历
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            导出PDF
                        </button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                            分享链接
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="mb-6">
                        <h4 class="text-xl font-bold text-gray-900">基于深度学习的智能制造系统研究</h4>
                        <p class="text-sm text-gray-500">项目编号：NBU2023-001 | 负责人：张教授</p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h5 class="text-md font-semibold text-gray-900 mb-2">项目基本信息</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex">
                                    <span class="text-gray-500 w-24">项目类型：</span>
                                    <span class="text-gray-900">国家自然科学基金</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">起止时间：</span>
                                    <span class="text-gray-900">2023.01-2026.12</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">项目经费：</span>
                                    <span class="text-gray-900">120万元</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">参与人数：</span>
                                    <span class="text-gray-900">8人</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h5 class="text-md font-semibold text-gray-900 mb-2">合作信息</h5>
                            <div class="space-y-2 text-sm">
                                <div class="flex">
                                    <span class="text-gray-500 w-24">合作单位：</span>
                                    <span class="text-gray-900">宁波智能制造研究院</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">企业合作：</span>
                                    <span class="text-gray-900">宁波XX科技</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">项目状态：</span>
                                    <span class="text-gray-900">在研</span>
                                </div>
                                <div class="flex">
                                    <span class="text-gray-500 w-24">成果形式：</span>
                                    <span class="text-gray-900">论文/专利/系统</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <h5 class="text-md font-semibold text-gray-900 mb-2">项目简介</h5>
                        <p class="text-sm text-gray-700">
                            本项目针对智能制造领域中的关键技术问题，研究基于深度学习的智能检测与优化方法。通过构建多模态数据融合模型，实现制造过程中的质量预测与工艺优化，提升生产效率和产品质量。研究成果将应用于宁波本地制造企业的智能化改造。
                        </p>
                    </div>
                    
                    <div class="mb-6">
                        <h5 class="text-md font-semibold text-gray-900 mb-2">项目成员</h5>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div class="flex items-center">
                                <img class="h-8 w-8 rounded-full mr-2" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                <span class="text-sm">李副教授</span>
                            </div>
                            <div class="flex items-center">
                                <img class="h-8 w-8 rounded-full mr-2" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                <span class="text-sm">王讲师</span>
                            </div>
                            <div class="flex items-center">
                                <img class="h-8 w-8 rounded-full mr-2" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                <span class="text-sm">张博士</span>
                            </div>
                            <div class="flex items-center">
                                <img class="h-8 w-8 rounded-full mr-2" src="https://source.unsplash.com/random/100x100/?portrait" alt="">
                                <span class="text-sm">刘工程师</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            查看全部成果
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            导出项目报告
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openProfileModal(id) {
            document.getElementById('profileModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeProfileModal() {
            document.getElementById('profileModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openProjectModal(id) {
            document.getElementById('projectModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeProjectModal() {
            document.getElementById('projectModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openDetailModal(type) {
            alert('将下钻到 ' + type + ' 详情页面');
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 学科领域分布图表
            const subjectCtx = document.getElementById('subjectChart').getContext('2d');
            new Chart(subjectCtx, {
                type: 'bar',
                data: {
                    labels: ['工学', '理学', '医学', '农学', '管理学', '其他'],
                    datasets: [{
                        label: '教师人数',
                        data: [856, 642, 523, 387, 298, 141],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 职称等级分布图表
            const titleCtx = document.getElementById('titleChart').getContext('2d');
            new Chart(titleCtx, {
                type: 'bar',
                data: {
                    labels: ['计算机学院', '机械学院', '材料学院', '电子学院'],
                    datasets: [
                        {
                            label: '教授',
                            data: [45, 38, 32, 28],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        },
                        {
                            label: '副教授',
                            data: [68, 52, 45, 40],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        },
                        {
                            label: '讲师',
                            data: [32, 28, 25, 22],
                            backgroundColor: 'rgba(249, 168, 37, 0.8)',
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // 年度项目承载图表
            const projectCtx = document.getElementById('projectChart').getContext('2d');
            new Chart(projectCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '国家级项目',
                        data: [45, 52, 68, 72, 85],
                        borderColor: 'rgba(239, 68, 68, 1)',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '省部级项目',
                        data: [78, 85, 92, 98, 105],
                        borderColor: 'rgba(16, 185, 129, 1)',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4,
                        fill: true
                    },
                    {
                        label: '横向项目',
                        data: [56, 62, 78, 85, 92],
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 高层次人才类型图表
            const talentCtx = document.getElementById('talentChart').getContext('2d');
            new Chart(talentCtx, {
                type: 'pie',
                data: {
                    labels: ['院士', '专家', '学者', '青年人才'],
                    datasets: [{
                        data: [5, 32, 68, 51],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(249, 168, 37, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ],
                        borderColor: [
                            'rgba(239, 68, 68, 1)',
                            'rgba(59, 130, 246, 1)',
                            'rgba(249, 168, 37, 1)',
                            'rgba(16, 185, 129, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('profileModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeProfileModal();
                }
            });

            document.getElementById('projectModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeProjectModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!document.getElementById('profileModal').classList.contains('hidden')) {
                        closeProfileModal();
                    }
                    if (!document.getElementById('projectModal').classList.contains('hidden')) {
                        closeProjectModal();
                    }
                }
            });
        });
    </script>
</body>
</html>