<!-- Generated by SpecStory -->

# Designing a management backend or data dashboard (2025-07-07 06:43Z)

_**User**_

@企业-项目展示_设计页面.md 请处理并生成同名的html文件，保存在public目录下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户需要我根据附件中的设计文档生成一个HTML文件。让我仔细分析这个文档。

文档标题是"企业-项目展示_设计页面.md"，功能名是"[企业-项目展示]"。

让我分析需求：

1. 功能说明：项目展示功能面向企业创新主体，集中呈现其研发项目与高新技术产业投资项目的全生命周期信息，通过可视化概览与可下钻的多维列表。

2. 界面结构包括：
   - 统计概览区：顶部卡片形式展示核心指标（研发项目总数、投资项目总数、在研项目数量、已验收项目数量、项目总投入等），右下角小型环形进度条
   - 条件筛选区：项目类型单选框、行业领域下拉框、项目状态复选框、时间范围选择器、关键字搜索框
   - 项目列表区：分栏数据网格，包含项目名称、项目类别、行业领域、负责人、当前阶段、立项金额、拨付金额、产出概览，每行有"查看详情"按钮
   - 关联图表区：可折叠面板，柱状图、堆积面积图、散点图
   - 项目详情抽屉：右侧滑出，展示项目详细信息

根据这个分析，这是一个管理后台类页面，因为：
- 有大量的筛选条件和表格展示
</details></think>

---

我将根据设计文档为您生成企业项目展示页面的HTML文件。这是一个管理后台类页面，我将按照文档要求的界面结构和功能来实现。

---

Error calling tool 'edit_file'.

---



---

_**User**_



---

_**Assistant**_



---

