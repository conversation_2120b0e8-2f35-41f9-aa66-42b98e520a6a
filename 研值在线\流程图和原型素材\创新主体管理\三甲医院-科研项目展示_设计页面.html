<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-科研项目展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研项目展示</h1>

        <!-- 项目概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">自然科学基金项目</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="fundChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">国家重点研发计划</p>
                        <p class="text-2xl font-bold text-gray-900">87</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="planChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">重大专项</p>
                        <p class="text-2xl font-bold text-gray-900">42</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="majorChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">其他科技计划</p>
                        <p class="text-2xl font-bold text-gray-900">65</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="otherChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="natural">自然科学基金</option>
                        <option value="national">国家重点研发计划</option>
                        <option value="major">重大专项</option>
                        <option value="other">其他科技计划</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="medical">医学</option>
                        <option value="biology">生物学</option>
                        <option value="chemistry">化学</option>
                        <option value="material">材料科学</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">执行阶段</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="preparation">准备阶段</option>
                        <option value="execution">执行阶段</option>
                        <option value="conclusion">结题阶段</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">立项年度</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" min="2010" max="2024" placeholder="起始" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span>-</span>
                        <input type="number" min="2010" max="2024" placeholder="结束" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">关键字搜索</label>
                    <input type="text" placeholder="项目名称/负责人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 项目列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <div>
                            <h2 class="text-lg font-medium text-gray-800">项目列表</h2>
                            <p class="text-sm text-gray-500">共找到 156 条记录</p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">立项金额</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">执行状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">基于人工智能的肿瘤早期诊断研究</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">自然科学基金</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">120万</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">执行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新型抗肿瘤药物研发</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家重点研发计划</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学医学院</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">350万</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">准备阶段</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">心血管疾病精准医疗研究</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重大专项</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市医疗中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">500万</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">结题阶段</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新型医用材料研发与应用</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">其他科技计划</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市生物医药研究所</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">80万</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">执行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">156</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目分析图表区 -->
            <div class="w-full lg:w-1/3 lg:min-w-[350px]">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-800">项目分析</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">收起</button>
                    </div>
                    <div class="space-y-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">各项目类型年度立项趋势</h4>
                            <div class="h-48">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">重点技术领域分布</h4>
                            <div class="h-48">
                                <canvas id="fieldDistChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情抽屉 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 w-full lg:w-1/2 h-full bg-white shadow-lg overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900">项目详情</h2>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">基本信息</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">项目名称</p>
                                <p class="text-sm font-medium text-gray-900">基于人工智能的肿瘤早期诊断研究</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目类型</p>
                                <p class="text-sm font-medium text-gray-900">自然科学基金</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目编号</p>
                                <p class="text-sm font-medium text-gray-900">NSFC-2023-12345</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">立项金额</p>
                                <p class="text-sm font-medium text-gray-900">120万元</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">承担单位</p>
                                <p class="text-sm font-medium text-gray-900">宁波市第一医院</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">项目负责人</p>
                                <p class="text-sm font-medium text-gray-900">张教授</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">执行周期</p>
                                <p class="text-sm font-medium text-gray-900">2023-01-01 至 2025-12-31</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">当前状态</p>
                                <p class="text-sm font-medium text-gray-900"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">执行中</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- 申报与批复节点 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">申报与批复节点</h3>
                        <div class="space-y-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">项目申报</p>
                                    <p class="text-sm text-gray-500">2022-08-15</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">形式审查通过</p>
                                    <p class="text-sm text-gray-500">2022-09-10</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">专家评审通过</p>
                                    <p class="text-sm text-gray-500">2022-11-20</p>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <p class="text-sm font-medium text-gray-900">立项批复</p>
                                    <p class="text-sm text-gray-500">2022-12-15</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 合作单位及角色 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">合作单位及角色</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费分配</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">宁波市第一医院</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">牵头单位</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">张教授</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">60万</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">宁波大学医学院</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">协作单位</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">李教授</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">30万</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">宁波市生物医药研究所</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">协作单位</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">王研究员</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">30万</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 阶段实施进度 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">阶段实施进度</h3>
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">总体进度</span>
                                    <span class="text-sm font-medium text-gray-700">45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm font-medium text-gray-700 mb-1">第一阶段：数据收集</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">已完成80%</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700 mb-1">第二阶段：算法开发</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-500 h-2 rounded-full" style="width: 30%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">已完成30%</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700 mb-1">第三阶段：临床验证</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gray-300 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">未开始</p>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-700 mb-1">第四阶段：成果总结</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-gray-300 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">未开始</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 经费拨付情况 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">经费拨付情况</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付批次</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付日期</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付金额</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付状态</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">凭证号</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">第一批</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">2023-02-15</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">50万</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已到账</span></td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">V20230215001</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">第二批</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">2023-08-20</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">40万</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待拨付</span></td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">-</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">第三批</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">2024-02-15</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">30万</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未到拨付期</span></td>
                                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 产出成果 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">产出成果</h3>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-700">已发表论文</p>
                                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside">
                                    <li>张XX, 李XX. 基于深度学习的肿瘤早期诊断方法研究. 中国医学影像技术, 2023, 39(5): 123-130.</li>
                                    <li>王XX, 张XX. 多模态医学影像融合算法研究. 计算机应用研究, 2023, 40(3): 456-462.</li>
                                </ul>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">专利申请</p>
                                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside">
                                    <li>一种基于深度学习的肿瘤早期诊断系统. 申请号: CN202310123456.7</li>
                                </ul>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-700">软件著作权</p>
                                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside">
                                    <li>肿瘤早期诊断系统V1.0. 登记号: 2023SR123456</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex justify-between mt-6">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            返回列表定位
                        </button>
                        <div class="space-x-3">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下载PDF
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                跳转详情页
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览卡片小图表
            const initSmallChart = (id, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                        datasets: [{
                            data: [12, 19, 3, 5],
                            borderColor: color,
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { display: false }, y: { display: false } }
                    }
                });
            };

            initSmallChart('fundChart', '#3B82F6');
            initSmallChart('planChart', '#10B981');
            initSmallChart('majorChart', '#F59E0B');
            initSmallChart('otherChart', '#8B5CF6');

            // 初始化项目趋势图表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '自然科学基金',
                            data: [45, 52, 68, 74, 89],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '国家重点研发计划',
                            data: [32, 38, 42, 50, 65],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '重大专项',
                            data: [18, 22, 25, 30, 42],
                            borderColor: '#F59E0B',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // 初始化技术领域分布图表
            const fieldCtx = document.getElementById('fieldDistChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'radar',
                data: {
                    labels: ['医学', '生物学', '化学', '材料科学', '信息科学', '工程学'],
                    datasets: [
                        {
                            label: '项目数量',
                            data: [65, 59, 90, 81, 56, 55],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: '#3B82F6',
                            pointBackgroundColor: '#3B82F6',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#3B82F6'
                        },
                        {
                            label: '经费占比',
                            data: [28, 48, 40, 19, 96, 27],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: '#10B981',
                            pointBackgroundColor: '#10B981',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: '#10B981'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20
                            }
                        }
                    },
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 点击详情抽屉外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭详情抽屉
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>