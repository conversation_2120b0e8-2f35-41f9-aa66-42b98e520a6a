<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策级别管理流程</text>

  <!-- 阶段一：政策录入与校验 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：政策录入与校验</text>
  
  <!-- 节点1: 政策新增/修改 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">政策新增/修改</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户操作)</text>
  </g>

  <!-- 节点2: 字段校验 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(必填字段与文号唯一性)</text>
  </g>

  <!-- 节点3: 生成政策记录 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成政策记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(标记状态为"待审核")</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 L 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 165 L 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与发布 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与发布</text>

  <!-- 节点4: 推送至管理员 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送至管理员</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(按预设规则推送待审核记录)</text>
  </g>

  <!-- 节点5: 管理员审核 -->
  <g transform="translate(500, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(审核政策内容)</text>
  </g>

  <!-- 节点6: 状态更新 -->
  <g transform="translate(800, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(更新为"已发布")</text>
  </g>
  
  <!-- 节点7: 写入检索索引 -->
  <g transform="translate(800, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入检索索引</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(触发同步任务)</text>
  </g>

  <!-- 连接线 3 -> 4 -->
  <path d="M 900 200 C 900 240, 400 240, 300 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 400 335 L 500 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 335 L 800 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6 -> 7 -->
  <path d="M 900 370 L 900 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：批量处理 -->
  <text x="300" y="420" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：批量处理</text>

  <!-- 节点8: 批量导入 -->
  <g transform="translate(50, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(上传文件)</text>
  </g>

  <!-- 节点9: 解析校验 -->
  <g transform="translate(300, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">解析校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(逐行校验内容)</text>
  </g>

  <!-- 节点10: 生成待审核记录 -->
  <g transform="translate(200, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成待审核记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(校验通过的数据)</text>
  </g>

  <!-- 节点11: 错误报告 -->
  <g transform="translate(450, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">错误报告</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(校验失败的数据)</text>
  </g>
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 250 505 L 300 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 (校验通过) -->
  <path d="M 400 540 C 400 565, 350 565, 300 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="350" y="565" text-anchor="middle" font-size="12" fill="#555">校验通过</text>

  <!-- 连接线 9 -> 11 (校验失败) -->
  <path d="M 400 540 C 400 565, 450 565, 500 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="450" y="565" text-anchor="middle" font-size="12" fill="#555">校验失败</text>

  <!-- 连接线 10 -> 4 -->
  <path d="M 300 590 C 150 500, 150 400, 200 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 11 -> 8 (修正后重新导入) -->
  <path d="M 550 590 C 650 500, 650 400, 150 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="600" y="500" text-anchor="middle" font-size="12" fill="#555">修正后重新导入</text>

  <!-- 阶段四：失效管理与导出 -->
  <text x="900" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：失效管理与导出</text>

  <!-- 节点12: 定时扫描 -->
  <g transform="translate(700, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时扫描</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(每日扫描政策库)</text>
  </g>

  <!-- 节点13: 失效提醒 -->
  <g transform="translate(700, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">失效提醒</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(推送给政策管理员)</text>
  </g>

  <!-- 节点14: 导出请求 -->
  <g transform="translate(950, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出请求</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户触发)</text>
  </g>

  <!-- 节点15: 数据封装 -->
  <g transform="translate(950, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据封装</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(生成下载链接)</text>
  </g>

  <!-- 节点16: 审计库 -->
  <g transform="translate(400, 730)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(记录导出操作供合规追踪)</text>
  </g>

  <!-- 连接线 12 -> 13 -->
  <path d="M 800 640 L 800 690" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 14 -> 15 -->
  <path d="M 1050 640 L 1050 690" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 15 -> 16 -->
  <path d="M 950 725 C 800 725, 650 725, 600 730" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 定时任务标识 -->
  <text x="700" y="550" font-size="12" text-anchor="middle" font-style="italic" fill="#555">每日定时任务</text>

</svg>