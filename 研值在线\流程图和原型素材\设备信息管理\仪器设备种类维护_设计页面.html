<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器设备种类维护</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">仪器设备种类维护</h1>
            <p class="text-gray-600">构建统一、标准化的设备分类体系，为设备管理、数据分析和绩效考核提供坚实的数据基础</p>
        </div>

        <div class="flex gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧分类树状管理区 -->
            <div class="w-1/3 bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                        </svg>
                        设备分类树
                    </h2>
                    <div class="flex space-x-2">
                        <button onclick="openAddCategoryModal()" class="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增
                        </button>
                        <button onclick="expandAll()" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm">
                            全部展开
                        </button>
                        <button onclick="collapseAll()" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm">
                            全部收起
                        </button>
                    </div>
                </div>
                
                <div class="border rounded-md p-2 h-[calc(100%-50px)] overflow-y-auto">
                    <ul class="space-y-1">
                        <li>
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-gray-500 cursor-pointer mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                    <span class="font-medium">分析仪器</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button onclick="editCategory('cat1')" class="text-blue-600 hover:text-blue-800 text-xs">
                                        编辑
                                    </button>
                                    <button onclick="deleteCategory('cat1')" class="text-red-600 hover:text-red-800 text-xs">
                                        删除
                                    </button>
                                </div>
                            </div>
                            <ul class="pl-6 mt-1">
                                <li class="p-2 hover:bg-gray-50 rounded flex justify-between items-center">
                                    <span>光谱分析仪</span>
                                    <div class="flex space-x-1">
                                        <button onclick="editCategory('cat2')" class="text-blue-600 hover:text-blue-800 text-xs">
                                            编辑
                                        </button>
                                        <button onclick="deleteCategory('cat2')" class="text-red-600 hover:text-red-800 text-xs">
                                            删除
                                        </button>
                                    </div>
                                </li>
                                <li class="p-2 hover:bg-gray-50 rounded flex justify-between items-center">
                                    <span>色谱分析仪</span>
                                    <div class="flex space-x-1">
                                        <button onclick="editCategory('cat3')" class="text-blue-600 hover:text-blue-800 text-xs">
                                            编辑
                                        </button>
                                        <button onclick="deleteCategory('cat3')" class="text-red-600 hover:text-red-800 text-xs">
                                            删除
                                        </button>
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100">
                                <div class="flex items-center">
                                    <svg class="w-4 h-4 text-gray-500 cursor-pointer mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                    <span class="font-medium">实验设备</span>
                                </div>
                                <div class="flex space-x-1">
                                    <button onclick="editCategory('cat4')" class="text-blue-600 hover:text-blue-800 text-xs">
                                        编辑
                                    </button>
                                    <button onclick="deleteCategory('cat4')" class="text-red-600 hover:text-red-800 text-xs">
                                        删除
                                    </button>
                                </div>
                            </div>
                            <ul class="pl-6 mt-1 hidden">
                                <li class="p-2 hover:bg-gray-50 rounded">离心机</li>
                                <li class="p-2 hover:bg-gray-50 rounded">恒温箱</li>
                            </ul>
                        </li>
                        <li class="p-2 hover:bg-gray-50 rounded flex justify-between items-center">
                            <span>测量仪器</span>
                            <div class="flex space-x-1">
                                <button onclick="editCategory('cat5')" class="text-blue-600 hover:text-blue-800 text-xs">
                                    编辑
                                </button>
                                <button onclick="deleteCategory('cat5')" class="text-red-600 hover:text-red-800 text-xs">
                                    删除
                                </button>
                            </div>
                        </li>
                        <li class="p-2 hover:bg-gray-50 rounded flex justify-between items-center">
                            <span>宁波市智能制造设备</span>
                            <div class="flex space-x-1">
                                <button onclick="editCategory('cat6')" class="text-blue-600 hover:text-blue-800 text-xs">
                                    编辑
                                </button>
                                <button onclick="deleteCategory('cat6')" class="text-red-600 hover:text-red-800 text-xs">
                                    删除
                                </button>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 右侧详情与操作区 -->
            <div class="flex-1 space-y-6">
                <!-- 分类统计卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        分类统计
                    </h2>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">分类总数</div>
                                <div class="text-2xl font-bold text-blue-600">28</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已启用分类</div>
                                <div class="text-2xl font-bold text-green-600">25</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已停用分类</div>
                                <div class="text-2xl font-bold text-yellow-600">3</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 操作日志区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            操作日志
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作内容</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-20 14:30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张伟</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新增分类</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">新增分类"宁波市智能制造设备"</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-18 09:15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王芳</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">编辑分类</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">修改分类"光谱分析仪"编码为"ANL-SP-001"</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15 16:20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李强</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">停用分类</td>
                                        <td class="px-6 py-4 text-sm text-gray-500">停用分类"老旧实验设备"</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">156</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑分类弹窗 -->
    <div id="categoryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900" id="modalTitle">新增设备分类</h3>
                    <button onclick="closeCategoryModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">分类名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入分类名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">分类编码 <span class="text-red-500">*</span></label>
                                <div class="flex">
                                    <input type="text" placeholder="自动生成" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <button class="px-3 py-2 bg-gray-100 text-gray-700 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        生成
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">父级分类</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">无(一级分类)</option>
                                    <option value="1">分析仪器</option>
                                    <option value="2">实验设备</option>
                                    <option value="3">测量仪器</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">排序号</label>
                                <input type="number" placeholder="请输入排序号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">分类描述</label>
                            <textarea rows="3" placeholder="请输入分类描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <span class="mr-3 text-sm font-medium text-gray-700">生效状态:</span>
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox" id="categoryStatusToggle" class="sr-only">
                                    <div class="block h-6 bg-gray-300 rounded-full w-12"></div>
                                    <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                                </div>
                                <span id="categoryStatusText" class="text-sm text-gray-700">已停用</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeCategoryModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 0v2m0-2h2m-2 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900" id="deleteModalTitle">删除确认</h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500" id="deleteModalContent">确定要删除这个分类吗？此操作不可恢复！</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 bg-gray-50 flex justify-end space-x-3">
                    <button onclick="closeDeleteModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        取消
                    </button>
                    <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分类树操作
        function toggleTreeNode(icon) {
            const parent = icon.closest('li');
            const sublist = parent.querySelector('ul');
            if (sublist) {
                sublist.classList.toggle('hidden');
                icon.classList.toggle('rotate-90');
            }
        }
        
        function expandAll() {
            document.querySelectorAll('.border ul ul').forEach(el => {
                el.classList.remove('hidden');
            });
            document.querySelectorAll('.border svg').forEach(el => {
                el.classList.add('rotate-90');
            });
        }
        
        function collapseAll() {
            document.querySelectorAll('.border ul ul').forEach(el => {
                el.classList.add('hidden');
            });
            document.querySelectorAll('.border svg').forEach(el => {
                el.classList.remove('rotate-90');
            });
        }
        
        // 分类弹窗操作
        function openAddCategoryModal() {
            document.getElementById('modalTitle').textContent = '新增设备分类';
            document.getElementById('categoryModal').classList.remove('hidden');
        }
        
        function editCategory(categoryId) {
            document.getElementById('modalTitle').textContent = '编辑设备分类';
            document.getElementById('categoryModal').classList.remove('hidden');
        }
        
        function closeCategoryModal() {
            document.getElementById('categoryModal').classList.add('hidden');
        }
        
        // 删除确认弹窗
        function deleteCategory(categoryId) {
            document.getElementById('deleteModal').classList.remove('hidden');
        }
        
        function closeDeleteModal() {
            document.getElementById('deleteModal').classList.add('hidden');
        }
        
        function confirmDelete() {
            alert('分类删除成功！');
            closeDeleteModal();
        }
        
        // 状态开关
        document.getElementById('categoryStatusToggle').addEventListener('change', function() {
            const statusText = document.getElementById('categoryStatusText');
            if (this.checked) {
                statusText.textContent = '已启用';
                document.querySelector('#categoryStatusToggle ~ .dot').classList.add('transform', 'translate-x-6');
            } else {
                statusText.textContent = '已停用';
                document.querySelector('#categoryStatusToggle ~ .dot').classList.remove('transform', 'translate-x-6');
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('categoryModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCategoryModal();
            }
        });
        
        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDeleteModal();
            }
        });
        
        // 自定义样式
        document.addEventListener('DOMContentLoaded', function() {
            // 状态开关样式
            const style = document.createElement('style');
            style.textContent = `
                #categoryStatusToggle:checked + .block {
                    background-color: #2563eb;
                }
                #categoryStatusToggle:checked ~ .dot {
                    transform: translateX(100%);
                }
                .rotate-90 {
                    transform: rotate(90deg);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>