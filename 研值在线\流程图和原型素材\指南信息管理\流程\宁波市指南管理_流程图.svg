<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">宁波市指南管理业务流程</text>

  <!-- 阶段一：指南新增与编辑 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：指南新增与编辑</text>
  
  <!-- 节点1: 运维人员新增 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">运维人员新增</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写必填字段并上传附件</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据完整性校验</text>
  </g>

  <!-- 节点3: 生成草稿版本 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成草稿版本</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态置为"待审核"</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与发布 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与发布</text>

  <!-- 节点4: 负责人审核 -->
  <g transform="translate(250, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">负责人审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">核对指南内容与关联信息</text>
  </g>

  <!-- 节点5: 审核通过 -->
  <g transform="translate(550, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核通过</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态更新为"已发布"</text>
  </g>

  <!-- 节点6: 审核驳回 -->
  <g transform="translate(850, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核驳回</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">附加意见返回编辑区</text>
  </g>

  <!-- 连接线 草稿 -> 审核 -->
  <path d="M 800 200 C 800 250, 350 280, 350 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 审核 -> 通过/驳回 -->
  <path d="M 450 345 Q 500 345 550 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 345 C 650 345, 750 345, 850 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 驳回反馈循环 -->
  <path d="M 950 310 C 1100 250, 1100 150, 200 120" stroke="#CE93D8" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="200" text-anchor="middle" font-size="12" fill="#CE93D8">修订反馈</text>

  <!-- 阶段三：批量导入与依赖检查 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：批量导入与依赖检查</text>

  <!-- 节点7: 批量导入 -->
  <g transform="translate(100, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">逐行校验字段合法性</text>
  </g>

  <!-- 节点8: 校验结果 -->
  <g transform="translate(400, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验结果</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成草稿或错误报告</text>
  </g>

  <!-- 节点9: 依赖检查 -->
  <g transform="translate(700, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">依赖检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">检查关联计划或编制任务</text>
  </g>

  <!-- 节点10: 操作执行 -->
  <g transform="translate(1000, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作执行</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">修改/删除并生成快照</text>
  </g>

  <!-- 连接线 批量导入流程 -->
  <path d="M 300 545 Q 350 545 400 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 545 Q 650 545 700 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 545 Q 950 545 1000 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：审计与归档 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审计与归档</text>

  <!-- 节点11: 审计日志 -->
  <g transform="translate(300, 710)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志记录</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">所有操作同步写入审计日志</text>
  </g>

  <!-- 节点12: 定时归档 -->
  <g transform="translate(650, 710)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时归档</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">每日归档日志与历史版本</text>
  </g>

  <!-- 节点13: 数据推送 -->
  <g transform="translate(1000, 710)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据推送</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">向统计分析模块推送数据</text>
  </g>

  <!-- 连接线 审计流程 -->
  <path d="M 550 745 Q 600 745 650 745" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 745 Q 950 745 1000 745" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 垂直连接线：各阶段到审计 -->
  <path d="M 650 380 C 650 600, 425 680, 425 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 580 C 800 650, 425 680, 425 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 标签说明 -->
  <text x="500" y="450" text-anchor="middle" font-size="12" fill="#555">通过</text>
  <text x="750" y="450" text-anchor="middle" font-size="12" fill="#555">驳回</text>

</svg>