'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  CreditCard, Building, User, FileText, DollarSign, Calendar,
  CheckCircle, AlertTriangle, Brain, ArrowRight, Pencil, 
  Eye, XCircle, Save, Calculator, Search, FileCheck
} from "lucide-react"

// 模拟数据
const paymentRequest = {
  id: 'PR2024050001',
  status: 'draft',
  date: '2024-05-15',
  amount: 250000,
  summary: '科技创新项目补助资金',
  
  targetInfo: {
    department: '宁波市科学技术局',
    budgetUnit: '宁波市科技创新中心',
    projectCode: 'KJ2024-XM-001',
    projectName: '科技创新专项资金',
    fundCategory: '科技支出',
    functionalCategory: '科学技术',
    economicCategory: '对企业补助',
    budgetSource: '财政拨款'
  },
  
  payeeInfo: {
    type: 'enterprise',
    code: '91330200MA2H7YU12X',
    name: '宁波智能科技有限公司',
    account: '33050161672700000123',
    bank: '建设银行宁波分行高新区支行'
  },
  
  paymentInfo: {
    paymentMethod: 'direct',
    settlementMethod: 'electronic',
    amount: 250000,
    summary: '科技创新项目补助资金',
    attachments: [
      { name: '拨款申请.pdf', size: '1.2MB' },
      { name: '项目验收报告.pdf', size: '3.5MB' },
      { name: '资金使用计划.xlsx', size: '350KB' }
    ]
  }
};

// 风险检查结果
const riskCheckResults = [
  { 
    type: 'low', 
    message: '企业具有相关科技创新项目经历，符合补助条件',
    detail: '根据企业信用信息查询，该企业近三年有4个科技创新项目，2项发明专利，符合科技创新专项资金补助条件。'
  }
];

export default function PaymentRequestPage() {
  const [activeTab, setActiveTab] = useState('basicInfo')
  const [isAIAnalysisOpen, setIsAIAnalysisOpen] = useState(false)
  
  return (
    <div className="flex-1 p-6 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className={`grid ${isAIAnalysisOpen ? 'grid-cols-3' : 'grid-cols-1'} gap-6 h-[calc(100vh-120px)]`}>
        <div className={isAIAnalysisOpen ? 'col-span-2' : 'col-span-1'}>
          <Card className="h-full shadow-lg border-blue-100 flex flex-col">
            <CardHeader className="border-b border-blue-100 bg-white p-4">
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle className="text-xl text-blue-800 flex items-center">
                    <CreditCard size={22} className="text-blue-500 mr-2" />
                    支付申请
                  </CardTitle>
                  <CardDescription className="text-gray-500 mt-1">
                    创建和管理支付申请单
                  </CardDescription>
                </div>
                
                <div className="flex items-center gap-3">
                  <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">
                    草稿
                  </Badge>
                  <div className="text-sm text-gray-500">
                    单号: {paymentRequest.id}
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="border-blue-200 text-blue-700 flex items-center gap-1"
                    onClick={() => setIsAIAnalysisOpen(!isAIAnalysisOpen)}
                  >
                    <Brain size={16} className="text-blue-500" />
                    AI辅助审核
                  </Button>
                </div>
              </div>
                
              <Tabs 
                defaultValue="basicInfo" 
                value={activeTab}
                onValueChange={setActiveTab}
                className="mt-4 w-full"
              >
                <TabsList className="grid grid-cols-3 w-full">
                  <TabsTrigger value="basicInfo" className="flex items-center">
                    <FileText size={16} className="mr-2" />
                    指标信息
                  </TabsTrigger>
                  <TabsTrigger value="payeeInfo" className="flex items-center">
                    <User size={16} className="mr-2" />
                    收款人信息
                  </TabsTrigger>
                  <TabsTrigger value="paymentInfo" className="flex items-center">
                    <DollarSign size={16} className="mr-2" />
                    支付信息
                  </TabsTrigger>
                </TabsList>
                
                <div className="flex-1 p-0 flex flex-col">
                  <ScrollArea className="flex-1">
                    <div className="p-6">
                      <TabsContent value="basicInfo" className="m-0">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">基本信息</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="department">业务处室</Label>
                                <Select defaultValue={paymentRequest.targetInfo.department}>
                                  <SelectTrigger id="department" className="mt-1">
                                    <SelectValue placeholder="请选择业务处室" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.department}>
                                      {paymentRequest.targetInfo.department}
                                    </SelectItem>
                                    <SelectItem value="宁波市经济和信息化局">宁波市经济和信息化局</SelectItem>
                                    <SelectItem value="宁波市财政局">宁波市财政局</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="budgetUnit">预算单位</Label>
                                <Select defaultValue={paymentRequest.targetInfo.budgetUnit}>
                                  <SelectTrigger id="budgetUnit" className="mt-1">
                                    <SelectValue placeholder="请选择预算单位" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.budgetUnit}>
                                      {paymentRequest.targetInfo.budgetUnit}
                                    </SelectItem>
                                    <SelectItem value="宁波市科技服务中心">宁波市科技服务中心</SelectItem>
                                    <SelectItem value="宁波市工业发展中心">宁波市工业发展中心</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">项目信息</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="projectCode">项目代码</Label>
                                <div className="relative mt-1">
                                  <Input 
                                    id="projectCode" 
                                    defaultValue={paymentRequest.targetInfo.projectCode} 
                                    className="pl-8"
                                  />
                                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                                </div>
                              </div>
                              
                              <div>
                                <Label htmlFor="projectName">项目名称</Label>
                                <Input 
                                  id="projectName" 
                                  defaultValue={paymentRequest.targetInfo.projectName} 
                                  className="mt-1"
                                  readOnly
                                />
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">分类信息</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="fundCategory">支出分类</Label>
                                <Select defaultValue={paymentRequest.targetInfo.fundCategory}>
                                  <SelectTrigger id="fundCategory" className="mt-1">
                                    <SelectValue placeholder="请选择支出分类" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.fundCategory}>
                                      {paymentRequest.targetInfo.fundCategory}
                                    </SelectItem>
                                    <SelectItem value="工业和信息产业支出">工业和信息产业支出</SelectItem>
                                    <SelectItem value="农业农村支出">农业农村支出</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="functionalCategory">功能分类</Label>
                                <Select defaultValue={paymentRequest.targetInfo.functionalCategory}>
                                  <SelectTrigger id="functionalCategory" className="mt-1">
                                    <SelectValue placeholder="请选择功能分类" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.functionalCategory}>
                                      {paymentRequest.targetInfo.functionalCategory}
                                    </SelectItem>
                                    <SelectItem value="社会保障和就业">社会保障和就业</SelectItem>
                                    <SelectItem value="城乡社区支出">城乡社区支出</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="economicCategory">经济分类</Label>
                                <Select defaultValue={paymentRequest.targetInfo.economicCategory}>
                                  <SelectTrigger id="economicCategory" className="mt-1">
                                    <SelectValue placeholder="请选择经济分类" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.economicCategory}>
                                      {paymentRequest.targetInfo.economicCategory}
                                    </SelectItem>
                                    <SelectItem value="基本工资">基本工资</SelectItem>
                                    <SelectItem value="对事业单位补助">对事业单位补助</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="budgetSource">资金来源</Label>
                                <Select defaultValue={paymentRequest.targetInfo.budgetSource}>
                                  <SelectTrigger id="budgetSource" className="mt-1">
                                    <SelectValue placeholder="请选择资金来源" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value={paymentRequest.targetInfo.budgetSource}>
                                      {paymentRequest.targetInfo.budgetSource}
                                    </SelectItem>
                                    <SelectItem value="政府性基金">政府性基金</SelectItem>
                                    <SelectItem value="国有资本经营预算">国有资本经营预算</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="payeeInfo" className="m-0">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">收款人类型</h3>
                            <RadioGroup defaultValue={paymentRequest.payeeInfo.type} className="flex gap-4">
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="enterprise" id="enterprise" />
                                <Label htmlFor="enterprise">企业</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="individual" id="individual" />
                                <Label htmlFor="individual">个人</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <RadioGroupItem value="institution" id="institution" />
                                <Label htmlFor="institution">事业单位</Label>
                              </div>
                            </RadioGroup>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">基本信息</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="payeeCode">统一社会信用代码</Label>
                                <div className="relative mt-1">
                                  <Input 
                                    id="payeeCode" 
                                    defaultValue={paymentRequest.payeeInfo.code} 
                                    className="pl-8"
                                  />
                                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                                </div>
                              </div>
                              
                              <div>
                                <Label htmlFor="payeeName">收款人名称</Label>
                                <Input 
                                  id="payeeName" 
                                  defaultValue={paymentRequest.payeeInfo.name} 
                                  className="mt-1"
                                />
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">账户信息</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="payeeAccount">账号</Label>
                                <Input 
                                  id="payeeAccount" 
                                  defaultValue={paymentRequest.payeeInfo.account} 
                                  className="mt-1"
                                />
                              </div>
                              
                              <div>
                                <Label htmlFor="payeeBank">开户行</Label>
                                <Input 
                                  id="payeeBank" 
                                  defaultValue={paymentRequest.payeeInfo.bank} 
                                  className="mt-1"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                      
                      <TabsContent value="paymentInfo" className="m-0">
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">支付方式</h3>
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="paymentMethod">支付方式</Label>
                                <Select defaultValue={paymentRequest.paymentInfo.paymentMethod}>
                                  <SelectTrigger id="paymentMethod" className="mt-1">
                                    <SelectValue placeholder="请选择支付方式" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="direct">直接支付</SelectItem>
                                    <SelectItem value="centralized">集中支付</SelectItem>
                                    <SelectItem value="reimbursement">报销支付</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              
                              <div>
                                <Label htmlFor="settlementMethod">结算方式</Label>
                                <Select defaultValue={paymentRequest.paymentInfo.settlementMethod}>
                                  <SelectTrigger id="settlementMethod" className="mt-1">
                                    <SelectValue placeholder="请选择结算方式" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="electronic">电子支付</SelectItem>
                                    <SelectItem value="check">支票</SelectItem>
                                    <SelectItem value="cash">现金</SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">支付金额</h3>
                            <div className="grid grid-cols-1 gap-4">
                              <div>
                                <Label htmlFor="paymentAmount">支付金额（元）</Label>
                                <div className="relative mt-1">
                                  <Input 
                                    id="paymentAmount" 
                                    type="number"
                                    defaultValue={paymentRequest.paymentInfo.amount} 
                                    className="pl-8"
                                  />
                                  <Calculator className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                                </div>
                              </div>
                              
                              <div>
                                <Label htmlFor="paymentSummary">摘要</Label>
                                <Textarea 
                                  id="paymentSummary" 
                                  defaultValue={paymentRequest.paymentInfo.summary} 
                                  className="mt-1"
                                  rows={3}
                                />
                              </div>
                            </div>
                          </div>
                          
                          <Separator />
                          
                          <div>
                            <h3 className="text-md font-medium text-blue-800 mb-3">附件资料</h3>
                            <div className="space-y-2">
                              {paymentRequest.paymentInfo.attachments.map((attachment, index) => (
                                <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border border-gray-200">
                                  <div className="flex items-center">
                                    <FileText size={16} className="mr-2 text-blue-500" />
                                    <span>{attachment.name}</span>
                                    <span className="ml-2 text-xs text-gray-500">{attachment.size}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <Button variant="ghost" size="sm" className="text-blue-700">
                                      <Eye size={14} className="mr-1" /> 查看
                                    </Button>
                                    <Button variant="ghost" size="sm" className="text-red-700">
                                      <XCircle size={14} className="mr-1" /> 删除
                                    </Button>
                                  </div>
                                </div>
                              ))}
                              
                              <Button variant="outline" size="sm" className="mt-2 w-full border-dashed border-blue-200 text-blue-700">
                                + 上传附件
                              </Button>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </div>
                  </ScrollArea>
                </div>
              </Tabs>
              
              <div className="p-4 border-t border-blue-100 bg-gray-50 flex justify-between">
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-gray-700">
                    保存草稿
                  </Button>
                  <Button variant="outline" size="sm" className="text-gray-700">
                    取消
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" className="text-blue-700 border-blue-200">
                    预览
                  </Button>
                  <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                    提交
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>
        
        {isAIAnalysisOpen && (
          <div className="col-span-1">
            <Card className="h-full shadow-lg border-blue-100 flex flex-col">
              <CardHeader className="border-b border-blue-100 bg-white p-4">
                <CardTitle className="text-lg text-blue-800 flex items-center">
                  <Brain size={20} className="text-blue-500 mr-2" />
                  AI辅助审核
                </CardTitle>
                <CardDescription className="text-gray-500">
                  智能分析支付申请合规性和风险情况
                </CardDescription>
              </CardHeader>
              
              <CardContent className="p-4 flex-1 flex flex-col">
                <ScrollArea className="flex-1">
                  <div className="space-y-4">
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                      <h3 className="text-sm font-medium text-blue-800 mb-2 flex items-center">
                        <FileCheck size={16} className="mr-2 text-blue-500" /> 
                        申请单信息分析
                      </h3>
                      <div className="text-gray-700 text-sm">
                        <p>
                          当前申请单用于拨付科技创新专项资金，支付金额为250,000元。
                          收款方为宁波智能科技有限公司，属于对企业补助类支出。
                        </p>
                        <div className="bg-white p-2 rounded-md mt-2 text-xs border border-blue-100">
                          <div className="font-medium text-blue-800 mb-1">资金用途核查</div>
                          <p>该笔资金用于科技创新项目补助，符合科技支出的预算分类和用途规定。</p>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-blue-800 mb-2">风险检查结果</h3>
                      <div className="space-y-2">
                        {riskCheckResults.map((risk, index) => (
                          <div 
                            key={index} 
                            className={`p-3 rounded-lg border ${
                              risk.type === 'low' ? 'bg-green-50 border-green-100' : 
                              risk.type === 'medium' ? 'bg-yellow-50 border-yellow-100' : 
                              'bg-red-50 border-red-100'
                            }`}
                          >
                            <div className="flex items-start">
                              {risk.type === 'low' ? (
                                <CheckCircle size={16} className="mr-2 text-green-500 mt-0.5" />
                              ) : risk.type === 'medium' ? (
                                <AlertTriangle size={16} className="mr-2 text-yellow-500 mt-0.5" />
                              ) : (
                                <XCircle size={16} className="mr-2 text-red-500 mt-0.5" />
                              )}
                              <div>
                                <div className={`text-sm font-medium ${
                                  risk.type === 'low' ? 'text-green-700' : 
                                  risk.type === 'medium' ? 'text-yellow-700' : 
                                  'text-red-700'
                                }`}>
                                  {risk.message}
                                </div>
                                <div className="text-xs text-gray-700 mt-1">
                                  {risk.detail}
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-blue-800 mb-2">AI审核建议</h3>
                      <div className="bg-white p-4 rounded-lg border border-blue-100">
                        <div className="flex items-start mb-3">
                          <Brain size={18} className="mr-2 text-blue-500 mt-0.5" />
                          <div className="text-sm text-gray-700">
                            根据系统分析，该支付申请基本符合规定要求，但存在一项中等风险点需要注意。建议在流程审批中关注以下方面：
                          </div>
                        </div>
                        <div className="space-y-2 text-sm text-gray-700 pl-7">
                          <div className="flex items-start">
                            <ArrowRight size={14} className="mr-2 text-blue-500 mt-1" />
                            <span>请补充提供研发投入明细数据，确认是否符合最低研发投入比例要求</span>
                          </div>
                          <div className="flex items-start">
                            <ArrowRight size={14} className="mr-2 text-blue-500 mt-1" />
                            <span>验证企业资质是否仍在有效期内，建议查询最新的企业信用信息</span>
                          </div>
                          <div className="flex items-start">
                            <ArrowRight size={14} className="mr-2 text-blue-500 mt-1" />
                            <span>建议附加项目成果证明材料以增强申请完整性</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-blue-800 mb-2">政策依据</h3>
                      <div className="bg-white p-3 rounded-lg border border-blue-100">
                        <div className="text-xs text-gray-700">
                          <div className="font-medium text-blue-800 mb-1">《宁波市科技企业研发补助实施细则》第十二条</div>
                          <p>科技型企业年度研发投入占营业收入比例应不低于8%，特殊情况下可放宽至7%，但需提供额外的创新成果证明。</p>
                        </div>
                        <Separator className="my-2" />
                        <div className="text-xs text-gray-700">
                          <div className="font-medium text-blue-800 mb-1">《财政专项资金管理办法》第八条</div>
                          <p>专项资金支付应当以银行转账方式支付至收款单位基本账户，确保资金使用安全合规。</p>
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h3 className="text-sm font-medium text-blue-800 mb-2">监控规则检查</h3>
                      <div className="bg-white p-4 rounded-lg border border-blue-100">
                        <div className="space-y-3">
                          <div className="p-3 rounded-lg border bg-yellow-50 border-yellow-100">
                            <div className="flex items-start">
                              <AlertTriangle size={16} className="mr-2 text-yellow-500 mt-0.5" />
                              <div>
                                <div className="text-sm font-medium text-yellow-700">
                                  检测到敏感关键词："活动中心"
                                </div>
                                <div className="text-xs text-gray-700 mt-1">
                                  支付摘要包含"活动中心"，根据监控规则可能涉及楼堂馆所支出。
                                </div>
                                
                                <div className="mt-2 text-xs">
                                  <div className="font-medium text-gray-700">白名单规则检查：</div>
                                  <div className="text-green-600 flex items-center mt-1">
                                    <CheckCircle size={14} className="mr-1" />
                                    符合白名单规则：现代职业教育质量提升计划资金支出可包含"活动中心"
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                          
                          <div className="text-xs text-gray-700">
                            <div className="font-medium text-blue-800 mb-1">适用的监控规则：</div>
                            <ul className="list-disc pl-5 space-y-1">
                              <li>资金支出用途含"办公楼"、"会议楼"、"礼堂"等字样的预警</li>
                              <li>转移支付资金用于楼堂馆所建设的预警</li>
                            </ul>
                          </div>
                          
                          <div className="text-xs text-gray-700">
                            <div className="font-medium text-blue-800 mb-1">适用的白名单规则：</div>
                            <ul className="list-disc pl-5 space-y-1">
                              <li>中央支持地方公共文化体系建设补助资金，支出用途中含"纪念馆"字样的，不预警</li>
                              <li>现代职业教育质量提升计划资金支出用途中含"办公楼"、"会议楼"、"大礼堂"、"活动中心"、"会议中心"字样的，不预警</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ScrollArea>
                
                <div className="flex justify-between gap-2 mt-4 pt-4 border-t border-blue-100">
                  <Button variant="outline" size="sm" className="border-blue-200 text-blue-700">
                    查看规则详情
                  </Button>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="border-blue-200 text-blue-700">
                      导出分析报告
                    </Button>
                    <Button size="sm" className="bg-blue-500 hover:bg-blue-600 text-white">
                      确认并继续
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}