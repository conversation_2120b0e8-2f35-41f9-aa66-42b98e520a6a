'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false })

export function AssetHealth() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100
    },
    yAxis: {
      type: 'category',
      data: ['服务器', '存储设备', '网络设备', '安全设备', '其他设备']
    },
    series: [
      {
        name: '健康度',
        type: 'bar',
        data: [
          {
            value: 98.5,
            itemStyle: { color: '#2563EB' }
          },
          {
            value: 97.8,
            itemStyle: { color: '#10B981' }
          },
          {
            value: 99.2,
            itemStyle: { color: '#F59E0B' }
          },
          {
            value: 99.5,
            itemStyle: { color: '#8B5CF6' }
          },
          {
            value: 96.8,
            itemStyle: { color: '#EC4899' }
          }
        ],
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  }

  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="border-b border-[#E5E9EF]">
        <CardTitle className="text-lg font-medium">资产健康度</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px]">
          {mounted && (
            <ReactECharts option={option} style={{ height: '100%' }} />
          )}
        </div>
      </CardContent>
    </Card>
  )
} 