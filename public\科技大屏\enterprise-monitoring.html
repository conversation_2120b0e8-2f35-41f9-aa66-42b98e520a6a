<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重点企业监测 - 科技创新监测平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white border-b border-gray-200 card-shadow sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-building text-green-500 mr-3"></i>
                        重点企业监测
                    </h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-calendar-alt"></i>
                        <span>数据时间：2025年1~6月</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出企业清单
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="gradient-bg min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            
            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">企业总数</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">1,286</p>
                            <p class="text-xs text-green-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+8.5%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-blue-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">研发费用500强</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">128</p>
                            <p class="text-xs text-blue-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+3.2%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">"研值"前100</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">86</p>
                            <p class="text-xs text-purple-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+12.4%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-trophy text-purple-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">异常企业</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">3</p>
                            <p class="text-xs text-red-600 mt-1">
                                <i class="fas fa-exclamation-triangle mr-1"></i>需关注
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选和分析区域 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">企业筛选与分析</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-sm">列表视图</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">图表视图</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-6">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">企业类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部类型</option>
                            <option value="hitech">高新技术企业</option>
                            <option value="sme">科技型中小企业</option>
                            <option value="unicorn">瞪羚之星企业</option>
                            <option value="leader">科技领军企业</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">"510"领域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部领域</option>
                            <option value="smart">智能制造</option>
                            <option value="bio">生物医药</option>
                            <option value="material">新材料</option>
                            <option value="energy">新能源</option>
                            <option value="digital">数字经济</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">区域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部区域</option>
                            <option value="haishu">海曙区</option>
                            <option value="jiangbei">江北区</option>
                            <option value="beilun">北仑区</option>
                            <option value="yinzhou">鄞州区</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">研发强度</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部</option>
                            <option value="high">高强度(>5%)</option>
                            <option value="medium">中等(2-5%)</option>
                            <option value="low">较低(<2%)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- "510"领域布局分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"510"领域企业分布</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">智能制造</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">342家</span>
                                <span class="text-xs text-gray-500 block">26.6%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">生物医药</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">186家</span>
                                <span class="text-xs text-gray-500 block">14.5%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">新材料</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">298家</span>
                                <span class="text-xs text-gray-500 block">23.2%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">新能源</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">234家</span>
                                <span class="text-xs text-gray-500 block">18.2%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-3 bg-teal-50 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-teal-500 rounded-full mr-3"></div>
                                <span class="text-sm text-gray-700">数字经济</span>
                            </div>
                            <div class="text-right">
                                <span class="text-sm font-semibold text-gray-900">226家</span>
                                <span class="text-xs text-gray-500 block">17.5%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 企业成长性分析 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">企业成长性分析</h3>
                    <div class="h-64 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg flex items-center justify-center">
                        <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=entropy" 
                             alt="企业成长分析图表" 
                             class="w-full h-full object-cover rounded-lg opacity-80">
                        <div class="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                            <div class="text-center text-blue-800">
                                <i class="fas fa-chart-area text-3xl mb-2"></i>
                                <p class="font-semibold">企业成长性趋势图</p>
                                <p class="text-sm mt-1">研发投入与营收增长关系</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 重点企业清单 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">重点企业清单</h3>
                    <div class="flex items-center space-x-2">
                        <input type="text" placeholder="搜索企业名称..." 
                               class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm">
                        <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="bg-gray-50 border-b border-gray-200">
                                <th class="px-4 py-3 text-left font-medium text-gray-700">企业名称</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">类型</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">"510"领域</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">研发费用(万元)</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">研发强度</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">状态</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-building text-blue-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">宁波智能制造有限公司</p>
                                            <p class="text-xs text-gray-500">统一社会信用代码: 91330200***</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">高新技术企业</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">智能制造</span>
                                </td>
                                <td class="px-4 py-3 font-medium text-gray-900">12,680</td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">5.8%</span>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 58%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">正常</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-building text-purple-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">江北生物医药研发中心</p>
                                            <p class="text-xs text-gray-500">统一社会信用代码: 91330200***</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">科技领军企业</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs">生物医药</span>
                                </td>
                                <td class="px-4 py-3 font-medium text-gray-900">8,940</td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">7.2%</span>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 72%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">正常</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-building text-orange-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">鄞州新材料科技公司</p>
                                            <p class="text-xs text-gray-500">统一社会信用代码: 91330200***</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">瞪羚之星</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">新材料</span>
                                </td>
                                <td class="px-4 py-3 font-medium text-gray-900">3,250</td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">4.1%</span>
                                        <div class="w-16 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 41%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">关注</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-500">
                        显示 1-10 条，共 1,286 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-500">上一页</button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <span class="px-2 text-gray-400">...</span>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">129</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技创新监测平台. 保留所有权利.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>数据更新频率：实时</span>
                    <span>|</span>
                    <span>监测企业总数：1,286家</span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html> 