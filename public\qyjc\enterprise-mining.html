<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业智能挖掘 - 企业检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        'blue-50': '#EFF6FF',
                        'blue-100': '#DBEAFE',
                        'blue-500': '#3B82F6',
                        'blue-600': '#2563EB'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F1F5F9 0%, #EFF6FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
        }
        .filter-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .filter-card:hover {
            border-color: #3B82F6;
            transform: translateY(-2px);
        }
        .filter-active {
            border-color: #3B82F6;
            background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
        }
        .result-item {
            transition: all 0.3s ease;
        }
        .result-item:hover {
            transform: translateX(4px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.1);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-blue-100 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg transition-colors">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-search-plus text-green-600"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">企业智能挖掘</h1>
                        <p class="text-sm text-gray-600">多维条件筛选工具，用于企业潜力发掘与对比分析</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存筛选模板
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 左侧筛选面板 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 快速模板 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">快速筛选模板</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">
                            <i class="fas fa-plus mr-1"></i>
                            新建模板
                        </button>
                    </div>
                    <div class="space-y-3">
                        <button class="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">潜在种子企业</p>
                                    <p class="text-sm text-gray-600">注册2年内 + 专利≥5项 + 博士≥5人</p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                        <button class="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">高成长企业</p>
                                    <p class="text-sm text-gray-600">研发增速≥30% + 营收增速≥20%</p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                        <button class="w-full p-3 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">产学研活跃企业</p>
                                    <p class="text-sm text-gray-600">高校合作≥3个 + 在研项目≥5个</p>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">筛选条件</h3>
                        <button class="text-red-600 hover:text-red-800 text-sm" onclick="clearAllFilters()">
                            <i class="fas fa-trash mr-1"></i>
                            清空条件
                        </button>
                    </div>

                    <!-- 企业类型 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">企业类型</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">科技型中小企业</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">高新技术企业</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">单项冠军</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">专精特新</span>
                            </label>
                        </div>
                    </div>

                    <!-- 成立年限 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">成立年限</label>
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">起始年份</label>
                                <input type="number" placeholder="2020" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">结束年份</label>
                                <input type="number" placeholder="2024" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    </div>

                    <!-- 技术领域 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">技术领域（510分类）</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择技术领域</option>
                            <option value="ai">人工智能</option>
                            <option value="iot">物联网</option>
                            <option value="bigdata">大数据</option>
                            <option value="blockchain">区块链</option>
                            <option value="5g">5G通信</option>
                            <option value="newmaterial">新材料</option>
                        </select>
                    </div>

                    <!-- 产业领域 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">产业领域（361分类）</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择产业领域</option>
                            <option value="manufacturing">智能制造</option>
                            <option value="biomedicine">生物医药</option>
                            <option value="newenergy">新能源</option>
                            <option value="digitaleconomy">数字经济</option>
                            <option value="marine">海洋科技</option>
                        </select>
                    </div>

                    <!-- 所在区域 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">所在区域</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">鄞州区</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">海曙区</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">江北区</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">北仑区</span>
                            </label>
                        </div>
                    </div>

                    <!-- 人才维度 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">人才维度</label>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">博士及以上人员</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">=</option>
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                    </select>
                                    <input type="number" placeholder="10" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="text-sm text-gray-600">人</span>
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">硕士及以上人员</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">=</option>
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                    </select>
                                    <input type="number" placeholder="50" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <span class="text-sm text-gray-600">人</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业指标 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">企业指标</label>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">研发费用（万元）</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                        <option value="">=</option>
                                    </select>
                                    <input type="number" placeholder="1000" class="w-24 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">研发强度（%）</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                        <option value="">=</option>
                                    </select>
                                    <input type="number" placeholder="10" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">研发增速（%）</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                        <option value="">=</option>
                                    </select>
                                    <input type="number" placeholder="10" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产出成果 -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-3">产出成果</label>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">发明专利数量</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                        <option value="">=</option>
                                    </select>
                                    <input type="number" placeholder="5" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">论文发表数量</label>
                                <div class="flex items-center space-x-2">
                                    <select class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">≥</option>
                                        <option value="">≤</option>
                                        <option value="">=</option>
                                    </select>
                                    <input type="number" placeholder="10" class="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="flex space-x-3">
                        <button onclick="performSearch()" class="flex-1 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
                            <i class="fas fa-search mr-2"></i>
                            开始筛选
                        </button>
                        <button onclick="resetFilters()" class="px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧结果展示 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 筛选结果统计 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">筛选结果</h3>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600">找到 <span class="font-bold text-blue-600">32</span> 家企业</span>
                            <button class="flex items-center px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                                <i class="fas fa-download mr-2"></i>
                                导出结果
                            </button>
                        </div>
                    </div>
                    
                    <!-- 结果统计图表 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-blue-50 rounded-lg p-4 text-center">
                            <p class="text-2xl font-bold text-blue-600">18</p>
                            <p class="text-sm text-gray-600">种子企业潜力</p>
                        </div>
                        <div class="bg-green-50 rounded-lg p-4 text-center">
                            <p class="text-2xl font-bold text-green-600">8</p>
                            <p class="text-sm text-gray-600">高成长企业</p>
                        </div>
                        <div class="bg-purple-50 rounded-lg p-4 text-center">
                            <p class="text-2xl font-bold text-purple-600">6</p>
                            <p class="text-sm text-gray-600">待挖掘企业</p>
                        </div>
                    </div>

                    <!-- 已选择的筛选条件 -->
                    <div class="mb-4">
                        <p class="text-sm font-medium text-gray-700 mb-2">已选择条件：</p>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm flex items-center">
                                成立时间：2020-2024
                                <button class="ml-2 text-blue-500 hover:text-blue-700">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </span>
                            <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm flex items-center">
                                博士人员≥10人
                                <button class="ml-2 text-green-500 hover:text-green-700">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </span>
                            <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm flex items-center">
                                发明专利≥5项
                                <button class="ml-2 text-purple-500 hover:text-purple-700">
                                    <i class="fas fa-times text-xs"></i>
                                </button>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 企业结果列表 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-gray-900">企业列表</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-gray-600">排序：</span>
                            <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
                                <option>综合评分</option>
                                <option>研发投入</option>
                                <option>成长性</option>
                                <option>创新能力</option>
                            </select>
                        </div>
                    </div>

                    <div class="space-y-4" id="search-results">
                        <!-- 结果企业1 -->
                        <div class="result-item border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-building text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">宁波新兴智能科技有限公司</h4>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                            <span>人工智能</span>
                                            <span>•</span>
                                            <span>2022年成立</span>
                                            <span>•</span>
                                            <span>鄞州区</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-8">
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-blue-600">15人</p>
                                        <p class="text-xs text-gray-600">博士</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-green-600">2,800万</p>
                                        <p class="text-xs text-gray-600">研发投入</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-purple-600">23项</p>
                                        <p class="text-xs text-gray-600">发明专利</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-bold text-green-600">92</span>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">匹配度</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">种子潜力</span>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">高成长</span>
                                        <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded text-xs">产学研活跃</span>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">
                                        查看详情 <i class="fas fa-arrow-right ml-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 结果企业2 -->
                        <div class="result-item border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-flask text-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">海曙生物医药创新研究院</h4>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                            <span>生物医药</span>
                                            <span>•</span>
                                            <span>2021年成立</span>
                                            <span>•</span>
                                            <span>海曙区</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-8">
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-blue-600">12人</p>
                                        <p class="text-xs text-gray-600">博士</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-green-600">1,950万</p>
                                        <p class="text-xs text-gray-600">研发投入</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-purple-600">18项</p>
                                        <p class="text-xs text-gray-600">发明专利</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-bold text-blue-600">87</span>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">匹配度</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">种子潜力</span>
                                        <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded text-xs">技术创新</span>
                                        <span class="px-2 py-1 bg-red-100 text-red-700 rounded text-xs">人才集聚</span>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">
                                        查看详情 <i class="fas fa-arrow-right ml-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 结果企业3 -->
                        <div class="result-item border border-gray-200 rounded-lg p-4 hover:border-blue-300 cursor-pointer">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center">
                                        <i class="fas fa-microchip text-purple-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900">江北新材料科技发展有限公司</h4>
                                        <div class="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                                            <span>新材料</span>
                                            <span>•</span>
                                            <span>2020年成立</span>
                                            <span>•</span>
                                            <span>江北区</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-8">
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-blue-600">18人</p>
                                        <p class="text-xs text-gray-600">博士</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-green-600">3,200万</p>
                                        <p class="text-xs text-gray-600">研发投入</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-sm font-bold text-purple-600">29项</p>
                                        <p class="text-xs text-gray-600">发明专利</p>
                                    </div>
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-bold text-purple-600">94</span>
                                        </div>
                                        <p class="text-xs text-gray-600 mt-1">匹配度</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 pt-3 border-t border-gray-100">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <span class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs">种子潜力</span>
                                        <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs">高成长</span>
                                        <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded text-xs">重点关注</span>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">
                                        查看详情 <i class="fas fa-arrow-right ml-1"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-100">
                        <div class="text-sm text-gray-600">
                            显示第 1-3 条，共 32 条记录
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="p-2 text-gray-400 hover:text-gray-600 rounded border border-gray-300 hover:border-gray-400 transition-colors">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                            <button class="px-3 py-2 bg-blue-500 text-white rounded">1</button>
                            <button class="px-3 py-2 text-gray-600 hover:text-gray-900 rounded">2</button>
                            <button class="px-3 py-2 text-gray-600 hover:text-gray-900 rounded">3</button>
                            <button class="p-2 text-gray-400 hover:text-gray-600 rounded border border-gray-300 hover:border-gray-400 transition-colors">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- AI推荐 -->
                <div class="bg-gradient-to-br from-purple-50 to-blue-50 rounded-xl card-shadow p-6 border border-purple-100">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-brain text-purple-600"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-purple-900">AI智能推荐</h3>
                            <p class="text-sm text-purple-700">基于筛选结果的智能分析建议</p>
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-purple-900">重点关注建议</span>
                                <span class="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded-full">高价值</span>
                            </div>
                            <p class="text-xs text-purple-800 leading-relaxed mb-3">
                                建议重点关注宁波新兴智能科技等18家种子潜力企业，这些企业在人才、技术、成长性方面表现突出。
                            </p>
                            <button class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                查看详细分析 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                        
                        <div class="bg-white/60 backdrop-blur-sm rounded-lg p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium text-purple-900">策略优化建议</span>
                                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">优化</span>
                            </div>
                            <p class="text-xs text-purple-800 leading-relaxed">
                                可考虑适当降低博士人员要求（≥8人），增加硕士人员条件，将发现更多潜力企业。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 筛选功能
        function performSearch() {
            console.log('执行企业筛选...');
            // 这里实际应该调用API进行筛选
            
            // 模拟加载效果
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>筛选中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                // 更新结果显示
                updateSearchResults();
            }, 2000);
        }

        function updateSearchResults() {
            // 更新结果统计
            console.log('更新搜索结果显示');
        }

        function clearAllFilters() {
            // 清空所有筛选条件
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            
            const inputs = document.querySelectorAll('input[type="number"]');
            inputs.forEach(input => input.value = '');
            
            const selects = document.querySelectorAll('select');
            selects.forEach(select => select.selectedIndex = 0);
            
            console.log('已清空所有筛选条件');
        }

        function resetFilters() {
            clearAllFilters();
        }

        // 模板功能
        document.addEventListener('DOMContentLoaded', function() {
            const templateButtons = document.querySelectorAll('button[onclick*="template"]');
            
            // 为模板按钮添加点击效果
            const quickTemplates = document.querySelectorAll('.space-y-3 > button');
            quickTemplates.forEach(btn => {
                btn.addEventListener('click', function() {
                    // 模拟应用模板
                    console.log('应用快速模板');
                    
                    // 添加视觉反馈
                    this.classList.add('bg-blue-100', 'border-blue-300');
                    setTimeout(() => {
                        this.classList.remove('bg-blue-100', 'border-blue-300');
                    }, 500);
                });
            });
        });
    </script>
</body>
</html> 