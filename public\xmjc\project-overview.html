<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目总览 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .tab-active {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            color: white;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">项目总览</h1>
                        <p class="text-sm text-gray-600">Project Overview</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出数据
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 顶部功能区 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <!-- 项目分类Tab -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">项目分类</h3>
                <div class="flex space-x-4 mb-4">
                    <button id="tab-tech" class="tab-active px-4 py-2 rounded-lg font-medium transition-colors">
                        科技计划项目
                    </button>
                    <button id="tab-research" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        自研项目
                    </button>
                    <button id="tab-investment" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        高新投资项目
                    </button>
                </div>
                
                <!-- 项目类别下拉 -->
                <div id="project-category" class="mb-4">
                    <label class="text-sm font-medium text-gray-700 mb-2 block">项目类别</label>
                    <select class="bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>国家重点研发计划</option>
                        <option>国家自然科学基金</option>
                        <option>省级科技计划</option>
                        <option>市级科技计划</option>
                    </select>
                </div>
            </div>

            <!-- 立项时间分类Tab -->
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-4">立项时间</h3>
                <div class="flex space-x-4">
                    <button id="time-year" class="tab-active px-4 py-2 rounded-lg font-medium transition-colors">
                        按立项年份
                    </button>
                    <button id="time-total" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        累计汇总
                    </button>
                </div>
            </div>
        </div>

        <!-- 内容展示区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 领域分布 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-industry text-blue-500 mr-2"></i>
                        领域分布
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-xs bg-blue-500 text-white px-3 py-1 rounded-lg">柱状图</button>
                        <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">饼图</button>
                    </div>
                </div>
                
                <!-- 图表显示区 -->
                <div class="h-64 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-column text-3xl mb-2"></i>
                        <p class="text-sm">361产业领域 & 510技术领域</p>
                        <p class="text-xs">项目数量与资金额分布</p>
                    </div>
                </div>

                <!-- 统计数据 -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <p class="text-sm text-gray-600">新一代信息技术</p>
                        <p class="text-lg font-bold text-blue-600">342项</p>
                        <p class="text-xs text-gray-500">34.5亿元</p>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <p class="text-sm text-gray-600">高端装备制造</p>
                        <p class="text-lg font-bold text-green-600">268项</p>
                        <p class="text-xs text-gray-500">28.9亿元</p>
                    </div>
                </div>
            </div>

            <!-- 承担主体 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-building text-green-500 mr-2"></i>
                        承担主体
                    </h3>
                    <button class="text-sm text-blue-500 hover:text-blue-700">
                        查看TOP10 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <!-- 主体类型占比 -->
                <div class="space-y-4 mb-6">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">企业</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">1,642项</span>
                            <div class="w-24 h-2 bg-gray-200 rounded-full">
                                <div class="w-20 h-2 bg-green-500 rounded-full"></div>
                            </div>
                            <span class="text-xs text-gray-500">58%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">高校</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">756项</span>
                            <div class="w-24 h-2 bg-gray-200 rounded-full">
                                <div class="w-12 h-2 bg-blue-500 rounded-full"></div>
                            </div>
                            <span class="text-xs text-gray-500">27%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">科研院所</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">449项</span>
                            <div class="w-24 h-2 bg-gray-200 rounded-full">
                                <div class="w-8 h-2 bg-purple-500 rounded-full"></div>
                            </div>
                            <span class="text-xs text-gray-500">15%</span>
                        </div>
                    </div>
                </div>

                <!-- 项目金额TOP3 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-700 mb-3">项目金额TOP3</p>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>宁波大学</span>
                            <span class="font-medium">8.6亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>中科院宁波材料所</span>
                            <span class="font-medium">6.2亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>宁波均胜电子</span>
                            <span class="font-medium">4.8亿元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目经费 & 区域分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 项目经费 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-dollar-sign text-yellow-500 mr-2"></i>
                        项目经费
                    </h3>
                    <button class="text-sm text-blue-500 hover:text-blue-700">
                        查看经费前10 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>

                <!-- 经费统计 -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center p-4 bg-yellow-50 rounded-lg">
                        <p class="text-sm text-gray-600">总经费</p>
                        <p class="text-2xl font-bold text-yellow-600">156.8亿</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +18.9%
                        </p>
                    </div>
                    <div class="text-center p-4 bg-blue-50 rounded-lg">
                        <p class="text-sm text-gray-600">财政经费</p>
                        <p class="text-2xl font-bold text-blue-600">89.2亿</p>
                        <p class="text-xs text-green-600">
                            <i class="fas fa-arrow-up mr-1"></i>
                            +22.5%
                        </p>
                    </div>
                </div>

                <!-- 拨动经费前5项目 -->
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-sm font-medium text-gray-700 mb-3">拨动经费前5项目</p>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span>新材料产业化示范项目</span>
                            <span class="font-medium">2.8亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>智能制造关键技术研发</span>
                            <span class="font-medium">2.1亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span>海洋工程装备创新平台</span>
                            <span class="font-medium">1.9亿元</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 区域分布 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-map text-cyan-500 mr-2"></i>
                        区域分布
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-xs bg-cyan-500 text-white px-3 py-1 rounded-lg">地图</button>
                        <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">图表</button>
                    </div>
                </div>

                <!-- 地图显示区 -->
                <div class="h-40 bg-gradient-to-r from-cyan-50 to-cyan-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-map text-3xl mb-2"></i>
                        <p class="text-sm">宁波市区县分布图</p>
                        <p class="text-xs">项目数量与资金额</p>
                    </div>
                </div>

                <!-- 区县排名 -->
                <div class="space-y-2">
                    <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                        <span class="text-sm font-medium">海曙区</span>
                        <div class="text-right">
                            <span class="text-sm font-bold text-blue-600">486项</span>
                            <span class="text-xs text-gray-500 block">34.2亿元</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                        <span class="text-sm font-medium">江北区</span>
                        <div class="text-right">
                            <span class="text-sm font-bold text-green-600">423项</span>
                            <span class="text-xs text-gray-500 block">28.9亿元</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                        <span class="text-sm font-medium">镇海区</span>
                        <div class="text-right">
                            <span class="text-sm font-bold text-purple-600">378项</span>
                            <span class="text-xs text-gray-500 block">25.6亿元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目阶段 & 层级分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 项目阶段 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-tasks text-indigo-500 mr-2"></i>
                    项目阶段
                </h3>

                <!-- 阶段分布图 -->
                <div class="h-48 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg chart-placeholder flex items-center justify-center mb-6">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-pie text-3xl mb-2"></i>
                        <p class="text-sm">项目生命周期分布</p>
                        <p class="text-xs">立项、中期、结题阶段</p>
                    </div>
                </div>

                <!-- 阶段统计 -->
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">立项阶段</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold">1,263项</span>
                            <span class="text-xs text-gray-500 block">44.4%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">中期检查</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold">956项</span>
                            <span class="text-xs text-gray-500 block">33.6%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">结题验收</span>
                        </div>
                        <div class="text-right">
                            <span class="text-sm font-bold">628项</span>
                            <span class="text-xs text-gray-500 block">22.0%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 层级分布 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-layer-group text-orange-500 mr-2"></i>
                        层级分布
                    </h3>
                    <button class="text-sm text-blue-500 hover:text-blue-700">
                        查看详细清单 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>

                <!-- 层级统计卡片 -->
                <div class="space-y-4">
                    <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-red-800">国家级项目</p>
                                <p class="text-xs text-red-600">National Level</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-red-700">346项</p>
                                <p class="text-xs text-red-600">45.8亿元</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-yellow-50 p-4 rounded-lg border-l-4 border-yellow-500">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-yellow-800">省级项目</p>
                                <p class="text-xs text-yellow-600">Provincial Level</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-yellow-700">892项</p>
                                <p class="text-xs text-yellow-600">67.2亿元</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm font-medium text-green-800">市级项目</p>
                                <p class="text-xs text-green-600">Municipal Level</p>
                            </div>
                            <div class="text-right">
                                <p class="text-xl font-bold text-green-700">1,609项</p>
                                <p class="text-xs text-green-600">43.8亿元</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab切换功能
            const projectTabs = document.querySelectorAll('#tab-tech, #tab-research, #tab-investment');
            const timeTabs = document.querySelectorAll('#time-year, #time-total');

            function switchTab(tabs, activeTab) {
                tabs.forEach(tab => {
                    tab.classList.remove('tab-active');
                    tab.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
                });
                activeTab.classList.add('tab-active');
                activeTab.classList.remove('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
            }

            projectTabs.forEach(tab => {
                tab.addEventListener('click', () => switchTab(projectTabs, tab));
            });

            timeTabs.forEach(tab => {
                tab.addEventListener('click', () => switchTab(timeTabs, tab));
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 