<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果综合分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        indigo: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex-1 p-6">
        <div class="h-[calc(100vh-120px)] flex flex-col">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-semibold text-indigo-800 flex items-center">
                    <svg class="mr-2 h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    成果综合分析
                </h1>
                
                <div class="flex gap-2">
                    <button class="flex items-center gap-1 px-3 py-2 text-sm border border-indigo-200 text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出报告
                    </button>
                    
                    <button class="flex items-center gap-1 px-3 py-2 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
            
            <!-- 筛选控制区 -->
            <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-4 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">成果类别</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option>全部类别</option>
                            <option>科技奖励</option>
                            <option>知识产权</option>
                            <option>论文期刊</option>
                            <option>技术标准</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option>全部状态</option>
                            <option>已授权</option>
                            <option>申请中</option>
                            <option>已转化</option>
                            <option>待转化</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">统计口径</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option>按申请时间</option>
                            <option>按授权时间</option>
                            <option>按转化时间</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option>近一年</option>
                            <option>近三年</option>
                            <option>近五年</option>
                            <option>自定义</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">地域范围</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            <option>全国</option>
                            <option>浙江省</option>
                            <option>杭州市</option>
                            <option>宁波市</option>
                        </select>
                    </div>
                    <div class="flex items-end gap-2">
                        <button class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                            分析
                        </button>
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                            重置
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="flex-1 overflow-y-auto">
                <!-- 指标概览区 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-4 cursor-pointer hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">成果总量</p>
                                <p class="text-2xl font-bold text-indigo-600">12,456</p>
                                <div class="flex items-center mt-1">
                                    <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                    </svg>
                                    <span class="text-sm text-green-600">+8.5%</span>
                                </div>
                            </div>
                            <div class="w-16 h-8">
                                <canvas id="chart1" width="64" height="32"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-4 cursor-pointer hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">年度新增</p>
                                <p class="text-2xl font-bold text-indigo-600">2,134</p>
                                <div class="flex items-center mt-1">
                                    <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                    </svg>
                                    <span class="text-sm text-green-600">+12.3%</span>
                                </div>
                            </div>
                            <div class="w-16 h-8">
                                <canvas id="chart2" width="64" height="32"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-4 cursor-pointer hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">转化率</p>
                                <p class="text-2xl font-bold text-indigo-600">68.2%</p>
                                <div class="flex items-center mt-1">
                                    <svg class="w-4 h-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                                    </svg>
                                    <span class="text-sm text-red-600">-2.1%</span>
                                </div>
                            </div>
                            <div class="w-16 h-8">
                                <canvas id="chart3" width="64" height="32"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-4 cursor-pointer hover:shadow-lg transition-shadow">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">交易金额</p>
                                <p class="text-2xl font-bold text-indigo-600">3.2亿</p>
                                <div class="flex items-center mt-1">
                                    <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                                    </svg>
                                    <span class="text-sm text-green-600">+15.7%</span>
                                </div>
                            </div>
                            <div class="w-16 h-8">
                                <canvas id="chart4" width="64" height="32"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                    <!-- 分类统计区 -->
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">成果分类统计</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md">柱状图</button>
                                <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md">饼图</button>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="categoryChart"></canvas>
                        </div>
                    </div>
                    
                    <!-- 主体分析区 -->
                    <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-6">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-gray-900">创新主体分析</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md">企业</button>
                                <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md">高校</button>
                                <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md">科研院所</button>
                            </div>
                        </div>
                        <div class="h-64">
                            <canvas id="entityChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <!-- 区域分布区 -->
                <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">区域分布热力图</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md">全国</button>
                            <button class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md">浙江省</button>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="lg:col-span-2">
                            <div class="h-80 bg-gray-100 rounded-lg flex items-center justify-center">
                                <img src="https://source.unsplash.com/800x320?map,china" alt="中国地图" class="w-full h-full object-cover rounded-lg">
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900 mb-3">区域排名</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-sm">浙江省</p>
                                        <p class="text-xs text-gray-500">成果总量: 3,245</p>
                                    </div>
                                    <span class="text-lg font-bold text-indigo-600">1</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-sm">江苏省</p>
                                        <p class="text-xs text-gray-500">成果总量: 2,987</p>
                                    </div>
                                    <span class="text-lg font-bold text-indigo-600">2</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-sm">广东省</p>
                                        <p class="text-xs text-gray-500">成果总量: 2,756</p>
                                    </div>
                                    <span class="text-lg font-bold text-indigo-600">3</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-sm">北京市</p>
                                        <p class="text-xs text-gray-500">成果总量: 2,543</p>
                                    </div>
                                    <span class="text-lg font-bold text-indigo-600">4</span>
                                </div>
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-sm">上海市</p>
                                        <p class="text-xs text-gray-500">成果总量: 2,234</p>
                                    </div>
                                    <span class="text-lg font-bold text-indigo-600">5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 转化交易与技术分布区 -->
                <div class="bg-white rounded-lg shadow-md border border-indigo-100 p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">转化交易与技术分布</h3>
                        <div class="flex space-x-2">
                            <div class="flex bg-gray-100 rounded-md p-1">
                                <button id="trendPageBtn" class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md transition-colors">
                                    趋势分析
                                </button>
                                <button id="distributionPageBtn" class="px-3 py-1 text-sm text-gray-600 hover:bg-gray-50 rounded-md transition-colors">
                                    技术分布
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 趋势分析页面 -->
                    <div id="trendPage" class="space-y-6">
                        <!-- 上半部分：趋势折线图 -->
                        <div class="relative">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-900">成果转化率、交易次数及交易金额趋势</h4>
                                <div class="flex space-x-2">
                                    <button onclick="exportChart('trendChart')" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                        导出PNG
                                    </button>
                                    <button onclick="drillDownTrend()" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        钻取明细
                                    </button>
                                </div>
                            </div>
                            <div class="h-80 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg p-4">
                                <canvas id="trendChart"></canvas>
                            </div>
                        </div>
                        
                        <!-- 下半部分：技术领域堆叠柱图 -->
                        <div class="relative">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-900">按技术领域划分的成果数量分布</h4>
                                <div class="flex space-x-2">
                                    <button onclick="exportChart('stackedChart')" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                        导出PNG
                                    </button>
                                    <button onclick="drillDownStacked()" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        钻取明细
                                    </button>
                                </div>
                            </div>
                            <div class="h-80 bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg p-4">
                                <canvas id="stackedChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 技术分布页面 -->
                    <div id="distributionPage" class="hidden">
                        <div class="relative">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-sm font-medium text-gray-900">技术领域分布</h4>
                                <div class="flex space-x-2">
                                    <button onclick="exportChart('technologyChart')" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                        导出PNG
                                    </button>
                                    <button onclick="drillDownTechnology()" class="px-2 py-1 text-xs border border-gray-300 text-gray-600 hover:bg-gray-50 rounded">
                                        <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        钻取明细
                                    </button>
                                </div>
                            </div>
                            <div class="h-96">
                                <canvas id="technologyChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 输出与分享区 -->
    <div class="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg border border-indigo-100 p-4">
        <div class="flex flex-col space-y-2">
            <button class="flex items-center gap-2 px-4 py-2 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                导出Excel
            </button>
            <button class="flex items-center gap-2 px-4 py-2 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                生成PDF报告
            </button>
            <button class="flex items-center gap-2 px-4 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                复制图表链接
            </button>
        </div>
        <div class="mt-3">
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="bg-indigo-600 h-2 rounded-full" style="width: 0%" id="exportProgress"></div>
            </div>
            <p class="text-xs text-gray-500 mt-1">导出进度: 0%</p>
        </div>
    </div>
    
    <script>
        // 初始化小型趋势图
        function initMiniCharts() {
            const charts = ['chart1', 'chart2', 'chart3', 'chart4'];
            charts.forEach((chartId, index) => {
                const ctx = document.getElementById(chartId).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1', '2', '3', '4', '5', '6'],
                        datasets: [{
                            data: [12, 19, 15, 25, 22, 30],
                            borderColor: '#4f46e5',
                            backgroundColor: 'rgba(79, 70, 229, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        },
                        elements: {
                            point: { radius: 0 }
                        }
                    }
                });
            });
        }
        
        // 分类统计图表实例
        let categoryChartInstance = null;
        
        // 初始化分类统计图
        function initCategoryChart() {
            const ctx = document.getElementById('categoryChart').getContext('2d');
            categoryChartInstance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['科技奖励', '知识产权', '论文期刊', '技术标准', '其他'],
                    datasets: [{
                        label: '成果数量',
                        data: [3245, 4567, 2890, 1234, 890],
                        backgroundColor: [
                            '#4f46e5',
                            '#06b6d4',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        hoverBackgroundColor: [
                            '#3730a3',
                            '#0891b2',
                            '#059669',
                            '#d97706',
                            '#dc2626'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { 
                            display: true,
                            position: 'bottom'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toLocaleString() + '项';
                                }
                            }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const label = categoryChartInstance.data.labels[index];
                            alert(`点击了: ${label}`);
                        }
                    },
                    onHover: (event, elements) => {
                        event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    }
                }
            });
        }
        
        // 切换分类统计图表类型
        function toggleCategoryChart(type) {
            if (categoryChartInstance) {
                categoryChartInstance.destroy();
            }
            
            const ctx = document.getElementById('categoryChart').getContext('2d');
            const chartData = {
                labels: ['科技奖励', '知识产权', '论文期刊', '技术标准', '其他'],
                datasets: [{
                    label: '成果数量',
                    data: [3245, 4567, 2890, 1234, 890],
                    backgroundColor: [
                        '#4f46e5',
                        '#06b6d4',
                        '#10b981',
                        '#f59e0b',
                        '#ef4444'
                    ],
                    hoverBackgroundColor: [
                        '#3730a3',
                        '#0891b2',
                        '#059669',
                        '#d97706',
                        '#dc2626'
                    ]
                }]
            };
            
            const commonOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { 
                        display: true,
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed.y || context.parsed) / total * 100).toFixed(1);
                                return context.label + ': ' + (context.parsed.y || context.parsed).toLocaleString() + '项 (' + percentage + '%)';
                            }
                        }
                    }
                },
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const index = elements[0].index;
                        const label = categoryChartInstance.data.labels[index];
                        alert(`点击了: ${label}`);
                    }
                },
                onHover: (event, elements) => {
                    event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
                }
            };
            
            categoryChartInstance = new Chart(ctx, {
                type: type,
                data: chartData,
                options: commonOptions
            });
        }
        
        // 初始化主体分析图
        function initEntityChart() {
            const ctx = document.getElementById('entityChart').getContext('2d');
            new Chart(ctx, {
                type: 'horizontalBar',
                data: {
                    labels: ['阿里巴巴', '浙江大学', '华为技术', '中科院', '腾讯科技'],
                    datasets: [{
                        label: '成果数量',
                        data: [456, 389, 345, 298, 267],
                        backgroundColor: '#4f46e5'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    }
                }
            });
        }
        
        // 初始化趋势图表
        function initTrendChart() {
            const ctx = document.getElementById('trendChart').getContext('2d');
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '转化率(%)',
                        data: [65, 68, 70, 67, 69, 72, 75, 73, 76, 78, 80, 82],
                        borderColor: '#4f46e5',
                        backgroundColor: 'rgba(79, 70, 229, 0.1)',
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '交易次数',
                        data: [120, 135, 150, 142, 158, 165, 172, 168, 175, 182, 190, 195],
                        borderColor: '#06b6d4',
                        backgroundColor: 'rgba(6, 182, 212, 0.1)',
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '交易金额(万元)',
                        data: [2300, 2800, 3200, 2900, 3400, 3800, 4100, 3900, 4200, 4500, 4800, 5100],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#4f46e5',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '月份'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: '转化率(%)'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: '交易次数'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            position: 'right',
                        }
                    }
                }
            });
        }
        
        // 初始化堆叠柱图
        function initStackedChart() {
            const ctx = document.getElementById('stackedChart').getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '电子信息', '先进制造'],
                    datasets: [{
                        label: '已转化',
                        data: [1200, 890, 756, 634, 523, 445],
                        backgroundColor: '#4f46e5',
                        borderColor: '#4f46e5',
                        borderWidth: 1
                    }, {
                        label: '转化中',
                        data: [680, 456, 389, 298, 267, 234],
                        backgroundColor: '#06b6d4',
                        borderColor: '#06b6d4',
                        borderWidth: 1
                    }, {
                        label: '待转化',
                        data: [465, 544, 422, 302, 198, 156],
                        backgroundColor: '#10b981',
                        borderColor: '#10b981',
                        borderWidth: 1
                    }, {
                        label: '暂停转化',
                        data: [123, 89, 67, 45, 34, 28],
                        backgroundColor: '#f59e0b',
                        borderColor: '#f59e0b',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#4f46e5',
                            borderWidth: 1,
                            callbacks: {
                                afterLabel: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = (context.parsed.y / total * 100).toFixed(1);
                                    return '占比: ' + percentage + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            title: {
                                display: true,
                                text: '技术领域'
                            }
                        },
                        y: {
                            stacked: true,
                            title: {
                                display: true,
                                text: '成果数量'
                            }
                        }
                    }
                }
            });
        }
        
        // 技术分布图表实例
        let technologyChartInstance = null;
        
        // 初始化技术分布图
        function initTechnologyChart() {
            const ctx = document.getElementById('technologyChart').getContext('2d');
            technologyChartInstance = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '其他'],
                    datasets: [{
                        data: [2345, 1890, 1567, 1234, 890],
                        backgroundColor: [
                            '#4f46e5',
                            '#06b6d4',
                            '#10b981',
                            '#f59e0b',
                            '#ef4444'
                        ],
                        hoverBackgroundColor: [
                            '#3730a3',
                            '#0891b2',
                            '#059669',
                            '#d97706',
                            '#dc2626'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = (context.parsed / total * 100).toFixed(1);
                                    return context.label + ': ' + context.parsed.toLocaleString() + '项 (' + percentage + '%)';
                                },
                                afterLabel: function(context) {
                                    return '点击查看详细信息';
                                }
                            },
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#4f46e5',
                            borderWidth: 1
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const label = technologyChartInstance.data.labels[index];
                            const value = technologyChartInstance.data.datasets[0].data[index];
                            const total = technologyChartInstance.data.datasets[0].data.reduce((a, b) => a + b, 0);
                            const percentage = (value / total * 100).toFixed(1);
                            
                            // 创建详细信息弹窗
                            showTechnologyDetail(label, value, percentage);
                        }
                    },
                    onHover: (event, elements) => {
                        event.native.target.style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    },
                    animation: {
                        animateRotate: true,
                        animateScale: true,
                        duration: 1000
                    }
                }
            });
        }
        
        // 显示技术领域详细信息
        function showTechnologyDetail(label, value, percentage) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-md mx-4">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">${label} 详细信息</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">成果数量:</span>
                            <span class="font-semibold">${value.toLocaleString()}项</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">占比:</span>
                            <span class="font-semibold">${percentage}%</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">技术成熟度:</span>
                            <span class="font-semibold">较高</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">市场前景:</span>
                            <span class="font-semibold">广阔</span>
                        </div>
                    </div>
                    <div class="mt-6 flex space-x-3">
                        <button onclick="alert('查看${label}详细报告')" class="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700">
                            查看详细报告
                        </button>
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-50">
                            关闭
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }
        
        // 页面切换功能
        function switchPage(pageType) {
            const trendPage = document.getElementById('trendPage');
            const distributionPage = document.getElementById('distributionPage');
            const trendBtn = document.getElementById('trendPageBtn');
            const distributionBtn = document.getElementById('distributionPageBtn');
            
            if (pageType === 'trend') {
                trendPage.classList.remove('hidden');
                distributionPage.classList.add('hidden');
                trendBtn.classList.add('bg-indigo-600', 'text-white');
                trendBtn.classList.remove('text-gray-600', 'hover:bg-gray-50');
                distributionBtn.classList.remove('bg-indigo-600', 'text-white');
                distributionBtn.classList.add('text-gray-600', 'hover:bg-gray-50');
            } else {
                trendPage.classList.add('hidden');
                distributionPage.classList.remove('hidden');
                distributionBtn.classList.add('bg-indigo-600', 'text-white');
                distributionBtn.classList.remove('text-gray-600', 'hover:bg-gray-50');
                trendBtn.classList.remove('bg-indigo-600', 'text-white');
                trendBtn.classList.add('text-gray-600', 'hover:bg-gray-50');
            }
        }
        
        // 导出图表为PNG
        function exportChart(chartId) {
            const canvas = document.getElementById(chartId);
            const url = canvas.toDataURL('image/png');
            const link = document.createElement('a');
            link.download = chartId + '_' + new Date().getTime() + '.png';
            link.href = url;
            link.click();
            
            // 显示导出成功提示
            alert('图表已成功导出为PNG格式！');
        }
        
        // 钻取明细功能
        function drillDownTrend() {
            alert('钻取趋势明细：将显示详细的转化率、交易次数和交易金额数据表格');
        }
        
        function drillDownStacked() {
            alert('钻取堆叠明细：将显示各技术领域的详细转化状态数据');
        }
        
        function drillDownTechnology() {
            alert('钻取技术明细：将显示各技术领域的详细分布数据');
        }
        
        // 页面加载完成后初始化所有图表
        document.addEventListener('DOMContentLoaded', function() {
            initMiniCharts();
            initCategoryChart();
            initEntityChart();
            initTrendChart();
            initStackedChart();
            initTechnologyChart();
            
            // 绑定分类统计图表切换按钮事件
            const categoryButtons = document.querySelectorAll('.bg-white.rounded-lg.shadow-md.border.border-indigo-100.p-6 .flex.space-x-2 button');
            if (categoryButtons.length >= 2) {
                categoryButtons[0].addEventListener('click', function() {
                    toggleCategoryChart('bar');
                    this.className = 'px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md';
                    categoryButtons[1].className = 'px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md';
                });
                
                categoryButtons[1].addEventListener('click', function() {
                    toggleCategoryChart('pie');
                    this.className = 'px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded-md';
                    categoryButtons[0].className = 'px-3 py-1 text-sm text-gray-600 hover:bg-gray-100 rounded-md';
                });
            }
            
            // 添加转化交易与技术分布区页面切换事件监听器
            document.getElementById('trendPageBtn').addEventListener('click', function() {
                switchPage('trend');
            });
            
            document.getElementById('distributionPageBtn').addEventListener('click', function() {
                switchPage('distribution');
            });
        });
        
        // 模拟导出进度
        function simulateExport() {
            const progressBar = document.getElementById('exportProgress');
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                progressBar.nextElementSibling.textContent = `导出进度: ${progress}%`;
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        progress = 0;
                        progressBar.style.width = '0%';
                        progressBar.nextElementSibling.textContent = '导出进度: 0%';
                    }, 2000);
                }
            }, 200);
        }
        
        // 绑定导出按钮事件
        document.querySelectorAll('button').forEach(button => {
            if (button.textContent.includes('导出') || button.textContent.includes('生成')) {
                button.addEventListener('click', simulateExport);
            }
        });
    </script>
</body>
</html>