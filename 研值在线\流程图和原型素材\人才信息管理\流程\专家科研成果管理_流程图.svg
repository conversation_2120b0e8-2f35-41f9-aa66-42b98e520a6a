<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">专家科研成果管理业务流程</text>

  <!-- 阶段一：数据初始化与展示 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与展示</text>
  
  <!-- 节点1: 系统加载 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统自动加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">显示所有专家科研成果数据</text>
  </g>

  <!-- 阶段二：查询筛选与交互管理 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询筛选与交互管理</text>

  <!-- 节点2: 条件筛选 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设定检索条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时刷新符合条件的成果列表</text>
  </g>

  <!-- 节点3: 详情钻取 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">弹窗展示完整信息与支撑材料</text>
  </g>

  <!-- 节点4: 关联成果 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联成果</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">批量勾选并校验去重入库</text>
  </g>

  <!-- 阶段三：编辑与操作管理 -->
  <text x="700" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：编辑与操作管理</text>

  <!-- 节点5: 编辑维护 -->
  <g transform="translate(350, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑维护</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写修订关键信息并保存</text>
  </g>

  <!-- 节点6: 移除操作 -->
  <g transform="translate(650, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">解绑失效成果并记录日志</text>
  </g>

  <!-- 阶段四：数据导出与系统管理 -->
  <text x="700" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与系统管理</text>

  <!-- 节点7: 数据导出 -->
  <g transform="translate(500, 720)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据导出与归档</text>
    <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-80">生成目标文件</tspan>
      <tspan dx="40">支持下载</tspan>
      <tspan dx="40">满足上报需求</tspan>
    </text>
  </g>

  <!-- 连接线 系统加载 -> 条件筛选 -->
  <path d="M 650 200 C 550 240, 400 280, 300 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 系统加载 -> 详情钻取 -->
  <path d="M 700 200 Q 700 260 600 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 系统加载 -> 关联成果 -->
  <path d="M 750 200 C 850 240, 950 280, 900 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情钻取 -> 编辑维护 -->
  <path d="M 550 390 C 500 450, 450 480, 450 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情钻取 -> 移除操作 -->
  <path d="M 650 390 C 700 450, 750 480, 750 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 编辑维护 -> 数据导出 -->
  <path d="M 450 590 C 500 640, 550 680, 600 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 移除操作 -> 数据导出 -->
  <path d="M 750 590 C 700 640, 650 680, 700 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 关联成果 -> 数据导出 -->
  <path d="M 900 390 C 950 500, 900 600, 800 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从数据导出回到系统加载 -->
  <path d="M 500 760 C 200 800, 100 600, 150 400, 200 200, 500 150, 600 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="780" text-anchor="middle" font-size="11" fill="#666">系统优化反馈</text>

</svg>