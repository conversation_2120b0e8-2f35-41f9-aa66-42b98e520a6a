<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="750" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">国家级省级农作物品种管理业务流程</text>

  <!-- 阶段一：数据加载与权限控制 -->
  <text x="750" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与权限控制</text>
  
  <!-- 节点1: 用户进入 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">农作物品种管理</text>
  </g>

  <!-- 节点2: 权限加载 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">可见品种数据</text>
  </g>

  <!-- 节点3: 指标概览 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成指标概览</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已加载</text>
  </g>
  
  <!-- 连接线 阶段一 -->
  <path d="M 420 165 Q 460 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 165 Q 760 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据检索与整合 -->
  <text x="750" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据检索与整合</text>

  <!-- 节点4: 筛选条件 -->
  <g transform="translate(150, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交筛选</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">筛选条件设定</text>
  </g>

  <!-- 节点5: 品种检索 -->
  <g transform="translate(450, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">品种检索服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">拉取符合条件数据</text>
  </g>

  <!-- 节点6: 主体关联 -->
  <g transform="translate(750, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主体关联服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">创新主体信息</text>
  </g>

  <!-- 节点7: 数据整合 -->
  <g transform="translate(1050, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据整合刷新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态：已完成</text>
  </g>

  <!-- 连接线 指标概览 -> 筛选条件 -->
  <path d="M 910 200 C 910 240, 350 270, 260 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 阶段二 -->
  <path d="M 370 345 Q 410 345 450 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 560 310 C 560 290, 640 290, 750 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="650" y="285" text-anchor="middle" font-size="11" fill="#666">并行触发</text>
  <path d="M 670 345 Q 710 345 750 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 970 345 Q 1010 345 1050 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="750" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点8: 新增编辑 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增编辑操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">字段合法性校验</text>
  </g>

  <!-- 节点9: 批量导入 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">文件格式校验</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">Excel文件生成</text>
  </g>

  <!-- 节点11: 缓存刷新 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">清空重载数据</text>
  </g>

  <!-- 连接线 数据整合 -> 各操作 -->
  <path d="M 1160 380 C 1160 420, 300 450, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1160 380 C 1160 420, 550 450, 450 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1160 380 C 1160 420, 800 450, 700 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1160 380 C 1160 420, 1050 450, 950 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：定时同步与数据更新 -->
  <text x="750" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：定时同步与数据更新</text>

  <!-- 节点12: 定时同步 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步服务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">全国/省级数据增量</text>
  </g>

  <!-- 节点13: 比对更新 -->
  <g transform="translate(650, 670)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">比对更新机制</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">审定状态与权属变动</text>
  </g>

  <!-- 节点14: 数据准确性 -->
  <g transform="translate(1000, 670)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据时效保障</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">确保准确性</text>
  </g>

  <!-- 连接线 阶段四 -->
  <path d="M 550 705 Q 590 705 650 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 705 Q 940 705 1000 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：各操作 -> 品种库更新 -->
  <path d="M 200 560 C 200 600, 100 620, 100 680" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <path d="M 450 560 C 450 600, 200 620, 200 680" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="590" text-anchor="middle" font-size="11" fill="#666">写入品种库</text>

  <!-- 定时循环箭头 -->
  <path d="M 425 740 C 425 780, 500 800, 575 780 C 650 760, 725 780, 775 740" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="600" y="795" text-anchor="middle" font-size="11" fill="#666">每日定时执行</text>

</svg>