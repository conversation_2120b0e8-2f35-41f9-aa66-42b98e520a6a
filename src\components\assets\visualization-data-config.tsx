'use client'

import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Database, 
  Globe, 
  FileJson, 
  Table2, 
  RefreshCw,
  Clock,
  Key,
  Shield,
  Settings2
} from "lucide-react"

export function VisualizationDataConfig() {
  return (
    <ScrollArea className="h-[calc(100vh-250px)]">
      <div className="p-4 space-y-6">
        {/* 数据源配置 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-700">数据源配置</h3>
            <Badge variant="outline" className="text-blue-500">
              <Clock className="mr-1 h-3 w-3" />
              实时
            </Badge>
          </div>

          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="w-full grid grid-cols-3">
              <TabsTrigger value="basic">基础配置</TabsTrigger>
              <TabsTrigger value="advanced">高级配置</TabsTrigger>
              <TabsTrigger value="security">安全配置</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>数据源类型</Label>
                  <Select defaultValue="api">
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="选择数据源类型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="api">
                        <div className="flex items-center">
                          <Globe className="mr-2 h-4 w-4" />
                          API 接口
                        </div>
                      </SelectItem>
                      <SelectItem value="database">
                        <div className="flex items-center">
                          <Database className="mr-2 h-4 w-4" />
                          数据库
                        </div>
                      </SelectItem>
                      <SelectItem value="file">
                        <div className="flex items-center">
                          <FileJson className="mr-2 h-4 w-4" />
                          文件上传
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>数据源地址</Label>
                  <Input placeholder="请输入数据源地址" className="bg-white" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>请求方式</Label>
                    <Select defaultValue="get">
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="选择请求方式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="get">GET</SelectItem>
                        <SelectItem value="post">POST</SelectItem>
                        <SelectItem value="put">PUT</SelectItem>
                        <SelectItem value="delete">DELETE</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>数据格式</Label>
                    <Select defaultValue="json">
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="选择数据格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="json">JSON</SelectItem>
                        <SelectItem value="xml">XML</SelectItem>
                        <SelectItem value="csv">CSV</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>请求头设置</Label>
                  <Input placeholder="Content-Type: application/json" className="bg-white" />
                </div>

                <div className="space-y-2">
                  <Label>请求参数</Label>
                  <Input placeholder='{"key": "value"}' className="bg-white" />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>刷新间隔（秒）</Label>
                    <Input type="number" defaultValue={30} className="bg-white" />
                  </div>
                  <div className="space-y-2">
                    <Label>超时时间（秒）</Label>
                    <Input type="number" defaultValue={10} className="bg-white" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>数据处理脚本</Label>
                  <Input as="textarea" rows={3} placeholder="function process(data) { return data; }" className="bg-white" />
                </div>

                <div className="flex items-center justify-between">
                  <Label>启用缓存</Label>
                  <Switch className="data-[state=checked]:bg-blue-500" />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="security" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>认证方式</Label>
                  <Select defaultValue="none">
                    <SelectTrigger className="bg-white">
                      <SelectValue placeholder="选择认证方式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">无认证</SelectItem>
                      <SelectItem value="basic">Basic Auth</SelectItem>
                      <SelectItem value="bearer">Bearer Token</SelectItem>
                      <SelectItem value="apikey">API Key</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>认证信息</Label>
                  <Input type="password" placeholder="请输入认证信息" className="bg-white" />
                </div>

                <div className="flex items-center justify-between">
                  <Label>启用SSL验证</Label>
                  <Switch className="data-[state=checked]:bg-blue-500" />
                </div>

                <div className="flex items-center justify-between">
                  <Label>数据加密</Label>
                  <Switch className="data-[state=checked]:bg-blue-500" />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* 数据预览 */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-700">数据预览</h3>
            <Button variant="outline" className="text-blue-500 hover:bg-blue-50">
              <RefreshCw className="mr-2 h-4 w-4" />
              刷新数据
            </Button>
          </div>
          <div className="border rounded-lg p-4 bg-gray-50 min-h-[200px]">
            <pre className="text-sm text-gray-600">
              {JSON.stringify({
                "code": 200,
                "data": {
                  "items": [
                    {"id": 1, "name": "示例数据1", "value": 100},
                    {"id": 2, "name": "示例数据2", "value": 200}
                  ]
                }
              }, null, 2)}
            </pre>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end gap-2">
          <Button variant="outline" className="hover:bg-blue-50">
            重置配置
          </Button>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white">
            保存配置
          </Button>
        </div>
      </div>
    </ScrollArea>
  )
} 