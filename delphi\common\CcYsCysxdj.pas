unit CcYsCysxdj;

interface

uses
  Classes;

type
  TCcYsCysxdj = class
  private

    FYsCysxdjid: Integer;
    FYsid: Integer;
    FCysxJyl: Double;
    FCysxDj: Double;
    FCysxSh: Double;
    FCysxCb: Double;

    FCyrxJyl: Double;
    FCyrxDj: Double;
    FCyrxSh: Double;
    FCyrxCb: Double;

    FCyyhJyl: Double;
    FCyyhDj: Double;
    FCyyhSh: Double;
    FCyyhCb: Double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property YsCysxdjid: Integer read FYsCysxdjid write FYsCysxdjid;
    property Ysid: Integer read FYsid write FYsid;
    property CysxJyl: Double read FCysxJyl write FCysxJyl;
    property CysxDj: Double read FCysxDj write FCysxDj;
    property CysxSh: Double read FCysxSh write FCysxSh;
    property CysxCb: Double read FCysxCb write FCysxCb;
    property CyrxJyl: Double read FCyrxJyl write FCyrxJyl;
    property CyrxDj: Double read FCyrxDj write FCyrxDj;
    property CyrxSh: Double read FCyrxSh write FCyrxSh;
    property CyrxCb: Double read FCyrxCb write FCyrxCb;
    property CyyhJyl: Double read FCyyhJyl write FCyyhJyl;
    property CyyhDj: Double read FCyyhDj write FCyyhDj;
    property CyyhSh: Double read FCyyhSh write FCyyhSh;
    property CyyhCb: Double read FCyyhCb write FCyyhCb;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
