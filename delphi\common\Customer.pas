unit Customer;

interface
uses
  Classes;

type
  TCustomer = class
  private
    FCustomerID: integer;
    FCustomerName: string;
    FCustomerDes: string;
    FCustomerDetail: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property CustomerID: integer read FCustomerID write FCustomerID;
    property CustomerName: string read FCustomerName write FCustomerName;
    property CustomerDes: string read FCustomerDes write FCustomerDes;
    property CustomerDetail: string read FCustomerDetail write FCustomerDetail;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.

