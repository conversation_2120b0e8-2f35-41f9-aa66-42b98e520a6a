<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技专项展示流程图</text>

  <!-- 阶段一：数据集成与同步 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据集成与同步</text>
  
  <!-- 节点1: 数据源同步 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据同步</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">国家科技项目数据库、省市平台、院所内部系统</text>
  </g>

  <!-- 节点2: 数据处理 -->
  <g transform="translate(550, 250)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据映射与处理</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">数据映射、去重、分类标签赋值</text>
  </g>

  <!-- 节点3: 专项主题库 -->
  <g transform="translate(550, 370)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科研码专项主题库</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">存储处理后的专项立项信息</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 320 Q 700 345 700 370" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计计算与缓存 -->
  <text x="700" y="500" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计计算与缓存</text>

  <!-- 节点4: 统计计算服务 -->
  <g transform="translate(200, 530)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计计算服务</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">每日增量聚合，生成统计数据</text>
  </g>

  <!-- 节点5: 缓存数据库 -->
  <g transform="translate(560, 530)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存数据库</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">存储立项数、经费汇总、年度序列数据</text>
  </g>

  <!-- 节点6: 前端接口调用 -->
  <g transform="translate(920, 530)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端接口调用</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">获取指标看板与统计图数据</text>
  </g>

  <!-- 连接线 专项主题库 -> 统计计算 -->
  <path d="M 600 440 C 500 470, 400 500, 340 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 统计计算 -> 缓存数据库 -->
  <path d="M 480 565 C 520 565, 520 565, 560 565" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 缓存数据库 -> 前端接口 -->
  <path d="M 840 565 C 880 565, 880 565, 920 565" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：用户交互与详情展示 -->
  <text x="700" y="670" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：用户交互与详情展示</text>

  <!-- 节点7: 下钻查询 -->
  <g transform="translate(150, 700)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下钻查询</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">饼图、数字卡片点击，清册接口调用</text>
  </g>

  <!-- 节点8: 项目详情 -->
  <g transform="translate(500, 700)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情展示</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">基本信息、资金拨付、阶段成果</text>
  </g>

  <!-- 节点9: 文件下载 -->
  <g transform="translate(850, 700)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件下载服务</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">原始批复文件下载链接</text>
  </g>

  <!-- 连接线 前端接口 -> 下钻查询 -->
  <path d="M 1000 600 C 950 630, 500 650, 290 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 下钻查询 -> 项目详情 -->
  <path d="M 430 735 C 470 735, 470 735, 500 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 项目详情 -> 文件下载 -->
  <path d="M 780 735 C 820 735, 820 735, 850 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据质量监控 -->
  <text x="700" y="840" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据质量监控与治理</text>
  
  <!-- 节点10: 数据质量监控与治理 -->
  <g transform="translate(500, 870)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据质量监控与治理</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">每日校验完整性</tspan>
        <tspan dx="60">异常推送工单</tspan>
        <tspan dx="60">人工确认修正</tspan>
      </text>
  </g>

  <!-- 连接线 项目详情 -> 数据质量监控 -->
  <path d="M 640 770 C 640 800, 640 840, 640 870" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 数据质量监控 -> 缓存数据库 -->
  <path d="M 500 910 C 200 940, 100 700, 400 600" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="930" text-anchor="middle" font-size="12" fill="#666">数据修正同步至缓存</text>

  <!-- 用户行为日志标注 -->
  <text x="290" y="790" text-anchor="middle" font-size="12" fill="#666">用户行为日志记录</text>

</svg>