<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员参与项目管理业务流程</text>

  <!-- 阶段一：数据加载与筛选 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与筛选</text>
  
  <!-- 节点1: 进入界面 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">进入管理界面</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">用户访问研发人员项目管理</text>
  </g>

  <!-- 节点2: 自动加载 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统自动加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">加载该研发人员全部项目信息</text>
  </g>

  <!-- 节点3: 条件筛选 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">条件筛选</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">设置检索条件，实时刷新列表</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 420 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 720 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：项目操作与管理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：项目操作与管理</text>

  <!-- 节点4: 新增项目 -->
  <g transform="translate(150, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">填写基础信息，系统校验关联</text>
  </g>

  <!-- 节点5: 编辑移除 -->
  <g transform="translate(450, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑移除</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">校验状态权限，更新数据库</text>
  </g>

  <!-- 节点6: 项目关联 -->
  <g transform="translate(750, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目关联</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">选择目标项目，完成关联绑定</text>
  </g>

  <!-- 连接线 筛选 -> 新增项目 -->
  <path d="M 800 200 C 700 250, 400 280, 260 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 新增项目 -> 编辑移除 -->
  <path d="M 370 345 Q 400 345 450 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 编辑移除 -> 项目关联 -->
  <path d="M 670 345 Q 700 345 750 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与数据钻取 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与数据钻取</text>

  <!-- 节点7: 项目详情 -->
  <g transform="translate(300, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">展示核心数据与人员名单</text>
  </g>

  <!-- 节点8: 数据钻取 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据钻取</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">深度分析项目信息</text>
  </g>

  <!-- 连接线 项目关联 -> 项目详情 -->
  <path d="M 750 380 C 650 420, 500 460, 410 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 项目详情 -> 数据钻取 -->
  <path d="M 520 525 Q 550 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与应用 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与应用</text>
  
  <!-- 节点9: 数据导出与分析 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
      <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据导出与分析应用</text>
      <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">Excel导出</tspan>
        <tspan dx="60">外部分析</tspan>
        <tspan dx="60">报告归档</tspan>
      </text>
  </g>

  <!-- 连接线 数据钻取 -> 数据导出 -->
  <path d="M 710 560 C 710 600, 700 640, 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 反馈循环：从数据导出回到条件筛选 -->
  <path d="M 900 710 C 1100 750, 1150 400, 1020 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1100" y="450" text-anchor="middle" font-size="11" fill="#666">数据反馈</text>
  
  <!-- 反馈循环：从编辑移除回到自动加载 -->
  <path d="M 450 345 C 300 280, 200 220, 500 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="300" y="250" text-anchor="middle" font-size="11" fill="#666">操作日志</text>

</svg>