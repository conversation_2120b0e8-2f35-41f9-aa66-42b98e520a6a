"use client"

import { useEffect, useState } from "react"
import { DragDropContext, Droppable, Draggable, DropResult } from "react-beautiful-dnd"

interface DndContextProps {
  children: React.ReactNode
  onDragEnd: (result: DropResult) => void
}

// 包装 DragDropContext 组件
export function DndContext({ children, onDragEnd }: DndContextProps) {
  const [enabled, setEnabled] = useState(false)

  useEffect(() => {
    const animation = requestAnimationFrame(() => setEnabled(true))
    return () => {
      cancelAnimationFrame(animation)
      setEnabled(false)
    }
  }, [])

  if (!enabled) {
    return null
  }

  return <DragDropContext onDragEnd={onDragEnd}>{children}</DragDropContext>
}

// 重新导出其他组件
export { Droppable, Draggable }

// 导出类型
export type {
  DraggableLocation,
  DraggableId,
  DroppableId,
  DropResult,
  DraggableProvided,
  DroppableProvided,
  DraggableStateSnapshot,
  DroppableStateSnapshot
} from 'react-beautiful-dnd' 