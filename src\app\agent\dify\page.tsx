'use client'

import { useState, useRef, useEffect, useMemo } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Send, Brain, Headset,Sparkles, Plus, Trash2, MessageSquare, FileText, ExternalLink, Play, CheckCircle, Clock, Zap, Download, File } from "lucide-react"

interface WorkflowNode {
  id: string
  node_id: string
  node_type: string
  title: string
  index: number
  status: 'waiting' | 'running' | 'completed' | 'failed'
  elapsed_time?: number
  created_at?: number
  finished_at?: number
  tokens?: number
}

interface MessageFile {
  id?: string
  filename: string
  url: string
  mime_type: string
  size: number
  type: string
  extension?: string
}

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  conversation_id?: string
  message_id?: string
  metadata?: {
    retriever_resources?: Array<{
      position: number
      dataset_name: string
      document_name: string
      score: number
      content: string
      hit_count: number
      word_count: number
    }>
    usage?: {
      total_tokens: number
      prompt_tokens: number
      completion_tokens: number
    }
  }
  suggested_questions?: string[]
  workflow_nodes?: WorkflowNode[]
  workflow_running?: boolean
  files?: MessageFile[]
}

interface Conversation {
  id: string
  name: string
  created_at: number
  updated_at: number
}

export default function DifyChatPage() {
  // 会话相关状态
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [conversationMessages, setConversationMessages] = useState<{[key: string]: Message[]}>({})
  
  // 聊天相关状态
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([])
  
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const userId = 'user-123' // 固定用户ID

  // 获取当前会话的消息 - 智能查找消息，处理状态切换过程中的消息显示
  const currentMessages = useMemo(() => {
    // 优先查找当前会话ID的消息
    if (currentConversationId && conversationMessages[currentConversationId]) {
      const messages = conversationMessages[currentConversationId]
      console.log('使用当前会话消息:', { currentConversationId, messageCount: messages.length })
      return messages
    }
    
    // 如果当前会话没有消息，查找temp消息
    if (conversationMessages['temp'] && conversationMessages['temp'].length > 0) {
      const messages = conversationMessages['temp']
      console.log('使用临时消息:', { messageCount: messages.length })
      return messages
    }
    
    // 如果没有找到任何消息，尝试查找最新的消息（用于状态切换期间）
    const allKeys = Object.keys(conversationMessages)
    if (allKeys.length > 0) {
      // 排除temp，找到最新的会话
      const nonTempKeys = allKeys.filter(key => key !== 'temp')
      if (nonTempKeys.length > 0) {
        const latestKey = nonTempKeys[nonTempKeys.length - 1]
        const messages = conversationMessages[latestKey] || []
        console.log('使用最新会话消息作为备选:', { latestKey, messageCount: messages.length })
        return messages
      }
    }
    
    console.log('没有找到任何消息')
    return []
  }, [currentConversationId, conversationMessages])

  // 自动滚动到底部
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [currentMessages])

  // 页面加载时获取会话列表
  useEffect(() => {
    loadConversations()
  }, [])

  // 聚焦输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [currentConversationId])

  // 加载会话列表
  const loadConversations = async () => {
    try {
      const response = await fetch(`/api/dify-chat?user=${userId}`)
      if (response.ok) {
        const data = await response.json()
        setConversations(data.data || [])
        
        // 如果没有当前会话且有会话列表，选择第一个
        if (!currentConversationId && data.data && data.data.length > 0) {
          setCurrentConversationId(data.data[0].id)
          loadConversationMessages(data.data[0].id)
        }
      }
    } catch (error) {
      console.error('加载会话列表失败:', error)
    }
  }

  // 去重文件列表
  const deduplicateFiles = (files: MessageFile[]): MessageFile[] => {
    const seen = new Set<string>()
    return files.filter(file => {
      // 使用文件名和大小作为唯一标识
      const key = `${file.filename}_${file.size}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  // 从文本中移除文件下载链接
  const removeFileLinksFromText = (content: string): string => {
    // 移除Markdown格式的文件链接：[文件名.ext](/files/...)
    return content.replace(/\[([^\]]+\.(docx?|xlsx?|pptx?|pdf|txt|zip|rar))\]\([^)]+\)/gi, '')
      .replace(/文件下载：\s*/g, '') // 移除"文件下载："前缀
      .replace(/\n\s*\n/g, '\n') // 清理多余的空行
      .trim()
  }

  // 加载指定会话的消息
  const loadConversationMessages = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/dify-chat/messages?conversation_id=${conversationId}&user=${userId}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        console.log('获取到的历史消息:', data)
        
        const messages: Message[] = []
        
        if (data.data && Array.isArray(data.data)) {
          data.data.forEach((item: any) => {
            // 添加用户消息
            if (item.query) {
              messages.push({
                id: `${item.id}_user`,
                role: 'user',
                content: item.query,
                timestamp: new Date(item.created_at * 1000),
                conversation_id: conversationId,
                message_id: item.id
              })
            }
            
            // 添加助手消息
            if (item.answer) {
              // 清理历史消息中的文件链接
              const cleanedAnswer = removeFileLinksFromText(item.answer)
              
              // 处理历史消息中的文件
              let messageFiles: MessageFile[] = []
              if (item.message_files && item.message_files.length > 0) {
                messageFiles = item.message_files
              }
              
              messages.push({
                id: `${item.id}_assistant`,
                role: 'assistant',
                content: cleanedAnswer,
                timestamp: new Date(item.created_at * 1000),
                conversation_id: conversationId,
                message_id: item.id,
                metadata: {
                  retriever_resources: item.retriever_resources || [],
                  usage: item.usage
                },
                files: deduplicateFiles(messageFiles)
              })
            }
          })
        }
        
        // 按时间排序
        messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
        
        setConversationMessages(prev => ({
          ...prev,
          [conversationId]: messages
        }))
      } else {
        console.error('获取历史消息失败:', response.status)
      }
    } catch (error) {
      console.error('加载会话消息失败:', error)
    }
  }

  // 获取建议问题
  const loadSuggestedQuestions = async (messageId: string) => {
    try {
      const response = await fetch(`/api/dify-chat/suggested?message_id=${messageId}&user=${userId}`)
      if (response.ok) {
        const data = await response.json()
        setSuggestedQuestions(data.data || [])
      }
    } catch (error) {
      console.error('获取建议问题失败:', error)
    }
  }

  // 创建新会话
  const createNewConversation = () => {
    setCurrentConversationId(null)
    setError(null)
    setSuggestedQuestions([])
    
    // 清理临时消息
    setConversationMessages(prev => {
      const newMessages = { ...prev }
      delete newMessages['temp']
      return newMessages
    })
    
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  // 选择会话
  const selectConversation = (conversationId: string) => {
    console.log('选择会话:', conversationId)
    setCurrentConversationId(conversationId)
    
    // 检查是否已经加载过这个会话的消息
    if (!conversationMessages[conversationId]) {
      console.log('加载会话历史消息:', conversationId)
      loadConversationMessages(conversationId)
    } else {
      console.log('会话消息已缓存，直接显示')
    }
    
    setError(null)
    setSuggestedQuestions([])
  }

  // 删除会话
  const deleteConversation = async (conversationId: string) => {
    if (!confirm('确定要删除这个会话吗？')) return
    
    try {
      const response = await fetch('/api/dify-chat', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ conversation_id: conversationId, user: userId })
      })
      
      if (response.ok) {
        setConversations(prev => prev.filter(conv => conv.id !== conversationId))
        setConversationMessages(prev => {
          const newMessages = { ...prev }
          delete newMessages[conversationId]
          return newMessages
        })
        
        if (currentConversationId === conversationId) {
          const remainingConversations = conversations.filter(conv => conv.id !== conversationId)
          if (remainingConversations.length > 0) {
            setCurrentConversationId(remainingConversations[0].id)
          } else {
            setCurrentConversationId(null)
          }
        }
      }
    } catch (error) {
      console.error('删除会话失败:', error)
      setError('删除会话失败')
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取文件图标
  const getFileIcon = (mimeType: string, extension?: string) => {
    if (mimeType.includes('image/')) return '🖼️'
    if (mimeType.includes('pdf')) return '📄'
    if (mimeType.includes('word') || extension === '.docx' || extension === '.doc') return '📝'
    if (mimeType.includes('excel') || extension === '.xlsx' || extension === '.xls') return '📊'
    if (mimeType.includes('powerpoint') || extension === '.pptx' || extension === '.ppt') return '📋'
    if (mimeType.includes('text/')) return '📄'
    return '📁'
  }

  // 处理文件下载
  const handleFileDownload = async (file: MessageFile) => {
    try {
      // 构建完整的下载URL
      const downloadUrl = file.url.startsWith('http') 
        ? file.url 
        : `http://111.229.163.150${file.url}`
      
      console.log('下载文件:', file.filename, downloadUrl)
      
      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = file.filename
      link.target = '_blank'
      
      // 添加到DOM并点击
      document.body.appendChild(link)
      link.click()
      
      // 清理
      document.body.removeChild(link)
    } catch (error) {
      console.error('下载文件失败:', error)
      setError('文件下载失败，请稍后重试')
    }
  }

  // 渲染文件列表
  const renderMessageFiles = (files: MessageFile[]) => {
    if (!files || files.length === 0) return null
    
    // 再次确保去重
    const uniqueFiles = deduplicateFiles(files)
    
    return (
      <div className="mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
        <div className="flex items-center gap-2 mb-2">
          <File size={16} className="text-purple-600" />
          <span className="text-sm font-medium text-purple-800">
            附件文件 {uniqueFiles.length > 1 && `(${uniqueFiles.length}个)`}
          </span>
        </div>
        <div className="space-y-2">
          {uniqueFiles.map((file, index) => (
            <div key={`${file.filename}_${file.size}_${index}`} className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-100 hover:bg-purple-50 transition-colors">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <span className="text-2xl">{getFileIcon(file.mime_type, file.extension)}</span>
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-800 truncate">
                    {file.filename}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center gap-2">
                    <span>{formatFileSize(file.size)}</span>
                    <span>•</span>
                    <span className="truncate">{file.mime_type}</span>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-100 hover:border-purple-300 transition-colors"
                onClick={() => handleFileDownload(file)}
              >
                <Download size={16} />
                下载
              </Button>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 修改发送消息方法，处理文件信息
  const handleSendMessage = async (messageText?: string) => {
    const queryText = messageText || input.trim()
    if (!queryText) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: queryText,
      timestamp: new Date(),
      conversation_id: currentConversationId || undefined
    }

    // 添加用户消息到当前会话
    let conversationId = currentConversationId || 'temp'
    setConversationMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), userMessage]
    }))

    setInput('')
    setIsLoading(true)
    setError(null)
    setSuggestedQuestions([])

    // 创建助手消息占位符
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      conversation_id: currentConversationId || undefined,
      workflow_nodes: [],
      workflow_running: false,
      files: []
    }
    
    console.log('创建助手消息占位符 - ID:', assistantMessageId, '会话ID:', conversationId)
    
    setConversationMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), assistantMessage]
    }))

    try {
      const requestBody = {
        query: queryText,
        user: userId,
        ...(currentConversationId && { conversation_id: currentConversationId })
      }
      
      const response = await fetch('/api/dify-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      })
      
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      
      if (reader) {
        let buffer = ''
        let accumulatedAnswer = ''
        let newConversationId = currentConversationId
        let finalMessageId = ''
        let messageMetadata: any = null
        let messageFiles: MessageFile[] = []
        let workflowNodes: WorkflowNode[] = []
        let workflowRunning = false
        let isNewConversation = !currentConversationId
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk
          
          // 处理完整的SSE事件
          const events = buffer.split('\n\n')
          buffer = events.pop() || ''
          
          for (const event of events) {
            if (!event.trim() || !event.startsWith('data:')) continue
            
            try {
              const jsonStr = event.substring(5).trim()
              const data = JSON.parse(jsonStr)
              
              console.log("收到Dify数据:", data)
              
                            // 获取会话ID（用于新会话）
              if (data.conversation_id && !newConversationId) {
                newConversationId = data.conversation_id
                
                // 将临时消息复制到新会话ID下，但不立即切换界面显示
                if (conversationId === 'temp' && newConversationId) {
                  console.log('复制临时消息到新会话:', newConversationId)
                  
                  // 立即更新conversationId变量以确保后续消息更新正确
                  conversationId = newConversationId
                  
                  setConversationMessages(prev => {
                    const tempMessages = prev['temp'] || []
                    console.log('复制临时消息详情:', tempMessages.map(msg => ({
                      id: msg.id,
                      role: msg.role,
                      content: msg.content.substring(0, 50),
                      workflow_nodes: msg.workflow_nodes?.length || 0,
                      workflow_running: msg.workflow_running
                    })))
                    
                    const newMessages = { ...prev }
                    // 复制到新会话ID下，但保留temp消息让界面继续显示
                    if (newConversationId) {
                      newMessages[newConversationId] = tempMessages.map(msg => ({
                        ...msg,
                        conversation_id: newConversationId || undefined
                      }))
                    }
                    return newMessages
                  })
                  
                  // 延迟切换界面显示的会话ID，确保消息已经完全复制
                  setTimeout(() => {
                    console.log('延迟切换界面显示到新会话ID:', newConversationId)
                    setCurrentConversationId(newConversationId)
                    
                    // 再延迟清理temp消息
                    setTimeout(() => {
                      setConversationMessages(prev => {
                        const newMessages = { ...prev }
                        delete newMessages['temp']
                        return newMessages
                      })
                    }, 50)
                  }, 50) // 短暂延迟，让消息复制完成
                }
                
                // 如果是新会话，需要重新加载会话列表
                if (isNewConversation) {
                  console.log('新会话创建，重新加载会话列表')
                  setTimeout(() => {
                    loadConversations()
                  }, 1000)
                }
              }
              
              // 获取消息ID
              if (data.message_id) {
                finalMessageId = data.message_id
              }
              
              // 处理工作流开始事件
              if (data.event === 'workflow_started') {
                workflowRunning = true
                workflowNodes = []
                
                const targetConvId = newConversationId || conversationId
                console.log('工作流开始 - 会话ID:', targetConvId, '助手消息ID:', assistantMessageId)
                
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的工作流状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_running: true,
                            workflow_nodes: []
                          }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的工作流状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_running: true,
                            workflow_nodes: []
                          }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理节点开始事件
              if (data.event === 'node_started') {
                const nodeData = data.data
                const newNode: WorkflowNode = {
                  id: nodeData.id,
                  node_id: nodeData.node_id,
                  node_type: nodeData.node_type,
                  title: nodeData.title,
                  index: nodeData.index,
                  status: 'running',
                  created_at: nodeData.created_at
                }
                
                // 更新现有节点为等待状态，新节点为运行状态
                workflowNodes = workflowNodes.map(node => ({
                  ...node,
                  status: node.status === 'running' ? 'completed' : node.status
                }))
                workflowNodes.push(newNode)
                
                const targetConvId = newConversationId || conversationId
                console.log('节点开始 - 会话ID:', targetConvId, '节点:', newNode.title, '总节点数:', workflowNodes.length)
                
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的节点状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, workflow_nodes: [...workflowNodes] }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的节点状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, workflow_nodes: [...workflowNodes] }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理节点完成事件
              if (data.event === 'node_finished') {
                const nodeData = data.data
                
                // 只从outputs.files中获取文件信息，避免重复
                if (nodeData.outputs && nodeData.outputs.files && nodeData.outputs.files.length > 0) {
                  const newFiles = nodeData.outputs.files.filter((file: any) => 
                    !messageFiles.some(existing => 
                      existing.filename === file.filename && existing.size === file.size
                    )
                  )
                  messageFiles = [...messageFiles, ...newFiles]
                  console.log('节点完成，发现新文件:', newFiles)
                }
                
                workflowNodes = workflowNodes.map(node => 
                  node.id === nodeData.id 
                    ? {
                        ...node,
                        status: nodeData.status === 'succeeded' ? 'completed' : 'failed',
                        elapsed_time: nodeData.elapsed_time,
                        finished_at: nodeData.finished_at,
                        tokens: nodeData.execution_metadata?.total_tokens
                      }
                    : node
                )
                
                const targetConvId = newConversationId || conversationId
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的节点和文件状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_nodes: [...workflowNodes],
                            files: [...messageFiles]
                          }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的节点和文件状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_nodes: [...workflowNodes],
                            files: [...messageFiles]
                          }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理增量消息内容
              if (data.event === 'message' && data.answer !== undefined) {
                accumulatedAnswer += data.answer
                
                // 确保使用正确的会话ID - 优先使用新获取的会话ID
                const currentConvId = newConversationId || conversationId
                console.log('更新消息内容:', {
                  targetConvId: currentConvId,
                  contentLength: accumulatedAnswer.length,
                  newConversationId,
                  originalConversationId: conversationId,
                  assistantMessageId
                })
                
                // 实时更新助手消息 - 同时更新temp和新会话（如果存在）确保界面显示正确
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话ID的消息
                  if (prev[currentConvId]) {
                    newMessages[currentConvId] = prev[currentConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedAnswer }
                        : msg
                    )
                  }
                  
                  // 如果有新会话ID且不同于原始会话ID，也同时更新temp消息（为了界面显示）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedAnswer }
                        : msg
                    )
                  }
                  
                  console.log('消息更新结果:', {
                    targetConvId: currentConvId,
                    updatedTemp: newConversationId && conversationId === 'temp',
                    tempMessageCount: newMessages['temp']?.length,
                    targetMessageCount: newMessages[currentConvId]?.length
                  })
                  
                  return newMessages
                })
              }
              
              // 处理消息结束事件
              if (data.event === 'message_end') {
                console.log("消息结束，最终答案:", accumulatedAnswer)
                messageMetadata = data.metadata
                workflowRunning = false
                
                // 清理文本内容，移除文件下载链接
                const cleanedContent = removeFileLinksFromText(accumulatedAnswer)
                
                // 去重文件列表
                const uniqueFiles = deduplicateFiles(messageFiles)
                
                console.log('最终文件列表（去重后）:', uniqueFiles)
                
                // 更新最终消息
                const targetConvId = newConversationId || conversationId
                setConversationMessages(prev => ({
                  ...prev,
                  [targetConvId]: prev[targetConvId]?.map(msg => 
                    msg.id === assistantMessageId 
                      ? { 
                          ...msg, 
                          content: cleanedContent, // 使用清理后的内容
                          metadata: messageMetadata,
                          message_id: finalMessageId,
                          workflow_running: false,
                          workflow_nodes: workflowNodes.map(node => ({
                            ...node,
                            status: node.status === 'running' ? 'completed' : node.status
                          })),
                          files: uniqueFiles // 使用去重后的文件列表
                        }
                      : msg
                  ) || []
                }))
                
                // 获取建议问题
                if (finalMessageId) {
                  setTimeout(() => {
                    loadSuggestedQuestions(finalMessageId)
                  }, 1000)
                }
                
                // 如果是新会话，在消息结束后重新加载历史消息以确保完整性
                if (isNewConversation && newConversationId) {
                  console.log('新会话消息结束，重新加载历史消息确保数据完整性')
                  setTimeout(() => {
                    if (newConversationId) {
                      loadConversationMessages(newConversationId)
                    }
                  }, 2000) // 给Dify更多时间保存消息到后端
                }
              }
              
            } catch (e) {
              console.error("处理SSE事件失败:", e, event)
            }
          }
        }
      }
      
    } catch (err) {
      console.error('发送消息失败:', err)
      setError('消息发送失败，请稍后重试')
      
      // 更新错误消息
      setConversationMessages(prev => ({
        ...prev,
        [conversationId]: prev[conversationId]?.map(msg => 
          msg.id === assistantMessageId 
            ? { ...msg, content: '抱歉，服务暂时不可用，请稍后再试。' }
            : msg
        ) || []
      }))
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 渲染工作流进度
  const renderWorkflowProgress = (nodes: WorkflowNode[], isRunning: boolean) => {
    if (!nodes || nodes.length === 0) return null
    
    const getNodeIcon = (nodeType: string, status: string) => {
      if (status === 'running') return <Clock size={16} className="text-blue-500 animate-spin" />
      if (status === 'completed') return <CheckCircle size={16} className="text-green-500" />
      if (status === 'failed') return <ExternalLink size={16} className="text-red-500" />
      return <Play size={16} className="text-gray-400" />
    }
    
    const getNodeTitle = (nodeType: string, title: string) => {
      const typeMap: {[key: string]: string} = {
        'start': 'START',
        'knowledge-retrieval': 'KNOWLEDGE RETRIEVAL',
        'llm': 'LLM',
        'answer': 'ANSWER'
      }
      return typeMap[nodeType] || title
    }
    
    const formatElapsedTime = (elapsedTime?: number) => {
      if (!elapsedTime) return ''
      if (elapsedTime < 1) return `${Math.round(elapsedTime * 1000)} ms`
      return `${elapsedTime.toFixed(3)} s`
    }
    
    return (
      <div className="mt-3 p-3 bg-green-50 rounded-lg border border-green-200">
        <div className="flex items-center gap-2 mb-3">
          <Zap size={16} className="text-green-600" />
          <span className="text-sm font-medium text-green-800">工作流</span>
          {isRunning && <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-800">运行中</Badge>}
        </div>
        
        <div className="space-y-2">
          {nodes.map((node, index) => (
            <div key={node.id} className="flex items-center justify-between p-2 bg-white rounded border border-green-100">
              <div className="flex items-center gap-2">
                {getNodeIcon(node.node_type, node.status)}
                <span className="text-sm font-medium text-gray-800">
                  {getNodeTitle(node.node_type, node.title)}
                </span>
              </div>
              <div className="flex items-center gap-2">
                {node.tokens && (
                  <span className="text-xs text-gray-600">
                    {node.tokens.toLocaleString()} tokens
                  </span>
                )}
                {node.elapsed_time && (
                  <span className="text-xs text-gray-600">
                    {formatElapsedTime(node.elapsed_time)}
                  </span>
                )}
                {node.status === 'completed' && (
                  <CheckCircle size={14} className="text-green-500" />
                )}
              </div>
            </div>
          ))}
        </div>
        
        {/* 进度条 */}
        <div className="mt-3">
          <Progress 
            value={(nodes.filter(n => n.status === 'completed').length / nodes.length) * 100} 
            className="h-2"
          />
          <div className="text-xs text-gray-600 mt-1">
            {nodes.filter(n => n.status === 'completed').length} / {nodes.length} 节点完成
          </div>
        </div>
      </div>
    )
  }

  // 渲染引用文档
  const renderRetrieverResources = (resources: any[]) => {
    if (!resources || resources.length === 0) return null
    
    return (
      <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center gap-2 mb-2">
          <FileText size={16} className="text-blue-600" />
          <span className="text-sm font-medium text-blue-800">引用文档</span>
        </div>
        <div className="space-y-2">
          {resources.map((resource, index) => (
            <div key={index} className="p-2 bg-white rounded border border-blue-100">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm font-medium text-gray-800">{resource.document_name}</span>
                <Badge variant="secondary" className="text-xs">
                  相似度: {(resource.score * 100).toFixed(1)}%
                </Badge>
              </div>
              <div className="text-xs text-gray-600 mb-1">
                数据集: {resource.dataset_name} | 命中次数: {resource.hit_count} | 字数: {resource.word_count}
              </div>
              <div className="text-xs text-gray-700 bg-gray-50 p-2 rounded">
                {resource.content.substring(0, 200)}...
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-[calc(100vh-120px)] bg-gradient-to-br from-gray-50 to-blue-50">
      {/* 左侧会话列表 */}
      <div className="w-80 border-r border-blue-100 bg-white shadow-sm">
        <div className="p-4 border-b border-blue-100">
          <Button 
            onClick={createNewConversation}
            className="w-full bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
          >
            <Plus size={16} />
            新建会话
          </Button>
        </div>
        
        <ScrollArea className="flex-1">
          <div className="p-2">
            {conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`p-3 mb-2 rounded-lg cursor-pointer transition-colors group ${
                  currentConversationId === conversation.id
                    ? 'bg-blue-50 border border-blue-200'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => selectConversation(conversation.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <MessageSquare size={16} className="text-blue-500 flex-shrink-0" />
                    <span className="text-sm font-medium truncate">
                      {conversation.name || '新会话'}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto"
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteConversation(conversation.id)
                    }}
                  >
                    <Trash2 size={14} className="text-red-500" />
                  </Button>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {new Date(conversation.updated_at * 1000).toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>

      {/* 右侧聊天区域 */}
      <div className="flex-1 flex flex-col">
        <Card className="flex-1 flex flex-col shadow-lg border-blue-100 rounded-none">
        <CardHeader className="border-b border-blue-100 bg-white">
          <CardTitle className="text-xl text-blue-800 flex items-center gap-2">
            <Sparkles size={20} className="text-blue-500" />
            智能问答助手
              {currentConversationId && (
                <span className="text-sm text-gray-500 font-normal">
                  - {conversations.find(c => c.id === currentConversationId)?.name || '当前会话'}
                </span>
              )}
          </CardTitle>
        </CardHeader>
          
        <CardContent className="flex-1 p-0 flex flex-col">
          <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
              {currentMessages.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-center p-6">
                <div className="mb-4">
                  <div className="w-[180px] h-[180px] flex items-center justify-center bg-blue-50 rounded-full p-6 shadow-inner">
                    <Headset size={100} className="text-blue-400" />
                  </div>
                </div>
                  <h3 className="text-2xl font-medium mb-2 text-blue-800">Dify智能助手</h3>
                <p className="text-gray-600 max-w-md leading-relaxed">
                  您可以向我询问任何问题，我会尽力为您解答。无论是业务咨询、数据分析还是日常问题，我都能帮到您。
                </p>
                {error && (
                  <div className="mt-4 text-red-500 p-3 bg-red-50 rounded-lg">
                    {error}
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                  {currentMessages.map((message) => (
                    <div 
                      key={message.id} 
                      className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div 
                        className={`max-w-[80%] rounded-lg p-4 shadow-sm ${
                          message.role === 'user' 
                            ? 'bg-blue-500 text-white rounded-tr-none' 
                            : 'bg-white border border-blue-100 text-gray-800 rounded-tl-none'
                        }`}
                      >
                        {message.role === 'assistant' && (
                          <div className="flex items-center mb-2">
                            <Headset size={16} className="mr-2 text-blue-500" />
                            <span className="font-medium text-blue-700">Dify助手</span>
                          </div>
                        )}
                        
                        {/* 显示工作流进度 */}
                        {message.role === 'assistant' && message.workflow_nodes && message.workflow_nodes.length > 0 && (
                          (() => {
                            console.log('渲染工作流 - 消息ID:', message.id, '节点数:', message.workflow_nodes.length, '运行状态:', message.workflow_running)
                            return renderWorkflowProgress(message.workflow_nodes, message.workflow_running || false)
                          })()
                        )}
                        

                        
                        <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                        
                        {/* 显示文件下载 */}
                        {message.role === 'assistant' && message.files && message.files.length > 0 && 
                          renderMessageFiles(message.files)
                        }
                        
                        {/* 显示引用文档 */}
                        {message.role === 'assistant' && message.metadata?.retriever_resources && 
                          renderRetrieverResources(message.metadata.retriever_resources)
                        }
                        
                        {/* 显示使用统计 */}
                        {message.role === 'assistant' && message.metadata?.usage && (
                          <div className="mt-2 text-xs text-gray-500">
                            Token使用: {message.metadata.usage.total_tokens} 
                            (输入: {message.metadata.usage.prompt_tokens}, 输出: {message.metadata.usage.completion_tokens})
                          </div>
                        )}
                        
                        <div className={`text-xs mt-2 ${message.role === 'user' ? 'text-blue-200' : 'text-gray-500'}`}>
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {/* 建议问题 */}
                  {suggestedQuestions.length > 0 && (
                    <div className="flex justify-start">
                      <div className="max-w-[80%] bg-gray-50 border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Sparkles size={16} className="text-blue-500" />
                          <span className="text-sm font-medium text-gray-700">建议问题</span>
                        </div>
                        <div className="space-y-2">
                          {suggestedQuestions.map((question, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              className="w-full text-left justify-start h-auto p-2 text-sm"
                              onClick={() => handleSendMessage(question)}
                            >
                              {question}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {isLoading && currentMessages.length > 0 && currentMessages[currentMessages.length-1].role === 'assistant' && !currentMessages[currentMessages.length-1].content && (
                  <div className="flex justify-start">
                    <div className="bg-white border border-blue-100 text-gray-800 rounded-lg rounded-tl-none max-w-[80%] p-4 shadow-sm">
                      <div className="flex items-center mb-2">
                        <Headset size={16} className="mr-2 text-blue-500" />
                          <span className="font-medium text-blue-700"></span>
                      </div>
                      <div className="flex space-x-2">
                        <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce"></div>
                        <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce delay-100"></div>
                        <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce delay-200"></div>
                      </div>
                    </div>
                  </div>
                )}
                {error && (
                  <div className="text-center p-3 mt-2">
                    <p className="text-red-500 text-sm bg-red-50 p-2 rounded-lg inline-block">{error}</p>
                  </div>
                )}
              </div>
            )}
          </ScrollArea>
          
            <div className="p-4 border-t border-blue-100 bg-white">
            <div className="flex gap-2">
              <Input
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="发送消息..."
                className="flex-1 border-blue-200 focus-visible:ring-blue-400 shadow-sm"
                disabled={isLoading}
              />
              <Button 
                  onClick={() => handleSendMessage()} 
                disabled={input.trim() === '' || isLoading}
                className={`bg-blue-500 hover:bg-blue-600 text-white transition-colors ${isLoading ? "opacity-70" : ""}`}
              >
                {isLoading ? (
                  <div className="h-5 w-5 border-2 border-t-transparent border-white rounded-full animate-spin"></div>
                ) : (
                  <Send size={18} />
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      </div>
    </div>
  )
}