unit CkConfig;

interface

uses
  Classes;

type
  TCkConfig = class
  private
    FCkid: integer;
    FCkType: string;
    FCkName: string;
    FCkSubType: string;
  public
    property Ckid: integer read FCkid write FCkid;
    property CkType: string read FCkType write FCkType;
    property CkName: string read FCkName write FCkName;
    property CkSubType: string read FCkSubType write FCkSubType;
  end;

implementation

end.
