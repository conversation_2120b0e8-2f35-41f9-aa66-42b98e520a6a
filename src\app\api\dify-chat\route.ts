import { NextRequest, NextResponse } from 'next/server';

// Dify配置 - 四种模式
const DIFY_BASE_URL = "http://***************/v1";

// API密钥配置
const API_KEYS = {
  // 普通模式（非联网搜索 + 非深度思考）
  normal: "app-plE9pju9RGzpYzEXqT97242P",
  // 深度思考模式（非联网搜索 + 深度思考）
  deepThinking: "app-teLdLwYK7xoNmXEvUHixOsie",
  // 联网搜索模式（联网搜索 + 非深度思考）
  webSearch: "app-1yIeqWsQFmsTAWrHcGp1doz4",
  // 联网搜索+深度思考模式（联网搜索 + 深度思考）
  webSearchDeepThinking: "app-4vMJ5BjKg9RgqyHeboCJfIp5"
};

// 获取API密钥的函数
function getApiKey(webSearch: boolean, deepThinking: boolean): string {
  if (webSearch && deepThinking) {
    return API_KEYS.webSearchDeepThinking;
  } else if (webSearch && !deepThinking) {
    return API_KEYS.webSearch;
  } else if (!webSearch && deepThinking) {
    return API_KEYS.deepThinking;
  } else {
    return API_KEYS.normal;
  }
}

// 获取模式描述的函数
function getModeDescription(webSearch: boolean, deepThinking: boolean): string {
  if (webSearch && deepThinking) {
    return '联网搜索+深度思考模式';
  } else if (webSearch && !deepThinking) {
    return '联网搜索模式';
  } else if (!webSearch && deepThinking) {
    return '深度思考模式';
  } else {
    return '普通模式';
  }
}

export async function POST(request: NextRequest) {
  console.log("接收到Dify聊天API请求");

  try {
    const { query, conversation_id, user = "user-123", deep_thinking = false, web_search = false } = await request.json();
    console.log("收到的查询:", query, "会话ID:", conversation_id, "深度思考:", deep_thinking, "联网搜索:", web_search);

    // 根据深度思考和联网搜索状态选择API密钥
    const apiKey = getApiKey(web_search, deep_thinking);
    const modeDescription = getModeDescription(web_search, deep_thinking);
    console.log(`使用API密钥: ${modeDescription}`);

    // 调用Dify Chat Messages API
    const chatUrl = `${DIFY_BASE_URL}/chat-messages`;
    console.log(`调用Dify API: ${chatUrl}`);

    const requestBody = {
      inputs: {},
      query: query,
      response_mode: "streaming",
      user: user,
      auto_generate_name: true,
      ...(conversation_id && { conversation_id })
    };

    const apiResponse = await fetch(chatUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!apiResponse.ok) {
      const errorText = await apiResponse.text();
      console.error(`Dify API响应错误: ${apiResponse.status}`, errorText);

      // 如果是会话不存在的错误，尝试不带会话ID重新请求
      if (apiResponse.status === 404 && conversation_id && errorText.includes("Conversation Not Exists")) {
        console.log("会话不存在，尝试创建新会话");
        const newRequestBody = {
          inputs: {},
          query: query,
          response_mode: "streaming",
          user: user,
          auto_generate_name: true
          // 不包含 conversation_id，让系统创建新会话
        };

        const retryResponse = await fetch(chatUrl, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(newRequestBody)
        });

        if (!retryResponse.ok) {
          const retryErrorText = await retryResponse.text();
          console.error(`重试API响应错误: ${retryResponse.status}`, retryErrorText);
          throw new Error(`重试API响应错误: ${retryResponse.status}`);
        }

        console.log("成功创建新会话，返回响应");
        // 使用重试的响应
        return handleApiResponse(retryResponse);
      }

      throw new Error(`API响应错误: ${apiResponse.status}`);
    }

    return handleApiResponse(apiResponse);
  } catch (error) {
    console.error('Dify聊天API错误:', error);
    return NextResponse.json(
      { error: '服务器处理请求时出错', success: false },
      { status: 500 }
    );
  }
}

// 处理API响应的通用函数
async function handleApiResponse(apiResponse: Response) {

    // 创建转换流来处理Dify的响应
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    
    const processStream = async () => {
      if (!apiResponse.body) {
        writer.close();
        return;
      }
      
      const reader = apiResponse.body.getReader();
      const decoder = new TextDecoder();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          //console.log("收到Dify原始数据块:", chunk);
          
          // 直接转发Dify的SSE格式数据，保持原有格式
          // 这样前端可以正确解析每个增量事件
          writer.write(new TextEncoder().encode(chunk));
        }
      } catch (e) {
        console.error("流处理错误:", e);
      } finally {
        writer.close();
      }
    };
    
    processStream();
    
    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
}

// 获取会话列表
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const user = searchParams.get('user') || 'user-123';
  
  try {
    const conversationsUrl = `${DIFY_BASE_URL}/conversations?user=${user}&limit=50`;
    
    const response = await fetch(conversationsUrl, {
      headers: {
        'Authorization': `Bearer ${API_KEYS.normal}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`获取会话列表失败: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('获取会话列表错误:', error);
    return NextResponse.json(
      { error: '获取会话列表失败', success: false },
      { status: 500 }
    );
  }
}

// 删除会话
export async function DELETE(request: NextRequest) {
  try {
    const { conversation_id, user = "user-123" } = await request.json();
    
    const deleteUrl = `${DIFY_BASE_URL}/conversations/${conversation_id}`;
    
    const response = await fetch(deleteUrl, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${API_KEYS.normal}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ user })
    });

    if (!response.ok) {
      throw new Error(`删除会话失败: ${response.status}`);
    }

    return NextResponse.json({ success: true });
    
  } catch (error) {
    console.error('删除会话错误:', error);
    return NextResponse.json(
      { error: '删除会话失败', success: false },
      { status: 500 }
    );
  }
}