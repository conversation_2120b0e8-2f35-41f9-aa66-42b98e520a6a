<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原政策关系管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style type="text/tailwindcss">
        @layer utilities {
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800">原政策关系管理</h1>
            <div class="flex space-x-2">
                <button id="createRelationBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center shadow-md transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                    </svg>
                    创建关系
                </button>
                <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md flex items-center shadow-sm hover:bg-gray-50 transition duration-300">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                    导出数据
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:space-x-6">
            <!-- 左侧内容区域 -->
            <div class="lg:w-3/4 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">新政策名称</label>
                            <div class="relative">
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10" placeholder="请输入新政策名称">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">旧政策名称</label>
                            <div class="relative">
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10" placeholder="请输入旧政策名称">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 absolute left-3 top-2.5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关系类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="revision">修订</option>
                                <option value="replacement">替代</option>
                                <option value="partial">部分修改</option>
                                <option value="supplement">补充</option>
                                <option value="extension">延伸</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效状态</label>
                            <div class="flex items-center space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">已生效</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">未生效</span>
                                </label>
                            </div>
                        </div>
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">创建日期</label>
                            <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-2 space-y-2 sm:space-y-0">
                                <div class="w-full sm:w-5/12">
                                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <span class="text-gray-500 text-center">至</span>
                                <div class="w-full sm:w-5/12">
                                    <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6 space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>

                <!-- 关系列表区 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6 pb-0">
                        <h2 class="text-lg font-semibold text-gray-800 mb-4">政策关系列表</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">新政策名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">旧政策名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关系类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效时间</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <!-- 关系项 1 -->
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRelation('rel1')">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">创新型企业认定管理办法（2023版）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">创新型企业认定管理办法（2018版）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">修订</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRelation('rel1'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRelation('rel1'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">编辑</button>
                                            <button onclick="deleteRelation('rel1'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- 关系项 2 -->
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRelation('rel2')">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技型中小企业评价办法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技型中小企业认定管理办法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">替代</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李华</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-12-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRelation('rel2'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRelation('rel2'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">编辑</button>
                                            <button onclick="deleteRelation('rel2'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- 关系项 3 -->
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRelation('rel3')">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">高新技术企业认定管理办法实施细则（2023）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新技术企业认定管理办法实施细则（2016）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">部分修改</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王强</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRelation('rel3'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRelation('rel3'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">编辑</button>
                                            <button onclick="deleteRelation('rel3'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- 关系项 4 -->
                                <tr class="hover:bg-gray-50 cursor-pointer" onclick="selectRelation('rel4')">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">企业研发费用税前加计扣除政策指引（2023）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">企业研发费用税前加计扣除政策指引（2021）</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">补充</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵敏</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <div class="flex space-x-2">
                                            <button onclick="viewRelation('rel4'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editRelation('rel4'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">编辑</button>
                                            <button onclick="deleteRelation('rel4'); event.stopPropagation();" class="text-blue-600 hover:text-blue-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!-- 分页器 -->
                    <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                上一页
                            </a>
                            <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                下一页
                            </a>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">
                                    显示第 <span class="font-medium">1</span> 至 <span class="font-medium">4</span> 条，共 <span class="font-medium">12</span> 条
                                </p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">上一页</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                    <a href="#" aria-current="page" class="z-10 bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        1
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        2
                                    </a>
                                    <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                        3
                                    </a>
                                    <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        <span class="sr-only">下一页</span>
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 版本对比区 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6 border-b border-gray-200 flex justify-between items-center cursor-pointer" onclick="toggleVersionCompare()">
                        <h2 class="text-lg font-semibold text-gray-800">版本对比</h2>
                        <svg id="versionCompareIcon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div id="versionCompareContent" class="p-6 hidden">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h3 class="text-md font-medium text-gray-800 mb-3">新政策：创新型企业认定管理办法（2023版）</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">发布机构</p>
                                        <p class="text-sm text-gray-800">科技部、财政部、税务总局</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">文号</p>
                                        <p class="text-sm text-gray-800">国科发创〔2023〕32号</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">发布日期</p>
                                        <p class="text-sm text-gray-800">2023-05-15</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">实施日期</p>
                                        <p class="text-sm text-gray-800">2023-06-15</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">政策级别</p>
                                        <p class="text-sm text-gray-800">部门规章</p>
                                    </div>
                                </div>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <h3 class="text-md font-medium text-gray-800 mb-3">旧政策：创新型企业认定管理办法（2018版）</h3>
                                <div class="space-y-4">
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">发布机构</p>
                                        <p class="text-sm text-gray-800">科技部、财政部</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">文号</p>
                                        <p class="text-sm text-gray-800">国科发创〔2018〕89号</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">发布日期</p>
                                        <p class="text-sm text-gray-800">2018-04-10</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">实施日期</p>
                                        <p class="text-sm text-gray-800">2018-05-01</p>
                                    </div>
                                    <div>
                                        <p class="text-sm font-medium text-gray-600">政策级别</p>
                                        <p class="text-sm text-gray-800">部门规章</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-6">
                            <h3 class="text-md font-medium text-gray-800 mb-3">主要变更点</h3>
                            <div class="border border-gray-200 rounded-lg overflow-hidden">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更项</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">旧版内容</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">新版内容</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">认定条件</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">研发投入占比不低于3%</td>
                                            <td class="px-6 py-4 text-sm text-gray-500 bg-green-50">研发投入占比不低于5%</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">知识产权要求</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">拥有自主知识产权</td>
                                            <td class="px-6 py-4 text-sm text-gray-500 bg-green-50">拥有核心自主知识产权且在技术上处于国内领先水平</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">认定流程</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">省级初审、部级复审</td>
                                            <td class="px-6 py-4 text-sm text-gray-500 bg-green-50">省级初审、部级复审、专家评审</td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">有效期</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">3年</td>
                                            <td class="px-6 py-4 text-sm text-gray-500">3年（不变）</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="mt-6">
                            <h3 class="text-md font-medium text-gray-800 mb-3">修订摘要</h3>
                            <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-700">
                                <p>本次修订主要针对创新型企业认定条件进行了调整，提高了研发投入比例要求，强化了知识产权质量要求，并完善了认定流程。修订后的办法更加注重企业创新能力的实质性评价，有利于提高认定质量，促进企业加大研发投入，提升自主创新能力。同时，新版办法还明确了税务部门作为联合发文单位，为后续创新型企业税收优惠政策的落实提供了制度基础。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="lg:w-1/4 space-y-6">
                <!-- 影响分析区 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="p-6 border-b border-gray-200 flex justify-between items-center cursor-pointer" onclick="toggleImpactAnalysis()">
                        <h2 class="text-lg font-semibold text-gray-800">影响分析</h2>
                        <svg id="impactAnalysisIcon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform duration-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div id="impactAnalysisContent" class="p-6 hidden">
                        <div class="space-y-4">
                            <div>
                                <h3 class="text-md font-medium text-gray-800 mb-2">引用旧政策的业务模块</h3>
                                <ul class="space-y-2">
                                    <li class="flex justify-between items-center">
                                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">企业认定申请</a>
                                        <span class="text-sm text-gray-500">128条数据</span>
                                    </li>
                                    <li class="flex justify-between items-center">
                                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">项目申报管理</a>
                                        <span class="text-sm text-gray-500">56条数据</span>
                                    </li>
                                    <li class="flex justify-between items-center">
                                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">税收优惠审核</a>
                                        <span class="text-sm text-gray-500">89条数据</span>
                                    </li>
                                    <li class="flex justify-between items-center">
                                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">统计分析报表</a>
                                        <span class="text-sm text-gray-500">12条数据</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="pt-4 border-t border-gray-200">
                                <h3 class="text-md font-medium text-gray-800 mb-2">影响评估</h3>
                                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm text-yellow-700">
                                                该政策变更将影响285条业务数据，建议在关系生效前完成数据迁移和业务规则调整。
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="pt-4 border-t border-gray-200">
                                <h3 class="text-md font-medium text-gray-800 mb-2">变更趋势</h3>
                                <div class="h-48 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <img src="https://source.unsplash.com/random/400x200?chart" alt="变更趋势图" class="w-full h-full object-cover rounded-lg">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关系统计卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">关系统计</h2>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">修订关系</span>
                            <span class="text-sm font-medium text-gray-800">45</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">替代关系</span>
                            <span class="text-sm font-medium text-gray-800">28</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-green-500 h-2.5 rounded-full" style="width: 28%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">部分修改</span>
                            <span class="text-sm font-medium text-gray-800">18</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 18%"></div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">补充关系</span>
                            <span class="text-sm font-medium text-gray-800">9</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-purple-500 h-2.5 rounded-full" style="width: 9%"></div>
                        </div>
                    </div>
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">总计</span>
                            <span class="text-sm font-medium text-gray-800">100</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关系编辑弹窗 -->
    <div id="relationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800" id="modalTitle">创建政策关系</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <form id="relationForm">
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">新政策</label>
                            <div class="relative">
                                <input type="text" id="newPolicy" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入或选择新政策">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">旧政策</label>
                            <div class="relative">
                                <input type="text" id="oldPolicy" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入或选择旧政策">
                                <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关系类型</label>
                            <div class="grid grid-cols-2 gap-4">
                                <label class="inline-flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="relationType" value="revision" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">修订</span>
                                </label>
                                <label class="inline-flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="relationType" value="replacement" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">替代</span>
                                </label>
                                <label class="inline-flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="relationType" value="partial" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">部分修改</span>
                                </label>
                                <label class="inline-flex items-center p-3 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50">
                                    <input type="radio" name="relationType" value="supplement" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">补充</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效时间</label>
                            <input type="date" id="effectiveDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关系描述</label>
                            <textarea id="relationDescription" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请描述政策关系的主要内容和变更点"></textarea>
                        </div>
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4" id="validationMessage" style="display: none;">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700" id="validationText">
                                        已存在相同的政策关系，请检查输入。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 pt-4 border-t border-gray-200 flex justify-end space-x-3">
                        <button type="button" onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="button" onclick="saveRelation()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteConfirmModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
            <div class="p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">确认删除</h3>
                <p class="text-sm text-gray-600 mb-6">您确定要删除这条政策关系吗？此操作无法撤销，可能会影响引用该关系的业务数据。</p>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeDeleteConfirmModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="button" onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentRelationId = null;
        let deleteRelationId = null;

        // 打开创建关系弹窗
        document.getElementById('createRelationBtn').addEventListener('click', function() {
            document.getElementById('modalTitle').textContent = '创建政策关系';
            document.getElementById('relationForm').reset();
            document.getElementById('validationMessage').style.display = 'none';
            document.getElementById('relationModal').classList.remove('hidden');
        });

        // 关闭弹窗
        function closeModal() {
            document.getElementById('relationModal').classList.add('hidden');
        }

        // 保存关系
        function saveRelation() {
            // 模拟表单验证
            const newPolicy = document.getElementById('newPolicy').value;
            const oldPolicy = document.getElementById('oldPolicy').value;
            
            if (!newPolicy || !oldPolicy) {
                document.getElementById('validationText').textContent = '新政策和旧政策不能为空';
                document.getElementById('validationMessage').style.display = 'block';
                return;
            }
            
            // 模拟保存成功
            closeModal();
            alert('政策关系保存成功！');
            // 实际应用中这里会刷新列表或添加新行
        }

        // 查看关系
        function viewRelation(relationId) {
            // 模拟查看操作，实际应用中可能会打开详情页或弹窗
            currentRelationId = relationId;
            toggleVersionCompare(true);
            toggleImpactAnalysis(true);
        }

        // 编辑关系
        function editRelation(relationId) {
            currentRelationId = relationId;
            document.getElementById('modalTitle').textContent = '编辑政策关系';
            
            // 模拟填充表单数据
            if (relationId === 'rel1') {
                document.getElementById('newPolicy').value = '创新型企业认定管理办法（2023版）';
                document.getElementById('oldPolicy').value = '创新型企业认定管理办法（2018版）';
                document.querySelector('input[name="relationType"][value="revision"]').checked = true;
                document.getElementById('effectiveDate').value = '2023-06-15';
                document.getElementById('relationDescription').value = '本次修订主要针对创新型企业认定条件进行了调整，提高了研发投入比例要求，强化了知识产权质量要求，并完善了认定流程。';
            }
            
            document.getElementById('validationMessage').style.display = 'none';
            document.getElementById('relationModal').classList.remove('hidden');
        }

        // 删除关系
        function deleteRelation(relationId) {
            deleteRelationId = relationId;
            document.getElementById('deleteConfirmModal').classList.remove('hidden');
        }

        // 关闭删除确认弹窗
        function closeDeleteConfirmModal() {
            document.getElementById('deleteConfirmModal').classList.add('hidden');
        }

        // 确认删除
        function confirmDelete() {
            // 模拟删除操作
            closeDeleteConfirmModal();
            alert('政策关系已删除！');
            // 实际应用中这里会从列表中移除该行
        }

        // 选择关系行
        function selectRelation(relationId) {
            // 移除所有行的选中状态
            document.querySelectorAll('tbody tr').forEach(tr => {
                tr.classList.remove('bg-blue-50');
            });
            
            // 添加选中状态
            const selectedRow = document.querySelector(`tr[onclick="selectRelation('${relationId}')"]`);
            if (selectedRow) {
                selectedRow.classList.add('bg-blue-50');
            }
            
            currentRelationId = relationId;
            toggleVersionCompare(true);
            toggleImpactAnalysis(true);
        }

        // 切换版本对比区域显示/隐藏
        function toggleVersionCompare(forceShow = false) {
            const content = document.getElementById('versionCompareContent');
            const icon = document.getElementById('versionCompareIcon');
            
            if (forceShow) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
                return;
            }
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }

        // 切换影响分析区域显示/隐藏
        function toggleImpactAnalysis(forceShow = false) {
            const content = document.getElementById('impactAnalysisContent');
            const icon = document.getElementById('impactAnalysisIcon');
            
            if (forceShow) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
                return;
            }
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }
    </script>
</body>
</html>