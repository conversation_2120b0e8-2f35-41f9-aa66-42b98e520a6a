// 创建通用的LLM API调用函数

export async function callLLMApi(prompt: string, onLog?: (entry: any) => void) {
  // 从环境变量获取API密钥，或使用配置的密钥
  const apiKey = process.env.ARK_API_KEY || "1c304c18-bff4-4ec2-b899-394901c8a17d";
  
  const requestBody = {
    "model": "ep-20250714222426-wkhkq",
    "messages": [
      {"role": "system", "content": "你是人工智能助手."},
      {"role": "user", "content": prompt}
    ]
  };
  
  // 记录请求
  if (onLog) {
    onLog({
      timestamp: new Date().toISOString(),
      type: 'request',
      data: {
        endpoint: "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        body: requestBody
      }
    });
  }
  
  try {
    const response = await fetch("https://ark.cn-beijing.volces.com/api/v3/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer 1c304c18-bff4-4ec2-b899-394901c8a17d`
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`LLM API请求失败: ${response.status} ${errorText}`);
    }
    
    const data = await response.json();
    
    // 记录响应
    if (onLog) {
      onLog({
        timestamp: new Date().toISOString(),
        type: 'response',
        data: data
      });
    }
    
    return data.choices[0].message.content;
    
  } catch (error) {
    console.error("调用LLM API出错:", error);
    
    // 记录错误
    if (onLog) {
      onLog({
        timestamp: new Date().toISOString(),
        type: 'response',
        data: { error: error instanceof Error ? error.message : '未知错误' }
      });
    }
    
    throw new Error("调用AI服务失败，请检查API配置");
  }
} 