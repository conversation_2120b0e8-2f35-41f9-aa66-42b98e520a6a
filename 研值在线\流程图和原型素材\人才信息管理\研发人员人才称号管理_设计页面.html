<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员人才称号管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">研发人员人才称号管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-3">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="personName" class="block text-sm font-medium text-gray-700 mb-1">人才姓名</label>
                            <input type="text" id="personName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入人才姓名">
                        </div>
                        <div>
                            <label for="titleType" class="block text-sm font-medium text-gray-700 mb-1">称号类型</label>
                            <select id="titleType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="academic">学术称号</option>
                                <option value="technical">技术称号</option>
                                <option value="management">管理称号</option>
                                <option value="honorary">荣誉称号</option>
                            </select>
                        </div>
                        <div>
                            <label for="titleLevel" class="block text-sm font-medium text-gray-700 mb-1">称号级别</label>
                            <select id="titleLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">认定时间</label>
                            <div class="flex space-x-2">
                                <input type="date" id="startDate" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500">至</span>
                                <input type="date" id="endDate" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 flex justify-end space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col">
                    <!-- 人才称号列表区 -->
                    <div class="w-full">
                        <!-- 列表头部 -->
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-800">人才称号列表</h2>
                            <div class="flex space-x-3">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                        </svg>
                                        导出
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openModal('addTitleModal')" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    新增称号
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">人才姓名</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">称号名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">称号类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">称号级别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">张明</div>
                                            <div class="text-xs text-gray-500">高级工程师</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市创新科技有限公司</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省杰出青年</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">学术称号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                有效
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editTitleModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">李华</div>
                                            <div class="text-xs text-gray-500">研究员</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家千人计划专家</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">学术称号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-11-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                有效
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editTitleModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">王芳</div>
                                            <div class="text-xs text-gray-500">教授</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波工程学院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市领军人才</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术称号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02-10</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                有效
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editTitleModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">赵强</div>
                                            <div class="text-xs text-gray-500">高级经济师</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波经济技术开发区</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省优秀企业家</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">管理称号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-09-05</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                即将到期
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editTitleModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">陈静</div>
                                            <div class="text-xs text-gray-500">工程师</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波高新区科创中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">高新区青年科技奖</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">荣誉称号</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">区级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-06-18</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                已过期
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editTitleModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页 -->
                        <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">246</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50" disabled>上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">4</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">5</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="w-full lg:w-1/4 lg:min-w-[300px] space-y-4">
                <!-- 统计信息卡片 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">统计信息</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <span class="text-sm text-gray-600">总称号数</span>
                            <span class="text-2xl font-bold text-blue-600">246</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                            <span class="text-sm text-gray-600">有效称号</span>
                            <span class="text-2xl font-bold text-green-600">203</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                            <span class="text-sm text-gray-600">即将到期</span>
                            <span class="text-2xl font-bold text-yellow-600">18</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                            <span class="text-sm text-gray-600">已过期</span>
                            <span class="text-2xl font-bold text-red-600">25</span>
                        </div>
                    </div>
                </div>

                <!-- 称号级别分布 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">称号级别分布</h3>
                    <div class="h-60">
                        <canvas id="titleLevelChart"></canvas>
                    </div>
                    <div class="space-y-2 mt-4">
                        <div class="flex justify-between text-sm">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                <span>国家级</span>
                            </div>
                            <span>12%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span>省级</span>
                            </div>
                            <span>28%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                <span>市级</span>
                            </div>
                            <span>40%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
                                <span>区级</span>
                            </div>
                            <span>20%</span>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <button onclick="toggleBatchImport()" class="w-full flex items-center justify-between text-left">
                            <h3 class="text-lg font-medium text-gray-800">批量导入</h3>
                            <svg id="batch-toggle-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>
                    </div>
                    <div id="batch-import-content" class="p-4 space-y-4 hidden">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                下载导入模板
                            </a>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div class="hidden" id="upload-progress">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">上传进度</span>
                                <span class="text-gray-600">75%</span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增称号弹窗 -->
    <div id="addTitleModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增人才称号</h3>
                    <button onclick="closeModal('addTitleModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="personNameAdd" class="block text-sm font-medium text-gray-700 mb-1">人才姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="personNameAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入人才姓名">
                        </div>
                        <div>
                            <label for="affiliationAdd" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="affiliationAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入所属单位">
                        </div>
                        <div>
                            <label for="titleNameAdd" class="block text-sm font-medium text-gray-700 mb-1">称号名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="titleNameAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入称号名称">
                        </div>
                        <div>
                            <label for="titleTypeAdd" class="block text-sm font-medium text-gray-700 mb-1">称号类型 <span class="text-red-500">*</span></label>
                            <select id="titleTypeAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择称号类型</option>
                                <option value="academic">学术称号</option>
                                <option value="technical">技术称号</option>
                                <option value="management">管理称号</option>
                                <option value="honorary">荣誉称号</option>
                            </select>
                        </div>
                        <div>
                            <label for="titleLevelAdd" class="block text-sm font-medium text-gray-700 mb-1">称号级别 <span class="text-red-500">*</span></label>
                            <select id="titleLevelAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择称号级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label for="certificationDateAdd" class="block text-sm font-medium text-gray-700 mb-1">认定时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="certificationDateAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="expiryDateAdd" class="block text-sm font-medium text-gray-700 mb-1">有效期至</label>
                            <input type="date" id="expiryDateAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="certifyingAuthorityAdd" class="block text-sm font-medium text-gray-700 mb-1">认定机构</label>
                            <input type="text" id="certifyingAuthorityAdd" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入认定机构">
                        </div>
                    </div>
                    <div>
                        <label for="policyBasisAdd" class="block text-sm font-medium text-gray-700 mb-1">政策依据</label>
                        <textarea id="policyBasisAdd" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入政策依据"></textarea>
                    </div>
                    <div>
                        <label for="descriptionAdd" class="block text-sm font-medium text-gray-700 mb-1">备注说明</label>
                        <textarea id="descriptionAdd" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入备注说明"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('addTitleModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑称号弹窗 -->
    <div id="editTitleModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑人才称号</h3>
                    <button onclick="closeModal('editTitleModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="personNameEdit" class="block text-sm font-medium text-gray-700 mb-1">人才姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="personNameEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张明" disabled>
                        </div>
                        <div>
                            <label for="affiliationEdit" class="block text-sm font-medium text-gray-700 mb-1">所属单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="affiliationEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市创新科技有限公司" disabled>
                        </div>
                        <div>
                            <label for="titleNameEdit" class="block text-sm font-medium text-gray-700 mb-1">称号名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="titleNameEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="浙江省杰出青年">
                        </div>
                        <div>
                            <label for="titleTypeEdit" class="block text-sm font-medium text-gray-700 mb-1">称号类型 <span class="text-red-500">*</span></label>
                            <select id="titleTypeEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择称号类型</option>
                                <option value="academic" selected>学术称号</option>
                                <option value="technical">技术称号</option>
                                <option value="management">管理称号</option>
                                <option value="honorary">荣誉称号</option>
                            </select>
                        </div>
                        <div>
                            <label for="titleLevelEdit" class="block text-sm font-medium text-gray-700 mb-1">称号级别 <span class="text-red-500">*</span></label>
                            <select id="titleLevelEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择称号级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial" selected>省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label for="certificationDateEdit" class="block text-sm font-medium text-gray-700 mb-1">认定时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="certificationDateEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2023-05-15">
                        </div>
                        <div>
                            <label for="expiryDateEdit" class="block text-sm font-medium text-gray-700 mb-1">有效期至</label>
                            <input type="date" id="expiryDateEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2028-05-14">
                        </div>
                        <div>
                            <label for="certifyingAuthorityEdit" class="block text-sm font-medium text-gray-700 mb-1">认定机构</label>
                            <input type="text" id="certifyingAuthorityEdit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="浙江省科技厅">
                        </div>
                    </div>
                    <div>
                        <label for="policyBasisEdit" class="block text-sm font-medium text-gray-700 mb-1">政策依据</label>
                        <textarea id="policyBasisEdit" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">《浙江省杰出青年科学基金项目管理办法》</textarea>
                    </div>
                    <div>
                        <label for="descriptionEdit" class="block text-sm font-medium text-gray-700 mb-1">备注说明</label>
                        <textarea id="descriptionEdit" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">张明在新材料领域取得突破性进展，符合浙江省杰出青年评选标准。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">当前状态</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="active" selected>有效</option>
                            <option value="expiring">即将到期</option>
                            <option value="expired">已过期</option>
                        </select>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editTitleModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white max-h-[80vh] overflow-y-auto">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-medium text-gray-900">人才称号详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="border-b pb-4 mb-6">
                    <h4 class="text-base font-semibold text-gray-900 mb-3">基本信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">
                        <div>
                            <span class="text-sm text-gray-500">人才姓名：</span>
                            <span class="text-sm font-medium text-gray-900">张明</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">所属单位：</span>
                            <span class="text-sm font-medium text-gray-900">宁波市创新科技有限公司</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">职称：</span>
                            <span class="text-sm font-medium text-gray-900">高级工程师</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">联系电话：</span>
                            <span class="text-sm font-medium text-gray-900">0574-88887777</span>
                        </div>
                    </div>
                </div>
                
                <div class="border-b pb-4 mb-6">
                    <h4 class="text-base font-semibold text-gray-900 mb-3">称号信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">
                        <div>
                            <span class="text-sm text-gray-500">称号名称：</span>
                            <span class="text-sm font-medium text-gray-900">浙江省杰出青年</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">称号类型：</span>
                            <span class="text-sm font-medium text-gray-900">学术称号</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">称号级别：</span>
                            <span class="text-sm font-medium text-gray-900">省级</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">当前状态：</span>
                            <span class="text-sm font-medium text-green-600">有效</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">认定时间：</span>
                            <span class="text-sm font-medium text-gray-900">2023-05-15</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">有效期至：</span>
                            <span class="text-sm font-medium text-gray-900">2028-05-14</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">认定机构：</span>
                            <span class="text-sm font-medium text-gray-900">浙江省科技厅</span>
                        </div>
                        <div>
                            <span class="text-sm text-gray-500">证书编号：</span>
                            <span class="text-sm font-medium text-gray-900">ZJST-2023-JQ-056</span>
                        </div>
                    </div>
                </div>
                
                <div class="border-b pb-4 mb-6">
                    <h4 class="text-base font-semibold text-gray-900 mb-3">详细说明</h4>
                    <div class="space-y-3">
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-1">政策依据</h5>
                            <p class="text-sm text-gray-600">《浙江省杰出青年科学基金项目管理办法》（浙科计〔2022〕12号）</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-1">认定理由</h5>
                            <p class="text-sm text-gray-600">张明在新材料领域取得突破性进展，主持多项省级以上科研项目，发表高水平学术论文15篇，申请发明专利8项，其中授权5项，符合浙江省杰出青年评选标准。</p>
                        </div>
                        <div>
                            <h5 class="text-sm font-medium text-gray-700 mb-1">评审流程</h5>
                            <ul class="text-sm text-gray-600 list-disc list-inside space-y-1">
                                <li>2023年2月：单位推荐</li>
                                <li>2023年3月：形式审查通过</li>
                                <li>2023年4月：专家评审通过</li>
                                <li>2023年5月：公示无异议</li>
                                <li>2023年5月15日：正式认定</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="border-b pb-4 mb-6">
                    <h4 class="text-base font-semibold text-gray-900 mb-3">相关附件</h4>
                    <div class="space-y-2">
                        <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">浙江省杰出青年证书.pdf</p>
                                <p class="text-xs text-gray-500">2.4MB · 2023-05-15上传</p>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">下载</a>
                        </div>
                        <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900">推荐表及附件材料.docx</p>
                                <p class="text-xs text-gray-500">1.8MB · 2023-02-10上传</p>
                            </div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm">下载</a>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 批量导入折叠功能
        function toggleBatchImport() {
            const content = document.getElementById('batch-import-content');
            const icon = document.getElementById('batch-toggle-icon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.style.transform = 'rotate(0deg)';
            } else {
                content.classList.add('hidden');
                icon.style.transform = 'rotate(-90deg)';
            }
        }

        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }

        // 删除确认
        function confirmDelete() {
            if (confirm('确定要删除这条人才称号记录吗？此操作不可恢复！')) {
                alert('删除成功！');
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化称号级别分布图表
            const titleLevelCtx = document.getElementById('titleLevelChart').getContext('2d');
            if (titleLevelCtx) {
                new Chart(titleLevelCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['国家级', '省级', '市级', '区级'],
                        datasets: [{
                            data: [12, 28, 40, 20],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(139, 92, 246, 0.8)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // 文件上传处理
            document.getElementById('file-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    document.getElementById('upload-progress').classList.remove('hidden');
                    // 模拟上传进度
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 10;
                        document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                        document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                        if (progress >= 100) {
                            clearInterval(interval);
                            setTimeout(() => {
                                document.getElementById('upload-progress').classList.add('hidden');
                                alert('文件上传成功！');
                            }, 500);
                        }
                    }, 200);
                }
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
            
            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', function(e) {
                // 导出下拉菜单
                const exportDropdown = document.getElementById('exportDropdown');
                const exportOptions = document.getElementById('exportOptions');
                if (exportDropdown && exportOptions && !exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                    exportOptions.classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>