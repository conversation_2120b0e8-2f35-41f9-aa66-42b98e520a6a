<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策信息维护管理流程</text>

  <!-- 阶段一：政策录入与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：政策录入与校验</text>
  
  <!-- 节点1: 用户录入 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户录入政策</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(新增/修改)</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(必填字段、文号唯一性)</text>
  </g>

  <!-- 节点3: 生成待审核记录 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成政策记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(状态：待审核)</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 350 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核发布 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核发布</text>

  <!-- 节点4: 推送管理员 -->
  <g transform="translate(300, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送管理员</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(按预设规则)</text>
  </g>

  <!-- 节点5: 审核通过 -->
  <g transform="translate(600, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(状态：已发布)</text>
  </g>

  <!-- 节点6: 同步索引 -->
  <g transform="translate(900, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">同步任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(写入检索索引)</text>
  </g>

  <!-- 连接线 3 -> 4 -> 5 -> 6 -->
  <path d="M 850 200 C 850 250, 400 260, 400 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 345 Q 550 345 600 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 345 Q 850 345 900 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：批量导入处理 -->
  <text x="350" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：批量导入处理</text>

  <!-- 节点7: 文件上传 -->
  <g transform="translate(50, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件上传</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(批量导入)</text>
  </g>

  <!-- 节点8: 逐行校验 -->
  <g transform="translate(280, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统解析</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(逐行校验内容)</text>
  </g>

  <!-- 节点9: 成功处理 -->
  <g transform="translate(510, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">校验通过</text>
    <text x="90" y="45" text-anchor="middle" font-size="11" fill="#555">(生成待审核记录)</text>
  </g>

  <!-- 节点10: 失败处理 -->
  <g transform="translate(510, 570)" filter="url(#soft-shadow)">
    <rect width="180" height="60" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="25" text-anchor="middle" font-size="14" font-weight="600" fill="#333">校验失败</text>
    <text x="90" y="45" text-anchor="middle" font-size="11" fill="#555">(返回错误报告)</text>
  </g>

  <!-- 连接线 7 -> 8 -->
  <path d="M 230 545 Q 255 545 280 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9, 10 -->
  <path d="M 460 530 C 485 520, 485 490, 510 480" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 460 560 C 485 570, 485 590, 510 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 标签 -->
  <text x="475" y="505" text-anchor="middle" font-size="11" fill="#555">通过</text>
  <text x="475" y="585" text-anchor="middle" font-size="11" fill="#555">失败</text>

  <!-- 阶段四：失效提醒管理 -->
  <text x="1050" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：失效提醒管理</text>

  <!-- 节点11: 定时扫描 -->
  <g transform="translate(850, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFF3E0" stroke="#FFCC80" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时任务</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(每日扫描政策库)</text>
  </g>

  <!-- 节点12: 生成提醒 -->
  <g transform="translate(1080, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFF3E0" stroke="#FFCC80" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">失效提醒</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(推送管理员)</text>
  </g>

  <!-- 连接线 11 -> 12 -->
  <path d="M 1030 545 Q 1055 545 1080 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：导出与审计 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：导出与审计</text>

  <!-- 节点13: 导出请求 -->
  <g transform="translate(300, 750)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出请求</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(用户触发)</text>
  </g>

  <!-- 节点14: 数据封装 -->
  <g transform="translate(530, 750)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(封装文件、记录日志)</text>
  </g>

  <!-- 节点15: 审计记录 -->
  <g transform="translate(760, 750)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(合规追踪)</text>
  </g>

  <!-- 连接线 13 -> 14 -> 15 -->
  <path d="M 480 785 Q 505 785 530 785" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 710 785 Q 735 785 760 785" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <path d="M 510 630 C 400 680, 200 700, 140 580" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="300" y="650" text-anchor="middle" font-size="11" fill="#666">修正后重新导入</text>

</svg>