object CC_FlCkCgshMainFrame: TCC_FlCkCgshMainFrame
  Left = 0
  Top = 0
  Width = 1144
  Height = 760
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object MainPanel: TRzPanel
    Left = 0
    Top = 60
    Width = 1144
    Height = 700
    Align = alClient
    BorderOuter = fsNone
    BorderColor = clWhite
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    object RightPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1144
      Height = 700
      Align = alClient
      BorderOuter = fsNone
      TabOrder = 0
      object RzPageControl1: TRzPageControl
        Left = 0
        Top = 0
        Width = 1144
        Height = 700
        Hint = ''
        ActivePage = TabSheet1
        Align = alClient
        UseColoredTabs = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        FlatColor = clActiveBorder
        ParentFont = False
        ShowCardFrame = False
        TabOrder = 0
        FixedDimension = 23
        object TabSheet2: TRzTabSheet
          Color = 16051944
          TabVisible = False
          Caption = 'TabSheet2'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          object RzPanel1: TRzPanel
            Left = 0
            Top = 0
            Width = 1144
            Height = 700
            Align = alClient
            BorderOuter = fsNone
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            Transparent = True
          end
        end
        object TabSheet3: TRzTabSheet
          TabVisible = False
          Caption = 'TabSheet3'
          object RzPanel3: TRzPanel
            Left = 0
            Top = 0
            Width = 1144
            Height = 700
            Align = alClient
            BorderOuter = fsNone
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            Transparent = True
          end
        end
        object TabSheet1: TRzTabSheet
          Color = 16051944
          TabVisible = False
          Caption = 'TabSheet1'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          object RzPanel2: TRzPanel
            Left = 0
            Top = 0
            Width = 1144
            Height = 700
            Align = alClient
            BorderOuter = fsNone
            Font.Charset = DEFAULT_CHARSET
            Font.Color = clWindowText
            Font.Height = -12
            Font.Name = #24494#36719#38597#40657
            Font.Style = []
            ParentFont = False
            TabOrder = 0
            Transparent = True
          end
        end
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 1144
    Height = 60
    Align = alTop
    BorderOuter = fsNone
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    GradientColorStyle = gcsCustom
    ParentFont = False
    TabOrder = 1
    object TopHeadPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1144
      Height = 60
      Align = alTop
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object TopBluePanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1144
        Height = 15
        Align = alTop
        BorderOuter = fsNone
        BorderSides = [sdLeft, sdTop, sdRight]
        BorderColor = 14671839
        BorderWidth = 1
        Color = 16049103
        DoubleBuffered = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        GradientColorStyle = gcsCustom
        GradientColorStart = 16643306
        GradientColorStop = 16049103
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 0
        VisualStyle = vsGradient
      end
      object TopButtonPanel: TRzPanel
        Left = 0
        Top = 15
        Width = 1144
        Height = 45
        Align = alTop
        BorderOuter = fsNone
        BorderColor = 14671839
        BorderWidth = 1
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        object Btn_Type1: TAdvGlowButton
          Left = 55
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#36741#26009' '#37319#36141
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 0
          OnClick = Btn_Type1Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
      end
    end
  end
end
