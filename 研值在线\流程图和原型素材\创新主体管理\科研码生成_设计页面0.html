<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研码生成管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研码生成管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                    <input type="text" id="searchInput" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="输入主体名称或科研码">
                </div>
                <div>
                    <label for="subjectType" class="block text-sm font-medium text-gray-700 mb-1">主体类型</label>
                    <select id="subjectType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="01">企业</option>
                        <option value="02">高校</option>
                        <option value="03">科研院所</option>
                        <option value="04">三甲医院</option>
                        <option value="05">创新载体</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">已生成</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">已失效</button>
                        <button class="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">待审核</button>
                    </div>
                </div>
                <div>
                    <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1">生成日期</label>
                    <div class="flex space-x-2">
                        <input type="date" id="startDate" class="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500 text-xs">至</span>
                        <input type="date" id="endDate" class="flex-1 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center space-x-2">
                <button onclick="openGenerateDrawer()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    生成科研码
                </button>
                <div class="relative">
                    <button onclick="toggleBatchMenu()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        批量操作
                    </button>
                    <div id="batchMenu" class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                        <div class="py-1">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">下载模板</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">批量导入</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">批量导出</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-sm text-gray-600">
                共 <span class="font-medium text-gray-900">128</span> 条记录
            </div>
        </div>

        <!-- 科研码列表 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科研码</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主体名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主体类型</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生成时间</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">公开范围</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波市科技研究院</div>
                            <div class="text-sm text-gray-500">91330104M...</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科研院所</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已生成</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基本信息+成果</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="showPreview('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                            <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                            <button onclick="showSharePanel('1')" class="text-green-600 hover:text-green-900">分享</button>
                            <button class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波大学</div>
                            <div class="text-sm text-gray-500">91330104M...</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">高校</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 14:20</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已生成</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">全部信息</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="showPreview('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                            <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                            <button onclick="showSharePanel('2')" class="text-green-600 hover:text-green-900">分享</button>
                            <button class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波市第一医院</div>
                            <div class="text-sm text-gray-500">91330104M...</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">三甲医院</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 09:15</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基本信息</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="showPreview('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                            <button class="text-indigo-600 hover:text-indigo-900" disabled>下载</button>
                            <button class="text-green-600 hover:text-green-900" disabled>分享</button>
                            <button class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 生成科研码抽屉 -->
    <div id="generateDrawer" class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform transition-transform translate-x-full z-50">
        <div class="p-6 h-full flex flex-col">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">生成科研码</h3>
                <button onclick="closeGenerateDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow overflow-y-auto">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">选择主体</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">请选择创新主体</option>
                            <option value="1">宁波市科技研究院</option>
                            <option value="2">宁波大学</option>
                            <option value="3">宁波市第一医院</option>
                            <option value="4">宁波市高新技术企业A</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">有效期</label>
                        <div class="flex items-center space-x-2">
                            <input type="date" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="text-gray-500">至</span>
                            <input type="date" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">公开范围</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">基本信息</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">科技人才</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">科研项目</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">科研成果</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">仪器设备</span>
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                </div>
            </div>
            <div class="pt-4 border-t border-gray-200">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    确认生成
                </button>
            </div>
        </div>
    </div>

    <!-- 预览侧边栏 -->
    <div id="previewPanel" class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform transition-transform translate-x-full z-50">
        <div class="p-6 h-full flex flex-col">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">科研码预览</h3>
                <button onclick="hidePreview()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow overflow-y-auto">
                <div class="space-y-6">
                    <div class="flex justify-center">
                        <div class="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">主体信息</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex">
                                <span class="text-gray-500 w-24">名称：</span>
                                <span class="text-gray-900">宁波市科技研究院</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">类型：</span>
                                <span class="text-gray-900">科研院所</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">信用代码：</span>
                                <span class="text-gray-900">91330104M...</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">生成时间：</span>
                                <span class="text-gray-900">2024-01-15 10:30</span>
                            </div>
                            <div class="flex">
                                <span class="text-gray-500 w-24">有效期：</span>
                                <span class="text-gray-900">2024-01-15 至 2025-01-15</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">公开信息</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700">基本信息</span>
                                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">已公开</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700">科技人才</span>
                                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">已公开</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700">科研项目</span>
                                <span class="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded-full">未公开</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700">科研成果</span>
                                <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">已公开</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-700">仪器设备</span>
                                <span class="text-xs px-2 py-1 bg-gray-100 text-gray-800 rounded-full">未公开</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded border-gray-300">
                            <span class="ml-2 text-sm text-gray-700">模拟公众视角</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="pt-4 border-t border-gray-200">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    下载高清码图
                </button>
            </div>
        </div>
    </div>

    <!-- 分享侧边栏 -->
    <div id="sharePanel" class="fixed inset-y-0 right-0 w-96 bg-white shadow-xl transform transition-transform translate-x-full z-50">
        <div class="p-6 h-full flex flex-col">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">分享科研码</h3>
                <button onclick="hideSharePanel()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow overflow-y-auto">
                <div class="space-y-6">
                    <div class="flex justify-center">
                        <div class="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">分享链接</h4>
                        <div class="flex">
                            <input type="text" value="https://scicode.nb.gov.cn/share/123456" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm" readonly>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 text-sm">
                                复制
                            </button>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">分享到</h4>
                        <div class="flex space-x-4">
                            <button class="p-3 bg-blue-100 rounded-full text-blue-600 hover:bg-blue-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 2h-3a5 5 0 00-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 011-1h3z" />
                                </svg>
                            </button>
                            <button class="p-3 bg-green-100 rounded-full text-green-600 hover:bg-green-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                </svg>
                            </button>
                            <button class="p-3 bg-red-100 rounded-full text-red-600 hover:bg-red-200">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-3">访问统计</h4>
                        <div class="grid grid-cols-3 gap-4 text-center">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">156</div>
                                <div class="text-xs text-gray-500">今日访问</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">892</div>
                                <div class="text-xs text-gray-500">本周访问</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">3,245</div>
                                <div class="text-xs text-gray-500">总访问量</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 批量操作菜单
        function toggleBatchMenu() {
            document.getElementById('batchMenu').classList.toggle('hidden');
        }

        // 生成科研码抽屉
        function openGenerateDrawer() {
            document.getElementById('generateDrawer').classList.remove('translate-x-full');
        }

        function closeGenerateDrawer() {
            document.getElementById('generateDrawer').classList.add('translate-x-full');
        }

        // 预览面板
        function showPreview(id) {
            document.getElementById('previewPanel').classList.remove('translate-x-full');
        }

        function hidePreview() {
            document.getElementById('previewPanel').classList.add('translate-x-full');
        }

        // 分享面板
        function showSharePanel(id) {
            document.getElementById('sharePanel').classList.remove('translate-x-full');
        }

        function hideSharePanel() {
            document.getElementById('sharePanel').classList.add('translate-x-full');
        }

        // 点击外部关闭
        document.addEventListener('click', function(e) {
            // 批量操作菜单
            const batchButton = document.querySelector('[onclick="toggleBatchMenu()"]');
            const batchMenu = document.getElementById('batchMenu');
            if (!batchButton.contains(e.target) && !batchMenu.contains(e.target) && !batchMenu.classList.contains('hidden')) {
                batchMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>