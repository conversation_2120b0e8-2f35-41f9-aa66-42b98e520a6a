<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>参保详情 - 企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="min-h-screen p-6">
        <!-- 导航栏 -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center text-blue-600 hover:text-blue-800 mr-4">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-1"></i>
                        返回企业概览
                    </a>
                    <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                        <i data-lucide="shield-check" class="mr-3 h-6 w-6 text-blue-600"></i>
                        参保详情
                    </h1>
                </div>
                <div class="flex gap-2">
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="calendar" class="w-4 h-4"></i>
                        时间筛选
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        导出报告
                    </button>
                </div>
            </div>
            <p class="text-gray-600 mt-2">宁波创新科技股份有限公司 - 社会保险参保分析</p>
        </div>

        <!-- 参保概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">当前参保人数</h3>
                    <i data-lucide="users" class="w-5 h-5 text-blue-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">326</p>
                <p class="text-sm text-green-600 mt-1">本月 +15人</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">缴费基数</h3>
                    <i data-lucide="calculator" class="w-5 h-5 text-green-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">8,500</p>
                <p class="text-sm text-blue-600 mt-1">元/月 平均</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">月缴费总额</h3>
                    <i data-lucide="credit-card" class="w-5 h-5 text-purple-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">89.5万</p>
                <p class="text-sm text-purple-600 mt-1">含个人+企业</p>
            </div>
            
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <div class="flex items-center justify-between mb-2">
                    <h3 class="text-sm font-medium text-gray-500">参保率</h3>
                    <i data-lucide="percent" class="w-5 h-5 text-orange-500"></i>
                </div>
                <p class="text-2xl font-bold text-gray-900">100%</p>
                <p class="text-sm text-orange-600 mt-1">五险全覆盖</p>
            </div>
        </div>

        <!-- 参保趋势分析 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- 参保人数趋势 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="trending-up" class="mr-2 h-5 w-5 text-blue-600"></i>
                    参保人数趋势
                </h2>
                <div class="h-64">
                    <canvas id="participantTrendChart"></canvas>
                </div>
            </div>

            <!-- 缴费基数分布 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="bar-chart-3" class="mr-2 h-5 w-5 text-blue-600"></i>
                    缴费基数分布
                </h2>
                <div class="h-64">
                    <canvas id="salaryDistributionChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 五险详细信息 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6 mb-6">
            <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                <i data-lucide="shield" class="mr-2 h-5 w-5 text-blue-600"></i>
                五险缴费详情
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div class="bg-blue-50 rounded-lg p-4 border border-blue-100">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium text-blue-800">养老保险</h3>
                        <i data-lucide="heart" class="w-5 h-5 text-blue-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">参保人数</span>
                            <span class="font-semibold">326人</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">企业比例</span>
                            <span class="font-semibold">16%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">个人比例</span>
                            <span class="font-semibold">8%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">月缴费额</span>
                            <span class="font-semibold text-blue-600">66.4万元</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-green-50 rounded-lg p-4 border border-green-100">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium text-green-800">医疗保险</h3>
                        <i data-lucide="heart-pulse" class="w-5 h-5 text-green-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">参保人数</span>
                            <span class="font-semibold">326人</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">企业比例</span>
                            <span class="font-semibold">9.5%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">个人比例</span>
                            <span class="font-semibold">2%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">月缴费额</span>
                            <span class="font-semibold text-green-600">31.9万元</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-100">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium text-yellow-800">失业保险</h3>
                        <i data-lucide="briefcase" class="w-5 h-5 text-yellow-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">参保人数</span>
                            <span class="font-semibold">326人</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">企业比例</span>
                            <span class="font-semibold">0.5%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">个人比例</span>
                            <span class="font-semibold">0.5%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">月缴费额</span>
                            <span class="font-semibold text-yellow-600">2.8万元</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-purple-50 rounded-lg p-4 border border-purple-100">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium text-purple-800">工伤保险</h3>
                        <i data-lucide="shield-alert" class="w-5 h-5 text-purple-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">参保人数</span>
                            <span class="font-semibold">326人</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">企业比例</span>
                            <span class="font-semibold">0.4%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">个人比例</span>
                            <span class="font-semibold">0%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">月缴费额</span>
                            <span class="font-semibold text-purple-600">1.1万元</span>
                        </div>
                    </div>
                </div>
                
                <div class="bg-red-50 rounded-lg p-4 border border-red-100">
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="font-medium text-red-800">生育保险</h3>
                        <i data-lucide="baby" class="w-5 h-5 text-red-600"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">参保人数</span>
                            <span class="font-semibold">326人</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">企业比例</span>
                            <span class="font-semibold">0.8%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">个人比例</span>
                            <span class="font-semibold">0%</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">月缴费额</span>
                            <span class="font-semibold text-red-600">2.2万元</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 参保人员详细信息 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-blue-800 flex items-center">
                        <i data-lucide="list" class="mr-2 h-5 w-5 text-blue-600"></i>
                        参保人员信息
                    </h2>
                    <div class="flex gap-2">
                        <input type="text" placeholder="搜索员工..." class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部部门</option>
                            <option>技术部</option>
                            <option>销售部</option>
                            <option>财务部</option>
                            <option>行政部</option>
                        </select>
                        <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>全部状态</option>
                            <option>正常参保</option>
                            <option>暂停参保</option>
                            <option>新增参保</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">缴费基数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参保状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参保开始时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">张**</div>
                                        <div class="text-sm text-gray-500">员工编号: 001</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">高级工程师</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15,000元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常参保</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2020-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">李**</div>
                                        <div class="text-sm text-gray-500">员工编号: 002</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">销售部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">销售经理</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12,000元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">正常参保</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2019-08-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                        
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">王**</div>
                                        <div class="text-sm text-gray-500">员工编号: 003</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">财务部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">财务专员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8,500元</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">新增参保</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">显示第 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">326</span> 条记录</p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i data-lucide="chevron-left" class="w-4 h-4"></i>
                            </button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">1</button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">109</button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i data-lucide="chevron-right" class="w-4 h-4"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 参保人数趋势图表
        const participantCtx = document.getElementById('participantTrendChart').getContext('2d');
        new Chart(participantCtx, {
            type: 'line',
            data: {
                labels: ['2021-01', '2021-07', '2022-01', '2022-07', '2023-01', '2023-07', '2024-01', '2024-05'],
                datasets: [{
                    label: '参保人数',
                    data: [280, 285, 295, 302, 311, 318, 321, 326],
                    borderColor: 'rgba(59, 130, 246, 1)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // 缴费基数分布图表
        const salaryCtx = document.getElementById('salaryDistributionChart').getContext('2d');
        new Chart(salaryCtx, {
            type: 'bar',
            data: {
                labels: ['3000-5000', '5000-8000', '8000-12000', '12000-20000', '20000+'],
                datasets: [{
                    label: '人数',
                    data: [25, 68, 125, 85, 23],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(239, 68, 68, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    </script>
</body>
</html> 