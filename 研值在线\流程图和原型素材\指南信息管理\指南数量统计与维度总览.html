<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指南数量统计与维度总览 - 科技创新管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-semibold text-gray-900">科技创新管理平台</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-primary-600 border-b-2 border-primary-600 px-3 py-2 text-sm font-medium">指南统计</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">指南管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">成果管理</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </button>
                    <div class="relative">
                        <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">指南数量统计与维度总览</h2>
            <p class="mt-1 text-sm text-gray-600">实时聚合分析指南库，按技术领域、发布单位与计划类别三大维度输出数量、占比及趋势</p>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">筛选条件</h3>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-500">最后更新:</span>
                    <span class="text-sm font-medium text-gray-900" id="lastUpdateTime">2024-01-15 14:30:25</span>
                    <button onclick="refreshData()" class="text-primary-600 hover:text-primary-700 text-sm">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div class="space-y-4 mb-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <div class="relative">
                        <button onclick="toggleDropdown('techField')" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-left focus:outline-none focus:ring-primary-500 focus:border-primary-500 flex items-center justify-between">
                            <span id="techFieldText" class="text-gray-500">请选择技术领域</span>
                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="techFieldDropdown" class="hidden absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto">
                            <label class="flex items-center px-3 py-2 hover:bg-gray-50">
                                <input type="checkbox" value="人工智能" class="mr-2 tech-field-checkbox">
                                <span class="text-sm">人工智能</span>
                            </label>
                            <label class="flex items-center px-3 py-2 hover:bg-gray-50">
                                <input type="checkbox" value="生物技术" class="mr-2 tech-field-checkbox">
                                <span class="text-sm">生物技术</span>
                            </label>
                            <label class="flex items-center px-3 py-2 hover:bg-gray-50">
                                <input type="checkbox" value="新材料" class="mr-2 tech-field-checkbox">
                                <span class="text-sm">新材料</span>
                            </label>
                            <label class="flex items-center px-3 py-2 hover:bg-gray-50">
                                <input type="checkbox" value="新能源" class="mr-2 tech-field-checkbox">
                                <span class="text-sm">新能源</span>
                            </label>
                            <label class="flex items-center px-3 py-2 hover:bg-gray-50">
                                <input type="checkbox" value="信息技术" class="mr-2 tech-field-checkbox">
                                <span class="text-sm">信息技术</span>
                            </label>
                        </div>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">发布单位</label>
                    <div class="relative">
                        <button onclick="toggleDropdown('publishUnit')" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-left focus:outline-none focus:ring-primary-500 focus:border-primary-500 flex items-center justify-between">
                            <span id="publishUnitText" class="text-gray-500">请选择发布单位</span>
                            <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="publishUnitDropdown" class="hidden absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto">
                            <div class="px-3 py-2 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" value="科技部" class="mr-2 publish-unit-checkbox">
                                    <span class="text-sm font-medium">科技部</span>
                                </div>
                                <div class="ml-4 mt-1 space-y-1">
                                    <label class="flex items-center">
                                        <input type="checkbox" value="基础研究司" class="mr-2 publish-unit-checkbox">
                                        <span class="text-xs text-gray-600">基础研究司</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" value="高新技术司" class="mr-2 publish-unit-checkbox">
                                        <span class="text-xs text-gray-600">高新技术司</span>
                                    </label>
                                </div>
                            </div>
                            <div class="px-3 py-2 border-b border-gray-200">
                                <div class="flex items-center">
                                    <input type="checkbox" value="国家自然科学基金委" class="mr-2 publish-unit-checkbox">
                                    <span class="text-sm font-medium">国家自然科学基金委</span>
                                </div>
                            </div>
                            <div class="px-3 py-2">
                                <div class="flex items-center">
                                    <input type="checkbox" value="工信部" class="mr-2 publish-unit-checkbox">
                                    <span class="text-sm font-medium">工信部</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">计划类别</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <option value="">全部类别</option>
                            <option value="国家重点研发计划">国家重点研发计划</option>
                            <option value="国家自然科学基金">国家自然科学基金</option>
                            <option value="技术创新引导专项">技术创新引导专项</option>
                            <option value="基地和人才专项">基地和人才专项</option>
                            <option value="国际科技创新合作">国际科技创新合作</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                        <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                            <input type="date" class="w-full sm:flex-1 px-2 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                            <span class="hidden sm:flex items-center text-gray-500 text-sm px-1">至</span>
                            <div class="sm:hidden text-center text-xs text-gray-500 py-1">至</div>
                            <input type="date" class="w-full sm:flex-1 px-2 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button onclick="resetFilters()" class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    重置
                </button>
                <button onclick="applyFilters()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-320px)]">
            <!-- 统计图表区 -->
            <div class="lg:col-span-3 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <h3 class="text-lg font-medium text-gray-900">统计图表</h3>
                            <div class="flex items-center space-x-2 text-sm text-gray-600">
                                <span>指南总数:</span>
                                <span class="font-semibold text-primary-600" id="totalGuides">1,248</span>
                                <span class="text-gray-400">|</span>
                                <span>已筛选:</span>
                                <span class="font-semibold text-green-600" id="filteredGuides">856</span>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="flex bg-gray-100 rounded-lg p-1">
                                <button onclick="switchChartType('pie')" id="pieBtn" class="px-3 py-1 text-xs font-medium rounded-md bg-primary-600 text-white">
                                    饼图
                                </button>
                                <button onclick="switchChartType('bar')" id="barBtn" class="px-3 py-1 text-xs font-medium rounded-md text-gray-600 hover:text-gray-900">
                                    柱状图
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="p-6">
                    <!-- 维度选择选项卡 -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="-mb-px flex space-x-8">
                            <button onclick="switchDimension('techField')" class="dimension-tab active border-transparent text-primary-600 border-b-2 border-primary-600 py-2 px-1 text-sm font-medium">
                                技术领域
                            </button>
                            <button onclick="switchDimension('publishUnit')" class="dimension-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                                发布单位
                            </button>
                            <button onclick="switchDimension('planCategory')" class="dimension-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                                计划类别
                            </button>
                        </nav>
                    </div>
                    
                    <!-- 图表容器 -->
                    <div class="relative h-96">
                        <canvas id="statisticsChart" class="w-full h-full"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 下钻导航区 -->
            <div class="lg:col-span-1 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                <div class="px-4 py-4 border-b border-gray-200 bg-gray-50">
                    <h3 class="text-lg font-medium text-gray-900">下钻导航</h3>
                    <p class="text-sm text-gray-500 mt-1">点击查看详细信息</p>
                </div>
                
                <div class="p-4 overflow-y-auto h-full">
                    <div id="drillDownContent" class="space-y-3">
                        <!-- 技术领域下钻 -->
                        <div class="drill-item bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('人工智能')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-900">人工智能</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-blue-600">245</div>
                                    <div class="text-xs text-gray-500">28.6%</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-blue-500 h-1.5 rounded-full" style="width: 28.6%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="drill-item bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('生物技术')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-900">生物技术</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-green-600">198</div>
                                    <div class="text-xs text-gray-500">23.1%</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-green-500 h-1.5 rounded-full" style="width: 23.1%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="drill-item bg-gradient-to-r from-purple-50 to-violet-50 rounded-lg p-3 border border-purple-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('新材料')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-900">新材料</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-purple-600">156</div>
                                    <div class="text-xs text-gray-500">18.2%</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-purple-500 h-1.5 rounded-full" style="width: 18.2%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="drill-item bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg p-3 border border-yellow-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('新能源')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-900">新能源</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-yellow-600">134</div>
                                    <div class="text-xs text-gray-500">15.7%</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-yellow-500 h-1.5 rounded-full" style="width: 15.7%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="drill-item bg-gradient-to-r from-red-50 to-rose-50 rounded-lg p-3 border border-red-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('信息技术')">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                    <span class="text-sm font-medium text-gray-900">信息技术</span>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-semibold text-red-600">123</div>
                                    <div class="text-xs text-gray-500">14.4%</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="w-full bg-gray-200 rounded-full h-1.5">
                                    <div class="bg-red-500 h-1.5 rounded-full" style="width: 14.4%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 查看更多按钮 -->
                        <div class="text-center pt-4">
                            <button onclick="viewAllDetails()" class="inline-flex items-center px-3 py-2 border border-primary-300 rounded-md text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100">
                                <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                查看全部详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示信息 -->
    <div id="toast" class="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg hidden">
        <div class="flex items-center">
            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span id="toastMessage">操作成功</span>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentChartType = 'pie';
        let currentDimension = 'techField';
        
        // 模拟数据
        const mockData = {
            techField: {
                labels: ['人工智能', '生物技术', '新材料', '新能源', '信息技术'],
                data: [245, 198, 156, 134, 123],
                colors: ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444']
            },
            publishUnit: {
                labels: ['科技部', '国家自然科学基金委', '工信部', '教育部', '中科院'],
                data: [312, 278, 156, 89, 21],
                colors: ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444']
            },
            planCategory: {
                labels: ['国家重点研发计划', '国家自然科学基金', '技术创新引导专项', '基地和人才专项', '国际科技创新合作'],
                data: [356, 298, 134, 45, 23],
                colors: ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B', '#EF4444']
            }
        };

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('statisticsChart').getContext('2d');
            const data = mockData[currentDimension];
            
            if (currentChart) {
                currentChart.destroy();
            }
            
            const config = {
                type: currentChartType,
                data: {
                    labels: data.labels,
                    datasets: [{
                        data: data.data,
                        backgroundColor: data.colors,
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: currentChartType === 'pie' ? 'right' : 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    onClick: (event, elements) => {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const label = data.labels[index];
                            drillDown(label);
                        }
                    }
                }
            };
            
            if (currentChartType === 'bar') {
                config.options.scales = {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 50
                        }
                    }
                };
            }
            
            currentChart = new Chart(ctx, config);
        }

        // 切换图表类型
        function switchChartType(type) {
            currentChartType = type;
            
            // 更新按钮状态
            document.getElementById('pieBtn').className = type === 'pie' ? 
                'px-3 py-1 text-xs font-medium rounded-md bg-primary-600 text-white' :
                'px-3 py-1 text-xs font-medium rounded-md text-gray-600 hover:text-gray-900';
            
            document.getElementById('barBtn').className = type === 'bar' ? 
                'px-3 py-1 text-xs font-medium rounded-md bg-primary-600 text-white' :
                'px-3 py-1 text-xs font-medium rounded-md text-gray-600 hover:text-gray-900';
            
            initChart();
        }

        // 切换维度
        function switchDimension(dimension) {
            currentDimension = dimension;
            
            // 更新选项卡状态
            document.querySelectorAll('.dimension-tab').forEach(tab => {
                tab.classList.remove('text-primary-600', 'border-primary-600');
                tab.classList.add('text-gray-500', 'border-transparent');
            });
            
            event.target.classList.remove('text-gray-500', 'border-transparent');
            event.target.classList.add('text-primary-600', 'border-primary-600');
            
            initChart();
            updateDrillDownContent();
        }

        // 更新下钻内容
        function updateDrillDownContent() {
            const data = mockData[currentDimension];
            const total = data.data.reduce((a, b) => a + b, 0);
            
            const colors = ['blue', 'green', 'purple', 'yellow', 'red'];
            const content = data.labels.map((label, index) => {
                const percentage = ((data.data[index] * 100) / total).toFixed(1);
                const color = colors[index % colors.length];
                
                return `
                    <div class="drill-item bg-gradient-to-r from-${color}-50 to-${color}-50 rounded-lg p-3 border border-${color}-200 cursor-pointer hover:shadow-md transition-shadow" onclick="drillDown('${label}')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-${color}-500 rounded-full"></div>
                                <span class="text-sm font-medium text-gray-900">${label}</span>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-semibold text-${color}-600">${data.data[index]}</div>
                                <div class="text-xs text-gray-500">${percentage}%</div>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="w-full bg-gray-200 rounded-full h-1.5">
                                <div class="bg-${color}-500 h-1.5 rounded-full" style="width: ${percentage}%"></div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');
            
            document.getElementById('drillDownContent').innerHTML = content + `
                <div class="text-center pt-4">
                    <button onclick="viewAllDetails()" class="inline-flex items-center px-3 py-2 border border-primary-300 rounded-md text-sm font-medium text-primary-700 bg-primary-50 hover:bg-primary-100">
                        <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                        查看全部详情
                    </button>
                </div>
            `;
        }

        // 下拉框切换
        function toggleDropdown(type) {
            const dropdown = document.getElementById(type + 'Dropdown');
            dropdown.classList.toggle('hidden');
        }

        // 应用筛选
        function applyFilters() {
            showToast('筛选条件已应用，正在更新统计数据...');
            
            // 更新筛选后的数量
            setTimeout(() => {
                document.getElementById('filteredGuides').textContent = '856';
                initChart();
            }, 1000);
        }

        // 重置筛选
        function resetFilters() {
            // 重置所有筛选条件
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
            document.querySelectorAll('select').forEach(select => select.selectedIndex = 0);
            document.querySelectorAll('input[type="date"]').forEach(input => input.value = '');
            
            // 重置下拉框显示文本
            document.getElementById('techFieldText').textContent = '请选择技术领域';
            document.getElementById('publishUnitText').textContent = '请选择发布单位';
            
            document.getElementById('filteredGuides').textContent = '1,248';
            showToast('筛选条件已重置');
        }

        // 刷新数据
        function refreshData() {
            const now = new Date();
            const timeString = now.getFullYear() + '-' + 
                String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                String(now.getDate()).padStart(2, '0') + ' ' + 
                String(now.getHours()).padStart(2, '0') + ':' + 
                String(now.getMinutes()).padStart(2, '0') + ':' + 
                String(now.getSeconds()).padStart(2, '0');
            
            document.getElementById('lastUpdateTime').textContent = timeString;
            showToast('数据已刷新');
        }

        // 下钻操作
        function drillDown(item) {
            showToast('正在跳转到 "' + item + '" 的详细信息页面...');
        }

        // 查看全部详情
        function viewAllDetails() {
            showToast('正在跳转到指南详情页面...');
        }

        // 显示提示信息
        function showToast(message) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toastMessage');
            
            toastMessage.textContent = message;
            toast.classList.remove('hidden');
            
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }

        // 点击外部关闭下拉框
        document.addEventListener('click', function(event) {
            if (!event.target.closest('.relative')) {
                document.querySelectorAll('[id$="Dropdown"]').forEach(dropdown => {
                    dropdown.classList.add('hidden');
                });
            }
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
    </script>
</body>
</html>