object IndexCkLoginForm: TIndexCkLoginForm
  Left = 0
  Top = 0
  BorderIcons = []
  BorderStyle = bsNone
  Caption = #21830#21073#31649#25511#36719#20214
  ClientHeight = 272
  ClientWidth = 439
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 17
  object RzPanel1: TRzPanel
    Left = 0
    Top = 0
    Width = 439
    Height = 272
    Align = alClient
    BorderOuter = fsNone
    BorderColor = 14540251
    BorderWidth = 1
    Color = clWhite
    TabOrder = 0
    object RzPanel2: TRzPanel
      Left = 1
      Top = 31
      Width = 437
      Height = 240
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 0
      object RzPanel5: TRzPanel
        Left = 0
        Top = 73
        Width = 437
        Height = 167
        Align = alClient
        BorderOuter = fsNone
        BorderSides = [sdTop]
        BorderColor = 11776947
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        object RzLabel1: TRzLabel
          Left = 60
          Top = 9
          Width = 60
          Height = 19
          Caption = #29992#25143#21517#65306
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
          Transparent = True
        end
        object RzLabel2: TRzLabel
          Left = 60
          Top = 54
          Width = 61
          Height = 19
          Caption = #23494'    '#30721#65306
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
          Transparent = True
        end
        object RzEdit1: TRzEdit
          Left = 139
          Top = 9
          Width = 201
          Height = 25
          Text = ''
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          TabOrder = 0
        end
        object Btn_Login: TAdvGlowButton
          Left = 145
          Top = 102
          Width = 111
          Height = 35
          BorderStyle = bsNone
          Caption = #30331'  '#24405
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWhite
          Font.Height = -21
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 1
          OnClick = Btn_LoginClick
          Appearance.Color = 1556823
          Appearance.ColorTo = 1556823
          Appearance.ColorChecked = 1556823
          Appearance.ColorCheckedTo = 1556823
          Appearance.ColorDisabled = 1556823
          Appearance.ColorDisabledTo = 1556823
          Appearance.ColorDown = 1556823
          Appearance.ColorDownTo = 1556823
          Appearance.ColorHot = clGreen
          Appearance.ColorHotTo = clGreen
          Appearance.ColorMirror = 1556823
          Appearance.ColorMirrorTo = 1556823
          Appearance.ColorMirrorHot = clGreen
          Appearance.ColorMirrorHotTo = clGreen
          Appearance.ColorMirrorDown = 1556823
          Appearance.ColorMirrorDownTo = 1556823
          Appearance.ColorMirrorChecked = 1556823
          Appearance.ColorMirrorCheckedTo = 1556823
          Appearance.ColorMirrorDisabled = 1556823
          Appearance.ColorMirrorDisabledTo = 1556823
          Appearance.SystemFont = False
          Appearance.TextColorDown = clWhite
          Appearance.TextColorHot = clWhite
        end
        object RzMaskEdit1: TRzMaskEdit
          Left = 139
          Top = 56
          Width = 201
          Height = 25
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          PasswordChar = '*'
          TabOrder = 2
          Text = ''
          OnChange = RzMaskEdit1Change
          OnKeyPress = RzMaskEdit1KeyPress
        end
        object RzCheckBox1: TRzCheckBox
          Left = 262
          Top = 113
          Width = 79
          Height = 19
          Caption = #35760#20303#29992#25143#21517
          Checked = True
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          State = cbChecked
          TabOrder = 3
          Transparent = True
        end
      end
      object RzPanel4: TRzPanel
        Left = 0
        Top = 0
        Width = 437
        Height = 73
        Align = alTop
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        object RzLabel3: TRzLabel
          Left = 60
          Top = 34
          Width = 75
          Height = 19
          Caption = #20179#24211#31867#21035#65306
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
          Transparent = True
        end
        object ComboBox_Zffs: TRzComboBox
          Left = 139
          Top = 31
          Width = 201
          Height = 29
          Color = 16051944
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -16
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ImeName = #35895#27468#25340#38899#36755#20837#27861' 2'
          ParentFont = False
          TabOrder = 0
          OnChange = ComboBox_ZffsChange
          Items.Strings = (
            #20027#26009#20179
            #36741#26009#20179)
        end
      end
    end
    object RzPanel3: TRzPanel
      Left = 1
      Top = 1
      Width = 437
      Height = 30
      Align = alTop
      BorderOuter = fsNone
      Color = 16049103
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentFont = False
      TabOrder = 1
      VisualStyle = vsGradient
      object lbl_Title: TRzLabel
        Left = 6
        Top = 5
        Width = 78
        Height = 19
        Caption = #21830#21073#31649#25511#36719#20214
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        Transparent = True
      end
      object htImg_Close: THTImage
        Left = 568
        Top = 0
        Width = 30
        Height = 24
        Cursor = crHandPoint
      end
      object HTImage1: THTImage
        Left = 403
        Top = 0
        Width = 30
        Height = 30
        Cursor = crHandPoint
        Picture.Data = {
          0954506E67496D61676589504E470D0A1A0A0000000D494844520000001E0000
          001E08060000003B30AEA2000004784944415478DAED966D4C5B6514C79F96B6
          29B4BD1496287DA5ADA52D813158800FCA4819BE44BF38E8ED2D75F325F19B89
          F18B7ED8C77D30D1444D16338DD947A2D217448DC698A19B332BD05C082F6571
          A116B8EB26F48DBEE11628B79EA72F4BD1D4DE65269AB82739696EFB9CF33BFF
          739EE7DCF2D0BFB4780FC1FF6F303546F6B26CFE39D81483AD6E8FDB1BAB1590
          1AB3E9F27944A27C9E8FF87CAF67C21BBA2F3040AD46A3714AAFD3CB73B91CBB
          B2B23C1B8DC61C1EF764B89A8F7DCCD6AD54285D1D1D1D269447E8C68D5F42CC
          CDF0331E9737C819FCD22B675C56AB95DADEDE42229108C9E54D689EA6FDB158
          DCE67679FF02A79C64B75AA99AECEAEA32446351747070809A9B8FA04BD3D367
          41F53BDCC12F9F1EB70E0D9D89442250315E01DE4834A2F9F9793A1A8DDA5C13
          5EA6BCD7F102751CA09EAE635D86783C8E72B9FDC2F78D8D72343D3DFD96C735
          F91E67B0DD410E98CDA66F753A1D914CEE201EAF0897490944837280DB5D131E
          660C43556ACF3180EEC0BEFDFD22B4A9A919050281D0AD70781892DCE00C2EF5
          8C6AB7B45FD4B5B612A974EA1E5C2A9521FF9C9F8E4423E75AB5ADE7BB7BBA0D
          C964F290D2959500B3C96CDABCAE49FABE0E57798122CA6C365FD483F274265D
          A15C8656AF5F678F761EE5A7D2C97B4A6532022B0D0374C4FDB997AE16B72698
          748CF285022169B1583ED1EBF5F24C092E140A91542245D9DD2C28CDC1EDC150
          19285D01A58CDDF599DBFF7771390D10D23102701189CB6E78CC4094E1D8F298
          889542EF979797990D66B3269433B8AC1CD69BFDBD7DEF0A454274E7CEDDC269
          C7CAB1D2F5F50DB4B6B636E29EF07EC9251E6730E5B4F72B158AC9B6B636752A
          9544799605EFA2EA3A810011D073504CC713099BBBE2AA3D1018EE69BFB245E1
          31994C5A7C9030140305A016F7172F01C0250D52802FF9E3F1841D860CF34060
          AC54A5507AE04E6BF1A966010A2547049CDE543A8D088240B8E7B8D745B8042D
          2E2DD189787CC4EDAA3E5E6BDC63B217CA3BD56EB1A833D9CC21E8DA5A708B09
          33E7DB8C6D67B51A0D91C9E2A48AF07A713D5A5C5CF427123BB66AB3BDFA4BC2
          61373DDAF2C82580169462457575758583140CFE7AFB669871DC0AFFE6536B54
          94D160FC58AD5117AE5A39B986FA067CB57CD0F3E7BDEE2F629CC1634EEAC2C0
          8981D72A83E189150A856E87D6432F7EF7CDF7BEBDBD3D188D72FED0B09584FE
          7FA852A9882C54A69CA45028627D3EDF1B00FE0842B2B5C00230B1C3691F1F1C
          1C3C854F305E128904A0EB91D5D5D5572FFFF0930FDE3E38101FFF26168BF927
          9FB48E767676BEAF5229A5BBBBBB451FE8F78F97AF7CF0D5D4D7E7E0F12E58AE
          9CC09FC13890084CFAD4D3C3A37DFD7D175A5A5A04587130188C2C2C2CBC3E73
          6D6E1614E1207B153E02A888D87A72F0D4F19E9EB7355AAD140786B1999DF1CD
          9DF65D9B9985C7DF4BC656538CC162B0060C6F3ED2FC2CCCE134C384C7693F1D
          28655ECEFE5095B00D9C78E271A54AE1C4DF6D6D6D7F7AF5CACF572BA0E564AB
          F658503251B99CA54C7325E75C151F51E9B3A60F9701521984EBAAE9F3DFFC97
          F910FC4FAE3F00955B0A3D57E4650A0000000049454E44AE426082}
        OnClick = HTImage1Click
      end
    end
  end
end
