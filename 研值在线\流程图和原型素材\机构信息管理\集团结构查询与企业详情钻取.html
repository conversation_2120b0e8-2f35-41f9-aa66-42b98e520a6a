<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>集团结构查询与企业详情钻取 - 科技创新管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-xl font-semibold text-gray-900">科技创新管理平台</h1>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">首页</a>
                        <a href="#" class="text-primary-600 border-b-2 border-primary-600 px-3 py-2 text-sm font-medium">集团结构查询</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">企业信息管理</a>
                        <a href="#" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">成果管理</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5v-12"></path>
                        </svg>
                    </button>
                    <div class="relative">
                        <button class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <img class="h-8 w-8 rounded-full" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" alt="用户头像">
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-900">集团结构查询与企业详情钻取</h2>
            <p class="mt-1 text-sm text-gray-600">通过多维度筛选快速定位目标集团，查看企业层级结构和详细信息</p>
        </div>

        <!-- 查询与过滤区 -->
        <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">查询条件</h3>
                <button onclick="toggleAdvancedFilter()" class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center">
                    <span id="advancedToggleText">展开高级筛选</span>
                    <svg id="advancedToggleIcon" class="ml-1 h-4 w-4 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 基础筛选 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">集团名称</label>
                    <input type="text" placeholder="请输入集团名称" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统一社会信用代码</label>
                    <input type="text" placeholder="请输入信用代码" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所在区域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">全部区域</option>
                        <option value="北京">北京市</option>
                        <option value="上海">上海市</option>
                        <option value="广东">广东省</option>
                        <option value="江苏">江苏省</option>
                        <option value="浙江">浙江省</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业分类</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        <option value="">全部行业</option>
                        <option value="制造业">制造业</option>
                        <option value="信息技术">信息技术</option>
                        <option value="金融业">金融业</option>
                        <option value="房地产">房地产</option>
                        <option value="能源">能源</option>
                    </select>
                </div>
            </div>
            
            <!-- 高级筛选 -->
            <div id="advancedFilter" class="hidden">
                <div class="border-t border-gray-200 pt-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键字</label>
                            <input type="text" placeholder="请输入关键字" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">更新时间起</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">更新时间止</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    重置
                </button>
                <button onclick="performSearch()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-320px)]">
            <!-- 集团层级视图区 -->
            <div class="lg:col-span-1 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">集团层级结构</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="expandAll()" class="text-xs text-primary-600 hover:text-primary-700">展开全部</button>
                            <span class="text-gray-300">|</span>
                            <button onclick="collapseAll()" class="text-xs text-primary-600 hover:text-primary-700">收起全部</button>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">共找到 3 个集团，156 家企业</p>
                </div>
                
                <div class="p-4 overflow-y-auto h-full">
                    <!-- 集团树状结构 -->
                    <div class="space-y-2">
                        <!-- 集团1 -->
                        <div class="group-node">
                            <div onclick="toggleGroup('group1')" class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200">
                                <svg id="group1-icon" class="h-4 w-4 text-gray-400 mr-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2">制造业</span>
                                        <h4 class="text-sm font-medium text-gray-900">华为技术有限公司</h4>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">91440300279174282F | 深圳市 | 23家子公司</p>
                                </div>
                            </div>
                            
                            <!-- 子企业列表 -->
                            <div id="group1-children" class="hidden ml-6 mt-2 space-y-1">
                                <div onclick="showCompanyDetail('华为终端有限公司')" class="flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer border-l-2 border-blue-200">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">全资子公司</span>
                                            <h5 class="text-sm font-medium text-gray-800">华为终端有限公司</h5>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">91440300MA5EQXXX8X | 深圳市</p>
                                    </div>
                                    <button onclick="copyToClipboard('91440300MA5EQXXX8X')" class="text-gray-400 hover:text-gray-600">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div onclick="showCompanyDetail('华为云计算技术有限公司')" class="flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer border-l-2 border-blue-200">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">全资子公司</span>
                                            <h5 class="text-sm font-medium text-gray-800">华为云计算技术有限公司</h5>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">91110108MA00XXXX1A | 北京市</p>
                                    </div>
                                    <button onclick="copyToClipboard('91110108MA00XXXX1A')" class="text-gray-400 hover:text-gray-600">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="text-center py-2">
                                    <button class="text-xs text-primary-600 hover:text-primary-700">查看更多 21 家子公司</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 集团2 -->
                        <div class="group-node">
                            <div onclick="toggleGroup('group2')" class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200">
                                <svg id="group2-icon" class="h-4 w-4 text-gray-400 mr-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 mr-2">信息技术</span>
                                        <h4 class="text-sm font-medium text-gray-900">腾讯控股有限公司</h4>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">91440300708461136T | 深圳市 | 45家子公司</p>
                                </div>
                            </div>
                            
                            <div id="group2-children" class="hidden ml-6 mt-2 space-y-1">
                                <div onclick="showCompanyDetail('腾讯科技（深圳）有限公司')" class="flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer border-l-2 border-purple-200">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mr-2">全资子公司</span>
                                            <h5 class="text-sm font-medium text-gray-800">腾讯科技（深圳）有限公司</h5>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">91440300708461136T | 深圳市</p>
                                    </div>
                                    <button onclick="copyToClipboard('91440300708461136T')" class="text-gray-400 hover:text-gray-600">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="text-center py-2">
                                    <button class="text-xs text-primary-600 hover:text-primary-700">查看更多 44 家子公司</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 集团3 -->
                        <div class="group-node">
                            <div onclick="toggleGroup('group3')" class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-gray-200">
                                <svg id="group3-icon" class="h-4 w-4 text-gray-400 mr-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">能源</span>
                                        <h4 class="text-sm font-medium text-gray-900">中国石油天然气集团有限公司</h4>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">91100000100000017Q | 北京市 | 88家子公司</p>
                                </div>
                            </div>
                            
                            <div id="group3-children" class="hidden ml-6 mt-2 space-y-1">
                                <div onclick="showCompanyDetail('中国石油天然气股份有限公司')" class="flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer border-l-2 border-green-200">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">控股子公司</span>
                                            <h5 class="text-sm font-medium text-gray-800">中国石油天然气股份有限公司</h5>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-1">91100000100000018X | 北京市</p>
                                    </div>
                                    <button onclick="copyToClipboard('91100000100000018X')" class="text-gray-400 hover:text-gray-600">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="text-center py-2">
                                    <button class="text-xs text-primary-600 hover:text-primary-700">查看更多 87 家子公司</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 企业详情抽屉区 -->
            <div class="lg:col-span-2 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">企业详情</h3>
                        <div class="flex items-center space-x-2">
                            <button onclick="jumpToCredit()" class="inline-flex items-center px-3 py-1.5 border border-primary-300 rounded-md text-xs font-medium text-primary-700 bg-primary-50 hover:bg-primary-100">
                                <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                跳转到企业信用
                            </button>
                            <button onclick="jumpToProject()" class="inline-flex items-center px-3 py-1.5 border border-green-300 rounded-md text-xs font-medium text-green-700 bg-green-50 hover:bg-green-100">
                                <svg class="-ml-0.5 mr-1.5 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                跳转到项目管理
                            </button>
                        </div>
                    </div>
                </div>
                
                <div id="companyDetailContent" class="p-6 overflow-y-auto h-full">
                    <!-- 默认状态 -->
                    <div id="defaultState" class="flex flex-col items-center justify-center h-full text-gray-500">
                        <svg class="h-16 w-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        <p class="text-lg font-medium mb-2">请选择企业查看详情</p>
                        <p class="text-sm">点击左侧企业节点查看详细信息</p>
                    </div>
                    
                    <!-- 企业详情内容 -->
                    <div id="detailContent" class="hidden space-y-6">
                        <!-- 企业基础信息 -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
                            <div class="flex items-start space-x-4">
                                <img class="h-16 w-16 rounded-lg" src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100&q=80" alt="企业logo">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 id="companyName" class="text-xl font-bold text-gray-900">华为终端有限公司</h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">正常经营</span>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div class="flex items-center">
                                            <span class="text-gray-500 mr-2">信用代码:</span>
                                            <span id="creditCode" class="font-mono text-gray-900">91440300MA5EQXXX8X</span>
                                            <button onclick="copyToClipboard('91440300MA5EQXXX8X')" class="ml-2 text-gray-400 hover:text-gray-600">
                                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                </svg>
                                            </button>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-gray-500 mr-2">法定代表人:</span>
                                            <span class="text-gray-900">何刚</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-gray-500 mr-2">注册资本:</span>
                                            <span class="text-gray-900">200,000万元</span>
                                        </div>
                                        <div class="flex items-center">
                                            <span class="text-gray-500 mr-2">成立日期:</span>
                                            <span class="text-gray-900">2018-07-09</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 选项卡导航 -->
                        <div class="border-b border-gray-200">
                            <nav class="-mb-px flex space-x-8">
                                <button onclick="switchTab('basic')" class="tab-button active border-transparent text-primary-600 border-b-2 border-primary-600 py-2 px-1 text-sm font-medium">
                                    基础信息
                                </button>
                                <button onclick="switchTab('structure')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                                    股东结构
                                </button>
                                <button onclick="switchTab('business')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                                    主营业务
                                </button>
                                <button onclick="switchTab('history')" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                                    变更历史
                                </button>
                            </nav>
                        </div>
                        
                        <!-- 选项卡内容 -->
                        <div id="tabContent">
                            <!-- 基础信息 -->
                            <div id="basicTab" class="tab-content">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div class="space-y-4">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">注册地址</dt>
                                            <dd class="mt-1 text-sm text-gray-900 flex items-center">
                                                深圳市龙岗区坂田街道华为总部办公楼
                                                <button onclick="copyToClipboard('深圳市龙岗区坂田街道华为总部办公楼')" class="ml-2 text-gray-400 hover:text-gray-600">
                                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                    </svg>
                                                </button>
                                            </dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">经营范围</dt>
                                            <dd class="mt-1 text-sm text-gray-900">移动通信终端设备、数据通信设备的技术开发、生产、销售及相关服务</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">企业类型</dt>
                                            <dd class="mt-1 text-sm text-gray-900">有限责任公司(法人独资)</dd>
                                        </div>
                                    </div>
                                    <div class="space-y-4">
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">营业期限</dt>
                                            <dd class="mt-1 text-sm text-gray-900">2018-07-09 至 长期</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">登记机关</dt>
                                            <dd class="mt-1 text-sm text-gray-900">深圳市市场监督管理局</dd>
                                        </div>
                                        <div>
                                            <dt class="text-sm font-medium text-gray-500">核准日期</dt>
                                            <dd class="mt-1 text-sm text-gray-900">2023-12-15</dd>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 股东结构 -->
                            <div id="structureTab" class="tab-content hidden">
                                <div class="space-y-4">
                                    <div class="bg-gray-50 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <h5 class="text-sm font-medium text-gray-900">华为技术有限公司</h5>
                                            <span class="text-sm text-gray-500">100%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-primary-600 h-2 rounded-full" style="width: 100%"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 mt-2">认缴出资: 200,000万元 | 实缴出资: 200,000万元</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 主营业务 -->
                            <div id="businessTab" class="tab-content hidden">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-900 mb-3">主要产品</h5>
                                        <div class="space-y-2">
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <span class="text-sm text-gray-900">智能手机</span>
                                                <span class="text-xs text-gray-500">核心业务</span>
                                            </div>
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <span class="text-sm text-gray-900">平板电脑</span>
                                                <span class="text-xs text-gray-500">重要业务</span>
                                            </div>
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <span class="text-sm text-gray-900">智能穿戴设备</span>
                                                <span class="text-xs text-gray-500">新兴业务</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 class="text-sm font-medium text-gray-900 mb-3">业务分布</h5>
                                        <div class="space-y-2">
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <span class="text-sm text-gray-900">国内市场</span>
                                                <span class="text-xs text-gray-500">65%</span>
                                            </div>
                                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                                <span class="text-sm text-gray-900">海外市场</span>
                                                <span class="text-xs text-gray-500">35%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 变更历史 -->
                            <div id="historyTab" class="tab-content hidden">
                                <div class="flow-root">
                                    <ul class="-mb-8">
                                        <li>
                                            <div class="relative pb-8">
                                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5">
                                                        <div>
                                                            <p class="text-sm text-gray-500">2023年12月15日</p>
                                                            <p class="text-sm font-medium text-gray-900">经营范围变更</p>
                                                            <p class="text-sm text-gray-500">新增：人工智能技术开发与应用</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="relative pb-8">
                                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5">
                                                        <div>
                                                            <p class="text-sm text-gray-500">2022年08月20日</p>
                                                            <p class="text-sm font-medium text-gray-900">注册资本变更</p>
                                                            <p class="text-sm text-gray-500">由100,000万元增加至200,000万元</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <li>
                                            <div class="relative">
                                                <div class="relative flex space-x-3">
                                                    <div>
                                                        <span class="h-8 w-8 rounded-full bg-gray-400 flex items-center justify-center ring-8 ring-white">
                                                            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                                            </svg>
                                                        </span>
                                                    </div>
                                                    <div class="min-w-0 flex-1 pt-1.5">
                                                        <div>
                                                            <p class="text-sm text-gray-500">2018年07月09日</p>
                                                            <p class="text-sm font-medium text-gray-900">企业成立</p>
                                                            <p class="text-sm text-gray-500">华为终端有限公司正式成立</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作提示与状态区 -->
    <div id="toast" class="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg hidden">
        <div class="flex items-center">
            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span id="toastMessage">操作成功</span>
        </div>
    </div>

    <!-- 查询进度条 -->
    <div id="progressBar" class="fixed top-0 left-0 w-full h-1 bg-primary-600 transform scale-x-0 origin-left transition-transform duration-300 z-50 hidden"></div>

    <script>
        // 高级筛选切换
        function toggleAdvancedFilter() {
            const filter = document.getElementById('advancedFilter');
            const icon = document.getElementById('advancedToggleIcon');
            const text = document.getElementById('advancedToggleText');
            
            if (filter.classList.contains('hidden')) {
                filter.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
                text.textContent = '收起高级筛选';
            } else {
                filter.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
                text.textContent = '展开高级筛选';
            }
        }

        // 执行搜索
        function performSearch() {
            const progressBar = document.getElementById('progressBar');
            progressBar.classList.remove('hidden');
            progressBar.style.transform = 'scaleX(0)';
            
            setTimeout(() => {
                progressBar.style.transform = 'scaleX(1)';
            }, 100);
            
            setTimeout(() => {
                progressBar.classList.add('hidden');
                showToast('查询完成，共找到 3 个集团');
            }, 1500);
        }

        // 切换集团展开/收起
        function toggleGroup(groupId) {
            const children = document.getElementById(groupId + '-children');
            const icon = document.getElementById(groupId + '-icon');
            
            if (children.classList.contains('hidden')) {
                children.classList.remove('hidden');
                icon.style.transform = 'rotate(90deg)';
            } else {
                children.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            }
        }

        // 展开全部
        function expandAll() {
            ['group1', 'group2', 'group3'].forEach(groupId => {
                const children = document.getElementById(groupId + '-children');
                const icon = document.getElementById(groupId + '-icon');
                children.classList.remove('hidden');
                icon.style.transform = 'rotate(90deg)';
            });
            showToast('已展开全部集团');
        }

        // 收起全部
        function collapseAll() {
            ['group1', 'group2', 'group3'].forEach(groupId => {
                const children = document.getElementById(groupId + '-children');
                const icon = document.getElementById(groupId + '-icon');
                children.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
            });
            showToast('已收起全部集团');
        }

        // 显示企业详情
        function showCompanyDetail(companyName) {
            document.getElementById('defaultState').classList.add('hidden');
            document.getElementById('detailContent').classList.remove('hidden');
            document.getElementById('companyName').textContent = companyName;
            
            // 重置到基础信息选项卡
            switchTab('basic');
        }

        // 切换选项卡
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // 重置所有选项卡按钮样式
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('text-primary-600', 'border-primary-600');
                button.classList.add('text-gray-500', 'border-transparent');
            });
            
            // 显示选中的选项卡内容
            document.getElementById(tabName + 'Tab').classList.remove('hidden');
            
            // 更新选中的选项卡按钮样式
            event.target.classList.remove('text-gray-500', 'border-transparent');
            event.target.classList.add('text-primary-600', 'border-primary-600');
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showToast('已复制: ' + text);
            }).catch(() => {
                showToast('复制失败，请手动复制');
            });
        }

        // 跳转到企业信用
        function jumpToCredit() {
            showToast('正在跳转到企业信用模块...');
        }

        // 跳转到项目管理
        function jumpToProject() {
            showToast('正在跳转到项目管理模块...');
        }

        // 显示提示信息
        function showToast(message) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toastMessage');
            
            toastMessage.textContent = message;
            toast.classList.remove('hidden');
            
            setTimeout(() => {
                toast.classList.add('hidden');
            }, 3000);
        }
    </script>
</body>
</html>