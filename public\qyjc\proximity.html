<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合作亲密度模块 - 企业监测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #EC4899, #BE185D);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" 
                            class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                        <span>返回</span>
                    </button>
                    <div class="w-8 h-8 bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-handshake text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">合作亲密度模块</h1>
                        <p class="text-sm text-gray-600">企业与高等院校、科研机构合作活跃度分析</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="bg-pink-500 text-white px-4 py-2 rounded-lg hover:bg-pink-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                    <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 功能说明卡片 -->
        <div class="bg-gradient-to-r from-pink-500 to-pink-600 rounded-xl p-6 text-white mb-6">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-handshake text-2xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold mb-2">合作亲密度分析</h2>
                    <p class="text-pink-100">
                        反映企业与高等院校、科研机构的合作活跃度，支持标志性产业链筛选，展示本地和外地合作TOP排名企业，为产学研合作决策提供数据支撑。
                    </p>
                </div>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-filter text-pink-500 mr-2"></i>
                筛选条件
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">产业链选择</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-pink-500 focus:border-pink-500">
                        <option>全部产业链</option>
                        <option>新能源汽车产业链</option>
                        <option>集成电路产业链</option>
                        <option>生物医药产业链</option>
                        <option>新材料产业链</option>
                        <option>人工智能产业链</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">合作类型</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-pink-500 focus:border-pink-500">
                        <option>全部类型</option>
                        <option>技术开发合作</option>
                        <option>人才培养合作</option>
                        <option>平台共建合作</option>
                        <option>成果转化合作</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-pink-500 focus:border-pink-500">
                        <option>近1年</option>
                        <option>近3年</option>
                        <option>近5年</option>
                        <option>全部时间</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业规模</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-pink-500 focus:border-pink-500">
                        <option>全部规模</option>
                        <option>大型企业</option>
                        <option>中型企业</option>
                        <option>小型企业</option>
                        <option>微型企业</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    重置
                </button>
                <button class="bg-pink-500 text-white px-4 py-2 rounded-lg hover:bg-pink-600 transition-colors">
                    应用筛选
                </button>
                <button class="border border-pink-500 text-pink-500 px-4 py-2 rounded-lg hover:bg-pink-50 transition-colors">
                    保存为模板
                </button>
            </div>
        </div>

        <!-- 概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">合作企业总数</p>
                        <p class="text-2xl font-bold text-gray-900">1,247</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上季度 +8.5%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-pink-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">合作院校数量</p>
                        <p class="text-2xl font-bold text-gray-900">156</p>
                        <p class="text-blue-600 text-xs">
                            <i class="fas fa-info-circle mr-1"></i>
                            本地82个 外地74个
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-university text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">合作项目数量</p>
                        <p class="text-2xl font-bold text-gray-900">2,847</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +15.2%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-project-diagram text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">平均亲密度指数</p>
                        <p class="text-2xl font-bold text-gray-900">78.5</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +5.8分
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 本地合作亲密度TOP10 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                        本地合作亲密度 TOP10
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                            <div>
                                <p class="font-medium text-gray-900">宁波智能制造科技有限公司</p>
                                <p class="text-xs text-gray-600">与宁波大学 · 合作项目15个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">95.8</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                            <div>
                                <p class="font-medium text-gray-900">浙江新材料研发集团</p>
                                <p class="text-xs text-gray-600">与浙江工业大学 · 合作项目12个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">92.3</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                            <div>
                                <p class="font-medium text-gray-900">海天生物医药有限公司</p>
                                <p class="text-xs text-gray-600">与宁波医学院 · 合作项目10个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-blue-600">89.7</p>
                            <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                        </div>
                    </div>
                    <!-- 更多企业 -->
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">4</span>
                                <span class="text-gray-700">慈溪新能源科技股份</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-blue-600">87.2</span>
                                <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">5</span>
                                <span class="text-gray-700">象山智能装备制造</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-blue-600">85.9</span>
                                <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">6</span>
                                <span class="text-gray-700">北仑精密仪器有限公司</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-blue-600">84.1</span>
                                <button class="text-xs text-blue-500 hover:text-blue-700">详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-blue-500 hover:text-blue-700 text-sm font-medium">
                        查看完整排名 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- 外地合作亲密度TOP10 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-globe text-green-500 mr-2"></i>
                        外地合作亲密度 TOP10
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                            <div>
                                <p class="font-medium text-gray-900">奥克斯集团有限公司</p>
                                <p class="text-xs text-gray-600">与清华大学 · 合作项目8个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">91.5</p>
                            <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                            <div>
                                <p class="font-medium text-gray-900">宁波方太集团</p>
                                <p class="text-xs text-gray-600">与上海交通大学 · 合作项目7个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">88.9</p>
                            <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                            <div>
                                <p class="font-medium text-gray-900">宁波均胜电子股份</p>
                                <p class="text-xs text-gray-600">与北京理工大学 · 合作项目6个</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-green-600">86.2</p>
                            <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                        </div>
                    </div>
                    <!-- 更多企业 -->
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">4</span>
                                <span class="text-gray-700">杉杉控股有限公司</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-green-600">83.7</span>
                                <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">5</span>
                                <span class="text-gray-700">宁波东力传动设备</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-green-600">81.4</span>
                                <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2">
                            <div class="flex items-center space-x-3">
                                <span class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-white text-xs">6</span>
                                <span class="text-gray-700">宁波韵升股份有限公司</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="font-medium text-green-600">79.8</span>
                                <button class="text-xs text-green-500 hover:text-green-700">详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-green-500 hover:text-green-700 text-sm font-medium">
                        查看完整排名 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 产业链合作分析 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-link text-purple-500 mr-2"></i>
                标志性产业链合作分析
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">新能源汽车产业链</h4>
                        <span class="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">活跃</span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">参与企业数</span>
                            <span class="font-medium">126家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">合作院校数</span>
                            <span class="font-medium">23所</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">平均亲密度</span>
                            <span class="font-medium text-blue-600">82.5</span>
                        </div>
                    </div>
                    <button class="w-full mt-3 bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors text-sm">
                        查看详情
                    </button>
                </div>
                <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">集成电路产业链</h4>
                        <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">快速发展</span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">参与企业数</span>
                            <span class="font-medium">89家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">合作院校数</span>
                            <span class="font-medium">18所</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">平均亲密度</span>
                            <span class="font-medium text-green-600">78.9</span>
                        </div>
                    </div>
                    <button class="w-full mt-3 bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors text-sm">
                        查看详情
                    </button>
                </div>
                <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h4 class="font-medium text-gray-900">生物医药产业链</h4>
                        <span class="bg-purple-500 text-white text-xs px-2 py-1 rounded-full">稳步增长</span>
                    </div>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">参与企业数</span>
                            <span class="font-medium">67家</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">合作院校数</span>
                            <span class="font-medium">15所</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">平均亲密度</span>
                            <span class="font-medium text-purple-600">75.3</span>
                        </div>
                    </div>
                    <button class="w-full mt-3 bg-purple-500 text-white py-2 rounded-lg hover:bg-purple-600 transition-colors text-sm">
                        查看详情
                    </button>
                </div>
            </div>
        </div>

        <!-- 合作趋势分析 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-gray-900 flex items-center">
                    <i class="fas fa-chart-line text-indigo-500 mr-2"></i>
                    合作趋势分析
                </h3>
                <div class="flex space-x-2">
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">年度</button>
                    <button class="text-xs bg-indigo-500 text-white px-3 py-1 rounded-lg">季度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">月度</button>
                </div>
            </div>
            <div class="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-line text-4xl mb-2"></i>
                    <p>合作趋势图表区域</p>
                    <p class="text-xs">显示企业与院校合作活跃度变化趋势</p>
                </div>
            </div>
        </div>

        <!-- 数据导出区域 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-download text-blue-500 mr-2"></i>
                数据导出与分享
            </h3>
            <div class="flex flex-wrap gap-3">
                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    导出Excel
                </button>
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                    <i class="fas fa-file-pdf mr-2"></i>
                    生成PDF报告
                </button>
                <button class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-share-alt mr-2"></i>
                    分享链接
                </button>
                <button class="border border-purple-500 text-purple-500 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>
                    定期推送设置
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 下拉框变化事件
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log('筛选条件变化:', this.value);
                });
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 