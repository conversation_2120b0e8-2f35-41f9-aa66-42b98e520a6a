<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>团队科技特派员备案登记管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        blue: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex-1 p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-blue-800 flex items-center">
                <svg class="w-6 h-6 mr-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                团队科技特派员备案登记管理
            </h1>
            <p class="text-gray-600 mt-2">为全市各类特色产业链及团队型科技服务主体提供全方位的信息采集、归档、审核及上报能力</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="bg-white rounded-lg shadow-lg border border-blue-100 h-[calc(100vh-120px)]">
            <!-- 选项卡导航 -->
            <div class="border-b border-blue-100 bg-white rounded-t-lg">
                <nav class="flex space-x-8 px-6 py-4" id="tabNav">
                    <button class="tab-button active text-blue-600 border-b-2 border-blue-600 pb-2 px-1 font-medium" data-tab="registration">
                        团队登记
                    </button>
                    <button class="tab-button text-gray-500 hover:text-blue-600 pb-2 px-1 font-medium" data-tab="batch">
                        批次管理
                    </button>
                    <button class="tab-button text-gray-500 hover:text-blue-600 pb-2 px-1 font-medium" data-tab="review">
                        审核管理
                    </button>
                    <button class="tab-button text-gray-500 hover:text-blue-600 pb-2 px-1 font-medium" data-tab="archive">
                        历史归档
                    </button>
                </nav>
            </div>

            <!-- 选项卡内容 -->
            <div class="p-6 overflow-y-auto h-[calc(100%-80px)]">
                <!-- 团队登记选项卡 -->
                <div id="registration" class="tab-content">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 团队基本信息 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                团队基本信息
                            </h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">团队名称</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入团队名称">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">特色产业</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>请选择特色产业</option>
                                        <option>现代农业</option>
                                        <option>智能制造</option>
                                        <option>生物医药</option>
                                        <option>新材料</option>
                                        <option>数字经济</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">团队负责人</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入负责人姓名">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">联系电话</label>
                                    <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入联系电话">
                                </div>
                            </div>
                        </div>

                        <!-- 成员信息 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                                团队成员信息
                                <button class="ml-auto bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600">
                                    添加成员
                                </button>
                            </h3>
                            <div class="space-y-3">
                                <div class="bg-white p-4 rounded-md border border-gray-200">
                                    <div class="grid grid-cols-2 gap-3">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="姓名">
                                        <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                            <option>性别</option>
                                            <option>男</option>
                                            <option>女</option>
                                        </select>
                                        <input type="date" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="职务">
                                        <input type="text" class="col-span-2 px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="专业技术特长">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="联系方式">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="所在单位">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产业链信息 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                </svg>
                                产业链信息
                                <button class="ml-auto bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600">
                                    添加主体
                                </button>
                            </h3>
                            <div class="space-y-4">
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="特色产业链基本情况描述"></textarea>
                                <div class="bg-white p-4 rounded-md border border-gray-200">
                                    <h4 class="font-medium text-gray-700 mb-3">核心生产经营主体</h4>
                                    <div class="grid grid-cols-2 gap-3">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="单位名称">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="联系人">
                                        <input type="text" class="col-span-2 px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="单位地址">
                                        <input type="tel" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="联系电话">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="主营业务">
                                        <textarea class="col-span-2 px-3 py-2 border border-gray-300 rounded-md text-sm" rows="2" placeholder="单位概况"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 服务对象信息 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2"></path>
                                </svg>
                                服务对象信息
                                <button class="ml-auto bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600">
                                    添加服务对象
                                </button>
                            </h3>
                            <div class="space-y-3">
                                <div class="bg-white p-4 rounded-md border border-gray-200">
                                    <div class="grid grid-cols-2 gap-3">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="单位名称">
                                        <input type="text" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="联系人">
                                        <input type="text" class="col-span-2 px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="单位地址">
                                        <input type="tel" class="px-3 py-2 border border-gray-300 rounded-md text-sm" placeholder="联系电话">
                                        <textarea class="col-span-2 px-3 py-2 border border-gray-300 rounded-md text-sm" rows="2" placeholder="服务内容"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 科技服务协议 -->
                        <div class="bg-gray-50 rounded-lg p-6 col-span-2">
                            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                科技服务协议及任务说明
                            </h3>
                            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">协议主要内容</label>
                                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4" placeholder="请描述协议主要内容"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">服务目标</label>
                                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4" placeholder="请描述服务目标"></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">预期成效</label>
                                    <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="4" placeholder="请描述预期成效"></textarea>
                                </div>
                            </div>
                            <div class="mt-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">协议文件上传</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="mt-4">
                                        <label class="cursor-pointer">
                                            <span class="mt-2 block text-sm font-medium text-gray-900">点击上传协议文件</span>
                                            <input type="file" class="sr-only" multiple accept=".pdf,.doc,.docx">
                                        </label>
                                        <p class="mt-1 text-xs text-gray-500">支持 PDF、DOC、DOCX 格式</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="col-span-2 flex justify-end space-x-4 pt-6 border-t border-gray-200">
                            <button class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                                保存草稿
                            </button>
                            <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                提交申报
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 批次管理选项卡 -->
                <div id="batch" class="tab-content hidden">
                    <div class="space-y-6">
                        <!-- 批次信息卡片 -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h3 class="text-lg font-semibold text-blue-800 mb-2">当前申报批次</h3>
                                    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">年度</label>
                                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                                <option>2024年</option>
                                                <option>2023年</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">批次名称</label>
                                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="第一批次">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">申报开始时间</label>
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-1">申报结束时间</label>
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        启用中
                                    </span>
                                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                                        新建批次
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 批次列表 -->
                        <div class="bg-white rounded-lg border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">批次管理列表</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年度</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">批次名称</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报时间段</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报数量</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024年</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第一批次</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-01 至 2024-03-31</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    启用中
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-01-01</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">编辑</button>
                                                <button class="text-red-600 hover:text-red-900">停用</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023年</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第二批次</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-07-01 至 2023-09-30</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    已结束
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">28</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-07-01</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                                                <button class="text-green-600 hover:text-green-900">导出</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 审核管理选项卡 -->
                <div id="review" class="tab-content hidden">
                    <div class="space-y-6">
                        <!-- 筛选条件 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">审核状态</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>全部状态</option>
                                        <option>待审核</option>
                                        <option>审核中</option>
                                        <option>已通过</option>
                                        <option>已退回</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报批次</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>全部批次</option>
                                        <option>2024年第一批次</option>
                                        <option>2023年第二批次</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报单位</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="搜索申报单位">
                                </div>
                                <div class="flex items-end">
                                    <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                        搜索
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 审核列表 -->
                        <div class="bg-white rounded-lg border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">待审核项目列表</h3>
                            </div>
                            <div class="divide-y divide-gray-200">
                                <!-- 审核项目卡片 -->
                                <div class="p-6">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3 mb-3">
                                                <h4 class="text-lg font-medium text-gray-900">智慧农业科技服务团队</h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    待审核
                                                </span>
                                            </div>
                                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                                                <div>
                                                    <span class="font-medium">申报单位：</span>
                                                    <span>宁波农业科技有限公司</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">特色产业：</span>
                                                    <span>现代农业</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">团队负责人：</span>
                                                    <span>张三</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">申报时间：</span>
                                                    <span>2024-01-15</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <span class="font-medium text-sm text-gray-700">团队成员：</span>
                                                <span class="text-sm text-gray-600">张三（负责人）、李四（技术专家）、王五（产业顾问）等5人</span>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2 ml-4">
                                            <button class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700">
                                                审核
                                            </button>
                                            <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">
                                                查看详情
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="p-6">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-3 mb-3">
                                                <h4 class="text-lg font-medium text-gray-900">新材料技术创新团队</h4>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    审核中
                                                </span>
                                            </div>
                                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                                                <div>
                                                    <span class="font-medium">申报单位：</span>
                                                    <span>宁波新材料研究院</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">特色产业：</span>
                                                    <span>新材料</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">团队负责人：</span>
                                                    <span>赵六</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium">申报时间：</span>
                                                    <span>2024-01-10</span>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <span class="font-medium text-sm text-gray-700">团队成员：</span>
                                                <span class="text-sm text-gray-600">赵六（负责人）、钱七（材料专家）、孙八（工艺工程师）等8人</span>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2 ml-4">
                                            <button class="bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700">
                                                通过
                                            </button>
                                            <button class="bg-red-600 text-white px-4 py-2 rounded-md text-sm hover:bg-red-700">
                                                退回
                                            </button>
                                            <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm hover:bg-gray-50">
                                                查看详情
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 历史归档选项卡 -->
                <div id="archive" class="tab-content hidden">
                    <div class="space-y-6">
                        <!-- 搜索和筛选 -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">年度</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>全部年度</option>
                                        <option>2024年</option>
                                        <option>2023年</option>
                                        <option>2022年</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">批次</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>全部批次</option>
                                        <option>第一批次</option>
                                        <option>第二批次</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报单位</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="搜索申报单位">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">特色产业</label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option>全部产业</option>
                                        <option>现代农业</option>
                                        <option>智能制造</option>
                                        <option>生物医药</option>
                                        <option>新材料</option>
                                    </select>
                                </div>
                                <div class="flex items-end space-x-2">
                                    <button class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                        搜索
                                    </button>
                                    <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                        导出
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 统计概览 -->
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="bg-white rounded-lg border border-gray-200 p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">总团队数</dt>
                                            <dd class="text-lg font-medium text-gray-900">156</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg border border-gray-200 p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">已通过</dt>
                                            <dd class="text-lg font-medium text-gray-900">142</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg border border-gray-200 p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">审核中</dt>
                                            <dd class="text-lg font-medium text-gray-900">8</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-lg border border-gray-200 p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0">
                                        <svg class="h-8 w-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-5 w-0 flex-1">
                                        <dl>
                                            <dt class="text-sm font-medium text-gray-500 truncate">已退回</dt>
                                            <dd class="text-lg font-medium text-gray-900">6</dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 归档列表 -->
                        <div class="bg-white rounded-lg border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-800">归档项目列表</h3>
                            </div>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">团队名称</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报单位</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">特色产业</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">团队负责人</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报批次</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智慧农业科技服务团队</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波农业科技有限公司</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">现代农业</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张三</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024年第一批次</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    已通过
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                                <button class="text-green-600 hover:text-green-900">下载档案</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">新材料技术创新团队</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波新材料研究院</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">新材料</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵六</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024年第一批次</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    已通过
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                                <button class="text-green-600 hover:text-green-900">下载档案</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">数字经济服务团队</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波数字科技公司</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">数字经济</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李四</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023年第二批次</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    已退回
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button class="text-blue-600 hover:text-blue-900 mr-3">查看详情</button>
                                                <button class="text-orange-600 hover:text-orange-900">查看退回原因</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换功能
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有活动状态
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
                        btn.classList.add('text-gray-500');
                    });

                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });

                    // 添加当前选项卡的活动状态
                    this.classList.add('active', 'text-blue-600', 'border-b-2', 'border-blue-600');
                    this.classList.remove('text-gray-500');

                    // 显示对应的内容
                    document.getElementById(targetTab).classList.remove('hidden');
                });
            });
        });
    </script>
</body>
</html>