<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技人才总数概览业务流程</text>

  <!-- 阶段一：数据汇聚与展示 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据汇聚与展示</text>
  
  <!-- 节点1: 用户访问 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问功能模块</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">系统自动触发数据汇聚</text>
  </g>

  <!-- 节点2: 数据汇聚 -->
  <g transform="translate(550, 240)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">汇聚全市科技人才数据</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">实时刷新概览卡片和分布大屏</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 220 700 240" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：交互筛选与联动 -->
  <text x="700" y="360" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：交互筛选与联动</text>

  <!-- 节点3: 类型切换 -->
  <g transform="translate(200, 390)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">类型切换区选择</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">用户选择感兴趣的人才类别</text>
  </g>

  <!-- 节点4: 数据联动 -->
  <g transform="translate(560, 390)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">界面数据全局联动</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">刷新概览卡片及分布数据</text>
  </g>

  <!-- 节点5: 点击查看 -->
  <g transform="translate(920, 390)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击卡片或区块</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">根据筛选条件展示清册</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 650 310 C 500 340, 400 360, 340 390" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 480 425 Q 520 425 560 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 840 425 Q 880 425 920 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与操作 -->
  <text x="700" y="540" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与操作</text>

  <!-- 节点6: 人员详情 -->
  <g transform="translate(200, 570)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击人员查看详情</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">科研背景、项目、获奖成果</text>
  </g>

  <!-- 节点7: 精细筛查 -->
  <g transform="translate(560, 570)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">精细筛查与导出</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">调整筛选条件或导出信息</text>
  </g>

  <!-- 连接线 5 -> 6 -->
  <path d="M 1060 460 C 1060 500, 500 500, 340 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="490" text-anchor="middle" font-size="12" fill="#555">点击人员</text>

  <!-- 连接线 5 -> 7 -->
  <path d="M 1060 460 C 1060 515, 840 515, 700 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="900" y="505" text-anchor="middle" font-size="12" fill="#555">筛查需求</text>

  <!-- 阶段四：系统记录与审计 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统记录与审计</text>
  
  <!-- 节点8: 操作日志 -->
  <g transform="translate(450, 750)" filter="url(#soft-shadow)">
      <rect width="500" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="250" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">系统全程记录操作日志</text>
      <text x="250" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-120">用户行为记录</tspan>
        <tspan dx="40">数据访问日志</tspan>
        <tspan dx="40">合规审计</tspan>
        <tspan dx="40">安全追溯</tspan>
      </text>
  </g>

  <!-- 连接线 6 -> 8 -->
  <path d="M 340 640 C 340 680, 550 720, 600 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 7 -> 8 -->
  <path d="M 700 640 C 700 680, 750 720, 800 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从操作日志回到数据汇聚 -->
  <path d="M 950 790 C 1200 790, 1200 275, 850 275" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1100" y="780" text-anchor="middle" font-size="12" fill="#555">数据优化反馈</text>

  <!-- 反馈循环：从详情查看回到类型切换 -->
  <path d="M 200 605 C 100 605, 100 425, 200 425" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="515" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 100, 515)">重新筛选</text>

</svg>