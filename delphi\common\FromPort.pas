unit FromPort;

interface
uses
    Classes;

type
    TFromPort = class
    private
        FFromPortID: integer;
        FFromPortName: string;
        FFromPortDes: string;
        FMarketTime: string;
        FMarketUser: string;
        FState: string;
    public
        property FromPortID: integer read FFromPortID write FFromPortID;
        property FromPortName: string read FFromPortName write FFromPortName;
        property FromPortDes: string read FFromPortDes write FFromPortDes;
        property MarketTime: string read FMarketTime write FMarketTime;
        property MarketUser:string read FMarketUser write FMarketUser;
        property State: string read FState write FState;
    end;

implementation

end.

