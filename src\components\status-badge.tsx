import { Badge } from "@/components/ui/badge"

export function StatusBadge({ status }: { status: string }) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
      case '成功':
        return 'bg-green-100 text-green-800 hover:bg-green-100'
      case 'failed':
      case '失败':
        return 'bg-red-100 text-red-800 hover:bg-red-100'
      case 'pending':
      case '进行中':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100'
    }
  }

  return (
    <Badge 
      variant="outline" 
      className={`${getStatusColor(status)} border-0`}
    >
      {status}
    </Badge>
  )
} 