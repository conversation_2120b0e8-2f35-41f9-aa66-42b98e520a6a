unit CcYsSzbdj;

interface
uses
  Classes;

type
  TCcYsSzbdj = class
  private
    FYsszbdjid: Integer;
    FYsid: Integer;
    FSzbpmid: string;
    FSzbggid: string;
    FTgs: Double;
    FPbj: Double;
    FZl: Double;
    FRsj: Double;
    FSzbcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysszbdjid: integer read FYsszbdjid write FYsszbdjid;
    property Ysid: integer read FYsid write FYsid;
    property Szbpmid: string read FSzbpmid write FSzbpmid;
    property Szbggid: string read FSzbggid write FSzbggid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Zl: double read FZl write FZl;
    property Rsj: double read FRsj write FRsj;
    property Szbcb: double read FSzbcb write FSzbcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

