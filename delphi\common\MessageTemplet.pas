unit MessageTemplet;

interface

uses
  Classes;

type
  TMessageTemplet = class
  private
    FMessageTempletid: integer;
    FMessageTempletType: string;
    FMessageTempletName: string;
  public
    property MessageTempletid: integer read FMessageTempletid
      write FMessageTempletid;
    property MessageTempletType: string read FMessageTempletType
      write FMessageTempletType;
    property MessageTempletName: string read FMessageTempletName
      write FMessageTempletName;
  end;

implementation

end.
