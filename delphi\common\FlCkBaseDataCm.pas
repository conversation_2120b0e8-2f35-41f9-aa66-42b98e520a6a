unit FlCkBaseDataCm;

interface

uses
  Classes;

type
  TFlCkBaseDataCm = class
  private
    FBasedataid: integer;
    FKsid: integer;
    FKh: string;
    FKsbm: string;
    FSjh: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FCm: string;
    FItem_1_a: string;
    FItem_2_a: string;
    FItem_3_a: string;
    FItem_4_a: string;
    FItem_5_a: string;
    FDjyl_a: double;
    FItem_1_b: string;
    FItem_2_b: string;
    FItem_3_b: string;
    FItem_4_b: string;
    FItem_5_b: string;
    FDjyl_b: double;
    FItem_1_c: string;
    FItem_2_c: string;
    FItem_3_c: string;
    FItem_4_c: string;
    FItem_5_c: string;
    FDjyl_c: double;
    FItem_1_d: string;
    FItem_2_d: string;
    FItem_3_d: string;
    FItem_4_d: string;
    FItem_5_d: string;
    FDjyl_d: double;
    FItem_1_e: string;
    FItem_2_e: string;
    FItem_3_e: string;
    FItem_4_e: string;
    FItem_5_e: string;
    FDjyl_e: double;
    FItem_1_f: string;
    FItem_2_f: string;
    FItem_3_f: string;
    FItem_4_f: string;
    FItem_5_f: string;
    FDjyl_f: double;
    FItem_1_g: string;
    FItem_2_g: string;
    FItem_3_g: string;
    FItem_4_g: string;
    FItem_5_g: string;
    FDjyl_g: double;
    FItem_1_h: string;
    FItem_2_h: string;
    FItem_3_h: string;
    FItem_4_h: string;
    FItem_5_h: string;
    FDjyl_h: double;
    FItem_1_i: string;
    FItem_2_i: string;
    FItem_3_i: string;
    FItem_4_i: string;
    FItem_5_i: string;
    FDjyl_i: double;
    FItem_1_j: string;
    FItem_2_j: string;
    FItem_3_j: string;
    FItem_4_j: string;
    FItem_5_j: string;
    FDjyl_j: double;

    FPicNo: string;
    FPicSuffix: string;

    FFlconfigtypeid: integer;
    FFlconfigsectypeid: integer;
    FFlconfigtypename: string;
    FFlconfigsectypename: string;
    FShortname: string;
    FDwjd: integer;
    FJjdw: string;
    FItem_1: string;
    FItem_2: string;
    FItem_3: string;
    FItem_4: string;
    FItem_5: string;
    FDjyl: double;
    FCmgl: integer;
    FSecxh: integer;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Basedataid: integer read FBasedataid write FBasedataid;
    property Ksid: integer read FKsid write FKsid;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Kh: string read FKh write FKh;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Cm: string read FCm write FCm;

    property Item_1_a: string read FItem_1_a write FItem_1_a;
    property Item_2_a: string read FItem_2_a write FItem_2_a;
    property Item_3_a: string read FItem_3_a write FItem_3_a;
    property Item_4_a: string read FItem_4_a write FItem_4_a;
    property Item_5_a: string read FItem_5_a write FItem_5_a;
    property Djyl_a: double read FDjyl_a write FDjyl_a;

    property Item_1_b: string read FItem_1_b write FItem_1_b;
    property Item_2_b: string read FItem_2_b write FItem_2_b;
    property Item_3_b: string read FItem_3_b write FItem_3_b;
    property Item_4_b: string read FItem_4_b write FItem_4_b;
    property Item_5_b: string read FItem_5_b write FItem_5_b;
    property Djyl_b: double read FDjyl_b write FDjyl_b;

    property Item_1_c: string read FItem_1_c write FItem_1_c;
    property Item_2_c: string read FItem_2_c write FItem_2_c;
    property Item_3_c: string read FItem_3_c write FItem_3_c;
    property Item_4_c: string read FItem_4_c write FItem_4_c;
    property Item_5_c: string read FItem_5_c write FItem_5_c;
    property Djyl_c: double read FDjyl_c write FDjyl_c;

    property Item_1_d: string read FItem_1_d write FItem_1_d;
    property Item_2_d: string read FItem_2_d write FItem_2_d;
    property Item_3_d: string read FItem_3_d write FItem_3_d;
    property Item_4_d: string read FItem_4_d write FItem_4_d;
    property Item_5_d: string read FItem_5_d write FItem_5_d;
    property Djyl_d: double read FDjyl_d write FDjyl_d;

    property Item_1_e: string read FItem_1_e write FItem_1_e;
    property Item_2_e: string read FItem_2_e write FItem_2_e;
    property Item_3_e: string read FItem_3_e write FItem_3_e;
    property Item_4_e: string read FItem_4_e write FItem_4_e;
    property Item_5_e: string read FItem_5_e write FItem_5_e;
    property Djyl_e: double read FDjyl_e write FDjyl_e;

    property Item_1_f: string read FItem_1_f write FItem_1_f;
    property Item_2_f: string read FItem_2_f write FItem_2_f;
    property Item_3_f: string read FItem_3_f write FItem_3_f;
    property Item_4_f: string read FItem_4_f write FItem_4_f;
    property Item_5_f: string read FItem_5_f write FItem_5_f;
    property Djyl_f: double read FDjyl_f write FDjyl_f;

    property Item_1_g: string read FItem_1_g write FItem_1_g;
    property Item_2_g: string read FItem_2_g write FItem_2_g;
    property Item_3_g: string read FItem_3_g write FItem_3_g;
    property Item_4_g: string read FItem_4_g write FItem_4_g;
    property Item_5_g: string read FItem_5_g write FItem_5_g;
    property Djyl_g: double read FDjyl_g write FDjyl_g;

    property Item_1_h: string read FItem_1_h write FItem_1_h;
    property Item_2_h: string read FItem_2_h write FItem_2_h;
    property Item_3_h: string read FItem_3_h write FItem_3_h;
    property Item_4_h: string read FItem_4_h write FItem_4_h;
    property Item_5_h: string read FItem_5_h write FItem_5_h;
    property Djyl_h: double read FDjyl_h write FDjyl_h;

    property Item_1_i: string read FItem_1_i write FItem_1_i;
    property Item_2_i: string read FItem_2_i write FItem_2_i;
    property Item_3_i: string read FItem_3_i write FItem_3_i;
    property Item_4_i: string read FItem_4_i write FItem_4_i;
    property Item_5_i: string read FItem_5_i write FItem_5_i;
    property Djyl_i: double read FDjyl_i write FDjyl_i;

    property Item_1_j: string read FItem_1_j write FItem_1_j;
    property Item_2_j: string read FItem_2_j write FItem_2_j;
    property Item_3_j: string read FItem_3_j write FItem_3_j;
    property Item_4_j: string read FItem_4_j write FItem_4_j;
    property Item_5_j: string read FItem_5_j write FItem_5_j;
    property Djyl_j: double read FDjyl_j write FDjyl_j;
    property Cmgl: integer read FCmgl write FCmgl;
    property Secxh: integer read FSecxh write FSecxh;

    property PicNo: string read FPicNo write FPicNo;
    property PicSuffix: string read FPicSuffix write FPicSuffix;

    property Flconfigtypeid: integer read FFlconfigtypeid write FFlconfigtypeid;
    property Flconfigsectypeid: integer read FFlconfigsectypeid
      write FFlconfigsectypeid;
    property Flconfigtypename: string read FFlconfigtypename
      write FFlconfigtypename;
    property Shortname: string read FShortname write FShortname;
    property Flconfigsectypename: string read FFlconfigsectypename
      write FFlconfigsectypename;
    property Dwjd: integer read FDwjd write FDwjd;
    property Jjdw: string read FJjdw write FJjdw;

    property Item_1: string read FItem_1 write FItem_1;
    property Item_2: string read FItem_2 write FItem_2;
    property Item_3: string read FItem_3 write FItem_3;
    property Item_4: string read FItem_4 write FItem_4;
    property Item_5: string read FItem_5 write FItem_5;
    property Djyl: double read FDjyl write FDjyl;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
