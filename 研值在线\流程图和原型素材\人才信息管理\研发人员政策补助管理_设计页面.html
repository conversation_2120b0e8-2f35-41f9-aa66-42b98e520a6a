<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员政策补助管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">研发人员政策补助管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-3">补助信息检索</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="subsidyType" class="block text-sm font-medium text-gray-700 mb-0.5">补助类型</label>
                            <select id="subsidyType" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="research">科研项目补助</option>
                                <option value="talent">人才激励补助</option>
                                <option value="equipment">设备购置补助</option>
                                <option value="patent">专利奖励补助</option>
                                <option value="other">其他补助</option>
                            </select>
                        </div>
                        <div>
                            <label for="subsidyYear" class="block text-sm font-medium text-gray-700 mb-0.5">补助年度</label>
                            <select id="subsidyYear" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部年度</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                                <option value="2021">2021年</option>
                                <option value="2020">2020年</option>
                            </select>
                        </div>
                        <div>
                            <label for="amountRange" class="block text-sm font-medium text-gray-700 mb-0.5">金额区间</label>
                            <select id="amountRange" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部金额</option>
                                <option value="0-5">5万元以下</option>
                                <option value="5-10">5-10万元</option>
                                <option value="10-50">10-50万元</option>
                                <option value="50-100">50-100万元</option>
                                <option value="100+">100万元以上</option>
                            </select>
                        </div>
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-0.5">发放状态</label>
                            <select id="status" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="pending">待发放</option>
                                <option value="distributed">已发放</option>
                                <option value="rejected">已驳回</option>
                                <option value="revoked">已撤销</option>
                            </select>
                        </div>
                        <div>
                            <label for="department" class="block text-sm font-medium text-gray-700 mb-0.5">所属部门</label>
                            <select id="department" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部部门</option>
                                <option value="tech">科技局</option>
                                <option value="edu">教育局</option>
                                <option value="finance">财政局</option>
                                <option value="industry">工业和信息化局</option>
                                <option value="other">其他部门</option>
                            </select>
                        </div>
                        <div>
                            <label for="keyword" class="block text-sm font-medium text-gray-700 mb-0.5">关键词搜索</label>
                            <input type="text" id="keyword" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="补助内容、编号或人员姓名">
                        </div>
                    </div>
                    <div class="mt-3 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3 flex-shrink-0">
                    <!-- 补助项目列表区 -->
                    <div class="w-full flex-1 flex flex-col">
                        <!-- 列表标题栏 -->
                        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-800">补助项目列表</h2>
                            <div class="flex space-x-3">
                                <button onclick="openExportDropdown()" class="px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                    </svg>
                                    导出
                                </button>
                                <div id="exportDropdown" class="hidden absolute right-6 mt-10 bg-white rounded-md shadow-lg border border-gray-200 z-10 w-40">
                                    <div class="py-1">
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为PDF</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                    </div>
                                </div>
                                <button onclick="openModal('addSubsidyModal')" class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    新增补助
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助编号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助内容</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助金额(万元)</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助年度</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发放状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB-2024-001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市重点实验室建设补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科研项目补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">150.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已发放</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB-2024-002</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">领军型创新创业团队资助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">人才激励补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">200.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-100 text-yellow-800">待发放</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB-2023-156</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">高端质谱仪购置补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">设备购置补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">85.50</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已发放</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB-2023-157</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">发明专利奖励</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">专利奖励补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3.50</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">已驳回</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">NB-2023-158</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">青年科技人才培养计划</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">人才激励补助</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12.00</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已发放</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900 transition-colors">编辑</button>
                                            <button onclick="confirmDelete()" class="text-red-600 hover:text-red-900 transition-colors">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- 分页区域 -->
                        <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between bg-gray-50">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">128</span> 条记录
                            </div>
                            <div class="flex space-x-1">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 transition-colors">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 transition-colors">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 transition-colors">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-500 hover:bg-gray-50 transition-colors">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="w-full lg:w-1/4 lg:min-w-[280px] space-y-4">
                <!-- 统计信息卡片 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">补助统计</h3>
                    <div class="space-y-6">
                        <!-- 补助金额统计 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">补助金额统计</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">总金额</span>
                                    <span class="text-lg font-bold text-gray-900">¥12,845.60万</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">2024年已发放</span>
                                    <span class="text-sm font-medium text-gray-900">¥3,568.20万</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">待发放金额</span>
                                    <span class="text-sm font-medium text-gray-900">¥1,245.80万</span>
                                </div>
                            </div>
                        </div>

                        <!-- 补助类型分布 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">补助类型分布</h4>
                            <div class="h-40">
                                <canvas id="subsidyTypeChart"></canvas>
                            </div>
                        </div>

                        <!-- 发放状态统计 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">发放状态统计</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">已发放</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 75%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">75%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">待发放</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 15%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">15%</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">已驳回/撤销</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 10%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">10%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区域 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-800">批量导入</h3>
                    </div>
                    <div class="p-4 space-y-4">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                下载补助导入模板
                            </a>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div id="upload-progress" class="hidden">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-gray-600">上传进度</span>
                                <span class="text-gray-600">75%</span>
                            </div>
                            <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增补助弹窗 -->
    <div id="addSubsidyModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增研发人员政策补助</h3>
                    <button onclick="closeModal('addSubsidyModal')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="subsidyNo" class="block text-sm font-medium text-gray-700 mb-1">补助编号</label>
                            <input type="text" id="subsidyNo" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="系统自动生成">
                        </div>
                        <div>
                            <label for="subsidyYear" class="block text-sm font-medium text-gray-700 mb-1">补助年度 <span class="text-red-500">*</span></label>
                            <select id="subsidyYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择年度</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                                <option value="2021">2021年</option>
                                <option value="2020">2020年</option>
                            </select>
                        </div>
                        <div>
                            <label for="subsidyContent" class="block text-sm font-medium text-gray-700 mb-1">补助内容 <span class="text-red-500">*</span></label>
                            <input type="text" id="subsidyContent" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入补助内容" required>
                        </div>
                        <div>
                            <label for="subsidyCategory" class="block text-sm font-medium text-gray-700 mb-1">补助类型 <span class="text-red-500">*</span></label>
                            <select id="subsidyCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择类型</option>
                                <option value="research">科研项目补助</option>
                                <option value="talent">人才激励补助</option>
                                <option value="equipment">设备购置补助</option>
                                <option value="patent">专利奖励补助</option>
                                <option value="other">其他补助</option>
                            </select>
                        </div>
                        <div>
                            <label for="subsidyAmount" class="block text-sm font-medium text-gray-700 mb-1">补助金额(万元) <span class="text-red-500">*</span></label>
                            <input type="number" id="subsidyAmount" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入补助金额" step="0.01" min="0" required>
                        </div>
                        <div>
                            <label for="subsidyDepartment" class="block text-sm font-medium text-gray-700 mb-1">所属部门 <span class="text-red-500">*</span></label>
                            <select id="subsidyDepartment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择部门</option>
                                <option value="tech">科技局</option>
                                <option value="edu">教育局</option>
                                <option value="finance">财政局</option>
                                <option value="industry">工业和信息化局</option>
                                <option value="other">其他部门</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="recipientName" class="block text-sm font-medium text-gray-700 mb-1">受助人员姓名 <span class="text-red-500">*</span></label>
                        <input type="text" id="recipientName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入受助人员姓名" required>
                    </div>
                    <div>
                        <label for="recipientUnit" class="block text-sm font-medium text-gray-700 mb-1">受助人员单位</label>
                        <input type="text" id="recipientUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入受助人员单位">
                    </div>
                    <div>
                        <label for="subsidyStatus" class="block text-sm font-medium text-gray-700 mb-1">发放状态 <span class="text-red-500">*</span></label>
                        <select id="subsidyStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <option value="pending">待发放</option>
                            <option value="distributed">已发放</option>
                            <option value="rejected">已驳回</option>
                            <option value="revoked">已撤销</option>
                        </select>
                    </div>
                    <div>
                        <label for="subsidyDesc" class="block text-sm font-medium text-gray-700 mb-1">补助说明</label>
                        <textarea id="subsidyDesc" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入补助详细说明"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('addSubsidyModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑补助弹窗 -->
    <div id="editModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑研发人员政策补助</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="editSubsidyNo" class="block text-sm font-medium text-gray-700 mb-1">补助编号</label>
                            <input type="text" id="editSubsidyNo" class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50" value="NB-2024-002" readonly>
                        </div>
                        <div>
                            <label for="editSubsidyYear" class="block text-sm font-medium text-gray-700 mb-1">补助年度 <span class="text-red-500">*</span></label>
                            <select id="editSubsidyYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择年度</option>
                                <option value="2024" selected>2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                                <option value="2021">2021年</option>
                                <option value="2020">2020年</option>
                            </select>
                        </div>
                        <div>
                            <label for="editSubsidyContent" class="block text-sm font-medium text-gray-700 mb-1">补助内容 <span class="text-red-500">*</span></label>
                            <input type="text" id="editSubsidyContent" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="领军型创新创业团队资助" required>
                        </div>
                        <div>
                            <label for="editSubsidyCategory" class="block text-sm font-medium text-gray-700 mb-1">补助类型 <span class="text-red-500">*</span></label>
                            <select id="editSubsidyCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择类型</option>
                                <option value="research">科研项目补助</option>
                                <option value="talent" selected>人才激励补助</option>
                                <option value="equipment">设备购置补助</option>
                                <option value="patent">专利奖励补助</option>
                                <option value="other">其他补助</option>
                            </select>
                        </div>
                        <div>
                            <label for="editSubsidyAmount" class="block text-sm font-medium text-gray-700 mb-1">补助金额(万元) <span class="text-red-500">*</span></label>
                            <input type="number" id="editSubsidyAmount" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="200.00" step="0.01" min="0" required>
                        </div>
                        <div>
                            <label for="editSubsidyDepartment" class="block text-sm font-medium text-gray-700 mb-1">所属部门 <span class="text-red-500">*</span></label>
                            <select id="editSubsidyDepartment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择部门</option>
                                <option value="tech" selected>科技局</option>
                                <option value="edu">教育局</option>
                                <option value="finance">财政局</option>
                                <option value="industry">工业和信息化局</option>
                                <option value="other">其他部门</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="editRecipientName" class="block text-sm font-medium text-gray-700 mb-1">受助人员姓名 <span class="text-red-500">*</span></label>
                        <input type="text" id="editRecipientName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张明" required>
                    </div>
                    <div>
                        <label for="editRecipientUnit" class="block text-sm font-medium text-gray-700 mb-1">受助人员单位</label>
                        <input type="text" id="editRecipientUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市科技创新研究院">
                    </div>
                    <div>
                        <label for="editSubsidyStatus" class="block text-sm font-medium text-gray-700 mb-1">发放状态 <span class="text-red-500">*</span></label>
                        <select id="editSubsidyStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            <option value="pending" selected>待发放</option>
                            <option value="distributed">已发放</option>
                            <option value="rejected">已驳回</option>
                            <option value="revoked">已撤销</option>
                        </select>
                    </div>
                    <div>
                        <label for="editSubsidyDesc" class="block text-sm font-medium text-gray-700 mb-1">补助说明</label>
                        <textarea id="editSubsidyDesc" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">根据《宁波市领军型创新创业团队引进培育计划实施细则》，对张明团队给予200万元资助，用于新能源材料研发项目。</textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 补助详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">补助项目详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">补助编号：</span>
                                <span class="font-medium text-gray-900">NB-2024-001</span>
                            </div>
                            <div>
                                <span class="text-gray-500">补助年度：</span>
                                <span class="font-medium text-gray-900">2024年</span>
                            </div>
                            <div>
                                <span class="text-gray-500">补助内容：</span>
                                <span class="font-medium text-gray-900">宁波市重点实验室建设补助</span>
                            </div>
                            <div>
                                <span class="text-gray-500">补助类型：</span>
                                <span class="font-medium text-gray-900">科研项目补助</span>
                            </div>
                            <div>
                                <span class="text-gray-500">补助金额：</span>
                                <span class="font-medium text-gray-900">150.00万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">所属部门：</span>
                                <span class="font-medium text-gray-900">科技局</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发放状态：</span>
                                <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">已发放</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发放日期：</span>
                                <span class="font-medium text-gray-900">2024-01-15</span>
                            </div>
                        </div>
                    </div>

                    <!-- 受助人员信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-md font-medium text-gray-900 mb-4">受助人员信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">姓名：</span>
                                <span class="font-medium text-gray-900">李华</span>
                            </div>
                            <div>
                                <span class="text-gray-500">性别：</span>
                                <span class="font-medium text-gray-900">男</span>
                            </div>
                            <div>
                                <span class="text-gray-500">工作单位：</span>
                                <span class="font-medium text-gray-900">宁波大学材料科学与工程学院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">职称：</span>
                                <span class="font-medium text-gray-900">教授</span>
                            </div>
                            <div>
                                <span class="text-gray-500">联系电话：</span>
                                <span class="font-medium text-gray-900">13800138000</span>
                            </div>
                            <div>
                                <span class="text-gray-500">电子邮箱：</span>
                                <span class="font-medium text-gray-900"><EMAIL></span>
                            </div>
                        </div>
                    </div>

                    <!-- 补助说明 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-md font-medium text-gray-900 mb-4">补助说明</h4>
                        <div class="text-sm text-gray-700">
                            <p>根据《宁波市重点实验室建设与运行管理办法》（甬科计〔2023〕12号）文件精神，对宁波大学材料科学与工程学院李华教授团队建设的"宁波市先进材料重点实验室"给予150万元建设补助。</p>
                            <p class="mt-2">补助资金主要用于实验室设备购置、人才引进和科研活动开展。要求项目单位严格按照预算执行，确保专款专用，提高资金使用效益。</p>
                        </div>
                    </div>

                    <!-- 发放流程记录 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-md font-medium text-gray-900 mb-4">发放流程记录</h4>
                        <div class="space-y-4 text-sm">
                            <div class="flex">
                                <div class="mr-4 flex flex-col items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div class="w-0.5 h-full bg-gray-200 mt-1"></div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">补助发放完成</div>
                                    <div class="text-gray-500">2024-01-15 10:30</div>
                                    <div class="text-gray-700 mt-1">财务部门已完成转账，补助资金150.00万元已发放至宁波大学指定账户。</div>
                                </div>
                            </div>
                            <div class="flex">
                                <div class="mr-4 flex flex-col items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div class="w-0.5 h-full bg-gray-200 mt-1"></div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">财务审核通过</div>
                                    <div class="text-gray-500">2024-01-10 14:15</div>
                                    <div class="text-gray-700 mt-1">补助申请材料审核通过，符合财务管理规定，同意发放。</div>
                                </div>
                            </div>
                            <div class="flex">
                                <div class="mr-4 flex flex-col items-center">
                                    <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div class="w-0.5 h-full bg-gray-200 mt-1"></div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">业务部门审核通过</div>
                                    <div class="text-gray-500">2024-01-05 09:45</div>
                                    <div class="text-gray-700 mt-1">经审核，宁波大学材料科学与工程学院李华教授团队符合宁波市重点实验室建设补助条件，同意上报。</div>
                                </div>
                            </div>
                            <div class="flex">
                                <div class="mr-4 flex flex-col items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                                        </svg>
                                    </div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">补助申请提交</div>
                                    <div class="text-gray-500">2023-12-20 16:20</div>
                                    <div class="text-gray-700 mt-1">宁波大学提交重点实验室建设补助申请，申请金额150万元。</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附件信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-md font-medium text-gray-900 mb-4">相关附件</h4>
                        <div class="space-y-2 text-sm">
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                                宁波市重点实验室建设补助申请表.docx
                            </a>
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                                宁波大学重点实验室建设可行性研究报告.pdf
                            </a>
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-900 transition-colors">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                </svg>
                                补助资金使用计划明细表.xlsx
                            </a>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end pt-6">
                    <button type="button" onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // 导出下拉菜单
        function openExportDropdown() {
            const dropdown = document.getElementById('exportDropdown');
            if (dropdown.classList.contains('hidden')) {
                dropdown.classList.remove('hidden');
            } else {
                dropdown.classList.add('hidden');
            }
        }

        // 删除确认
        function confirmDelete() {
            if (confirm('确定要删除这条补助记录吗？此操作不可恢复。')) {
                alert('删除成功');
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {

            // 初始化补助类型分布图表
            const subsidyTypeCtx = document.getElementById('subsidyTypeChart');
            if (subsidyTypeCtx) {
                const ctx = subsidyTypeCtx.getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['科研项目补助', '人才激励补助', '设备购置补助', '专利奖励补助', '其他补助'],
                        datasets: [{
                            data: [45, 25, 15, 10, 5],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(239, 68, 68, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ],
                            borderColor: [
                                'rgba(59, 130, 246, 1)',
                                'rgba(16, 185, 129, 1)',
                                'rgba(245, 158, 11, 1)',
                                'rgba(239, 68, 68, 1)',
                                'rgba(107, 114, 128, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10,
                                    font: {
                                        size: 10
                                    }
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        return context.label + ': ' + context.raw + '%';
                                    }
                                }
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // 文件上传处理
            document.getElementById('file-upload').addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    document.getElementById('upload-progress').classList.remove('hidden');
                    // 模拟上传进度
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 10;
                        document.querySelector('#upload-progress .bg-blue-600').style.width = progress + '%';
                        document.querySelector('#upload-progress span:last-child').textContent = progress + '%';
                        if (progress >= 100) {
                            clearInterval(interval);
                            setTimeout(() => {
                                document.getElementById('upload-progress').classList.add('hidden');
                                alert('文件上传成功！');
                            }, 500);
                        }
                    }, 200);
                }
            });

            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', function(e) {
                const exportDropdown = document.getElementById('exportDropdown');
                const exportButton = document.querySelector('[onclick="openExportDropdown()"]');
                
                if (exportDropdown && !exportDropdown.classList.contains('hidden') && 
                    !exportDropdown.contains(e.target) && !exportButton.contains(e.target)) {
                    exportDropdown.classList.add('hidden');
                }
            });

            // 为所有弹窗绑定“点击外部关闭”事件和ESC键关闭
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('操作成功 (原型演示)');
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>