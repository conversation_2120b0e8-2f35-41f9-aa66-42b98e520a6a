<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 错误箭头样式 -->
    <marker id="error-arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#e74c3c" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">地址匹配查询系统流程</text>

  <!-- 阶段一：任务创建与初始化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：任务创建与初始化 (Task Creation &amp; Initialization)</text>
  
  <!-- 节点1: 用户提交 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">查询条件 / 批量文件</text>
  </g>

  <!-- 节点2: 任务创建 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务创建</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">唯一编号 + "待处理"状态</text>
  </g>

  <!-- 连接线 用户提交 -> 任务创建 -->
  <path d="M 500 165 Q 550 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据校验 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据校验 (Data Validation)</text>

  <!-- 节点3: 完整性校验 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">完整性校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据格式 + 必填字段检查</text>
  </g>

  <!-- 节点4: 校验失败处理 -->
  <g transform="translate(1000, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFEBEE" stroke="#EF9A9A" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#e74c3c">校验失败</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#c0392b">状态"终止" + 错误信息</text>
    <text x="100" y="75" text-anchor="middle" font-size="12" fill="#c0392b">返回前端</text>
  </g>

  <!-- 连接线 任务创建 -> 完整性校验 -->
  <path d="M 700 200 Q 700 260 700 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 校验失败分支 -->
  <path d="M 800 360 Q 900 360 1000 360" stroke="#e74c3c" stroke-width="2" fill="none" marker-end="url(#error-arrow)" stroke-dasharray="5,5" />
  <text x="900" y="345" text-anchor="middle" font-size="12" fill="#e74c3c">校验失败</text>

  <!-- 阶段三：地址匹配处理 -->
  <text x="700" y="500" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：地址匹配处理 (Address Matching Process)</text>

  <!-- 节点5: 地址标准化引擎 -->
  <g transform="translate(400, 540)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">地址标准化引擎</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">规范化解析</text>
    <text x="110" y="75" text-anchor="middle" font-size="12" fill="#555">空间层级匹配</text>
  </g>

  <!-- 节点6: 临时缓存 -->
  <g transform="translate(700, 540)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">临时缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">中间处理结果</text>
    <text x="100" y="75" text-anchor="middle" font-size="12" fill="#555">实时存储</text>
  </g>

  <!-- 连接线 校验通过 -> 地址标准化 -->
  <path d="M 700 400 Q 650 470 510 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="600" y="470" text-anchor="middle" font-size="12" fill="#2ecc71">校验通过</text>

  <!-- 连接线 地址标准化 -> 临时缓存 -->
  <path d="M 620 580 Q 660 580 700 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：结果处理与通知 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：结果处理与通知 (Result Processing &amp; Notification)</text>

  <!-- 节点7: 结果处理 -->
  <g transform="translate(200, 760)" filter="url(#soft-shadow)">
    <rect width="250" height="100" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果处理</text>
    <text x="125" y="45" text-anchor="middle" font-size="12" fill="#555">计算置信度</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">去重合并同源地址</text>
    <text x="125" y="85" text-anchor="middle" font-size="12" fill="#555">生成最终结果集</text>
  </g>

  <!-- 节点8: 数据落盘 -->
  <g transform="translate(550, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="100" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据落盘</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">保存至数据库</text>
    <text x="100" y="80" text-anchor="middle" font-size="12" fill="#555">状态"已完成"</text>
  </g>

  <!-- 节点9: 消息通知 -->
  <g transform="translate(850, 760)" filter="url(#soft-shadow)">
    <rect width="250" height="100" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息通知</text>
    <text x="125" y="45" text-anchor="middle" font-size="12" fill="#555">推送任务完成事件</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">用户查看结果</text>
    <text x="125" y="85" text-anchor="middle" font-size="12" fill="#555">下载文件 / 新查询</text>
  </g>

  <!-- 连接线 临时缓存 -> 结果处理 -->
  <path d="M 750 620 C 650 680, 500 720, 325 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 结果处理 -> 数据落盘 -->
  <path d="M 450 810 Q 500 810 550 810" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据落盘 -> 消息通知 -->
  <path d="M 750 810 Q 800 810 850 810" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 流程标注 -->
  <text x="550" y="175" text-anchor="middle" font-size="11" fill="#666">任务初始化</text>
  <text x="700" y="450" text-anchor="middle" font-size="11" fill="#666">格式校验</text>
  <text x="660" y="590" text-anchor="middle" font-size="11" fill="#666">地址解析</text>
  <text x="650" y="870" text-anchor="middle" font-size="11" fill="#666">完成通知</text>

  <!-- 系统边界标记 -->
  <rect x="50" y="110" width="1300" height="800" rx="10" ry="10" fill="none" stroke="#ddd" stroke-width="1" stroke-dasharray="10,5" />
  <text x="60" y="130" font-size="12" fill="#999">系统边界</text>

</svg> 