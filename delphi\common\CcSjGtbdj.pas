unit CcSjGtbdj;

interface
uses
  Classes;

type
  TCcSjGtbdj = class
  private

    FSjgtbdjid: Integer;
    FDdid: Integer;
    FGtbgysid: string;
    FTgs: Double;
    FPbj: Double;
    FGtbcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjgtbdjid: integer read FSjgtbdjid write FSjgtbdjid;
    property Ddid: integer read FDdid write FDdid;
    property Gtbgysid: string read FGtbgysid write FGtbgysid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Gtbcb: double read FGtbcb write FGtbcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

