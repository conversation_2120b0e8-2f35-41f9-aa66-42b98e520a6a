<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">申报管理流程图</text>

  <!-- 阶段一：通知创建与配置 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：通知创建与配置</text>
  
  <!-- 节点1: 新增申报通知 -->
  <g transform="translate(250, 130)" filter="url(#soft-shadow)">
    <rect width="240" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="120" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增申报通知</text>
    <text x="120" y="55" text-anchor="middle" font-size="12" fill="#555">选派年度、对象类型、标题等</text>
  </g>

  <!-- 节点2: 信息校验 -->
  <g transform="translate(910, 130)" filter="url(#soft-shadow)">
    <rect width="240" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="120" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息校验保存</text>
    <text x="120" y="55" text-anchor="middle" font-size="12" fill="#555">完整性与逻辑有效性验证</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 490 165 Q 650 165, 910 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：模板配置与发布 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：模板配置与发布</text>

  <!-- 节点3: 字段模板配置 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="260" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="130" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段模板配置</text>
    <text x="130" y="55" text-anchor="middle" font-size="12" fill="#555">项目信息、申请人、服务对象</text>
  </g>

  <!-- 节点4: 发布通知 -->
  <g transform="translate(940, 310)" filter="url(#soft-shadow)">
    <rect width="260" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="130" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">发布申报通知</text>
    <text x="130" y="55" text-anchor="middle" font-size="12" fill="#555">状态设为已发布，开放申报</text>
  </g>

  <!-- 连接线 校验 -> 模板配置 -->
  <path d="M 910 200 C 750 230, 500 260, 330 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 模板配置 -> 发布 -->
  <path d="M 460 345 Q 700 345, 940 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：申报期管理与操作 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：申报期管理与操作</text>

  <!-- 节点5: 用户申报填报 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户申报填报</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">备案填报与申报信息提交</text>
  </g>

  <!-- 节点6: 系统记录数据 -->
  <g transform="translate(570, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统记录数据</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">时间、单位、服务对象明细</text>
  </g>

  <!-- 节点7: 撤销通知 -->
  <g transform="translate(970, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">撤销通知管理</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">隐藏入口，标记已撤销</text>
  </g>

  <!-- 连接线 发布 -> 用户申报 -->
  <path d="M 940 380 C 800 420, 500 450, 290 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 用户申报 -> 系统记录 -->
  <path d="M 430 525 Q 500 525, 570 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 发布 -> 撤销 -->
  <path d="M 1070 380 Q 1110 435, 1110 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：申报期结束与数据导出 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：申报期结束与数据导出</text>

  <!-- 节点8: 封闭入口 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">申报期结束封闭</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">根据设定规则自动封闭入口</text>
  </g>

  <!-- 节点9: 数据导出分析 -->
  <g transform="translate(800, 670)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出分析</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">评审、分派与绩效分析</text>
  </g>

  <!-- 连接线 系统记录 -> 封闭入口 -->
  <path d="M 570 560 C 500 600, 480 630, 450 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 封闭入口 -> 数据导出 -->
  <path d="M 600 705 Q 700 705, 800 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 节点10: 全流程监控 -->
  <g transform="translate(550, 800)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">全流程状态监控</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">实时跟踪申报通知状态</text>
  </g>

  <!-- 连接线 数据导出 -> 全流程监控 -->
  <path d="M 950 740 C 950 770, 800 800, 700 835" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从全流程监控回到新增申报 -->
  <path d="M 550 835 C 100 900, 100 100, 250 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="500" text-anchor="middle" font-size="11" fill="#666">持续优化</text>

  <!-- 反馈循环：从撤销管理回到模板配置 -->
  <path d="M 1250 525 C 1350 400, 1350 280, 460 345" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1350" y="400" text-anchor="middle" font-size="11" fill="#666">重新配置</text>

</svg>