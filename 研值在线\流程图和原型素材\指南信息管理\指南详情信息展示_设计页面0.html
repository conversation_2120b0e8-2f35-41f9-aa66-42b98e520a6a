<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指南详情信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100">
    <!-- 指南详情弹窗 -->
    <div id="guideDetailModal" class="fixed inset-y-0 right-0 w-full max-w-xl bg-white shadow-xl transform transition-transform duration-300 ease-in-out translate-x-full z-50">
        <div class="h-full flex flex-col">
            <!-- 标题区 -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <h2 class="text-xl font-bold text-gray-900 mr-3">宁波市智能制造关键技术研发指南</h2>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        征集中
                    </span>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                        </svg>
                    </button>
                    <button onclick="closeGuideDetail()" class="text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="relative">
                    <input type="text" placeholder="搜索指南内容..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- 内容区 -->
            <div class="flex-1 overflow-y-auto p-6 space-y-6">
                <!-- 基本信息区 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            基本信息
                        </h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-500">发布单位</p>
                                <p class="text-sm font-medium text-gray-900">宁波市科技局</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-500">技术领域</p>
                                <p class="text-sm font-medium text-gray-900">智能制造/工业互联网</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-sm text-gray-500">关联计划类别</p>
                                <p class="text-sm font-medium text-gray-900">宁波市重点研发计划</p>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 研究与考核区 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            研究内容
                        </h3>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-700">
                            <span class="font-medium text-blue-600">智能制造</span>领域关键技术研发，重点支持工业互联网平台、智能工厂/数字化车间、智能装备与系统等方向。研究内容包括：1）面向<span class="font-medium text-blue-600">宁波市</span>特色产业的工业互联网平台架构设计与应用；2）基于数字孪生的智能工厂建模与优化技术；3）高端装备智能感知与控制系统研发...
                            <span id="researchMore" class="hidden">4）智能生产线数字孪生系统集成与应用示范；5）面向中小企业的低成本智能化改造解决方案；6）智能装备核心零部件国产化替代研究。本指南重点支持宁波市"246"万千亿级产业集群相关技术研发，优先支持产学研联合申报。</span>
                        </p>
                        <button onclick="toggleResearchContent()" class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <span id="researchToggleText">展开更多</span>
                            <svg id="researchToggleIcon" class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                            </svg>
                            考核指标
                        </h3>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <p class="text-sm text-gray-700">
                            1）形成具有自主知识产权的工业互联网平台架构设计方案，并在<span class="font-medium text-blue-600">宁波市</span>2家以上企业应用；2）开发智能工厂数字孪生系统1套，实现生产流程优化效率提升20%以上；3）研制智能装备核心控制系统1套，关键指标达到国际先进水平...
                            <span id="assessmentMore" class="hidden">4）申请发明专利不少于5项，软件著作权不少于3项；5）形成行业标准或技术规范草案1-2项；6）项目期内实现技术成果转化收入不低于500万元。项目执行期为2年，中期检查需完成技术方案设计和关键模块开发。</span>
                        </p>
                        <button onclick="toggleAssessmentContent()" class="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <span id="assessmentToggleText">展开更多</span>
                            <svg id="assessmentToggleIcon" class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 申报要求区 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                            申报要求
                        </h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"></path>
                            </svg>
                            复制全部
                        </button>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg space-y-3">
                        <div>
                            <p class="text-sm font-medium text-gray-900">申报条件</p>
                            <p class="text-sm text-gray-700 mt-1">1）申报单位应为在<span class="font-medium text-blue-600">宁波市</span>注册的高新技术企业或科研院所；2）项目负责人应具有高级职称或博士学位；3）企业配套资金不低于财政资助金额的2倍；4）优先支持产学研联合申报。</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">资助额度</p>
                            <p class="text-sm text-gray-700 mt-1">单个项目财政资助金额50-100万元，特别优秀项目可适当增加。</p>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">截止时间</p>
                            <p class="text-sm text-gray-700 mt-1">2024年8月31日17:00（以系统提交时间为准）</p>
                        </div>
                    </div>
                </div>

                <!-- 专家与标引区 -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                            制定专家
                        </h3>
                    </div>
                    <div class="flex items-center space-x-3">
                        <div class="relative group">
                            <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-medium">
                                张
                            </div>
                            <div class="absolute z-10 left-0 mt-2 w-48 bg-white shadow-lg rounded-md p-3 hidden group-hover:block">
                                <p class="text-sm font-medium text-gray-900">张伟</p>
                                <p class="text-xs text-gray-500 mt-1">宁波大学智能制造研究所</p>
                                <p class="text-xs text-gray-500">研究方向：工业互联网</p>
                            </div>
                        </div>
                        <div class="relative group">
                            <div class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-600 font-medium">
                                王
                            </div>
                            <div class="absolute z-10 left-0 mt-2 w-48 bg-white shadow-lg rounded-md p-3 hidden group-hover:block">
                                <p class="text-sm font-medium text-gray-900">王芳</p>
                                <p class="text-xs text-gray-500 mt-1">宁波智能制造研究院</p>
                                <p class="text-xs text-gray-500">研究方向：智能装备</p>
                            </div>
                        </div>
                        <div class="relative group">
                            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-medium">
                                李
                            </div>
                            <div class="absolute z-10 left-0 mt-2 w-48 bg-white shadow-lg rounded-md p-3 hidden group-hover:block">
                                <p class="text-sm font-medium text-gray-900">李强</p>
                                <p class="text-xs text-gray-500 mt-1">宁波XX集团技术中心</p>
                                <p class="text-xs text-gray-500">研究方向：数字化车间</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                            </svg>
                            指南标引
                        </h3>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full hover:bg-blue-200">智能制造</button>
                        <button class="px-3 py-1 bg-green-100 text-green-800 text-xs rounded-full hover:bg-green-200">工业互联网</button>
                        <button class="px-3 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full hover:bg-yellow-200">数字孪生</button>
                        <button class="px-3 py-1 bg-purple-100 text-purple-800 text-xs rounded-full hover:bg-purple-200">智能装备</button>
                        <button class="px-3 py-1 bg-pink-100 text-pink-800 text-xs rounded-full hover:bg-pink-200">宁波246产业</button>
                    </div>
                </div>
            </div>

            <!-- 底部操作区 -->
            <div class="p-4 border-t border-gray-200 bg-gray-50">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    立即申报
                </button>
            </div>
        </div>
    </div>

    <script>
        // 打开指南详情弹窗
        function openGuideDetail() {
            document.getElementById('guideDetailModal').classList.remove('translate-x-full');
        }

        // 关闭指南详情弹窗
        function closeGuideDetail() {
            document.getElementById('guideDetailModal').classList.add('translate-x-full');
        }

        // 切换研究内容展开/收起
        function toggleResearchContent() {
            const content = document.getElementById('researchMore');
            const toggleText = document.getElementById('researchToggleText');
            const toggleIcon = document.getElementById('researchToggleIcon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                toggleText.textContent = '收起';
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                toggleText.textContent = '展开更多';
                toggleIcon.style.transform = 'rotate(0deg)';
            }
        }

        // 切换考核指标展开/收起
        function toggleAssessmentContent() {
            const content = document.getElementById('assessmentMore');
            const toggleText = document.getElementById('assessmentToggleText');
            const toggleIcon = document.getElementById('assessmentToggleIcon');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                toggleText.textContent = '收起';
                toggleIcon.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                toggleText.textContent = '展开更多';
                toggleIcon.style.transform = 'rotate(0deg)';
            }
        }

        // 模拟打开详情弹窗
        document.addEventListener('DOMContentLoaded', function() {
            openGuideDetail();
        });
    </script>
</body>
</html>