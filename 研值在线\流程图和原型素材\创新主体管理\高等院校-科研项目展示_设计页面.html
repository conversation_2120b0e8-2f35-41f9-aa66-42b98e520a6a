<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校-科研项目展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研项目展示</h1>

        <!-- 项目概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">纵向项目</p>
                        <p class="text-2xl font-bold">156</p>
                    </div>
                    <div class="w-20 h-16">
                        <canvas id="verticalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">横向项目</p>
                        <p class="text-2xl font-bold">89</p>
                    </div>
                    <div class="w-20 h-16">
                        <canvas id="horizontalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">在研项目</p>
                        <p class="text-2xl font-bold">203</p>
                    </div>
                    <div class="w-20 h-16">
                        <canvas id="researchChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">累计经费(万元)</p>
                        <p class="text-2xl font-bold">5,689</p>
                    </div>
                    <div class="w-20 h-16">
                        <canvas id="fundChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2">
                            纵向项目
                        </label>
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2">
                            横向项目
                        </label>
                        <label class="inline-flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 mr-2">
                            产学研联合
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="material">新材料</option>
                        <option value="energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目阶段</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center">
                            <input type="radio" name="stage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            全部
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="stage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            申报中
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="stage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            在研
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="stage" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            结题
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">执行年度</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" min="2010" max="2024" value="2021" class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span>至</span>
                        <input type="number" min="2010" max="2024" value="2024" class="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <div class="flex space-x-2">
                        <input type="text" placeholder="项目名称/负责人/编号" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            搜索
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 项目列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-800">项目列表</h2>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">阶段</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费(万)</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止日期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">智能制造关键技术研究</div>
                                        <div class="text-sm text-gray-500">宁波市科技计划项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">纵向</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">120</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023.01-2024.12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('projectDetail')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新能源汽车电池技术开发</div>
                                        <div class="text-sm text-gray-500">宁波市重点研发计划</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">纵向</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">申报中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024.03-2026.03</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('projectDetail')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">智能家居系统开发</div>
                                        <div class="text-sm text-gray-500">宁波XX科技合作项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">横向</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">中期检查</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">80</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023.06-2024.06</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('projectDetail')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新材料产业化应用研究</div>
                                        <div class="text-sm text-gray-500">产学研联合项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">横向</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">结题</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">150</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022.01-2023.12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('projectDetail')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">海洋生物医药研发</div>
                                        <div class="text-sm text-gray-500">国家自然科学基金</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">纵向</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">300</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022.09-2025.09</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('projectDetail')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">156</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目分析图表区 -->
            <div class="w-full lg:w-1/3 space-y-4">
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">立项趋势</h3>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">经费与产出</h3>
                    <div class="h-64">
                        <canvas id="outputChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="projectDetail" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-bold text-gray-900">项目详情</h3>
                <button onclick="closeModal('projectDetail')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 基础信息 -->
                <div class="space-y-4">
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">基础信息</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">项目名称：</span>
                                <span class="font-medium">智能制造关键技术研究</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目编号：</span>
                                <span class="font-medium">NB2023KJ001</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目类型：</span>
                                <span class="font-medium">纵向项目</span>
                            </div>
                            <div>
                                <span class="text-gray-500">项目来源：</span>
                                <span class="font-medium">宁波市科技计划项目</span>
                            </div>
                            <div>
                                <span class="text-gray-500">负责人：</span>
                                <span class="font-medium">张教授</span>
                            </div>
                            <div>
                                <span class="text-gray-500">所属单位：</span>
                                <span class="font-medium">宁波大学机械工程学院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">立项金额：</span>
                                <span class="font-medium">120万元</span>
                            </div>
                            <div>
                                <span class="text-gray-500">执行期限：</span>
                                <span class="font-medium">2023.01-2024.12</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 合作单位 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">合作单位</h4>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span>宁波XX科技有限公司</span>
                                <span class="text-gray-500">企业</span>
                            </div>
                            <div class="flex justify-between items-center p-2 bg-gray-50 rounded">
                                <span>宁波市智能制造研究院</span>
                                <span class="text-gray-500">科研机构</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 进度与成果 -->
                <div class="space-y-4">
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">项目进度</h4>
                        <div class="space-y-3">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">当前进度</span>
                                    <span class="text-sm font-medium text-gray-700">65%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 65%"></div>
                                </div>
                            </div>
                            <div class="grid grid-cols-3 gap-2 text-sm">
                                <div class="p-2 bg-blue-50 rounded text-center">
                                    <div class="font-medium">申报</div>
                                    <div class="text-xs text-gray-500">2023.01</div>
                                </div>
                                <div class="p-2 bg-blue-50 rounded text-center">
                                    <div class="font-medium">立项</div>
                                    <div class="text-xs text-gray-500">2023.03</div>
                                </div>
                                <div class="p-2 bg-blue-100 rounded text-center">
                                    <div class="font-medium">中期</div>
                                    <div class="text-xs text-gray-500">2023.12</div>
                                </div>
                                <div class="p-2 bg-gray-100 rounded text-center">
                                    <div class="font-medium">结题</div>
                                    <div class="text-xs text-gray-500">2024.12</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">阶段成果</h4>
                        <div class="space-y-2 text-sm">
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="font-medium">论文发表</div>
                                <div class="text-gray-500">已发表SCI论文2篇，EI论文1篇</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="font-medium">专利申请</div>
                                <div class="text-gray-500">申请发明专利3项，实用新型专利5项</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="font-medium">人才培养</div>
                                <div class="text-gray-500">培养硕士研究生5名，博士研究生2名</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-4 border-t flex justify-end space-x-3">
                <button onclick="closeModal('projectDetail')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    关闭
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    导出PDF
                </button>
                <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    跳转详情页
                </button>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览卡片图表
            const verticalCtx = document.getElementById('verticalChart').getContext('2d');
            new Chart(verticalCtx, {
                type: 'bar',
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        data: [30, 40, 60, 26],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            const horizontalCtx = document.getElementById('horizontalChart').getContext('2d');
            new Chart(horizontalCtx, {
                type: 'bar',
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        data: [20, 35, 18, 16],
                        backgroundColor: 'rgba(16, 185, 129, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            const researchCtx = document.getElementById('researchChart').getContext('2d');
            new Chart(researchCtx, {
                type: 'bar',
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        data: [45, 55, 68, 35],
                        backgroundColor: 'rgba(245, 158, 11, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            const fundCtx = document.getElementById('fundChart').getContext('2d');
            new Chart(fundCtx, {
                type: 'bar',
                data: {
                    labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                    datasets: [{
                        data: [1200, 1500, 1800, 1189],
                        backgroundColor: 'rgba(139, 92, 246, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    }
                }
            });

            // 初始化分析图表
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023', '2024'],
                    datasets: [
                        {
                            label: '纵向项目',
                            data: [25, 32, 40, 45, 50],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.3,
                            fill: true
                        },
                        {
                            label: '横向项目',
                            data: [15, 20, 25, 30, 35],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.3,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            const outputCtx = document.getElementById('outputChart').getContext('2d');
            new Chart(outputCtx, {
                type: 'scatter',
                data: {
                    datasets: [
                        {
                            label: '纵向项目',
                            data: [
                                {x: 50, y: 2},
                                {x: 80, y: 3},
                                {x: 120, y: 5},
                                {x: 150, y: 7},
                                {x: 200, y: 8}
                            ],
                            backgroundColor: '#3B82F6'
                        },
                        {
                            label: '横向项目',
                            data: [
                                {x: 30, y: 1},
                                {x: 50, y: 2},
                                {x: 80, y: 3},
                                {x: 100, y: 4},
                                {x: 150, y: 5}
                            ],
                            backgroundColor: '#10B981'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: '经费(万元)'
                            }
                        },
                        y: {
                            title: {
                                display: true,
                                text: '预期成果数'
                            }
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('projectDetail').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('projectDetail');
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeModal('projectDetail');
                }
            });
        });
    </script>
</body>
</html>