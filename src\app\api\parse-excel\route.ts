import { NextRequest, NextResponse } from 'next/server';
import * as XLSX from 'xlsx';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: '没有找到文件', success: false },
        { status: 400 }
      );
    }

    // 检查文件类型
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ];
    
    if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|csv)$/i)) {
      return NextResponse.json(
        { error: '不支持的文件格式，请上传Excel或CSV文件', success: false },
        { status: 400 }
      );
    }

    // 读取文件内容
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    let testCases: any[] = [];

    if (file.type === 'text/csv' || file.name.toLowerCase().endsWith('.csv')) {
      // 处理CSV文件
      const text = buffer.toString('utf-8');
      const lines = text.split('\n').filter(line => line.trim());
      
      if (lines.length < 2) {
        throw new Error('CSV文件格式错误，至少需要标题行和一行数据');
      }

      // 跳过标题行
      for (let i = 1; i < lines.length; i++) {
        const columns = lines[i].split(',').map(col => col.trim().replace(/^"|"$/g, ''));
        
        if (columns.length >= 5) {
          testCases.push({
            id: i,
            question: columns[0] || '',
            answer: columns[1] || '',
            scenarioDescription: columns[2] || '',
            testUrl: columns[3] || 'http://************:8081/v1',
            testKey: columns[4] || 'app-hXBah6VG2H7FcwuHQ6iSiI4A',
            modelTestResult: columns[5] || '',
            aiJudgment: columns[6] || '',
            status: 'pending'
          });
        }
      }
    } else {
      // 处理Excel文件
      console.log('正在解析Excel文件...');
      
      try {
        // 使用XLSX库解析Excel文件
        const workbook = XLSX.read(buffer, { type: 'buffer' });
        const firstSheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[firstSheetName];
        
        // 将工作表转换为JSON数组
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        if (jsonData.length < 2) {
          throw new Error('Excel文件格式错误，至少需要标题行和一行数据');
        }
        
        // 获取标题行，查找列索引
        const headers = jsonData[0] as string[];
        const questionIndex = headers.findIndex(h => h && h.includes('问题'));
        const answerIndex = headers.findIndex(h => h && h.includes('答案'));
        const scenarioIndex = headers.findIndex(h => h && h.includes('测试场景说明'));
        const urlIndex = headers.findIndex(h => h && h.includes('测试地址'));
        const keyIndex = headers.findIndex(h => h && h.includes('测试key'));
        
        console.log('找到的列索引:', {
          questionIndex, answerIndex, scenarioIndex, urlIndex, keyIndex
        });
        
        if (questionIndex === -1 || answerIndex === -1 || urlIndex === -1 || keyIndex === -1) {
          throw new Error('Excel文件缺少必要的列：问题、答案、测试地址、测试key');
        }
        
        // 跳过标题行，解析数据行
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i] as any[];
          
          // 检查行是否有数据
          if (!row || row.length === 0 || !row[questionIndex]) {
            continue;
          }
          
          testCases.push({
            id: i,
            question: String(row[questionIndex] || '').trim(),
            answer: String(row[answerIndex] || '').trim(),
            scenarioDescription: scenarioIndex >= 0 ? String(row[scenarioIndex] || '').trim() : '',
            testUrl: String(row[urlIndex] || '').trim() || 'http://************:8081/v1',
            testKey: String(row[keyIndex] || '').trim() || 'app-hXBah6VG2H7FcwuHQ6iSiI4A',
            modelTestResult: "",
            aiJudgment: "",
            status: 'pending'
          });
        }
        
        console.log(`成功解析${testCases.length}个测试用例`);
        
      } catch (excelError) {
        console.error('Excel解析失败:', excelError);
        throw new Error(`Excel文件解析失败: ${excelError instanceof Error ? excelError.message : '未知错误'}`);
      }
    }

    if (testCases.length === 0) {
      return NextResponse.json(
        { error: '文件中没有找到有效的测试数据', success: false },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: testCases,
      message: `成功解析 ${testCases.length} 个测试用例`
    });

  } catch (error) {
    console.error('文件解析失败:', error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '文件解析失败', 
        success: false 
      },
      { status: 500 }
    );
  }
} 