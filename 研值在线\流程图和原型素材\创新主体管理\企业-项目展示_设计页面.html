<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业项目展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">企业项目展示</h1>

        <!-- 统计概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4 flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-500">研发项目总数</p>
                    <p class="text-2xl font-bold text-gray-900">156</p>
                </div>
                <div class="w-12 h-12">
                    <canvas id="projectCountChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-500">投资项目总数</p>
                    <p class="text-2xl font-bold text-gray-900">87</p>
                </div>
                <div class="w-12 h-12">
                    <canvas id="investCountChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-500">在研项目数量</p>
                    <p class="text-2xl font-bold text-gray-900">92</p>
                </div>
                <div class="w-12 h-12">
                    <canvas id="ongoingChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-500">已验收项目</p>
                    <p class="text-2xl font-bold text-gray-900">64</p>
                </div>
                <div class="w-12 h-12">
                    <canvas id="completedChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-500">项目总投入</p>
                    <p class="text-2xl font-bold text-gray-900">¥3.2亿</p>
                </div>
                <div class="w-12 h-12">
                    <canvas id="totalInvestChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" class="h-4 w-4 text-blue-600" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">研发项目</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="projectType" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">投资项目</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部行业</option>
                        <option>生物医药</option>
                        <option>智能制造</option>
                        <option>新材料</option>
                        <option>新能源</option>
                        <option>信息技术</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 mr-1">
                            立项
                        </label>
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 mr-1">
                            在研
                        </label>
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 mr-1">
                            验收
                        </label>
                        <label class="inline-flex items-center px-2 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 mr-1">
                            结题
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-1">
                        <input type="date" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500 text-xs">至</span>
                        <input type="date" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <div class="relative">
                        <input type="text" placeholder="输入项目名称、负责人或关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 pl-10">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 项目列表区 -->
            <div class="flex-1">
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-800">项目列表</h2>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                导出
                            </button>
                            <button onclick="openProjectModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                </svg>
                                新增项目
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前阶段</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">立项金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新型抗癌药物研发</div>
                                        <div class="text-sm text-gray-500">宁波市科技计划项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张研究员</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥5,800,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥3,200,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="showProjectDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">智能机器人生产线</div>
                                        <div class="text-sm text-gray-500">宁波市智能制造专项</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">投资项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李工程师</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">验收</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥12,500,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥12,500,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="showProjectDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">石墨烯复合材料</div>
                                        <div class="text-sm text-gray-500">国家新材料重点专项</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">立项</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥3,200,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥800,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="showProjectDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">新能源汽车电池</div>
                                        <div class="text-sm text-gray-500">宁波市新能源产业项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">投资项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵总监</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">结题</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥8,600,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥8,600,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="showProjectDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">工业互联网平台</div>
                                        <div class="text-sm text-gray-500">浙江省数字经济项目</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研发项目</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">信息技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">钱经理</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">在研</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥4,500,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥2,700,000</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="showProjectDetail()" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">156</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关联图表区 -->
            <div class="lg:w-1/3">
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-800">资金投入结构</h2>
                        <button id="toggleChart" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            展开
                        </button>
                    </div>
                    <div id="chartPanel" class="hidden">
                        <div class="p-4">
                            <div class="h-64">
                                <canvas id="fundingChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情抽屉 -->
    <div id="projectDetail" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex justify-end h-full">
            <div class="bg-white w-full lg:w-1/2 h-full overflow-y-auto">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="hideProjectDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="space-y-6">
                        <!-- 基础信息 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">项目名称：</span>
                                    <span class="font-medium text-gray-900">新型抗癌药物研发</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目编号：</span>
                                    <span class="font-medium text-gray-900">NB2023RD001</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">项目类别：</span>
                                    <span class="font-medium text-gray-900">研发项目</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">行业领域：</span>
                                    <span class="font-medium text-gray-900">生物医药</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">负责人：</span>
                                    <span class="font-medium text-gray-900">张研究员</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">联系电话：</span>
                                    <span class="font-medium text-gray-900">138****8888</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">立项时间：</span>
                                    <span class="font-medium text-gray-900">2023-03-15</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">计划完成时间：</span>
                                    <span class="font-medium text-gray-900">2025-03-14</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="text-gray-500">项目简介：</span>
                                    <p class="font-medium text-gray-900 mt-1">本项目旨在研发一种针对肺癌的新型靶向药物，通过抑制特定蛋白激酶活性来阻断肿瘤细胞增殖，具有高效低毒的特点。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 合同与合作情况 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">合同与合作情况</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">合同金额：</span>
                                        <span class="font-medium text-gray-900">¥5,800,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">合作单位：</span>
                                        <span class="font-medium text-gray-900">宁波大学医学院、宁波市第一医院</span>
                                    </div>
                                    <div class="md:col-span-2">
                                        <span class="text-gray-500">合同附件：</span>
                                        <div class="mt-1 flex space-x-2">
                                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                </svg>
                                                项目合同.pdf
                                            </a>
                                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                                </svg>
                                                合作协议.pdf
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 阶段验收节点 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">阶段验收节点</h4>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">立项评审</p>
                                        <p class="text-sm text-gray-500">2023-03-15 已完成</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                        <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">中期检查</p>
                                        <p class="text-sm text-gray-500">2023-09-20 已完成</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center">
                                        <svg class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">结题验收</p>
                                        <p class="text-sm text-gray-500">计划 2025-03-14</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 经费拨付记录 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">经费拨付记录</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付批次</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">拨付时间</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">第一批</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">2023-04-10</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">¥1,500,000</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">已到账</td>
                                        </tr>
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">第二批</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">2023-10-15</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">¥1,700,000</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">已到账</td>
                                        </tr>
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">第三批</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">计划 2024-04-15</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">¥2,600,000</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">待拨付</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 研发费用归集 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">研发费用归集</h4>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">设备费：</span>
                                        <span class="font-medium text-gray-900">¥1,200,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">材料费：</span>
                                        <span class="font-medium text-gray-900">¥850,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">测试化验加工费：</span>
                                        <span class="font-medium text-gray-900">¥680,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">燃料动力费：</span>
                                        <span class="font-medium text-gray-900">¥150,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">差旅费：</span>
                                        <span class="font-medium text-gray-900">¥120,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">会议费：</span>
                                        <span class="font-medium text-gray-900">¥80,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">劳务费：</span>
                                        <span class="font-medium text-gray-900">¥900,000</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">专家咨询费：</span>
                                        <span class="font-medium text-gray-900">¥100,000</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 产出成果 -->
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-4">产出成果</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mt-1">
                                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">已发表SCI论文2篇</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mt-1">
                                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">申请发明专利3项</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-6 w-6 rounded-full bg-blue-100 flex items-center justify-center mt-1">
                                        <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-gray-900">培养研究生5名</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="flex justify-end space-x-3 pt-6">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                查看投资关联
                            </button>
                            <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                导出PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增项目弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex items-center justify-between p-6 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增项目</h3>
                    <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <form class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">项目类别 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="">请选择项目类别</option>
                                    <option value="research">研发项目</option>
                                    <option value="investment">投资项目</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">行业领域 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                    <option value="">请选择行业领域</option>
                                    <option value="biomedical">生物医药</option>
                                    <option value="manufacturing">智能制造</option>
                                    <option value="material">新材料</option>
                                    <option value="energy">新能源</option>
                                    <option value="it">信息技术</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">负责人 <span class="text-red-500">*</span></label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">立项金额(万元) <span class="text-red-500">*</span></label>
                                <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">立项时间 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">计划完成时间 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">联系电话 <span class="text-red-500">*</span></label>
                                <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目简介 <span class="text-red-500">*</span></label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">上传合同附件</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                        <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .doc, .docx 格式</span>
                                    </label>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".pdf,.doc,.docx">
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-3 pt-4">
                            <button type="button" onclick="closeProjectModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                取消
                            </button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function showProjectDetail() {
            document.getElementById('projectDetail').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function hideProjectDetail() {
            document.getElementById('projectDetail').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function openProjectModal() {
            document.getElementById('projectModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeProjectModal() {
            document.getElementById('projectModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览图表
            const initSmallChart = (id, value) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        datasets: [{
                            data: [value, 100-value],
                            backgroundColor: ['#3B82F6', '#E5E7EB'],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        cutout: '70%',
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            tooltip: { enabled: false }
                        }
                    }
                });
            };

            initSmallChart('projectCountChart', 75);
            initSmallChart('investCountChart', 60);
            initSmallChart('ongoingChart', 45);
            initSmallChart('completedChart', 85);
            initSmallChart('totalInvestChart', 65);

            // 初始化资金投入结构图表
            const fundingCtx = document.getElementById('fundingChart').getContext('2d');
            new Chart(fundingCtx, {
                type: 'bar',
                data: {
                    labels: ['生物医药', '智能制造', '新材料', '新能源', '信息技术'],
                    datasets: [{
                        label: '研发项目',
                        data: [580, 320, 420, 280, 450],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }, {
                        label: '投资项目',
                        data: [1250, 980, 760, 860, 540],
                        backgroundColor: 'rgba(16, 185, 129, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // 图表面板切换
            document.getElementById('toggleChart').addEventListener('click', function() {
                const panel = document.getElementById('chartPanel');
                if (panel.classList.contains('hidden')) {
                    panel.classList.remove('hidden');
                    this.textContent = '收起';
                } else {
                    panel.classList.add('hidden');
                    this.textContent = '展开';
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('projectDetail').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideProjectDetail();
                }
            });

            document.getElementById('projectModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeProjectModal();
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    if (!document.getElementById('projectDetail').classList.contains('hidden')) {
                        hideProjectDetail();
                    }
                    if (!document.getElementById('projectModal').classList.contains('hidden')) {
                        closeProjectModal();
                    }
                }
            });

            // 表单提交处理
            document.querySelector('#projectModal form').addEventListener('submit', function(e) {
                e.preventDefault();
                alert('项目已保存 (原型演示)');
                closeProjectModal();
            });
        });
    </script>
</body>
</html>