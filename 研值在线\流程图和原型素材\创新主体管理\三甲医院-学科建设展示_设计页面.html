<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院学科建设展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">宁波市第一医院学科建设展示</h1>
            <p class="mt-2 text-gray-600">医院重点学科建设成果与布局的多维度展示</p>
        </div>

        <!-- 学科概况区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 国家级重点学科 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">国家级重点学科</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">12</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="nationalTrend"></canvas>
                </div>
            </div>

            <!-- 省级重点学科 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">省级重点学科</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">28</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="provincialTrend"></canvas>
                </div>
            </div>

            <!-- 市级重点学科 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">市级重点学科</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">45</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="municipalTrend"></canvas>
                </div>
            </div>

            <!-- 近三年新增 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500">近三年新增</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">16</p>
                    </div>
                    <div class="bg-yellow-100 p-2 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="mt-4 h-16">
                    <canvas id="newTrend"></canvas>
                </div>
            </div>
        </div>

        <!-- 级别分布图区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">学科级别分布</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">全部</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">国家级</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">省级</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">市级</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="levelDistribution"></canvas>
            </div>
        </div>

        <!-- 建设进程视图区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">学科建设进程</h2>
            <div class="relative">
                <!-- 时间轴 -->
                <div class="flex justify-between mb-4">
                    <div class="text-center">
                        <div class="w-4 h-4 bg-blue-500 rounded-full mx-auto mb-1"></div>
                        <p class="text-sm text-gray-700">获批</p>
                        <p class="text-xs text-gray-500">2018-2020</p>
                    </div>
                    <div class="text-center">
                        <div class="w-4 h-4 bg-yellow-500 rounded-full mx-auto mb-1"></div>
                        <p class="text-sm text-gray-700">建设中</p>
                        <p class="text-xs text-gray-500">2020-2022</p>
                    </div>
                    <div class="text-center">
                        <div class="w-4 h-4 bg-green-500 rounded-full mx-auto mb-1"></div>
                        <p class="text-sm text-gray-700">验收通过</p>
                        <p class="text-xs text-gray-500">2022-2023</p>
                    </div>
                    <div class="text-center">
                        <div class="w-4 h-4 bg-purple-500 rounded-full mx-auto mb-1"></div>
                        <p class="text-sm text-gray-700">持续提升</p>
                        <p class="text-xs text-gray-500">2023-2025</p>
                    </div>
                </div>
                
                <!-- 进度条 -->
                <div class="relative h-2 bg-gray-200 rounded-full mb-8">
                    <div class="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 via-yellow-500 to-green-500 rounded-full" style="width: 75%"></div>
                </div>
                
                <!-- 学科数量变化 -->
                <div class="h-64">
                    <canvas id="progressChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 学科清单列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 md:mb-0">重点学科清单</h2>
                    <div class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-4">
                        <div class="relative">
                            <select class="block w-full px-4 py-2 pr-8 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                <option>全部级别</option>
                                <option>国家级</option>
                                <option>省级</option>
                                <option>市级</option>
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                                <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="relative">
                            <input type="text" placeholder="搜索学科名称..." class="block w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出 Excel
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获批年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">牵头科室</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设周期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前阶段</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('1')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">心血管内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">心血管内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018-2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">验收通过</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('2')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">神经外科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">神经外科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019-2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">建设中</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('3')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">肿瘤科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">市级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">肿瘤内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-2025</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs">建设中</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('4')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">儿科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2017</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">儿科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2017-2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">验收通过</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 cursor-pointer" onclick="openDetailModal('5')">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">呼吸内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">呼吸内科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-2026</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">获批</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 85 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 学科详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">学科详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基础信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">学科名称</label>
                                <p class="text-lg font-medium text-gray-900">心血管内科</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">建设级别</label>
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">国家级重点学科</span>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">牵头科室</label>
                                <p class="text-sm text-gray-900">心血管内科</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">学科负责人</label>
                                <p class="text-sm text-gray-900">张教授</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">获批年度</label>
                                <p class="text-sm text-gray-900">2018年</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">建设周期</label>
                                <p class="text-sm text-gray-900">2018-2023年</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">当前阶段</label>
                                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">验收通过</span>
                            </div>
                        </div>

                        <!-- 学科定位 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">学科定位</label>
                            <p class="text-sm text-gray-700">
                                本学科致力于心血管疾病的预防、诊断和治疗，重点发展冠心病介入治疗、心律失常射频消融、心力衰竭综合管理等方向，打造区域心血管疾病诊疗中心。
                            </p>
                        </div>

                        <!-- 团队结构 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">团队结构</label>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm font-medium text-gray-900">学科带头人</p>
                                    <p class="text-sm text-gray-600">张教授（主任医师）</p>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm font-medium text-gray-900">骨干医师</p>
                                    <p class="text-sm text-gray-600">12人（高级职称8人）</p>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm font-medium text-gray-900">科研人员</p>
                                    <p class="text-sm text-gray-600">5人（博士3人）</p>
                                </div>
                            </div>
                        </div>

                        <!-- 科研方向 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">科研方向</label>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                    <p class="text-sm text-gray-700">冠心病精准介入治疗技术</p>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                    <p class="text-sm text-gray-700">心律失常发病机制研究</p>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                                    <p class="text-sm text-gray-700">心力衰竭生物标志物筛选</p>
                                </div>
                            </div>
                        </div>

                        <!-- 代表性成果 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">代表性成果</label>
                            <div class="space-y-3">
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm font-medium text-gray-900">冠心病介入治疗新技术</p>
                                    <p class="text-xs text-gray-500">获2022年浙江省科技进步二等奖</p>
                                </div>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm font-medium text-gray-900">心律失常射频消融术式改良</p>
                                    <p class="text-xs text-gray-500">发表SCI论文8篇，影响因子总计32.5</p>
                                </div>
                            </div>
                        </div>

                        <!-- 建设目标完成度 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">建设目标完成度</label>
                            <div class="space-y-2">
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700">科研项目</span>
                                        <span class="text-sm font-medium text-gray-700">85%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700">论文发表</span>
                                        <span class="text-sm font-medium text-gray-700">90%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 90%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-sm font-medium text-gray-700">人才培养</span>
                                        <span class="text-sm font-medium text-gray-700">75%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        查看科研成果
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // ========= 初始化所有图表 =========
            // 国家级学科趋势图
            const nationalCtx = document.getElementById('nationalTrend').getContext('2d');
            new Chart(nationalCtx, {
                type: 'line',
                data: {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        data: [8, 9, 10, 11, 11, 12],
                        borderColor: '#3B82F6',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        pointBackgroundColor: '#3B82F6',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            // 省级学科趋势图
            const provincialCtx = document.getElementById('provincialTrend').getContext('2d');
            new Chart(provincialCtx, {
                type: 'line',
                data: {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        data: [18, 21, 23, 25, 26, 28],
                        borderColor: '#10B981',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        pointBackgroundColor: '#10B981',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            // 市级学科趋势图
            const municipalCtx = document.getElementById('municipalTrend').getContext('2d');
            new Chart(municipalCtx, {
                type: 'line',
                data: {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        data: [32, 36, 39, 42, 43, 45],
                        borderColor: '#8B5CF6',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        pointBackgroundColor: '#8B5CF6',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            // 近三年新增趋势图
            const newCtx = document.getElementById('newTrend').getContext('2d');
            new Chart(newCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        data: [3, 5, 8, 16],
                        borderColor: '#F59E0B',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: false,
                        pointBackgroundColor: '#F59E0B',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { x: { display: false }, y: { display: false } }
                }
            });

            // 级别分布图
            const levelCtx = document.getElementById('levelDistribution').getContext('2d');
            new Chart(levelCtx, {
                type: 'bar',
                data: {
                    labels: ['国家级', '省级', '市级'],
                    datasets: [{
                        label: '重点学科',
                        data: [12, 28, 45],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.raw;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#6B7280' },
                            grid: { color: 'rgba(0, 0, 0, 0.05)' }
                        },
                        x: {
                            ticks: { color: '#6B7280' },
                            grid: { display: false }
                        }
                    }
                }
            });

            // 建设进程图
            const progressCtx = document.getElementById('progressChart').getContext('2d');
            new Chart(progressCtx, {
                type: 'line',
                data: {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '获批',
                            data: [5, 8, 12, 15, 18, 20],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '建设中',
                            data: [3, 6, 9, 12, 15, 18],
                            borderColor: '#F59E0B',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '验收通过',
                            data: [1, 3, 5, 8, 12, 15],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: { color: '#6B7280' }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#6B7280' },
                            grid: { color: 'rgba(0, 0, 0, 0.05)' }
                        },
                        x: {
                            ticks: { color: '#6B7280' },
                            grid: { display: false }
                        }
                    }
                }
            });

            // ========= 为弹窗绑定"点击外部关闭"事件 =========
            const detailModal = document.getElementById('detailModal');
            if (detailModal) {
                detailModal.addEventListener('click', function(event) {
                    if (event.target === detailModal) {
                        closeDetailModal();
                    }
                });

                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && !detailModal.classList.contains('hidden')) {
                        closeDetailModal();
                    }
                });
            }
        });
    </script>
</body>
</html>