'use client'

import { usePathname, useSearchParams } from 'next/navigation'
import { Sidebar } from "./sidebar"

interface RootLayoutProps {
  children: React.ReactNode
}

export function RootLayout({ children }: RootLayoutProps) {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // 检查是否是需要隐藏侧边栏的页面
  const shouldHideSidebar = pathname === '/agent/difyznwd' && searchParams.get('key')
  
  if (shouldHideSidebar) {
    // 隐藏侧边栏的布局
    return (
      <main className="min-h-screen w-full">
        {children}
      </main>
    )
  }
  
  // 默认布局（显示侧边栏）
  return (
    <div className="flex min-h-screen">
      <Sidebar className="w-64 border-r" />
      <main className="flex-1">
        {children}
      </main>
    </div>
  )
} 