<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">企业信息展示</h1>
            <p class="text-gray-600">全面展示企业工商信息、股东结构、对外投资、参保情况及股权穿透关系</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-2 space-y-6">
                <!-- 企业概况区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        企业概况
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="flex items-center mb-4">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center mr-4">
                                    <svg class="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-900">宁波智能科技有限公司</h3>
                                    <p class="text-sm text-gray-500">统一社会信用代码：91330201MA2XXXXXX</p>
                                </div>
                            </div>
                            <div class="flex flex-wrap gap-2 mb-4">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">存续</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">高新技术企业</span>
                                <span class="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded-full">制造业</span>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-500">注册时间</p>
                                    <p class="text-sm font-medium text-gray-900">2018-05-15</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">注册资本</p>
                                    <p class="text-sm font-medium text-gray-900">5000万元人民币</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">企业类型</p>
                                    <p class="text-sm font-medium text-gray-900">有限责任公司</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">法定代表人</p>
                                    <p class="text-sm font-medium text-gray-900">张明</p>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">注册地址</p>
                                <p class="text-sm font-medium text-gray-900">浙江省宁波市鄞州区科技大道188号</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 股东信息与出资占比区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            股东信息与出资占比
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看全部股东</button>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div id="shareholder-chart" class="w-full h-[300px]"></div>
                        <div class="overflow-y-auto max-h-[300px]">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股东名称</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">股东类型</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资比例</th>
                                        <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资额</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewShareholderDetail('1')">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">宁波创新投资有限公司</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">企业法人</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">45%</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">2250万元</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewShareholderDetail('2')">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">张明</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">自然人</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">30%</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">1500万元</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewShareholderDetail('3')">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">宁波科技发展基金</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">企业法人</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">15%</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">750万元</td>
                                    </tr>
                                    <tr class="hover:bg-gray-50 cursor-pointer" onclick="viewShareholderDetail('4')">
                                        <td class="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">李华</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">自然人</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">10%</td>
                                        <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-500">500万元</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 对外投资总览区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        对外投资总览
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">对外投资总额</div>
                                <div class="text-2xl font-bold text-blue-600">1.2亿</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">投资企业数量</div>
                                <div class="text-2xl font-bold text-green-600">8家</div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">主要投资地区</div>
                                <div class="text-2xl font-bold text-purple-600">3个</div>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资企业</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投资金额</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">出资比例</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波智能装备有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5000万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">51%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高端装备制造</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波数字科技有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3000万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">30%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">信息技术服务</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波新能源研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2000万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">20%</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="flex justify-end mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            导出投资清单
                        </button>
                    </div>
                </div>

                <!-- 参保信息区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        参保信息
                    </h2>
                    <div id="insurance-chart" class="w-full h-[300px] mb-6"></div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-md font-medium text-gray-900 mb-3">当前参保情况</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">养老保险</span>
                                    <span class="text-sm font-medium text-gray-900">156人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">医疗保险</span>
                                    <span class="text-sm font-medium text-gray-900">156人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">失业保险</span>
                                    <span class="text-sm font-medium text-gray-900">156人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">工伤保险</span>
                                    <span class="text-sm font-medium text-gray-900">156人</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">生育保险</span>
                                    <span class="text-sm font-medium text-gray-900">156人</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-md font-medium text-gray-900 mb-3">五险缴纳基数</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">养老保险基数</span>
                                    <span class="text-sm font-medium text-gray-900">3957元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">医疗保险基数</span>
                                    <span class="text-sm font-medium text-gray-900">3957元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">失业保险基数</span>
                                    <span class="text-sm font-medium text-gray-900">3957元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">工伤保险基数</span>
                                    <span class="text-sm font-medium text-gray-900">3957元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">生育保险基数</span>
                                    <span class="text-sm font-medium text-gray-900">3957元</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-6">
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                            查看参保详情
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 右侧侧边栏 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 股权穿透可视化区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            股权穿透可视化
                        </h2>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">全屏查看</button>
                    </div>
                    <div id="equity-chart" class="w-full h-[400px]"></div>
                    <div class="mt-4 text-sm text-gray-500">
                        <p>点击节点可展开二级股权关系，右键可复制链路</p>
                    </div>
                </div>

                <!-- 行业产业与集团关系区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        行业产业与集团关系
                    </h2>
                    <div id="industry-chart" class="w-full h-[300px] mb-4"></div>
                    <div class="text-sm text-gray-500">
                        <p>悬停查看比例，双击节点可跳转至企业详情</p>
                    </div>
                </div>

                <!-- 研发人员展示 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        研发人员展示
                    </h2>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-blue-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500 mb-1">研发人员总数</div>
                            <div class="text-xl font-bold text-blue-600">86人</div>
                        </div>
                        <div class="bg-green-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500 mb-1">技术顾问数</div>
                            <div class="text-xl font-bold text-green-600">12人</div>
                        </div>
                        <div class="bg-purple-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500 mb-1">硕博士及以上</div>
                            <div class="text-xl font-bold text-purple-600">45人</div>
                        </div>
                        <div class="bg-yellow-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-500 mb-1">高级职称</div>
                            <div class="text-xl font-bold text-yellow-600">32人</div>
                        </div>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">参与项目数</span>
                            <span class="text-sm font-medium text-blue-600">156个</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">知识产权数</span>
                            <span class="text-sm font-medium text-blue-600">89项</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">政策补助</span>
                            <span class="text-sm font-medium text-blue-600">23次</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500">企业获奖数</span>
                            <span class="text-sm font-medium text-blue-600">15项</span>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            查看研发人员清册
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 股东信息柱状图
        const shareholderChart = echarts.init(document.getElementById('shareholder-chart'));
        const shareholderOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['出资比例']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: '{value}%'
                }
            },
            yAxis: {
                type: 'category',
                data: ['宁波创新投资', '张明', '宁波科技发展基金', '李华']
            },
            series: [
                {
                    name: '出资比例',
                    type: 'bar',
                    data: [45, 30, 15, 10],
                    itemStyle: {
                        color: function(params) {
                            const colorList = ['#5470C6', '#91CC75', '#FAC858', '#EE6666'];
                            return colorList[params.dataIndex];
                        }
                    },
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{c}%'
                    }
                }
            ]
        };
        shareholderChart.setOption(shareholderOption);

        // 参保信息折线图
        const insuranceChart = echarts.init(document.getElementById('insurance-chart'));
        const insuranceOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['养老保险', '医疗保险', '失业保险', '工伤保险', '生育保险']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['2019', '2020', '2021', '2022', '2023']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '养老保险',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 156],
                    itemStyle: {
                        color: '#5470C6'
                    }
                },
                {
                    name: '医疗保险',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 156],
                    itemStyle: {
                        color: '#91CC75'
                    }
                },
                {
                    name: '失业保险',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 156],
                    itemStyle: {
                        color: '#FAC858'
                    }
                },
                {
                    name: '工伤保险',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 156],
                    itemStyle: {
                        color: '#EE6666'
                    }
                },
                {
                    name: '生育保险',
                    type: 'line',
                    stack: '总量',
                    data: [120, 132, 101, 134, 156],
                    itemStyle: {
                        color: '#73C0DE'
                    }
                }
            ]
        };
        insuranceChart.setOption(insuranceOption);

        // 股权穿透图
        const equityChart = echarts.init(document.getElementById('equity-chart'));
        const equityOption = {
            tooltip: {
                trigger: 'item',
                triggerOn: 'mousemove'
            },
            series: [
                {
                    type: 'tree',
                    data: [{
                        name: '宁波智能科技有限公司',
                        children: [
                            {
                                name: '宁波创新投资有限公司\n持股45%',
                                children: [
                                    { name: '宁波国有资产投资\n持股60%' },
                                    { name: '浙江投资集团\n持股40%' }
                                ]
                            },
                            {
                                name: '张明\n持股30%',
                                children: [
                                    { name: '李华\n持股10%' }
                                ]
                            },
                            {
                                name: '宁波科技发展基金\n持股15%'
                            },
                            {
                                name: '李华\n持股10%'
                            }
                        ]
                    }],
                    symbol: 'roundRect',
                    symbolSize: 10,
                    orient: 'vertical',
                    expandAndCollapse: true,
                    initialTreeDepth: 2,
                    label: {
                        position: 'top',
                        rotate: 0,
                        verticalAlign: 'middle',
                        align: 'center',
                        fontSize: 10,
                        formatter: function(params) {
                            return params.name;
                        }
                    },
                    leaves: {
                        label: {
                            position: 'right',
                            verticalAlign: 'middle',
                            align: 'left'
                        }
                    },
                    emphasis: {
                        focus: 'descendant'
                    },
                    animationDurationUpdate: 750
                }
            ]
        };
        equityChart.setOption(equityOption);

        // 行业产业雷达图
        const industryChart = echarts.init(document.getElementById('industry-chart'));
        const industryOption = {
            tooltip: {
                trigger: 'item'
            },
            radar: {
                indicator: [
                    { name: '研发投入', max: 100 },
                    { name: '市场占有率', max: 100 },
                    { name: '专利数量', max: 100 },
                    { name: '行业影响力', max: 100 },
                    { name: '产业链位置', max: 100 }
                ],
                radius: '65%'
            },
            series: [
                {
                    type: 'radar',
                    data: [
                        {
                            value: [85, 72, 90, 65, 75],
                            name: '宁波智能科技',
                            areaStyle: {
                                color: 'rgba(84, 112, 198, 0.4)'
                            },
                            lineStyle: {
                                color: '#5470C6'
                            }
                        },
                        {
                            value: [65, 85, 70, 80, 60],
                            name: '行业平均',
                            areaStyle: {
                                color: 'rgba(145, 204, 117, 0.4)'
                            },
                            lineStyle: {
                                color: '#91CC75'
                            }
                        }
                    ]
                }
            ]
        };
        industryChart.setOption(industryOption);

        // 窗口大小变化时重新调整图表大小
        window.addEventListener('resize', function() {
            shareholderChart.resize();
            insuranceChart.resize();
            equityChart.resize();
            industryChart.resize();
        });

        // 查看股东详情
        function viewShareholderDetail(id) {
            console.log('查看股东详情:', id);
            // 这里可以添加跳转逻辑或打开模态框
        }
    </script>
</body>
</html>