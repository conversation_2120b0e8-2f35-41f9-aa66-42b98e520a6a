unit CcCkReport;

interface

uses
  Classes;

type
  TCcCkReport = class
  private
    FCk: string;
    FPmtype: string;
    FSPmgg: string;
    FPmgg: string;
    FPm: string;
    FGg: string;
    Famount: double;
    Fbamount: double;
    FPmgg1: string;
    FPm1: string;
    FGg1: string;
    FLb1: string;
    FKz1: string;
    Famount1: double;
    FCk1: string;
    FPmgg2: string;
    FPm2: string;
    FGg2: string;
    FLb2: string;
    FKz2: string;
    Famount2: double;
    FCk2: string;
    FPmgg3: string;
    FPm3: string;
    FGg3: string;
    FLb3: string;
    FKz3: string;
    Famount3: double;
    FCk3: string;
    FPm4: string;
    Famount4: double;
    FCk4: string;
  public
    property Ck: string read FCk write FCk;
    property Pmtype: string read FPmtype write FPmtype;
    property SPmgg: string read FSPmgg write FSPmgg;
    property Pmgg: string read FPmgg write FPmgg;
    property Pm: string read FPm write FPm;
    property Gg: string read FGg write FGg;
    property amount: double read Famount write Famount;
    property bamount: double read Fbamount write Fbamount;
    property Pmgg1: string read FPmgg1 write FPmgg1;
    property Pm1: string read FPm1 write FPm1;
    property Gg1: string read FGg1 write FGg1;
    property Lb1: string read FLb1 write FLb1;
    property Kz1: string read FKz1 write FKz1;
    property amount1: double read Famount1 write Famount1;
    property Ck1: string read FCk1 write FCk1;
    property Pmgg2: string read FPmgg2 write FPmgg2;
    property Pm2: string read FPm2 write FPm2;
    property Gg2: string read FGg2 write FGg2;
    property Lb2: string read FLb2 write FLb2;
    property Kz2: string read FKz2 write FKz2;
    property amount2: double read Famount2 write Famount2;
    property Ck2: string read FCk2 write FCk2;
    property Pmgg3: string read FPmgg3 write FPmgg3;
    property Pm3: string read FPm3 write FPm3;
    property Gg3: string read FGg3 write FGg3;
    property Lb3: string read FLb3 write FLb3;
    property Kz3: string read FKz3 write FKz3;
    property amount3: double read Famount3 write Famount3;
    property Ck3: string read FCk3 write FCk3;
    property Pm4: string read FPm4 write FPm4;
    property amount4: double read Famount4 write Famount4;
    property Ck4: string read FCk4 write FCk4;
  end;

implementation

end.
