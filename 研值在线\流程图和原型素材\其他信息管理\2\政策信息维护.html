<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策信息维护</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <!-- 引入富文本编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">政策信息维护</h1>
            <p class="text-gray-600">管理科研人才服务与空间定位相关政策资料，提供全生命周期管理能力</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">政策名称</label>
                            <input type="text" placeholder="输入关键词模糊搜索" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">发文文号</label>
                            <input type="text" placeholder="输入发文文号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">发布机构</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部机构</option>
                                <option value="ningbo_gov">宁波市人民政府</option>
                                <option value="ningbo_science">宁波市科学技术局</option>
                                <option value="ningbo_talent">宁波市人才服务中心</option>
                                <option value="ningbo_finance">宁波市财政局</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">有效期状态</label>
                            <div class="flex items-center space-x-4 mt-2">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">全部</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">有效</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="expiring" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">即将到期</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="status" value="expired" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已过期</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">发布日期</label>
                            <div class="flex items-center space-x-2">
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="text-gray-500">至</span>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 政策列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                政策列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为PDF</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openPolicyModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增政策
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发文文号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布机构</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布日期</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后修改时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市科技创新人才扶持计划实施办法</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">甬科人〔2023〕45号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市科学技术局</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">2023-06-15</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">有效</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-06-20 14:30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewPolicy('policy1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editPolicy('policy1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deletePolicy('policy1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市高层次人才安居工程实施细则</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">甬人社〔2022〕78号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市人才服务中心</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">2022-09-10</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">即将到期</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-15 09:45</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewPolicy('policy2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editPolicy('policy2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deletePolicy('policy2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市科技企业研发经费补助管理办法</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">甬财科〔2021〕32号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市财政局</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">2021-05-20</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">已过期</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-05-25 16:20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewPolicy('policy3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                            <button onclick="editPolicy('policy3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deletePolicy('policy3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">28</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        政策统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">政策总数</div>
                                <div class="text-2xl font-bold text-blue-600">28</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">即将到期</div>
                                <div class="text-2xl font-bold text-yellow-600">5</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近三月新增</div>
                                <div class="text-2xl font-bold text-green-600">12</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量导入
                            </h2>
                            <button onclick="toggleImportPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="importPanelToggle">展开</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500">支持Excel批量导入政策信息</p>
                    </div>
                    <div id="importPanel" class="hidden">
                        <div class="p-6 space-y-4">
                            <div class="flex items-center justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                    </svg>
                                    下载导入模板
                                </a>
                                <span class="text-xs text-gray-500">支持.xlsx格式</span>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                            </div>
                            <div id="uploadProgress" class="hidden">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">上传进度</span>
                                    <span class="text-sm font-medium text-gray-700">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div id="uploadResult" class="hidden bg-green-50 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-800">成功导入15条政策信息</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 政策编辑弹窗 -->
    <div id="policyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">政策信息编辑</h3>
                    <button onclick="closePolicyModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">政策名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入政策名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">发文文号 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入发文文号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">发布日期 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">发布机构 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择发布机构</option>
                                    <option value="ningbo_gov">宁波市人民政府</option>
                                    <option value="ningbo_science">宁波市科学技术局</option>
                                    <option value="ningbo_talent">宁波市人才服务中心</option>
                                    <option value="ningbo_finance">宁波市财政局</option>
                                </select>
                            </div>
                        </div>

                        <!-- 政策依据 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">政策依据</label>
                            <textarea rows="3" placeholder="请输入政策依据" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <!-- 政策有效期 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">政策有效期 <span class="text-red-500">*</span></label>
                            <div class="flex items-center space-x-2">
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="text-gray-500">至</span>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 原文上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">原文上传</label>
                            <div class="flex items-center space-x-4">
                                <div class="flex-1">
                                    <div class="border border-gray-300 rounded-md px-3 py-2 flex items-center justify-between">
                                        <span class="text-sm text-gray-500">选择文件上传</span>
                                        <button class="text-blue-600 hover:text-blue-800">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13l-3 3m0 0l-3-3m3 3V8m0 13a9 9 0 110-18 9 9 0 010 18z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">支持PDF、Word、Excel等格式，最大20MB</p>
                                </div>
                            </div>
                        </div>

                        <!-- 全文编辑器 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">政策全文 <span class="text-red-500">*</span></label>
                            <div id="editor" class="h-64 border border-gray-300 rounded-md"></div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closePolicyModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 border border-blue-300 text-blue-700 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存草稿
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        提交审核
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化富文本编辑器
        var quill = new Quill('#editor', {
            theme: 'snow',
            placeholder: '请输入政策全文内容...',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'indent': '-1'}, { 'indent': '+1' }],
                    [{ 'align': [] }],
                    ['clean']
                ]
            }
        });
        
        // 政策编辑弹窗
        function openPolicyModal() {
            document.getElementById('policyModal').classList.remove('hidden');
        }
        
        function closePolicyModal() {
            document.getElementById('policyModal').classList.add('hidden');
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 批量导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('importPanel');
            const toggle = document.getElementById('importPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('uploadResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 其他功能函数
        function viewPolicy(policyId) {
            console.log('查看政策:', policyId);
        }
        
        function editPolicy(policyId) {
            openPolicyModal();
            console.log('编辑政策:', policyId);
        }
        
        function deletePolicy(policyId) {
            if (confirm('确定要删除这条政策信息吗？')) {
                console.log('删除政策:', policyId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('policyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePolicyModal();
            }
        });
        
        // 点击其他区域关闭导出下拉菜单
        document.addEventListener('click', function(e) {
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>