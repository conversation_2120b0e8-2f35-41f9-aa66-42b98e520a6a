<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发信息管理业务流程</text>

  <!-- 阶段一：数据加载与缓存检查 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与缓存检查</text>
  
  <!-- 节点1: 用户触发 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户触发数据加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">进入页面或年度标签切换</text>
  </g>

  <!-- 节点2: 缓存检查 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存检查</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">检查本地缓存数据有效性</text>
  </g>

  <!-- 节点3: 数据整合服务 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据整合服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">拉取最新信息并合并</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 320 165 Q 385 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 670 165 Q 735 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="735" y="155" text-anchor="middle" font-size="12" fill="#555">缓存失效</text>

  <!-- 阶段二：数据校验与处理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据校验与处理</text>

  <!-- 节点4: 字段校验 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段完整性校验</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">异常检测与质量队列</text>
  </g>

  <!-- 节点5: 统计计算 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计结果生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">费用结构、人员梯度分析</text>
  </g>

  <!-- 节点6: 数据返回 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据返回前端</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">记录日志与性能指标</text>
  </g>

  <!-- 连接线 缓存检查 -> 字段校验 -->
  <path d="M 560 200 C 560 240, 400 280, 310 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 数据整合 -> 字段校验 -->
  <path d="M 910 200 C 910 240, 400 280, 310 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 420 355 Q 460 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 720 355 Q 760 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据编辑与审核 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据编辑与审核</text>

  <!-- 节点7: 用户编辑 -->
  <g transform="translate(150, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户编辑操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">单条编辑或批量导入</text>
  </g>

  <!-- 节点8: 格式校验 -->
  <g transform="translate(450, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">格式与规则校验</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">写入临时版本</text>
  </g>

  <!-- 节点9: 审核流程 -->
  <g transform="translate(750, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核流程</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">推送至审核队列</text>
  </g>

  <!-- 连接线 7 -> 8 -->
  <path d="M 370 555 Q 410 555 450 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 670 555 Q 710 555 750 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据落库与预警 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据落库与预警</text>

  <!-- 节点10: 正式落库 -->
  <g transform="translate(200, 720)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">正式版本落库</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">触发统计指标重计算</text>
  </g>

  <!-- 节点11: 消息通知 -->
  <g transform="translate(500, 720)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">消息中心通知</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">通知相关订阅者</text>
  </g>

  <!-- 节点12: 风险预警 -->
  <g transform="translate(800, 720)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">风险预警识别</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">更新预警标识</text>
  </g>

  <!-- 连接线 审核 -> 落库 -->
  <path d="M 860 590 C 860 630, 400 680, 310 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="585" y="655" text-anchor="middle" font-size="12" fill="#555">审核通过</text>
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 420 755 Q 460 755 500 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12 -->
  <path d="M 720 755 Q 760 755 800 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：预警回到数据返回 -->
  <path d="M 910 720 C 1100 720, 1100 355, 1020 355" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1060" y="540" text-anchor="middle" font-size="12" fill="#666">预警反馈循环</text>

  <!-- 质量反馈循环：质量队列回到校验 -->
  <path d="M 200 320 C 50 320, 50 165, 100 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="75" y="240" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 75, 240)">质量反馈</text>

</svg>