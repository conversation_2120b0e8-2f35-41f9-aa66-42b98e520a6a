unit CcSjMlAlldj;

interface

uses
  Classes;

type
  TCcSjMlAlldj = class
  private

    FSjmlalldjid: Integer;
    FDdid: Integer;
    FHjlgysid: string;
    FHjlTgs_a: Double;
    FHjlTgs_b: Double;
    FHjlTgs_c: Double;
    FHjlPbj: Double;

    FHjlpmid: string;
    FHjlggid: string;
    FHjlCzf: Double;
    FHjlZzsh: Double;
    FHjlRsf: Double;

    FHjljgf: Double;
    FHjlcb: Double;
    FHjlBz: string;
    FRzdgysid: string;
    FRzd_a: Double;
    FRzdDj_a: Double;
    FRzd_b: Double;
    FRzdDj_b: Double;
    FRzdcb: Double;
    FRzdBz: string;
    FGtbgysid: string;
    FGtbTgs: Double;
    FGtbPbj: Double;
    FGtbcb: Double;
    FGtbBz: string;
    FLwgysid: string;
    FLwTgs: Double;
    FLwPbj: Double;

    FLwSb: Double;
    FLwAlj: Double;
    FLwAlbl: Double;
    FLwZzf: Double;
    FLwZzsh: Double;
    FLwRsf: Double;

    FLwcb: Double;
    FLwBz: string;
    FSzbpmid: string;
    FSzbggid: string;
    FSzbTgs: Double;
    FSzbPbj: Double;
    FSzbZl: Double;
    FSzbRsj: Double;
    FSzbcb: Double;
    FSzbBz: string;
    FMss: Double;
    FMsdj: Double;
    FMsje: Double;
    FMsbz: string;
    FmPmbmid_a: string;
    FmQtsl_a: Double;
    FmQtdj_a: Double;
    FmQtje_a: Double;
    FmPmbmid_b: string;
    FmQtsl_b: Double;
    FmQtdj_b: Double;
    FmQtje_b: Double;
    FmPmbmid_c: string;
    FmQtsl_c: Double;
    FmQtdj_c: Double;
    FmQtje_c: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Sjmlalldjid: Integer read FSjmlalldjid write FSjmlalldjid;
    property Ddid: Integer read FDdid write FDdid;
    property Hjlgysid: string read FHjlgysid write FHjlgysid;
    property Hjltgs_a: Double read FHjlTgs_a write FHjlTgs_a;
    property Hjltgs_b: Double read FHjlTgs_b write FHjlTgs_b;
    property Hjltgs_c: Double read FHjlTgs_c write FHjlTgs_c;
    property Hjlpbj: Double read FHjlPbj write FHjlPbj;

    property Hjlpmid: string read FHjlpmid write FHjlpmid;
    property Hjlggid: string read FHjlggid write FHjlggid;
    property HjlCzf: Double read FHjlCzf write FHjlCzf;
    property HjlZzsh: Double read FHjlZzsh write FHjlZzsh;
    property HjlRsf: Double read FHjlRsf write FHjlRsf;

    property Hjljgf: Double read FHjljgf write FHjljgf;
    property Hjlcb: Double read FHjlcb write FHjlcb;
    property Hjlbz: string read FHjlBz write FHjlBz;
    property Rzdgysid: string read FRzdgysid write FRzdgysid;
    property Rzd_a: Double read FRzd_a write FRzd_a;
    property Rzddj_a: Double read FRzdDj_a write FRzdDj_a;
    property Rzd_b: Double read FRzd_b write FRzd_b;
    property Rzddj_b: Double read FRzdDj_b write FRzdDj_b;
    property Rzdcb: Double read FRzdcb write FRzdcb;
    property Rzdbz: string read FRzdBz write FRzdBz;
    property Gtbgysid: string read FGtbgysid write FGtbgysid;
    property Gtbtgs: Double read FGtbTgs write FGtbTgs;
    property Gtbpbj: Double read FGtbPbj write FGtbPbj;
    property Gtbcb: Double read FGtbcb write FGtbcb;
    property Gtbbz: string read FGtbBz write FGtbBz;
    property Lwgysid: string read FLwgysid write FLwgysid;
    property Lwtgs: Double read FLwTgs write FLwTgs;
    property Lwpbj: Double read FLwPbj write FLwPbj;
    property LwSb: Double read FLwSb write FLwSb;
    property LwAlj: Double read FLwAlj write FLwAlj;
    property LwAlbl: Double read FLwAlbl write FLwAlbl;
    property LwZzf: Double read FLwZzf write FLwZzf;
    property LwZzsh: Double read FLwZzsh write FLwZzsh;
    property LwRsf: Double read FLwRsf write FLwRsf;
    property Lwcb: Double read FLwcb write FLwcb;
    property Lwbz: string read FLwBz write FLwBz;
    property Szbpmid: string read FSzbpmid write FSzbpmid;
    property Szbggid: string read FSzbggid write FSzbggid;
    property Szbtgs: Double read FSzbTgs write FSzbTgs;
    property Szbpbj: Double read FSzbPbj write FSzbPbj;
    property Szbzl: Double read FSzbZl write FSzbZl;
    property SzbRsj: Double read FSzbRsj write FSzbRsj;
    property Szbcb: Double read FSzbcb write FSzbcb;
    property Szbbz: string read FSzbBz write FSzbBz;
    property Mss: Double read FMss write FMss;
    property Msdj: Double read FMsdj write FMsdj;
    property Msje: Double read FMsje write FMsje;
    property Msbz: string read FMsbz write FMsbz;
    property mPmbmid_a: string read FmPmbmid_a write FmPmbmid_a;
    property mQtsl_a: Double read FmQtsl_a write FmQtsl_a;
    property mQtdj_a: Double read FmQtdj_a write FmQtdj_a;
    property mQtje_a: Double read FmQtje_a write FmQtje_a;
    property mPmbmid_b: string read FmPmbmid_b write FmPmbmid_b;
    property mQtsl_b: Double read FmQtsl_b write FmQtsl_b;
    property mQtdj_b: Double read FmQtdj_b write FmQtdj_b;
    property mQtje_b: Double read FmQtje_b write FmQtje_b;
    property mPmbmid_c: string read FmPmbmid_c write FmPmbmid_c;
    property mQtsl_c: Double read FmQtsl_c write FmQtsl_c;
    property mQtdj_c: Double read FmQtdj_c write FmQtdj_c;
    property mQtje_c: Double read FmQtje_c write FmQtje_c;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
