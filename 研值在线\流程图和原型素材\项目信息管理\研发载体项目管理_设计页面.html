<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发载体项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">研发载体项目管理</h1>
            <p class="text-gray-600">管理各类科技研发平台、孵化器、创新空间等研发载体相关投资项目信息</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        条件筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
                            <input type="text" placeholder="输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目代码</label>
                            <input type="text" placeholder="输入项目代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目属地</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="haishu">海曙区</option>
                                <option value="jiangdong">江东区</option>
                                <option value="jiangbei">江北区</option>
                                <option value="zhenhai">镇海区</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目类别</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="platform">科技研发平台</option>
                                <option value="incubator">孵化器</option>
                                <option value="space">创新空间</option>
                                <option value="lab">实验室</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">建设状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="planning">规划中</option>
                                <option value="building">建设中</option>
                                <option value="completed">已建成</option>
                                <option value="operating">运营中</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">资金来源</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="government">政府资金</option>
                                <option value="enterprise">企业自筹</option>
                                <option value="mixed">混合资金</option>
                                <option value="foreign">外资</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 项目列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                项目列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openProjectModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    新增项目
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目代码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目属地</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总投资额(万元)</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">RDP-2024-001</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波智能科技创新中心</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">海曙区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">12,500</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">科技研发平台</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">运营中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewProject('proj1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editProject('proj1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteProject('proj1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">RDP-2024-002</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波数字产业孵化基地</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">江东区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">8,600</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">孵化器</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">建设中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewProject('proj2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editProject('proj2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteProject('proj2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">RDP-2024-003</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波新材料创新实验室</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">江北区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">5,300</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">实验室</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">规划中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="viewProject('proj3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editProject('proj3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteProject('proj3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">28</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        项目统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">项目总数</div>
                                <div class="text-2xl font-bold text-blue-600">28</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">总投资额(万元)</div>
                                <div class="text-2xl font-bold text-green-600">156,800</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">在建项目</div>
                                <div class="text-2xl font-bold text-yellow-600">12</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目类型分布图表 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        项目类型分布
                    </h2>
                    <div id="projectTypeChart" class="w-full h-[300px]"></div>
                </div>

                <!-- 批量导入区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量导入
                            </h2>
                            <button onclick="toggleImportPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="importPanelToggle">展开</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500">支持Excel批量导入项目信息</p>
                    </div>
                    <div id="importPanel" class="hidden">
                        <div class="p-6 space-y-4">
                            <div class="flex items-center justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                    </svg>
                                    下载导入模板
                                </a>
                                <span class="text-xs text-gray-500">支持.xlsx格式</span>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                            </div>
                            <div id="uploadProgress" class="hidden">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">上传进度</span>
                                    <span class="text-sm font-medium text-gray-700">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                        <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                            <li>第3行：项目代码已存在</li>
                                            <li>第5行：投资额为必填项</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目编辑弹窗 -->
    <div id="projectModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">项目信息编辑</h3>
                    <button onclick="closeProjectModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">项目代码 <span class="text-red-500">*</span></label>
                                    <input type="text" placeholder="请输入项目代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">项目名称 <span class="text-red-500">*</span></label>
                                    <input type="text" placeholder="请输入项目名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">项目属地 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择项目属地</option>
                                        <option value="haishu">海曙区</option>
                                        <option value="jiangdong">江东区</option>
                                        <option value="jiangbei">江北区</option>
                                        <option value="zhenhai">镇海区</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">项目类别 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择项目类别</option>
                                        <option value="platform">科技研发平台</option>
                                        <option value="incubator">孵化器</option>
                                        <option value="space">创新空间</option>
                                        <option value="lab">实验室</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">建设状态 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择建设状态</option>
                                        <option value="planning">规划中</option>
                                        <option value="building">建设中</option>
                                        <option value="completed">已建成</option>
                                        <option value="operating">运营中</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">资金来源 <span class="text-red-500">*</span></label>
                                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择资金来源</option>
                                        <option value="government">政府资金</option>
                                        <option value="enterprise">企业自筹</option>
                                        <option value="mixed">混合资金</option>
                                        <option value="foreign">外资</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 资金信息 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">资金信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">总投资额(万元) <span class="text-red-500">*</span></label>
                                    <input type="number" placeholder="请输入总投资额" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">固定资产投资(万元)</label>
                                    <input type="number" placeholder="请输入固定资产投资" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">注册资金(万元)</label>
                                    <input type="number" placeholder="请输入注册资金" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                            </div>
                        </div>

                        <!-- 项目详情 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">项目详情</h4>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">详细地址</label>
                                    <input type="text" placeholder="请输入详细地址" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">国标行业</label>
                                    <input type="text" placeholder="请输入国标行业" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
                                    <textarea rows="3" placeholder="请输入项目描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 科技成果关联 -->
                        <div>
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">科技成果关联</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">已关联成果: 3项</span>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">添加关联</button>
                                </div>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between bg-white p-2 rounded border">
                                        <span class="text-sm text-gray-700">智能传感器技术专利</span>
                                        <button class="text-red-600 hover:text-red-800 text-sm">移除</button>
                                    </div>
                                    <div class="flex items-center justify-between bg-white p-2 rounded border">
                                        <span class="text-sm text-gray-700">新材料制备工艺</span>
                                        <button class="text-red-600 hover:text-red-800 text-sm">移除</button>
                                    </div>
                                    <div class="flex items-center justify-between bg-white p-2 rounded border">
                                        <span class="text-sm text-gray-700">智能制造系统软件</span>
                                        <button class="text-red-600 hover:text-red-800 text-sm">移除</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeProjectModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">项目详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息卡片 -->
                        <div class="bg-white rounded-lg shadow-sm border p-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">项目代码</label>
                                    <p class="text-sm text-gray-900">RDP-2024-001</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">项目名称</label>
                                    <p class="text-sm text-gray-900">宁波智能科技创新中心</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">项目属地</label>
                                    <p class="text-sm text-gray-900">海曙区</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">项目类别</label>
                                    <p class="text-sm text-gray-900">科技研发平台</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">建设状态</label>
                                    <p class="text-sm text-gray-900">运营中</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">资金来源</label>
                                    <p class="text-sm text-gray-900">混合资金</p>
                                </div>
                            </div>
                        </div>

                        <!-- 资金信息卡片 -->
                        <div class="bg-white rounded-lg shadow-sm border p-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">资金信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">总投资额(万元)</label>
                                    <p class="text-sm text-gray-900">12,500</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">固定资产投资(万元)</label>
                                    <p class="text-sm text-gray-900">9,800</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">注册资金(万元)</label>
                                    <p class="text-sm text-gray-900">5,000</p>
                                </div>
                            </div>
                        </div>

                        <!-- 项目详情卡片 -->
                        <div class="bg-white rounded-lg shadow-sm border p-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">项目详情</h4>
                            <div class="grid grid-cols-1 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">详细地址</label>
                                    <p class="text-sm text-gray-900">宁波市海曙区望春工业园区聚才路299号</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">国标行业</label>
                                    <p class="text-sm text-gray-900">科学研究和技术服务业</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500 mb-1">项目描述</label>
                                    <p class="text-sm text-gray-900">宁波智能科技创新中心致力于人工智能、物联网等前沿技术的研发与应用，打造宁波市科技创新高地。</p>
                                </div>
                            </div>
                        </div>

                        <!-- 科技成果卡片 -->
                        <div class="bg-white rounded-lg shadow-sm border p-6">
                            <h4 class="text-md font-medium text-gray-900 mb-4 border-b pb-2">关联科技成果</h4>
                            <div class="space-y-3">
                                <div class="flex items-start p-3 bg-gray-50 rounded border">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">智能传感器技术专利</p>
                                        <p class="text-xs text-gray-500 mt-1">专利号：ZL202310123456.7</p>
                                        <p class="text-xs text-gray-500">关联时间：2024-01-15</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                                </div>
                                <div class="flex items-start p-3 bg-gray-50 rounded border">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">新材料制备工艺</p>
                                        <p class="text-xs text-gray-500 mt-1">技术秘密</p>
                                        <p class="text-xs text-gray-500">关联时间：2024-01-10</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                                </div>
                                <div class="flex items-start p-3 bg-gray-50 rounded border">
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-900">智能制造系统软件</p>
                                        <p class="text-xs text-gray-500 mt-1">软件著作权</p>
                                        <p class="text-xs text-gray-500">关联时间：2024-01-05</p>
                                    </div>
                                    <button class="text-blue-600 hover:text-blue-800 text-sm">查看</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    
    <script>
        // 项目类型分布图表初始化
        function initProjectTypeChart() {
            const chartDom = document.getElementById('projectTypeChart');
            const myChart = echarts.init(chartDom);
            
            const option = {
                title: {
                    text: '项目类型分布',
                    left: 'center',
                    textStyle: {
                        fontSize: 14,
                        fontWeight: 'normal'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: 10,
                    top: 'center',
                    data: ['科技研发平台', '孵化器', '创新空间', '实验室']
                },
                series: [
                    {
                        name: '项目类型',
                        type: 'pie',
                        radius: ['50%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 12, name: '科技研发平台', itemStyle: { color: '#5470C6' } },
                            { value: 8, name: '孵化器', itemStyle: { color: '#91CC75' } },
                            { value: 5, name: '创新空间', itemStyle: { color: '#FAC858' } },
                            { value: 3, name: '实验室', itemStyle: { color: '#EE6666' } }
                        ]
                    }
                ]
            };
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 项目编辑弹窗
        function openProjectModal() {
            document.getElementById('projectModal').classList.remove('hidden');
        }
        
        function closeProjectModal() {
            document.getElementById('projectModal').classList.add('hidden');
        }
        
        // 项目详情弹窗
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }
        
        // 批量导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('importPanel');
            const toggle = document.getElementById('importPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 其他功能函数
        function viewProject(projectId) {
            openDetailModal();
            console.log('查看项目:', projectId);
        }
        
        function editProject(projectId) {
            openProjectModal();
            console.log('编辑项目:', projectId);
        }
        
        function deleteProject(projectId) {
            if (confirm('确定要删除这个项目吗？此操作不可恢复！')) {
                console.log('删除项目:', projectId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('projectModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProjectModal();
            }
        });
        
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initProjectTypeChart();
        });
    </script>
</body>
</html>