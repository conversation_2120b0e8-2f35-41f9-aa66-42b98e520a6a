<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研院所仪器设备展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">科研院所仪器设备展示</h1>
                <p class="text-gray-600 mt-2">系统化呈现全院仪器设备基础信息清册</p>
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出Excel
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增设备
                </button>
            </div>
        </div>

        <!-- 设备概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">大型精密仪器</p>
                        <p class="text-2xl font-bold text-gray-900 mt-2">1,245</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="largeEquipmentChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">常规分析设备</p>
                        <p class="text-2xl font-bold text-gray-900 mt-2">3,876</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="regularEquipmentChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">专用实验装置</p>
                        <p class="text-2xl font-bold text-gray-900 mt-2">892</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="specialEquipmentChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between">
                    <div>
                        <p class="text-sm text-gray-500">共享开放率</p>
                        <p class="text-2xl font-bold text-gray-900 mt-2">78.5%</p>
                    </div>
                    <div class="w-20 h-20">
                        <canvas id="shareRateChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视图切换区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-lg font-semibold text-gray-900">设备分布视图</h2>
                <div class="flex space-x-2">
                    <button id="categoryViewBtn" class="px-4 py-2 bg-blue-600 text-white rounded-md">分类分布图</button>
                    <button id="usageViewBtn" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">用途分析图</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="mainChart"></canvas>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">设备类别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            光谱仪器
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            色谱仪器
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            质谱仪器
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">用途</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部用途</option>
                        <option>研发实验</option>
                        <option>教学培训</option>
                        <option>公共服务</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属部门</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部部门</option>
                        <option>材料研究所</option>
                        <option>生物实验室</option>
                        <option>化学分析中心</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">购置年份</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" placeholder="起始" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span>至</span>
                        <input type="number" placeholder="结束" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <div class="flex">
                        <input type="text" placeholder="输入设备名称或编号" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备列表区 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">设备列表</h2>
                    <p class="text-sm text-gray-600">共找到 1,245 台设备</p>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备图片</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">购置年份</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">管理部门</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">共享状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">使用率</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">高分辨率质谱仪</div>
                                <div class="text-sm text-gray-500">MS-2024-001</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Q Exactive HF-X</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">质谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">材料研究所</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">开放共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 85%"></div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">85%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="downloadDetail('1')" class="text-blue-600 hover:text-blue-900">下载明细</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">液相色谱仪</div>
                                <div class="text-sm text-gray-500">LC-2024-002</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1290 Infinity II</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">色谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">化学分析中心</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">内部使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">60%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="downloadDetail('2')" class="text-blue-600 hover:text-blue-900">下载明细</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-16 h-16 bg-gray-200 rounded-md flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                                    </svg>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">原子吸收光谱仪</div>
                                <div class="text-sm text-gray-500">AAS-2024-003</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AA-7000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物实验室</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">开放共享</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                                <div class="text-xs text-gray-500 mt-1">75%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="downloadDetail('3')" class="text-blue-600 hover:text-blue-900">下载明细</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 1,245 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 下载进度提示 -->
    <div id="downloadToast" class="fixed bottom-4 right-4 bg-white shadow-lg rounded-lg p-4 hidden max-w-sm w-full">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium text-gray-900">正在下载设备明细</p>
                <div class="mt-2">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="downloadProgress" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <button onclick="hideDownloadToast()" class="ml-4 flex-shrink-0 text-gray-400 hover:text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function downloadDetail(id) {
            const toast = document.getElementById('downloadToast');
            const progress = document.getElementById('downloadProgress');
            
            toast.classList.remove('hidden');
            progress.style.width = '0%';
            
            // 模拟下载进度
            let currentProgress = 0;
            const interval = setInterval(() => {
                currentProgress += 10;
                progress.style.width = currentProgress + '%';
                
                if (currentProgress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        toast.classList.add('hidden');
                    }, 1000);
                }
            }, 200);
        }

        function hideDownloadToast() {
            document.getElementById('downloadToast').classList.add('hidden');
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览区小图表
            const initSmallChart = (id, type, data, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: type,
                    data: {
                        labels: ['2021', '2022', '2023'],
                        datasets: [{
                            data: data,
                            backgroundColor: [
                                color + '20',
                                color + '40',
                                color + '80'
                            ],
                            borderColor: color,
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false },
                            tooltip: { enabled: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        }
                    }
                });
            };

            initSmallChart('largeEquipmentChart', 'bar', [120, 180, 220], '#3b82f6');
            initSmallChart('regularEquipmentChart', 'bar', [300, 350, 400], '#10b981');
            initSmallChart('specialEquipmentChart', 'bar', [80, 100, 120], '#f59e0b');
            initSmallChart('shareRateChart', 'doughnut', [78.5, 21.5], '#6366f1');

            // 初始化主图表
            const mainCtx = document.getElementById('mainChart').getContext('2d');
            let mainChart = new Chart(mainCtx, {
                type: 'bar',
                data: {
                    labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],
                    datasets: [{
                        label: '设备数量',
                        data: [3245, 2876, 1987, 1654, 1432, 1653],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });

            // 视图切换
            document.getElementById('usageViewBtn').addEventListener('click', function() {
                mainChart.destroy();
                mainChart = new Chart(mainCtx, {
                    type: 'pie',
                    data: {
                        labels: ['研发实验', '教学培训', '公共服务', '其他'],
                        datasets: [{
                            data: [45, 30, 20, 5],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
                
                document.getElementById('categoryViewBtn').classList.remove('bg-blue-600', 'text-white');
                document.getElementById('categoryViewBtn').classList.add('bg-gray-200', 'text-gray-700');
                this.classList.remove('bg-gray-200', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');
            });

            document.getElementById('categoryViewBtn').addEventListener('click', function() {
                mainChart.destroy();
                mainChart = new Chart(mainCtx, {
                    type: 'bar',
                    data: {
                        labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],
                        datasets: [{
                            label: '设备数量',
                            data: [3245, 2876, 1987, 1654, 1432, 1653],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
                
                document.getElementById('usageViewBtn').classList.remove('bg-blue-600', 'text-white');
                document.getElementById('usageViewBtn').classList.add('bg-gray-200', 'text-gray-700');
                this.classList.remove('bg-gray-200', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');
            });
        });
    </script>
</body>
</html>