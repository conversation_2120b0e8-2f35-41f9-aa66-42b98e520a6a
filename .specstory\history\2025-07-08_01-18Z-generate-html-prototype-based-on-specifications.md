<!-- Generated by SpecStory -->

# Generate HTML prototype based on specifications (2025-07-08 01:18Z)

_**User**_

### **通用原型开发模板 (可以直接复制使用)**
---

### **【提示词模板正文】**

你是一位资深的全栈工程师，精通产品规划和UI/UX设计，目前正在为一个已有的复杂Web应用项目贡献代码。你的核心任务是根据详细的需求文档，并严格遵循项目既定的设计规范，输出一个功能完整的、高质量的HTML原型页面。

### **核心指令与项目规范 (每次任务均需严格遵守)**

**1. 设计系统分析 (Design System Analysis):**
*   **布局:** 统一采用卡片式布局，结构为“页面大标题 + 内容区域”。内容区域常使用 `h-[calc(100vh-120px)]` 来计算高度以实现视口内滚动。
*   **颜色方案:** 主色调为蓝色。页面背景使用从 `from-gray-50` 到 `to-blue-50` 的微妙渐变。
*   **组件库:** 广泛使用Tailwind CSS构建通用UI组件，如卡片（Card）、按钮（Button）、输入框（Input）、选项卡（Tabs）、徽章（Badge）、下拉菜单（Dropdown）等。
*   **视觉元素:** 卡片具有阴影效果 (`shadow-md`)，按钮常带有图标，状态通常用不同颜色的徽章表示。
*   **交互模式:** 普遍使用选项卡（Tabs）切换内容视图，以及“列表-详情”的交互模式。
*   **响应式:** 默认使用Flexbox和Grid进行响应式布局。

**2. 代码与风格参考 (Code & Style References):**
*   请参考以下已开发页面的代码和实现，以确保新页面在视觉和结构上的一致性：
    *   `makemoney-web/src/app/template/ai-budget-review/page.tsx`
    *   `makemoney-web/src/app/template/assets/applications/page.tsx`
    *   `makemoney-web/src/app/template/customer-profile/page.tsx`
    *   `makemoney-web/src/app/template/document-management/page.tsx`
    *   `makemoney-web/src/app/template/zfsq/page.tsx`

**3. 技术与输出规范 (Technical & Output Specifications):**
*   **最终产物:** 一个独立的、完整的HTML文件。
*   **文件命名:** 基于下方提供的 `[功能名]`，将其翻译为有意义的英文并使用连字符（kebab-case）命名，例如“地址匹配查询”应命名为 `address-matching-query.html`。
*   **存放路径:** 所有HTML文件都将被放置在 `public` 目录下，这意味着所有资源路径（如图片）都应该是相对或绝对的，以便直接在浏览器中打开。
*   **样式:** **必须**完全使用 **Tailwind CSS** 类名。禁止使用内联 `<style>` 标签或外部CSS文件。
*   **图片:** 所有图片（如图标占位符、地图预览）均使用 **Unsplash API** (`https://source.unsplash.com/random/宽x高?主题`)。
*   **图标:** 请使用 Heroicons 或类似的 SVG 图标库，并直接将 SVG 代码嵌入 HTML 中，同时用 Tailwind CSS 控制其样式。
*   **语言:** 所有输出内容，包括页面上的文本、标签、按钮、表头以及代码中的注释，**必须全部使用简体中文**。
*   **质量标准:** 请以顶级UX设计师的眼光和审美标准进行创作，确保页面布局合理、信息层次清晰、交互直观，创造出令人满意的、专业级别的原型。

---

### **--- 本次开发任务 ---**

请替换下面的占位符内容以定义本次的具体任务：

**一、功能名:**
`[企业信息展示]`

**二、功能设计需求:**
`[**一、功能说明**

企业信息展示功能面向创新主体科研码的企业视图，汇聚工商注册、股东结构、对外投资、参保情况、股权穿透、行业与产业归属、集团关系等多源数据，通过可视化组件与层级下钻页面，向用户直观呈现企业经营与股权全貌，并支持关键要素的深度分析与关联跳转，为风险研判、投融资决策、产业链画像等业务场景提供权威基础信息和洞察支撑。

**二、界面结构与要素说明**

企业概况区位于页面顶部，集中展示企业名称、统一社会信用代码、注册时间、成立日期、注册资本、企业类型、注册地址及营业执照副本缩略图，辅以企业状态与行业分类标签，便于用户快速获取核心工商信息与经营资质。

股东信息与出资占比区采用多维柱状图及列表联动方式呈现主要股东及持股比例，每个股东条目包含名称、股东类型和出资额，支持点击条目跳转至股东详情下钻页，进一步查看该股东的投资关系及关联企业网络。

对外投资总览区以卡片式统计和可展开列表结合的形式展示企业对外投资总额、投资企业数量及地区分布，用户可点击“查看详情”进入对外投资列表页，获取每笔投资的企业名称、投资金额、出资比例、行业领域等明细信息并支持进一步跳转。

参保信息区通过折线图叠加数据表格展示企业历年员工参保人数、参保类型和社保缴费趋势，列表列出当前有效参保人数、五险缴纳基数等关键指标，并提供“参保详情”链接以查询员工参保记录。

股权穿透可视化区以互动式股权穿透图呈现企业股权链条，显示直接和间接持股主体、出资额与持股比例并支持节点点击展开二级股权关系，用户可切换视角查看自然人或法人最终受益结构并在独立页面进行全屏分析。

行业产业与集团关系区利用雷达图和关系网络图展示企业在行业分类、产业链位置及集团母子公司层级，网络图节点标识集团成员企业，边表示控股关系及比例，实现集团结构的全景展现。

**三、业务流程**

系统接收用户选择的企业科研码，调用工商、股东、投资、社保、股权链等多源接口聚合数据并完成字段标准化与画像计算。

后台服务根据企业统一社会信用代码匹配股东及对外投资信息，生成股权结构数据并同步图形渲染服务输出可视化组件。

数据组装完成后，前端按预设布局依次渲染企业概况、股东信息、投资、参保、股权穿透、行业产业及集团关系各模块，实现首页展示。

当用户触发下钻操作（如点击股东条目或对外投资卡片），系统按所选实体加载对应详情页数据，提供二级或三级深度信息并记录用户访问日志。

用户在股权穿透图或集团关系图中展开节点时，前端通过异步请求获取新增股权链数据并动态更新可视化层级，后台同步缓存已查询结果。

所有用户浏览、下钻、导出等行为写入操作日志并供后续统计分析，为企业画像准确度与使用体验持续优化提供数据依据。

**四、操作流程**

用户在科研码门户选择目标企业科研码并进入信息展示页面，首先浏览顶部企业概况获取工商注册核心数据与经营状态。

用户向下滚动至股东信息区，通过柱状图观察持股比例，点击某股东条目自动跳转至股东详情页，查看该股东的基本信息及其投资网络后点击浏览器返回回到主页面。

用户在对外投资区点击“查看详情”进入投资列表，筛选或排序投资金额并跳转至指定投资企业的科研码页面进行交叉分析。

用户切换至参保信息区，通过折线图查看近三年参保人数变化趋势，如需下载数据点击“导出”按钮获取 CSV 文件。

用户在股权穿透区点击“全屏查看”打开独立页面，逐层展开股权节点直至最终受益人并对关键节点右键“复制链路”以备报告引用。

用户最后在行业产业与集团关系区查看集团网络图，对集团成员企业进行悬停查看比例或双击节点跳转其科研码页面，完成集团层级多点分析后关闭页面结束浏览。

研发人员展示

展示企业内部研发人员和企业技术顾问情况、参与项目情况、知识产权情况、工作经历、教育经历、职称证书、政策补助、获奖信息、合作工作轨迹等内容；

研发人员数或企业技术顾问数，支持下钻页面展示企业研发人员清册；

学历，支持下钻展示该研发人员的教育经历清册；

职称，支持下钻页面展示该研发人员的职称晋升详情；

参与项目数，支持下钻页面展示该研发人员参与项目；

研发成果数，支持下钻页面展示该研发人员负责或参与的专利、论文、奖励等明细清册；

参与项目数标签，支持下钻页面展示该企业历年参与项目清册；

知识产权数量标签，支持下钻页面展示该企业历次主导或参与的研发成果清册及各类知识产权数量变化趋势；

政策补助详情，支持下钻页面展示该企业历史获得政策补助清册；

企业获奖数，支持下钻页面展示该企业历史获奖清册及各类奖项分级占比饼图；

**一、功能说明**

研发人员展示功能面向企业、高校与科研机构的管理与洞察场景，通过统一界面将内部研发人员与外部技术顾问的数量、学历层级、职称分布、项目参与度、研发成果、知识产权、政策补助与获奖情况等多维信息进行集中呈现，并支持从宏观指标到明细清册的逐级下钻，以帮助管理层快速识别人才结构、创新贡献与激励成效，亦为业务人员提供便捷的数据查询、对比和导出通道，从而提升研发资源配置效率与决策准确性。

**二、界面结构与要素说明**

指标概览区位于页面顶部，以信息卡形式展示研发人员总数、技术顾问总数、硕博士及以上学历人数、各级职称人数、累计参与项目数、已取得知识产权数量、累计政策补助次数与企业获奖总数等关键指标，每张信息卡内嵌同比环比箭头与点击下钻联动，用户可通过点击任一指标进入对应明细或趋势分析视图。

条件筛选区紧跟指标区展开，包含主体名称智能搜索框、人员类别多选、学历下拉、职称下拉、项目年份范围选择与成果类型多选等筛选元素，并提供“查询”“重置”按钮，可组合条件精准定位特定研发人员或顾问群体。

人员列表区以可分页数据表格展示检索结果，核心列涵盖姓名、人员类别、最高学历、当前职称、在职状态、参与项目数、成果数、知识产权数、最近政策补助、最近获奖信息及操作列，操作列内置“查看画像”“下钻项目”“下钻成果”“下钻补助”“下钻奖项”快捷入口，支持单行展开查看教育经历与工作经历摘要。

人员画像侧栏在点击“查看画像”后自右侧滑入，顶部展示个人基础信息与高清头像，中部通过标签页分别呈现教育经历、工作经历、职称证书、参与项目、知识产权、政策补助与获奖记录，底部配有时间轴视图展示合作工作轨迹，侧栏支持一键导出个人报告与发送邮件分享。

统计分析区位于页面下方，默认以柱状图与堆积面积图组合展示学历分布、职称晋升趋势、年度参与项目数量变化与知识产权累积趋势，同时提供切换按钮在散点图、雷达图及饼图之间切换以观察奖项级别占比与补助类别分布。

**三、业务流程**

用户进入研发人员展示模块，系统根据用户权限拉取可见主体范围并初始化指标概览区所需的聚合统计结果。

当用户修改筛选条件并点击查询时，系统将筛选参数发送至数据服务层，按条件过滤研发人员主数据、项目信息与成果数据后返回列表结果与更新后的指标概览。

用户点击某指标卡片或列表内下钻入口时，系统判断所选指标对应的数据域，并触发明细数据拉取与视图跳转或侧栏加载，同时记录用户行为用于后续推荐与审计。

在人员画像侧栏中切换标签页或展开时间轴时，系统按需调用对应数据集，例如教育经历表、项目参与表、知识产权表等，组装成统一输出对象并渲染到前端组件。

若用户执行导出或分享操作，系统根据当前可见数据生成符合模板规范的PDF或Excel文件，或生成加密分享链接，并在日志中心记录导出内容、时间与受众信息。

当用户关闭侧栏或返回主页面时，系统释放本次会话的缓存资源，同时异步更新访问统计与热度排行，为后续推荐与性能优化提供数据支撑。

**四、操作流程**

进入研发人员展示页面后先浏览顶部指标概览，以快速了解人才规模与创新贡献概况。

在条件筛选区设置主体名称、人员类别及时间范围等过滤条件后点击查询，获取符合要求的人员列表与更新后的指标。

在人员列表中点击某位研发人员的“查看画像”以打开侧栏，切换教育经历、职称证书等标签检查详细履历或合作轨迹。

返回列表后点击“下钻项目”“下钻成果”等操作按钮，查看该人员参与的项目清册、专利论文清册或奖项分级占比饼图。

若需整体分析趋势，可下拉至统计分析区，通过切换不同图表类型观察学历分布、职称晋升与知识产权增长等维度的年度变化。

在任意视图中如需线下存档或共享，点击“导出”选择PDF或Excel，或点击“分享”复制外链，并根据提示完成发送或下载。]`

---

### **最终指令**

请基于以上所有信息，特别是严格遵循项目规范和参考文件风格，为本次指定的开发任务生成对应的HTML原型页面代码。

**请直接开始设计并输出最终的HTML文件代码。不要在代码前添加任何解释性文字，直接从 `<!DOCTYPE html>` 开始。**

在public文件夹下保存html页面

---

_**Assistant**_



---

