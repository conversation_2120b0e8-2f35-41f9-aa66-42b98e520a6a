<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>趋势分析 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .trend-up { color: #10B981; }
        .trend-down { color: #EF4444; }
        .trend-stable { color: #6B7280; }
        .metric-card {
            background: linear-gradient(135deg, #EBF8FF, #F0F9FF);
            border: 1px solid #3B82F6;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">趋势分析</h1>
                        <p class="text-sm text-gray-600">Trend Analysis</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm">
                        <i class="fas fa-calendar text-gray-500"></i>
                        <span class="text-gray-700">2019-2024年度数据</span>
                    </div>
                    <button class="bg-purple-500 text-white px-4 py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 时间维度选择 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">时间维度</h3>
                <div class="flex space-x-4">
                    <button id="time-annual" class="bg-purple-500 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        年度分析
                    </button>
                    <button id="time-quarterly" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        季度分析
                    </button>
                    <button id="time-monthly" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        月度分析
                    </button>
                </div>
            </div>
            
            <!-- 年份选择器 -->
            <div class="grid grid-cols-2 md:grid-cols-6 gap-3">
                <button class="year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium" data-year="2019">2019</button>
                <button class="year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium" data-year="2020">2020</button>
                <button class="year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium" data-year="2021">2021</button>
                <button class="year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium" data-year="2022">2022</button>
                <button class="year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium" data-year="2023">2023</button>
                <button class="year-btn bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium" data-year="2024">2024</button>
            </div>
        </div>

        <!-- 核心趋势指标 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="metric-card rounded-xl p-6 hover-lift">
                <div class="text-center">
                    <i class="fas fa-project-diagram text-blue-600 text-3xl mb-3"></i>
                    <p class="text-gray-700 text-sm mb-2">项目数量增长率</p>
                    <p class="text-3xl font-bold text-blue-600">+18.5%</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年提升3.2个百分点
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-coins text-yellow-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">投资金额增长率</p>
                    <p class="text-3xl font-bold text-yellow-600">+22.8%</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年提升5.6个百分点
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-users text-green-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">参与单位增长率</p>
                    <p class="text-3xl font-bold text-green-600">+15.3%</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年提升2.1个百分点
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="text-center">
                    <i class="fas fa-trophy text-purple-600 text-3xl mb-3"></i>
                    <p class="text-gray-600 text-sm mb-2">成果产出增长率</p>
                    <p class="text-3xl font-bold text-purple-600">+26.4%</p>
                    <p class="text-green-600 text-xs mt-2">
                        <i class="fas fa-arrow-up mr-1"></i>
                        较上年提升8.9个百分点
                    </p>
                </div>
            </div>
        </div>

        <!-- 主要趋势图表 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 项目数量趋势 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                        年度立项项目数变化
                    </h3>
                    <div class="flex space-x-2">
                        <button id="chart1-line" class="text-xs bg-blue-500 text-white px-3 py-1 rounded-lg">折线图</button>
                        <button id="chart1-bar" class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">柱状图</button>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="h-80 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-line text-4xl mb-4"></i>
                        <p class="text-lg">项目数量趋势图</p>
                        <p class="text-sm mt-2">2019-2024年度对比</p>
                    </div>
                </div>

                <!-- 数据点 -->
                <div class="grid grid-cols-3 gap-4 text-sm">
                    <div class="text-center p-2 bg-blue-50 rounded">
                        <p class="text-blue-600 font-bold">2,847</p>
                        <p class="text-gray-600">2024年</p>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <p class="text-gray-700 font-bold">2,402</p>
                        <p class="text-gray-600">2023年</p>
                    </div>
                    <div class="text-center p-2 bg-green-50 rounded">
                        <p class="text-green-600 font-bold">+445</p>
                        <p class="text-gray-600">净增长</p>
                    </div>
                </div>
            </div>

            <!-- 项目金额趋势 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-chart-bar text-green-500 mr-2"></i>
                        年度项目金额变化
                    </h3>
                    <div class="flex space-x-2">
                        <button id="chart2-line" class="text-xs bg-green-500 text-white px-3 py-1 rounded-lg">折线图</button>
                        <button id="chart2-bar" class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">柱状图</button>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="h-80 bg-gradient-to-r from-green-50 to-green-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-bar text-4xl mb-4"></i>
                        <p class="text-lg">投资金额趋势图</p>
                        <p class="text-sm mt-2">单位：亿元</p>
                    </div>
                </div>

                <!-- 数据点 -->
                <div class="grid grid-cols-3 gap-4 text-sm">
                    <div class="text-center p-2 bg-green-50 rounded">
                        <p class="text-green-600 font-bold">156.8亿</p>
                        <p class="text-gray-600">2024年</p>
                    </div>
                    <div class="text-center p-2 bg-gray-50 rounded">
                        <p class="text-gray-700 font-bold">127.6亿</p>
                        <p class="text-gray-600">2023年</p>
                    </div>
                    <div class="text-center p-2 bg-yellow-50 rounded">
                        <p class="text-yellow-600 font-bold">+29.2亿</p>
                        <p class="text-gray-600">净增长</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分领域趋势对比 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <i class="fas fa-layer-group text-indigo-500 mr-2"></i>
                    分领域趋势对比
                </h3>
                <div class="flex space-x-2">
                    <button class="text-sm bg-indigo-500 text-white px-3 py-1 rounded-lg">项目数量</button>
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">投资金额</button>
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">增长率</button>
                </div>
            </div>

            <!-- 大图表区域 -->
            <div class="h-96 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg chart-placeholder flex items-center justify-center mb-6">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-area text-5xl mb-4"></i>
                    <p class="text-xl">分领域趋势对比图</p>
                    <p class="text-sm mt-2">多条趋势线对比各技术领域发展情况</p>
                </div>
            </div>

            <!-- 领域排行 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h4 class="font-medium text-gray-700 mb-3">增长最快领域</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm">人工智能</span>
                            <span class="text-green-600 font-bold text-sm">+45.2%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm">量子技术</span>
                            <span class="text-green-600 font-bold text-sm">+38.7%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm">新能源</span>
                            <span class="text-green-600 font-bold text-sm">+32.4%</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-700 mb-3">稳定增长领域</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm">新材料</span>
                            <span class="text-blue-600 font-bold text-sm">+18.9%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm">生物医药</span>
                            <span class="text-blue-600 font-bold text-sm">+15.6%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm">高端装备</span>
                            <span class="text-blue-600 font-bold text-sm">+12.3%</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-medium text-gray-700 mb-3">需要关注领域</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-2 bg-yellow-50 rounded">
                            <span class="text-sm">传统制造</span>
                            <span class="text-yellow-600 font-bold text-sm">+3.2%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-orange-50 rounded">
                            <span class="text-sm">化工材料</span>
                            <span class="text-orange-600 font-bold text-sm">+1.8%</span>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-red-50 rounded">
                            <span class="text-sm">纺织服装</span>
                            <span class="text-red-600 font-bold text-sm">-2.4%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分层级趋势对比 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 层级项目数量趋势 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-sitemap text-orange-500 mr-2"></i>
                    分层级项目数量趋势
                </h3>
                
                <div class="h-64 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-line text-3xl mb-2"></i>
                        <p class="text-sm">国家级、省级、市级项目趋势</p>
                    </div>
                </div>

                <!-- 层级数据 -->
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">国家级</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">346项</span>
                            <span class="text-green-600 text-xs">+25.8%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">省级</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">892项</span>
                            <span class="text-green-600 text-xs">+19.3%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">市级</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">1,609项</span>
                            <span class="text-green-600 text-xs">+12.7%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 承担主体趋势 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-building text-cyan-500 mr-2"></i>
                    承担主体参与趋势
                </h3>
                
                <div class="h-64 bg-gradient-to-r from-cyan-50 to-cyan-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-bar text-3xl mb-2"></i>
                        <p class="text-sm">企业、高校、科研院所参与趋势</p>
                    </div>
                </div>

                <!-- 主体数据 -->
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">企业</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">1,642项</span>
                            <span class="text-green-600 text-xs">+20.5%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">高校</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">756项</span>
                            <span class="text-green-600 text-xs">+16.8%</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm text-gray-600">科研院所</span>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm font-medium">449项</span>
                            <span class="text-green-600 text-xs">+13.2%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 预测分析 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-crystal-ball text-purple-500 mr-2"></i>
                趋势预测分析
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 预测图表 -->
                <div class="lg:col-span-2">
                    <div class="h-64 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-line text-4xl mb-4"></i>
                            <p class="text-lg">2025-2027年预测趋势</p>
                            <p class="text-sm mt-2">基于历史数据的智能预测</p>
                        </div>
                    </div>
                </div>

                <!-- 预测指标 -->
                <div class="space-y-4">
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-medium text-blue-800 mb-2">2025年预测</h4>
                        <div class="text-sm space-y-1">
                            <div>项目数量：<span class="font-bold">3,200+项</span></div>
                            <div>投资金额：<span class="font-bold">185亿+</span></div>
                            <div>增长率：<span class="font-bold text-green-600">+16%</span></div>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h4 class="font-medium text-green-800 mb-2">重点发展方向</h4>
                        <div class="text-sm space-y-1">
                            <div>• 人工智能技术</div>
                            <div>• 新能源材料</div>
                            <div>• 生物医药研发</div>
                            <div>• 智能制造升级</div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <h4 class="font-medium text-yellow-800 mb-2">风险提示</h4>
                        <div class="text-sm space-y-1">
                            <div>• 注意传统行业转型</div>
                            <div>• 关注技术更新周期</div>
                            <div>• 监控资金使用效率</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 时间维度切换
            const timeButtons = document.querySelectorAll('#time-annual, #time-quarterly, #time-monthly');
            timeButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    timeButtons.forEach(b => {
                        b.className = 'bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors';
                    });
                    this.className = 'bg-purple-500 text-white px-4 py-2 rounded-lg font-medium transition-colors';
                });
            });

            // 年份选择
            const yearButtons = document.querySelectorAll('.year-btn');
            yearButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    yearButtons.forEach(b => {
                        b.className = 'year-btn bg-blue-100 text-blue-600 px-3 py-2 rounded-lg text-sm font-medium';
                    });
                    this.className = 'year-btn bg-blue-500 text-white px-3 py-2 rounded-lg text-sm font-medium';
                });
            });

            // 图表类型切换
            const chartButtons = document.querySelectorAll('[id^="chart"]');
            chartButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const chartGroup = this.id.includes('chart1') ? 'chart1' : 'chart2';
                    const groupButtons = document.querySelectorAll(`[id^="${chartGroup}"]`);
                    
                    groupButtons.forEach(b => {
                        if (chartGroup === 'chart1') {
                            b.className = 'text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors';
                        } else {
                            b.className = 'text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors';
                        }
                    });
                    
                    if (chartGroup === 'chart1') {
                        this.className = 'text-xs bg-blue-500 text-white px-3 py-1 rounded-lg';
                    } else {
                        this.className = 'text-xs bg-green-500 text-white px-3 py-1 rounded-lg';
                    }
                });
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 