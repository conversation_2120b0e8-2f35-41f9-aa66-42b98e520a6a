<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研院所基础信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">科研院所基础信息</h1>
            <p class="mt-2 text-gray-600">科研院所画像首屏入口，展示院所类型、建制属性及合作单位信息</p>
        </div>

        <!-- 概览卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 院所类型卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">院所类型</h3>
                        <p class="text-xl font-semibold text-gray-900">省级重点研究院</p>
                        <p class="text-xs text-gray-400 mt-1">更新于2024-01-15</p>
                    </div>
                </div>
            </div>

            <!-- 成立年份卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">成立年份</h3>
                        <p class="text-xl font-semibold text-gray-900">2015年</p>
                        <p class="text-xs text-gray-400 mt-1">更新于2024-01-15</p>
                    </div>
                </div>
            </div>

            <!-- 所在地区卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-yellow-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">所在地区</h3>
                        <p class="text-xl font-semibold text-gray-900">宁波市高新区</p>
                        <p class="text-xs text-gray-400 mt-1">更新于2024-01-15</p>
                    </div>
                </div>
            </div>

            <!-- 主管部门卡片 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 p-3 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-500">主管部门</h3>
                        <p class="text-xl font-semibold text-gray-900">宁波市科技局</p>
                        <p class="text-xs text-gray-400 mt-1">更新于2024-01-15</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 基础信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">基础信息</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- 法定登记信息 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">法定登记信息</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">法人名称</label>
                            <p class="mt-1 text-sm text-gray-900">宁波市智能装备研究院</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">统一社会信用代码</label>
                            <p class="mt-1 text-sm text-gray-900">91330201MA2B2XXXXX</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">负责人</label>
                            <p class="mt-1 text-sm text-gray-900">张明</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">联系方式</label>
                            <p class="mt-1 text-sm text-gray-900">0574-88888888</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">注册地址</label>
                            <p class="mt-1 text-sm text-gray-900">宁波市高新区聚贤路1299号</p>
                        </div>
                    </div>
                    <div class="mt-6 flex space-x-3">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            营业执照查看
                        </button>
                    </div>
                </div>

                <!-- 运营指标 -->
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4 border-b pb-2">运营指标</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-500">研究领域</label>
                            <p class="mt-1 text-sm text-gray-900">智能制造、机器人技术、工业自动化</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">人员规模</label>
                            <p class="mt-1 text-sm text-gray-900">300+ 
                                <button class="ml-2 text-blue-600 hover:text-blue-800" onclick="openTooltip('personnel')">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">场地面积</label>
                            <p class="mt-1 text-sm text-gray-900">15,000㎡</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">资产规模</label>
                            <p class="mt-1 text-sm text-gray-900">2.5亿元</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500">近三年研发经费</label>
                            <p class="mt-1 text-sm text-gray-900">8,700万元</p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <button class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            官网跳转
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 依托单位与合作共建单位区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">依托单位与合作共建单位</h2>
                <div class="flex space-x-3">
                    <input type="text" placeholder="搜索单位名称" class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                    <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部类型</option>
                        <option>依托单位</option>
                        <option>合作共建单位</option>
                    </select>
                </div>
            </div>

            <!-- 单位树形列表 -->
            <div class="border rounded-lg overflow-hidden">
                <!-- 依托单位 -->
                <div class="border-b">
                    <button onclick="toggleUnitList('trustee')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">依托单位</span>
                        <svg id="trustee-arrow" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="trustee-list" class="divide-y divide-gray-200">
                        <div class="px-4 py-3 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium text-gray-900">宁波市科技局</p>
                                    <p class="text-sm text-gray-500">政府机构 · 宁波市</p>
                                </div>
                                <button onclick="showUnitDetail('1')" class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 合作共建单位 -->
                <div>
                    <button onclick="toggleUnitList('partner')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">合作共建单位</span>
                        <svg id="partner-arrow" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="partner-list" class="divide-y divide-gray-200 hidden">
                        <div class="px-4 py-3 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium text-gray-900">浙江大学</p>
                                    <p class="text-sm text-gray-500">高等院校 · 浙江省</p>
                                </div>
                                <button onclick="showUnitDetail('2')" class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                        </div>
                        <div class="px-4 py-3 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium text-gray-900">宁波市智能制造协会</p>
                                    <p class="text-sm text-gray-500">行业协会 · 宁波市</p>
                                </div>
                                <button onclick="showUnitDetail('3')" class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                        </div>
                        <div class="px-4 py-3 hover:bg-gray-50">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="font-medium text-gray-900">宁波市XX科技集团</p>
                                    <p class="text-sm text-gray-500">企业 · 宁波市</p>
                                </div>
                                <button onclick="showUnitDetail('4')" class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 单位详情侧滑页 -->
    <div id="unitDetail" class="fixed inset-y-0 right-0 w-full max-w-md bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="h-full overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">单位详情</h3>
                <button onclick="hideUnitDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-2">基础信息</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">单位名称：</span>
                                    <span class="font-medium text-gray-900">浙江大学</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">单位类型：</span>
                                    <span class="font-medium text-gray-900">高等院校</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">所在地区：</span>
                                    <span class="font-medium text-gray-900">浙江省杭州市</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">联系人：</span>
                                    <span class="font-medium text-gray-900">李教授</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">联系电话：</span>
                                    <span class="font-medium text-gray-900">0571-88888888</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-2">合作协议摘要</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-700">双方就智能制造领域开展深度合作，共同建立联合实验室，开展技术研发、人才培养和成果转化等工作。合作期限为5年，自2020年1月1日起至2025年12月31日止。</p>
                        </div>
                    </div>

                    <div>
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            查看完整协议
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 人员规模提示弹窗 -->
    <div id="personnelTooltip" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
                <div class="flex justify-between items-start mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">人员规模说明</h3>
                    <button onclick="closeTooltip('personnel')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="text-sm text-gray-700 space-y-2">
                    <p>人员规模统计包括：</p>
                    <ul class="list-disc pl-5">
                        <li>全职科研人员</li>
                        <li>技术支撑人员</li>
                        <li>行政管理人员</li>
                        <li>博士后研究人员</li>
                    </ul>
                    <p class="mt-2">统计时间：2023年12月31日</p>
                </div>
                <div class="mt-6 flex justify-end">
                    <button onclick="closeTooltip('personnel')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 单位列表折叠/展开
        function toggleUnitList(type) {
            const list = document.getElementById(`${type}-list`);
            const arrow = document.getElementById(`${type}-arrow`);
            
            if (list.classList.contains('hidden')) {
                list.classList.remove('hidden');
                arrow.classList.remove('rotate-0');
                arrow.classList.add('rotate-180');
            } else {
                list.classList.add('hidden');
                arrow.classList.remove('rotate-180');
                arrow.classList.add('rotate-0');
            }
        }

        // 单位详情侧滑页
        function showUnitDetail(id) {
            const detailPanel = document.getElementById('unitDetail');
            detailPanel.classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function hideUnitDetail() {
            const detailPanel = document.getElementById('unitDetail');
            detailPanel.classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        // 提示弹窗
        function openTooltip(id) {
            document.getElementById(`${id}Tooltip`).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeTooltip(id) {
            document.getElementById(`${id}Tooltip`).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击外部关闭弹窗
        document.addEventListener('click', function(e) {
            const personnelTooltip = document.getElementById('personnelTooltip');
            if (personnelTooltip && !personnelTooltip.contains(e.target) && e.target.id !== 'personnel') {
                closeTooltip('personnel');
            }
        });
    </script>
</body>
</html>