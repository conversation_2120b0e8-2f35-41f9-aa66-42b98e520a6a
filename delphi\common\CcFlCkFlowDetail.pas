unit CcFlCkFlowDetail;

interface

uses
  Classes;

type
  TCcFlCkFlowDetail = class
  private
    FFlCkflowdetailid: integer;
    FFlCkplandetailid: integer;
    FFlCkflowid: integer;
    FFlCkflowtypenum: integer;
    FYwlxnum: integer;
    FFlconfigtypeid: integer;
    FFlconfigsectypeid: integer;
    FGyscd: string;
    FItem_1: string;
    FItem_2: string;
    FItem_3: string;
    FItem_4: string;
    FItem_5: string;
    FCmgl: integer;
    FDdh: string;
    FKh: string;
    FKz: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FYs: string;
    FJhs: double;
    FCgs: double;
    FLys: double;
    FSccj: string;
    FYjzt: integer;
    FXdrq: string;
    FKsid: integer;
    FDdid: integer;

    FCknamein: string;
    FCknameinamount: double;
    FCknameout: string;
    FCknameoutamount: double;
    FNote: string;
    FRecordtype: integer;
    FCkdate: string;
    FCkuser: string;
    FDetailindex: integer;
    FCm: string;
    FDds: integer;
    FAmount: double;
    FDzdjmj: double;
    FDzdj: double;
    FDzje: double;
    FDzrq: string;
    FDzpk: double;
    FDzfph: string;
    FAuditstate: integer;
    FAuditResult: string;
    FHisPrice: double;
    Fce: double;
    FDzFpje: double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FCkflowcwid: integer;
    FCkYwlxnum: integer;
    FCkDzFph: string;
    FCkContent: string;
    FCkOutName: string;
    FCkDzrq: string;
    FCkDzje: double;
    FCkAuditState: integer;
    FCkAuditStateContent: string;

  public
    property FlCkflowdetailid: integer read FFlCkflowdetailid
      write FFlCkflowdetailid;
    property FlCkPlandetailid: integer read FFlCkplandetailid
      write FFlCkplandetailid;
    property FlCkflowid: integer read FFlCkflowid write FFlCkflowid;
    property FlCkflowtypenum: integer read FFlCkflowtypenum
      write FFlCkflowtypenum;
    property Ywlxnum: integer read FYwlxnum write FYwlxnum;
    property Gyscd: string read FGyscd write FGyscd;
    property Flconfigtypeid: integer read FFlconfigtypeid write FFlconfigtypeid;
    property Flconfigsectypeid: integer read FFlconfigsectypeid
      write FFlconfigsectypeid;
    property Item_1: string read FItem_1 write FItem_1;
    property Item_2: string read FItem_2 write FItem_2;
    property Item_3: string read FItem_3 write FItem_3;
    property Item_4: string read FItem_4 write FItem_4;
    property Item_5: string read FItem_5 write FItem_5;
    property Cmgl: integer read FCmgl write FCmgl;
    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Kz: string read FKz write FKz;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Ys: string read FYs write FYs;
    property Jhs: double read FJhs write FJhs;
    property Cgs: double read FCgs write FCgs;
    property Lys: double read FLys write FLys;
    property Sccj: string read FSccj write FSccj;
    property Yjzt: integer read FYjzt write FYjzt;
    property Xdrq: string read FXdrq write FXdrq;
    property Ksid: integer read FKsid write FKsid;
    property Ddid: integer read FDdid write FDdid;

    property Cknamein: string read FCknamein write FCknamein;
    property Cknameinamount: double read FCknameinamount write FCknameinamount;
    property Cknameout: string read FCknameout write FCknameout;
    property Cknameoutamount: double read FCknameoutamount
      write FCknameoutamount;
    property Note: string read FNote write FNote;
    property Recordtype: integer read FRecordtype write FRecordtype;
    property Ckdate: string read FCkdate write FCkdate;
    property Ckuser: string read FCkuser write FCkuser;

    property Detailindex: integer read FDetailindex write FDetailindex;
    property Cm: string read FCm write FCm;
    property Dds: integer read FDds write FDds;
    property Amount: double read FAmount write FAmount;

    property Dzdjmj: double read FDzdjmj write FDzdjmj;
    property Dzdj: double read FDzdj write FDzdj;
    property Dzje: double read FDzje write FDzje;
    property Dzpk: double read FDzpk write FDzpk;
    property Dzrq: string read FDzrq write FDzrq;
    property Dzfph: string read FDzfph write FDzfph;
    property DzFpje: double read FDzFpje write FDzFpje;

    property Auditstate: integer read FAuditstate write FAuditstate;
    property AuditResult: string read FAuditResult write FAuditResult;
    property HisPrice: double read FHisPrice write FHisPrice;
    property Ce: double read Fce write Fce;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

    property Ckflowcwid: integer read FCkflowcwid write FCkflowcwid;
    property CkYwlxnum: integer read FCkYwlxnum write FCkYwlxnum;
    property CkDzFph: string read FCkDzFph write FCkDzFph;
    property CkContent: string read FCkContent write FCkContent;
    property CkOutName: string read FCkOutName write FCkOutName;
    property CkDzrq: string read FCkDzrq write FCkDzrq;
    property CkDzje: double read FCkDzje write FCkDzje;
    property CkAuditState: integer read FCkAuditState write FCkAuditState;
    property CkAuditStateContent: string read FCkAuditStateContent
      write FCkAuditStateContent;
  end;

implementation

end.
