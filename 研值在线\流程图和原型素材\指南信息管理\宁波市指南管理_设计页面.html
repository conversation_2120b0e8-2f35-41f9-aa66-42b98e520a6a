<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宁波市指南管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">宁波市指南管理</h1>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="guideName" class="block text-sm font-medium text-gray-700 mb-1">指南名称</label>
                    <input type="text" id="guideName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入指南名称">
                </div>
                <div>
                    <label for="publishYear" class="block text-sm font-medium text-gray-700 mb-1">发布年份</label>
                    <select id="publishYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部年份</option>
                        <option value="2024">2024年</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                    </select>
                </div>
                <div>
                    <label for="techField" class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                    <select id="techField" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="new-energy">新能源</option>
                        <option value="material">新材料</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">计划类别</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-2 py-1 text-xs border border-blue-500 text-blue-500 rounded hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500">重点研发</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">技术创新</button>
                        <button class="px-2 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">成果转化</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">已发布</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="status" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">待审核</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 指南列表区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-medium text-gray-800">指南列表</h2>
                    <div class="flex space-x-3">
                        <div class="relative">
                            <button onclick="toggleExportDropdown()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出
                            </button>
                            <div id="exportDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 hidden">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出Excel</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出CSV</a>
                                </div>
                            </div>
                        </div>
                        <button onclick="openGuideModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                            新增指南
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">指南名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">计划类别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市人工智能产业发展指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市科技局</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">人工智能</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">重点研发</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 10:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewGuide('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editGuide('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteGuide('1')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市生物医药创新指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市卫健委</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">生物医药</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">技术创新</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待审核</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 14:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewGuide('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editGuide('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteGuide('2')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新能源技术应用指南</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">宁波市发改委</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">新能源</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-500">成果转化</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewGuide('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                <button onclick="editGuide('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button onclick="deleteGuide('3')" class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 12 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 指南编辑弹窗 -->
    <div id="guideModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">指南编辑</h3>
                    <button onclick="closeModal('guideModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">指南名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入指南名称" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">发布单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入发布单位" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">技术领域 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择技术领域</option>
                                <option value="ai">人工智能</option>
                                <option value="bio">生物医药</option>
                                <option value="new-energy">新能源</option>
                                <option value="material">新材料</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">计划类别 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="">请选择计划类别</option>
                                <option value="key">重点研发</option>
                                <option value="tech">技术创新</option>
                                <option value="transfer">成果转化</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">研究内容 <span class="text-red-500">*</span></label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入研究内容" required></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">考核指标 <span class="text-red-500">*</span></label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入考核指标" required></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">申报要求 <span class="text-red-500">*</span></label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入申报要求" required></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-4">
                                <label for="file-upload" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .pdf, .doc, .docx 格式</span>
                                </label>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".pdf,.doc,.docx">
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('guideModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 指南详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">指南详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">指南名称</label>
                            <p class="text-sm text-gray-900">宁波市人工智能产业发展指南</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">发布单位</label>
                            <p class="text-sm text-gray-900">宁波市科技局</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                            <p class="text-sm text-gray-900">人工智能</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">计划类别</label>
                            <p class="text-sm text-gray-900">重点研发</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已发布</span>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">最后更新时间</label>
                            <p class="text-sm text-gray-900">2024-01-15 10:30</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">研究内容</label>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-sm text-gray-900">1. 人工智能基础理论研究<br>2. 智能芯片与系统研发<br>3. 智能算法与应用场景开发</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">考核指标</label>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-sm text-gray-900">1. 发表高水平论文10篇以上<br>2. 申请发明专利20项以上<br>3. 形成3个以上示范应用场景</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">申报要求</label>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-sm text-gray-900">1. 在宁波市注册的企业或科研机构<br>2. 具有相关领域研发基础<br>3. 项目团队具备高级职称人员不少于2人</p>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">附件</label>
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-800">宁波市人工智能产业发展指南.pdf</a>
                        </div>
                    </div>
                    <div class="flex justify-end pt-4">
                        <button type="button" onclick="closeModal('detailModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openGuideModal() {
            document.getElementById('guideModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function viewGuide(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function editGuide(id) {
            document.getElementById('guideModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function deleteGuide(id) {
            if (confirm('确定要删除这条指南吗？此操作不可恢复！')) {
                alert('指南已删除 (原型演示)');
            }
        }

        function toggleExportDropdown() {
            document.getElementById('exportDropdown').classList.toggle('hidden');
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.id === 'guideModal' || e.target.id === 'detailModal') {
                closeModal(e.target.id);
            }
            
            // 点击其他区域关闭导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            if (!e.target.closest('#exportDropdown') && !e.target.matches('[onclick="toggleExportDropdown()"]')) {
                exportDropdown.classList.add('hidden');
            }
        });

        // ESC键关闭弹窗
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (!document.getElementById('guideModal').classList.contains('hidden')) {
                    closeModal('guideModal');
                }
                if (!document.getElementById('detailModal').classList.contains('hidden')) {
                    closeModal('detailModal');
                }
            }
        });

        // 表单提交处理
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('表单已提交 (原型演示)');
                closeModal('guideModal');
            });
        });
    </script>
</body>
</html>