unit ErrorInfoFrm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, RzButton, ExtCtrls, RzPanel, StdCtrls, RzLabel, pngimage,
  PngFunctions, AdvGlowButton, HtImage, frxClass;

type
  TErrorInfoForm = class(TForm)
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    RzPanel3: TRzPanel;
    Image1: TImage;
    L_Info1: TRzLabel;
    L_Info3: TRzLabel;
    Btn_Save: TAdvGlowButton;
    procedure B_CloseClick(Sender: TObject);
    procedure FormShow(Sender: TObject);
    procedure Btn_SaveClick(Sender: TObject);
    procedure HTImage2Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure Init(infoTitle, infoErrorDes, infoDetail: string);
  end;

var
  ErrorInfoForm: TErrorInfoForm;
  FInfoTitle, FInfoErrorDes, FInfoDetail: string;

implementation

{$R *.dfm}

procedure TErrorInfoForm.Btn_SaveClick(Sender: TObject);
begin
  self.Close;
end;

procedure TErrorInfoForm.B_CloseClick(Sender: TObject);
begin
  self.Close;
end;

procedure TErrorInfoForm.Init(infoTitle, infoErrorDes, infoDetail: string);
begin
  FInfoTitle := infoTitle;
  FInfoErrorDes := infoErrorDes;
  FInfoDetail := infoDetail;
  self.Caption := infoTitle;
end;

procedure TErrorInfoForm.FormShow(Sender: TObject);
begin
  L_Info1.Caption := FInfoTitle;
  // L_Info2.Caption := FInfoErrorDes;
  L_Info3.Caption := FInfoDetail;
end;

procedure TErrorInfoForm.HTImage2Click(Sender: TObject);
begin
  self.Close;
end;

end.
