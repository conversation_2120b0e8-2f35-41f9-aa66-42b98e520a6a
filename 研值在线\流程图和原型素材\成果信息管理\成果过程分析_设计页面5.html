<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果过程分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">成果过程分析</h1>

        <!-- 筛选控制区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类别</label>
                    <div class="flex space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="type" class="h-4 w-4 text-blue-600" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="type" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">专利</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="type" class="h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">论文</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部状态</option>
                        <option>申请中</option>
                        <option>已授权</option>
                        <option>已失效</option>
                        <option>投稿中</option>
                        <option>已发表</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部领域</option>
                        <option>电子信息</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>智能制造</option>
                        <option>节能环保</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    分析
                </button>
            </div>
        </div>

        <!-- 过程指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white shadow-md rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">专利申请量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">1,245</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">12.5%</span>
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <canvas id="sparkline1"></canvas>
                </div>
            </div>
            
            <div class="bg-white shadow-md rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">专利授权率</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">68.2%</p>
                    </div>
                    <div class="flex items-center text-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">3.2%</span>
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <canvas id="sparkline2"></canvas>
                </div>
            </div>
            
            <div class="bg-white shadow-md rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">论文发表量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">856</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">8.7%</span>
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <canvas id="sparkline3"></canvas>
                </div>
            </div>
            
            <div class="bg-white shadow-md rounded-lg p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">平均影响因子</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">4.56</p>
                    </div>
                    <div class="flex items-center text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm ml-1">0.32</span>
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <canvas id="sparkline4"></canvas>
                </div>
            </div>
        </div>

        <!-- 专利过程分析区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-800">专利过程分析</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">申请阶段</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">审查阶段</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">授权阶段</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="patentProcessChart"></canvas>
            </div>
        </div>

        <!-- 论文过程分析区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h3 class="text-lg font-medium text-gray-800 mb-6">论文过程分析</h3>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2">
                    <div class="h-80">
                        <canvas id="paperProcessChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="h-80">
                        <canvas id="paperDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 对比趋势区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-medium text-gray-800">授权率与发表率对比趋势</h3>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">季度</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">半年</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200">年度</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="trendComparisonChart"></canvas>
            </div>
        </div>

        <!-- 明细表格区 -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-800">成果明细列表</h3>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出Excel
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 1H8a2 2 0 01-2-2V8a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            生成报告
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前阶段</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">历时(天)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CN202310123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种新型纳米材料制备方法</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">授权</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">186</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showDetail('patent1')" class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出JSON</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">10.1016/j.matdes.2023.112345</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">论文</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">基于深度学习的材料性能预测模型研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">已发表</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">92</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showDetail('paper1')" class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出JSON</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CN202310654321.0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种环保型污水处理装置</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">审查中</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">120</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showDetail('patent2')" class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出JSON</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">10.1038/s41586-023-06789-9</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">论文</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">量子计算在材料设计中的应用突破</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">已发表</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showDetail('paper2')" class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出JSON</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">CN202310987654.3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专利</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一种智能家居控制系统</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">申请中</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="showDetail('patent3')" class="text-blue-600 hover:text-blue-900 mr-3">查看轨迹</button>
                                <button class="text-indigo-600 hover:text-indigo-900">导出JSON</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 2,103 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">成果详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">成果编号</label>
                                <div class="text-sm text-gray-900">CN202310123456.7</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">类型</label>
                                <div class="text-sm text-gray-900">专利</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">标题</label>
                                <div class="text-sm text-gray-900">一种新型纳米材料制备方法</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">当前阶段</label>
                                <div class="text-sm text-gray-900">授权</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">历时(天)</label>
                                <div class="text-sm text-gray-900">186</div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请人</label>
                                <div class="text-sm text-gray-900">宁波市新材料研究院</div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">过程轨迹</label>
                            <div class="h-64">
                                <canvas id="processTimelineChart"></canvas>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键节点</label>
                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">申请提交</p>
                                        <p class="text-sm text-gray-500">2023-01-15</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">初审通过</p>
                                        <p class="text-sm text-gray-500">2023-03-20</p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <p class="text-sm font-medium text-gray-900">授权公告</p>
                                        <p class="text-sm text-gray-500">2023-07-20</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeModal('detailModal')" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        导出完整报告
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function showDetail(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // ========= 初始化所有图表 =========
            // Sparkline图表
            const sparklineOptions = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: { x: { display: false }, y: { display: false } },
                elements: { line: { tension: 0.4, borderWidth: 2 }, point: { radius: 0 } }
            };

            // 专利申请量Sparkline
            const sparkline1 = document.getElementById('sparkline1');
            if (sparkline1) {
                new Chart(sparkline1, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [120, 190, 130, 170, 150, 180],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                });
            }

            // 专利授权率Sparkline
            const sparkline2 = document.getElementById('sparkline2');
            if (sparkline2) {
                new Chart(sparkline2, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [65, 59, 70, 72, 68, 71],
                            borderColor: '#EF4444',
                            backgroundColor: 'rgba(239, 68, 68, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                });
            }

            // 论文发表量Sparkline
            const sparkline3 = document.getElementById('sparkline3');
            if (sparkline3) {
                new Chart(sparkline3, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [80, 90, 85, 95, 100, 110],
                            borderColor: '#10B981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                });
            }

            // 平均影响因子Sparkline
            const sparkline4 = document.getElementById('sparkline4');
            if (sparkline4) {
                new Chart(sparkline4, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: [4.2, 4.3, 4.5, 4.6, 4.5, 4.7],
                            borderColor: '#F59E0B',
                            backgroundColor: 'rgba(245, 158, 11, 0.1)',
                            fill: true
                        }]
                    },
                    options: sparklineOptions
                });
            }

            // 专利过程分析图表
            const patentProcessChart = document.getElementById('patentProcessChart');
            if (patentProcessChart) {
                new Chart(patentProcessChart, {
                    type: 'bar',
                    data: {
                        labels: ['发明专利', '实用新型', '外观设计'],
                        datasets: [
                            {
                                label: '申请量',
                                data: [850, 620, 320],
                                backgroundColor: '#3B82F6'
                            },
                            {
                                label: '授权量',
                                data: [580, 520, 280],
                                backgroundColor: '#10B981'
                            },
                            {
                                label: '失效量',
                                data: [120, 80, 40],
                                backgroundColor: '#EF4444'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 论文过程分析图表
            const paperProcessChart = document.getElementById('paperProcessChart');
            if (paperProcessChart) {
                new Chart(paperProcessChart, {
                    type: 'line',
                    data: {
                        labels: ['2020', '2021', '2022', '2023'],
                        datasets: [
                            {
                                label: '投稿量',
                                data: [120, 150, 180, 210],
                                borderColor: '#3B82F6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: '录用量',
                                data: [80, 110, 140, 170],
                                borderColor: '#10B981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: '发表量',
                                data: [70, 100, 130, 160],
                                borderColor: '#8B5CF6',
                                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                                fill: true,
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 论文分布图表
            const paperDistributionChart = document.getElementById('paperDistributionChart');
            if (paperDistributionChart) {
                new Chart(paperDistributionChart, {
                    type: 'pie',
                    data: {
                        labels: ['SCI一区', 'SCI二区', 'SCI三区', 'SCI四区', 'EI', '其他'],
                        datasets: [{
                            data: [120, 180, 150, 100, 200, 50],
                            backgroundColor: [
                                '#3B82F6',
                                '#10B981',
                                '#F59E0B',
                                '#EF4444',
                                '#8B5CF6',
                                '#64748B'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 对比趋势图表
            const trendComparisonChart = document.getElementById('trendComparisonChart');
            if (trendComparisonChart) {
                new Chart(trendComparisonChart, {
                    type: 'line',
                    data: {
                        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                        datasets: [
                            {
                                label: '专利授权率',
                                data: [65, 59, 70, 72],
                                borderColor: '#3B82F6',
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                fill: true,
                                tension: 0.4,
                                yAxisID: 'y'
                            },
                            {
                                label: '论文发表率',
                                data: [75, 80, 78, 85],
                                borderColor: '#10B981',
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                fill: true,
                                tension: 0.4,
                                yAxisID: 'y1'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                min: 50,
                                max: 90
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                min: 50,
                                max: 90,
                                grid: {
                                    drawOnChartArea: false
                                }
                            }
                        }
                    }
                });
            }

            // 详情弹窗中的时间线图表
            const processTimelineChart = document.getElementById('processTimelineChart');
            if (processTimelineChart) {
                new Chart(processTimelineChart, {
                    type: 'line',
                    data: {
                        labels: ['申请', '初审', '实审', '授权', '维持'],
                        datasets: [{
                            data: [1, 2, 3, 4, 5],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                display: false
                            }
                        }
                    }
                });
            }

            // ========= 为所有弹窗绑定"点击外部关闭"事件 =========
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('detailModal');
                }
            });

            // ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeModal('detailModal');
                }
            });
        });
    </script>
</body>
</html>