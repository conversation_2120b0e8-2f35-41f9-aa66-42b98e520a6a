'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, AlertCircle, Bell, Info, ArrowDown } from "lucide-react"

export function AssetAlerts() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>资产告警监控</CardTitle>
        <Badge variant="outline" className="bg-red-50 text-red-600">
          3个紧急告警
        </Badge>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* 告警统计 */}
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">紧急告警</span>
              </div>
              <div className="text-xl font-semibold text-red-600">3</div>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-2 mb-1">
                <AlertCircle className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">重要告警</span>
              </div>
              <div className="text-xl font-semibold text-orange-600">5</div>
            </div>
          </div>

          {/* 告警列表 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <div>
                  <div className="font-medium">存储空间不足</div>
                  <div className="text-sm text-gray-500">剩余空间低于10%</div>
                </div>
              </div>
              <Badge className="bg-red-50 text-red-600">紧急</Badge>
            </div>

            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
              <div className="flex items-center gap-3">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <div>
                  <div className="font-medium">CPU使用率过高</div>
                  <div className="text-sm text-gray-500">使用率超过90%</div>
                </div>
              </div>
              <Badge className="bg-red-50 text-red-600">紧急</Badge>
            </div>

            <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-4 w-4 text-orange-500" />
                <div>
                  <div className="font-medium">网络延迟异常</div>
                  <div className="text-sm text-gray-500">响应时间 > 200ms</div>
                </div>
              </div>
              <Badge className="bg-orange-50 text-orange-600">重要</Badge>
            </div>
          </div>

          {/* 告警趋势 */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Info className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">24小时告警趋势</span>
              </div>
              <div className="flex items-center gap-2">
                <ArrowDown className="h-3 w-3 text-green-500" />
                <span className="text-sm font-medium text-green-600">-15%</span>
              </div>
            </div>
            
            {/* 趋势图表区域 */}
            <div className="relative h-[60px] bg-white rounded-lg p-2">
              {/* 这里可以添加实际的趋势图表 */}
              <div className="absolute bottom-0 left-0 right-0 h-[40px]">
                {/* 示例趋势线 - 实际项目中替换为真实图表组件 */}
                <div className="relative h-full">
                  <div className="absolute bottom-0 left-0 right-0 h-[2px] bg-gray-100"></div>
                  <div className="absolute bottom-[10px] left-0 right-0 h-[2px] bg-gray-100"></div>
                  <div className="absolute bottom-[20px] left-0 right-0 h-[2px] bg-gray-100"></div>
                  <div className="absolute bottom-[30px] left-0 right-0 h-[2px] bg-gray-100"></div>
                </div>
              </div>
            </div>

            {/* 图例说明 */}
            <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
              <span>00:00</span>
              <span>06:00</span>
              <span>12:00</span>
              <span>18:00</span>
              <span>24:00</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 