<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校科技成果展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">高等院校科技成果展示</h1>
            <p class="text-gray-600">集中呈现专利、商标、软件著作权等知识产权的申请与授权量、当前拥有量及成果转化情况</p>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">当年知识产权申请量</p>
                        <p class="text-2xl font-bold text-gray-900">1,245</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm">12.5%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">当年知识产权授权量</p>
                        <p class="text-2xl font-bold text-gray-900">856</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm">8.3%</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">累计知识产权拥有量</p>
                        <p class="text-2xl font-bold text-gray-900">5,672</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">年度成果转化金额(万元)</p>
                        <p class="text-2xl font-bold text-gray-900">3,245</p>
                    </div>
                    <div class="text-green-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd" />
                        </svg>
                        <span class="text-sm">25.7%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai">人工智能</option>
                        <option value="bio">生物医药</option>
                        <option value="material">新材料</option>
                        <option value="energy">新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">知识产权类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="patent">专利</option>
                        <option value="copyright">软件著作权</option>
                        <option value="trademark">商标</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年份范围</label>
                    <div class="flex space-x-2">
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                        <span class="flex items-center">至</span>
                        <select class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>2024</option>
                            <option>2023</option>
                            <option>2022</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入成果名称或编号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 图表分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">申请与授权年度对比</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-md">柱状图</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200">折线图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="applyChart"></canvas>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">技术领域分布</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-md">饼图</button>
                        <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-md hover:bg-gray-200">雷达图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="techChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 知识产权列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">知识产权列表</h2>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称/标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">发明专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">基于深度学习的图像识别方法</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310123456.7</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已转让</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">软件著作权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智能医疗数据分析系统V1.0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023SR123456</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">许可中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">发明专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">一种新型锂电池正极材料</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310654321.0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">未转化</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">转化</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">124</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-semibold text-gray-900">科技成果详情</h3>
                <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧基础信息 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">基础信息</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">成果名称</p>
                                <p class="text-sm font-medium text-gray-900">基于深度学习的图像识别方法</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">成果类型</p>
                                <p class="text-sm font-medium text-gray-900">发明专利</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请号</p>
                                <p class="text-sm font-medium text-gray-900">CN202310123456.7</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">授权号</p>
                                <p class="text-sm font-medium text-gray-900">CN1234567B</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请日期</p>
                                <p class="text-sm font-medium text-gray-900">2023-01-15</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">授权日期</p>
                                <p class="text-sm font-medium text-gray-900">2023-12-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">技术领域</p>
                                <p class="text-sm font-medium text-gray-900">人工智能/计算机视觉</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">发明人</p>
                                <p class="text-sm font-medium text-gray-900">张三, 李四, 王五</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">技术摘要</h4>
                        <p class="text-sm text-gray-700">
                            本发明公开了一种基于深度学习的图像识别方法，该方法通过构建多尺度特征融合网络，有效提升了复杂场景下的目标检测精度。具体包括以下步骤：首先对输入图像进行预处理，然后通过卷积神经网络提取多尺度特征，接着使用注意力机制进行特征融合，最后通过分类器输出识别结果。本方法在公开数据集上的准确率达到98.7%，比现有方法提高了5.2个百分点。
                        </p>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">转化信息</h4>
                        <div class="space-y-3">
                            <div class="border-b pb-3">
                                <p class="text-sm font-medium text-gray-900">转让信息</p>
                                <div class="grid grid-cols-3 gap-2 mt-2 text-sm">
                                    <div>
                                        <p class="text-gray-500">受让方</p>
                                        <p>宁波市智能科技公司</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500">转让金额</p>
                                        <p>120万元</p>
                                    </div>
                                    <div>
                                        <p class="text-gray-500">转让日期</p>
                                        <p>2023-10-15</p>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">许可信息</p>
                                <p class="text-sm text-gray-500 mt-2">暂无许可信息</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧操作区 -->
                <div>
                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">操作</h4>
                        <div class="space-y-3">
                            <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                下载专利文件
                            </button>
                            <button class="w-full px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                                编辑信息
                            </button>
                            <button class="w-full px-4 py-2 border border-indigo-600 text-indigo-600 rounded-md hover:bg-indigo-50 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                                </svg>
                                登记转化
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h4 class="text-lg font-medium text-gray-900 mb-3">状态历史</h4>
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-900">2023-12-20</p>
                                <p class="text-sm text-gray-500">专利授权</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">2023-10-15</p>
                                <p class="text-sm text-gray-500">专利转让</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">2023-07-20</p>
                                <p class="text-sm text-gray-500">实质审查通过</p>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">2023-03-15</p>
                                <p class="text-sm text-gray-500">公开</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 申请与授权图表
            const applyCtx = document.getElementById('applyChart').getContext('2d');
            new Chart(applyCtx, {
                type: 'bar',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '申请量',
                            data: [856, 923, 1045, 1245],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)'
                        },
                        {
                            label: '授权量',
                            data: [523, 612, 745, 856],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 技术领域分布图表
            const techCtx = document.getElementById('techChart').getContext('2d');
            new Chart(techCtx, {
                type: 'pie',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(156, 163, 175, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>