<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器设备使用分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">仪器设备使用分析</h1>
                    <p class="mt-2 text-sm text-gray-600">对科研仪器设备的使用行为、功能参数、图片资源及设备提供方等多维数据进行统计分析与可视化展示</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出报表
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 仪器基础信息概览区 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">仪器基础信息概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <!-- 总设备数量 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">设备总数</p>
                            <p class="text-2xl font-semibold text-gray-900">2,847</p>
                        </div>
                    </div>
                </div>
                
                <!-- 总原值 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">总原值</p>
                            <p class="text-2xl font-semibold text-gray-900">45.6亿</p>
                        </div>
                    </div>
                </div>
                
                <!-- 制造商数量 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">制造商数量</p>
                            <p class="text-2xl font-semibold text-gray-900">156</p>
                        </div>
                    </div>
                </div>
                
                <!-- 设备分类 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">设备分类</p>
                            <p class="text-2xl font-semibold text-gray-900">23</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 基础信息图表 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 制造商分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">主要制造商分布</h3>
                    <div class="h-64">
                        <canvas id="manufacturerChart"></canvas>
                    </div>
                </div>
                
                <!-- 产地分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">产地国别分布</h3>
                    <div class="h-64">
                        <canvas id="countryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 仪器使用状况统计区 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">仪器使用状况统计</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                <!-- 使用率统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">设备使用率分布</h3>
                    <div class="h-64">
                        <canvas id="usageRateChart"></canvas>
                    </div>
                </div>
                
                <!-- 预约状态 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">预约状态统计</h3>
                    <div class="h-64">
                        <canvas id="bookingStatusChart"></canvas>
                    </div>
                </div>
                
                <!-- 月度使用趋势 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">月度使用趋势</h3>
                    <div class="h-64">
                        <canvas id="monthlyTrendChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 使用时间分析 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">设备使用时间分析</h3>
                <div class="h-80">
                    <canvas id="usageTimeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 功能参数与应用领域区 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">功能参数与应用领域</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 主要功能分类 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">主要功能分类</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">光谱分析</span>
                            <span class="text-sm text-gray-500">856台</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">色谱分离</span>
                            <span class="text-sm text-gray-500">642台</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">质谱检测</span>
                            <span class="text-sm text-gray-500">523台</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">电化学测试</span>
                            <span class="text-sm text-gray-500">387台</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">显微观察</span>
                            <span class="text-sm text-gray-500">298台</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <span class="font-medium text-gray-900">其他功能</span>
                            <span class="text-sm text-gray-500">141台</span>
                        </div>
                    </div>
                </div>
                
                <!-- 应用领域词云 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">主要应用领域</h3>
                    <div class="flex flex-wrap gap-3">
                        <span class="px-4 py-2 bg-blue-100 text-blue-800 text-lg font-medium rounded-full">生物医学</span>
                        <span class="px-3 py-2 bg-green-100 text-green-800 text-base font-medium rounded-full">材料科学</span>
                        <span class="px-4 py-2 bg-yellow-100 text-yellow-800 text-lg font-medium rounded-full">环境科学</span>
                        <span class="px-3 py-2 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">食品安全</span>
                        <span class="px-4 py-2 bg-red-100 text-red-800 text-base font-medium rounded-full">化学分析</span>
                        <span class="px-3 py-2 bg-indigo-100 text-indigo-800 text-sm font-medium rounded-full">药物研发</span>
                        <span class="px-4 py-2 bg-pink-100 text-pink-800 text-lg font-medium rounded-full">物理研究</span>
                        <span class="px-3 py-2 bg-gray-100 text-gray-800 text-sm font-medium rounded-full">地质勘探</span>
                        <span class="px-3 py-2 bg-blue-100 text-blue-800 text-base font-medium rounded-full">能源技术</span>
                        <span class="px-3 py-2 bg-green-100 text-green-800 text-sm font-medium rounded-full">农业科学</span>
                        <span class="px-4 py-2 bg-yellow-100 text-yellow-800 text-base font-medium rounded-full">海洋科学</span>
                        <span class="px-3 py-2 bg-purple-100 text-purple-800 text-sm font-medium rounded-full">航空航天</span>
                    </div>
                </div>
            </div>
            
            <!-- 技术指标统计 -->
            <div class="mt-6 bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">核心技术指标分布</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">检测精度等级</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">超高精度 (ppm级)</span>
                                <span class="text-sm font-medium text-gray-900">234台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">高精度 (ppb级)</span>
                                <span class="text-sm font-medium text-gray-900">567台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">中等精度 (mg级)</span>
                                <span class="text-sm font-medium text-gray-900">1,234台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">常规精度</span>
                                <span class="text-sm font-medium text-gray-900">812台</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">检测范围覆盖</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">宽范围检测</span>
                                <span class="text-sm font-medium text-gray-900">1,456台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">中等范围</span>
                                <span class="text-sm font-medium text-gray-900">987台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">专用范围</span>
                                <span class="text-sm font-medium text-gray-900">404台</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-3">自动化程度</h4>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">全自动</span>
                                <span class="text-sm font-medium text-gray-900">1,123台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">半自动</span>
                                <span class="text-sm font-medium text-gray-900">1,345台</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">手动操作</span>
                                <span class="text-sm font-medium text-gray-900">379台</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图片资源概览区 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">图片资源概览</h2>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 图片统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">图片资源统计</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">总图片数量</span>
                            <span class="text-lg font-semibold text-gray-900">8,542</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">高清图片</span>
                            <span class="text-lg font-semibold text-green-600">6,234</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">对外可见</span>
                            <span class="text-lg font-semibold text-blue-600">7,123</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">内部使用</span>
                            <span class="text-lg font-semibold text-yellow-600">1,419</span>
                        </div>
                    </div>
                </div>
                
                <!-- 图片类型分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">图片类型分布</h3>
                    <div class="h-64">
                        <canvas id="imageTypeChart"></canvas>
                    </div>
                </div>
                
                <!-- 图片质量分析 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">图片质量分析</h3>
                    <div class="h-64">
                        <canvas id="imageQualityChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 图片展示区 -->
            <div class="mt-6 bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">设备图片展示</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">网格视图</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">列表视图</button>
                    </div>
                </div>
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?laboratory,microscope" alt="显微镜" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">可见</span>
                        </div>
                    </div>
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?laboratory,spectrometer" alt="光谱仪" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">可见</span>
                        </div>
                    </div>
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?laboratory,chromatography" alt="色谱仪" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">内部</span>
                        </div>
                    </div>
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?laboratory,equipment" alt="实验设备" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">可见</span>
                        </div>
                    </div>
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?scientific,instrument" alt="科学仪器" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">可见</span>
                        </div>
                    </div>
                    <div class="relative group cursor-pointer">
                        <img src="https://source.unsplash.com/300x200?mass,spectrometer" alt="质谱仪" class="w-full h-32 object-cover rounded-lg">
                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <div class="absolute top-2 right-2">
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">内部</span>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-sm hover:bg-gray-50">查看更多图片</button>
                </div>
            </div>
        </div>

        <!-- 仪器提供方分析区 -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">仪器提供方分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- 单位类型分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">单位类型分布</h3>
                    <div class="h-64">
                        <canvas id="unitTypeChart"></canvas>
                    </div>
                </div>
                
                <!-- 地区分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">地区分布统计</h3>
                    <div class="h-64">
                        <canvas id="regionDistributionChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- 提供方详细信息表 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">仪器提供方详细信息</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">筛选</button>
                            <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">导出</button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在地区</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备数量</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总原值(万元)</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">共享范围</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对外可见</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">市科学院</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研院所</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">156</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12,456.8</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">全市范围</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">是</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">市第一医院</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗机构</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">89</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8,234.5</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗联盟</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">部分</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">市大学</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高等院校</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">大学城</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">234</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15,678.9</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高校联盟</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">是</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">市环保局</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">政府机构</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">经开区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">67</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4,567.2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">政府部门</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">是</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">市检测中心</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">检测机构</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">123</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9,876.3</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">全市范围</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">是</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                    <button class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500">
                            显示第 1-5 条，共 156 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 图表初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 制造商分布图表
            const manufacturerCtx = document.getElementById('manufacturerChart').getContext('2d');
            new Chart(manufacturerCtx, {
                type: 'bar',
                data: {
                    labels: ['赛默飞世尔', '安捷伦', '岛津', '布鲁克', '沃特世', '其他'],
                    datasets: [{
                        label: '设备数量',
                        data: [456, 387, 298, 234, 189, 1283],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 产地分布图表
            const countryCtx = document.getElementById('countryChart').getContext('2d');
            new Chart(countryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['美国', '德国', '日本', '中国', '英国', '其他'],
                    datasets: [{
                        data: [1234, 856, 567, 423, 298, 469],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 使用率分布图表
            const usageRateCtx = document.getElementById('usageRateChart').getContext('2d');
            new Chart(usageRateCtx, {
                type: 'pie',
                data: {
                    labels: ['高使用率(>80%)', '中等使用率(50-80%)', '低使用率(<50%)', '闲置'],
                    datasets: [{
                        data: [567, 1234, 856, 190],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 预约状态图表
            const bookingStatusCtx = document.getElementById('bookingStatusChart').getContext('2d');
            new Chart(bookingStatusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['已预约', '可预约', '维护中', '暂停服务'],
                    datasets: [{
                        data: [1456, 987, 234, 170],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 月度使用趋势图表
            const monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
            new Chart(monthlyTrendCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '使用次数',
                        data: [1234, 1456, 1678, 1345, 1567, 1789],
                        borderColor: 'rgba(59, 130, 246, 1)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 使用时间分析图表
            const usageTimeCtx = document.getElementById('usageTimeChart').getContext('2d');
            new Chart(usageTimeCtx, {
                type: 'bar',
                data: {
                    labels: ['0-2小时', '2-4小时', '4-6小时', '6-8小时', '8-10小时', '10-12小时', '12小时以上'],
                    datasets: [{
                        label: '使用次数',
                        data: [234, 567, 890, 1234, 987, 456, 123],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 图片类型分布图表
            const imageTypeCtx = document.getElementById('imageTypeChart').getContext('2d');
            new Chart(imageTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['设备外观', '操作界面', '内部结构', '配件附件', '使用场景'],
                    datasets: [{
                        data: [2456, 1789, 1234, 987, 2076],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 图片质量分析图表
            const imageQualityCtx = document.getElementById('imageQualityChart').getContext('2d');
            new Chart(imageQualityCtx, {
                type: 'bar',
                data: {
                    labels: ['超高清', '高清', '标清', '低清'],
                    datasets: [{
                        label: '图片数量',
                        data: [2345, 3889, 1567, 741],
                        backgroundColor: [
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 单位类型分布图表
            const unitTypeCtx = document.getElementById('unitTypeChart').getContext('2d');
            new Chart(unitTypeCtx, {
                type: 'pie',
                data: {
                    labels: ['科研院所', '高等院校', '医疗机构', '检测机构', '政府机构', '企业'],
                    datasets: [{
                        data: [45, 38, 23, 18, 12, 20],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 地区分布统计图表
            const regionDistributionCtx = document.getElementById('regionDistributionChart').getContext('2d');
            new Chart(regionDistributionCtx, {
                type: 'bar',
                data: {
                    labels: ['高新区', '市中心区', '经开区', '大学城', '工业园区', '其他'],
                    datasets: [{
                        label: '单位数量',
                        data: [45, 38, 28, 23, 15, 7],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>