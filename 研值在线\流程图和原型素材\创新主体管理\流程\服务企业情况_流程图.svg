<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="750" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">服务企业情况管理业务流程图</text>

  <!-- 阶段一：系统初始化与权限验证 -->
  <text x="750" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限验证</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">服务企业情况管理</text>
  </g>

  <!-- 节点2: 权限验证与数据加载 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与数据加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">加载服务企业主数据</text>
  </g>

  <!-- 节点3: 指标计算与概览更新 -->
  <g transform="translate(900, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">指标计算与概览更新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">聚合指标与概览区展示</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 Q 475 165 550 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 770 165 Q 835 165 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：条件筛选与数据查询 -->
  <text x="750" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：条件筛选与数据查询</text>

  <!-- 节点4: 用户设定筛选条件 -->
  <g transform="translate(150, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户设定筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">合作类型、年份、行业</text>
  </g>

  <!-- 节点5: 服务数据查询 -->
  <g transform="translate(450, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">服务数据查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">发送过滤参数</text>
  </g>

  <!-- 节点6: 结果返回与指标刷新 -->
  <g transform="translate(750, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果返回与指标刷新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">列表结果与汇总指标</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 350 355 Q 400 355 450 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 650 355 Q 700 355 750 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与数据组装 -->
  <text x="750" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与数据组装</text>

  <!-- 节点7: 详情查看操作 -->
  <g transform="translate(100, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击列表或指标卡片</text>
  </g>

  <!-- 节点8: 数据拉取与组装 -->
  <g transform="translate(400, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据拉取与组装</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">合同、经费和成果数据</text>
  </g>

  <!-- 节点9: 详情侧栏渲染 -->
  <g transform="translate(700, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情侧栏渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">统一对象展示</text>
  </g>

  <!-- 节点10: 子系统数据交互 -->
  <g transform="translate(1000, 510)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">子系统数据交互</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">合同管理与财务系统</text>
  </g>

  <!-- 连接线 7 -> 8 -->
  <path d="M 300 545 Q 350 545 400 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 600 545 Q 650 545 700 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 900 545 Q 950 545 1000 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：图表分析与数据导出 -->
  <text x="750" y="660" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：图表分析与数据导出</text>

  <!-- 节点11: 图表分析交互 -->
  <g transform="translate(200, 700)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">图表分析交互</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合与可视化</text>
  </g>

  <!-- 节点12: 数据导出服务 -->
  <g transform="translate(550, 700)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">PDF或Excel文件生成</text>
  </g>

  <!-- 节点13: 审计日志记录 -->
  <g transform="translate(900, 700)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志记录</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">操作行为与合规追踪</text>
  </g>

  <!-- 连接线 11 -> 12 -->
  <path d="M 420 735 Q 485 735 550 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 12 -> 13 -->
  <path d="M 770 735 Q 835 735 900 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从审计日志回到系统初始化 -->
  <path d="M 1010 700 C 1010 650, 1200 600, 1200 400 C 1200 250, 1100 180, 1120 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="450" text-anchor="middle" font-size="11" fill="#666">系统优化反馈</text>

  <!-- 从概览更新到用户筛选的用户交互流 -->
  <path d="M 1010 200 C 1010 250, 250 280, 250 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="630" y="260" text-anchor="middle" font-size="11" fill="#666">用户交互</text>

  <!-- 从结果刷新到详情查看的深度钻取流 -->
  <path d="M 860 390 C 860 450, 200 480, 200 510" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="530" y="450" text-anchor="middle" font-size="11" fill="#666">深度钻取</text>

  <!-- 从详情侧栏到图表分析的分析流 -->
  <path d="M 800 580 C 800 640, 310 670, 310 700" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="555" y="640" text-anchor="middle" font-size="11" fill="#666">分析交互</text>

</svg>