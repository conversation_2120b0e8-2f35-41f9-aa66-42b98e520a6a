<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商标管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">商标管理</h1>
            <p class="text-gray-600">提供商标资产全生命周期管理，包括申请、核准、维持到失效的全流程信息维护与查询服务</p>
        </div>

        <div class="space-y-6 h-[calc(100vh-180px)] overflow-y-auto">
            <!-- 条件筛选区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    条件筛选
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">商标状态</label>
                        <div class="flex flex-wrap gap-2">
                            <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                                有效
                            </label>
                            <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                异议中
                            </label>
                            <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                已失效
                            </label>
                            <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                                即将到期
                            </label>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">尼斯分类</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部分类</option>
                            <option value="1">第1类 - 化学品</option>
                            <option value="9">第9类 - 科学仪器</option>
                            <option value="35">第35类 - 广告销售</option>
                            <option value="42">第42类 - 科技服务</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">注册日期</label>
                        <div class="flex space-x-2">
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="flex items-center text-gray-500">至</span>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">申请日期</label>
                        <div class="flex space-x-2">
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <span class="flex items-center text-gray-500">至</span>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                        <input type="text" placeholder="商标名称/注册号/申请人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div class="md:col-span-2 lg:col-span-1">
                        <button class="text-sm text-blue-600 hover:text-blue-800" onclick="toggleAdvancedFilter()">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                            高级筛选
                        </button>
                        <div id="advancedFilter" class="hidden mt-2 space-y-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">类似群号</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">申请人类型</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">全部</option>
                                    <option value="enterprise">企业</option>
                                    <option value="individual">个人</option>
                                    <option value="organization">组织</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-between mt-4">
                    <div>
                        <button class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                            </svg>
                            保存当前筛选方案
                        </button>
                    </div>
                    <div class="flex space-x-3">
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                    </div>
                </div>
            </div>

            <!-- 指标概览区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    商标指标概览
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">商标总量</div>
                            <div class="flex items-center">
                                <span class="text-xs text-green-500 mr-1">↑12%</span>
                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-2xl font-bold text-blue-600 mt-2">1,245</div>
                        <div class="h-10 mt-2">
                            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                                <path d="M0,20 L20,15 L40,18 L60,10 L80,12 L100,5" stroke="#3b82f6" stroke-width="2" fill="none" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg cursor-pointer hover:bg-green-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">当年新增</div>
                            <div class="flex items-center">
                                <span class="text-xs text-green-500 mr-1">↑24%</span>
                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-2xl font-bold text-green-600 mt-2">156</div>
                        <div class="h-10 mt-2">
                            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                                <path d="M0,15 L20,12 L40,8 L60,5 L80,10 L100,5" stroke="#10b981" stroke-width="2" fill="none" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg cursor-pointer hover:bg-yellow-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">有效商标</div>
                            <div class="flex items-center">
                                <span class="text-xs text-green-500 mr-1">↑8%</span>
                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-2xl font-bold text-yellow-600 mt-2">1,028</div>
                        <div class="h-10 mt-2">
                            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                                <path d="M0,18 L20,15 L40,10 L60,8 L80,12 L100,10" stroke="#eab308" stroke-width="2" fill="none" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-red-50 p-4 rounded-lg cursor-pointer hover:bg-red-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">即将到期</div>
                            <div class="flex items-center">
                                <span class="text-xs text-red-500 mr-1">↓5%</span>
                                <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-2xl font-bold text-red-600 mt-2">42</div>
                        <div class="h-10 mt-2">
                            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                                <path d="M0,5 L20,8 L40,12 L60,15 L80,10 L100,5" stroke="#ef4444" stroke-width="2" fill="none" />
                            </svg>
                        </div>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg cursor-pointer hover:bg-purple-100 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">异议中</div>
                            <div class="flex items-center">
                                <span class="text-xs text-red-500 mr-1">↓3%</span>
                                <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="text-2xl font-bold text-purple-600 mt-2">15</div>
                        <div class="h-10 mt-2">
                            <svg class="w-full h-full" viewBox="0 0 100 20" preserveAspectRatio="none">
                                <path d="M0,10 L20,12 L40,8 L60,5 L80,8 L100,10" stroke="#8b5cf6" stroke-width="2" fill="none" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 商标列表区 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            商标列表
                        </h2>
                        <div class="flex space-x-2">
                            <div class="relative" id="exportDropdown">
                                <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                    </svg>
                                    导出
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                    <div class="py-1">
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                    </div>
                                </div>
                            </div>
                            <button onclick="openImportModal()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量导入
                            </button>
                            <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新增商标
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">商标名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">尼斯分类</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请人</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">宁波智造</div>
                                            <div class="text-sm text-gray-500">文字商标</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12345678</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第9类</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市智造科技有限公司</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-05-15</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2030-05-14</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewDetail('tm1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editItem('tm1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="deleteItem('tm1')" class="text-red-600 hover:text-red-900">删除</button>
                                        <button onclick="linkEntity('tm1')" class="text-purple-600 hover:text-purple-900">关联主体</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">甬创</div>
                                            <div class="text-sm text-gray-500">图形商标</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23456789</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第35类</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">即将到期</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波甬创集团</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2015-08-20</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-08-19</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewDetail('tm2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editItem('tm2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="deleteItem('tm2')" class="text-red-600 hover:text-red-900">删除</button>
                                        <button onclick="linkEntity('tm2')" class="text-purple-600 hover:text-purple-900">关联主体</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">宁波港</div>
                                            <div class="text-sm text-gray-500">文字商标</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">34567890</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第39类</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">异议中</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波港集团有限公司</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-10</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2033-03-09</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewDetail('tm3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editItem('tm3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="deleteItem('tm3')" class="text-red-600 hover:text-red-900">删除</button>
                                        <button onclick="linkEntity('tm3')" class="text-purple-600 hover:text-purple-900">关联主体</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10">
                                            <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">甬江</div>
                                            <div class="text-sm text-gray-500">图形商标</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">45678901</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">第42类</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已失效</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市甬江科技研究院</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018-11-05</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2028-11-04</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewDetail('tm4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editItem('tm4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="deleteItem('tm4')" class="text-red-600 hover:text-red-900">删除</button>
                                        <button onclick="linkEntity('tm4')" class="text-purple-600 hover:text-purple-900">关联主体</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">1,245</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表分析区 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <button onclick="toggleChartPanel()" class="w-full flex items-center justify-between text-left">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            图表分析
                        </h2>
                        <svg id="chartToggleIcon" class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div id="chartPanel" class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 mb-4">商标类型分布</h3>
                        <div class="h-64 flex items-center justify-center">
                            <svg class="w-full h-full" viewBox="0 0 100 100">
                                <circle cx="50" cy="50" r="40" fill="#3b82f6" stroke="white" stroke-width="2" stroke-dasharray="25 75" stroke-dashoffset="0" />
                                <circle cx="50" cy="50" r="40" fill="#10b981" stroke="white" stroke-width="2" stroke-dasharray="75 25" stroke-dashoffset="-25" />
                                <text x="50" y="30" text-anchor="middle" font-size="10" fill="#1f2937">文字商标</text>
                                <text x="50" y="40" text-anchor="middle" font-size="8" fill="#6b7280">75%</text>
                                <text x="50" y="70" text-anchor="middle" font-size="10" fill="#1f2937">图形商标</text>
                                <text x="50" y="80" text-anchor="middle" font-size="8" fill="#6b7280">25%</text>
                            </svg>
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 mb-4">商标注册进度</h3>
                        <div class="h-64">
                            <svg class="w-full h-full" viewBox="0 0 100 60" preserveAspectRatio="none">
                                <rect x="10" y="40" width="10" height="20" fill="#3b82f6" rx="1" ry="1" />
                                <rect x="25" y="30" width="10" height="30" fill="#3b82f6" rx="1" ry="1" />
                                <rect x="40" y="20" width="10" height="40" fill="#3b82f6" rx="1" ry="1" />
                                <rect x="55" y="10" width="10" height="50" fill="#3b82f6" rx="1" ry="1" />
                                <rect x="70" y="25" width="10" height="35" fill="#3b82f6" rx="1" ry="1" />
                                <rect x="85" y="15" width="10" height="45" fill="#3b82f6" rx="1" ry="1" />
                                <text x="15" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2020</text>
                                <text x="30" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2021</text>
                                <text x="45" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2022</text>
                                <text x="60" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2023</text>
                                <text x="75" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2024</text>
                                <text x="90" y="58" text-anchor="middle" font-size="6" fill="#6b7280">2025</text>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 商标详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full max-w-md bg-white shadow-xl transform transition-transform duration-300 translate-x-full z-50">
        <div class="flex flex-col h-full">
            <div class="p-6 border-b border-gray-200 flex-shrink-0">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">商标详情</h3>
                    <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            <div class="flex-1 overflow-y-auto p-6 space-y-6">
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">基本信息</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-xs text-gray-500">商标名称</p>
                            <p class="text-sm text-gray-900">宁波智造</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">商标类型</p>
                            <p class="text-sm text-gray-900">文字商标</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">注册号</p>
                            <p class="text-sm text-gray-900">12345678</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">申请号</p>
                            <p class="text-sm text-gray-900">2020-123456</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">尼斯分类</p>
                            <p class="text-sm text-gray-900">第9类 - 科学仪器</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">类似群号</p>
                            <p class="text-sm text-gray-900">0901, 0907, 0913</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">申请人</p>
                            <p class="text-sm text-gray-900">宁波市智造科技有限公司</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">申请人类型</p>
                            <p class="text-sm text-gray-900">企业</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">注册日期</p>
                            <p class="text-sm text-gray-900">2020-05-15</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">到期日期</p>
                            <p class="text-sm text-gray-900">2030-05-14</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500">商标状态</p>
                            <p class="text-sm text-gray-900">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">商标图样</h4>
                    <div class="border border-gray-200 rounded-md p-4 flex items-center justify-center">
                        <svg class="w-32 h-32 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">法律状态历史</h4>
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-4 py-2">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-900">商标注册核准</p>
                                <p class="text-xs text-gray-500">2020-05-15</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">商标注册申请已核准，注册号：12345678</p>
                        </div>
                        <div class="border-l-4 border-green-500 pl-4 py-2">
                            <div class="flex justify-between">
                                <p class="text-sm font-medium text-gray-900">商标申请受理</p>
                                <p class="text-xs text-gray-500">2020-01-10</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">商标注册申请已受理，申请号：2020-123456</p>
                        </div>
                    </div>
                </div>

                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-2">关联创新主体</h4>
                    <div class="space-y-2">
                        <div class="p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer">
                            <p class="text-sm font-medium text-gray-900">宁波市智造科技有限公司</p>
                            <p class="text-xs text-gray-500">统一社会信用代码：91330201MA2XXXXXX</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-md hover:bg-gray-100 cursor-pointer">
                            <p class="text-sm font-medium text-gray-900">宁波市智能制造研究院</p>
                            <p class="text-xs text-gray-500">统一社会信用代码：91330201MA2YYYYYY</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 border-t border-gray-200 flex-shrink-0">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    下载商标证书
                </button>
            </div>
        </div>
    </div>

    <!-- 新增商标模态框 -->
    <div id="addModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">新增商标</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">商标名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入商标名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">商标类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择商标类型</option>
                                    <option value="text">文字商标</option>
                                    <option value="image">图形商标</option>
                                    <option value="composite">组合商标</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">尼斯分类 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择尼斯分类</option>
                                    <option value="1">第1类 - 化学品</option>
                                    <option value="9">第9类 - 科学仪器</option>
                                    <option value="35">第35类 - 广告销售</option>
                                    <option value="42">第42类 - 科技服务</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">类似群号</label>
                                <input type="text" placeholder="请输入类似群号，多个用逗号分隔" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请人 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入申请人名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请人类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择申请人类型</option>
                                    <option value="enterprise">企业</option>
                                    <option value="individual">个人</option>
                                    <option value="organization">组织</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请日期 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">注册日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">商标图样 <span class="text-red-500">*</span></label>
                            <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-4">
                                    <label for="trademark-image" class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">点击上传商标图样</span>
                                        <span class="mt-1 block text-xs text-gray-500">支持 JPG, PNG 格式，最大10MB</span>
                                    </label>
                                    <input id="trademark-image" name="trademark-image" type="file" class="sr-only" accept="image/jpeg,image/png">
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">备注说明</label>
                            <textarea rows="3" placeholder="请输入备注说明" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeAddModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入商标</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                下载导入模板
                            </a>
                            <p class="text-xs text-gray-500 mt-1">请使用标准模板填写商标信息</p>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 11115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-4">
                                <label for="import-file" class="cursor-pointer">
                                    <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                    <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式，最大10MB</span>
                                </label>
                                <input id="import-file" name="import-file" type="file" class="sr-only" accept=".xlsx,.xls">
                            </div>
                        </div>
                        <div id="importProgress" class="hidden">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span class="text-sm font-medium text-gray-700">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        <div id="importResult" class="hidden">
                            <div class="bg-green-50 border-l-4 border-green-500 p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-green-800">导入成功</p>
                                        <div class="mt-2 text-sm text-green-700">
                                            <p>成功导入 <span class="font-bold">25</span> 条商标数据</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeImportModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button id="importButton" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 右下角工具条 -->
    <div class="fixed bottom-6 right-6 space-y-2 z-40">
        <div class="flex items-center justify-end">
            <div class="bg-white shadow-md rounded-full p-2 cursor-pointer hover:bg-gray-50 transition-colors" title="刷新缓存">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
            </div>
        </div>
        <div class="flex items-center justify-end">
            <div class="bg-white shadow-md rounded-full p-2 cursor-pointer hover:bg-gray-50 transition-colors" title="任务进度">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
        </div>
    </div>

    <script>
        // 高级筛选切换
        function toggleAdvancedFilter() {
            const filter = document.getElementById('advancedFilter');
            filter.classList.toggle('hidden');
        }
        
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 图表面板切换
        function toggleChartPanel() {
            const panel = document.getElementById('chartPanel');
            const icon = document.getElementById('chartToggleIcon');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                icon.classList.remove('rotate-180');
            } else {
                panel.classList.add('hidden');
                icon.classList.add('rotate-180');
            }
        }
        
        // 商标详情抽屉
        function viewDetail(trademarkId) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
        }
        
        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }
        
        // 新增商标模态框
        function openAddModal() {
            document.getElementById('addModal').classList.remove('hidden');
        }
        
        function closeAddModal() {
            document.getElementById('addModal').classList.add('hidden');
        }
        
        // 批量导入模态框
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
        }
        
        function closeImportModal() {
            document.getElementById('importModal').classList.add('hidden');
        }
        
        // 文件上传处理
        document.getElementById('import-file').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('importProgress').classList.remove('hidden');
                document.getElementById('importButton').disabled = false;
                
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#importProgress span:last-child').textContent = progress + '%';
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        document.getElementById('importResult').classList.remove('hidden');
                        document.getElementById('importButton').disabled = true;
                    }
                }, 200);
            }
        });
        
        // 其他功能函数
        function editItem(itemId) {
            openAddModal();
            console.log('编辑项目:', itemId);
        }
        
        function deleteItem(itemId) {
            if (confirm('确定要删除这个商标吗？此操作不可恢复！')) {
                console.log('删除项目:', itemId);
            }
        }
        
        function linkEntity(itemId) {
            console.log('关联主体:', itemId);
        }
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>