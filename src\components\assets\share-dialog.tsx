'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Copy, Link, Mail, QrCode } from "lucide-react"
import { QRCodeSVG as QRCode } from "qrcode.react"

interface ShareDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ShareDialog({ open, onOpenChange }: ShareDialogProps) {
  const shareUrl = "https://example.com/visualization/123"

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>分享可视化</DialogTitle>
        </DialogHeader>
        <Tabs defaultValue="link">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="link">
              <Link className="h-4 w-4 mr-2" />
              链接
            </TabsTrigger>
            <TabsTrigger value="qrcode">
              <QrCode className="h-4 w-4 mr-2" />
              二维码
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="h-4 w-4 mr-2" />
              邮件
            </TabsTrigger>
          </TabsList>
          <TabsContent value="link" className="space-y-4">
            <div className="space-y-2">
              <Label>分享链接</Label>
              <div className="flex gap-2">
                <Input value={shareUrl} readOnly />
                <Button variant="outline" size="icon">
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="qrcode" className="flex justify-center p-4">
            <QRCode value={shareUrl} size={200} />
          </TabsContent>
          <TabsContent value="email" className="space-y-4">
            <div className="space-y-2">
              <Label>收件人邮箱</Label>
              <Input type="email" placeholder="请输入邮箱地址" />
            </div>
            <div className="space-y-2">
              <Label>邮件内容</Label>
              <textarea 
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" 
                rows={3} 
                placeholder="请输入邮件内容" 
              />
            </div>
            <Button className="w-full">发送</Button>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 