unit CwfyRolePz;

interface

uses
  Classes;

type
  TCwfyRolePz = class
  private
    FCwfyrolepzid: integer;
    FPzxh: integer;
    FPzrole: string;
    FPzddsbegin: integer;
    FPzddsend: integer;
    FCwfy: double;
  public
    property Cwfyrolepzid: integer read FCwfyrolepzid write FCwfyrolepzid;
    property pzxh: integer read FPzxh write FPzxh;
    property Pzrole: string read FPzrole write FPzrole;
    property Pzddsbegin: integer read FPzddsbegin write FPzddsbegin;
    property Pzddsend: integer read FPzddsend write FPzddsend;
    property Cwfy: double read FCwfy write FCwfy;
  end;

implementation

end.
