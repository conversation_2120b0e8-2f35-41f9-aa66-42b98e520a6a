unit CcSjYxhdj;

interface

uses
  Classes;

type
  TCcSjYxhdj = class
  private

    FSjyxhdjid: Integer;
    FDdid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FXhscs: Double;
    FXhchs: Double;
    FXhjgc: string;
    FXhKprq: string;

    FYhscs: Double;
    FYhchs: Double;
    FYhjgc: string;
    FYhKprq: string;

    FYhsl_a: Double;
    FYhdj_a: Double;
    FYhsh_a: Double;
    FYhje_a: Double;

    FYhsl_b: Double;
    FYhdj_b: Double;
    FYhsh_b: Double;
    FYhje_b: Double;

    FYhsl_c: Double;
    FYhdj_c: Double;
    FYhsh_c: Double;
    FYhje_c: Double;

    FYhsl_d: Double;
    FYhdj_d: Double;
    FYhsh_d: Double;
    FYhje_d: Double;

    FYhzje: Double;

    FXhsl_a: Double;
    FXhdj_a: Double;
    FXhsh_a: Double;
    FXhje_a: Double;

    FXhsl_b: Double;
    FXhdj_b: Double;
    FXhsh_b: Double;
    FXhje_b: Double;

    FXhsl_c: Double;
    FXhdj_c: Double;
    FXhsh_c: Double;
    FXhje_c: Double;

    FXhsl_d: Double;
    FXhdj_d: Double;
    FXhsh_d: Double;
    FXhje_d: Double;

    FXhzje: Double;

    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Sjyxhdjid: Integer read FSjyxhdjid write FSjyxhdjid;
    property Ddid: Integer read FDdid write FDdid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Xhscs: Double read FXhscs write FXhscs;
    property Xhchs: Double read FXhchs write FXhchs;
    property Xhjgc: string read FXhjgc write FXhjgc;
    property XhKprq: string read FXhKprq write FXhKprq;

    property Yhscs: Double read FYhscs write FYhscs;
    property Yhchs: Double read FYhchs write FYhchs;
    property Yhjgc: string read FYhjgc write FYhjgc;
    property YhKprq: string read FYhKprq write FYhKprq;

    property Yhsl_a: Double read FYhsl_a write FYhsl_a;
    property Yhdj_a: Double read FYhdj_a write FYhdj_a;
    property Yhsh_a: Double read FYhsh_a write FYhsh_a;
    property Yhje_a: Double read FYhje_a write FYhje_a;

    property Yhsl_b: Double read FYhsl_b write FYhsl_b;
    property Yhdj_b: Double read FYhdj_b write FYhdj_b;
    property Yhsh_b: Double read FYhsh_b write FYhsh_b;
    property Yhje_b: Double read FYhje_b write FYhje_b;

    property Yhsl_c: Double read FYhsl_c write FYhsl_c;
    property Yhdj_c: Double read FYhdj_c write FYhdj_c;
    property Yhsh_c: Double read FYhsh_c write FYhsh_c;
    property Yhje_c: Double read FYhje_c write FYhje_c;

    property Yhsl_d: Double read FYhsl_d write FYhsl_d;
    property Yhdj_d: Double read FYhdj_d write FYhdj_d;
    property Yhsh_d: Double read FYhsh_d write FYhsh_d;
    property Yhje_d: Double read FYhje_d write FYhje_d;

    property Yhzje: Double read FYhzje write FYhzje;

    property Xhsl_a: Double read FXhsl_a write FXhsl_a;
    property Xhdj_a: Double read FXhdj_a write FXhdj_a;
    property Xhsh_a: Double read FXhsh_a write FXhsh_a;
    property Xhje_a: Double read FXhje_a write FXhje_a;

    property Xhsl_b: Double read FXhsl_b write FXhsl_b;
    property Xhdj_b: Double read FXhdj_b write FXhdj_b;
    property Xhsh_b: Double read FXhsh_b write FXhsh_b;
    property Xhje_b: Double read FXhje_b write FXhje_b;

    property Xhsl_c: Double read FXhsl_c write FXhsl_c;
    property Xhdj_c: Double read FXhdj_c write FXhdj_c;
    property Xhsh_c: Double read FXhsh_c write FXhsh_c;
    property Xhje_c: Double read FXhje_c write FXhje_c;

    property Xhsl_d: Double read FXhsl_d write FXhsl_d;
    property Xhdj_d: Double read FXhdj_d write FXhdj_d;
    property Xhsh_d: Double read FXhsh_d write FXhsh_d;
    property Xhje_d: Double read FXhje_d write FXhje_d;

    property Xhzje: Double read FXhzje write FXhzje;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
