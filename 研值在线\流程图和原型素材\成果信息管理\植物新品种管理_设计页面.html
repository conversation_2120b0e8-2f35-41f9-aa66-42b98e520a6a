<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>植物新品种管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">植物新品种管理</h1>
            <p class="text-gray-600">管理植物新品种申请信息，跟踪品种权状态，关联创新主体，为农业科技成果保护提供数据支撑</p>
        </div>

        <!-- 筛选检索区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">植物种类</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部种类</option>
                        <option value="crop">粮食作物</option>
                        <option value="vegetable">蔬菜</option>
                        <option value="fruit">果树</option>
                        <option value="flower">花卉</option>
                        <option value="forest">林木</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申请日期</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">品种权状态</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            已授权
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            申请中
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            已驳回
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="新品种名称/申请号/品种权人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-between mt-4">
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    保存当前筛选方案
                </button>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">新品种总量</p>
                        <p class="text-2xl font-bold text-blue-600 mt-1">1,245</p>
                    </div>
                    <div class="bg-blue-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-2 flex items-center text-sm text-green-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>同比 +12.5%</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">年度新增</p>
                        <p class="text-2xl font-bold text-green-600 mt-1">328</p>
                    </div>
                    <div class="bg-green-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-2 flex items-center text-sm text-green-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>环比 +8.3%</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">授权率</p>
                        <p class="text-2xl font-bold text-purple-600 mt-1">78.5%</p>
                    </div>
                    <div class="bg-purple-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-2 flex items-center text-sm text-green-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>同比 +5.2%</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">即将到期</p>
                        <p class="text-2xl font-bold text-yellow-600 mt-1">42</p>
                    </div>
                    <div class="bg-yellow-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-2 flex items-center text-sm text-red-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                    <span>同比 +15.8%</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">主体覆盖率</p>
                        <p class="text-2xl font-bold text-indigo-600 mt-1">92.3%</p>
                    </div>
                    <div class="bg-indigo-100 p-2 rounded-full">
                        <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="mt-2 flex items-center text-sm text-green-500">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                    </svg>
                    <span>同比 +3.6%</span>
                </div>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧表格区 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 操作工具栏 -->
                <div class="bg-white rounded-lg shadow-md p-4">
                    <div class="flex flex-wrap items-center justify-between gap-3">
                        <div class="flex space-x-3">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                新增新品种
                            </button>
                            <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                批量导入
                            </button>
                        </div>
                        <div class="flex space-x-3">
                            <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                导出Excel
                            </button>
                            <button class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                刷新
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 新品种列表表格 -->
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">新品种名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">植物种类</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">甬优12号</div>
                                        <div class="text-sm text-gray-500">宁波市农科院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">粮食作物</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CNA20120001</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2012-05-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁海白枇杷</div>
                                        <div class="text-sm text-gray-500">宁海县农林局</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">果树</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CNA20150023</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2015-03-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">慈溪杨梅</div>
                                        <div class="text-sm text-gray-500">慈溪市农技中心</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">果树</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CNA20180045</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018-04-18</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">申请中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">象山红美人</div>
                                        <div class="text-sm text-gray-500">象山县农林局</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">果树</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CNA20190012</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019-02-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">鄞州雪菜</div>
                                        <div class="text-sm text-gray-500">鄞州区农技站</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">蔬菜</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">CNA20200078</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-06-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已驳回</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                        <button class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button class="text-red-600 hover:text-red-900">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">124</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧图表区 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 植物种类分布饼图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                        </svg>
                        植物种类分布
                    </h2>
                    <div id="plantTypeChart" class="w-full h-[300px]"></div>
                </div>

                <!-- 申请授权趋势柱形图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        申请授权趋势
                    </h2>
                    <div id="trendChart" class="w-full h-[300px]"></div>
                </div>

                <!-- 到期提醒折线图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        到期提醒
                    </h2>
                    <div id="expiryChart" class="w-full h-[300px]"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新品种详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full max-w-2xl bg-white shadow-xl transform transition-transform duration-300 ease-in-out translate-x-full z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">新品种详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <!-- 基础信息 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">基础信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">新品种名称</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">甬优12号</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">植物种类</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">粮食作物</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请号</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">CNA20120001</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">申请日</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">2012-05-15</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">授权日</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">2014-08-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">到期日</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">2024-08-20</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">品种权人</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">宁波市农科院</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">培育人</p>
                                <p class="text-sm font-medium text-gray-900 mt-1">张三, 李四</p>
                            </div>
                        </div>
                    </div>

                    <!-- 法律状态 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">法律状态</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">申请受理</p>
                                    <p class="text-xs text-gray-500">2012-05-15</p>
                                </div>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">初步审查</p>
                                    <p class="text-xs text-gray-500">2012-08-20</p>
                                </div>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">实质审查</p>
                                    <p class="text-xs text-gray-500">2013-05-10</p>
                                </div>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">授权公告</p>
                                    <p class="text-xs text-gray-500">2014-08-20</p>
                                </div>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">当前状态</span>
                            </div>
                        </div>
                    </div>

                    <!-- 证书扫描件 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">证书扫描件</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="border border-gray-200 rounded-lg p-3 flex flex-col items-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-sm text-gray-500 mt-2">授权证书</p>
                            </div>
                            <div class="border border-gray-200 rounded-lg p-3 flex flex-col items-center">
                                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                <p class="text-sm text-gray-500 mt-2">审查报告</p>
                            </div>
                        </div>
                    </div>

                    <!-- 关联创新主体 -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">关联创新主体</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">宁波市农业科学研究院</p>
                                    <p class="text-xs text-gray-500">科研院所 | 宁波市</p>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">查看详情</button>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">宁波甬优种业科技有限公司</p>
                                    <p class="text-xs text-gray-500">企业 | 宁波市</p>
                                </div>
                                <button class="text-blue-600 hover:text-blue-900 text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 border-t">
                <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    关联新主体
                </button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑新品种模态框 -->
    <div id="newVarietyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">新增植物新品种</h3>
                    <button onclick="closeNewVarietyModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">新品种名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入新品种名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">植物种类 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择植物种类</option>
                                    <option value="crop">粮食作物</option>
                                    <option value="vegetable">蔬菜</option>
                                    <option value="fruit">果树</option>
                                    <option value="flower">花卉</option>
                                    <option value="forest">林木</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请号 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入申请号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申请日 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">品种权人 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入品种权人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">培育人 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入培育人，多个用逗号分隔" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 品种描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">品种描述</label>
                            <textarea rows="3" placeholder="请输入品种特征特性描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <!-- 证书上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">证书上传</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="certificate-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="certificate-upload" name="certificate-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">支持PDF、JPG、PNG格式，最大10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeNewVarietyModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化植物种类分布饼图
        const plantTypeChart = echarts.init(document.getElementById('plantTypeChart'));
        plantTypeChart.setOption({
            title: {
                text: '植物种类分布',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'horizontal',
                bottom: 0,
                data: ['粮食作物', '蔬菜', '果树', '花卉', '林木']
            },
            series: [
                {
                    name: '植物种类',
                    type: 'pie',
                    radius: ['50%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 335, name: '粮食作物', itemStyle: { color: '#5470C6' } },
                        { value: 310, name: '蔬菜', itemStyle: { color: '#91CC75' } },
                        { value: 234, name: '果树', itemStyle: { color: '#FAC858' } },
                        { value: 135, name: '花卉', itemStyle: { color: '#EE6666' } },
                        { value: 154, name: '林木', itemStyle: { color: '#73C0DE' } }
                    ]
                }
            ]
        });

        // 初始化申请授权趋势柱形图
        const trendChart = echarts.init(document.getElementById('trendChart'));
        trendChart.setOption({
            title: {
                text: '近5年申请授权趋势',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['申请量', '授权量'],
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['2019', '2020', '2021', '2022', '2023']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '申请量',
                    type: 'bar',
                    data: [120, 132, 101, 134, 190],
                    itemStyle: {
                        color: '#5470C6'
                    }
                },
                {
                    name: '授权量',
                    type: 'bar',
                    data: [80, 95, 70, 110, 145],
                    itemStyle: {
                        color: '#91CC75'
                    }
                }
            ]
        });

        // 初始化到期提醒折线图
        const expiryChart = echarts.init(document.getElementById('expiryChart'));
        expiryChart.setOption({
            title: {
                text: '未来12个月到期提醒',
                left: 'center',
                textStyle: {
                    fontSize: 14,
                    fontWeight: 'normal'
                }
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['到期数量'],
                bottom: 0
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '到期数量',
                    type: 'line',
                    data: [5, 3, 4, 7, 5, 8, 3, 6, 9, 12, 15, 10],
                    itemStyle: {
                        color: '#EE6666'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0,
                                color: 'rgba(238, 102, 102, 0.5)'
                            }, {
                                offset: 1,
                                color: 'rgba(238, 102, 102, 0.1)'
                            }]
                        }
                    }
                }
            ]
        });

        // 窗口大小变化时重新调整图表大小
        window.addEventListener('resize', function() {
            plantTypeChart.resize();
            trendChart.resize();
            expiryChart.resize();
        });

        // 详情抽屉控制
        function openDetailDrawer() {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
        }

        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }

        // 新增/编辑模态框控制
        function openNewVarietyModal() {
            document.getElementById('newVarietyModal').classList.remove('hidden');
        }

        function closeNewVarietyModal() {
            document.getElementById('newVarietyModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('newVarietyModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeNewVarietyModal();
            }
        });

        // 表格行详情点击事件
        document.querySelectorAll('table button:first-child').forEach(button => {
            button.addEventListener('click', openDetailDrawer);
        });

        // 新增按钮点击事件
        document.querySelector('button:contains("新增新品种")').addEventListener('click', openNewVarietyModal);
    </script>
</body>
</html>