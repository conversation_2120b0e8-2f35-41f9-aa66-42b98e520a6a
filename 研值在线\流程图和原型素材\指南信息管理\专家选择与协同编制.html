<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家选择与协同编制</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        indigo: {
                            50: '#eef2ff',
                            100: '#e0e7ff',
                            600: '#4f46e5',
                            700: '#4338ca',
                            800: '#3730a3'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex">
        <!-- 主内容区域 -->
        <div class="flex-1 p-6 pr-80">
            <div class="h-[calc(100vh-120px)] flex flex-col">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-4">
                <h1 class="text-2xl font-semibold text-indigo-800 flex items-center">
                    <svg class="mr-2 h-6 w-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    专家选择与协同编制
                </h1>
                
                <div class="flex gap-2">
                    <button class="flex items-center gap-1 px-3 py-2 text-sm border border-indigo-200 text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        协同统计
                    </button>
                    
                    <button class="flex items-center gap-1 px-3 py-2 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        发起协同
                    </button>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="flex-1 bg-white rounded-lg shadow-lg border border-indigo-100">
                <!-- 标签页导航 -->
                <div class="border-b border-indigo-100 bg-indigo-50/50 p-4">
                    <div class="flex space-x-1 bg-indigo-100 p-1 rounded-lg w-fit">
                        <button id="tab-selection" class="px-4 py-2 text-sm font-medium rounded-md bg-indigo-600 text-white transition-colors">
                            专家筛选
                        </button>
                        <button id="tab-collaboration" class="px-4 py-2 text-sm font-medium rounded-md text-indigo-700 hover:bg-indigo-200 transition-colors">
                            协同空间
                        </button>
                        <button id="tab-progress" class="px-4 py-2 text-sm font-medium rounded-md text-indigo-700 hover:bg-indigo-200 transition-colors">
                            进度管控
                        </button>
                    </div>
                </div>
                
                <!-- 专家筛选标签页 -->
                <div id="content-selection" class="p-6">
                    <!-- 筛选区域 -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">专家筛选条件</h3>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option>请选择技术领域</option>
                                    <option>农业技术</option>
                                    <option>环境工程</option>
                                    <option>建筑工程</option>
                                    <option>水利工程</option>
                                    <option>信息技术</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">职称/职务</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                                    <option>不限</option>
                                    <option>正高级</option>
                                    <option>副高级</option>
                                    <option>中级</option>
                                    <option>初级</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">经验关键词</label>
                                <input type="text" placeholder="输入关键词搜索" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div class="flex items-end">
                                <div class="flex items-center space-x-2">
                                    <input type="checkbox" id="match-sort" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                                    <label for="match-sort" class="text-sm text-gray-700">按匹配度排序</label>
                                </div>
                            </div>
                        </div>
                        <div class="flex gap-2 mt-4">
                            <button class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                筛选
                            </button>
                            <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                                重置
                            </button>
                        </div>
                    </div>
                    
                    <!-- 专家列表和邀请清单 -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 候选专家列表 -->
                        <div class="lg:col-span-2">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-gray-900">候选专家列表</h3>
                                <span class="text-sm text-gray-500">共找到 12 位专家</span>
                            </div>
                            
                            <div class="space-y-4 max-h-96 overflow-y-auto">
                                <!-- 专家卡片 -->
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-2">
                                                <h4 class="font-medium text-gray-900">张教授</h4>
                                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">正高级</span>
                                                <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">匹配度: 95%</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2">中国农业大学 - 农业工程学院</p>
                                            <p class="text-xs text-gray-500">近三年参与项目: 8个 | 专业领域: 农业技术、环境工程</p>
                                        </div>
                                        <button class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                            添加至邀请清单
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-2">
                                                <h4 class="font-medium text-gray-900">李研究员</h4>
                                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">副高级</span>
                                                <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">匹配度: 88%</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2">中科院环境研究所</p>
                                            <p class="text-xs text-gray-500">近三年参与项目: 6个 | 专业领域: 环境工程、水利工程</p>
                                        </div>
                                        <button class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                            添加至邀请清单
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-2">
                                                <h4 class="font-medium text-gray-900">王工程师</h4>
                                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">中级</span>
                                                <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">匹配度: 76%</span>
                                            </div>
                                            <p class="text-sm text-gray-600 mb-2">省建筑设计院</p>
                                            <p class="text-xs text-gray-500">近三年参与项目: 12个 | 专业领域: 建筑工程、项目管理</p>
                                        </div>
                                        <button class="px-3 py-1 text-sm bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                            添加至邀请清单
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 邀请清单 -->
                        <div>
                            <div class="bg-indigo-50 rounded-lg p-4">
                                <h3 class="text-lg font-medium text-indigo-900 mb-4">邀请清单</h3>
                                
                                <div class="space-y-3 mb-4">
                                    <div class="flex justify-between items-center p-2 bg-white rounded border">
                                        <div>
                                            <p class="font-medium text-sm">张教授</p>
                                            <p class="text-xs text-gray-500">中国农业大学</p>
                                        </div>
                                        <button class="text-red-500 hover:text-red-700">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="border-t border-indigo-200 pt-4">
                                    <div class="flex justify-between text-sm mb-2">
                                        <span class="text-gray-600">已选专家:</span>
                                        <span class="font-medium">1 位</span>
                                    </div>
                                    <div class="flex justify-between text-sm mb-4">
                                        <span class="text-gray-600">预计成本:</span>
                                        <span class="font-medium text-indigo-600">¥8,000</span>
                                    </div>
                                    
                                    <button class="w-full px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors">
                                        发送邀请
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 协同空间标签页 -->
                <div id="content-collaboration" class="p-6 hidden">
                    <div class="w-full">
                        <!-- 协同内容区 -->
                        <div class="w-full">
                            <!-- 子标签页 -->
                            <div class="border-b border-gray-200 mb-6">
                                <nav class="flex space-x-8">
                                    <button id="sub-tab-draft" class="py-2 px-1 border-b-2 border-indigo-500 text-indigo-600 font-medium text-sm" onclick="switchSubTab('draft')">
                                        编制草稿
                                    </button>
                                    <button id="sub-tab-review" class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" onclick="switchSubTab('review')">
                                        评审意见
                                    </button>
                                    <button id="sub-tab-library" class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" onclick="switchSubTab('library')">
                                        资料库
                                    </button>
                                    <button id="sub-tab-version" class="py-2 px-1 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm" onclick="switchSubTab('version')">
                                        版本记录
                                    </button>
                                </nav>
                            </div>
                            
                            <!-- 编制草稿内容 -->
                            <div id="sub-content-draft" class="space-y-6">
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-900 mb-3">富文本编辑器</h4>
                                    <div class="bg-white border border-gray-300 rounded-md p-4 min-h-64">
                                        <div class="border-b border-gray-200 pb-2 mb-4">
                                            <div class="flex space-x-2">
                                                <button class="p-1 text-gray-600 hover:text-gray-900">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2l3 6 3-6M6 18l3-6 3 6M6 10h12"></path>
                                                    </svg>
                                                </button>
                                                <button class="p-1 text-gray-600 hover:text-gray-900">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                    </svg>
                                                </button>
                                                <button class="p-1 text-gray-600 hover:text-gray-900">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                        <textarea class="w-full h-48 border-none resize-none focus:outline-none" placeholder="开始编写指南内容..."></textarea>
                                    </div>
                                </div>
                                
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-900 mb-3">附件上传</h4>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="mt-4">
                                            <label class="cursor-pointer">
                                                <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                                <input type="file" class="sr-only" multiple>
                                            </label>
                                            <p class="mt-2 text-xs text-gray-500">支持 PDF, DOC, DOCX, XLS, XLSX 格式</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 评审意见内容 -->
                            <div id="sub-content-review" class="space-y-6 hidden">
                                <div class="bg-white border border-gray-200 rounded-lg">
                                    <div class="p-4 border-b border-gray-200">
                                        <h4 class="font-medium text-gray-900">评审讨论区</h4>
                                        <p class="text-sm text-gray-500 mt-1">类 Slack 线程评论系统</p>
                                    </div>
                                    
                                    <!-- 评论线程列表 -->
                                    <div class="divide-y divide-gray-200">
                                        <!-- 评论线程 1 -->
                                        <div class="p-4 hover:bg-gray-50">
                                            <div class="flex items-start space-x-3">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                                        张
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center space-x-2">
                                                        <p class="text-sm font-medium text-gray-900">张教授</p>
                                                        <span class="text-xs text-gray-500">2024-06-09 14:30</span>
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                                                            待回复
                                                        </span>
                                                    </div>
                                                    <p class="mt-1 text-sm text-gray-700">关于第三章节的技术标准部分，建议增加国际标准的对比分析，这样能够提升指南的权威性和实用性。</p>
                                                    
                                                    <!-- 回复按钮和统计 -->
                                                    <div class="mt-2 flex items-center space-x-4">
                                                        <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">
                                                            💬 回复 (2)
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            👍 赞同 (3)
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            🔗 引用
                                                        </button>
                                                    </div>
                                                    
                                                    <!-- 回复展开区域 -->
                                                    <div class="mt-3 ml-4 pl-4 border-l-2 border-gray-200 space-y-3">
                                                        <div class="flex items-start space-x-2">
                                                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
                                                                李
                                                            </div>
                                                            <div class="flex-1">
                                                                <div class="flex items-center space-x-2">
                                                                    <span class="text-xs font-medium text-gray-900">李研究员</span>
                                                                    <span class="text-xs text-gray-500">15:20</span>
                                                                </div>
                                                                <p class="text-xs text-gray-700 mt-1">同意张教授的建议，我可以提供ISO相关标准的资料。</p>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="flex items-start space-x-2">
                                                            <div class="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs">
                                                                王
                                                            </div>
                                                            <div class="flex-1">
                                                                <div class="flex items-center space-x-2">
                                                                    <span class="text-xs font-medium text-gray-900">王工程师</span>
                                                                    <span class="text-xs text-gray-500">16:45</span>
                                                                </div>
                                                                <p class="text-xs text-gray-700 mt-1">建议在3.2节增加对比表格，更直观。</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 评论线程 2 -->
                                        <div class="p-4 hover:bg-gray-50">
                                            <div class="flex items-start space-x-3">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                                        李
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center space-x-2">
                                                        <p class="text-sm font-medium text-gray-900">李研究员</p>
                                                        <span class="text-xs text-gray-500">2024-06-09 16:15</span>
                                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                            已解决
                                                        </span>
                                                    </div>
                                                    <p class="mt-1 text-sm text-gray-700">第二章的案例分析部分写得很好，建议在结尾增加一个小结，总结关键要点。</p>
                                                    
                                                    <div class="mt-2 flex items-center space-x-4">
                                                        <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium">
                                                            💬 回复 (1)
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            👍 赞同 (5)
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            ✅ 已采纳
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 新评论输入框 -->
                                    <div class="p-4 border-t border-gray-200 bg-gray-50">
                                        <div class="flex items-start space-x-3">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                                    我
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <textarea class="w-full p-3 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" rows="3" placeholder="输入您的评审意见..."></textarea>
                                                <div class="mt-2 flex justify-between items-center">
                                                    <div class="flex space-x-2">
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            📎 附件
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            😊 表情
                                                        </button>
                                                        <button class="text-xs text-gray-500 hover:text-gray-700">
                                                            @ 提及
                                                        </button>
                                                    </div>
                                                    <button class="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                                                        发送
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 资料库内容 -->
                            <div id="sub-content-library" class="space-y-6 hidden">
                                <div class="bg-white border border-gray-200 rounded-lg">
                                    <div class="p-4 border-b border-gray-200">
                                        <div class="flex justify-between items-center">
                                            <div>
                                                <h4 class="font-medium text-gray-900">项目资料库</h4>
                                                <p class="text-sm text-gray-500 mt-1">分组文件树展示上传文档</p>
                                            </div>
                                            <button class="px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                                                上传文件
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- 文件分组标签 -->
                                    <div class="p-4 border-b border-gray-200">
                                        <div class="flex space-x-2">
                                            <button class="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm rounded-full font-medium">
                                                全部 (24)
                                            </button>
                                            <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200">
                                                政策文件 (8)
                                            </button>
                                            <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200">
                                                技术标准 (6)
                                            </button>
                                            <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200">
                                                案例分析 (5)
                                            </button>
                                            <button class="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full hover:bg-gray-200">
                                                参考资料 (5)
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <!-- 文件树结构 -->
                                    <div class="p-4">
                                        <div class="space-y-2">
                                            <!-- 政策文件组 -->
                                            <div class="border border-gray-200 rounded-lg">
                                                <div class="p-3 bg-blue-50 border-b border-gray-200 cursor-pointer" onclick="toggleFileGroup('policy')">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <svg id="policy-icon" class="w-4 h-4 text-blue-600 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                            <span class="font-medium text-blue-900">📋 政策文件</span>
                                                            <span class="text-xs text-blue-700 bg-blue-200 px-2 py-0.5 rounded-full">8个文件</span>
                                                        </div>
                                                        <span class="text-xs text-blue-600">2.3 MB</span>
                                                    </div>
                                                </div>
                                                <div id="policy-files" class="divide-y divide-gray-100">
                                                    <div class="p-3 hover:bg-gray-50 flex items-center justify-between">
                                                        <div class="flex items-center space-x-3">
                                                            <div class="w-8 h-8 bg-red-100 rounded flex items-center justify-center">
                                                                <span class="text-xs font-medium text-red-600">PDF</span>
                                                            </div>
                                                            <div>
                                                                <p class="text-sm font-medium text-gray-900">农村公益事业建设管理办法.pdf</p>
                                                                <p class="text-xs text-gray-500">上传者：张教授 • 2024-06-08 • 1.2 MB</p>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <button class="text-xs text-indigo-600 hover:text-indigo-800">预览</button>
                                                            <button class="text-xs text-gray-500 hover:text-gray-700">下载</button>
                                                        </div>
                                                    </div>
                                                    <div class="p-3 hover:bg-gray-50 flex items-center justify-between">
                                                        <div class="flex items-center space-x-3">
                                                            <div class="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                                                                <span class="text-xs font-medium text-blue-600">DOC</span>
                                                            </div>
                                                            <div>
                                                                <p class="text-sm font-medium text-gray-900">财政资金使用规范.docx</p>
                                                                <p class="text-xs text-gray-500">上传者：李研究员 • 2024-06-08 • 856 KB</p>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <button class="text-xs text-indigo-600 hover:text-indigo-800">预览</button>
                                                            <button class="text-xs text-gray-500 hover:text-gray-700">下载</button>
                                                        </div>
                                                    </div>
                                                    <div class="p-3 text-center">
                                                        <button class="text-xs text-indigo-600 hover:text-indigo-800">查看全部 6 个文件</button>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 技术标准组 -->
                                            <div class="border border-gray-200 rounded-lg">
                                                <div class="p-3 bg-green-50 border-b border-gray-200 cursor-pointer" onclick="toggleFileGroup('standard')">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <svg id="standard-icon" class="w-4 h-4 text-green-600 transform transition-transform rotate-[-90deg]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                            <span class="font-medium text-green-900">⚙️ 技术标准</span>
                                                            <span class="text-xs text-green-700 bg-green-200 px-2 py-0.5 rounded-full">6个文件</span>
                                                        </div>
                                                        <span class="text-xs text-green-600">4.1 MB</span>
                                                    </div>
                                                </div>
                                                <div id="standard-files" class="divide-y divide-gray-100 hidden">
                                                    <div class="p-3 hover:bg-gray-50 flex items-center justify-between">
                                                        <div class="flex items-center space-x-3">
                                                            <div class="w-8 h-8 bg-red-100 rounded flex items-center justify-center">
                                                                <span class="text-xs font-medium text-red-600">PDF</span>
                                                            </div>
                                                            <div>
                                                                <p class="text-sm font-medium text-gray-900">ISO 9001质量管理体系标准.pdf</p>
                                                                <p class="text-xs text-gray-500">上传者：王工程师 • 2024-06-09 • 2.1 MB</p>
                                                            </div>
                                                        </div>
                                                        <div class="flex items-center space-x-2">
                                                            <button class="text-xs text-indigo-600 hover:text-indigo-800">预览</button>
                                                            <button class="text-xs text-gray-500 hover:text-gray-700">下载</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- 案例分析组 -->
                                            <div class="border border-gray-200 rounded-lg">
                                                <div class="p-3 bg-yellow-50 border-b border-gray-200 cursor-pointer" onclick="toggleFileGroup('case')">
                                                    <div class="flex items-center justify-between">
                                                        <div class="flex items-center space-x-2">
                                                            <svg id="case-icon" class="w-4 h-4 text-yellow-600 transform transition-transform rotate-[-90deg]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                            </svg>
                                                            <span class="font-medium text-yellow-900">📊 案例分析</span>
                                                            <span class="text-xs text-yellow-700 bg-yellow-200 px-2 py-0.5 rounded-full">5个文件</span>
                                                        </div>
                                                        <span class="text-xs text-yellow-600">3.2 MB</span>
                                                    </div>
                                                </div>
                                                <div id="case-files" class="divide-y divide-gray-100 hidden">
                                                    <!-- 案例文件列表 -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 版本记录内容 -->
                            <div id="sub-content-version" class="space-y-6 hidden">
                                <div class="bg-white border border-gray-200 rounded-lg">
                                    <div class="p-4 border-b border-gray-200">
                                        <h4 class="font-medium text-gray-900">版本记录</h4>
                                        <p class="text-sm text-gray-500 mt-1">按时间线呈现草稿比对与修改摘要</p>
                                    </div>
                                    
                                    <!-- 版本时间线 -->
                                    <div class="p-4">
                                        <div class="relative">
                                            <!-- 时间线线条 -->
                                            <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                                            
                                            <!-- 版本记录项 -->
                                            <div class="space-y-6">
                                                <!-- 当前版本 -->
                                                <div class="relative flex items-start space-x-4">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow"></div>
                                                    </div>
                                                    <div class="flex-1 bg-green-50 rounded-lg p-4 border border-green-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <div class="flex items-center space-x-2">
                                                                <span class="font-medium text-green-900">v1.3 (当前版本)</span>
                                                                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                                                    最新
                                                                </span>
                                                            </div>
                                                            <span class="text-sm text-green-700">2024-06-09 17:30</span>
                                                        </div>
                                                        <p class="text-sm text-green-800 mb-3">修改者：张教授</p>
                                                        <div class="text-sm text-green-700">
                                                            <p class="font-medium mb-1">修改摘要：</p>
                                                            <ul class="list-disc list-inside space-y-1 text-xs">
                                                                <li>第三章增加国际标准对比分析表格</li>
                                                                <li>第二章案例分析部分增加小结</li>
                                                                <li>修正了第一章中的几处表述错误</li>
                                                                <li>更新了参考文献格式</li>
                                                            </ul>
                                                        </div>
                                                        <div class="mt-3 flex space-x-2">
                                                            <button class="px-3 py-1 bg-green-600 text-white text-xs rounded hover:bg-green-700">
                                                                查看详情
                                                            </button>
                                                            <button class="px-3 py-1 border border-green-300 text-green-700 text-xs rounded hover:bg-green-100">
                                                                与上版本比对
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 历史版本 1 -->
                                                <div class="relative flex items-start space-x-4">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow"></div>
                                                    </div>
                                                    <div class="flex-1 bg-blue-50 rounded-lg p-4 border border-blue-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <span class="font-medium text-blue-900">v1.2</span>
                                                            <span class="text-sm text-blue-700">2024-06-09 14:15</span>
                                                        </div>
                                                        <p class="text-sm text-blue-800 mb-3">修改者：李研究员</p>
                                                        <div class="text-sm text-blue-700">
                                                            <p class="font-medium mb-1">修改摘要：</p>
                                                            <ul class="list-disc list-inside space-y-1 text-xs">
                                                                <li>完善了第四章的实施流程图</li>
                                                                <li>增加了风险评估章节</li>
                                                                <li>调整了文档结构层次</li>
                                                            </ul>
                                                        </div>
                                                        <div class="mt-3 flex space-x-2">
                                                            <button class="px-3 py-1 border border-blue-300 text-blue-700 text-xs rounded hover:bg-blue-100">
                                                                查看详情
                                                            </button>
                                                            <button class="px-3 py-1 border border-blue-300 text-blue-700 text-xs rounded hover:bg-blue-100">
                                                                与当前版本比对
                                                            </button>
                                                            <button class="px-3 py-1 border border-blue-300 text-blue-700 text-xs rounded hover:bg-blue-100">
                                                                恢复此版本
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- 历史版本 2 -->
                                                <div class="relative flex items-start space-x-4">
                                                    <div class="flex-shrink-0">
                                                        <div class="w-3 h-3 bg-gray-400 rounded-full border-2 border-white shadow"></div>
                                                    </div>
                                                    <div class="flex-1 bg-gray-50 rounded-lg p-4 border border-gray-200">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <span class="font-medium text-gray-900">v1.1</span>
                                                            <span class="text-sm text-gray-700">2024-06-08 16:45</span>
                                                        </div>
                                                        <p class="text-sm text-gray-800 mb-3">修改者：王工程师</p>
                                                        <div class="text-sm text-gray-700">
                                                            <p class="font-medium mb-1">修改摘要：</p>
                                                            <ul class="list-disc list-inside space-y-1 text-xs">
                                                                <li>初始版本创建</li>
                                                                <li>建立基本文档框架</li>
                                                                <li>添加目录结构</li>
                                                            </ul>
                                                        </div>
                                                        <div class="mt-3 flex space-x-2">
                                                            <button class="px-3 py-1 border border-gray-300 text-gray-700 text-xs rounded hover:bg-gray-100">
                                                                查看详情
                                                            </button>
                                                            <button class="px-3 py-1 border border-gray-300 text-gray-700 text-xs rounded hover:bg-gray-100">
                                                                与当前版本比对
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 版本比对弹窗触发按钮 -->
                                        <div class="mt-6 pt-4 border-t border-gray-200">
                                            <button class="w-full px-4 py-2 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700">
                                                打开版本比对工具
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 版本比对工具 -->
                                <div class="bg-white border border-gray-200 rounded-lg">
                                    <div class="p-4 border-b border-gray-200">
                                        <h4 class="font-medium text-gray-900">版本比对</h4>
                                        <div class="mt-2 flex space-x-4">
                                            <select class="text-sm border border-gray-300 rounded px-3 py-1">
                                                <option>v1.3 (当前版本)</option>
                                                <option>v1.2</option>
                                                <option>v1.1</option>
                                            </select>
                                            <span class="text-sm text-gray-500 self-center">对比</span>
                                            <select class="text-sm border border-gray-300 rounded px-3 py-1">
                                                <option>v1.2</option>
                                                <option>v1.1</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- 比对结果展示 -->
                                    <div class="p-4">
                                        <div class="grid grid-cols-2 gap-4">
                                            <div>
                                                <h5 class="text-sm font-medium text-gray-900 mb-2">v1.3 (当前版本)</h5>
                                                <div class="bg-green-50 border border-green-200 rounded p-3 text-sm">
                                                    <p class="text-gray-700">第三章 技术标准</p>
                                                    <p class="text-green-700 mt-1">+ 3.2 国际标准对比分析</p>
                                                    <p class="text-green-700">+ 表格3-1：国内外标准对比</p>
                                                    <p class="text-gray-700 mt-2">第二章 案例分析</p>
                                                    <p class="text-green-700 mt-1">+ 2.4 关键要点小结</p>
                                                </div>
                                            </div>
                                            <div>
                                                <h5 class="text-sm font-medium text-gray-900 mb-2">v1.2</h5>
                                                <div class="bg-red-50 border border-red-200 rounded p-3 text-sm">
                                                    <p class="text-gray-700">第三章 技术标准</p>
                                                    <p class="text-red-700 mt-1">- 仅包含国内标准</p>
                                                    <p class="text-gray-700 mt-2">第二章 案例分析</p>
                                                    <p class="text-red-700 mt-1">- 缺少总结部分</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        

                    </div>
                </div>
                
                <!-- 进度管控标签页 -->
                <div id="content-progress" class="p-6 hidden">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 时间线 -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">协同时间线</h3>
                            <div class="space-y-4">
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0 w-3 h-3 bg-green-500 rounded-full mt-1"></div>
                                    <div>
                                        <p class="font-medium text-sm">任务发起</p>
                                        <p class="text-xs text-gray-500">2024-06-09 09:00</p>
                                        <p class="text-sm text-gray-600 mt-1">向5位专家发送协同邀请</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0 w-3 h-3 bg-green-500 rounded-full mt-1"></div>
                                    <div>
                                        <p class="font-medium text-sm">专家确认参与</p>
                                        <p class="text-xs text-gray-500">2024-06-09 14:30</p>
                                        <p class="text-sm text-gray-600 mt-1">张教授、李研究员确认参与</p>
                                    </div>
                                </div>
                                
                                <div class="flex items-start gap-4">
                                    <div class="flex-shrink-0 w-3 h-3 bg-yellow-500 rounded-full mt-1"></div>
                                    <div>
                                        <p class="font-medium text-sm">草稿编制中</p>
                                        <p class="text-xs text-gray-500">进行中</p>
                                        <p class="text-sm text-gray-600 mt-1">专家正在协同编制指南草稿</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 统计信息 -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">协同统计</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div class="bg-blue-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-blue-600">5</div>
                                    <div class="text-sm text-blue-800">邀请专家数</div>
                                </div>
                                <div class="bg-green-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-green-600">3</div>
                                    <div class="text-sm text-green-800">确认参与数</div>
                                </div>
                                <div class="bg-yellow-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-yellow-600">12</div>
                                    <div class="text-sm text-yellow-800">讨论条数</div>
                                </div>
                                <div class="bg-purple-50 rounded-lg p-4">
                                    <div class="text-2xl font-bold text-purple-600">3</div>
                                    <div class="text-sm text-purple-800">版本迭代数</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 固定右侧节点控制面板 -->
        <div class="fixed right-0 top-0 w-72 h-full bg-white shadow-lg border-l border-indigo-100 p-6 overflow-y-auto">
            <div class="bg-indigo-50 rounded-lg p-4">
                <h3 class="text-lg font-medium text-indigo-900 mb-4">节点控制面板</h3>
                
                <div class="space-y-4">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">当前阶段</h4>
                        <div class="flex items-center gap-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-sm text-gray-700">草稿编制中</span>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">专家回复进度</h4>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-indigo-600 h-2 rounded-full" style="width: 60%"></div>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">3/5 位专家已回复</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">待完成任务</h4>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li class="flex items-center gap-2">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                初稿提交
                            </li>
                            <li class="flex items-center gap-2">
                                <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                专家评审
                            </li>
                            <li class="flex items-center gap-2">
                                <div class="w-2 h-2 bg-gray-300 rounded-full"></div>
                                修改完善
                            </li>
                        </ul>
                    </div>
                    
                    <div class="pt-4 border-t border-indigo-200">
                        <button class="w-full mb-2 px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors text-sm">
                            催办提醒
                        </button>
                        <button class="w-full px-4 py-2 border border-red-300 text-red-700 rounded-md hover:bg-red-50 transition-colors text-sm">
                            结束协同
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 标签页切换功能
        function switchTab(tabName) {
            // 隐藏所有内容
            document.querySelectorAll('[id^="content-"]').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有标签的激活状态
            document.querySelectorAll('[id^="tab-"]').forEach(tab => {
                tab.classList.remove('bg-indigo-600', 'text-white');
                tab.classList.add('text-indigo-700', 'hover:bg-indigo-200');
            });
            
            // 显示选中的内容
            document.getElementById(`content-${tabName}`).classList.remove('hidden');
            
            // 激活选中的标签
            const activeTab = document.getElementById(`tab-${tabName}`);
            activeTab.classList.add('bg-indigo-600', 'text-white');
            activeTab.classList.remove('text-indigo-700', 'hover:bg-indigo-200');
        }
        
        // 子标签页切换功能
        function switchSubTab(subTabName) {
            // 隐藏所有子内容
            document.querySelectorAll('[id^="sub-content-"]').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有子标签的激活状态
            document.querySelectorAll('[id^="sub-tab-"]').forEach(tab => {
                tab.classList.remove('border-indigo-500', 'text-indigo-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的子内容
            document.getElementById(`sub-content-${subTabName}`).classList.remove('hidden');
            
            // 激活选中的子标签
            const activeSubTab = document.getElementById(`sub-tab-${subTabName}`);
            activeSubTab.classList.add('border-indigo-500', 'text-indigo-600');
            activeSubTab.classList.remove('border-transparent', 'text-gray-500');
        }
        
        // 文件组展开/收起功能
        function toggleFileGroup(groupName) {
            const filesDiv = document.getElementById(`${groupName}-files`);
            const iconSvg = document.getElementById(`${groupName}-icon`);
            
            if (filesDiv.classList.contains('hidden')) {
                filesDiv.classList.remove('hidden');
                iconSvg.classList.remove('rotate-[-90deg]');
                iconSvg.classList.add('rotate-0');
            } else {
                filesDiv.classList.add('hidden');
                iconSvg.classList.add('rotate-[-90deg]');
                iconSvg.classList.remove('rotate-0');
            }
        }
        
        // 绑定标签页点击事件
        document.getElementById('tab-selection').addEventListener('click', () => switchTab('selection'));
        document.getElementById('tab-collaboration').addEventListener('click', () => switchTab('collaboration'));
        document.getElementById('tab-progress').addEventListener('click', () => switchTab('progress'));
    </script>
</body>
</html>