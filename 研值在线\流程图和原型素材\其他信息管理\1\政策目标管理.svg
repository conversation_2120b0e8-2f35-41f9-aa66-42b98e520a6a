<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策目标管理流程</text>

  <!-- 阶段一：目标创建与设定 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：目标创建与设定</text>
  
  <!-- 节点1: 目标创建 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">目标创建</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(管理人员在目标编辑区)</text>
  </g>

  <!-- 节点2: 字段校验 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(校验字段完整性)</text>
  </g>

  <!-- 节点3: 生成目标记录 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成目标记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(状态置为"未开始")</text>
  </g>
  
  <!-- 节点4: 写入评估计划 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入评估计划</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(同时生成评估计划)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 L 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 165 L 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 900 200 L 900 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：自动监测与评估 -->
  <text x="600" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：自动监测与评估</text>

  <!-- 节点5: 定时触发监测 -->
  <g transform="translate(200, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时触发监测</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(按设定评估周期)</text>
  </g>

  <!-- 节点6: 数据抓取 -->
  <g transform="translate(500, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据抓取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(抓取实际数据)</text>
  </g>

  <!-- 节点7: 计算达成度 -->
  <g transform="translate(800, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">计算达成度</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(计算并更新状态)</text>
  </g>
  
  <!-- 节点8: 记录监测结果 -->
  <g transform="translate(800, 540)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录监测结果</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(保存监测数据)</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 800 320 C 700 350, 400 380, 300 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 400 455 L 500 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6 -> 7 -->
  <path d="M 700 455 L 800 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 7 -> 8 -->
  <path d="M 900 490 L 900 540" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：修订与版本管理 -->
  <text x="300" y="540" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：修订与版本管理</text>

  <!-- 节点9: 偏离阈值检查 -->
  <g transform="translate(50, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">偏离阈值检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(监测结果分析)</text>
  </g>

  <!-- 节点10: 推送修订建议 -->
  <g transform="translate(300, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送修订建议</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(推送至目标负责人)</text>
  </g>

  <!-- 节点11: 负责人评估 -->
  <g transform="translate(200, 710)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">负责人评估</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(在监测与修订区)</text>
  </g>

  <!-- 节点12: 一键应用 -->
  <g transform="translate(450, 710)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">一键应用</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(版本化保存旧目标)</text>
  </g>
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 800 610 C 700 610, 250 610, 150 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 (偏离阈值) -->
  <path d="M 250 625 L 300 625" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="275" y="615" text-anchor="middle" font-size="12" fill="#555">偏离阈值</text>

  <!-- 连接线 10 -> 11 -->
  <path d="M 400 660 C 400 685, 350 685, 300 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 11 -> 12 -->
  <path d="M 400 745 L 450 745" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：报告生成与审计 -->
  <text x="900" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：报告生成与审计</text>

  <!-- 节点13: 报告生成请求 -->
  <g transform="translate(700, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">报告生成请求</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户触发)</text>
  </g>

  <!-- 节点14: 数据汇总 -->
  <g transform="translate(950, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据汇总</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(汇总全量数据)</text>
  </g>

  <!-- 节点15: 渲染报告 -->
  <g transform="translate(700, 810)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">渲染报告</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(生成选定格式文件)</text>
  </g>

  <!-- 节点16: 写入报告库 -->
  <g transform="translate(950, 810)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入报告库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(供下载或分享)</text>
  </g>

  <!-- 节点17: 审计日志 -->
  <g transform="translate(400, 830)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(记录所有变更操作)</text>
  </g>

  <!-- 节点18: 定期归档 -->
  <g transform="translate(100, 830)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定期归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(系统定期归档)</text>
  </g>

  <!-- 连接线 13 -> 14 -->
  <path d="M 900 725 L 950 725" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 14 -> 15 -->
  <path d="M 1050 760 C 1050 785, 850 785, 800 810" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 15 -> 16 -->
  <path d="M 900 845 L 950 845" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 12 -> 17 (目标变更写入审计) -->
  <path d="M 550 745 C 600 745, 600 830, 500 830" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="575" y="785" text-anchor="middle" font-size="12" fill="#555">目标变更</text>

  <!-- 连接线 16 -> 17 (报告生成写入审计) -->
  <path d="M 950 845 C 800 845, 700 845, 600 845" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="775" y="835" text-anchor="middle" font-size="12" fill="#555">报告生成</text>

  <!-- 连接线 17 -> 18 -->
  <path d="M 400 865 L 300 865" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 18 -> 上游决策分析模块 (虚线表示数据接口) -->
  <path d="M 200 830 C 200 750, 200 650, 200 580" stroke="#333" stroke-width="2" fill="none" stroke-dasharray="5,5" />
  <text x="150" y="700" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90, 150, 700)">数据接口</text>

  <!-- 定时任务标识 -->
  <text x="300" y="400" font-size="12" text-anchor="middle" font-style="italic" fill="#555">定时任务</text>

</svg>