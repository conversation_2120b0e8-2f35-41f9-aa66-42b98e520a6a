unit CcYsYxhdj;

interface
uses
  Classes;

type
  TCcYsYxhdj = class
  private

    FYsyxhdjid: Integer;
    FYsid: Integer;
    FYhsl: Double;
    FYhdj: Double;
    FYhje: Double;
    FXhsl: Double;
    FXhdj: Double;
    FXhje: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Ysyxhdjid: integer read FYsyxhdjid write FYsyxhdjid;
    property Ysid: integer read FYsid write FYsid;
    property Yhsl: double read FYhsl write FYhsl;
    property Yhdj: double read FYhdj write FYhdj;
    property Yhje: double read FYhje write FYhje;
    property Xhsl: double read FXhsl write FXhsl;
    property Xhdj: double read FXhdj write FXhdj;
    property Xhje: double read FXhje write FXhje;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

