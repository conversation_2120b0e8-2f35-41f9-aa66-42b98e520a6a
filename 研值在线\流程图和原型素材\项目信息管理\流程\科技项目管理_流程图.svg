<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技项目管理业务流程</text>

  <!-- 阶段一：用户入口与数据检索 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户入口与数据检索</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择业务入口</text>
  </g>

  <!-- 节点2: 检索与过滤区 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">检索与过滤区</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设定筛选条件</text>
  </g>

  <!-- 节点3: 数据列表展示 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据列表展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">项目数据呈现</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：项目操作管理 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：项目操作管理</text>

  <!-- 节点4: 新增项目 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">录入表单填写</text>
  </g>

  <!-- 节点5: 项目详情 -->
  <g transform="translate(320, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目详情</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">详情弹窗展示</text>
  </g>

  <!-- 节点6: 批量操作 -->
  <g transform="translate(540, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">导出/删除/关联</text>
  </g>

  <!-- 节点7: 批量导入 -->
  <g transform="translate(760, 320)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">模板下载上传</text>
  </g>

  <!-- 从数据列表到各操作的连接线 -->
  <path d="M 750 200 C 700 250, 300 280, 190 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 200 C 750 250, 500 280, 410 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 200 C 800 250, 700 280, 630 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 820 200 C 850 250, 900 280, 850 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据处理与校验 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据处理与校验</text>

  <!-- 节点8: 字段校验 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据完整性检查</text>
  </g>

  <!-- 节点9: 数据入库 -->
  <g transform="translate(500, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据入库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新数据列表</text>
  </g>

  <!-- 节点10: 异常处理 -->
  <g transform="translate(800, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">异常处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">错误数据提示</text>
  </g>

  <!-- 从操作到校验的连接线 -->
  <path d="M 190 390 C 190 450, 250 480, 300 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 390 C 850 450, 800 480, 900 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 校验到入库的连接线 -->
  <path d="M 400 555 Q 450 555 500 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="450" y="545" text-anchor="middle" font-size="12" fill="#555">校验通过</text>

  <!-- 校验到异常的连接线 -->
  <path d="M 350 590 C 450 620, 650 620, 800 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="575" y="635" text-anchor="middle" font-size="12" fill="#555">校验失败</text>

  <!-- 阶段四：历史记录与成果关联 -->
  <text x="700" y="700" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：历史记录与成果关联</text>

  <!-- 节点11: 历史记录 -->
  <g transform="translate(300, 740)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">历史记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">变更日志追踪</text>
  </g>

  <!-- 节点12: 成果关联 -->
  <g transform="translate(600, 740)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果关联</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全链条跟踪</text>
  </g>

  <!-- 从数据入库到历史记录和成果关联的连接线 -->
  <path d="M 550 590 C 500 650, 450 700, 400 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 590 C 700 650, 750 700, 700 740" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从成果关联回到数据列表的反馈循环 -->
  <path d="M 700 740 C 1100 700, 1100 200, 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="450" text-anchor="middle" font-size="12" fill="#555" transform="rotate(-90 1050 450)">持续优化</text>

</svg>