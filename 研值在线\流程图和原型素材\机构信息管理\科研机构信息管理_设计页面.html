<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研机构信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研机构信息管理</h1>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                检索条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="orgName" class="block text-sm font-medium text-gray-700 mb-1">机构名称</label>
                    <input type="text" id="orgName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入科研机构名称">
                </div>
                <div>
                    <label for="creditCode" class="block text-sm font-medium text-gray-700 mb-1">统一信用代码</label>
                    <input type="text" id="creditCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入统一社会信用代码">
                </div>
                <div>
                    <label for="legalPerson" class="block text-sm font-medium text-gray-700 mb-1">法定代表人</label>
                    <input type="text" id="legalPerson" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入法定代表人姓名">
                </div>
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-1">住所</label>
                    <input type="text" id="address" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入机构住所">
                </div>
                <div>
                    <label for="hostUnit" class="block text-sm font-medium text-gray-700 mb-1">举办单位</label>
                    <input type="text" id="hostUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入举办单位">
                </div>
                <div>
                    <label for="techField" class="block text-sm font-medium text-gray-700 mb-1">技术领域</label>
                    <input type="text" id="techField" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入技术领域">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 批量操作区 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex space-x-3">
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增机构
                </button>
                <div class="relative">
                    <button onclick="toggleExportDropdown()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        批量导出
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="exportDropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                        <div class="py-1">
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                        </div>
                    </div>
                </div>
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    批量导入
                </button>
            </div>
            <div class="text-sm text-gray-600">
                共 <span class="font-medium text-gray-900">128</span> 条记录
            </div>
        </div>

        <!-- 数据列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科研机构名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">统一信用代码</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">法定代表人</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举办单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">研发人员</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科创平台</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科研项目</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波市材料研究所</div>
                            <div class="text-sm text-gray-500">新材料研发</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">91330201MA2XXXXX1</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张明远</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">86</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal()" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openRelationModal()" class="text-green-600 hover:text-green-900">关联</button>
                            <button onclick="deleteItem()" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波大学海洋研究院</div>
                            <div class="text-sm text-gray-500">海洋科技</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">91330201MA2XXXXX2</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李海洋</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波大学</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">124</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal()" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openRelationModal()" class="text-green-600 hover:text-green-900">关联</button>
                            <button onclick="deleteItem()" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波智能制造研究院</div>
                            <div class="text-sm text-gray-500">智能制造</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">91330201MA2XXXXX3</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王智能</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市经信局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">67</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">15</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal()" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openRelationModal()" class="text-green-600 hover:text-green-900">关联</button>
                            <button onclick="deleteItem()" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波生物医药研究院</div>
                            <div class="text-sm text-gray-500">生物医药</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">91330201MA2XXXXX4</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">赵健康</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市卫健委</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">92</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">6</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">18</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal()" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openRelationModal()" class="text-green-600 hover:text-green-900">关联</button>
                            <button onclick="deleteItem()" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波新能源研究院</div>
                            <div class="text-sm text-gray-500">新能源技术</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">91330201MA2XXXXX5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">孙清洁</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市能源局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">78</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">7</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">21</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal()" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openRelationModal()" class="text-green-600 hover:text-green-900">关联</button>
                            <button onclick="deleteItem()" class="text-red-600 hover:text-red-900">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">128</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">科研机构详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 基础信息 -->
                    <div class="lg:col-span-2">
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">基础信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <span class="text-gray-500">机构名称：</span>
                                    <span class="font-medium text-gray-900">宁波市材料研究所</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">统一信用代码：</span>
                                    <span class="font-medium text-gray-900">91330201MA2XXXXX1</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">法定代表人：</span>
                                    <span class="font-medium text-gray-900">张明远</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">举办单位：</span>
                                    <span class="font-medium text-gray-900">宁波市科技局</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">住所：</span>
                                    <span class="font-medium text-gray-900">宁波市高新区光华路299号</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">成立日期：</span>
                                    <span class="font-medium text-gray-900">2018-05-12</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">机构类型：</span>
                                    <span class="font-medium text-gray-900">科研院所</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">技术领域：</span>
                                    <span class="font-medium text-gray-900">新材料研发</span>
                                </div>
                                <div class="md:col-span-2">
                                    <span class="text-gray-500">机构简介：</span>
                                    <p class="font-medium text-gray-900 mt-1">宁波市材料研究所是宁波市政府重点支持的新型研发机构，专注于新材料领域的基础研究与应用开发，拥有国家级材料检测中心和多个专业实验室。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 研发团队 -->
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-md font-semibold text-gray-900">研发团队</h4>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看全部</button>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-blue-600 font-medium">张</span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">张明远</div>
                                            <div class="text-sm text-gray-500">所长/研究员</div>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-sm text-gray-700">
                                        <p>材料学博士，享受国务院特殊津贴专家，主持国家级科研项目5项。</p>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <span class="text-blue-600 font-medium">李</span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">李科研</div>
                                            <div class="text-sm text-gray-500">副研究员</div>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-sm text-gray-700">
                                        <p>高分子材料专家，发表SCI论文20余篇，获发明专利5项。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧信息 -->
                    <div>
                        <!-- 关联统计 -->
                        <div class="bg-gray-50 rounded-lg p-6 mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">关联统计</h4>
                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-700">研发人员总数</span>
                                        <span class="text-sm font-medium text-gray-900">86人</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 80%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-700">科创平台</span>
                                        <span class="text-sm font-medium text-gray-900">5个</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-600 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-700">科研项目</span>
                                        <span class="text-sm font-medium text-gray-900">12项</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-600 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between items-center mb-1">
                                        <span class="text-sm text-gray-700">科研成果</span>
                                        <span class="text-sm font-medium text-gray-900">28项</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-red-600 h-2 rounded-full" style="width: 70%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 最近动态 -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">最近动态</h4>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4 py-1">
                                    <div class="text-sm font-medium text-gray-900">新增科研项目立项</div>
                                    <div class="text-xs text-gray-500">2024-01-10</div>
                                </div>
                                <div class="border-l-4 border-green-500 pl-4 py-1">
                                    <div class="text-sm font-medium text-gray-900">获得发明专利授权</div>
                                    <div class="text-xs text-gray-500">2023-12-25</div>
                                </div>
                                <div class="border-l-4 border-yellow-500 pl-4 py-1">
                                    <div class="text-sm font-medium text-gray-900">参加国际学术会议</div>
                                    <div class="text-xs text-gray-500">2023-11-18</div>
                                </div>
                                <button class="w-full text-center text-blue-600 hover:text-blue-800 text-sm mt-2">
                                    查看更多动态
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-2/3 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑科研机构信息</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">机构名称 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市材料研究所" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">统一信用代码 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="91330201MA2XXXXX1" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">法定代表人 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="张明远" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">举办单位 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市科技局" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">住所 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="宁波市高新区光华路299号" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">成立日期 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="2018-05-12" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">机构类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                                <option value="科研院所" selected>科研院所</option>
                                <option value="高校">高校</option>
                                <option value="企业研发中心">企业研发中心</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">技术领域 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" value="新材料研发" required>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">机构简介 <span class="text-red-500">*</span></label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" required>宁波市材料研究所是宁波市政府重点支持的新型研发机构，专注于新材料领域的基础研究与应用开发，拥有国家级材料检测中心和多个专业实验室。</textarea>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 关联管理弹窗 -->
    <div id="relationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">关联管理 - 宁波市材料研究所</h3>
                    <button onclick="closeModal('relationModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="mb-6">
                    <ul class="flex border-b border-gray-200">
                        <li class="mr-1">
                            <button class="bg-white inline-block py-2 px-4 text-blue-600 font-medium border-b-2 border-blue-500">研发团队</button>
                        </li>
                        <li class="mr-1">
                            <button class="bg-white inline-block py-2 px-4 text-gray-600 hover:text-blue-600 font-medium">科创平台</button>
                        </li>
                        <li class="mr-1">
                            <button class="bg-white inline-block py-2 px-4 text-gray-600 hover:text-blue-600 font-medium">科研项目</button>
                        </li>
                        <li class="mr-1">
                            <button class="bg-white inline-block py-2 px-4 text-gray-600 hover:text-blue-600 font-medium">科研成果</button>
                        </li>
                    </ul>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 已关联列表 -->
                    <div class="lg:col-span-1">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-4">已关联研发团队</h4>
                            <div class="space-y-3">
                                <div class="border border-gray-200 rounded-lg p-3 flex justify-between items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">张明远团队</div>
                                        <div class="text-xs text-gray-500">材料科学</div>
                                    </div>
                                    <button class="text-red-600 hover:text-red-900 text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-3 flex justify-between items-center">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">李科研团队</div>
                                        <div class="text-xs text-gray-500">高分子材料</div>
                                    </div>
                                    <button class="text-red-600 hover:text-red-900 text-sm">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 可关联列表 -->
                    <div class="lg:col-span-2">
                        <div class="bg-gray-50 rounded-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h4 class="text-md font-semibold text-gray-900">可关联研发团队</h4>
                                <div class="relative w-64">
                                    <input type="text" placeholder="搜索团队名称或负责人" class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 absolute left-3 top-2.5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-3 hover:border-blue-500">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">王创新团队</div>
                                            <div class="text-xs text-gray-500">纳米材料</div>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-700">
                                        <p>负责人：王创新</p>
                                        <p>成员：5人</p>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-3 hover:border-blue-500">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">赵技术团队</div>
                                            <div class="text-xs text-gray-500">复合材料</div>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-700">
                                        <p>负责人：赵技术</p>
                                        <p>成员：8人</p>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-3 hover:border-blue-500">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">钱研发团队</div>
                                            <div class="text-xs text-gray-500">功能材料</div>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-700">
                                        <p>负责人：钱研发</p>
                                        <p>成员：6人</p>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-3 hover:border-blue-500">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">孙科技团队</div>
                                            <div class="text-xs text-gray-500">智能材料</div>
                                        </div>
                                        <button class="text-blue-600 hover:text-blue-900 text-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="mt-2 text-xs text-gray-700">
                                        <p>负责人：孙科技</p>
                                        <p>成员：7人</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-4 flex justify-between items-center">
                                <div class="text-sm text-gray-500">
                                    显示 1-4 条，共 12 条记录
                                </div>
                                <div class="flex space-x-2">
                                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal('relationModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        保存关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 1. 全局UI控制函数
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // 快捷函数
        function openDetailModal() { openModal('detailModal'); }
        function openEditModal() { openModal('editModal'); }
        function openRelationModal() { openModal('relationModal'); }
        
        function deleteItem() {
            if (confirm('确定要删除此科研机构吗？此操作不可恢复！')) {
                alert('删除成功 (原型演示)');
            }
        }

        function toggleExportDropdown() {
            document.getElementById('exportDropdown').classList.toggle('hidden');
        }

        // 2. DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 点击弹窗外部关闭
            document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(modal.id);
                    }
                });
            });

            // 绑定ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.querySelectorAll('[id$="Modal"]').forEach(modal => {
                        if (!modal.classList.contains('hidden')) {
                            closeModal(modal.id);
                        }
                    });
                }
            });

            // 表单提交处理
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    alert('表单已提交 (原型演示)');
                    closeModal(form.closest('[id$="Modal"]').id);
                });
            });

            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', function(e) {
                if (!e.target.closest('#exportDropdown') && !e.target.closest('[onclick*="toggleExportDropdown"]')) {
                    document.getElementById('exportDropdown').classList.add('hidden');
                }
            });
        });
    </script>
</body>
</html>