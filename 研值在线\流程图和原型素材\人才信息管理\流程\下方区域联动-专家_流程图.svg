<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下方区域联动-专家业务流程</text>

  <!-- 阶段一：数据同步与信息归集 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与信息归集</text>
  
  <!-- 节点1: 多源数据同步 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">科技局、高校、科研院所</text>
  </g>

  <!-- 节点2: 专家信息归集 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专家信息归集</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">基础信息与标签整合</text>
  </g>

  <!-- 节点3: 属性数据生成 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">属性数据生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">研究领域、学历、职称等</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 420 165 Q 460 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 720 165 Q 760 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：页面加载与可视化展示 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：页面加载与可视化展示</text>

  <!-- 节点4: 页面访问加载 -->
  <g transform="translate(150, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面访问加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">专家联动分析页面</text>
  </g>

  <!-- 节点5: 数据结构加载 -->
  <g transform="translate(450, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据结构加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">最新专家结构数据</text>
  </g>

  <!-- 节点6: 可视化组件渲染 -->
  <g transform="translate(750, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化组件渲染</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">词云图、饼图等展示</text>
  </g>

  <!-- 连接线 属性数据 -> 页面访问 -->
  <path d="M 910 200 C 910 240, 350 270, 260 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 370 345 Q 410 345 450 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 670 345 Q 710 345 750 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：交互筛选与联动分析 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：交互筛选与联动分析</text>

  <!-- 节点7: 词云关键词点击 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">词云关键词点击</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">专家分组筛选</text>
  </g>

  <!-- 节点8: 饼图分块选择 -->
  <g transform="translate(400, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">饼图分块选择</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">属性条件筛选</text>
  </g>

  <!-- 节点9: 联动数据刷新 -->
  <g transform="translate(700, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">联动数据刷新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">多属性条件组合分析</text>
  </g>

  <!-- 节点10: 统计结果更新 -->
  <g transform="translate(1000, 490)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计结果更新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">实时分析展示</text>
  </g>

  <!-- 连接线 可视化组件 -> 词云点击 -->
  <path d="M 750 380 C 600 420, 300 450, 210 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 可视化组件 -> 饼图选择 -->
  <path d="M 800 380 C 700 420, 600 450, 510 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 9 -->
  <path d="M 320 525 C 450 525, 550 525, 700 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 620 525 Q 660 525 700 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 920 525 Q 960 525 1000 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：清册管理与数据应用 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：清册管理与数据应用</text>

  <!-- 节点11: 钻取入口访问 -->
  <g transform="translate(150, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">钻取入口访问</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">专家清册明细页面</text>
  </g>

  <!-- 节点12: 专家信息展示 -->
  <g transform="translate(450, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">专家信息展示</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">全量信息查询筛选</text>
  </g>

  <!-- 节点13: 数据导出管理 -->
  <g transform="translate(750, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出管理</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">支持后续操作</text>
  </g>

  <!-- 节点14: 操作日志归档 -->
  <g transform="translate(1050, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志归档</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">政策支持与资源配置</text>
  </g>

  <!-- 连接线 统计结果 -> 钻取入口 -->
  <path d="M 1000 560 C 900 600, 400 640, 260 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 11 -> 12 -->
  <path d="M 370 705 Q 410 705 450 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 12 -> 13 -->
  <path d="M 670 705 Q 710 705 750 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 13 -> 14 -->
  <path d="M 970 705 Q 1010 705 1050 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 操作日志 -> 数据同步 -->
  <path d="M 1160 670 C 1300 600, 1350 300, 1300 200, 1200 100, 400 100, 310 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1300" y="350" text-anchor="middle" font-size="11" fill="#666">流程优化反馈</text>

  <!-- 反馈循环2: 数据导出 -> 可视化组件 -->
  <path d="M 750 705 C 50 650, 20 500, 50 400, 150 350, 750 350" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="550" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 50, 550)">数据应用反馈</text>

</svg>