<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">机构详细信息分析流程图</text>

  <!-- 阶段一：数据触发与加载 -->
  <text x="700" y="80" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据触发与加载</text>
  
  <!-- 节点1: 用户访问触发 -->
  <g transform="translate(50, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问触发</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">页面访问或搜索定位</text>
  </g>

  <!-- 节点2: 数据获取 -->
  <g transform="translate(350, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">综合数据获取</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">档案、层级、资质、财务数据</text>
  </g>

  <!-- 节点3: 数据合并 -->
  <g transform="translate(650, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据合并</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">科技大脑+本项目数据</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 250 135 Q 300 135 350 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 570 135 Q 610 135 650 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据处理与校验 -->
  <text x="700" y="230" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据处理与校验</text>

  <!-- 节点4: 数据校验 -->
  <g transform="translate(200, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">完整性、一致性校验</text>
  </g>

  <!-- 节点5: 质量报告 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">质量报告</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">异常数据推送维护队列</text>
  </g>

  <!-- 节点6: 数据处理 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">结构化、去重、计算指标</text>
  </g>

  <!-- 连接线从数据合并到数据校验 -->
  <path d="M 750 170 C 750 200, 300 220, 300 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 285 Q 450 285 500 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：模型生成与可视化 -->
  <text x="700" y="380" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：模型生成与可视化</text>

  <!-- 节点7: 可视化模型 -->
  <g transform="translate(150, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化模型</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">树形图、趋势图数据</text>
  </g>

  <!-- 节点8: 前端渲染 -->
  <g transform="translate(450, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端渲染</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">卡片、图表、交互组件</text>
  </g>

  <!-- 节点9: 交互事件 -->
  <g transform="translate(750, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">交互事件</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">钻取、筛选、详情查看</text>
  </g>

  <!-- 连接线从数据处理到可视化模型 -->
  <path d="M 900 320 C 900 350, 250 370, 250 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 7 -> 8 -> 9 -->
  <path d="M 350 435 Q 400 435 450 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 435 Q 700 435 750 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：操作管理与监控 -->
  <text x="700" y="530" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：操作管理与监控</text>

  <!-- 节点10: 编辑操作 -->
  <g transform="translate(100, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑操作</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">字段校验、审批流程</text>
  </g>

  <!-- 节点11: 实时监控 -->
  <g transform="translate(350, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时监控</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">财务异常、资质到期</text>
  </g>

  <!-- 节点12: 预警推送 -->
  <g transform="translate(600, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">预警推送</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">风险标识、消息提醒</text>
  </g>

  <!-- 节点13: 报告导出 -->
  <g transform="translate(850, 550)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">报告导出</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">PDF生成、下载链接</text>
  </g>

  <!-- 连接线 10 -> 11 -> 12 -> 13 -->
  <path d="M 280 585 Q 315 585 350 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 530 585 Q 565 585 600 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 585 Q 815 585 850 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从编辑操作回到数据处理 -->
  <path d="M 190 550 C 190 500, 100 350, 200 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="120" y="430" font-size="11" fill="#666">审批通过后</text>
  <text x="120" y="445" font-size="11" fill="#666">重算指标</text>

  <!-- 监控循环：从实时监控到预警推送再回到前端渲染 -->
  <path d="M 440 550 C 440 520, 550 520, 550 470" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="480" y="510" font-size="11" fill="#666">持续监控</text>

  <!-- 从交互事件到编辑操作的连接 -->
  <path d="M 850 470 C 850 500, 190 520, 190 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="520" y="495" font-size="11" fill="#555">用户操作触发</text>

  <!-- 从交互事件到报告导出的连接 -->
  <path d="M 850 470 Q 850 510 940 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="880" y="500" font-size="11" fill="#555">导出请求</text>

</svg>