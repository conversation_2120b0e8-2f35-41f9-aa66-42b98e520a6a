<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="40" font-size="24" text-anchor="middle" font-weight="600" fill="#333">集团结构查询与企业详情钻取流程图</text>

  <!-- 阶段一：查询请求与数据检索 -->
  <text x="700" y="80" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：查询请求与数据检索</text>
  
  <!-- 节点1: 用户筛选 -->
  <g transform="translate(100, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户筛选</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">多维度筛选条件</text>
  </g>

  <!-- 节点2: 查询排队 -->
  <g transform="translate(400, 100)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询排队</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">系统接收请求</text>
  </g>

  <!-- 节点3: 数据检索 -->
  <g transform="translate(700, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据检索</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">机构主数据仓检索</text>
  </g>

  <!-- 节点4: 树结构生成 -->
  <g transform="translate(1000, 100)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">树结构生成</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">按层级关系生成</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -> 4 -->
  <path d="M 300 135 Q 350 135 400 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 135 Q 650 135 700 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 920 135 Q 960 135 1000 135" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：视图渲染与缓存管理 -->
  <text x="700" y="230" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：视图渲染与缓存管理</text>

  <!-- 节点5: 层级视图 -->
  <g transform="translate(200, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">层级视图</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">集团层级渲染</text>
  </g>

  <!-- 节点6: 结果缓存 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结果缓存</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">缓存当前结果集</text>
  </g>

  <!-- 节点7: 节点展开 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">节点展开</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">用户展开集团节点</text>
  </g>

  <!-- 连接线从树结构生成到层级视图 -->
  <path d="M 1110 170 C 1110 200, 300 220, 300 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 5 -> 6 -> 7 -->
  <path d="M 400 285 Q 450 285 500 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：缓存校验与增量请求 -->
  <text x="700" y="380" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：缓存校验与增量请求</text>

  <!-- 节点8: 缓存校验 -->
  <g transform="translate(300, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">检查企业清单</text>
  </g>

  <!-- 节点9: 增量请求 -->
  <g transform="translate(600, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">增量请求</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">拉取关联企业</text>
  </g>

  <!-- 节点10: 缓存更新 -->
  <g transform="translate(900, 400)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存更新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">更新本地缓存</text>
  </g>

  <!-- 连接线从节点展开到缓存校验 -->
  <path d="M 900 320 C 900 350, 400 370, 400 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 8 -> 9 -> 10 -->
  <path d="M 500 435 Q 550 435 600 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="550" y="425" font-size="11" fill="#666">缺失时</text>
  <path d="M 800 435 Q 850 435 900 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：企业详情与数据聚合 -->
  <text x="700" y="530" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：企业详情与数据聚合</text>

  <!-- 节点11: 企业点击 -->
  <g transform="translate(150, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">企业点击</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">用户点击企业节点</text>
  </g>

  <!-- 节点12: 数据聚合 -->
  <g transform="translate(400, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据聚合</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">基础信息、变更记录</text>
  </g>

  <!-- 节点13: 详情数据包 -->
  <g transform="translate(650, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情数据包</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">合并生成数据包</text>
  </g>

  <!-- 节点14: 访问日志 -->
  <g transform="translate(900, 550)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">访问日志</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">写入访问记录</text>
  </g>

  <!-- 连接线从缓存更新到企业点击 -->
  <path d="M 1000 470 C 1000 500, 250 520, 250 550" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 11 -> 12 -> 13 -> 14 -->
  <path d="M 350 585 Q 375 585 400 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 585 Q 625 585 650 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 585 Q 875 585 900 585" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：前端渲染与跨模块跳转 -->
  <text x="700" y="680" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段五：前端渲染与跨模块跳转</text>

  <!-- 节点15: 抽屉渲染 -->
  <g transform="translate(200, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">抽屉渲染</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">前端详情展示</text>
  </g>

  <!-- 节点16: 行为记录 -->
  <g transform="translate(450, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">行为记录</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">点击行为记录</text>
  </g>

  <!-- 节点17: 跨模块跳转 -->
  <g transform="translate(700, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">跨模块跳转</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">携带参数重定向</text>
  </g>

  <!-- 节点18: 操作日志中心 -->
  <g transform="translate(950, 700)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#81C784" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志中心</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">同步写入日志</text>
  </g>

  <!-- 连接线从详情数据包到抽屉渲染 -->
  <path d="M 750 620 C 750 650, 300 670, 300 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线从访问日志到行为记录 -->
  <path d="M 1000 620 C 1000 650, 550 670, 550 700" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线从抽屉渲染到跨模块跳转 -->
  <path d="M 400 735 Q 550 735 700 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="550" y="725" font-size="11" fill="#666">用户选择跳转</text>
  <!-- 连接线到操作日志中心 -->
  <path d="M 900 735 Q 925 735 950 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从缓存校验回到结果缓存 -->
  <path d="M 300 435 C 200 450, 100 450, 50 450, 50 300, 50 285, 500 285" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="470" font-size="11" fill="#666">缓存命中循环</text>

  <!-- 反馈循环：从操作日志中心回到用户筛选 -->
  <path d="M 1050 770 C 1200 800, 1300 800, 1350 800, 1350 150, 1350 135, 300 135" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1200" y="830" font-size="11" fill="#666">审计追溯循环</text>

</svg>