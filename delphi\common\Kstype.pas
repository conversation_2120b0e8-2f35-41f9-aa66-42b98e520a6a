unit Kstype;

interface

uses
  Classes;

type
  TKstype = class
  private
    Fkstypemainname: string;
    Fkstypesecid: integer;
    Fkstypemainid: integer;
    Fkstypesecname: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property kstypemainname: string read Fkstypemainname write Fkstypemainname;
    property kstypesecid: integer read Fkstypesecid write Fkstypesecid;
    property kstypemainid: integer read Fkstypemainid write Fkstypemainid;
    property kstypesecname: string read Fkstypesecname write Fkstypesecname;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
