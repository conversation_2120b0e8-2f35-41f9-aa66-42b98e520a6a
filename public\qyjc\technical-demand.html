<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>技术需求模块 - 企业监测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #F97316, #EA580C);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .status-pending { color: #F59E0B; }
        .status-matched { color: #10B981; }
        .status-processing { color: #3B82F6; }
        .status-completed { color: #8B5CF6; }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 头部导航 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" 
                            class="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-arrow-left"></i>
                        <span>返回</span>
                    </button>
                    <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-lightbulb text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">技术需求模块</h1>
                        <p class="text-sm text-gray-600">企业技术难题采集与对接管理平台</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                    <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 功能说明卡片 -->
        <div class="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white mb-6">
            <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-lightbulb text-2xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold mb-2">技术需求采集与对接</h2>
                    <p class="text-orange-100">
                        采集区域企业在近期提出的技术难题或研发方向，支持按技术领域、区域、提报时间进行分类管理，显示原始提报内容及匹配状态，促进产学研对接合作。
                    </p>
                </div>
            </div>
        </div>

        <!-- 筛选面板 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-filter text-orange-500 mr-2"></i>
                筛选条件
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                        <option>全部领域</option>
                        <option>新材料技术</option>
                        <option>电子信息技术</option>
                        <option>生物医药技术</option>
                        <option>先进制造技术</option>
                        <option>新能源技术</option>
                        <option>环保技术</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所在区域</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                        <option>全部区域</option>
                        <option>海曙区</option>
                        <option>江北区</option>
                        <option>北仑区</option>
                        <option>镇海区</option>
                        <option>鄞州区</option>
                        <option>奉化区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">提报时间</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                        <option>全部时间</option>
                        <option>近1个月</option>
                        <option>近3个月</option>
                        <option>近6个月</option>
                        <option>近1年</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">对接状态</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                        <option>全部状态</option>
                        <option>待对接</option>
                        <option>已匹配</option>
                        <option>对接中</option>
                        <option>已完成</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">紧急程度</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-orange-500 focus:border-orange-500">
                        <option>全部等级</option>
                        <option>紧急</option>
                        <option>一般</option>
                        <option>待评估</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    重置
                </button>
                <button class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    应用筛选
                </button>
                <button class="border border-orange-500 text-orange-500 px-4 py-2 rounded-lg hover:bg-orange-50 transition-colors">
                    保存为模板
                </button>
            </div>
        </div>

        <!-- 概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">技术需求总数</p>
                        <p class="text-2xl font-bold text-gray-900">411</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上月 +23条
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-lightbulb text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">待对接需求</p>
                        <p class="text-2xl font-bold text-gray-900">284</p>
                        <p class="text-yellow-600 text-xs">
                            <i class="fas fa-clock mr-1"></i>
                            占比 69.1%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">已对接成功</p>
                        <p class="text-2xl font-bold text-gray-900">127</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-check mr-1"></i>
                            占比 30.9%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">覆盖技术领域</p>
                        <p class="text-2xl font-bold text-gray-900">15</p>
                        <p class="text-blue-600 text-xs">
                            <i class="fas fa-sitemap mr-1"></i>
                            主要领域覆盖
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-sitemap text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">平均对接周期</p>
                        <p class="text-2xl font-bold text-gray-900">45</p>
                        <p class="text-purple-600 text-xs">
                            <i class="fas fa-calendar mr-1"></i>
                            天数
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 技术需求列表 -->
            <div class="lg:col-span-2 bg-white rounded-xl border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-list text-orange-500 mr-2"></i>
                        最新技术需求
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="space-y-4 max-h-[600px] overflow-y-auto">
                    <!-- 需求项目1 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h4 class="font-medium text-gray-900">高性能锂电池隔膜材料技术开发</h4>
                                    <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">紧急</span>
                                    <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">待对接</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">提报企业：宁波新能源材料科技有限公司</p>
                                <p class="text-xs text-gray-500 mb-3">技术领域：新材料技术 | 提报时间：2024-01-12</p>
                                <p class="text-sm text-gray-700 mb-3">
                                    需求描述：急需开发高温稳定性、低透气率的锂电池隔膜材料，要求在150℃下保持结构稳定，透气率小于200s/100mL，用于高端动力电池产品...
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span><i class="fas fa-dollar-sign mr-1"></i>预算：500-800万</span>
                                    <span><i class="fas fa-clock mr-1"></i>周期：12个月</span>
                                    <span><i class="fas fa-users mr-1"></i>联系人：张工</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-2">
                            <button class="bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600 transition-colors">
                                推荐匹配
                            </button>
                            <button class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600 transition-colors">
                                查看详情
                            </button>
                        </div>
                    </div>

                    <!-- 需求项目2 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h4 class="font-medium text-gray-900">智能机器视觉检测算法优化</h4>
                                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">一般</span>
                                    <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">对接中</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">提报企业：宁波智能制造装备有限公司</p>
                                <p class="text-xs text-gray-500 mb-3">技术领域：电子信息技术 | 提报时间：2024-01-10</p>
                                <p class="text-sm text-gray-700 mb-3">
                                    需求描述：现有机器视觉检测系统在复杂光照环境下识别精度不足，需要优化深度学习算法，提升检测精度至99%以上...
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span><i class="fas fa-dollar-sign mr-1"></i>预算：200-300万</span>
                                    <span><i class="fas fa-clock mr-1"></i>周期：8个月</span>
                                    <span><i class="fas fa-users mr-1"></i>联系人：李总</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-2">
                            <button class="bg-gray-400 text-white px-3 py-1 rounded text-xs cursor-not-allowed">
                                已匹配
                            </button>
                            <button class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600 transition-colors">
                                查看详情
                            </button>
                        </div>
                    </div>

                    <!-- 需求项目3 -->
                    <div class="border border-gray-200 rounded-lg p-4 hover-lift">
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <h4 class="font-medium text-gray-900">生物酶催化工艺技术改进</h4>
                                    <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">一般</span>
                                    <span class="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full">已完成</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2">提报企业：海天生物医药有限公司</p>
                                <p class="text-xs text-gray-500 mb-3">技术领域：生物医药技术 | 提报时间：2023-12-15</p>
                                <p class="text-sm text-gray-700 mb-3">
                                    需求描述：现有生物酶催化工艺转化率较低，需要筛选高活性酶种或改进反应条件，提升产物收率20%以上...
                                </p>
                                <div class="flex items-center space-x-4 text-xs text-gray-500">
                                    <span><i class="fas fa-dollar-sign mr-1"></i>预算：150-250万</span>
                                    <span><i class="fas fa-clock mr-1"></i>周期：6个月</span>
                                    <span><i class="fas fa-users mr-1"></i>联系人：王博士</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-end space-x-2">
                            <span class="bg-green-100 text-green-600 px-3 py-1 rounded text-xs">
                                <i class="fas fa-check mr-1"></i>
                                对接成功
                            </span>
                            <button class="bg-orange-500 text-white px-3 py-1 rounded text-xs hover:bg-orange-600 transition-colors">
                                查看详情
                            </button>
                        </div>
                    </div>

                    <!-- 更多需求 -->
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <div class="flex-1">
                                <span class="font-medium text-gray-900">纳米复合材料表面改性技术</span>
                                <span class="text-gray-500 ml-2">- 慈溪新材料研发中心</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">待对接</span>
                                <button class="text-orange-500 hover:text-orange-700 text-xs">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <div class="flex-1">
                                <span class="font-medium text-gray-900">工业废水深度处理技术</span>
                                <span class="text-gray-500 ml-2">- 象山环保科技股份</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="bg-blue-100 text-blue-600 text-xs px-2 py-1 rounded-full">对接中</span>
                                <button class="text-orange-500 hover:text-orange-700 text-xs">详情</button>
                            </div>
                        </div>
                        <div class="flex justify-between items-center py-2 border-b border-gray-100">
                            <div class="flex-1">
                                <span class="font-medium text-gray-900">5G通信模组小型化设计</span>
                                <span class="text-gray-500 ml-2">- 北仑通信设备制造</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full">待对接</span>
                                <button class="text-orange-500 hover:text-orange-700 text-xs">详情</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button class="text-orange-500 hover:text-orange-700 text-sm font-medium">
                        查看全部需求 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
            </div>

            <!-- 侧边栏统计 -->
            <div class="space-y-6">
                <!-- 领域分布 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6">
                    <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-chart-pie text-blue-500 mr-2"></i>
                        需求领域分布
                    </h3>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">新材料技术</span>
                            </div>
                            <span class="text-sm font-medium text-blue-600">89</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">电子信息技术</span>
                            </div>
                            <span class="text-sm font-medium text-green-600">76</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">生物医药技术</span>
                            </div>
                            <span class="text-sm font-medium text-purple-600">67</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">先进制造技术</span>
                            </div>
                            <span class="text-sm font-medium text-yellow-600">54</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <span class="text-sm text-gray-700">新能源技术</span>
                            </div>
                            <span class="text-sm font-medium text-red-600">42</span>
                        </div>
                        <div class="text-center mt-3">
                            <button class="text-blue-500 text-xs hover:text-blue-700">查看更多</button>
                        </div>
                    </div>
                </div>

                <!-- 对接状态统计 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6">
                    <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-tasks text-green-500 mr-2"></i>
                        对接状态统计
                    </h3>
                    <div class="space-y-4">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                <i class="fas fa-clock text-yellow-600 text-xl"></i>
                            </div>
                            <p class="text-2xl font-bold text-yellow-600">284</p>
                            <p class="text-sm text-gray-600">待对接</p>
                        </div>
                        <div class="grid grid-cols-3 gap-3 text-center">
                            <div>
                                <p class="text-lg font-bold text-blue-600">42</p>
                                <p class="text-xs text-gray-600">已匹配</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-purple-600">58</p>
                                <p class="text-xs text-gray-600">对接中</p>
                            </div>
                            <div>
                                <p class="text-lg font-bold text-green-600">27</p>
                                <p class="text-xs text-gray-600">已完成</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 紧急需求提醒 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6">
                    <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                        紧急需求提醒
                    </h3>
                    <div class="space-y-3">
                        <div class="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                            <p class="text-sm font-medium text-red-700">高性能锂电池隔膜材料</p>
                            <p class="text-xs text-red-600">宁波新能源材料科技 · 3天前</p>
                        </div>
                        <div class="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                            <p class="text-sm font-medium text-red-700">医用植入材料生物相容性</p>
                            <p class="text-xs text-red-600">海天医疗器械 · 5天前</p>
                        </div>
                        <div class="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                            <p class="text-sm font-medium text-red-700">工业机器人精密减速器</p>
                            <p class="text-xs text-red-600">宁波智能装备 · 7天前</p>
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <button class="text-red-500 hover:text-red-700 text-sm font-medium">
                            查看全部紧急需求 <i class="fas fa-arrow-right ml-1"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据导出区域 -->
        <div class="mt-6 bg-white rounded-xl border border-gray-200 p-6">
            <h3 class="font-bold text-gray-900 mb-4 flex items-center">
                <i class="fas fa-download text-orange-500 mr-2"></i>
                数据导出与分享
            </h3>
            <div class="flex flex-wrap gap-3">
                <button class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors">
                    <i class="fas fa-file-excel mr-2"></i>
                    导出Excel
                </button>
                <button class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                    <i class="fas fa-file-pdf mr-2"></i>
                    生成PDF报告
                </button>
                <button class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors">
                    <i class="fas fa-share-alt mr-2"></i>
                    分享链接
                </button>
                <button class="border border-purple-500 text-purple-500 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors">
                    <i class="fas fa-plus mr-2"></i>
                    新增需求
                </button>
                <button class="border border-blue-500 text-blue-500 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                    <i class="fas fa-calendar mr-2"></i>
                    定期推送设置
                </button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 下拉框变化事件
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log('筛选条件变化:', this.value);
                });
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 状态标签点击事件
            const statusButtons = document.querySelectorAll('.status-pending, .status-matched, .status-processing, .status-completed');
            statusButtons.forEach(button => {
                button.addEventListener('click', function() {
                    console.log('点击状态:', this.textContent);
                });
            });
        });
    </script>
</body>
</html> 