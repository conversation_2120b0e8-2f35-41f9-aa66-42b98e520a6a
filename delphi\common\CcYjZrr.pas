unit CcYjZrr;

interface

uses
  Classes;

type
  TCcYjZrr = class
  private
    FZrr1: string;
    FZrr2: string;
    FBz: string;
    FDdid: integer;
    FYsid: integer;
    FYjxm: string;

    FPk1: double;
    FPk1Date: string;
    FPk2: double;
    FPk2Date: string;
    FYjtypedl: string;
    FYjtypexl: string;
    FShenhe: string;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Zrr1: string read FZrr1 write FZrr1;
    property Zrr2: string read FZrr2 write FZrr2;
    property Bz: string read FBz write FBz;
    property Ddid: integer read FDdid write FDdid;
    property Ysid: integer read FYsid write FYsid;
    property Yjxm: string read FYjxm write FYjxm;
    property Pk1: double read FPk1 write FPk1;
    property Pk1Date: string read FPk1Date write FPk1Date;
    property Pk2: double read FPk2 write FPk2;
    property Pk2Date: string read FPk2Date write FPk2Date;
    property Yjtypedl: string read FYjtypedl write FYjtypedl;
    property Yjtypexl: string read FYjtypexl write FYjtypexl;
    property Shenhe: string read FShenhe write FShenhe;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
