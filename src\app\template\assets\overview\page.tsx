'use client'

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue 
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { FileText, Filter, Calendar } from "lucide-react"
import { AssetOverviewStats } from "@/components/assets/overview-stats"
import { AssetDistribution } from "@/components/assets/asset-distribution"
import { AssetHealth } from "@/components/assets/asset-health"
import { AssetLifecycle } from "@/components/assets/asset-lifecycle"
import { AssetAlerts } from "@/components/assets/asset-alerts"
import { AssetUtilization } from "@/components/assets/asset-utilization"
import { AssetTrends } from "@/components/assets/asset-trends"

export default function AssetOverviewPage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-medium text-gray-900">资产总览</h1>
        </div>
        <div className="flex items-center gap-4">
          <Select defaultValue="all">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="资产类型" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部资产</SelectItem>
              <SelectItem value="infrastructure">基础设施</SelectItem>
              <SelectItem value="application">应用系统</SelectItem>
              <SelectItem value="network">网络设备</SelectItem>
              <SelectItem value="security">安全设备</SelectItem>
              <SelectItem value="storage">存储设备</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="month">
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">最近一周</SelectItem>
              <SelectItem value="month">最近一月</SelectItem>
              <SelectItem value="quarter">最近一季</SelectItem>
              <SelectItem value="year">最近一年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            高级筛选
          </Button>
          <Button variant="secondary">
            <FileText className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      <AssetOverviewStats />
      <div className="grid grid-cols-2 gap-6">
        <AssetDistribution />
        <AssetHealth />
      </div>
      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          <AssetLifecycle />
        </div>
        <AssetAlerts />
      </div>
      <div className="grid grid-cols-3 gap-6">
        <div className="col-span-2">
          <AssetTrends />
        </div>
        <AssetUtilization />
      </div>
      <Card className="border-[#E5E9EF] shadow-sm">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>维护提醒</CardTitle>
          <Badge variant="outline" className="bg-orange-50 text-orange-600">
            <Calendar className="mr-2 h-4 w-4" />
            近期待处理
          </Badge>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">核心交换机年度维护</div>
                <div className="text-sm text-gray-500 mt-1">计划时间：2024-04-15</div>
              </div>
              <Badge className="bg-blue-50 text-blue-600">待排期</Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">存储设备扩容升级</div>
                <div className="text-sm text-gray-500 mt-1">计划时间：2024-04-20</div>
              </div>
              <Badge className="bg-purple-50 text-purple-600">已排期</Badge>
            </div>
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium">安全设备固件更新</div>
                <div className="text-sm text-gray-500 mt-1">计划时间：2024-04-25</div>
              </div>
              <Badge className="bg-orange-50 text-orange-600">紧急</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 