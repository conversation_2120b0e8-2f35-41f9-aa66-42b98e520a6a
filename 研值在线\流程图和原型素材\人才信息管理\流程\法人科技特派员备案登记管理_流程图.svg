<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">法人科技特派员备案登记管理流程</text>

  <!-- 阶段一：批次配置与入口 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：批次配置与入口</text>
  
  <!-- 节点1: 批次管理配置 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批次管理配置</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">配置备案年度、时间区间</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">设置启用状态作为入口绑定项</text>
  </g>

  <!-- 阶段二：备案登记流程 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：备案登记流程</text>

  <!-- 节点2: 法人单位登录 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">法人单位登录</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">在备案周期内登录系统</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">点击新增备案按钮</text>
  </g>

  <!-- 节点3: 信息填写校验 -->
  <g transform="translate(480, 320)" filter="url(#soft-shadow)">
    <rect width="240" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="120" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息填写校验</text>
    <text x="120" y="50" text-anchor="middle" font-size="12" fill="#555">填写单位信息、服务协议</text>
    <text x="120" y="65" text-anchor="middle" font-size="12" fill="#555">实时格式校验与必填验证</text>
  </g>

  <!-- 节点4: 保存提交 -->
  <g transform="translate(780, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">保存提交</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">暂存草稿或提交上报</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">记录提交时间与版本号</text>
  </g>

  <!-- 阶段三：审核管理 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：审核管理</text>

  <!-- 节点5: 后台审核 -->
  <g transform="translate(300, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">后台审核</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">管理人员进入待审列表</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">逐条审阅备案数据</text>
  </g>

  <!-- 节点6: 审核决策 -->
  <g transform="translate(650, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核决策</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">通过或退回补正操作</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">记录操作日志与原因</text>
  </g>

  <!-- 节点7: 状态标记 -->
  <g transform="translate(1000, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态标记</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">标记为已备案状态</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">归档至备案数据库</text>
  </g>

  <!-- 阶段四：归档与复用 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：归档与复用</text>
  
  <!-- 节点8: 历史档案与数据复用 -->
  <g transform="translate(500, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">历史档案与数据复用</text>
    <text x="200" y="55" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">自动归档</tspan>
      <tspan dx="60">查询导出</tspan>
      <tspan dx="60">政策匹配</tspan>
    </text>
  </g>

  <!-- 连接线 1 -> 2 (曲线) -->
  <path d="M 650 210 C 600 250, 400 280, 310 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -> 4 (曲线) -->
  <path d="M 420 360 C 450 360, 450 360, 480 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 360 C 750 360, 750 360, 780 360" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5,6,7 (曲线) -->
  <path d="M 800 400 C 700 450, 500 480, 425 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 400 C 890 450, 775 480, 775 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 400 C 1000 450, 1050 480, 1110 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5,6,7 -> 8 (曲线) -->
  <path d="M 425 600 C 500 650, 600 680, 650 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 775 600 C 775 650, 700 680, 700 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1110 600 C 1000 650, 800 680, 750 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 退回补正反馈循环 -->
  <path d="M 650 560 C 500 580, 400 500, 500 400 C 550 380, 600 380, 600 400" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="450" y="590" font-size="12" fill="#666">退回补正</text>

  <!-- 反馈循环：从历史档案回到批次配置 -->
  <path d="M 500 760 C 200 820, 100 400, 200 200 C 300 100, 500 80, 650 130" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="850" font-size="12" fill="#666">持续优化反馈</text>

</svg>