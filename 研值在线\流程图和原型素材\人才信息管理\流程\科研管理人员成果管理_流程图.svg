<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="24" text-anchor="middle" font-weight="600" fill="#333">科研管理人员成果管理流程</text>

  <!-- 阶段一：数据加载与展示 -->
  <text x="700" y="100" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与展示</text>
  
  <!-- 节点1: 页面进入 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面进入</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">成果管理功能模块</text>
  </g>

  <!-- 节点2: 数据加载 -->
  <g transform="translate(610, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据加载</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">已关联成果列表</text>
  </g>

  <!-- 节点3: 列表展示 -->
  <g transform="translate(820, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">列表展示</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">成果信息展示</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 580 165 Q 595 165 610 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 790 165 Q 805 165 820 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：查询与筛选 -->
  <text x="700" y="270" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询与筛选</text>

  <!-- 节点4: 检索条件输入 -->
  <g transform="translate(450, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">检索条件输入</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">查询与筛选区</text>
  </g>

  <!-- 节点5: 实时筛选 -->
  <g transform="translate(700, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时筛选</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">符合条件数据</text>
  </g>

  <!-- 连接线 列表展示 -> 检索条件 -->
  <path d="M 910 200 C 910 230, 650 250, 550 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 650 335 Q 675 335 700 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：成果操作管理 -->
  <text x="700" y="440" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：成果操作管理</text>

  <!-- 节点6: 关联成果 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联成果</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">弹窗选择目标</text>
  </g>

  <!-- 节点7: 修改操作 -->
  <g transform="translate(350, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">修改操作</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">记录变更日志</text>
  </g>

  <!-- 节点8: 移除操作 -->
  <g transform="translate(550, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除操作</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">确认提示删除</text>
  </g>

  <!-- 节点9: 详情钻取 -->
  <g transform="translate(750, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">全量信息展示</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(950, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">选择字段格式</text>
  </g>

  <!-- 连接线 实时筛选 -> 各操作 -->
  <path d="M 750 370 C 650 400, 350 430, 230 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 770 370 C 700 400, 500 430, 430 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 790 370 C 750 400, 700 430, 630 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 370 C 810 400, 830 430, 830 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 830 370 C 870 400, 950 430, 1030 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统处理与同步 -->
  <text x="700" y="610" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统处理与同步</text>

  <!-- 节点11: 校验入库 -->
  <g transform="translate(200, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验入库</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">唯一性合规性</text>
  </g>

  <!-- 节点12: 多层级展示 -->
  <g transform="translate(450, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多层级展示</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">科研活动业绩</text>
  </g>

  <!-- 节点13: 文件生成 -->
  <g transform="translate(700, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文件生成</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">指定格式下载</text>
  </g>

  <!-- 节点14: 数据同步 -->
  <g transform="translate(950, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据同步</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">上游数据源</text>
  </g>

  <!-- 连接线 关联成果 -> 校验入库 -->
  <path d="M 230 540 C 230 580, 270 600, 280 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情钻取 -> 多层级展示 -->
  <path d="M 830 540 C 830 580, 570 600, 540 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据导出 -> 文件生成 -->
  <path d="M 1030 540 C 1030 580, 830 600, 790 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 系统 -> 数据同步 -->
  <path d="M 1100 400 C 1150 500, 1100 580, 1040 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从校验入库回到数据加载 -->
  <path d="M 290 640 C 150 600, 100 300, 150 165 C 200 165, 350 165, 400 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="200" y="400" text-anchor="middle" font-size="11" fill="#666">刷新列表</text>

  <!-- 反馈循环：从数据同步回到数据加载 -->
  <path d="M 1040 640 C 1200 600, 1250 300, 1200 165 C 1150 165, 950 165, 910 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="400" text-anchor="middle" font-size="11" fill="#666">时效性保障</text>

</svg>