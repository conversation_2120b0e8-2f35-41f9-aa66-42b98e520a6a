import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log("接收到批量测试Dify API请求");
  
  try {
    const { query, user = "batch-test-user", baseUrl, apiKey } = await request.json();
    console.log("收到的查询:", query, "API配置:", baseUrl);
    
    if (!baseUrl || !apiKey) {
      return NextResponse.json(
        { error: '缺少API配置信息', success: false },
        { status: 400 }
      );
    }
    
    // 调用指定的Dify API
    const chatUrl = `${baseUrl}/chat-messages`;
    console.log(`调用自定义Dify API: ${chatUrl}`);
    
    const requestBody = {
      inputs: {},
      query: query,
      response_mode: "streaming",
      user: user,
      auto_generate_name: false
    };
    
    const apiResponse = await fetch(chatUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    if (!apiResponse.ok) {
      const errorText = await apiResponse.text();
      console.error(`自定义Dify API响应错误: ${apiResponse.status}`, errorText);
      throw new Error(`API响应错误: ${apiResponse.status}`);
    }

    // 创建转换流来处理Dify的响应
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    
    const processStream = async () => {
      if (!apiResponse.body) {
        writer.close();
        return;
      }
      
      const reader = apiResponse.body.getReader();
      const decoder = new TextDecoder();
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          const chunk = decoder.decode(value, { stream: true });
          //console.log("收到自定义Dify API数据块:", chunk.substring(0, 100) + "...");
          
          // 直接转发Dify的SSE格式数据
          writer.write(new TextEncoder().encode(chunk));
        }
      } catch (e) {
        console.error("自定义API流处理错误:", e);
      } finally {
        writer.close();
      }
    };
    
    processStream();
    
    return new Response(readable, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
    
  } catch (error) {
    console.error('批量测试Dify API错误:', error);
    return NextResponse.json(
      { error: '服务器处理请求时出错', success: false },
      { status: 500 }
    );
  }
} 