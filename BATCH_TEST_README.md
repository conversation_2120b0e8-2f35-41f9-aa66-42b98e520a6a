# 批量测试工具使用说明

## 功能概述

批量测试工具是一个用于对AI模型进行大批量问答测试和准确性评估的工具。它可以：

1. 上传Excel/CSV文件进行批量测试
2. 手动添加单个测试用例
3. 自动调用AI模型获取回答
4. 使用另一个AI模型判定回答准确性
5. 提供详细的测试进度和结果统计
6. 支持暂停、继续、重置等操作
7. 导出测试结果

## 访问地址

```
http://localhost:3000/agent/batch-test
```

## 使用步骤

### 1. 准备测试数据

#### 方法一：使用CSV文件
1. 点击"下载模板"按钮下载CSV模板文件
2. 按照以下格式填写数据：

```csv
问题,答案,测试场景说明,测试地址,测试key,模型测试结果,AI结果判定
"你的问题1","期望的答案1","测试场景描述1","http://************:8081/v1","app-hXBah6VG2H7FcwuHQ6iSiI4A","",""
"你的问题2","期望的答案2","测试场景描述2","http://************:8081/v1","app-hXBah6VG2H7FcwuHQ6iSiI4A","",""
```

#### 方法二：手动添加
1. 点击"手动添加"按钮
2. 填写问题、答案、测试场景说明、测试地址、测试key等信息
3. 点击"添加"按钮

### 2. 配置测试参数

在左侧配置面板中设置：

- **请求间隔(ms)**: 两次API调用之间的间隔时间，避免频率限制

**AI判定配置**:
- 启用AI结果判定：开启后会自动比较标准答案和模型回答的语义一致性
- 判定API地址：用于判定的AI服务地址
- 判定API密钥：对应的API密钥

### 3. 开始测试

1. 点击"开始测试"按钮
2. 系统会依次处理每个测试用例：
   - 调用目标AI模型获取回答
   - 将回答结果写入"模型测试结果"字段
   - 如果启用了AI判定，会调用判定API进行语义对比
   - 更新测试状态和判定结果

### 4. 监控进度

在左侧进度面板中可以看到：
- 总体进度百分比
- 已完成、错误、正确率等统计信息
- 当前执行的测试用例编号和问题内容
- 平均响应时间和总Token使用量
- 实时性能指标

### 5. 查看结果

在右侧结果面板中：
- 查看每个测试用例的详细信息
- 使用筛选器按状态或判定结果过滤显示
- 删除不需要的测试用例（仅在测试停止时）
- 查看性能指标（响应时间、Token使用量、会话ID等）
- 实时显示测试结果和错误信息

### 6. 控制测试流程

- **暂停**: 暂停当前测试，可以稍后继续
- **继续**: 从暂停位置继续执行
- **停止**: 完全停止测试并重置进度
- **重置**: 清除所有测试结果，重新开始

### 7. 导出结果

点击"导出结果"按钮，下载包含测试数据和结果的Excel文件。导出的文件只包含7个核心列，去除了调试信息。

## 文件格式说明

### 输入CSV格式
```
问题,答案,测试场景说明,测试地址,测试key,模型测试结果,AI结果判定
```

**字段说明**：
- `问题`: 要测试的问题内容
- `答案`: 期望的标准答案
- `测试场景说明`: 对测试场景的描述
- `测试地址`: 被测试AI模型的API地址
- `测试key`: 被测试AI模型的API密钥
- `模型测试结果`: 模型回答结果（程序会自动填写，输入时留空）
- `AI结果判定`: AI判定结果（暂时不实现，输入时留空）

### 输出Excel格式

导出的Excel文件包含以下7列：

| 列名 | 说明 |
|------|------|
| 问题 | 测试的问题内容 |
| 答案 | 期望的标准答案 |
| 测试场景说明 | 测试场景描述 |
| 测试地址 | API地址 |
| 测试key | API密钥 |
| **模型测试结果** | **AI模型的实际回答（程序自动填写）** |
| AI结果判定 | 判定结果（"正确"、"错误"、"部分正确"等） |

## 注意事项

1. **API密钥安全**: 请妥善保管API密钥，不要在公共场所展示
2. **请求频率**: 建议设置合理的请求间隔，避免触发API频率限制
3. **文件编码**: CSV文件请使用UTF-8编码，确保中文正确显示
4. **错误处理**: 如果某个测试用例失败，系统会继续执行后续用例
5. **数据备份**: 建议定期导出测试结果进行备份

## 常见问题

### Q: 为什么有些测试用例显示错误？
A: 可能的原因包括：
- API地址不正确
- API密钥无效或过期
- 网络连接问题
- API服务暂时不可用

### Q: 如何提高测试准确性？
A: 建议：
- 编写清晰、具体的问题
- 提供准确、完整的标准答案
- 使用合适的判定AI模型
- 设置合理的请求间隔

### Q: 可以同时运行多个测试吗？
A: 当前版本不支持并发测试，建议按顺序执行以确保稳定性。

### Q: 如何处理Excel文件？
A: 当前版本完全支持Excel文件解析。程序会自动查找包含"问题"、"答案"、"测试场景说明"、"测试地址"、"测试key"等列的Excel文件，并读取实际数据。

### Q: 模型测试结果为什么没有显示思考过程？
A: 程序会自动过滤掉`<think>...</think>`标签内的内容，只保留最终的正式回答结果。

### Q: AI判定功能如何工作？
A: AI判定功能使用专门的提示词来比较标准答案和模型回答的语义一致性。它会：
- 重点关注语义一致性，而不是字面完全相同
- 允许不同的表达方式，只要核心意思正确
- 返回"正确"、"错误"、"部分正确"等判定结果

### Q: 如何配置AI判定功能？
A: 在测试配置区域：
1. 勾选"启用AI结果判定"
2. 填写判定API地址和密钥
3. 开始测试时系统会自动调用判定API

## 技术支持

如遇到问题，请检查：
1. 浏览器控制台的错误信息
2. API服务的可用性
3. 网络连接状态
4. 文件格式的正确性 