unit CC_YinWinFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  CC_Ccgk_XjllReportConFrm,CC_SdfReportFrm;

type
  TCC_YinWinForm = class(TForm)
    RzPanel1: TRzPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_Ccgk_XjllReportConFrame: TCC_Ccgk_XjllReportConFrame;
  end;

var
  CC_YinWinForm: TCC_YinWinForm;

implementation

{$R *.dfm}

procedure TCC_YinWinForm.FormShow(Sender: TObject);
begin
  if (FCC_Ccgk_XjllReportConFrame <> nil) then
  begin
    FCC_Ccgk_XjllReportConFrame.Free;
    FCC_Ccgk_XjllReportConFrame := nil;
  end;

  if (FCC_Ccgk_XjllReportConFrame = nil) then
  begin
    FCC_Ccgk_XjllReportConFrame := TCC_Ccgk_XjllReportConFrame.Create(self);
    FCC_Ccgk_XjllReportConFrame.Parent := self.RzPanel1;
    FCC_Ccgk_XjllReportConFrame.Align := alClient;
    FCC_Ccgk_XjllReportConFrame.Init(1, '',2);
  end;
end;

end.
