<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宁波市研发人员区域联动分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">宁波市研发人员区域联动分析</h1>
            <p class="text-gray-600">基于宁波市12个区（县、市）的研发人员分布热力与多维属性分析，支持区域联动与人才清册钻取</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        筛选条件
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部领域</option>
                                <option value="ai">人工智能</option>
                                <option value="material">新材料</option>
                                <option value="energy">新能源</option>
                                <option value="bio">生物医药</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">行业类别</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部行业</option>
                                <option value="manufacture">制造业</option>
                                <option value="it">信息技术</option>
                                <option value="finance">金融业</option>
                                <option value="education">教育</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                            <div class="flex space-x-4">
                                <label class="inline-flex items-center">
                                    <input type="radio" name="gender" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">全部</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="gender" value="male" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">男</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="radio" name="gender" value="female" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">女</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">年龄区间</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="20-30">20-30岁</option>
                                <option value="30-40">30-40岁</option>
                                <option value="40-50">40-50岁</option>
                                <option value="50+">50岁以上</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">学历层次</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="doctor">博士</option>
                                <option value="master">硕士</option>
                                <option value="bachelor">本科</option>
                                <option value="college">大专及以下</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">职称等级</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="senior">高级职称</option>
                                <option value="middle">中级职称</option>
                                <option value="junior">初级职称</option>
                                <option value="none">无职称</option>
                            </select>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 地图热力区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        宁波市研发人员热力分布图
                    </h2>
                    <div class="relative">
                        <!-- 地图占位图 -->
                        <svg class="w-full h-96 bg-gray-100 rounded-md" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
                            <!-- 宁波市地图轮廓简化版 -->
                            <path d="M200,100 L300,150 L400,120 L500,180 L600,150 L700,200 L650,300 L600,250 L550,300 L500,350 L400,400 L300,350 L250,400 L200,350 L150,300 Z" fill="#3b82f6" fill-opacity="0.3" stroke="#1e40af" stroke-width="2" class="hover:fill-opacity-50 cursor-pointer" onclick="selectRegion('haishu')"/>
                            <text x="250" y="250" font-size="14" fill="#1e40af">海曙区</text>
                            
                            <path d="M300,150 L350,200 L400,180 L450,220 L500,200 L550,250 L500,300 L450,250 L400,300 L350,250 L300,300 L250,250 Z" fill="#3b82f6" fill-opacity="0.5" stroke="#1e40af" stroke-width="2" class="hover:fill-opacity-70 cursor-pointer" onclick="selectRegion('jiangbei')"/>
                            <text x="400" y="250" font-size="14" fill="#1e40af">江北区</text>
                            
                            <path d="M500,180 L550,220 L600,200 L650,250 L700,220 L750,270 L700,350 L650,300 L600,350 L550,300 L500,350 L450,300 Z" fill="#3b82f6" fill-opacity="0.7" stroke="#1e40af" stroke-width="2" class="hover:fill-opacity-90 cursor-pointer" onclick="selectRegion('yinzhou')"/>
                            <text x="600" y="300" font-size="14" fill="#1e40af">鄞州区</text>
                            
                            <!-- 其他区域省略... -->
                        </svg>
                        <div class="absolute bottom-4 right-4 bg-white p-2 rounded-md shadow-sm">
                            <div class="flex items-center">
                                <span class="text-xs mr-2">热力值:</span>
                                <div class="flex">
                                    <div class="w-6 h-4 bg-blue-100 mr-1"></div>
                                    <div class="w-6 h-4 bg-blue-300 mr-1"></div>
                                    <div class="w-6 h-4 bg-blue-500 mr-1"></div>
                                    <div class="w-6 h-4 bg-blue-700"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 多维属性分析区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        研发人员多维属性分析
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 技术领域词云 -->
                        <div class="bg-gray-50 p-4 rounded-md">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">技术领域分布</h3>
                            <div class="flex flex-wrap gap-2 justify-center">
                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">人工智能</span>
                                <span class="px-3 py-1 bg-blue-200 text-blue-800 rounded-full text-sm">新材料</span>
                                <span class="px-3 py-1 bg-blue-300 text-blue-800 rounded-full text-sm">新能源</span>
                                <span class="px-3 py-1 bg-blue-400 text-blue-800 rounded-full text-sm">生物医药</span>
                                <span class="px-3 py-1 bg-blue-500 text-white rounded-full text-sm">智能制造</span>
                                <span class="px-3 py-1 bg-blue-600 text-white rounded-full text-sm">集成电路</span>
                            </div>
                        </div>
                        
                        <!-- 行业分布饼图 -->
                        <div class="bg-gray-50 p-4 rounded-md">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">行业分布</h3>
                            <div class="flex items-center justify-center">
                                <svg class="w-40 h-40" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="40" fill="#3b82f6" stroke="white" stroke-width="2"/>
                                    <circle cx="50" cy="50" r="30" fill="#60a5fa" stroke="white" stroke-width="2"/>
                                    <circle cx="50" cy="50" r="20" fill="#93c5fd" stroke="white" stroke-width="2"/>
                                    <circle cx="50" cy="50" r="10" fill="#bfdbfe" stroke="white" stroke-width="2"/>
                                </svg>
                                <div class="ml-4">
                                    <div class="flex items-center mb-1">
                                        <div class="w-3 h-3 bg-blue-600 mr-1"></div>
                                        <span class="text-xs">制造业 45%</span>
                                    </div>
                                    <div class="flex items-center mb-1">
                                        <div class="w-3 h-3 bg-blue-400 mr-1"></div>
                                        <span class="text-xs">信息技术 30%</span>
                                    </div>
                                    <div class="flex items-center mb-1">
                                        <div class="w-3 h-3 bg-blue-200 mr-1"></div>
                                        <span class="text-xs">金融业 15%</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-100 mr-1"></div>
                                        <span class="text-xs">其他 10%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 性别比例 -->
                        <div class="bg-gray-50 p-4 rounded-md">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">性别比例</h3>
                            <div class="flex items-center justify-center">
                                <svg class="w-32 h-32" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="40" fill="#93c5fd"/>
                                    <path d="M50,50 L50,10 A40,40 0 0,1 90,50 Z" fill="#3b82f6"/>
                                </svg>
                                <div class="ml-4">
                                    <div class="flex items-center mb-1">
                                        <div class="w-3 h-3 bg-blue-600 mr-1"></div>
                                        <span class="text-xs">男性 65%</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-3 h-3 bg-blue-300 mr-1"></div>
                                        <span class="text-xs">女性 35%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 学历层次 -->
                        <div class="bg-gray-50 p-4 rounded-md">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">学历层次</h3>
                            <div class="h-40">
                                <div class="flex items-end h-full space-x-2">
                                    <div class="w-8 bg-blue-600" style="height: 30%;">
                                        <span class="text-xs text-white text-center block">大专</span>
                                    </div>
                                    <div class="w-8 bg-blue-500" style="height: 50%;">
                                        <span class="text-xs text-white text-center block">本科</span>
                                    </div>
                                    <div class="w-8 bg-blue-400" style="height: 70%;">
                                        <span class="text-xs text-white text-center block">硕士</span>
                                    </div>
                                    <div class="w-8 bg-blue-300" style="height: 90%;">
                                        <span class="text-xs text-white text-center block">博士</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与操作区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 区域统计总览 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        区域统计总览
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">全市研发人员总数</div>
                                <div class="text-2xl font-bold text-blue-600">12,568</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">高层次人才数量</div>
                                <div class="text-2xl font-bold text-green-600">1,245</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近一月新增</div>
                                <div class="text-2xl font-bold text-yellow-600">156</div>
                            </div>
                        </div>
                    </div>
                    
                    <h3 class="text-sm font-medium text-gray-700 mt-6 mb-3">各区县分布</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">鄞州区</span>
                            <span class="text-sm font-medium text-gray-900">3,245</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">海曙区</span>
                            <span class="text-sm font-medium text-gray-900">2,156</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">江北区</span>
                            <span class="text-sm font-medium text-gray-900">1,892</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">北仑区</span>
                            <span class="text-sm font-medium text-gray-900">1,567</span>
                        </div>
                        <button class="text-blue-600 hover:text-blue-800 text-xs font-medium mt-2">
                            查看更多区县 →
                        </button>
                    </div>
                </div>

                <!-- 高层次人才分类 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        高层次人才分类
                    </h2>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            <span class="text-sm text-gray-700">国家级人才</span>
                            <span class="ml-auto text-xs text-gray-500">328人</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            <span class="text-sm text-gray-700">省级人才</span>
                            <span class="ml-auto text-xs text-gray-500">456人</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            <span class="text-sm text-gray-700">市级人才</span>
                            <span class="ml-auto text-xs text-gray-500">461人</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            <span class="text-sm text-gray-700">区级人才</span>
                            <span class="ml-auto text-xs text-gray-500">1,024人</span>
                        </label>
                    </div>
                </div>

                <!-- 操作区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        数据操作
                    </h2>
                    <div class="space-y-3">
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            导出当前数据
                        </button>
                        <button class="w-full px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            下载人才清册
                        </button>
                        <button onclick="openDetailModal()" class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 人才详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">人才详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 基本信息 -->
                        <div class="md:col-span-1">
                            <div class="flex flex-col items-center">
                                <div class="w-24 h-24 bg-gray-200 rounded-full mb-4 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900">张伟</h4>
                                <p class="text-sm text-gray-500">宁波市智能制造研究院</p>
                            </div>
                            
                            <div class="mt-6 space-y-3">
                                <div>
                                    <p class="text-sm text-gray-500">性别</p>
                                    <p class="text-sm text-gray-900">男</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">年龄</p>
                                    <p class="text-sm text-gray-900">38岁</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">学历</p>
                                    <p class="text-sm text-gray-900">博士</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">职称</p>
                                    <p class="text-sm text-gray-900">高级工程师</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 专业信息 -->
                        <div class="md:col-span-2">
                            <h4 class="text-lg font-semibold text-gray-900 mb-4">专业信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-500">技术领域</p>
                                    <p class="text-sm text-gray-900">智能制造、人工智能</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">所属行业</p>
                                    <p class="text-sm text-gray-900">制造业</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">工作单位</p>
                                    <p class="text-sm text-gray-900">宁波市智能制造研究院</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">所在区域</p>
                                    <p class="text-sm text-gray-900">鄞州区</p>
                                </div>
                                <div class="md:col-span-2">
                                    <p class="text-sm text-gray-500">研究方向</p>
                                    <p class="text-sm text-gray-900">工业机器人、智能控制系统、智能制造系统集成</p>
                                </div>
                            </div>
                            
                            <h4 class="text-lg font-semibold text-gray-900 mt-6 mb-4">人才项目</h4>
                            <div class="space-y-3">
                                <div class="border-l-4 border-blue-500 pl-3 py-1">
                                    <p class="text-sm font-medium text-gray-900">宁波市智能制造重点研发计划</p>
                                    <p class="text-xs text-gray-500">2022-2024 | 项目负责人</p>
                                </div>
                                <div class="border-l-4 border-blue-300 pl-3 py-1">
                                    <p class="text-sm font-medium text-gray-900">浙江省重大科技专项</p>
                                    <p class="text-xs text-gray-500">2020-2022 | 技术负责人</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        导出个人档案
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 区域选择
        function selectRegion(region) {
            console.log('选择区域:', region);
            // 这里可以添加更新统计数据的逻辑
        }
        
        // 人才详情弹窗
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
    </script>
</body>
</html>