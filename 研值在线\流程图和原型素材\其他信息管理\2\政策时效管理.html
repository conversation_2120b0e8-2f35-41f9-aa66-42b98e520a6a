<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策时效管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">政策时效管理</h1>
            <p class="text-gray-600">通过集中设置生效与失效时间、自动状态流转与到期预警，确保各业务模块在引用政策条目时始终基于最新且合法的时效状态</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 条件筛选区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-start mb-4">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            条件筛选
                        </h2>
                        <!-- 到期预警区 -->
                        <div class="flex space-x-4">
                            <div class="bg-yellow-50 px-4 py-2 rounded-lg border border-yellow-200">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                    <a href="#" class="text-sm font-medium text-yellow-700 hover:text-yellow-800">
                                        30天内即将失效: <span class="font-bold">12</span>
                                    </a>
                                </div>
                            </div>
                            <div class="bg-red-50 px-4 py-2 rounded-lg border border-red-200">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <a href="#" class="text-sm font-medium text-red-700 hover:text-red-800">
                                        已失效但被引用: <span class="font-bold">5</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">政策名称</label>
                            <input type="text" placeholder="输入政策名称关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">发文文号</label>
                            <input type="text" placeholder="输入发文文号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                            <div class="flex items-center space-x-4 mt-2">
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">未生效</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">生效中</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已失效</span>
                                </label>
                            </div>
                        </div>
                        <div class="lg:col-span-2">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">生效时间</label>
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-2 space-y-2 sm:space-y-0">
                                        <div class="w-full sm:w-5/12">
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <span class="text-gray-500 text-center">至</span>
                                        <div class="w-full sm:w-5/12">
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">失效时间</label>
                                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-2 space-y-2 sm:space-y-0">
                                        <div class="w-full sm:w-5/12">
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                        <span class="text-gray-500 text-center">至</span>
                                        <div class="w-full sm:w-5/12">
                                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-3 mt-4">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            查询
                        </button>
                        <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            重置
                        </button>
                    </div>
                </div>

                <!-- 时效列表区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                政策时效列表
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="openBatchModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                                    </svg>
                                    批量设置
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发文文号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失效时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余有效天数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">关于促进科技创新的若干政策措施</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">国发〔2023〕15号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-05-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-04-30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        生效中
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">485</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openValidityModal('policy1')" class="text-blue-600 hover:text-blue-900">设置时效</button>
                                            <button onclick="extendValidity('policy1')" class="text-blue-600 hover:text-blue-900">延长有效期</button>
                                            <button onclick="invalidatePolicy('policy1')" class="text-blue-600 hover:text-blue-900">失效处理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">关于加强知识产权保护的实施意见</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">国办发〔2023〕28号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        即将失效
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openValidityModal('policy2')" class="text-blue-600 hover:text-blue-900">设置时效</button>
                                            <button onclick="extendValidity('policy2')" class="text-blue-600 hover:text-blue-900">延长有效期</button>
                                            <button onclick="invalidatePolicy('policy2')" class="text-blue-600 hover:text-blue-900">失效处理</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">高新技术企业认定管理办法</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">国科发火〔2022〕11号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-01-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-30</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        已失效
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openValidityModal('policy3')" class="text-blue-600 hover:text-blue-900">设置时效</button>
                                            <button onclick="extendValidity('policy3')" class="text-blue-600 hover:text-blue-900">延长有效期</button>
                                            <button onclick="viewReferences('policy3')" class="text-blue-600 hover:text-blue-900">查看引用</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">科技型中小企业评价办法</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">国科发政〔2023〕52号</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-01</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2026-12-31</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        未生效
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="openValidityModal('policy4')" class="text-blue-600 hover:text-blue-900">设置时效</button>
                                            <button onclick="activatePolicy('policy4')" class="text-blue-600 hover:text-blue-900">立即生效</button>
                                            <button onclick="invalidatePolicy('policy4')" class="text-blue-600 hover:text-blue-900">取消发布</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">56</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 统计信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        时效统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">生效中政策</div>
                                <div class="text-2xl font-bold text-green-600">42</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">即将失效政策</div>
                                <div class="text-2xl font-bold text-yellow-600">12</div>
                            </div>
                        </div>
                        <div class="bg-red-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">已失效政策</div>
                                <div class="text-2xl font-bold text-red-600">8</div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">未生效政策</div>
                                <div class="text-2xl font-bold text-purple-600">6</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量维护区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between mb-2">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                批量维护
                            </h2>
                            <button onclick="toggleImportPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                <span id="importPanelToggle">展开</span>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500">支持Excel批量导入政策时效信息</p>
                    </div>
                    <div id="importPanel" class="hidden">
                        <div class="p-6 space-y-4">
                            <div class="flex items-center justify-between">
                                <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                    </svg>
                                    下载导入模板
                                </a>
                                <span class="text-xs text-gray-500">支持.xlsx格式</span>
                            </div>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                            </div>
                            <div id="uploadProgress" class="hidden">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700">上传进度</span>
                                    <span class="text-sm font-medium text-gray-700">75%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                            <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm font-medium text-yellow-800">发现3条数据存在问题</p>
                                        <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                            <li>第2行：失效时间早于生效时间</li>
                                            <li>第5行：发文文号不存在</li>
                                            <li>第8行：生效时间为必填项</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-end">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 时效变更历史 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        近期变更历史
                    </h2>
                    <div class="space-y-4 mt-4">
                        <div class="border-l-4 border-blue-500 pl-4 py-2">
                            <div class="text-sm font-medium text-gray-900">关于加强知识产权保护的实施意见</div>
                            <div class="text-xs text-gray-500 mt-1">延长有效期：2023-12-31 → 2024-06-30</div>
                            <div class="text-xs text-gray-500 mt-1">操作人：张三 | 2023-12-05 14:30</div>
                        </div>
                        <div class="border-l-4 border-red-500 pl-4 py-2">
                            <div class="text-sm font-medium text-gray-900">高新技术企业认定管理办法</div>
                            <div class="text-xs text-gray-500 mt-1">状态变更：生效中 → 已失效</div>
                            <div class="text-xs text-gray-500 mt-1">操作人：系统自动 | 2023-11-30 00:00</div>
                        </div>
                        <div class="border-l-4 border-green-500 pl-4 py-2">
                            <div class="text-sm font-medium text-gray-900">科技型中小企业评价办法</div>
                            <div class="text-xs text-gray-500 mt-1">新增时效设置：2024-01-01 至 2026-12-31</div>
                            <div class="text-xs text-gray-500 mt-1">操作人：李四 | 2023-11-28 10:15</div>
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800">查看更多历史记录</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 时效编辑弹窗 -->
    <div id="validityModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">设置政策时效</h3>
                    <button onclick="closeValidityModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div>
                            <div class="text-sm font-medium text-gray-700 mb-1">政策名称</div>
                            <div class="text-base font-semibold text-gray-900">关于促进科技创新的若干政策措施</div>
                            <div class="text-sm text-gray-500 mt-1">国发〔2023〕15号</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">生效时间 <span class="text-red-500">*</span></label>
                            <input type="date" value="2023-05-01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">失效时间 <span class="text-red-500">*</span></label>
                            <input type="date" value="2025-04-30" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="mt-1 text-xs text-gray-500">失效时间必须晚于生效时间</p>
                        </div>
                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                <span class="ml-2 text-sm text-gray-700">启用自动状态更新</span>
                            </label>
                            <p class="mt-1 text-xs text-gray-500 ml-6">系统将根据时间自动更新政策状态</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">变更说明</label>
                            <textarea rows="3" placeholder="请输入时效变更的原因或说明" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeValidityModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量设置弹窗 -->
    <div id="batchModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-lg w-full overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">批量设置政策时效</h3>
                    <button onclick="closeBatchModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择操作类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="extend">批量延长有效期</option>
                                <option value="set">批量设置新时效</option>
                                <option value="invalidate">批量失效处理</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选择政策范围</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="policyScope" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">当前筛选结果的所有政策</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="policyScope" value="selected" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">手动选择政策</span>
                                </label>
                            </div>
                        </div>
                        <div id="policySelector" class="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">关于促进科技创新的若干政策措施</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">关于加强知识产权保护的实施意见</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                                    <span class="ml-2 text-sm text-gray-700">高新技术企业认定管理办法</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">科技型中小企业评价办法</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">延长时间（月）</label>
                            <input type="number" min="1" value="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">变更说明</label>
                            <textarea rows="3" placeholder="请输入批量时效变更的原因或说明" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeBatchModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        确认设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 批量导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('importPanel');
            const toggle = document.getElementById('importPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 时效编辑弹窗
        function openValidityModal(policyId) {
            document.getElementById('validityModal').classList.remove('hidden');
            console.log('打开政策时效编辑:', policyId);
        }
        
        function closeValidityModal() {
            document.getElementById('validityModal').classList.add('hidden');
        }
        
        // 批量设置弹窗
        function openBatchModal() {
            document.getElementById('batchModal').classList.remove('hidden');
        }
        
        function closeBatchModal() {
            document.getElementById('batchModal').classList.add('hidden');
        }
        
        // 延长有效期
        function extendValidity(policyId) {
            openValidityModal(policyId);
            console.log('延长政策有效期:', policyId);
        }
        
        // 失效处理
        function invalidatePolicy(policyId) {
            if (confirm('确定要将该政策标记为失效吗？')) {
                console.log('失效处理政策:', policyId);
            }
        }
        
        // 立即生效
        function activatePolicy(policyId) {
            if (confirm('确定要将该政策立即生效吗？')) {
                console.log('立即生效政策:', policyId);
            }
        }
        
        // 查看引用
        function viewReferences(policyId) {
            console.log('查看政策引用:', policyId);
            alert('该政策被3个业务模块引用，请先处理引用关系再进行操作。');
        }
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('validityModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeValidityModal();
            }
        });
        
        document.getElementById('batchModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBatchModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (exportDropdown && exportOptions && !exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>