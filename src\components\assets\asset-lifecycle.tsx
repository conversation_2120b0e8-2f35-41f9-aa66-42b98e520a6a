'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Clock, AlertTriangle, CheckCircle, XCircle, RotateCcw } from "lucide-react"

export function AssetLifecycle() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>资产生命周期分析</CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline">更新时间: 2024-03-21</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 生命周期阶段统计 */}
          <div className="grid grid-cols-4 gap-4">
            <div className="p-4 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="font-medium">正常运行</span>
              </div>
              <div className="text-2xl font-semibold text-green-600">1,245</div>
              <div className="text-sm text-gray-500 mt-1">占比 68.5%</div>
            </div>
            <div className="p-4 bg-yellow-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-5 w-5 text-yellow-500" />
                <span className="font-medium">即将过保</span>
              </div>
              <div className="text-2xl font-semibold text-yellow-600">286</div>
              <div className="text-sm text-gray-500 mt-1">占比 15.7%</div>
            </div>
            <div className="p-4 bg-red-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <span className="font-medium">已过保</span>
              </div>
              <div className="text-2xl font-semibold text-red-600">178</div>
              <div className="text-sm text-gray-500 mt-1">占比 9.8%</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <XCircle className="h-5 w-5 text-gray-500" />
                <span className="font-medium">待报废</span>
              </div>
              <div className="text-2xl font-semibold text-gray-600">109</div>
              <div className="text-sm text-gray-500 mt-1">占比 6.0%</div>
            </div>
          </div>

          {/* 近期变更记录 */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">近期变更记录</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <RotateCcw className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="font-medium">核心交换机升级</div>
                    <div className="text-sm text-gray-500">设备编号: NET-2024-001</div>
                  </div>
                </div>
                <div className="text-sm text-gray-500">2024-03-20</div>
              </div>
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <RotateCcw className="h-4 w-4 text-blue-500" />
                  <div>
                    <div className="font-medium">存储阵列扩容</div>
                    <div className="text-sm text-gray-500">设备编号: STO-2024-003</div>
                  </div>
                </div>
                <div className="text-sm text-gray-500">2024-03-18</div>
              </div>
            </div>
          </div>

          {/* 生命周期预警 */}
          <div>
            <h3 className="text-sm font-medium text-gray-500 mb-3">生命周期预警</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <div>
                    <div className="font-medium">防火墙设备即将过保</div>
                    <div className="text-sm text-gray-500">剩余 30 天</div>
                  </div>
                </div>
                <Badge className="bg-yellow-50 text-yellow-600">需关注</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <div>
                    <div className="font-medium">服务器设备已超期服役</div>
                    <div className="text-sm text-gray-500">超期 45 天</div>
                  </div>
                </div>
                <Badge className="bg-red-50 text-red-600">紧急处理</Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 