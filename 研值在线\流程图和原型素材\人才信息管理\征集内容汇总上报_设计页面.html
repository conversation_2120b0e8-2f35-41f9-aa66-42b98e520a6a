<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>农业农村技术服务需求征集汇总上报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6',
                        light: '#f3f4f6',
                        dark: '#1f2937'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">农业农村技术服务需求征集汇总上报</h1>
                <p class="text-gray-600 mt-1">标准化采集与管理农业技术服务需求，实现高效流转与精准对接</p>
            </div>
            <div class="flex space-x-3">
                <button onclick="openModal('helpModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    使用帮助
                </button>
                <button onclick="openModal('newDemandModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增需求
                </button>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 左侧主内容区 -->
            <div class="flex-1">
                <!-- 需求填报表单卡片 -->
                <div class="bg-white shadow-md rounded-lg p-6 mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        需求填报表单
                    </h2>
                    
                    <form id="demandForm" class="space-y-5">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                            <div>
                                <label for="demandName" class="block text-sm font-medium text-gray-700 mb-1">需求名称 <span class="text-red-500">*</span></label>
                                <input type="text" id="demandName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入需求名称">
                            </div>
                            <div>
                                <label for="serviceType" class="block text-sm font-medium text-gray-700 mb-1">服务类型 <span class="text-red-500">*</span></label>
                                <select id="serviceType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择服务类型</option>
                                    <option value="plant">种植业技术</option>
                                    <option value="animal">养殖业技术</option>
                                    <option value="machinery">农业机械</option>
                                    <option value="agronomy">农艺技术</option>
                                    <option value="processing">农产品加工</option>
                                    <option value="other">其他服务</option>
                                </select>
                            </div>
                            <div>
                                <label for="region" class="block text-sm font-medium text-gray-700 mb-1">所属区域 <span class="text-red-500">*</span></label>
                                <div class="grid grid-cols-2 gap-3">
                                    <select id="city" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="ningbo">宁波市</option>
                                    </select>
                                    <select id="district" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        <option value="">请选择区县</option>
                                        <option value="haishu">海曙区</option>
                                        <option value="jiangdong">江东区</option>
                                        <option value="jiangbei">江北区</option>
                                        <option value="yinzhou">鄞州区</option>
                                        <option value="beilun">北仑区</option>
                                        <option value="zhenhai">镇海区</option>
                                        <option value="yuyao">余姚市</option>
                                        <option value="cixi">慈溪市</option>
                                        <option value="fenghua">奉化区</option>
                                    </select>
                                </div>
                            </div>
                            <div>
                                <label for="expectDate" class="block text-sm font-medium text-gray-700 mb-1">期望解决时间 <span class="text-red-500">*</span></label>
                                <input type="date" id="expectDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        
                        <div>
                            <label for="contactPerson" class="block text-sm font-medium text-gray-700 mb-1">联系人 <span class="text-red-500">*</span></label>
                            <input type="text" id="contactPerson" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入联系人姓名">
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                            <div>
                                <label for="contactPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话 <span class="text-red-500">*</span></label>
                                <input type="tel" id="contactPhone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入联系电话">
                            </div>
                            <div>
                                <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-1">电子邮箱</label>
                                <input type="email" id="contactEmail" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入电子邮箱">
                            </div>
                        </div>
                        
                        <div>
                            <label for="demandDescription" class="block text-sm font-medium text-gray-700 mb-1">需求详细描述 <span class="text-red-500">*</span></label>
                            <textarea id="demandDescription" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述需求背景、现状、存在问题及期望解决的技术方向等内容"></textarea>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">需求附件（可选）</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="cursor-pointer">
                                        <span class="text-sm font-medium text-blue-600 hover:text-blue-800">点击上传文件</span>
                                        <span class="mt-1 block text-xs text-gray-500">或拖拽文件到此处</span>
                                    </label>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png">
                                </div>
                                <p class="mt-2 text-xs text-gray-500">支持PDF、Word、Excel、图片格式，单个文件不超过10MB</p>
                            </div>
                            <div id="uploadedFiles" class="mt-3 hidden">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                    <div class="flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                        </svg>
                                        <span class="text-sm text-gray-700">需求现场照片.jpg</span>
                                    </div>
                                    <button class="text-red-500 hover:text-red-700">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex justify-between pt-4 border-t border-gray-200">
                            <div class="text-sm text-gray-500">
                                <span class="text-red-500">*</span> 为必填项
                            </div>
                            <div class="flex space-x-3">
                                <button type="button" onclick="saveDraft()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    保存草稿
                                </button>
                                <button type="button" onclick="openModal('submitConfirmModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    提交上报
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
                
                <!-- 上报历史与归档区 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                </svg>
                                上报历史与归档
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative">
                                    <input type="text" placeholder="搜索需求..." class="pl-8 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="absolute left-3 top-2.5 h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <select class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="all">全部状态</option>
                                    <option value="pending">待处理</option>
                                    <option value="processing">处理中</option>
                                    <option value="completed">已完成</option>
                                    <option value="rejected">已退回</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">需求名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属区域</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上报时间</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">水稻病虫害绿色防控技术需求</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">种植业技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市鄞州区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-12</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDemand('1')" class="text-blue-600 hover:text-blue-900">查看</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">智能化养猪场环境控制技术</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">养殖业技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市余姚市</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-08</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">处理中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDemand('2')" class="text-blue-600 hover:text-blue-900">查看</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">茶叶深加工技术与设备引进</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农产品加工</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市海曙区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-05</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">待处理</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDemand('3')" class="text-blue-600 hover:text-blue-900">查看</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">果蔬冷链物流保鲜技术</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">农艺技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市慈溪市</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-28</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">已退回</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDemand('4')" class="text-blue-600 hover:text-blue-900">查看</button>
                                        <button onclick="editDemand('4')" class="text-indigo-600 hover:text-indigo-900">重新编辑</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-4 条，共 26 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧辅助功能区 -->
            <div class="w-full lg:w-80 space-y-6">
                <!-- 草稿管理 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            草稿管理
                        </h3>
                    </div>
                    <div class="p-4">
                        <div class="space-y-3">
                            <div class="p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">草莓种植技术需求</h4>
                                        <p class="text-xs text-gray-500 mt-1">上次编辑: 2024-05-15 14:30</p>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button onclick="editDraft('1')" class="text-indigo-600 hover:text-indigo-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </button>
                                        <button onclick="deleteDraft('1')" class="text-red-600 hover:text-red-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900">水产养殖水质监测系统</h4>
                                        <p class="text-xs text-gray-500 mt-1">上次编辑: 2024-05-14 09:15</p>
                                    </div>
                                    <div class="flex space-x-1">
                                        <button onclick="editDraft('2')" class="text-indigo-600 hover:text-indigo-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </button>
                                        <button onclick="deleteDraft('2')" class="text-red-600 hover:text-red-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 text-center">
                            <button onclick="openModal('newDemandModal')" class="w-full py-2 border border-dashed border-gray-300 rounded-md text-sm text-gray-600 hover:bg-gray-50">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                                </svg>
                                创建新草稿
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 需求分类指南 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            需求分类指南
                        </h3>
                    </div>
                    <div class="p-4 text-sm">
                        <div class="space-y-3">
                            <div>
                                <h4 class="font-medium text-gray-900">种植业技术</h4>
                                <p class="text-gray-600 text-xs mt-1">包括粮食、蔬菜、水果、花卉等农作物的种植技术需求</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">养殖业技术</h4>
                                <p class="text-gray-600 text-xs mt-1">包括畜禽养殖、水产养殖等领域的技术需求</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">农业机械</h4>
                                <p class="text-gray-600 text-xs mt-1">涉及农业生产、加工、运输等环节的机械设备需求</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">农艺技术</h4>
                                <p class="text-gray-600 text-xs mt-1">包括耕作、育种、施肥、灌溉等农业生产技术需求</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900">农产品加工</h4>
                                <p class="text-gray-600 text-xs mt-1">涉及农产品保鲜、加工、包装、储存等技术需求</p>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-xs flex items-center">
                                <span>查看完整分类说明</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- 填报进度统计 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                            填报进度统计
                        </h3>
                    </div>
                    <div class="p-4">
                        <div class="space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-xs text-gray-600">本月已上报</span>
                                    <span class="text-xs font-medium text-gray-900">8 条</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 67%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-xs text-gray-600">草稿数量</span>
                                    <span class="text-xs font-medium text-gray-900">2 条</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-500 h-2 rounded-full" style="width: 17%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <span class="text-xs text-gray-600">已完成需求</span>
                                    <span class="text-xs font-medium text-gray-900">5 条</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-green-500 h-2 rounded-full" style="width: 42%"></div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex justify-between items-center text-sm">
                                <span class="text-gray-600">需求响应率</span>
                                <span class="font-medium text-green-600">92%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 提交确认弹窗 -->
    <div id="submitConfirmModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="text-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <h3 class="text-lg font-semibold text-gray-900 mt-2">确认提交需求</h3>
                        <p class="text-gray-600 mt-1">请选择需求归类标签，确认无误后提交上报</p>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">需求归类标签</label>
                        <div class="grid grid-cols-2 gap-2">
                            <label class="flex items-center p-2 border border-gray-300 rounded-md hover:border-blue-300 cursor-pointer">
                                <input type="radio" name="category" value="technical" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">技术难题</span>
                            </label>
                            <label class="flex items-center p-2 border border-gray-300 rounded-md hover:border-blue-300 cursor-pointer">
                                <input type="radio" name="category" value="equipment" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">设备需求</span>
                            </label>
                            <label class="flex items-center p-2 border border-gray-300 rounded-md hover:border-blue-300 cursor-pointer">
                                <input type="radio" name="category" value="training" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">培训指导</span>
                            </label>
                            <label class="flex items-center p-2 border border-gray-300 rounded-md hover:border-blue-300 cursor-pointer">
                                <input type="radio" name="category" value="policy" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">政策咨询</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label for="submitRemark" class="block text-sm font-medium text-gray-700 mb-1">备注说明（可选）</label>
                        <textarea id="submitRemark" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入补充说明或特殊要求"></textarea>
                    </div>
                </div>
                <div class="bg-gray-50 px-6 py-4 flex justify-end space-x-3 rounded-b-lg">
                    <button onclick="closeModal('submitConfirmModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        取消
                    </button>
                    <button onclick="submitDemand()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        确认提交
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 需求查看弹窗 -->
    <div id="viewDemandModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">需求详情</h3>
                    <button onclick="closeModal('viewDemandModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="md:col-span-2">
                            <div class="mb-6">
                                <h4 class="text-xl font-semibold text-gray-900" id="view-demand-name">水稻病虫害绿色防控技术需求</h4>
                                <div class="flex items-center mt-2 space-x-3">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                                    <span class="text-sm text-gray-500">上报时间: 2024-05-12</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-500">服务类型</h5>
                                    <p class="text-gray-900">种植业技术</p>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-gray-500">所属区域</h5>
                                    <p class="text-gray-900">宁波市鄞州区</p>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-gray-500">需求归类</h5>
                                    <p class="text-gray-900">技术难题</p>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-gray-500">期望解决时间</h5>
                                    <p class="text-gray-900">2024-06-30</p>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-500 mb-2">需求详细描述</h5>
                                <div class="p-4 bg-gray-50 rounded-lg">
                                    <p class="text-gray-900">我合作社种植水稻面积约200亩，近期发现稻飞虱和稻瘟病发生较为严重，传统化学防治方法效果不佳且影响生态环境。急需绿色防控技术指导，包括生物防治、物理防治等非化学手段的应用，以及抗病品种选择、科学栽培管理等综合防控措施。希望专家能实地考察并提供针对性解决方案，以减少农药使用量，提高稻米品质和安全性。</p>
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <h5 class="text-sm font-medium text-gray-500 mb-2">处理记录</h5>
                                <div class="space-y-4">
                                    <div class="p-4 bg-gray-50 rounded-lg">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <span class="font-medium text-gray-900">专家现场指导</span>
                                                <span class="text-xs text-gray-500 ml-2">2024-05-18</span>
                                            </div>
                                            <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">已解决</span>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">组织农业技术推广中心专家赴现场考察，针对病虫害情况制定了综合防控方案，包括：1. 释放天敌昆虫进行生物防治；2. 安装杀虫灯进行物理诱杀；3. 推荐使用抗病品种；4. 优化水肥管理措施。</p>
                                    </div>
                                    <div class="p-4 bg-gray-50 rounded-lg">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <span class="font-medium text-gray-900">需求受理</span>
                                                <span class="text-xs text-gray-500 ml-2">2024-05-13</span>
                                            </div>
                                            <span class="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded-full">处理中</span>
                                        </div>
                                        <p class="text-sm text-gray-700 mt-2">需求已受理，已分派至农业技术推广中心处理，预计3个工作日内安排专家现场查看。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h5 class="text-sm font-medium text-gray-500 mb-3">联系人信息</h5>
                                <div class="space-y-3">
                                    <div>
                                        <p class="text-xs text-gray-500">联系人</p>
                                        <p class="text-gray-900">王建国</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">联系电话</p>
                                        <p class="text-gray-900">138****5678</p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">电子邮箱</p>
                                        <p class="text-gray-900"><EMAIL></p>
                                    </div>
                                    <div>
                                        <p class="text-xs text-gray-500">所属单位</p>
                                        <p class="text-gray-900">宁波市鄞州区丰收水稻专业合作社</p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h5 class="text-sm font-medium text-gray-500 mb-3">相关附件</h5>
                                <div class="space-y-2">
                                    <a href="#" class="flex items-center p-2 bg-white rounded border border-gray-200 hover:border-blue-300">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                        </svg>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm text-gray-900 truncate">水稻病虫害发生情况照片.jpg</p>
                                            <p class="text-xs text-gray-500">2.4 MB</p>
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                        </svg>
                                    </a>
                                    <a href="#" class="flex items-center p-2 bg-white rounded border border-gray-200 hover:border-blue-300">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm text-gray-900 truncate">稻田种植情况说明.docx</p>
                                            <p class="text-xs text-gray-500">1.2 MB</p>
                                        </div>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h5 class="text-sm font-medium text-gray-500 mb-3">需求评价</h5>
                                <div class="flex text-yellow-400 mb-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-current" style="fill-opacity: 0.5" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                </div>
                                <p class="text-sm text-gray-500 mb-3">非常满意，专家提供的技术方案有效解决了我们的病虫害问题，农药使用量减少了60%。</p>
                                <div class="text-xs text-gray-500">评价时间: 2024-05-25</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助弹窗 -->
    <div id="helpModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen px-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">使用帮助</h3>
                    <button onclick="closeModal('helpModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-3">农业农村技术服务需求填报指南</h4>
                    <div class="space-y-4 text-sm">
                        <div>
                            <h5 class="font-medium text-gray-900 mb-1">一、需求填报流程</h5>
                            <ol class="list-decimal list-inside text-gray-700 space-y-1 pl-2">
                                <li>点击"新增需求"按钮，填写需求基本信息</li>
                                <li>填写完成后可选择"保存草稿"或"提交上报"</li>
                                <li>提交前需选择需求归类标签，确认后完成上报</li>
                                <li>上报后可在"上报历史"中查看处理进度</li>
                            </ol>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900 mb-1">二、填报注意事项</h5>
                            <ul class="list-disc list-inside text-gray-700 space-y-1 pl-2">
                                <li>带<span class="text-red-500">*</span>的为必填项，必须填写完整</li>
                                <li>需求描述应详细、准确，包括现状、问题和期望解决的技术方向</li>
                                <li>可上传相关图片、文档等附件，辅助说明需求情况</li>
                                <li>草稿可随时保存，保存后可在右侧"草稿管理"中继续编辑</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900 mb-1">三、需求分类说明</h5>
                            <ul class="list-disc list-inside text-gray-700 space-y-1 pl-2">
                                <li><strong>技术难题</strong>：生产中遇到的技术瓶颈和难题</li>
                                <li><strong>设备需求</strong>：需要引进或使用特定农业机械设备</li>
                                <li><strong>培训指导</strong>：需要技术培训或现场指导</li>
                                <li><strong>政策咨询</strong>：农业相关政策法规咨询</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium text-gray-900 mb-1">四、常见问题</h5>
                            <div class="space-y-2 text-gray-700">
                                <p><strong>Q: 如何修改已提交的需求？</strong></p>
                                <p>A: 已提交的需求在"待处理"状态下可联系管理员撤回修改；若已处理，可通过"重新编辑"功能提交补充说明。</p>
                                <p><strong>Q: 需求提交后多久会得到响应？</strong></p>
                                <p>A: 一般情况下，需求会在3个工作日内得到受理，7个工作日内安排专家对接。</p>
                                <p><strong>Q: 可以上传多大的附件？</strong></p>
                                <p>A: 支持单个文件不超过10MB，最多可上传5个附件。</p>
                            </div>
                        </div>
                        <