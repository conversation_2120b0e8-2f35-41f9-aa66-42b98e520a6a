unit CcJcdm;

interface

uses
  Classes;

type
  TCcJcdm = class
  private
    FJcdmid: integer;
    FJcdmtypeid: integer;
    FJcdmname: string;
    FJcdmnote: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Jcdmid: integer read FJcdmid write FJcdmid;
    property Jcdmtypeid: integer read FJcdmtypeid write FJcdmtypeid;
    property Jcdmname: string read FJcdmname write FJcdmname;
    property Jcdmnote: string read FJcdmnote write FJcdmnote;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
