'use client'

import { useState } from 'react'
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, BarChart2, Trash2 } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface ChartConfig {
  id: string
  title: string
  type: string
  dataSource: string
}

export function ChartConfig() {
  const [charts, setCharts] = useState<ChartConfig[]>([
    { id: '1', title: '销售趋势', type: 'Line', dataSource: '销售数据' },
    { id: '2', title: '财务分析', type: 'Bar', dataSource: '财务数据' },
  ])

  const addChart = () => {
    const newChart: ChartConfig = {
      id: `${Date.now()}`,
      title: '新图表',
      type: 'Line',
      dataSource: ''
    }
    setCharts([...charts, newChart])
  }

  const removeChart = (id: string) => {
    setCharts(charts.filter(chart => chart.id !== id))
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">图表配置</h3>
        <Button onClick={addChart}>
          <Plus className="h-4 w-4 mr-2" />
          添加图表
        </Button>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-4">
          {charts.map((chart) => (
            <div key={chart.id} className="p-4 bg-gray-50 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <BarChart2 className="h-4 w-4 mr-2 text-green-500" />
                  <Input
                    value={chart.title}
                    onChange={(e) => {
                      const newTitle = e.target.value
                      setCharts(charts.map(c => c.id === chart.id ? { ...c, title: newTitle } : c))
                    }}
                    placeholder="图表标题"
                    className="w-48"
                  />
                </div>
                <Button variant="ghost" size="sm" onClick={() => removeChart(chart.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="mb-2">
                <Select
                  value={chart.type}
                  onValueChange={(value) => {
                    setCharts(charts.map(c => c.id === chart.id ? { ...c, type: value } : c))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择图表类型" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Line">折线图</SelectItem>
                    <SelectItem value="Bar">柱状图</SelectItem>
                    <SelectItem value="Pie">饼图</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select
                  value={chart.dataSource}
                  onValueChange={(value) => {
                    setCharts(charts.map(c => c.id === chart.id ? { ...c, dataSource: value } : c))
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择数据源" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="销售数据">销售数据</SelectItem>
                    <SelectItem value="财务数据">财务数据</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
} 