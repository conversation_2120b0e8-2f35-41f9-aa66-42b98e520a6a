<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研管理人员社会活动情况管理流程</text>

  <!-- 阶段一：数据加载与展示 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与展示</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(社会活动情况管理)</text>
  </g>

  <!-- 节点2: 系统默认加载 -->
  <g transform="translate(590, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统默认加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(全部已登记活动信息)</text>
  </g>

  <!-- 节点3: 信息列表展示 -->
  <g transform="translate(880, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息列表展示</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">(社会活动数据)</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 520 165 Q 555 165 590 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 810 165 Q 845 165 880 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：查询筛选与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询筛选与检索</text>

  <!-- 节点4: 设置检索条件 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设置检索条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(查询筛选区)</text>
  </g>

  <!-- 节点5: 自动筛选 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(系统即时处理)</text>
  </g>

  <!-- 节点6: 刷新展示 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">刷新展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(符合条件的活动数据)</text>
  </g>

  <!-- 连接线 列表展示 -> 设置检索 -->
  <path d="M 990 200 C 990 240, 300 270, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：活动操作管理 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：活动操作管理</text>

  <!-- 节点7: 新增活动 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增活动</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(录入表单、数据校验)</text>
  </g>

  <!-- 节点8: 编辑活动 -->
  <g transform="translate(320, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑活动</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(修正信息、更新记录)</text>
  </g>

  <!-- 节点9: 删除活动 -->
  <g transform="translate(540, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除活动</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(确认提示、移除数据)</text>
  </g>

  <!-- 节点10: 查看详情 -->
  <g transform="translate(760, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看详情</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(完整信息、历史记录)</text>
  </g>

  <!-- 节点11: 导出数据 -->
  <g transform="translate(980, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出数据</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(批量导出、下载链接)</text>
  </g>

  <!-- 连接线 刷新展示 -> 各操作 -->
  <path d="M 850 380 C 750 420, 250 450, 190 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 880 380 C 780 420, 480 450, 410 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 380 C 800 420, 700 450, 630 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 920 380 C 920 420, 900 450, 850 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 380 C 1000 420, 1100 450, 1070 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统处理与同步 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统处理与同步</text>
  
  <!-- 节点12: 数据同步 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
      <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据同步</text>
      <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-80">上游单位</tspan>
        <tspan dx="40">权限控制</tspan>
        <tspan dx="40">定期同步</tspan>
      </text>
  </g>

  <!-- 节点13: 完整性保障 -->
  <g transform="translate(700, 670)" filter="url(#soft-shadow)">
      <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="150" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">完整性保障</text>
      <text x="150" y="60" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-80">数据完整</tspan>
        <tspan dx="40">时效性</tspan>
        <tspan dx="40">一致性</tspan>
      </text>
  </g>

  <!-- 连接线 各操作 -> 数据同步 -->
  <path d="M 190 560 C 190 600, 350 640, 400 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 410 560 C 410 600, 450 640, 500 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 各操作 -> 完整性保障 -->
  <path d="M 630 560 C 630 600, 750 640, 800 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 560 C 850 600, 850 640, 850 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1070 560 C 1070 600, 950 640, 900 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从数据同步回到系统加载 -->
  <path d="M 450 670 C 50 600, 50 100, 300 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 100 400)">数据同步循环</text>

  <!-- 反馈循环：从查看详情回到检索条件 -->
  <path d="M 850 490 C 1200 420, 1200 280, 1000 310" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1150" y="400" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90 1150 400)">深度查询循环</text>

</svg>