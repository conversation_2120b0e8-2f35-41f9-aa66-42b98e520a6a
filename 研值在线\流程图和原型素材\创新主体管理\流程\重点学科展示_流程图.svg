<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">重点学科展示系统流程图</text>

  <!-- 阶段一：数据同步与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与标准化</text>
  
  <!-- 节点1: 数据源 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">教育部数据源</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">重点学科与专业数据</text>
  </g>

  <!-- 节点2: 省教育厅 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">省教育厅系统</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">地方学科建设数据</text>
  </g>

  <!-- 节点3: 高校系统 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">高校教务科研系统</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">内部学科管理数据</text>
  </g>

  <!-- 节点4: 数据标准化 -->
  <g transform="translate(450, 250)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据标准化处理</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">门类、级别、学科代码标准化</text>
  </g>

  <!-- 连接线到标准化 -->
  <path d="M 250 200 C 250 225, 350 225, 450 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 200 Q 550 225 550 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 200 C 850 225, 750 225, 650 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计分析与缓存 -->
  <text x="700" y="380" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计分析与缓存</text>

  <!-- 节点5: 统计服务 -->
  <g transform="translate(250, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计分析服务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">数量、增长率、指标得分计算</text>
  </g>

  <!-- 节点6: 可视化数据构建 -->
  <g transform="translate(650, 420)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化数据构建</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">分布图、雷达图、协作网络数据</text>
  </g>

  <!-- 连接线 标准化 -> 统计分析 -->
  <path d="M 550 320 C 450 350, 400 380, 375 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 统计服务 -> 可视化构建 -->
  <path d="M 500 455 C 550 455, 600 455, 650 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：页面展示与交互 -->
  <text x="700" y="560" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：页面展示与交互</text>

  <!-- 节点7: 前端渲染 -->
  <g transform="translate(200, 600)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端页面渲染</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">概况卡片、图表展示</text>
  </g>

  <!-- 节点8: 交互功能 -->
  <g transform="translate(550, 600)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互处理</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">筛选、下钻、详情查看</text>
  </g>

  <!-- 节点9: 异步数据加载 -->
  <g transform="translate(900, 600)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">异步数据请求</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">补充数据、实时更新</text>
  </g>

  <!-- 连接线 可视化数据 -> 前端渲染 -->
  <path d="M 700 490 C 600 530, 450 560, 350 600" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 前端渲染 -> 交互处理 -->
  <path d="M 450 635 C 500 635, 500 635, 550 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 交互处理 -> 异步请求 -->
  <path d="M 800 635 C 850 635, 850 635, 900 635" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：日志记录与分析 -->
  <text x="700" y="750" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：日志记录与分析</text>
  
  <!-- 节点10: 操作日志 -->
  <g transform="translate(500, 780)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">操作行为日志记录</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">用户行为统计</tspan>
      <tspan dx="60">访问分析</tspan>
      <tspan dx="60">性能优化</tspan>
    </text>
  </g>

  <!-- 连接线 异步请求 -> 日志记录 -->
  <path d="M 1025 670 C 1025 720, 800 750, 800 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：定期同步 -->
  <path d="M 600 320 C 100 320, 100 100, 150 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="210" font-size="12" fill="#666">定期同步</text>

  <!-- 反馈循环：数据更新 -->
  <path d="M 700 780 C 1200 780, 1200 450, 950 455" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1100" y="620" font-size="12" fill="#666">统计反馈</text>

</svg>