<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员科技诚信管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                        info: '#3b82f6'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">研发人员科技诚信管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-full flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-3 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                        </svg>
                        诚信行为查询与筛选
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="misconductType" class="block text-sm font-medium text-gray-700 mb-0.5">失信行为类型</label>
                            <select id="misconductType" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="data-falsification">数据造假</option>
                                <option value="plagiarism">抄袭剽窃</option>
                                <option value="conflict-of-interest">利益冲突未声明</option>
                                <option value="misconduct-review">评审失当</option>
                                <option value="other">其他失信行为</option>
                            </select>
                        </div>
                        <div>
                            <label for="keyword" class="block text-sm font-medium text-gray-700 mb-0.5">关键词</label>
                            <input type="text" id="keyword" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入描述关键词">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-0.5">发生时间</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="punishment" class="block text-sm font-medium text-gray-700 mb-0.5">惩戒措施</label>
                            <select id="punishment" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部措施</option>
                                <option value="warning">警告</option>
                                <option value="reprimand">通报批评</option>
                                <option value="project-suspension">项目暂停</option>
                                <option value="fund-withdrawal">经费追回</option>
                                <option value="qualification-revocation">资格撤销</option>
                            </select>
                        </div>
                        <div>
                            <label for="repairStatus" class="block text-sm font-medium text-gray-700 mb-0.5">修复状态</label>
                            <select id="repairStatus" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="pending">待修复</option>
                                <option value="in-progress">修复中</option>
                                <option value="completed">已修复</option>
                                <option value="rejected">修复未通过</option>
                                <option value="not-applicable">不适用</option>
                            </select>
                        </div>
                        <div>
                            <label for="relatedProject" class="block text-sm font-medium text-gray-700 mb-0.5">关联项目</label>
                            <input type="text" id="relatedProject" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称或编号">
                        </div>
                    </div>
                    <div class="mt-3 flex justify-between items-center">
                        <div class="flex space-x-3">
                            <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                重置
                            </button>
                            <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                查询
                            </button>
                        </div>
                        <button id="exportBtn" class="px-4 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            导出数据
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 诚信行为列表区 -->
                    <div class="w-full">
                        <!-- 列表标题区 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                                    </svg>
                                    诚信行为列表
                                </h2>
                                <button onclick="openModal('addMisconductModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                    新增诚信行为
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">失信行为</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">事项描述</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发生时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">惩戒措施</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联项目</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">数据造假</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">在宁波市智能装备研究院"智能传感器研发"项目中伪造实验数据</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目暂停</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-09-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB20230012</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待修复</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="openModal('deleteModal')" class="text-red-600 hover:text-red-900">删除</button>
                                            <button onclick="openModal('repairModal')" class="text-green-600 hover:text-green-900">修复</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">抄袭剽窃</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">在"宁波新材料技术研究"论文中剽窃他人研究成果</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">通报批评</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-07-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB20220567</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已修复</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="openModal('deleteModal')" class="text-red-600 hover:text-red-900">删除</button>
                                            <button class="text-gray-400 cursor-not-allowed">修复</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">利益冲突未声明</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">担任宁波某科技公司顾问期间未声明，参与相关项目评审</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-10</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">警告</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-05-10</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB20230890</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">修复中</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="openModal('deleteModal')" class="text-red-600 hover:text-red-900">删除</button>
                                            <button onclick="openModal('repairModal')" class="text-green-600 hover:text-green-900">修复</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">评审失当</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">在宁波市重点实验室评审中存在明显倾向性，未公正评分</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-05</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">资格撤销</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-04-05</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">待修复</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="openModal('deleteModal')" class="text-red-600 hover:text-red-900">删除</button>
                                            <button onclick="openModal('repairModal')" class="text-green-600 hover:text-green-900">修复</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">其他失信行为</td>
                                        <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">未按时提交宁波海洋经济研究院项目进展报告，且未说明理由</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">警告</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB20230456</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">修复未通过</span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openModal('editModal')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="openModal('deleteModal')" class="text-red-600 hover:text-red-900">删除</button>
                                            <button onclick="openModal('repairModal')" class="text-green-600 hover:text-green-900">修复</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <!-- 分页控件 -->
                    <div class="px-6 py-3 border-t border-gray-200 flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">24</span> 条记录
                        </div>
                        <div class="flex space-x-1">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50" disabled>上一页</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">4</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">5</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增诚信行为弹窗 -->
    <div id="addMisconductModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">新增诚信行为</h3>
                    <button onclick="closeModal('addMisconductModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="add-researcher" class="block text-sm font-medium text-gray-700 mb-1">研发人员 <span class="text-red-500">*</span></label>
                            <select id="add-researcher" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择研发人员</option>
                                <option value="1">张三 - 宁波市智能装备研究院</option>
                                <option value="2">李四 - 宁波大学</option>
                                <option value="3">王五 - 中科院宁波材料所</option>
                                <option value="4">赵六 - 宁波海洋经济研究院</option>
                            </select>
                        </div>
                        <div>
                            <label for="add-misconduct-type" class="block text-sm font-medium text-gray-700 mb-1">失信行为类型 <span class="text-red-500">*</span></label>
                            <select id="add-misconduct-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择失信行为类型</option>
                                <option value="data-falsification">数据造假</option>
                                <option value="plagiarism">抄袭剽窃</option>
                                <option value="conflict-of-interest">利益冲突未声明</option>
                                <option value="misconduct-review">评审失当</option>
                                <option value="other">其他失信行为</option>
                            </select>
                        </div>
                        <div>
                            <label for="add-occurrence-date" class="block text-sm font-medium text-gray-700 mb-1">发生时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="add-occurrence-date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="add-punishment" class="block text-sm font-medium text-gray-700 mb-1">惩戒措施 <span class="text-red-500">*</span></label>
                            <select id="add-punishment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择惩戒措施</option>
                                <option value="warning">警告</option>
                                <option value="reprimand">通报批评</option>
                                <option value="project-suspension">项目暂停</option>
                                <option value="fund-withdrawal">经费追回</option>
                                <option value="qualification-revocation">资格撤销</option>
                            </select>
                        </div>
                        <div>
                            <label for="add-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">到期时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="add-expiry-date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="add-related-project" class="block text-sm font-medium text-gray-700 mb-1">关联项目</label>
                            <input type="text" id="add-related-project" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="项目名称或编号">
                        </div>
                    </div>
                    <div>
                        <label for="add-description" class="block text-sm font-medium text-gray-700 mb-1">事项描述 <span class="text-red-500">*</span></label>
                        <textarea id="add-description" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述失信行为的具体情况"></textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">相关证据材料</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-2">
                                <label for="add-evidence" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>上传文件</span>
                                    <input id="add-evidence" name="add-evidence" type="file" class="sr-only" multiple>
                                </label>
                                <p class="text-xs text-gray-500 mt-1">支持 PDF, DOC, DOCX, JPG, PNG 格式，单个文件不超过10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" onclick="closeModal('addMisconductModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑诚信行为弹窗 -->
    <div id="editModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">编辑诚信行为</h3>
                    <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="edit-researcher" class="block text-sm font-medium text-gray-700 mb-1">研发人员 <span class="text-red-500">*</span></label>
                            <select id="edit-researcher" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" disabled>
                                <option value="1" selected>张三 - 宁波市智能装备研究院</option>
                            </select>
                        </div>
                        <div>
                            <label for="edit-misconduct-type" class="block text-sm font-medium text-gray-700 mb-1">失信行为类型 <span class="text-red-500">*</span></label>
                            <select id="edit-misconduct-type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择失信行为类型</option>
                                <option value="data-falsification" selected>数据造假</option>
                                <option value="plagiarism">抄袭剽窃</option>
                                <option value="conflict-of-interest">利益冲突未声明</option>
                                <option value="misconduct-review">评审失当</option>
                                <option value="other">其他失信行为</option>
                            </select>
                        </div>
                        <div>
                            <label for="edit-occurrence-date" class="block text-sm font-medium text-gray-700 mb-1">发生时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="edit-occurrence-date" value="2024-03-15" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="edit-punishment" class="block text-sm font-medium text-gray-700 mb-1">惩戒措施 <span class="text-red-500">*</span></label>
                            <select id="edit-punishment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择惩戒措施</option>
                                <option value="warning">警告</option>
                                <option value="reprimand">通报批评</option>
                                <option value="project-suspension" selected>项目暂停</option>
                                <option value="fund-withdrawal">经费追回</option>
                                <option value="qualification-revocation">资格撤销</option>
                            </select>
                        </div>
                        <div>
                            <label for="edit-expiry-date" class="block text-sm font-medium text-gray-700 mb-1">到期时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="edit-expiry-date" value="2024-09-15" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="edit-related-project" class="block text-sm font-medium text-gray-700 mb-1">关联项目</label>
                            <input type="text" id="edit-related-project" value="NB20230012" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="项目名称或编号">
                        </div>
                    </div>
                    <div>
                        <label for="edit-description" class="block text-sm font-medium text-gray-700 mb-1">事项描述 <span class="text-red-500">*</span></label>
                        <textarea id="edit-description" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述失信行为的具体情况">在宁波市智能装备研究院"智能传感器研发"项目中伪造实验数据，涉及传感器灵敏度测试数据篡改，影响项目评估结果。</textarea>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">相关证据材料</label>
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 18h2a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2h2m4 0h1m-7 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-sm text-gray-700">实验数据对比报告.pdf</span>
                                </div>
                                <button type="button" class="text-red-500 hover:text-red-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-2">
                                <label for="edit-evidence" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>上传更多文件</span>
                                    <input id="edit-evidence" name="edit-evidence" type="file" class="sr-only" multiple>
                                </label>
                                <p class="text-xs text-gray-500 mt-1">支持 PDF, DOC, DOCX, JPG, PNG 格式，单个文件不超过10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 诚信行为详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">诚信行为详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">研发人员</h4>
                        <p class="text-base font-semibold text-gray-900">张三</p>
                        <p class="text-sm text-gray-500">宁波市智能装备研究院</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">失信行为类型</h4>
                        <p class="text-base font-semibold text-gray-900">数据造假</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">发生时间</h4>
                        <p class="text-base font-semibold text-gray-900">2024-03-15</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">当前状态</h4>
                        <p class="text-base font-semibold text-yellow-600">待修复</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">惩戒措施</h4>
                        <p class="text-base font-semibold text-gray-900">项目暂停</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">到期时间</h4>
                        <p class="text-base font-semibold text-gray-900">2024-09-15</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">关联项目</h4>
                        <p class="text-base font-semibold text-gray-900">NB20230012</p>
                        <p class="text-sm text-gray-500">智能传感器研发</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-1">记录创建时间</h4>
                        <p class="text-base font-semibold text-gray-900">2024-03-16 09:30:00</p>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-500 mb-2">事项描述</h4>
                    <div class="p-4 bg-gray-50 rounded-lg border border-gray-200">
                        <p class="text-base text-gray-700">在宁波市智能装备研究院"智能传感器研发"项目中伪造实验数据，涉及传感器灵敏度测试数据篡改，将实际灵敏度值从85%提高至98%，影响项目中期评估结果。经调查核实，该行为违反了《宁波市科研诚信管理办法》第三章第十一条规定。</p>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-500 mb-2">证据材料</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 18h2a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2h2m4 0h1m-7 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">实验数据对比报告.pdf</p>
                                    <p class="text-xs text-gray-500">上传于 2024-03-16 10:15</p>
                                </div>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </button>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">调查询问笔录.docx</p>
                                    <p class="text-xs text-gray-500">上传于 2024-03-17 14:22</p>
                                </div>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-500 mb-2">处理历史</h4>
                    <div class="space-y-4">
                        <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                            <div class="flex justify-between items-start mb-1">
                                <div class="flex items-center">
                                    <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 mr-2">记录创建</span>
                                    <span class="text-sm font-medium text-gray-900">系统管理员</span>
                                </div>
                                <span class="text-xs text-gray-500">2024-03-16 09:30:00</span>
                            </div>
                            <p class="text-sm text-gray-700">创建了该诚信行为记录，状态设为"待修复"</p>
                        </div>
                        <div class="p-3 bg-gray-50 rounded-md border border-gray-200">
                            <div class="flex justify-between items-start mb-1">
                                <div class="flex items-center">
                                    <span class="px-2 py-0.5 text-xs font-semibold rounded-full bg-green-100 text-green-800 mr-2">惩戒决定</span>
                                    <span class="text-sm font-medium text-gray-900">科技诚信办公室</span>
                                </div>
                                <span class="text-xs text-gray-500">2024-03-17 15:45:00</span>
                            </div>
                            <p class="text-sm text-gray-700">决定对该失信行为采取"项目暂停"惩戒措施，期限6个月</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4 border-t">
                    <button type="button" onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button type="button" onclick="openModal('repairModal'); closeModal('detailModal')" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        申请修复
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 诚信修复弹窗 -->
    <div id="repairModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">诚信修复申请</h3>
                    <button onclick="closeModal('repairModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-blue-800">修复指南</h3>
                            <div class="mt-2 text-sm text-blue-700">
                                <p>请详细描述失信行为的整改情况和修复措施，上传相关证明材料。修复申请将由科技诚信办公室审核，审核通过后状态将更新为"已修复"。</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form class="space-y-4">
                    <div>
                        <label for="repair-description" class="block text-sm font-medium text-gray-700 mb-1">修复说明 <span class="text-red-500">*</span></label>
                        <textarea id="repair-description" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请详细描述整改情况和修复措施"></textarea>
                    </div>
                    
                    <div>
                        <label for="repair-date" class="block text-sm font-medium text-gray-700 mb-1">整改完成日期 <span class="text-red-500">*</span></label>
                        <input type="date" id="repair-date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">修复佐证材料 <span class="text-red-500">*</span></label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-2">
                                <label for="repair-evidence" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>上传文件</span>
                                    <input id="repair-evidence" name="repair-evidence" type="file" class="sr-only" required multiple>
                                </label>
                                <p class="text-xs text-gray-500 mt-1">支持 PDF, DOC, DOCX, JPG, PNG 格式，单个文件不超过10MB</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" onclick="closeModal('repairModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            提交修复申请
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
                    <button onclick="closeModal('deleteModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="flex items-start mb-6">
                    <div class="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <h3 class="text-lg font-medium text-gray-900">确定要删除该诚信行为记录吗？</h3>
                        <div class="mt-2 text-sm text-gray-500">
                            <p>此操作不可撤销。删除后，该诚信行为的所有相关数据将被永久删除，包括修复记录和证据材料。</p>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('deleteModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button type="button" onclick="closeModal('deleteModal')" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 导出按钮点击事件
            document.getElementById('exportBtn').addEventListener('click', function() {
                alert('数据导出功能已触发，正在准备导出文件...');
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    
                    // 根据不同弹窗显示不同提示
                    let message = '';
                    if (parentModalId === 'addMisconductModal') {
                        message = '诚信行为记录已成功添加！';
                    } else if (parentModalId === 'editModal') {
                        message = '诚信行为记录已成功更新！';
                    } else if (parentModalId === 'repairModal') {
                        message = '诚信修复申请已提交，等待审核！';
                    }
                    
                    alert(message);
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>