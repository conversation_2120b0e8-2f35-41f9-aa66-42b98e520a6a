<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业接触收集表单 - 科技表单信息系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .form-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid #E5E9EF;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-building text-green-600"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">企业接触收集表单</h1>
                        <p class="text-sm text-gray-600">记录走访企业过程中采集到的技术需求信息</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-gray-600">企业走访模块</span>
                    </div>
                    <button onclick="window.open('index1.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回首页
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-5xl mx-auto px-6 py-8">
        <!-- 走访活动概览 -->
        <div class="bg-white rounded-xl card-shadow p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">走访活动概览</h3>
                <div class="flex items-center space-x-2">
                    <button class="px-4 py-2 bg-green-100 text-green-700 rounded-lg text-sm">本月</button>
                    <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">本年</button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center p-4 bg-green-50 rounded-lg">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-building text-green-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-green-600">328</p>
                    <p class="text-sm text-gray-600">走访企业数</p>
                </div>
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-clipboard-list text-blue-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-blue-600">892</p>
                    <p class="text-sm text-gray-600">收集需求数</p>
                </div>
                <div class="text-center p-4 bg-purple-50 rounded-lg">
                    <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-users text-purple-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-purple-600">56</p>
                    <p class="text-sm text-gray-600">参与人员数</p>
                </div>
                <div class="text-center p-4 bg-orange-50 rounded-lg">
                    <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-handshake text-orange-600"></i>
                    </div>
                    <p class="text-2xl font-bold text-orange-600">156</p>
                    <p class="text-sm text-gray-600">成功对接数</p>
                </div>
            </div>
        </div>

        <!-- 表单内容 -->
        <form class="space-y-6">
            <!-- 走访基本信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-calendar-check text-green-500 mr-2"></i>
                    走访基本信息
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            走访时间 <span class="text-red-500">*</span>
                        </label>
                        <input type="datetime-local" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            走访类型 <span class="text-red-500">*</span>
                        </label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">请选择走访类型</option>
                            <option value="诊断服务">诊断服务</option>
                            <option value="技术走访">技术走访</option>
                            <option value="需求调研">需求调研</option>
                            <option value="对接服务">对接服务</option>
                            <option value="政策宣讲">政策宣讲</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            走访人员 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" placeholder="请输入走访人员姓名" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>
            </div>

            <!-- 企业信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-building text-blue-500 mr-2"></i>
                    企业信息
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            企业名称 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" placeholder="搜索企业名称" 
                                   class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">支持模糊搜索企业名称</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            统一社会信用代码
                        </label>
                        <input type="text" readonly placeholder="自动填充" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                        <p class="mt-1 text-xs text-gray-500">选择企业后自动填充</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            企业类型
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">高新技术企业</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">科技型中小企业</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">规上企业</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            所属行业
                        </label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">请选择行业</option>
                            <option value="智能制造">智能制造</option>
                            <option value="生物医药">生物医药</option>
                            <option value="新材料">新材料</option>
                            <option value="新能源">新能源</option>
                            <option value="数字经济">数字经济</option>
                            <option value="海洋科技">海洋科技</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 技术需求信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                    技术需求信息
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            技术需求描述 <span class="text-red-500">*</span>
                        </label>
                        <textarea rows="4" placeholder="请详细描述企业的技术需求..." 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                技术领域 <span class="text-red-500">*</span>
                            </label>
                            <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                <option value="">请选择技术领域</option>
                                <option value="人工智能">人工智能</option>
                                <option value="物联网">物联网</option>
                                <option value="大数据">大数据</option>
                                <option value="云计算">云计算</option>
                                <option value="区块链">区块链</option>
                                <option value="工业互联网">工业互联网</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                需求紧急程度
                            </label>
                            <div class="flex items-center space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="urgency" value="urgent" class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">紧急</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="urgency" value="normal" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">一般</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="urgency" value="low" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300">
                                    <span class="ml-2 text-sm text-gray-700">较低</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                预期研发投入（万元）
                            </label>
                            <div class="relative">
                                <input type="number" placeholder="0.00" step="0.01" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500">
                                    <span class="text-sm">万元</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                预期完成时间
                            </label>
                            <input type="date" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            现有技术基础
                        </label>
                        <textarea rows="3" placeholder="请描述企业现有的技术基础和条件..." 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            期望合作方式
                        </label>
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-3">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">技术转让</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">合作开发</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">委托研发</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">人才引进</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-address-book text-purple-500 mr-2"></i>
                    联系信息
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            企业联系人 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" placeholder="请输入联系人姓名" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            联系人职务
                        </label>
                        <input type="text" placeholder="请输入职务" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            联系电话 <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" placeholder="请输入联系电话" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            电子邮箱
                        </label>
                        <input type="email" placeholder="请输入电子邮箱" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>
            </div>

            <!-- 走访记录 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-file-alt text-orange-500 mr-2"></i>
                    走访记录
                </h3>
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            走访情况记录 <span class="text-red-500">*</span>
                        </label>
                        <textarea rows="6" placeholder="请详细记录走访过程、企业反馈、存在问题等..." 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            后续跟进计划
                        </label>
                        <textarea rows="3" placeholder="请描述后续跟进的具体计划和措施..." 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            附件上传
                        </label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-2">点击上传或拖拽文件到此处</p>
                            <p class="text-xs text-gray-500">支持PDF、DOC、DOCX、JPG、PNG格式，单个文件不超过10MB</p>
                            <input type="file" multiple class="hidden" id="file-upload">
                            <button type="button" onclick="document.getElementById('file-upload').click()" 
                                    class="mt-4 px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                                选择文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单操作按钮 -->
            <div class="flex items-center justify-between pt-6">
                <div class="flex items-center space-x-4">
                    <button type="button" class="flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存草稿
                    </button>
                    <button type="button" class="flex items-center px-6 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-print mr-2"></i>
                        打印记录
                    </button>
                </div>
                <div class="flex items-center space-x-4">
                    <button type="button" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        重置表单
                    </button>
                    <button type="submit" class="px-8 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
                        提交走访记录
                    </button>
                </div>
            </div>
        </form>

        <!-- 最近走访记录 -->
        <div class="bg-white rounded-xl card-shadow p-6 mt-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">最近走访记录</h3>
                <button class="text-green-600 hover:text-green-800 text-sm font-medium">
                    查看全部记录 <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
            <div class="space-y-4">
                <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-building text-green-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">宁波智能制造有限公司</h4>
                            <span class="text-xs text-gray-500">2024-03-15</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">技术需求：工业互联网平台建设</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">已跟进</span>
                            <span class="text-xs text-gray-500">走访人员：张工程师</span>
                        </div>
                    </div>
                </div>
                
                <div class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-building text-blue-600"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h4 class="font-medium text-gray-900">鄞州新材料科技园</h4>
                            <span class="text-xs text-gray-500">2024-03-12</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">技术需求：新型复合材料研发</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">待跟进</span>
                            <span class="text-xs text-gray-500">走访人员：李主任</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 文件上传处理
        document.addEventListener('DOMContentLoaded', function() {
            const fileUpload = document.getElementById('file-upload');
            
            fileUpload.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                if (files.length > 0) {
                    const fileNames = files.map(file => file.name).join(', ');
                    alert('已选择文件：' + fileNames);
                }
            });

            // 表单数据验证
            const form = document.querySelector('form');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // 基本验证
                const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        isValid = false;
                        field.classList.add('border-red-500');
                    } else {
                        field.classList.remove('border-red-500');
                    }
                });
                
                if (isValid) {
                    alert('走访记录提交成功！');
                } else {
                    alert('请填写所有必填字段');
                }
            });
        });
    </script>
</body>
</html> 