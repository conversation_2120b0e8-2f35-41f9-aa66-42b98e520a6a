<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">企业诊断书数据处理与展示流程</text>

  <!-- 阶段一：权限验证与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：权限验证与数据加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入诊断书模块</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">权限验证与界面初始化</text>
  </g>

  <!-- 节点2: 主数据加载 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">加载企业诊断主数据</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">依据用户权限获取数据</text>
  </g>

  <!-- 节点3: 指标计算 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">计算汇总指标</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">更新指标概览区</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点4: 筛选条件设定 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设定筛选条件</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">年份、机构、专家、关键字</text>
  </g>

  <!-- 节点5: 诊断数据服务 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">诊断数据服务查询</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">过滤诊断记录</text>
  </g>

  <!-- 节点6: 返回结果 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">返回列表结果</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">更新指标数据</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 420 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>

  <!-- 节点7: 用户点击详情 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击查看详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">列表项或指标卡片</text>
  </g>

  <!-- 节点8: 拉取诊断报告 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">拉取诊断报告</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">附件和提升记录</text>
  </g>

  <!-- 节点9: 渲染详情侧栏 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">渲染详情侧栏</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">组装统一对象</text>
  </g>

  <!-- 节点10: 文档存储系统 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">文档存储系统</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">附件管理子系统</text>
  </g>

  <!-- 连接线 7 -> 8 -> 9 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 (文档调用) -->
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：统计分析与导出 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：统计分析与导出</text>

  <!-- 节点11: 统计分析 -->
  <g transform="translate(200, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计分析交互</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合生成图表</text>
  </g>

  <!-- 节点12: 导出打印 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出打印诊断书</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">生成PDF/Excel</text>
  </g>

  <!-- 节点13: 审计日志 -->
  <g transform="translate(800, 670)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志记录</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">操作行为与合规审计</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -->
  <path d="M 420 705 Q 450 705 500 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 705 Q 750 705 800 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <!-- 从指标概览回到筛选条件 -->
  <path d="M 860 200 C 1100 200, 1100 280, 420 280 C 420 300, 420 310, 420 310" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="1050" y="195" font-size="11" fill="#666">用户交互触发</text>

  <!-- 从详情侧栏到文档存储的操作记录 -->
  <path d="M 700 560 C 700 600, 910 600, 910 670" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="750" y="595" font-size="11" fill="#666">操作记录</text>

  <!-- 从统计分析回到查询结果的数据同步 -->
  <path d="M 310 670 C 310 620, 310 420, 910 420 C 910 380, 910 380, 910 380" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="320" y="615" font-size="11" fill="#666">数据一致性保持</text>

</svg>