<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中科院成果管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">中科院成果管理</h1>
            <p class="text-gray-600">规范化管理中科院科研平台产生的各类创新成果，提供便捷高效的成果信息管理功能</p>
        </div>

        <!-- 查询条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                查询条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果名称</label>
                    <input type="text" placeholder="请输入成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="paper">学术论文</option>
                        <option value="patent">发明专利</option>
                        <option value="software">软件著作权</option>
                        <option value="standard">技术标准</option>
                        <option value="award">科技奖励</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属科研平台</label>
                    <input type="text" placeholder="请输入科研平台名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">年度区间</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="起始年份" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" placeholder="结束年份" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="published">已发布</option>
                        <option value="applied">已应用</option>
                        <option value="transferred">已转化</option>
                        <option value="archived">已归档</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">数据来源</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部来源</option>
                        <option value="platform">科研平台</option>
                        <option value="institute">研究所</option>
                        <option value="external">外部合作</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-between items-center mt-4">
                <div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                        </svg>
                        保存查询方案
                    </button>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        科研成果列表
                    </h2>
                    <div class="flex space-x-2">
                        <div class="relative" id="exportDropdown">
                            <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                导出
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                </div>
                            </div>
                        </div>
                        <button onclick="toggleImportPanel()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导入
                        </button>
                        <button onclick="openResultModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增成果
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属平台</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智慧港口智能调度系统</div>
                                <div class="text-sm text-gray-500">宁波市XX研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">软件著作权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能港口研究中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张伟</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已应用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewResult('res1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editResult('res1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteResult('res1')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">新型海洋生物材料制备技术</div>
                                <div class="text-sm text-gray-500">宁波市XX研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">发明专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市海洋生物技术实验室</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王芳</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已转化</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewResult('res2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editResult('res2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteResult('res2')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能制造关键技术标准研究</div>
                                <div class="text-sm text-gray-500">宁波市XX研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">技术标准</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李强</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewResult('res3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editResult('res3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteResult('res3')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">基于人工智能的医疗影像分析系统</div>
                                <div class="text-sm text-gray-500">宁波市XX研究所</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">软件著作权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市医疗健康大数据中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵明</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已归档</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewResult('res4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editResult('res4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteResult('res4')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">128</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- PDF成果汇编管理区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        PDF成果汇编管理
                    </h2>
                    <button onclick="togglePdfPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <span id="pdfPanelToggle">展开</span>
                    </button>
                </div>
            </div>
            <div id="pdfPanel" class="hidden">
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                            </svg>
                            下载PDF解析模板
                        </a>
                        <span class="text-xs text-gray-500">支持.pdf格式</span>
                    </div>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        <div class="mt-2">
                            <label for="pdf-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                <span>点击上传PDF文件</span>
                                <input id="pdf-upload" name="pdf-upload" type="file" class="sr-only" accept=".pdf">
                            </label>
                            <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">最大支持20MB</p>
                    </div>
                    <div id="pdfUploadProgress" class="hidden">
                        <div class="flex items-center justify-between mb-1">
                            <span class="text-sm font-medium text-gray-700">上传进度</span>
                            <span class="text-sm font-medium text-gray-700">65%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                            <div class="bg-blue-600 h-2.5 rounded-full" style="width: 65%"></div>
                        </div>
                    </div>
                    <div id="pdfParseResult" class="hidden bg-blue-50 p-4 rounded-lg">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-800">PDF解析完成</p>
                                <p class="mt-1 text-sm text-blue-700">已识别出23条成果记录，请确认后导入</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                            取消
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            开始解析
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 指标与图表分析区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        指标与图表分析
                    </h2>
                    <button onclick="toggleChartPanel()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        <span id="chartPanelToggle">展开</span>
                    </button>
                </div>
            </div>
            <div id="chartPanel" class="hidden">
                <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 年度增长趋势 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 mb-3">科研成果年度增长趋势</h3>
                        <div class="h-64 flex items-end space-x-1">
                            <div class="w-6 bg-blue-200 rounded-t" style="height: 30%"></div>
                            <div class="w-6 bg-blue-300 rounded-t" style="height: 50%"></div>
                            <div class="w-6 bg-blue-400 rounded-t" style="height: 40%"></div>
                            <div class="w-6 bg-blue-500 rounded-t" style="height: 70%"></div>
                            <div class="w-6 bg-blue-600 rounded-t" style="height: 100%"></div>
                        </div>
                        <div class="flex justify-between mt-2 text-xs text-gray-500">
                            <span>2019</span>
                            <span>2020</span>
                            <span>2021</span>
                            <span>2022</span>
                            <span>2023</span>
                        </div>
                    </div>
                    
                    <!-- 成果类型分布 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h3 class="text-sm font-medium text-gray-700 mb-3">科研成果类型分布</h3>
                        <div class="flex items-center justify-center h-64">
                            <div class="relative w-40 h-40">
                                <div class="absolute inset-0 rounded-full bg-blue-100" style="clip: rect(0, 80px, 80px, 40px); transform: rotate(0deg);"></div>
                                <div class="absolute inset-0 rounded-full bg-green-100" style="clip: rect(0, 80px, 80px, 40px); transform: rotate(72deg);"></div>
                                <div class="absolute inset-0 rounded-full bg-yellow-100" style="clip: rect(0, 80px, 80px, 40px); transform: rotate(144deg);"></div>
                                <div class="absolute inset-0 rounded-full bg-purple-100" style="clip: rect(0, 80px, 80px, 40px); transform: rotate(216deg);"></div>
                                <div class="absolute inset-0 rounded-full bg-red-100" style="clip: rect(0, 80px, 80px, 40px); transform: rotate(288deg);"></div>
                                <div class="absolute inset-0 rounded-full bg-white" style="width: 30px; height: 30px; margin: 25px;"></div>
                            </div>
                        </div>
                        <div class="flex flex-wrap justify-center gap-2 mt-2 text-xs">
                            <div class="flex items-center">
                                <span class="w-3 h-3 mr-1 bg-blue-500 rounded-full"></span>
                                <span>论文(35%)</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 mr-1 bg-green-500 rounded-full"></span>
                                <span>专利(25%)</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 mr-1 bg-yellow-500 rounded-full"></span>
                                <span>软件(20%)</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 mr-1 bg-purple-500 rounded-full"></span>
                                <span>标准(15%)</span>
                            </div>
                            <div class="flex items-center">
                                <span class="w-3 h-3 mr-1 bg-red-500 rounded-full"></span>
                                <span>其他(5%)</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 科研平台贡献 -->
                    <div class="bg-gray-50 p-4 rounded-lg md:col-span-2">
                        <h3 class="text-sm font-medium text-gray-700 mb-3">科研平台成果贡献</h3>
                        <div class="h-64 flex items-end space-x-4">
                            <div class="flex flex-col items-center">
                                <div class="w-10 bg-blue-500 rounded-t" style="height: 80%"></div>
                                <span class="mt-1 text-xs text-gray-500">智能港口</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-10 bg-green-500 rounded-t" style="height: 60%"></div>
                                <span class="mt-1 text-xs text-gray-500">海洋生物</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-10 bg-yellow-500 rounded-t" style="height: 40%"></div>
                                <span class="mt-1 text-xs text-gray-500">智能制造</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-10 bg-purple-500 rounded-t" style="height: 70%"></div>
                                <span class="mt-1 text-xs text-gray-500">医疗健康</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-10 bg-red-500 rounded-t" style="height: 30%"></div>
                                <span class="mt-1 text-xs text-gray-500">新材料</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 成果详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/3 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">成果详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">基本信息</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-xs text-gray-500">成果名称</p>
                                <p class="text-sm font-medium text-gray-900">宁波市智慧港口智能调度系统</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">成果类型</p>
                                <p class="text-sm font-medium text-gray-900">软件著作权</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">所属平台</p>
                                <p class="text-sm font-medium text-gray-900">宁波市智能港口研究中心</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">负责人</p>
                                <p class="text-sm font-medium text-gray-900">张伟</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">完成年度</p>
                                <p class="text-sm font-medium text-gray-900">2023</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">成果状态</p>
                                <p class="text-sm font-medium text-gray-900">已应用</p>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">成果描述</h4>
                        <p class="text-sm text-gray-700">该系统基于人工智能技术，实现了宁波港集装箱码头的智能调度与优化，显著提高了港口作业效率，降低了运营成本。</p>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">创新点</h4>
                        <ul class="list-disc list-inside text-sm text-gray-700 space-y-1">
                            <li>首创基于深度学习的集装箱装卸预测算法</li>
                            <li>创新性地将强化学习应用于港口调度决策</li>
                            <li>开发了多目标优化模型，平衡效率与成本</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">关键技术指标</h4>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <div>
                                    <p class="text-gray-500">调度准确率</p>
                                    <p class="font-medium">98.5%</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">作业效率提升</p>
                                    <p class="font-medium">35%</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">能耗降低</p>
                                    <p class="font-medium">22%</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">系统响应时间</p>
                                    <p class="font-medium">≤200ms</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">参与人员</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">张伟(负责人)</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">李强</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">王芳</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">赵明</span>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-2">相关附件</h4>
                        <div class="space-y-2">
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                技术报告.pdf
                            </a>
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                软件著作权证书.pdf
                            </a>
                            <a href="#" class="flex items-center text-blue-600 hover:text-blue-800 text-sm">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                应用案例.docx
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 border-t bg-gray-50">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    导出详情报告
                </button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑成果模态框 -->
    <div id="resultModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">科研成果编辑</h3>
                    <button onclick="closeResultModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">成果名称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">成果类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择成果类型</option>
                                    <option value="paper">学术论文</option>
                                    <option value="patent">发明专利</option>
                                    <option value="software">软件著作权</option>
                                    <option value="standard">技术标准</option>
                                    <option value="award">科技奖励</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">所属科研平台 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择科研平台</option>
                                    <option value="port">宁波市智能港口研究中心</option>
                                    <option value="ocean">宁波市海洋生物技术实验室</option>
                                    <option value="manufacture">宁波市智能制造研究院</option>
                                    <option value="medical">宁波市医疗健康大数据中心</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">负责人 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入负责人姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">完成年度 <span class="text-red-500">*</span></label>
                                <input type="number" placeholder="请输入完成年度" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">成果状态 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择成果状态</option>
                                    <option value="published">已发布</option>
                                    <option value="applied">已应用</option>
                                    <option value="transferred">已转化</option>
                                    <option value="archived">已归档</option>
                                </select>
                            </div>
                        </div>

                        <!-- 成果描述 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">成果描述</label>
                            <textarea rows="3" placeholder="请输入成果描述" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <!-- 创新点 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">创新点</label>
                            <div id="innovationPoints" class="space-y-2">
                                <div class="flex">
                                    <input type="text" placeholder="请输入创新点" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <button type="button" onclick="removeInnovationPoint(this)" class="ml-2 px-3 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addInnovationPoint()" class="mt-2 px-3 py-2 bg-blue-100 text-blue-600 rounded-md hover:bg-blue-200 text-sm flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                添加创新点
                            </button>
                        </div>

                        <!-- 关键技术指标 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关键技术指标</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">指标名称</label>
                                    <input type="text" placeholder="指标名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">指标值</label>
                                    <input type="text" placeholder="指标值" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">指标名称</label>
                                    <input type="text" placeholder="指标名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                </div>
                                <div>
                                    <label class="block text-xs text-gray-500 mb-1">指标值</label>
                                    <input type="text" placeholder="指标值" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                                </div>
                            </div>
                        </div>

                        <!-- 附件上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">相关附件</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-4">
                                    <label for="file-upload" class="cursor-pointer">
                                        <span class="block text-sm font-medium text-gray-900">点击上传文件</span>
                                        <span class="mt-1 block text-xs text-gray-500">支持 PDF, DOCX, XLSX 格式</span>
                                    </label>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                                </div>
                            </div>
                            <div class="mt-2 space-y-2">
                                <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                    <span class="text-sm text-gray-700">技术报告.pdf</span>
                                    <button class="text-red-500 hover:text-red-700">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                                <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                    <span class="text-sm text-gray-700">软件著作权证书.pdf</span>
                                    <button class="text-red-500 hover:text-red-700">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeResultModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 固定在右下角的工具栏 -->
    <div class="fixed bottom-6 right-6 space-y-2">
        <button class="p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
        </button>
        <button class="p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
        </button>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // PDF面板切换
        function togglePdfPanel() {
            const panel = document.getElementById('pdfPanel');
            const toggle = document.getElementById('pdfPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 图表面板切换
        function toggleChartPanel() {
            const panel = document.getElementById('chartPanel');
            const toggle = document.getElementById('chartPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 导入面板切换
        function toggleImportPanel() {
            const panel = document.getElementById('pdfPanel');
            const toggle = document.getElementById('pdfPanelToggle');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                toggle.textContent = '收起';
            } else {
                panel.classList.add('hidden');
                toggle.textContent = '展开';
            }
        }
        
        // 成果详情抽屉
        function viewResult(resultId) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
        }
        
        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }
        
        // 成果编辑模态框
        function openResultModal() {
            document.getElementById('resultModal').classList.remove('hidden');
        }
        
        function closeResultModal() {
            document.getElementById('resultModal').classList.add('hidden');
        }
        
        // 添加创新点
        function addInnovationPoint() {
            const container = document.getElementById('innovationPoints');
            const div = document.createElement('div');
            div.className = 'flex';
            div.innerHTML = `
                <input type="text" placeholder="请输入创新点" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <button type="button" onclick="removeInnovationPoint(this)" class="ml-2 px-3 py-2 bg-red-100 text-red-600 rounded-md hover:bg-red-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            `;
            container.appendChild(div);
        }
        
        function removeInnovationPoint(button) {
            button.parentElement.remove();
        }
        
        // 文件上传处理
        document.getElementById('pdf-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('pdfUploadProgress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    document.querySelector('#pdfUploadProgress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#pdfUploadProgress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('pdfParseResult').classList.remove('hidden');
                        }, 500);
                    }
                }, 100);
            }
        });
        
        // 点击模态框外部关闭
        document.getElementById('resultModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeResultModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>