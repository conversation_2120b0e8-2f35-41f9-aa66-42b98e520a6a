unit CcYsRzddj;

interface
uses
  Classes;

type
  TCcYsRzddj = class
  private
    FYsrzddjid: Integer;
    FYsid: Integer;
    FRzd_a: Double;
    FDj_a: Double;
    FRzd_b: Double;
    FDj_b: Double;
    FRzdcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysrzddjid: integer read FYsrzddjid write FYsrzddjid;
    property Ysid: integer read FYsid write FYsid;
    property Rzd_a: double read FRzd_a write FRzd_a;
    property Dj_a: double read FDj_a write FDj_a;
    property Rzd_b: double read FRzd_b write FRzd_b;
    property Dj_b: double read FDj_b write FDj_b;
    property Rzdcb: double read FRzdcb write FRzdcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

