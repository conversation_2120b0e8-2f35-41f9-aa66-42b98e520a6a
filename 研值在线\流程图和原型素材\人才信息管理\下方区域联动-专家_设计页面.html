<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家资源联动分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">专家资源联动分析</h1>
            <p class="text-gray-600">可视化展示宁波市科技专家队伍结构分布，支持多维度联动分析与人才清册钻取</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 研究领域词云区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path>
                        </svg>
                        研究领域分布（词云）
                    </h2>
                    <div class="flex flex-wrap justify-center gap-3 p-4 bg-gray-50 rounded-lg min-h-[200px]">
                        <span class="text-4xl font-bold text-blue-600 hover:text-blue-800 cursor-pointer" title="人工智能：286人 (12.5%)">人工智能</span>
                        <span class="text-3xl font-bold text-blue-500 hover:text-blue-700 cursor-pointer" title="新材料：245人 (10.7%)">新材料</span>
                        <span class="text-2xl font-semibold text-blue-400 hover:text-blue-600 cursor-pointer" title="智能制造：198人 (8.6%)">智能制造</span>
                        <span class="text-xl font-medium text-blue-300 hover:text-blue-500 cursor-pointer" title="生物医药：156人 (6.8%)">生物医药</span>
                        <span class="text-lg text-blue-200 hover:text-blue-400 cursor-pointer" title="海洋经济：132人 (5.8%)">海洋经济</span>
                        <span class="text-sm text-blue-100 hover:text-blue-300 cursor-pointer" title="新能源：98人 (4.3%)">新能源</span>
                    </div>
                </div>

                <!-- 专家属性分析区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        专家属性分析
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- 性别分布 -->
                        <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md cursor-pointer">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">性别分布</h3>
                            <div class="relative h-40">
                                <svg viewBox="0 0 100 100" class="w-full h-full">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" stroke-width="10"></circle>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3b82f6" stroke-width="10" stroke-dasharray="251.2 125.6" stroke-dashoffset="0"></circle>
                                    <text x="50" y="45" text-anchor="middle" font-size="12" fill="#1e40af">男 68%</text>
                                    <text x="50" y="60" text-anchor="middle" font-size="10" fill="#64748b">女 32%</text>
                                </svg>
                            </div>
                        </div>

                        <!-- 年龄分布 -->
                        <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md cursor-pointer">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">年龄分布</h3>
                            <div class="relative h-40">
                                <svg viewBox="0 0 100 100" class="w-full h-full">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" stroke-width="10"></circle>
                                    <path d="M50 10 A40 40 0 0 1 86 30 L50 50 Z" fill="#3b82f6" opacity="0.8"></path>
                                    <path d="M86 30 A40 40 0 0 1 50 90 L50 50 Z" fill="#60a5fa" opacity="0.8"></path>
                                    <path d="M50 90 A40 40 0 0 1 14 30 L50 50 Z" fill="#93c5fd" opacity="0.8"></path>
                                    <text x="50" y="30" text-anchor="middle" font-size="8" fill="#1e40af">30-40岁 42%</text>
                                    <text x="80" y="50" text-anchor="middle" font-size="8" fill="#1e40af">40-50岁 35%</text>
                                    <text x="50" y="80" text-anchor="middle" font-size="8" fill="#1e40af">50岁以上 23%</text>
                                </svg>
                            </div>
                        </div>

                        <!-- 学历分布 -->
                        <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md cursor-pointer">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">学历分布</h3>
                            <div class="relative h-40">
                                <svg viewBox="0 0 100 100" class="w-full h-full">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" stroke-width="10"></circle>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#10b981" stroke-width="10" stroke-dasharray="188.4 62.8 62.8" stroke-dashoffset="0"></circle>
                                    <text x="50" y="40" text-anchor="middle" font-size="10" fill="#065f46">博士 62%</text>
                                    <text x="50" y="60" text-anchor="middle" font-size="8" fill="#047857">硕士 25%</text>
                                    <text x="50" y="75" text-anchor="middle" font-size="6" fill="#059669">本科 13%</text>
                                </svg>
                            </div>
                        </div>

                        <!-- 职称分布 -->
                        <div class="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md cursor-pointer">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">职称分布</h3>
                            <div class="relative h-40">
                                <svg viewBox="0 0 100 100" class="w-full h-full">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e5e7eb" stroke-width="10"></circle>
                                    <path d="M50 10 A40 40 0 0 1 86 30 L50 50 Z" fill="#f59e0b" opacity="0.8"></path>
                                    <path d="M86 30 A40 40 0 0 1 86 70 L50 50 Z" fill="#fbbf24" opacity="0.8"></path>
                                    <path d="M86 70 A40 40 0 0 1 50 90 L50 50 Z" fill="#fcd34d" opacity="0.8"></path>
                                    <path d="M50 90 A40 40 0 0 1 14 70 L50 50 Z" fill="#fde68a" opacity="0.8"></path>
                                    <path d="M14 70 A40 40 0 0 1 14 30 L50 50 Z" fill="#fef3c7" opacity="0.8"></path>
                                    <text x="50" y="25" text-anchor="middle" font-size="6" fill="#92400e">教授级 28%</text>
                                    <text x="75" y="50" text-anchor="middle" font-size="6" fill="#92400e">副教授级 35%</text>
                                    <text x="50" y="85" text-anchor="middle" font-size="6" fill="#92400e">中级 22%</text>
                                    <text x="25" y="50" text-anchor="middle" font-size="6" fill="#92400e">其他 15%</text>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与操作区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 专家统计卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        专家统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">专家总数</div>
                                <div class="text-2xl font-bold text-blue-600">2,286</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">近一月新增</div>
                                <div class="text-2xl font-bold text-green-600">156</div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">高级职称专家</div>
                                <div class="text-2xl font-bold text-purple-600">1,432</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 钻取操作区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 21h7a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v11m0 5l4.879-4.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242z"></path>
                        </svg>
                        人才清册钻取
                    </h2>
                    <div class="space-y-4">
                        <p class="text-sm text-gray-600">当前筛选条件：人工智能领域、男性、40-50岁、博士学历、教授级职称</p>
                        <button class="w-full bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                            查看符合条件的专家清册 (86人)
                        </button>
                        <div class="text-center">
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-800">重置所有筛选条件</a>
                        </div>
                    </div>
                </div>

                <!-- 热门专家机构 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        热门专家机构
                    </h2>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">宁波大学</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">428人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">中科院宁波材料所</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">356人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">宁波市智能制造研究院</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">289人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">宁波市海洋研究院</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">187人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm font-medium text-gray-700">宁波市第一医院</span>
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">156人</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 词云点击事件
        document.querySelectorAll('.flex-wrap span').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.getAttribute('title');
                alert(`已筛选${title.split('：')[1]}`);
                // 实际应用中这里会触发联动筛选逻辑
            });
        });

        // 饼图点击事件
        document.querySelectorAll('.bg-white.p-4.rounded-lg').forEach(item => {
            item.addEventListener('click', function() {
                const title = this.querySelector('h3').textContent;
                alert(`已按${title}进行筛选`);
                // 实际应用中这里会触发联动筛选逻辑
            });
        });
    </script>
</body>
</html>