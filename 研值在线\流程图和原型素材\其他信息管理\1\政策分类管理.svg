<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="600" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策分类管理流程</text>

  <!-- 阶段一：分类录入与校验 -->
  <text x="600" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：分类录入与校验</text>
  
  <!-- 节点1: 分类新增/修改 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分类新增/修改</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(运维人员操作)</text>
  </g>

  <!-- 节点2: 校验处理 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">校验处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(名称编码唯一性、父子关系)</text>
  </g>

  <!-- 节点3: 生成分类记录 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成分类记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(标记状态为"待审核")</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 400 165 L 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2 -> 3 -->
  <path d="M 700 165 L 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与发布 -->
  <text x="600" y="250" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与发布</text>

  <!-- 节点4: 推送至管理员 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送至管理员</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(待审核记录推送)</text>
  </g>

  <!-- 节点5: 管理员审核 -->
  <g transform="translate(500, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(数据管理员核对)</text>
  </g>

  <!-- 节点6: 状态更新 -->
  <g transform="translate(800, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(更新为"已启用")</text>
  </g>
  
  <!-- 节点7: 缓存与快照 -->
  <g transform="translate(800, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存与快照</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(写入缓存、记录版本)</text>
  </g>

  <!-- 连接线 3 -> 4 -->
  <path d="M 900 200 C 900 240, 400 240, 300 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 400 335 L 500 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 335 L 800 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6 -> 7 -->
  <path d="M 900 370 L 900 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：停用删除与依赖检查 -->
  <text x="300" y="420" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：停用删除与依赖检查</text>

  <!-- 节点8: 停用/删除请求 -->
  <g transform="translate(50, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">停用/删除请求</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(用户操作)</text>
  </g>

  <!-- 节点9: 依赖检查 -->
  <g transform="translate(300, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">依赖检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(政策条目、子分类引用)</text>
  </g>

  <!-- 节点10: 阻止操作 -->
  <g transform="translate(200, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">阻止操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(提示处理依赖)</text>
  </g>

  <!-- 节点11: 执行操作 -->
  <g transform="translate(450, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">执行操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(停用/删除并更新缓存)</text>
  </g>
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 250 505 L 300 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 (存在依赖) -->
  <path d="M 400 540 C 400 565, 350 565, 300 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="350" y="565" text-anchor="middle" font-size="12" fill="#555">存在依赖</text>

  <!-- 连接线 9 -> 11 (无依赖) -->
  <path d="M 400 540 C 400 565, 450 565, 500 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="450" y="565" text-anchor="middle" font-size="12" fill="#555">无依赖</text>

  <!-- 阶段四：批量导入处理 -->
  <text x="900" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：批量导入处理</text>

  <!-- 节点12: 批量导入 -->
  <g transform="translate(700, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(上传文件)</text>
  </g>

  <!-- 节点13: 逐行校验 -->
  <g transform="translate(700, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">逐行校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(校验文件内容)</text>
  </g>

  <!-- 节点14: 批量审核 -->
  <g transform="translate(950, 570)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(管理员一次性审核)</text>
  </g>

  <!-- 节点15: 统一生效 -->
  <g transform="translate(950, 690)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统一生效</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(批量更新状态)</text>
  </g>

  <!-- 节点16: 定时扫描 -->
  <g transform="translate(400, 730)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时扫描</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(每日扫描分类库)</text>
  </g>

  <!-- 节点17: 修复任务 -->
  <g transform="translate(100, 800)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">修复任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(推送至运维人员)</text>
  </g>

  <!-- 节点18: 审计库 -->
  <g transform="translate(700, 800)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(记录变更操作)</text>
  </g>

  <!-- 连接线 12 -> 13 -->
  <path d="M 800 640 L 800 690" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 13 -> 14 -->
  <path d="M 900 725 C 925 725, 925 605, 950 605" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 14 -> 15 -->
  <path d="M 1050 640 L 1050 690" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 13 -> 4 (合规记录加入待审核队列) -->
  <path d="M 700 725 C 600 725, 150 725, 150 370, 200 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="400" y="740" text-anchor="middle" font-size="12" fill="#555">合规记录加入待审核队列</text>

  <!-- 连接线 16 -> 17 -->
  <path d="M 500 765 C 450 765, 350 765, 300 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 16 -> 18 -->
  <path d="M 500 765 C 550 765, 650 765, 700 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 定时任务标识 -->
  <text x="500" y="710" font-size="12" text-anchor="middle" font-style="italic" fill="#555">每日定时任务</text>

</svg>