unit Bgbh;

interface

uses
  Classes;

type
  TBgbh = class
  private
    FPmdzywid: string;
    FKsbmno: string;
    FMs: string;
    FLx: string;
    FMl: string;
    FBm: string;
    FBgbh: string;
    FPm: string;
  public
    property Pmdzywid: string read FPmdzywid write FPmdzywid;
    property Ksbmno: string read FKsbmno write FKsbmno;
    property Ms: string read FMs write FMs;
    property Lx: string read FLx write FLx;
    property Ml: string read FMl write FMl;
    property Bm: string read FBm write FBm;
    property Bgbh: string read FBgbh write FBgbh;
    property Pm: string read FPm write FPm;
  end;

implementation

end.
