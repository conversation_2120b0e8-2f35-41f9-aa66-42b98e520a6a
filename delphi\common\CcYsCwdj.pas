unit CcYsCwdj;

interface

uses
  Classes;

type
  TCcYsCwdj = class
  private

    FYscwdjid: Integer;
    FYsid: Integer;
    FGj: Double;
    FCbhj: Double;
    FShuil: Double;
    FYsjg: Double;
    FSjbj: Double;
    FZzjgrmb: Double;
    FZzjgmj: Double;
    FBz: string;

    FRmb_Rmbj: Double;
    FRmb_Cwfy: Double;
    FRmb_Djlr: Double;
    FMj_Tsl: Double;
    FMj_Mjj: Double;
    FMj_Rmbj: Double;
    FMj_Cwfy: Double;
    FMj_Djlr: Double;

    FGjCj: Double;
    FGjXs: Double;
    FYjl: Double;
    FYjje: Double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Yscwdjid: Integer read FYscwdjid write FYscwdjid;
    property Ysid: Integer read FYsid write FYsid;
    property Gj: Double read FGj write FGj;
    property Cbhj: Double read FCbhj write FCbhj;
    property Shuil: Double read FShuil write FShuil;
    property Ysjg: Double read FYsjg write FYsjg;
    property Sjbj: Double read FSjbj write FSjbj;
    property Zzjgrmb: Double read FZzjgrmb write FZzjgrmb;
    property Zzjgmj: Double read FZzjgmj write FZzjgmj;

    property Rmb_Rmbj: Double read FRmb_Rmbj write FRmb_Rmbj;
    property Rmb_Cwfy: Double read FRmb_Cwfy write FRmb_Cwfy;
    property Rmb_Djlr: Double read FRmb_Djlr write FRmb_Djlr;
    property Mj_Tsl: Double read FMj_Tsl write FMj_Tsl;
    property Mj_Mjj: Double read FMj_Mjj write FMj_Mjj;
    property Mj_Rmbj: Double read FMj_Rmbj write FMj_Rmbj;
    property Mj_Cwfy: Double read FMj_Cwfy write FMj_Cwfy;
    property Mj_Djlr: Double read FMj_Djlr write FMj_Djlr;

    property GjCj: Double read FGjCj write FGjCj;
    property GjXs: Double read FGjXs write FGjXs;
    property Yjl: Double read FYjl write FYjl;
    property Yjje: Double read FYjje write FYjje;

    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
