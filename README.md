# AI应用管理后台 - 基线版本

这是一个基于Next.js 14的企业级应用管理后台模板项目，提供了几个核心的界面模板，可作为后续可视化界面原型开发的基础。

## 🚀 技术栈

- **框架**: Next.js 14 (App Router)
- **UI组件**: React + TypeScript
- **样式**: Tailwind CSS + shadcn/ui
- **图表**: ECharts + Recharts
- **构建工具**: Webpack + PostCSS

## 📦 项目结构

```
src/
├── app/                    # 页面路由 (App Router)
│   └── template/          # 模板页面
│       ├── assets/            # 资源概览相关页面
│       │   ├── infrastructure/    # 基础设施管理
│       │   ├── applications/      # 应用管理
│       │   ├── operations/        # 运维监控
│       │   ├── overview/          # 概览仪表盘
│       │   └── visualization/     # 可视化工具
│       ├── ai-budget-review/     # AI预算项目审核
│       ├── customer-profile/     # 客户详情管理
│       ├── document-management/  # 文档处理系统
│       └── zfsq/                # 支付申请系统
├── components/            # 组件库
│   ├── ui/               # 基础UI组件 (shadcn/ui)
│   ├── layout/           # 布局组件
│   ├── assets/           # 资源管理相关组件
│   ├── dashboard/        # 仪表盘组件
│   └── theme/            # 主题相关组件
└── lib/                  # 工具函数
```

## 🎯 核心功能模板

### 📋 基础模板
#### 1. 客户详情管理 (`/template/customer-profile`)
- 客户基础信息展示
- 财务状况分析
- 购买历史记录
- AI智能分析和建议

#### 2. AI预算项目审核 (`/template/ai-budget-review`)
- 项目基础信息
- 预算分解和分析
- 风险评估
- AI智能决策建议

#### 3. 文档处理系统 (`/template/document-management`)
- 文档上传和管理
- 文档处理工作流
- 审批状态跟踪
- 自动化处理

#### 4. 支付申请系统 (`/template/zfsq`)
- 申请单创建和管理
- 审批流程
- 财务处理
- 状态跟踪

#### 5. 资源概览系统 (`/template/assets/*`)
- 基础设施监控
- 应用系统管理
- 运维统计分析
- 可视化配置工具

### 🧠 研值在线
#### 1. 创新主体科研码管理 (`/research/innovation-code/*`)
- 科研码管理 - 创新主体科研码的创建、编辑和管理
- 科研码展示 - 科研码信息的可视化展示

#### 2. 创新库管理 (`/research/innovation-lib/*`)
- **人才信息管理**
  - 人才总览分析 - 全面的人才数据统计和分析
  - 各类人才管理 - 不同类型人才的分类管理
  - 科技特派员活动管理 - 科技特派员相关活动的组织和管理
- **项目信息管理**
  - 项目总览分析 - 项目数据的统计分析
  - 各类项目管理 - 不同类型项目的管理
- **设备信息管理**
  - 仪器设备总览分析 - 设备使用情况统计
  - 仪器设备管理 - 设备的登记和管理
- **成果信息管理**
  - 成果总览分析 - 科技成果的统计分析
  - 科技成果管理 - 成果的录入和管理
- **机构信息管理**
  - 机构总览分析 - 机构数据统计
  - 机构管理 - 各类机构的管理
- **指南信息管理**
  - 指南总览分析 - 指南使用情况分析
  - 指南管理 - 各类指南的管理
  - 指南编制 - 新指南的创建和编制
- **其他信息管理**
  - 地理信息分析管理 - 地理数据的分析和管理
  - 政策法规库管理 - 相关政策法规的管理

## 🛠 开发

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3002

### 构建生产版本
```bash
npm run build
npm start
```

## 🎨 设计规范

- **主色调**: 蓝色系 (`#3B82F6`, `#F0F7FF`)
- **布局**: 左侧导航 + 主内容区
- **组件**: 统一使用shadcn/ui组件库
- **图标**: Lucide React
- **字体**: Inter

## 📝 使用说明

1. **导航结构**: 左侧边栏分为"资源概览"和"基础模板"两个主要部分
2. **页面风格**: 所有页面保持一致的视觉风格和交互模式
3. **组件复用**: 大量可复用的UI组件，方便快速开发新页面
4. **响应式**: 所有页面支持不同屏幕尺寸

## 🔧 定制开发

本项目作为基线版本，可以：

1. 参考现有页面的设计风格和布局
2. 复用现有的UI组件和样式
3. 基于现有模板快速开发新功能
4. 保持一致的用户体验

## �� 许可证

MIT License
