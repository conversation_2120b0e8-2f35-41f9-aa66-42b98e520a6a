unit CC_YfCalWinFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  CC_YfCalReportFrm;

type
  TCC_YfCalWinForm = class(TForm)
    RzPanel1: TRzPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_YfCalReportFrame: TCC_YfCalReportFrame;
  end;

var
  CC_YfCalWinForm: TCC_YfCalWinForm;

implementation

{$R *.dfm}

procedure TCC_YfCalWinForm.FormShow(Sender: TObject);
begin
  if (FCC_YfCalReportFrame <> nil) then
  begin
    FCC_YfCalReportFrame.Free;
    FCC_YfCalReportFrame := nil;
  end;

  if (FCC_YfCalReportFrame = nil) then
  begin
    FCC_YfCalReportFrame := TCC_YfCalReportFrame.Create(self);
    FCC_YfCalReportFrame.Parent := self.RzPanel1;
    FCC_YfCalReportFrame.Align := alClient;
    FCC_YfCalReportFrame.InitZbje();
  end;
end;

end.
