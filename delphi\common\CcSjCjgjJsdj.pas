unit CcSjCjgjJsdj;

interface

uses
  Classes;

type
  TCcSjCjgjJsdj = class
  private

    FSjgjjsdjid: Int64;
    FDdid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FSccj_Xl1: string;
    FWczt_Xl1: string;
    FScs_Xl1: double;
    FGj_Xl1: double;
    FXs_Xl1: double;
    FZj_Xl1: double;
    FJsrq_Xl1: string;
    FNote_Xl1: string;

    FSccj_Xl2: string;
    FWczt_Xl2: string;
    FScs_Xl2: double;
    FScsD_Xl2: double;
    FGj_Xl2: double;
    FXs_Xl2: double;
    FZj_Xl2: double;
    FJsrq_Xl2: string;
    FNote_Xl2: string;

    FSccj_Xl3: string;
    FWczt_Xl3: string;
    FScs_Xl3: double;
    FGj_Xl3: double;
    FXs_Xl3: double;
    FZj_Xl3: double;
    FJsrq_Xl3: string;
    FNote_Xl3: string;

    FCe_Xl1: double;
    FCe_Xl2: double;
    FCe_Xl3: double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjgjjsdjid: Int64 read FSjgjjsdjid write FSjgjjsdjid;
    property Ddid: Integer read FDdid write FDdid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Sccj_Xl1: string read FSccj_Xl1 write FSccj_Xl1;
    property Wczt_Xl1: string read FWczt_Xl1 write FWczt_Xl1;
    property Scs_Xl1: double read FScs_Xl1 write FScs_Xl1;
    property Gj_Xl1: double read FGj_Xl1 write FGj_Xl1;
    property Xs_Xl1: double read FXs_Xl1 write FXs_Xl1;
    property Zj_Xl1: double read FZj_Xl1 write FZj_Xl1;
    property Jsrq_Xl1: string read FJsrq_Xl1 write FJsrq_Xl1;
    property Note_Xl1: string read FNote_Xl1 write FNote_Xl1;

    property Sccj_Xl2: string read FSccj_Xl2 write FSccj_Xl2;
    property Wczt_Xl2: string read FWczt_Xl2 write FWczt_Xl2;
    property Scs_Xl2: double read FScs_Xl2 write FScs_Xl2;
    property ScsD_Xl2: double read FScsD_Xl2 write FScsD_Xl2;
    property Gj_Xl2: double read FGj_Xl2 write FGj_Xl2;
    property Xs_Xl2: double read FXs_Xl2 write FXs_Xl2;
    property Zj_Xl2: double read FZj_Xl2 write FZj_Xl2;
    property Jsrq_Xl2: string read FJsrq_Xl2 write FJsrq_Xl2;
    property Note_Xl2: string read FNote_Xl2 write FNote_Xl2;

    property Sccj_Xl3: string read FSccj_Xl3 write FSccj_Xl3;
    property Wczt_Xl3: string read FWczt_Xl3 write FWczt_Xl3;
    property Scs_Xl3: double read FScs_Xl3 write FScs_Xl3;
    property Gj_Xl3: double read FGj_Xl3 write FGj_Xl3;
    property Xs_Xl3: double read FXs_Xl3 write FXs_Xl3;
    property Zj_Xl3: double read FZj_Xl3 write FZj_Xl3;
    property Jsrq_Xl3: string read FJsrq_Xl3 write FJsrq_Xl3;
    property Note_Xl3: string read FNote_Xl3 write FNote_Xl3;

    property Ce_Xl1: double read FCe_Xl1 write FCe_Xl1;
    property Ce_Xl2: double read FCe_Xl2 write FCe_Xl2;
    property Ce_Xl3: double read FCe_Xl3 write FCe_Xl3;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
