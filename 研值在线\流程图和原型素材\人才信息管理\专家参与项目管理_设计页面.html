<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家参与项目管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">专家参与项目管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-3 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-1">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <label for="expertName" class="block text-sm font-medium text-gray-700 mb-0.5">专家姓名</label>
                            <input type="text" id="expertName" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专家姓名">
                        </div>
                        <div>
                            <label for="projectName" class="block text-sm font-medium text-gray-700 mb-0.5">项目名称</label>
                            <input type="text" id="projectName" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                        </div>
                        <div>
                            <label for="projectLevel" class="block text-sm font-medium text-gray-700 mb-0.5">项目级别</label>
                            <select id="projectLevel" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label for="projectType" class="block text-sm font-medium text-gray-700 mb-0.5">项目类型</label>
                            <select id="projectType" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="research">科研项目</option>
                                <option value="industry">产业项目</option>
                                <option value="service">社会服务项目</option>
                                <option value="other">其他项目</option>
                            </select>
                        </div>
                        <div>
                            <label for="projectPerson" class="block text-sm font-medium text-gray-700 mb-0.5">项目完成人</label>
                            <input type="text" id="projectPerson" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目完成人">
                        </div>
                        <div>
                            <label for="projectDate" class="block text-sm font-medium text-gray-700 mb-0.5">项目时间</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endDate" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                    </div>
                    <div class="mt-1.5 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 项目列表区 -->
                    <div class="w-full">
                        <!-- 项目列表区标题 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800">专家参与项目列表</h2>
                                <div class="flex space-x-3">
                                    <button id="batchRemoveBtn" class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 flex items-center" disabled>
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        批量移除
                                    </button>
                                    <button onclick="openModal('addProjectModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        新增项目
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专家姓名</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目级别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目总投资</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目完成人</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="project-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="1">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">张明教授</div>
                                            <div class="text-xs text-gray-500">材料科学专家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市新型纳米材料研发与应用</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科研项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15<br>至<br>2025-12-31</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥580万</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明、李华、王芳</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openDetailModal(1)" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openEditModal(1)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmRemove(1)" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="project-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="2">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">李华博士</div>
                                            <div class="text-xs text-gray-500">人工智能专家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市智能制造数字化转型平台建设</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">产业项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-06-10<br>至<br>2024-06-10</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥1200万</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李华、赵伟、陈静</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openDetailModal(2)" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openEditModal(2)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmRemove(2)" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="project-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="3">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">王芳研究员</div>
                                            <div class="text-xs text-gray-500">生物医药专家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市海洋生物医药资源开发与利用研究</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科研项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-11-05<br>至<br>2026-11-04</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥2300万</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王芳、刘强、赵敏</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openDetailModal(3)" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openEditModal(3)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmRemove(3)" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="project-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="4">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">赵伟教授</div>
                                            <div class="text-xs text-gray-500">环境科学专家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市生态环境保护与可持续发展评估</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">社会服务项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-20<br>至<br>2024-03-19</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥350万</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵伟、钱明、孙丽</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openDetailModal(4)" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openEditModal(4)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmRemove(4)" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="project-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="5">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">陈静工程师</div>
                                            <div class="text-xs text-gray-500">新能源技术专家</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市新能源汽车关键技术研发及产业化</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">产业项目</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-15<br>至<br>2025-09-14</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥1800万</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈静、吴刚、郑敏</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="openDetailModal(5)" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="openEditModal(5)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="confirmRemove(5)" class="text-red-600 hover:text-red-900">移除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">23</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与批量导入区 -->
            <div class="w-full lg:w-1/4 lg:min-w-[280px] space-y-4">
                <!-- 统计与图表区 -->
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">统计分析</h3>
                    <div class="space-y-6">
                        <!-- 项目级别分布 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">项目级别分布</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">国家级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 25%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">8</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">省级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 40%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">12</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">市级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">18</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">区级</span>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-24 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 30%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900">9</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 项目类型分布 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">项目类型分布</h4>
                            <div class="h-32">
                                <canvas id="projectTypeChart"></canvas>
                            </div>
                        </div>

                        <!-- 项目投资统计 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">项目投资统计</h4>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">总投资</span>
                                    <span class="text-lg font-bold text-blue-600">¥1.26亿</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">平均投资</span>
                                    <span class="text-sm font-medium text-gray-900">¥547万</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">最大投资项目</span>
                                    <span class="text-xs text-gray-500">海洋生物医药研发</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 批量导入/导出区 -->
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-800">数据导入导出</h3>
                    </div>
                    <div class="p-4 space-y-4">
                        <!-- 导出功能 -->
                        <div class="border-b border-gray-200 pb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">导出数据</h4>
                            <div class="space-y-2">
                                <button id="exportCurrentBtn" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                    </svg>
                                    导出当前页数据
                                </button>
                                <button id="exportAllBtn" class="w-full px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                    </svg>
                                    导出全部筛选数据
                                </button>
                            </div>
                        </div>

                        <!-- 导入功能 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">导入数据</h4>
                            <div>
                                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center mb-3">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    下载标准模板
                                </a>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                    </svg>
                                    <div class="mt-2">
                                        <label for="file-upload" class="cursor-pointer">
                                            <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                            <span class="mt-1 block text-xs text-gray-500">支持 .xlsx, .xls 格式</span>
                                        </label>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only" accept=".xlsx,.xls">
                                    </div>
                                </div>
                                <div id="uploadProgress" class="hidden mt-3">
                                    <div class="flex items-center justify-between text-sm">
                                        <span class="text-gray-600">上传进度</span>
                                        <span class="text-gray-600" id="progressPercent">0%</span>
                                    </div>
                                    <div class="mt-1 w-full bg-gray-200 rounded-full h-2">
                                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                                <button id="importBtn" class="w-full mt-3 px-3 py-2 bg-green-600 text-white rounded-md text-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500" disabled>
                                    开始导入
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑项目弹窗 -->
    <div id="addProjectModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" aria-hidden="true">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="projectModalTitle">新增专家参与项目</h3>
                    <button onclick="closeModal('addProjectModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form id="projectForm" class="space-y-4">
                    <input type="hidden" id="projectId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="modalExpertName" class="block text-sm font-medium text-gray-700 mb-1">专家姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalExpertName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专家姓名">
                        </div>
                        <div>
                            <label for="modalExpertTitle" class="block text-sm font-medium text-gray-700 mb-1">专家职称</label>
                            <input type="text" id="modalExpertTitle" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入专家职称">
                        </div>
                        <div>
                            <label for="modalProjectName" class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="modalProjectName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目名称">
                        </div>
                        <div>
                            <label for="modalProjectLevel" class="block text-sm font-medium text-gray-700 mb-1">项目级别 <span class="text-red-500">*</span></label>
                            <select id="modalProjectLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择项目级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalProjectType" class="block text-sm font-medium text-gray-700 mb-1">项目类型 <span class="text-red-500">*</span></label>
                            <select id="modalProjectType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择项目类型</option>
                                <option value="research">科研项目</option>
                                <option value="industry">产业项目</option>
                                <option value="service">社会服务项目</option>
                                <option value="other">其他项目</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalProjectRole" class="block text-sm font-medium text-gray-700 mb-1">专家角色</label>
                            <select id="modalProjectRole" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="leader">项目负责人</option>
                                <option value="participant">核心参与人</option>
                                <option value="advisor">技术顾问</option>
                                <option value="cooperator">合作研究人员</option>
                            </select>
                        </div>
                        <div>
                            <label for="modalStartDate" class="block text-sm font-medium text-gray-700 mb-1">开始时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="modalStartDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="modalEndDate" class="block text-sm font-medium text-gray-700 mb-1">结束时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="modalEndDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="modalInvestment" class="block text-sm font-medium text-gray-700 mb-1">项目总投资（万元）</label>
                            <input type="number" id="modalInvestment" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目总投资">
                        </div>
                        <div>
                            <label for="modalProjectPerson" class="block text-sm font-medium text-gray-700 mb-1">项目完成人</label>
                            <input type="text" id="modalProjectPerson" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目完成人，多个用逗号分隔">
                        </div>
                    </div>
                    <div>
                        <label for="modalProjectDesc" class="block text-sm font-medium text-gray-700 mb-1">项目描述</label>
                        <textarea id="modalProjectDesc" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目描述"></textarea>
                    </div>
                    <div>
                        <label for="modalProjectAchievements" class="block text-sm font-medium text-gray-700 mb-1">主要成果</label>
                        <textarea id="modalProjectAchievements" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入项目主要成果"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4 border-t">
                        <button type="button" onclick="closeModal('addProjectModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" aria-hidden="true">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- 左侧专家信息 -->
                    <div class="md:col-span-1">
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">专家信息</h4>
                            <div class="space-y-3">
                                <div>
                                    <p class="text-sm text-gray-500">专家姓名</p>
                                    <p class="text-base font-medium text-gray-900" id="detailExpertName">张明教授</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">专家职称</p>
                                    <p class="text-base font-medium text-gray-900" id="detailExpertTitle">材料科学专家</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">专家单位</p>
                                    <p class="text-base font-medium text-gray-900" id="detailExpertUnit">宁波大学材料科学与工程学院</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">参与角色</p>
                                    <p class="text-base font-medium text-gray-900" id="detailExpertRole">项目负责人</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">项目状态</h4>
                            <div class="space-y-3">
                                <div>
                                    <p class="text-sm text-gray-500">项目阶段</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectPhase">进行中</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目进度</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 65%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">65%</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">最近更新</p>
                                    <p class="text-base font-medium text-gray-900" id="detailLastUpdate">2024-03-15</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 右侧项目详情 -->
                    <div class="md:col-span-2">
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">项目基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-500">项目名称</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectName">宁波市新型纳米材料研发与应用</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目编号</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectCode">NBXM2023001</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目级别</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectLevel">省级</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目类型</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectType">科研项目</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">开始时间</p>
                                    <p class="text-base font-medium text-gray-900" id="detailStartDate">2023-01-15</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">结束时间</p>
                                    <p class="text-base font-medium text-gray-900" id="detailEndDate">2025-12-31</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目总投资</p>
                                    <p class="text-base font-medium text-gray-900" id="detailInvestment">¥580万</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">项目完成人</p>
                                    <p class="text-base font-medium text-gray-900" id="detailProjectPerson">张明、李华、王芳</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">项目描述</h4>
                            <p class="text-base text-gray-700" id="detailProjectDesc">
                                本项目旨在研发新型纳米材料并探索其在能源、环境和生物医药领域的应用。通过创新纳米制备技术，开发具有优异性能的纳米复合材料，解决传统材料在催化效率、传感灵敏度和生物相容性等方面的瓶颈问题。项目将建立从材料设计、制备到应用评价的完整技术体系，推动纳米材料产业化进程，为宁波市相关产业转型升级提供技术支撑。
                            </p>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">主要成果</h4>
                            <ul class="text-base text-gray-700 space-y-2" id="detailProjectAchievements">
                                <li class="flex items-start">
                                    <span class="text-blue-600 mr-2">•</span>
                                    <span>开发出3种新型纳米复合材料，性能达到国际领先水平</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-600 mr-2">•</span>
                                    <span>申请发明专利5项，其中已授权2项</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-600 mr-2">•</span>
                                    <span>发表SCI论文8篇，其中影响因子大于10的论文3篇</span>
                                </li>
                                <li class="flex items-start">
                                    <span class="text-blue-600 mr-2">•</span>
                                    <span>与宁波2家企业达成技术合作意向，预计产业化后年经济效益可达5000万元</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="text-md font-medium text-gray-900 mb-3 border-b pb-2">历史变更记录</h4>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead>
                                        <tr>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更时间</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更内容</th>
                                            <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-500">2024-03-15 10:23</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">更新项目进度至65%</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">系统管理员</td>
                                        </tr>
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-500">2023-12-20 14:45</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">添加项目中期成果</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">张明</td>
                                        </tr>
                                        <tr>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-500">2023-01-15 09:30</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">创建项目记录</td>
                                            <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">系统管理员</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button id="detailEditBtn" onclick="switchToEditMode()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        编辑
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认删除弹窗 -->
    <div id="confirmModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" aria-hidden="true">
        <div class="relative top-40 mx-auto p-5 border w-80 shadow-lg rounded-md bg-white">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900">确认移除</h3>
                <div class="mt-2">
                    <p class="text-sm text-gray-500">您确定要移除该专家参与项目记录吗？此操作不可撤销。</p>
                </div>
                <input type="hidden" id="removeId">
                <div class="mt-6">
                    <div class="flex justify-center space-x-3">
                        <button type="button" onclick="closeModal('confirmModal')" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="button" onclick="removeProject()" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                            确认移除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            const projectTypeCtx = document.getElementById('projectTypeChart');
            if (projectTypeCtx) {
                new Chart(projectTypeCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['科研项目', '产业项目', '社会服务项目', '其他'],
                        datasets: [{
                            data: [15, 12, 6, 2],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.8)',
                                'rgba(16, 185, 129, 0.8)',
                                'rgba(245, 158, 11, 0.8)',
                                'rgba(107, 114, 128, 0.8)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    padding: 10,
                                    font: {
                                        size: 11
                                    }
                                }
                            }
                        },
                        cutout: '70%'
                    }
                });
            }

            // 全选/取消全选功能
            document.getElementById('selectAll').addEventListener('change', function(e) {
                const checkboxes = document.querySelectorAll('.project-checkbox');
                const batchRemoveBtn = document.getElementById('batchRemoveBtn');
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
                
                batchRemoveBtn.disabled = !e.target.checked;
            });

            // 单个复选框变化时更新批量按钮状态
            document.querySelectorAll('.project-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateBatchButtonState);
            });

            // 批量删除按钮点击事件
            document.getElementById('batchRemoveBtn').addEventListener('click', function() {
                const checkedBoxes = document.querySelectorAll('.project-checkbox:checked');
                if (checkedBoxes.length > 0) {
                    // 这里可以获取所有选中的ID
                    const ids = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.id);
                    document.getElementById('removeId').value = ids.join(',');
                    openModal('confirmModal');
                }
            });

            // 文件上传处理
            document.getElementById('file-upload').addEventListener('change', function(e) {
                const importBtn = document.getElementById('importBtn');
                const uploadProgress = document.getElementById('uploadProgress');
                
                if (e.target.files.length > 0) {
                    importBtn.disabled = false;
                    uploadProgress.classList.remove('hidden');
                    
                    // 模拟上传进度
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 5;
                        document.getElementById('progressBar').style.width = progress + '%';
                        document.getElementById('progressPercent').textContent = progress + '%';
                        
                        if (progress >= 100) {
                            clearInterval(interval);
                        }
                    }, 100);
                } else {
                    importBtn.disabled = true;
                    uploadProgress.classList.add('hidden');
                }
            });

            // 导入按钮点击事件
            document.getElementById('importBtn').addEventListener('click', function() {
                alert('数据导入成功！');
                closeModal('addProjectModal');
                // 这里可以添加刷新数据的逻辑
            });

            // 导出按钮点击事件
            document.getElementById('exportCurrentBtn').addEventListener('click', function() {
                alert('当前页数据导出成功！');
            });

            document.getElementById('exportAllBtn').addEventListener('click', function() {
                alert('全部筛选数据导出成功！');
            });

            // 表单提交处理
            document.getElementById('projectForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const projectId = document.getElementById('projectId').value;
                
                if (projectId) {
                    alert('项目更新成功！');
                } else {
                    alert('项目添加成功！');
                }
                
                closeModal('addProjectModal');
                // 这里可以添加刷新数据的逻辑
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // ESC键关闭
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
        });

        // 更新批量按钮状态
        function updateBatchButtonState() {
            const checkedBoxes = document.querySelectorAll('.project-checkbox:checked');
            const batchRemoveBtn = document.getElementById('batchRemoveBtn');
            const selectAllCheckbox = document.getElementById('selectAll');
            
            batchRemoveBtn.disabled = checkedBoxes.length === 0;
            
            // 更新全选框状态
            const totalBoxes = document.querySelectorAll('.project-checkbox').length;
            selectAllCheckbox.checked = checkedBoxes.length > 0 && checkedBoxes.length === totalBoxes;
        }

        // 打开详情模态框
        function openDetailModal(id) {
            // 在实际应用中，这里会根据id从服务器获取数据
            // 这里仅做演示，使用固定数据
            document.getElementById('detailExpertName').textContent = ['张明教授', '李华博士', '王芳研究员', '赵伟教授', '陈静工程师'][id-1];
            document.getElementById('detailExpertTitle').textContent = ['材料科学专家', '人工智能专家', '生物医药专家', '环境科学专家', '新能源技术专家'][id-1];
            document.getElementById('detailProjectName').textContent = [
                '宁波市新型纳米材料研发与应用',
                '宁波市智能制造数字化转型平台建设',
                '宁波市海洋生物医药资源开发与利用研究',
                '宁波市生态环境保护与可持续发展评估',
                '宁波市新能源汽车关键技术研发及产业化'
            ][id-1];
            document.getElementById('detailProjectLevel').textContent = ['省级', '市级', '国家级', '市级', '省级'][id-1];
            document.getElementById('detailProjectType').textContent = ['科研项目', '产业项目', '科研项目', '社会服务项目', '产业项目'][id-1];
            document.getElementById('detailStartDate').textContent = ['2023-01-15', '2022-06-10', '2021-11-05', '2023-03-20', '2022-09-15'][id-1];
            document.getElementById('detailEndDate').textContent = ['2025-12-31', '2024-06-10', '2026-11-04', '2024-03-19', '2025-09-14'][id-1];
            document.getElementById('detailInvestment').textContent = ['¥580万', '¥1200万', '¥2300万', '¥350万', '¥1800万'][id-1];
            
            // 保存当前查看的项目ID，用于切换到编辑模式
            document.getElementById('detailEditBtn').dataset.id = id;
            
            openModal('detailModal');
        }

        // 打开编辑模态框
        function openEditModal(id) {
            // 在实际应用中，这里会根据id从服务器获取数据并填充表单
            document.getElementById('projectModalTitle').textContent = '编辑专家参与项目';
            document.getElementById('projectId').value = id;
            
            // 这里仅做演示，填充一些示例数据
            document.getElementById('modalExpertName').value = ['张明教授', '李华博士', '王芳研究员', '赵伟教授', '陈静工程师'][id-1];
            document.getElementById('modalExpertTitle').value = ['材料科学专家', '人工智能专家', '生物医药专家', '环境科学专家', '新能源技术专家'][id-1];
            document.getElementById('modalProjectName').value = [
                '宁波市新型纳米材料研发与应用',
                '宁波市智能制造数字化转型平台建设',
                '宁波市海洋生物医药资源开发与利用研究',
                '宁波市生态环境保护与可持续发展评估',
                '宁波市新能源汽车关键技术研发及产业化'
            ][id-1];
            
            const levels = ['', 'national', 'provincial', 'municipal', 'district'];
            const levelIndex = ['', '省级', '市级', '国家级', '市级', '省级'].indexOf(['', '省级', '市级', '国家级', '市级', '省级'][id]);
            document.getElementById('modalProjectLevel').value = levels[levelIndex];
            
            const types = ['', 'research', 'industry', 'service', 'other'];
            const typeIndex = ['', '科研项目', '产业项目', '科研项目', '社会服务项目', '产业项目'].indexOf(['', '科研项目', '产业项目', '科研项目', '社会服务项目', '产业项目'][id]);
            document.getElementById('modalProjectType').value = types[typeIndex];
            
            document.getElementById('modalStartDate').value = ['', '2023-01-15', '2022-06-10', '2021-11-05', '2023-03-20', '2022-09-15'][id];
            document.getElementById('modalEndDate').value = ['', '2025-12-31', '2024-06-10', '2026-11-04', '2024-03-19', '2025-09-14'][id];
            document.getElementById('modalInvestment').value = ['', '580', '1200', '2300', '350', '1800'][id];
            document.getElementById('modalProjectPerson').value = ['', '张明、李华、王芳', '李华、赵伟、陈静', '王芳、刘强、赵敏', '赵伟、钱明、孙丽', '陈静、吴刚、郑敏'][id];
            
            openModal('addProjectModal');
        }

        // 从详情页切换到编辑模式
        function switchToEditMode() {
            const id = document.getElementById('detailEditBtn').dataset.id;
            closeModal('detailModal');
            openEditModal(id);
        }

        // 确认删除
        function confirmRemove(id) {
            document.getElementById('removeId').value = id;
            openModal('confirmModal');
        }

        // 执行删除
        function removeProject() {
            const id = document.getElementById('removeId').value;
            // 在实际应用中，这里会发送删除请求到服务器
            
            alert('项目记录已成功移除！');
            closeModal('confirmModal');
            
            // 这里可以添加刷新数据的逻辑
            // 如果是批量删除，需要更新复选框状态
            if (id.includes(',')) {
                document.getElementById('selectAll').checked = false;
                updateBatchButtonState();
            }
        }
    </script>
</body>
</html>