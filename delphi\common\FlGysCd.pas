unit FlGysCd;

interface

uses
  Classes;

type
  TFlGysCd = class
  private
    FFlGysCdid: integer;
    FFlGysCdname: string;
    FFltype: integer;
    FFltypeName: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property FlGysCdid: integer read FFlGysCdid write FFlGysCdid;
    property FlGysCdname: string read FFlGysCdname write FFlGysCdname;
    property Fltype: integer read FFltype write FFltype;
    property FltypeName: string read FFltypeName write FFltypeName;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
