<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下属科研平台建设展示流程</text>

  <!-- 阶段一：页面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与数据加载</text>
  
  <!-- 节点1: 用户访问页面 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">科研平台建设展示页面</text>
  </g>

  <!-- 节点2: 平台数据聚合服务 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">平台数据聚合服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">加载平台类型与研究领域</text>
  </g>

  <!-- 节点3: 默认视图生成 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">默认视图生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">概览卡片、列表、图表</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点4: 筛选条件调整 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件调整</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">用户输入关键字或条件</text>
  </g>

  <!-- 节点5: 查询参数组合 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询参数组合</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">调用数据服务</text>
  </g>

  <!-- 节点6: 实时数据刷新 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时数据刷新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">多区块数据同步</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 420 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>

  <!-- 节点7: 查看简介按钮 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击查看简介</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">列表中的按钮</text>
  </g>

  <!-- 节点8: 平台信息检索 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">平台信息检索</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">依据唯一标识获取</text>
  </g>

  <!-- 节点9: 会话缓存写入 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">会话缓存写入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全量平台信息</text>
  </g>

  <!-- 节点10: 简介抽屉渲染 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">简介抽屉渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录浏览日志</text>
  </g>

  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：操作执行与状态管理 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：操作执行与状态管理</text>

  <!-- 节点11: 项目一览操作 -->
  <g transform="translate(100, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看项目一览</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">路由至项目列表</text>
  </g>

  <!-- 节点12: PDF下载操作 -->
  <g transform="translate(350, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下载简介PDF</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">文件服务生成</text>
  </g>

  <!-- 节点13: 状态保留 -->
  <g transform="translate(600, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选状态保留</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">供后续访问复用</text>
  </g>

  <!-- 节点14: 资源清理 -->
  <g transform="translate(850, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存销毁清理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">写入最终日志</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -> 14 -->
  <path d="M 300 705 Q 325 705 350 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 705 Q 575 705 600 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 705 Q 825 705 850 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 从默认视图生成到筛选条件调整 -->
  <path d="M 860 200 C 860 240, 310 240, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从实时数据刷新到查看简介 -->
  <path d="M 910 380 C 910 420, 200 420, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从简介抽屉到操作执行 -->
  <path d="M 950 560 C 950 600, 200 600, 200 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <!-- 从筛选条件回到实时刷新 -->
  <path d="M 310 380 C 310 400, 910 400, 910 380" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="610" y="395" font-size="11" fill="#666">数据同步反馈</text>

  <!-- 从操作统计回到审计 -->
  <path d="M 450 740 C 450 780, 950 780, 950 740" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="700" y="795" font-size="11" fill="#666">操作统计更新</text>

  <!-- 从状态保留回到筛选条件 -->
  <path d="M 600 670 C 580 650, 320 650, 310 650 C 310 620, 310 380, 310 380" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="450" y="665" font-size="11" fill="#666">状态复用</text>

</svg>