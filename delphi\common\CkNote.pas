unit CkNote;

interface

uses
  Classes;

type
  TCkNote = class
  private
    FCkNoteid: integer;
    FCkNotename: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property CkNoteid: integer read FCkNoteid write FCkNoteid;
    property CkNotename: string read FCkNotename write FCkNotename;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
