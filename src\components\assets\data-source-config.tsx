'use client'

import { useState } from 'react'
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Plus, Database, Trash2 } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface DataSource {
  id: string
  name: string
  type: string
  connection: string
}

export function DataSourceConfig() {
  const [dataSources, setDataSources] = useState<DataSource[]>([
    { id: '1', name: '财务数据', type: 'MySQL', connection: 'mysql://user:pass@localhost:3306/db' },
    { id: '2', name: '销售数据', type: 'PostgreSQL', connection: 'postgres://user:pass@localhost:5432/db' },
  ])

  const addDataSource = () => {
    const newDataSource: DataSource = {
      id: `${Date.now()}`,
      name: '新数据源',
      type: 'MySQL',
      connection: ''
    }
    setDataSources([...dataSources, newDataSource])
  }

  const removeDataSource = (id: string) => {
    setDataSources(dataSources.filter(ds => ds.id !== id))
  }

  return (
    <div className="p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">数据源配置</h3>
        <Button onClick={addDataSource}>
          <Plus className="h-4 w-4 mr-2" />
          添加数据源
        </Button>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-4">
          {dataSources.map((ds) => (
            <div key={ds.id} className="p-4 bg-gray-50 rounded-lg shadow-sm">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <Database className="h-4 w-4 mr-2 text-blue-500" />
                  <Input
                    value={ds.name}
                    onChange={(e) => {
                      const newName = e.target.value
                      setDataSources(dataSources.map(d => d.id === ds.id ? { ...d, name: newName } : d))
                    }}
                    placeholder="数据源名称"
                    className="w-48"
                  />
                </div>
                <Button variant="ghost" size="sm" onClick={() => removeDataSource(ds.id)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="mb-2">
                <Input
                  value={ds.connection}
                  onChange={(e) => {
                    const newConnection = e.target.value
                    setDataSources(dataSources.map(d => d.id === ds.id ? { ...d, connection: newConnection } : d))
                  }}
                  placeholder="连接字符串"
                  className="w-full"
                />
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
} 