unit CcSjDyyfdj;

interface
uses
  Classes;

type
  TCcSjDyyfdj = class
  private

    FSjdyyfdjid: Integer;
    FDdid: Integer;
    FDyml: Double;
    FDyyfzp: Double;
    FDyyfcp: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;


  public
    property Sjdyyfdjid: integer read FSjdyyfdjid write FSjdyyfdjid;
    property Ddid: integer read FDdid write FDdid;
    property Dyml: double read FDyml write FDyml;
    property Dyyfzp: double read FDyyfzp write FDyyfzp;
    property Dyyfcp: double read FDyyfcp write FDyyfcp;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

