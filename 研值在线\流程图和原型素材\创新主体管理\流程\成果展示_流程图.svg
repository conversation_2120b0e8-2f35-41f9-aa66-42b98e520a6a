<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">成果展示业务流程图</text>

  <!-- 阶段一：系统初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与数据加载</text>
  
  <!-- 节点1: 用户访问页面 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">进入成果展示模块</text>
  </g>

  <!-- 节点2: 数据聚合服务 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果数据聚合服务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">加载枚举数据与默认视图</text>
  </g>

  <!-- 节点3: 页面渲染 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面初始化渲染</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">概览卡片、列表与图表</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 620 165 Q 685 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：交互筛选与数据同步 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：交互筛选与数据同步</text>

  <!-- 节点4: 用户筛选操作 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户筛选操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调整条件或输入关键字</text>
  </g>

  <!-- 节点5: 查询参数组合 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查询参数组合</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调用数据服务</text>
  </g>

  <!-- 节点6: 多区块同步刷新 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多区块同步刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时更新概览与列表</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情钻取与文件操作 -->
  <text x="700" y="470" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情钻取与文件操作</text>

  <!-- 节点7: 查看清册操作 -->
  <g transform="translate(150, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">查看清册操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">点击成果列表行按钮</text>
  </g>

  <!-- 节点8: 清册数据查询 -->
  <g transform="translate(450, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">清册数据查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">详细清册与年度统计</text>
  </g>

  <!-- 节点9: 抽屉渲染与日志 -->
  <g transform="translate(750, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">抽屉渲染与日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">下钻展示并记录浏览</text>
  </g>

  <!-- 节点10: PDF下载服务 -->
  <g transform="translate(1050, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">PDF下载服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">文件生成与审计统计</text>
  </g>

  <!-- 连接线 7 -> 8 -->
  <path d="M 350 545 Q 400 545 450 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 650 545 Q 700 545 750 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 950 545 Q 1000 545 1050 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：会话管理与资源释放 -->
  <text x="700" y="660" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：会话管理与资源释放</text>

  <!-- 节点11: 状态保留机制 -->
  <g transform="translate(300, 700)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态保留机制</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">筛选状态供后续复用</text>
  </g>

  <!-- 节点12: 资源清理与日志 -->
  <g transform="translate(680, 700)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">资源清理与日志</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">缓存销毁与安全保障</text>
  </g>

  <!-- 连接线 状态保留 -> 资源清理 -->
  <path d="M 520 735 Q 600 735 680 735" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从资源清理回到系统初始化 -->
  <path d="M 790 700 C 790 650, 1100 600, 1100 300 C 1100 200, 900 150, 970 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" text-anchor="middle" font-size="11" fill="#666">系统优化反馈</text>

  <!-- 从页面渲染到用户筛选的用户交互流 -->
  <path d="M 860 200 C 860 250, 300 280, 300 320" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="580" y="260" text-anchor="middle" font-size="11" fill="#666">用户交互</text>

  <!-- 从多区块刷新到查看清册的用户操作流 -->
  <path d="M 900 390 C 900 450, 250 480, 250 510" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="575" y="450" text-anchor="middle" font-size="11" fill="#666">深度钻取</text>

</svg>