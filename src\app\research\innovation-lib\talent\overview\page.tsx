'use client'

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  GraduationCap, 
  Award, 
  TrendingUp,
  BarChart3,
  <PERSON><PERSON>hart,
  Download,
  Filter
} from "lucide-react"

export default function TalentOverviewPage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      {/* 头部区域 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-medium text-gray-900">人才总览分析</h1>
          <p className="text-sm text-gray-500 mt-1">全面分析人才结构与分布情况</p>
        </div>
        <div className="flex items-center gap-4">
          <Select defaultValue="2024">
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="年份" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="2024">2024年</SelectItem>
              <SelectItem value="2023">2023年</SelectItem>
              <SelectItem value="2022">2022年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            筛选
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标 */}
      <div className="grid grid-cols-4 gap-6">
        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">总人才数量</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">15,248</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 18% 较去年同期
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">高层次人才</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">2,156</p>
              </div>
              <div className="h-12 w-12 bg-purple-50 rounded-lg flex items-center justify-center">
                <Award className="h-6 w-6 text-purple-500" />
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 25% 较去年同期
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">博士学位</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">4,892</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-green-500" />
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 12% 较去年同期
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">增长率</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">15.6%</p>
              </div>
              <div className="h-12 w-12 bg-orange-50 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-orange-500" />
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 3.2% 较去年同期
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 图表分析 */}
      <div className="grid grid-cols-2 gap-6">
        <Card className="border-[#E5E9EF]">
          <CardHeader className="border-b border-[#E5E9EF]">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <BarChart3 className="mr-2 h-5 w-5 text-blue-500" />
                人才类型分布
              </CardTitle>
              <Badge variant="outline">实时更新</Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[
                { type: "科研人员", count: 6248, percentage: 41, color: "bg-blue-500" },
                { type: "工程技术人员", count: 4521, percentage: 30, color: "bg-green-500" },
                { type: "管理人员", count: 2864, percentage: 19, color: "bg-yellow-500" },
                { type: "其他人员", count: 1615, percentage: 10, color: "bg-gray-500" }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full ${item.color} mr-3`}></div>
                    <span className="text-sm text-gray-700">{item.type}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 mr-2">{item.count.toLocaleString()}</span>
                    <span className="text-xs text-gray-500">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardHeader className="border-b border-[#E5E9EF]">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <PieChart className="mr-2 h-5 w-5 text-purple-500" />
                学历结构分析
              </CardTitle>
              <Badge variant="outline">月度统计</Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {[
                { degree: "博士", count: 4892, percentage: 32, color: "bg-purple-500" },
                { degree: "硕士", count: 6124, percentage: 40, color: "bg-blue-500" },
                { degree: "本科", count: 3458, percentage: 23, color: "bg-green-500" },
                { degree: "专科及以下", count: 774, percentage: 5, color: "bg-gray-500" }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full ${item.color} mr-3`}></div>
                    <span className="text-sm text-gray-700">{item.degree}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 mr-2">{item.count.toLocaleString()}</span>
                    <span className="text-xs text-gray-500">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 趋势分析 */}
      <Card className="border-[#E5E9EF]">
        <CardHeader className="border-b border-[#E5E9EF]">
          <CardTitle>人才增长趋势</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <BarChart3 className="h-12 w-12 mx-auto mb-2" />
              <p>人才增长趋势图表</p>
              <p className="text-sm">显示最近12个月的人才数量变化</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 区域分布 */}
      <div className="grid grid-cols-3 gap-6">
        <Card className="border-[#E5E9EF]">
          <CardHeader className="border-b border-[#E5E9EF]">
            <CardTitle>重点区域分布</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-3">
              {[
                { region: "北京市", count: 3248, growth: "+12%" },
                { region: "上海市", count: 2864, growth: "+18%" },
                { region: "深圳市", count: 2156, growth: "+25%" },
                { region: "杭州市", count: 1892, growth: "+15%" },
                { region: "成都市", count: 1624, growth: "+22%" }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-700">{item.region}</span>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 mr-2">{item.count.toLocaleString()}</span>
                    <Badge className="bg-green-100 text-green-700 text-xs">{item.growth}</Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardHeader className="border-b border-[#E5E9EF]">
            <CardTitle>机构类型分布</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-3">
              {[
                { type: "高等院校", count: 6248, percentage: 41 },
                { type: "科研院所", count: 3864, percentage: 25 },
                { type: "高新企业", count: 3124, percentage: 20 },
                { type: "医疗机构", count: 1456, percentage: 10 },
                { type: "其他机构", count: 556, percentage: 4 }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-700">{item.type}</span>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 mr-2">{item.count.toLocaleString()}</span>
                    <span className="text-xs text-gray-500">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardHeader className="border-b border-[#E5E9EF]">
            <CardTitle>专业领域分布</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-3">
              {[
                { field: "信息技术", count: 3864, percentage: 25 },
                { field: "生物医学", count: 2648, percentage: 17 },
                { field: "新材料", count: 2156, percentage: 14 },
                { field: "新能源", count: 1892, percentage: 12 },
                { field: "其他领域", count: 4688, percentage: 32 }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-700">{item.field}</span>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900 mr-2">{item.count.toLocaleString()}</span>
                    <span className="text-xs text-gray-500">({item.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 