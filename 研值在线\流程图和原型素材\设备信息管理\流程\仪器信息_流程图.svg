<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">仪器信息管理流程图</text>

  <!-- 阶段一：数据同步与处理 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与处理</text>
  
  <!-- 节点1: 数据源对接 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据源对接</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">每日自动对接各数据源</text>
  </g>

  <!-- 节点2: 数据处理 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">结构化/标准化/去重</text>
  </g>

  <!-- 节点3: 仪器信息主库 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">仪器信息主库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成最新信息主库</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 400 165 Q 450 165 500 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 165 Q 750 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户访问与初始化 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户访问与初始化</text>

  <!-- 节点4: 用户访问 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">访问仪器信息页面</text>
  </g>

  <!-- 节点5: 权限验证 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按用户权限范围加载</text>
  </g>

  <!-- 节点6: 页面初始化 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面初始化</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载图表及基础数据</text>
  </g>

  <!-- 连接线 主库 -> 用户访问 -->
  <path d="M 900 200 C 900 250, 300 250, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据交互与操作 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据交互与操作</text>

  <!-- 节点7: 筛选条件输入 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件输入</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">输入筛选条件</text>
  </g>

  <!-- 节点8: 数据检索过滤 -->
  <g transform="translate(320, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据检索过滤</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">实时执行检索</text>
  </g>

  <!-- 节点9: 详情查看 -->
  <g transform="translate(540, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">弹出详情弹窗</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(760, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">生成导出文件</text>
  </g>

  <!-- 节点11: 动态刷新 -->
  <g transform="translate(980, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态刷新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">刷新列表和图表</text>
  </g>

  <!-- 连接线 初始化 -> 筛选 -->
  <path d="M 800 380 C 800 430, 190 430, 190 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -> 11 -->
  <path d="M 280 525 Q 300 525 320 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 525 C 600 525, 800 525, 980 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 410 490 C 410 470, 630 470, 630 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 10 -->
  <path d="M 410 560 C 410 580, 850 580, 850 560" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据维护与归档 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据维护与归档</text>

  <!-- 节点12: 数据编辑删除 -->
  <g transform="translate(200, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据编辑删除</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">维护设备数据</text>
  </g>

  <!-- 节点13: 实时校验保存 -->
  <g transform="translate(500, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时校验保存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">校验并保存变更</text>
  </g>

  <!-- 节点14: 历史归档 -->
  <g transform="translate(800, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">历史归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">归档使用预约记录</text>
  </g>

  <!-- 连接线 详情/导出 -> 编辑 -->
  <path d="M 630 560 C 630 610, 300 610, 300 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 12 -> 13 -> 14 -->
  <path d="M 400 705 Q 450 705 500 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 705 Q 750 705 800 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据接口支持 -->
  <g transform="translate(500, 800)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据接口支持</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">绩效评估</tspan>
      <tspan dx="40">共享率分析</tspan>
      <tspan dx="40">资源配置决策</tspan>
    </text>
  </g>

  <!-- 连接线 归档 -> 接口支持 -->
  <path d="M 900 740 C 900 770, 700 770, 700 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 数据同步循环箭头 -->
  <path d="M 1200 705 C 1300 705, 1300 165, 1000 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="435" text-anchor="middle" font-size="12" fill="#666" transform="rotate(90, 1250, 435)">持续同步更新</text>

  <!-- 实时更新标注 -->
  <text x="600" y="620" text-anchor="middle" font-size="11" fill="#666">变更信息同步更新至所有数据视图</text>

</svg>