<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业奖励展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">企业奖励展示</h1>
        <p class="text-gray-600 mb-6">集中呈现创新主体所获得的科技奖项与专利奖信息</p>

        <!-- 奖项概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">国家级奖项</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">28</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="nationalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">省级奖项</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">56</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="provincialChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">市级奖项</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">89</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="municipalChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">专利奖项</p>
                        <p class="text-3xl font-bold text-gray-900 mt-2">42</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="patentChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">奖项级别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">奖项类别</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类别</option>
                        <option value="tech">科技进步奖</option>
                        <option value="invention">发明奖</option>
                        <option value="patent">专利奖</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">获奖年度</label>
                    <div class="flex space-x-1">
                        <input type="number" placeholder="开始" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="2000" max="2024">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="number" placeholder="结束" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" min="2000" max="2024">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">授奖单位</label>
                    <input type="text" placeholder="输入授奖单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">关键字搜索</label>
                    <input type="text" placeholder="输入奖项或成果名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 荣誉列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900">荣誉列表</h2>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖项名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授奖单位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年度</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市科技进步一等奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能物联网平台关键技术研发</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('1')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省专利金奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省知识产权局</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">一种新型智能传感器装置</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('2')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">国家科技进步二等奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家科学技术奖励办公室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新一代人工智能算法研究与应用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('3')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市发明奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市发明协会</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能家居控制系统</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">待归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('4')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省科技进步三等奖</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省科技厅</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">大数据分析平台</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已归档</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openCertificateModal('5')" class="text-blue-600 hover:text-blue-900">查看证书</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 215 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 相关图表区 -->
            <div class="w-full lg:w-1/3 lg:min-w-[350px]">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">奖项年度趋势</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">近5年</button>
                    </div>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">奖项类别分布</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">全部</button>
                    </div>
                    <div class="h-64">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 证书预览弹窗 -->
    <div id="certificateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">奖项证书预览</h3>
                    <button onclick="closeCertificateModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- 证书图片 -->
                        <div class="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
                            <img src="https://source.unsplash.com/800x600/?certificate,award" alt="奖项证书" class="max-w-full max-h-[70vh] rounded-lg shadow-md">
                        </div>
                        
                        <!-- 证书信息 -->
                        <div class="space-y-6">
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">奖项信息</h4>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-500">奖项名称：</span>
                                            <span class="font-medium text-gray-900" id="cert-name">宁波市科技进步一等奖</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">奖项级别：</span>
                                            <span class="font-medium text-gray-900" id="cert-level">市级</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">授奖单位：</span>
                                            <span class="font-medium text-gray-900" id="cert-org">宁波市科技局</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">获奖年度：</span>
                                            <span class="font-medium text-gray-900" id="cert-year">2023</span>
                                        </div>
                                        <div class="col-span-2">
                                            <span class="text-gray-500">成果名称：</span>
                                            <span class="font-medium text-gray-900" id="cert-project">智能物联网平台关键技术研发</span>
                                        </div>
                                        <div class="col-span-2">
                                            <span class="text-gray-500">获奖人员：</span>
                                            <span class="font-medium text-gray-900" id="cert-winner">张三、李四、王五等</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">证书状态</h4>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="grid grid-cols-2 gap-4 text-sm">
                                        <div>
                                            <span class="text-gray-500">归档状态：</span>
                                            <span class="font-medium text-gray-900">已归档</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">归档时间：</span>
                                            <span class="font-medium text-gray-900">2023-06-15</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">档案编号：</span>
                                            <span class="font-medium text-gray-900">NJ2023-001</span>
                                        </div>
                                        <div>
                                            <span class="text-gray-500">保管期限：</span>
                                            <span class="font-medium text-gray-900">永久</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-medium text-gray-900 mb-2">相关成果</h4>
                                <div class="bg-gray-50 rounded-lg p-4">
                                    <div class="text-sm space-y-2">
                                        <p>• 已申请发明专利3项（授权2项）</p>
                                        <p>• 发表SCI论文2篇</p>
                                        <p>• 形成企业标准1项</p>
                                        <p>• 实现产业化应用，年产值超5000万元</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeCertificateModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        返回
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        下载证书
                    </button>
                    <button class="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        打印
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openCertificateModal(id) {
            document.getElementById('certificateModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化雷达图
            const nationalCtx = document.getElementById('nationalChart').getContext('2d');
            new Chart(nationalCtx, {
                type: 'radar',
                data: {
                    labels: ['科技进步', '发明', '专利', '其他'],
                    datasets: [{
                        data: [12, 8, 5, 3],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        r: {
                            angleLines: { display: false },
                            suggestedMin: 0,
                            suggestedMax: 15,
                            ticks: { display: false }
                        }
                    }
                }
            });

            const provincialCtx = document.getElementById('provincialChart').getContext('2d');
            new Chart(provincialCtx, {
                type: 'radar',
                data: {
                    labels: ['科技进步', '发明', '专利', '其他'],
                    datasets: [{
                        data: [20, 15, 12, 9],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: 'rgba(16, 185, 129, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(16, 185, 129, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        r: {
                            angleLines: { display: false },
                            suggestedMin: 0,
                            suggestedMax: 25,
                            ticks: { display: false }
                        }
                    }
                }
            });

            const municipalCtx = document.getElementById('municipalChart').getContext('2d');
            new Chart(municipalCtx, {
                type: 'radar',
                data: {
                    labels: ['科技进步', '发明', '专利', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 9],
                        backgroundColor: 'rgba(245, 158, 11, 0.2)',
                        borderColor: 'rgba(245, 158, 11, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(245, 158, 11, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        r: {
                            angleLines: { display: false },
                            suggestedMin: 0,
                            suggestedMax: 40,
                            ticks: { display: false }
                        }
                    }
                }
            });

            const patentCtx = document.getElementById('patentChart').getContext('2d');
            new Chart(patentCtx, {
                type: 'radar',
                data: {
                    labels: ['金奖', '银奖', '优秀奖', '其他'],
                    datasets: [{
                        data: [8, 12, 15, 7],
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 1,
                        pointBackgroundColor: 'rgba(239, 68, 68, 1)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        r: {
                            angleLines: { display: false },
                            suggestedMin: 0,
                            suggestedMax: 20,
                            ticks: { display: false }
                        }
                    }
                }
            });

            // 初始化趋势图
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '国家级',
                            data: [3, 4, 5, 6, 8],
                            borderColor: 'rgba(239, 68, 68, 1)',
                            backgroundColor: 'rgba(239, 68, 68, 0.05)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '省级',
                            data: [8, 10, 12, 15, 18],
                            borderColor: 'rgba(16, 185, 129, 1)',
                            backgroundColor: 'rgba(16, 185, 129, 0.05)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '市级',
                            data: [12, 15, 18, 22, 25],
                            borderColor: 'rgba(245, 158, 11, 1)',
                            backgroundColor: 'rgba(245, 158, 11, 0.05)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化类别分布图
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'bar',
                data: {
                    labels: ['科技进步', '发明', '专利', '其他'],
                    datasets: [{
                        label: '奖项数量',
                        data: [120, 85, 65, 45],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('certificateModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeCertificateModal();
                }
            });
        });
    </script>
</body>
</html>