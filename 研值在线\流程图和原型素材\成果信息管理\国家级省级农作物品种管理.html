<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国家级/省级农作物品种管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .drawer-overlay {
            backdrop-filter: blur(4px);
        }
        .drawer {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .drawer.open {
            transform: translateX(0);
        }
        .mini-chart {
            width: 60px;
            height: 30px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">国家级/省级农作物品种管理</h1>
            <p class="text-gray-600">覆盖从品种基本信息录入、批量导入、细粒度检索到导出及生命周期状态维护的全流程管理</p>
        </div>

        <!-- 筛选检索区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">筛选检索</h2>
                <button id="advancedToggle" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    高级筛选 
                    <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="toggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
            </div>
            
            <!-- 基础筛选 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">审定级别</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="level" value="national" class="mr-2 text-blue-600" checked>
                            <span class="text-sm">国家级</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="level" value="provincial" class="mr-2 text-blue-600">
                            <span class="text-sm">省级</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">作物种类</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部种类</option>
                        <option value="rice">水稻</option>
                        <option value="wheat">小麦</option>
                        <option value="corn">玉米</option>
                        <option value="soybean">大豆</option>
                        <option value="cotton">棉花</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">审定年度</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="开始年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="self-center text-gray-500">-</span>
                        <input type="number" placeholder="结束年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键词搜索</label>
                    <input type="text" placeholder="品种名称、学名、审定编号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>

            <!-- 高级筛选面板 -->
            <div id="advancedPanel" class="hidden border-t pt-4">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">适宜区域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部区域</option>
                            <option value="north">北方地区</option>
                            <option value="south">南方地区</option>
                            <option value="northeast">东北地区</option>
                            <option value="northwest">西北地区</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">权属单位</label>
                        <input type="text" placeholder="权属单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">品种状态</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部状态</option>
                            <option value="approved">已审定</option>
                            <option value="promoting">推广中</option>
                            <option value="review">待复审</option>
                            <option value="expired">已过期</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-wrap gap-3">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 transition-colors">
                    <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置
                </button>
                <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                    <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                    </svg>
                    保存方案
                </button>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="filterByMetric('total')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">已审定品种总量</p>
                        <p class="text-2xl font-bold text-blue-600">2,847</p>
                        <div class="flex items-center mt-1">
                            <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="text-sm text-green-600">+12.5%</span>
                        </div>
                    </div>
                    <canvas class="mini-chart" id="totalChart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="filterByMetric('annual')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">年度新增量</p>
                        <p class="text-2xl font-bold text-green-600">156</p>
                        <div class="flex items-center mt-1">
                            <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="text-sm text-green-600">+8.3%</span>
                        </div>
                    </div>
                    <canvas class="mini-chart" id="annualChart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="filterByMetric('promoting')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">推广中品种占比</p>
                        <p class="text-2xl font-bold text-purple-600">68.2%</p>
                        <div class="flex items-center mt-1">
                            <svg class="w-4 h-4 text-red-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6"></path>
                            </svg>
                            <span class="text-sm text-red-600">-2.1%</span>
                        </div>
                    </div>
                    <canvas class="mini-chart" id="promotingChart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="filterByMetric('review')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">即将复审数量</p>
                        <p class="text-2xl font-bold text-orange-600">89</p>
                        <div class="flex items-center mt-1">
                            <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="text-sm text-green-600">+15.6%</span>
                        </div>
                    </div>
                    <canvas class="mini-chart" id="reviewChart"></canvas>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow" onclick="filterByMetric('coverage')">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">关联主体覆盖率</p>
                        <p class="text-2xl font-bold text-indigo-600">92.4%</p>
                        <div class="flex items-center mt-1">
                            <svg class="w-4 h-4 text-green-500 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
                            </svg>
                            <span class="text-sm text-green-600">+3.7%</span>
                        </div>
                    </div>
                    <canvas class="mini-chart" id="coverageChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 数据表格区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-800">品种明细列表</h2>
                    <div class="flex space-x-3">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                            <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增品种
                        </button>
                        <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors">
                            <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                            </svg>
                            批量导入
                        </button>
                        <button class="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors">
                            <svg class="inline w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导出
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">种名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审定编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">审定年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">适宜区域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">权属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">复审日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">中稻39</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Oryza sativa L.</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国审稻20230001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">长江中下游</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中国农科院</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">推广中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2028-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showVarietyDetail('中稻39')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">豫麦158</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Triticum aestivum L.</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国审麦20230012</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">黄淮海地区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">河南农业大学</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已审定</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2028-06-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showVarietyDetail('豫麦158')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">吉单27</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Zea mays L.</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国审玉20220089</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">东北地区</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">吉林农业大学</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">待复审</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2027-04-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showVarietyDetail('吉单27')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                                <button class="text-purple-600 hover:text-purple-900">关联主体</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">2,847</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-gray-800">图表分析</h2>
                    <button id="chartToggle" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        收起图表
                        <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="chartToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <div id="chartPanel" class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- 作物种类分布饼图 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">作物种类分布</h3>
                        <canvas id="cropTypeChart" width="300" height="300"></canvas>
                    </div>
                    
                    <!-- 年度审定趋势柱形图 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">年度审定趋势</h3>
                        <canvas id="annualTrendChart" width="300" height="300"></canvas>
                    </div>
                    
                    <!-- 复审提醒折线图 -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <h3 class="text-lg font-medium text-gray-800 mb-4">复审提醒</h3>
                        <canvas id="reviewReminderChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 品种详情抽屉 -->
    <div id="drawerOverlay" class="fixed inset-0 bg-black bg-opacity-50 drawer-overlay hidden z-40"></div>
    <div id="varietyDrawer" class="fixed top-0 right-0 h-full w-96 bg-white shadow-xl drawer z-50">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-800">品种详情</h3>
                <button onclick="closeDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <div id="drawerContent" class="p-6 overflow-y-auto h-full pb-20">
            <!-- 动态内容将在这里加载 -->
        </div>
    </div>

    <!-- 工具条 -->
    <div class="fixed bottom-6 right-6 flex flex-col space-y-3">
        <button class="bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-colors" title="模板下载">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
        </button>
        <button class="bg-green-600 text-white p-3 rounded-full shadow-lg hover:bg-green-700 transition-colors" title="数据导出Excel">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
        </button>
        <button class="bg-gray-600 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors" title="刷新缓存">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
        </button>
        
        <!-- 任务进度指示 -->
        <div class="bg-white rounded-lg shadow-lg p-3 text-center">
            <div class="text-xs text-gray-600 mb-1">数据同步</div>
            <div class="w-16 bg-gray-200 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
            </div>
            <div class="text-xs text-gray-500 mt-1">75%</div>
        </div>
    </div>

    <script>
        // 高级筛选切换
        document.getElementById('advancedToggle').addEventListener('click', function() {
            const panel = document.getElementById('advancedPanel');
            const icon = document.getElementById('toggleIcon');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                icon.style.transform = 'rotate(180deg)';
                this.innerHTML = '收起高级筛选 <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="toggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>';
            } else {
                panel.classList.add('hidden');
                icon.style.transform = 'rotate(0deg)';
                this.innerHTML = '高级筛选 <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="toggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>';
            }
        });

        // 图表面板切换
        document.getElementById('chartToggle').addEventListener('click', function() {
            const panel = document.getElementById('chartPanel');
            const icon = document.getElementById('chartToggleIcon');
            
            if (panel.classList.contains('hidden')) {
                panel.classList.remove('hidden');
                icon.style.transform = 'rotate(0deg)';
                this.innerHTML = '收起图表 <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="chartToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>';
            } else {
                panel.classList.add('hidden');
                icon.style.transform = 'rotate(180deg)';
                this.innerHTML = '展开图表 <svg class="inline w-4 h-4 ml-1 transform transition-transform" id="chartToggleIcon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>';
            }
        });

        // 指标卡片点击筛选
        function filterByMetric(type) {
            console.log('筛选类型:', type);
            // 这里可以添加实际的筛选逻辑
        }

        // 显示品种详情抽屉
        function showVarietyDetail(varietyName) {
            const overlay = document.getElementById('drawerOverlay');
            const drawer = document.getElementById('varietyDrawer');
            const content = document.getElementById('drawerContent');
            
            // 模拟品种详情数据
            const varietyData = {
                '中稻39': {
                    name: '中稻39',
                    scientificName: 'Oryza sativa L.',
                    approvalNumber: '国审稻20230001',
                    approvalYear: '2023',
                    suitableArea: '长江中下游',
                    owner: '中国农科院',
                    status: '推广中',
                    reviewDate: '2028-03-15',
                    abstract: '该品种具有高产、优质、抗病等特点，适宜在长江中下游地区种植。',
                    characteristics: '株高适中，分蘖力强，穗大粒多，千粒重较高。',
                    resistance: '抗稻瘟病，中抗白叶枯病。',
                    quality: '米质优良，整精米率高，垩白度低。'
                },
                '豫麦158': {
                    name: '豫麦158',
                    scientificName: 'Triticum aestivum L.',
                    approvalNumber: '国审麦20230012',
                    approvalYear: '2023',
                    suitableArea: '黄淮海地区',
                    owner: '河南农业大学',
                    status: '已审定',
                    reviewDate: '2028-06-20',
                    abstract: '高产稳产小麦品种，适应性强，品质优良。',
                    characteristics: '株型紧凑，抗倒伏能力强，穗粒数多。',
                    resistance: '抗条纹花叶病毒病，中抗赤霉病。',
                    quality: '强筋小麦，蛋白质含量高，面筋质量好。'
                },
                '吉单27': {
                    name: '吉单27',
                    scientificName: 'Zea mays L.',
                    approvalNumber: '国审玉20220089',
                    approvalYear: '2022',
                    suitableArea: '东北地区',
                    owner: '吉林农业大学',
                    status: '待复审',
                    reviewDate: '2027-04-10',
                    abstract: '适宜东北地区种植的玉米杂交种，抗逆性强。',
                    characteristics: '株高中等，穗位适中，籽粒黄色。',
                    resistance: '抗大斑病，中抗丝黑穗病。',
                    quality: '籽粒容重高，淀粉含量适中。'
                }
            };
            
            const data = varietyData[varietyName] || varietyData['中稻39'];
            
            content.innerHTML = `
                <div class="space-y-6">
                    <!-- 基础信息 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">基础信息</h4>
                        <div class="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">种名:</span>
                                <span class="text-sm text-gray-900">${data.name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">学名:</span>
                                <span class="text-sm text-gray-900">${data.scientificName}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">审定编号:</span>
                                <span class="text-sm text-gray-900">${data.approvalNumber}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">审定年度:</span>
                                <span class="text-sm text-gray-900">${data.approvalYear}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">适宜区域:</span>
                                <span class="text-sm text-gray-900">${data.suitableArea}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">权属单位:</span>
                                <span class="text-sm text-gray-900">${data.owner}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">当前状态:</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">${data.status}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-sm font-medium text-gray-600">复审日期:</span>
                                <span class="text-sm text-gray-900">${data.reviewDate}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 品种特性 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">品种特性</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700">${data.characteristics}</p>
                        </div>
                    </div>
                    
                    <!-- 抗性表现 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">抗性表现</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700">${data.resistance}</p>
                        </div>
                    </div>
                    
                    <!-- 品质指标 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">品质指标</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700">${data.quality}</p>
                        </div>
                    </div>
                    
                    <!-- 审定公告 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">审定公告</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <p class="text-sm text-gray-700">${data.abstract}</p>
                            <div class="mt-3">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看完整公告 →</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 证书扫描件 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">证书扫描件</h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <img src="https://source.unsplash.com/400x300?certificate,document" alt="审定证书" class="w-full rounded-lg">
                            <div class="mt-3 text-center">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">下载原件</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 关联主体 -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-800 mb-3">关联创新主体</h4>
                        <div class="space-y-2">
                            <div class="bg-gray-50 rounded-lg p-3 flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">${data.owner}</div>
                                    <div class="text-xs text-gray-500">育种单位</div>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看详情 →</button>
                            </div>
                            <div class="bg-gray-50 rounded-lg p-3 flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">农业农村部种子管理局</div>
                                    <div class="text-xs text-gray-500">审定机构</div>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看详情 →</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            overlay.classList.remove('hidden');
            drawer.classList.add('open');
        }

        // 关闭抽屉
        function closeDrawer() {
            const overlay = document.getElementById('drawerOverlay');
            const drawer = document.getElementById('varietyDrawer');
            
            overlay.classList.add('hidden');
            drawer.classList.remove('open');
        }

        // 点击遮罩层关闭抽屉
        document.getElementById('drawerOverlay').addEventListener('click', closeDrawer);

        // 初始化图表
        function initCharts() {
            // 初始化迷你图表
            initMiniCharts();
            
            // 初始化主要图表
            initMainCharts();
        }

        // 初始化迷你图表
        function initMiniCharts() {
            const chartIds = ['totalChart', 'annualChart', 'promotingChart', 'reviewChart', 'coverageChart'];
            const chartData = [
                [65, 70, 75, 80, 85, 90, 95],
                [20, 25, 30, 35, 40, 45, 50],
                [80, 78, 76, 74, 72, 70, 68],
                [10, 15, 20, 25, 30, 35, 40],
                [85, 87, 89, 91, 93, 95, 92]
            ];
            
            chartIds.forEach((id, index) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['', '', '', '', '', '', ''],
                        datasets: [{
                            data: chartData[index],
                            borderColor: '#3B82F6',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            x: { display: false },
                            y: { display: false }
                        },
                        elements: {
                            point: { radius: 0 }
                        }
                    }
                });
            });
        }

        // 初始化主要图表
        function initMainCharts() {
            // 作物种类分布饼图
            const cropTypeCtx = document.getElementById('cropTypeChart').getContext('2d');
            new Chart(cropTypeCtx, {
                type: 'doughnut',
                data: {
                    labels: ['水稻', '小麦', '玉米', '大豆', '棉花', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 10, 5, 5],
                        backgroundColor: [
                            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#6B7280'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 年度审定趋势柱形图
            const annualTrendCtx = document.getElementById('annualTrendChart').getContext('2d');
            new Chart(annualTrendCtx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '审定数量',
                        data: [120, 135, 150, 165, 180],
                        backgroundColor: '#3B82F6'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 复审提醒折线图
            const reviewReminderCtx = document.getElementById('reviewReminderChart').getContext('2d');
            new Chart(reviewReminderCtx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '待复审数量',
                        data: [15, 20, 25, 30, 35, 40],
                        borderColor: '#F59E0B',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initCharts();
        });
    </script>
</body>
</html>