<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">创新载体管理流程图</text>

  <!-- 阶段一：数据提交与初始校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据提交与初始校验</text>
  
  <!-- 节点1: 用户提交 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">新增或批量导入</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">必填项与唯一性</text>
  </g>

  <!-- 节点3: 生成初始档案 -->
  <g transform="translate(550, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成初始档案</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标记为"待完善"</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 750 200 C 750 225, 650 225, 650 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：自动补全与任务推送 -->
  <text x="700" y="380" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：自动补全与任务推送</text>

  <!-- 节点4: 外部补全 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">外部数据补全</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">调用共享目录</text>
  </g>

  <!-- 节点5: 生成任务 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成维护任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">缺失信息处理</text>
  </g>

  <!-- 节点6: 推送责任人 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送责任人</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">待办中心通知</text>
  </g>

  <!-- 连接线 初始档案 -> 外部补全 -->
  <path d="M 600 320 C 500 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 外部补全 -> 生成任务 -->
  <path d="M 500 455 Q 550 455 600 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 生成任务 -> 推送责任人 -->
  <path d="M 800 455 Q 850 455 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：信息完善与状态更新 -->
  <text x="700" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：信息完善与状态更新</text>

  <!-- 节点7: 责任人补录 -->
  <g transform="translate(250, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">责任人补录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">待办中心操作</text>
  </g>

  <!-- 节点8: 状态更新 -->
  <g transform="translate(550, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标记为"已完善"</text>
  </g>

  <!-- 节点9: 统计刷新 -->
  <g transform="translate(850, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">计算完整度评分</text>
  </g>

  <!-- 连接线 推送 -> 补录 -->
  <path d="M 950 490 C 850 520, 450 550, 350 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 补录 -> 状态更新 -->
  <path d="M 450 625 Q 500 625 550 625" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 状态更新 -> 统计刷新 -->
  <path d="M 750 625 Q 800 625 850 625" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：关联管理与删除操作 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：关联管理与删除操作</text>

  <!-- 节点10: 关联管理 -->
  <g transform="translate(150, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">建立/解除关系</text>
  </g>

  <!-- 节点11: 映射同步 -->
  <g transform="translate(400, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">映射同步</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新反向记录</text>
  </g>

  <!-- 节点12: 删除检查 -->
  <g transform="translate(650, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">关联依赖检查</text>
  </g>

  <!-- 节点13: 逻辑删除 -->
  <g transform="translate(900, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">逻辑删除</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">归档历史数据</text>
  </g>

  <!-- 节点14: 数据治理 -->
  <g transform="translate(1150, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据治理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">定期质量扫描</text>
  </g>

  <!-- 连接线 统计刷新 -> 关联管理 -->
  <path d="M 900 660 C 800 690, 400 720, 250 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 关联管理 -> 映射同步 -->
  <path d="M 350 795 Q 375 795 400 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 映射同步 -> 删除检查 -->
  <path d="M 600 795 Q 625 795 650 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 删除检查 -> 逻辑删除 -->
  <path d="M 850 795 Q 875 795 900 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 逻辑删除 -> 数据治理 -->
  <path d="M 1100 795 Q 1125 795 1150 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线：数据治理 -> 生成任务 -->
  <path d="M 1200 760 C 1300 700, 1300 500, 1200 455 C 1100 455, 900 455, 800 455" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1300" y="600" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 1300, 600)">质量提升清单</text>

</svg>