<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识产权公共服务平台PC端智能问答</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .floating-window {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 400px;
            height: 500px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .floating-trigger {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            z-index: 1001;
            transition: all 0.3s ease;
        }
        .message-slide {
            animation: slideUp 0.3s ease-out;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 模拟的公共服务平台页面 -->
    <div class="flex-1 p-6">
        <div class="h-[calc(100vh-120px)] flex flex-col">
            <!-- 页面标题 -->
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-3xl font-bold text-blue-800 flex items-center">
                    <svg class="mr-3 h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    知识产权公共服务平台PC端智能问答
                </h1>
                <div class="flex gap-3">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                        </svg>
                        甬知AI
                    </button>
                    <button onclick="login()" class="bg-white border border-blue-200 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors">
                        登录
                    </button>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 左侧内容 -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- 功能介绍卡片 -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h2 class="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                            <svg class="mr-2 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            智能问答功能介绍
                        </h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 rounded-lg p-4">
                                <h3 class="font-medium text-blue-800 mb-2">一站式咨询</h3>
                                <p class="text-sm text-gray-600">覆盖知识产权全业务场景，提供专业智能咨询服务</p>
                            </div>
                            <div class="bg-indigo-50 rounded-lg p-4">
                                <h3 class="font-medium text-indigo-800 mb-2">个性化追溯</h3>
                                <p class="text-sm text-gray-600">登录用户可查看历史咨询记录，个性化服务体验</p>
                            </div>
                            <div class="bg-green-50 rounded-lg p-4">
                                <h3 class="font-medium text-green-800 mb-2">匿名友好</h3>
                                <p class="text-sm text-gray-600">支持匿名用户咨询，可随时转为实名用户</p>
                            </div>
                            <div class="bg-purple-50 rounded-lg p-4">
                                <h3 class="font-medium text-purple-800 mb-2">智能监控</h3>
                                <p class="text-sm text-gray-600">实时监控服务状态，异常时自动降级到人工服务</p>
                            </div>
                        </div>
                    </div>

                    <!-- 使用统计 -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h2 class="text-xl font-semibold text-blue-800 mb-4 flex items-center">
                            <svg class="mr-2 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            使用统计
                        </h2>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">1,234</div>
                                <div class="text-sm text-gray-500">今日咨询</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">98.5%</div>
                                <div class="text-sm text-gray-500">满意度</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">24/7</div>
                                <div class="text-sm text-gray-500">在线服务</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧状态面板 -->
                <div class="space-y-6">
                    <!-- 系统状态 -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">系统状态</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">AI服务</span>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span class="text-sm text-green-600">正常</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">知识库</span>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span class="text-sm text-green-600">正常</span>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">对话网关</span>
                                <div class="flex items-center">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 pulse-dot"></div>
                                    <span class="text-sm text-green-600">正常</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户状态 -->
                    <div class="bg-white rounded-xl shadow-md p-6 border border-blue-100">
                        <h3 class="text-lg font-semibold text-blue-800 mb-4">用户状态</h3>
                        <div id="userStatus" class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">登录状态</span>
                                <span class="text-sm text-orange-600">匿名用户</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">会话ID</span>
                                <span class="text-xs text-gray-500 font-mono">UUID-xxx</span>
                            </div>
                        </div>
                        <button onclick="login()" class="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors">
                            转为实名用户
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮窗触发按钮 -->
    <div id="floatingTrigger" class="floating-trigger bg-blue-600 hover:bg-blue-700 rounded-full shadow-lg cursor-pointer flex items-center justify-center text-white" onclick="toggleFloatingWindow()">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
    </div>

    <!-- 智能问答浮窗 -->
    <div id="floatingWindow" class="floating-window bg-white rounded-xl shadow-2xl border border-blue-200 hidden">
        <!-- 浮窗头部 -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-t-xl px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-white font-semibold text-sm">甬知AI</h3>
                    <p class="text-blue-100 text-xs">智能问答助手</p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-2 h-2 bg-green-400 rounded-full pulse-dot"></div>
                <button onclick="toggleFloatingWindow()" class="text-white hover:bg-blue-500 rounded p-1">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- 聊天内容区域 -->
        <div id="chatMessages" class="h-80 overflow-y-auto custom-scrollbar p-4 space-y-3">
            <!-- 欢迎消息 -->
            <div class="message-slide">
                <div class="flex items-start space-x-2">
                    <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                        <p class="text-sm text-gray-800">您好！我是甬知AI智能助手，可以为您提供知识产权相关咨询服务。请问有什么可以帮助您的？</p>
                        <div class="mt-2 text-xs text-gray-500">刚刚</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 输入区域 -->
        <div class="border-t border-gray-200 p-3">
            <div class="flex items-end space-x-2">
                <div class="flex-1">
                    <textarea
                        id="messageInput"
                        placeholder="请输入您的问题..."
                        rows="1"
                        class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onkeydown="handleInputKeyDown(event)"
                    ></textarea>
                </div>
                <button
                    id="sendButton"
                    onclick="sendMessage()"
                    class="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-3 py-2 transition-colors"
                >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 人工服务备用通道弹窗 -->
    <div id="fallbackModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 mx-4 max-w-md w-full shadow-xl">
            <div class="flex items-center mb-4">
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <h3 class="font-semibold text-gray-900">AI服务暂时不可用</h3>
            </div>
            <p class="text-gray-600 text-sm mb-4">很抱歉，智能问答服务暂时不可用。您可以通过以下方式获得帮助：</p>
            <div class="space-y-3">
                <div class="flex items-center p-3 bg-blue-50 rounded-lg">
                    <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">人工热线</div>
                        <div class="text-sm text-gray-600">400-123-4567</div>
                    </div>
                </div>
                <div class="border-t pt-3">
                    <label class="block text-sm font-medium text-gray-700 mb-2">或提交问题表单：</label>
                    <textarea placeholder="请描述您的问题..." rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    <div class="flex justify-end space-x-3 mt-3">
                        <button onclick="hideFallbackModal()" class="px-4 py-2 text-gray-600 hover:text-gray-800">取消</button>
                        <button onclick="submitFallbackForm()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors">提交</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let isFloatingWindowOpen = false;
        let messageCount = 0;
        let errorCount = 0;
        let sessionUUID = 'uuid_' + Date.now();
        let isLoggedIn = false;
        let userProfile = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectDeviceType();
            initializeHeartbeat();
            updateUserStatus();
        });

        // 检测设备类型
        function detectDeviceType() {
            const isMobile = window.innerWidth <= 768;
            if (isMobile) {
                // 移动端适配样式
                document.querySelector('.floating-window').style.width = '90%';
                document.querySelector('.floating-window').style.right = '5%';
            }
        }

        // 切换浮窗显示
        function toggleFloatingWindow() {
            const floatingWindow = document.getElementById('floatingWindow');
            const trigger = document.getElementById('floatingTrigger');
            
            isFloatingWindowOpen = !isFloatingWindowOpen;
            
            if (isFloatingWindowOpen) {
                floatingWindow.classList.remove('hidden');
                trigger.style.display = 'none';
            } else {
                floatingWindow.classList.add('hidden');
                trigger.style.display = 'flex';
            }
        }

        // 处理输入键盘事件
        function handleInputKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // 发送消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            input.value = '';
            messageCount++;
            
            // 模拟AI回复
            simulateAIResponse(message);
        }

        // 添加消息到聊天
        function addMessage(content, type, hasReference = false) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message-slide';
            
            const time = new Date().toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });

            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2 justify-end">
                        <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs">
                            <p class="text-sm">${content}</p>
                            <div class="mt-2 text-xs text-blue-100">${time}</div>
                        </div>
                        <div class="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                    </div>
                `;
            } else {
                const feedbackButtons = hasReference ? `
                    <div class="flex items-center justify-between mt-3 pt-2 border-t border-blue-100">
                        <span class="text-xs text-gray-500">这个回答有帮助吗？</span>
                        <div class="flex space-x-2">
                            <button onclick="feedback(true)" class="text-green-600 hover:bg-green-50 p-1 rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"></path>
                                </svg>
                            </button>
                            <button onclick="feedback(false)" class="text-red-600 hover:bg-red-50 p-1 rounded">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10h2a2 2 0 012 2v6a2 2 0 01-2 2h-2.5"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                ` : '';
                
                messageDiv.innerHTML = `
                    <div class="flex items-start space-x-2">
                        <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014.846 21H9.154a3.374 3.374 0 00-2.869-1.51l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="bg-blue-50 border border-blue-100 rounded-lg p-3 max-w-xs">
                            <p class="text-sm text-gray-800">${content}</p>
                            ${feedbackButtons}
                            <div class="mt-2 text-xs text-gray-500">${time}</div>
                        </div>
                    </div>
                `;
            }
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 模拟AI回复
        function simulateAIResponse(userMessage) {
            setTimeout(() => {
                // 模拟网关异常检测
                if (Math.random() < 0.15) {
                    handleGatewayError();
                    return;
                }
                
                let response = '';
                if (userMessage.includes('专利')) {
                    response = '关于专利申请，您需要准备以下材料：请求书、说明书、权利要求书等。整个流程大约需要18-24个月。具体细节建议您咨询专业代理机构。';
                } else if (userMessage.includes('商标')) {
                    response = '商标注册需要经过形式审查、实质审查、公告等环节，整个周期约9-12个月。建议您先进行商标查询以提高注册成功率。';
                } else {
                    response = '我可以为您提供知识产权相关咨询，包括专利、商标、版权等方面的问题。请具体描述您需要了解的内容。';
                }
                
                addMessage(response, 'assistant', true);
                
                // 记录脱敏日志
                logInteraction(userMessage.substring(0, 20) + '...', Date.now());
                
            }, 1000 + Math.random() * 1000);
        }

        // 处理网关错误
        function handleGatewayError() {
            errorCount++;
            if (errorCount >= 3) {
                showFallbackModal();
                toggleFloatingWindow(); // 隐藏浮窗
            } else {
                addMessage('抱歉，服务暂时不可用，请稍后重试。', 'assistant');
            }
        }

        // 反馈功能
        function feedback(isPositive) {
            const message = isPositive ? '感谢您的反馈！' : '我会继续改进，感谢您的建议。';
            // 这里可以调用反馈服务API
            console.log('用户反馈:', isPositive ? '有帮助' : '无帮助');
        }

        // 登录功能
        function login() {
            // 模拟单点登录
            isLoggedIn = true;
            userProfile = {
                name: '张先生',
                id: 'user_123',
                level: 'VIP用户'
            };
            updateUserStatus();
            
            // 合并匿名对话记录
            console.log('合并UUID对话记录到用户账户');
        }

        // 更新用户状态
        function updateUserStatus() {
            const userStatus = document.getElementById('userStatus');
            if (isLoggedIn) {
                userStatus.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">登录状态</span>
                        <span class="text-sm text-green-600">已登录</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">用户</span>
                        <span class="text-sm text-blue-600">${userProfile.name}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">等级</span>
                        <span class="text-sm text-purple-600">${userProfile.level}</span>
                    </div>
                `;
            } else {
                userStatus.innerHTML = `
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">登录状态</span>
                        <span class="text-sm text-orange-600">匿名用户</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">会话ID</span>
                        <span class="text-xs text-gray-500 font-mono">${sessionUUID.substring(0, 12)}...</span>
                    </div>
                `;
            }
        }

        // 显示备用通道弹窗
        function showFallbackModal() {
            document.getElementById('fallbackModal').classList.remove('hidden');
        }

        // 隐藏备用通道弹窗
        function hideFallbackModal() {
            document.getElementById('fallbackModal').classList.add('hidden');
            errorCount = 0; // 重置错误计数
        }

        // 提交备用表单
        function submitFallbackForm() {
            alert('您的问题已提交，工作人员将尽快联系您！');
            hideFallbackModal();
        }

        // 心跳探针
        function initializeHeartbeat() {
            setInterval(() => {
                // 模拟心跳检测
                console.log('心跳检测 - 服务正常');
            }, 30000);
        }

        // 记录交互日志（脱敏）
        function logInteraction(questionSummary, timestamp) {
            const logData = {
                summary: questionSummary,
                timestamp: timestamp,
                sessionId: sessionUUID,
                userId: isLoggedIn ? userProfile.id : 'anonymous'
            };
            console.log('记录脱敏日志:', logData);
            
            // 7天后自动清理
            setTimeout(() => {
                console.log('清理7天前的日志');
            }, 7 * 24 * 60 * 60 * 1000);
        }

        // 响应式调整
        window.addEventListener('resize', detectDeviceType);
    </script>
</body>
</html> 