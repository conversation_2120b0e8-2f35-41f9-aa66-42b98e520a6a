<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员参与企业家培训情况管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b',
                        success: '#10b981',
                        warning: '#f59e0b',
                        danger: '#ef4444',
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">科研管理人员参与企业家培训情况管理</h1>

        <!-- 主要内容区域 -->
        <div class="flex flex-col lg:flex-row gap-4 h-[calc(100vh-120px)]">
            <!-- 左侧主内容区 -->
            <div class="flex-1 lg:w-full flex flex-col space-y-3">
                <!-- 条件筛选区 -->
                <div class="bg-white shadow-md rounded-lg p-4 mb-3">
                    <h2 class="text-lg font-medium text-gray-800 mb-3">筛选条件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="managerName" class="block text-sm font-medium text-gray-700 mb-0.5">科研管理人员姓名</label>
                            <input type="text" id="managerName" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入姓名">
                        </div>
                        <div>
                            <label for="trainingTopic" class="block text-sm font-medium text-gray-700 mb-0.5">培训主题</label>
                            <input type="text" id="trainingTopic" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训主题">
                        </div>
                        <div>
                            <label for="workUnit" class="block text-sm font-medium text-gray-700 mb-0.5">工作单位</label>
                            <input type="text" id="workUnit" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                        </div>
                        <div>
                            <label for="trainingCategory" class="block text-sm font-medium text-gray-700 mb-0.5">培训类别</label>
                            <select id="trainingCategory" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部</option>
                                <option value="policy">政策解读</option>
                                <option value="management">管理能力</option>
                                <option value="technology">技术创新</option>
                                <option value="market">市场营销</option>
                                <option value="finance">金融投资</option>
                            </select>
                        </div>
                        <div>
                            <label for="trainingTime" class="block text-sm font-medium text-gray-700 mb-0.5">参与培训时间</label>
                            <div class="flex space-x-1">
                                <input type="date" id="startTime" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <span class="flex items-center text-gray-500 text-xs">至</span>
                                <input type="date" id="endTime" class="w-full px-2 py-1.5 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>
                        <div>
                            <label for="trainingInstitution" class="block text-sm font-medium text-gray-700 mb-0.5">培训机构</label>
                            <input type="text" id="trainingInstitution" class="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训机构">
                        </div>
                    </div>
                    <div class="mt-3 flex justify-end space-x-3">
                        <button class="px-4 py-1.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            重置
                        </button>
                        <button class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            查询
                        </button>
                        <button class="px-4 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                            </svg>
                            导出数据
                        </button>
                    </div>
                </div>


                <!-- 主内容区域 -->
                <div class="bg-white shadow-md rounded-lg flex-1 flex flex-col space-y-3">
                    <!-- 培训情况列表区 -->
                    <div class="w-full">
                        <!-- 列表标题区 -->
                        <div class="p-4 border-b border-gray-200">
                            <div class="flex justify-between items-center">
                                <h2 class="text-lg font-medium text-gray-800">培训情况列表</h2>
                                <div class="flex items-center space-x-3">
                                    <div class="relative">
                                        <button id="batchDropdownBtn" class="px-3 py-1.5 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                            <span>批量操作</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                            </svg>
                                        </button>
                                        <div id="batchDropdownMenu" class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                            <div class="py-1">
                                                <a href="#" onclick="batchDelete()" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">批量删除</a>
                                                <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">批量导出</a>
                                            </div>
                                        </div>
                                    </div>
                                    <button onclick="openModal('addTrainingModal')" class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                        新增培训记录
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="flex-1 overflow-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50 sticky top-0">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科研管理人员姓名</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工作单位</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与培训主题</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">培训类别</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">培训机构</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参与培训时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="1">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">张明</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技信息研究院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技创新政策解读与应用</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">政策解读</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省科技管理干部学院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 至 2024-01-17</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="viewTraining('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editTraining('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteTraining('1')" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="2">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">李华</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市高新技术产业开发区</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技型企业管理创新实践</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">管理能力</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江大学管理学院</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-10 至 2024-01-12</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="viewTraining('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editTraining('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteTraining('2')" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="3">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">王芳</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科学技术局</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">人工智能与产业创新发展</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术创新</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">中科院宁波材料所</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05 至 2024-01-08</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="viewTraining('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editTraining('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteTraining('3')" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="4">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">赵强</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波大学科研处</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技成果转化与市场对接</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市场营销</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技成果转化中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-28 至 2023-12-30</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="viewTraining('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editTraining('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteTraining('4')" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="5">
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">陈静</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技创业服务中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技企业融资与资本运作</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">金融投资</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波国家高新区金融服务中心</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-20 至 2023-12-22</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                            <button onclick="viewTraining('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                            <button onclick="editTraining('5')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                            <button onclick="deleteTraining('5')" class="text-red-600 hover:text-red-900">删除</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 分页区域 -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">24</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">4</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">5</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑培训记录弹窗 -->
    <div id="addTrainingModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900" id="modalTitle">新增培训记录</h3>
                    <button onclick="closeModal('addTrainingModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form id="trainingForm" class="space-y-4">
                    <input type="hidden" id="trainingId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="formManagerName" class="block text-sm font-medium text-gray-700 mb-1">科研管理人员姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="formManagerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入科研管理人员姓名">
                        </div>
                        <div>
                            <label for="formWorkUnit" class="block text-sm font-medium text-gray-700 mb-1">工作单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="formWorkUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                        </div>
                        <div>
                            <label for="formTrainingTopic" class="block text-sm font-medium text-gray-700 mb-1">参与培训主题 <span class="text-red-500">*</span></label>
                            <input type="text" id="formTrainingTopic" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入参与培训主题">
                        </div>
                        <div>
                            <label for="formTrainingCategory" class="block text-sm font-medium text-gray-700 mb-1">培训类别 <span class="text-red-500">*</span></label>
                            <select id="formTrainingCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择培训类别</option>
                                <option value="policy">政策解读</option>
                                <option value="management">管理能力</option>
                                <option value="technology">技术创新</option>
                                <option value="market">市场营销</option>
                                <option value="finance">金融投资</option>
                            </select>
                        </div>
                        <div>
                            <label for="formTrainingInstitution" class="block text-sm font-medium text-gray-700 mb-1">培训机构 <span class="text-red-500">*</span></label>
                            <input type="text" id="formTrainingInstitution" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训机构">
                        </div>
                        <div>
                            <label for="formTrainingHours" class="block text-sm font-medium text-gray-700 mb-1">培训时长（小时）</label>
                            <input type="number" id="formTrainingHours" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训时长">
                        </div>
                        <div>
                            <label for="formTrainingStartDate" class="block text-sm font-medium text-gray-700 mb-1">培训开始时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="formTrainingStartDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="formTrainingEndDate" class="block text-sm font-medium text-gray-700 mb-1">培训结束时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="formTrainingEndDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="formTrainingLocation" class="block text-sm font-medium text-gray-700 mb-1">培训地点</label>
                            <input type="text" id="formTrainingLocation" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训地点">
                        </div>
                        <div>
                            <label for="formTrainingCertificate" class="block text-sm font-medium text-gray-700 mb-1">是否获得证书</label>
                            <select id="formTrainingCertificate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="no">否</option>
                                <option value="yes">是</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="formTrainingContent" class="block text-sm font-medium text-gray-700 mb-1">培训主要内容</label>
                        <textarea id="formTrainingContent" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入培训主要内容"></textarea>
                    </div>
                    <div>
                        <label for="formTrainingRemark" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                        <textarea id="formTrainingRemark" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入备注信息"></textarea>
                    </div>
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="closeModal('addTrainingModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            保存记录
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 培训详情弹窗 -->
    <div id="detailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-full max-w-3xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">培训记录详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="space-y-6">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">科研管理人员姓名：</span>
                                <span class="font-medium text-gray-900" id="detailManagerName">张明</span>
                            </div>
                            <div>
                                <span class="text-gray-500">工作单位：</span>
                                <span class="font-medium text-gray-900" id="detailWorkUnit">宁波市科技信息研究院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">参与培训主题：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingTopic">科技创新政策解读与应用</span>
                            </div>
                            <div>
                                <span class="text-gray-500">培训类别：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingCategory">政策解读</span>
                            </div>
                            <div>
                                <span class="text-gray-500">培训机构：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingInstitution">浙江省科技管理干部学院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">培训时长：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingHours">24小时</span>
                            </div>
                            <div>
                                <span class="text-gray-500">培训时间：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingTime">2024-01-15 至 2024-01-17</span>
                            </div>
                            <div>
                                <span class="text-gray-500">培训地点：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingLocation">宁波市科技大厦5楼会议室</span>
                            </div>
                            <div>
                                <span class="text-gray-500">是否获得证书：</span>
                                <span class="font-medium text-gray-900" id="detailTrainingCertificate">是</span>
                            </div>
                            <div>
                                <span class="text-gray-500">记录创建时间：</span>
                                <span class="font-medium text-gray-900" id="detailCreateTime">2024-01-18 09:30:00</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-900 mb-3">培训主要内容</h4>
                        <div class="text-sm text-gray-700" id="detailTrainingContent">
                            本次培训主要围绕最新科技创新政策进行解读，包括国家和地方层面的科技扶持政策、科研项目申报流程、科技成果转化激励政策等内容。通过案例分析和互动讨论，帮助科研管理人员更好地理解和应用相关政策，提升服务企业科技创新的能力。
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-900 mb-3">备注</h4>
                        <div class="text-sm text-gray-700" id="detailTrainingRemark">
                            本次培训由浙江省科技厅主办，宁波市科技信息研究院组织相关人员参加，共计15人参与。培训结束后，参训人员将结合工作实际，开展政策宣讲和企业对接服务。
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="text-md font-semibold text-gray-900 mb-3">历史变更记录</h4>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-100">
                                    <tr>
                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更时间</th>
                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">变更内容</th>
                                        <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr>
                                        <td class="px-4 py-2 whitespace-nowrap text-xs text-gray-500">2024-01-18 09:30:00</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-xs text-gray-700">创建培训记录</td>
                                        <td class="px-4 py-2 whitespace-nowrap text-xs text-gray-700">系统管理员</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-6">
                    <button onclick="closeModal('detailModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        关闭
                    </button>
                    <button id="editFromDetailBtn" onclick="editTrainingFromDetail()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        编辑记录
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
                <p class="text-sm text-gray-500 mb-6" id="deleteConfirmText">您确定要删除这条培训记录吗？此操作不可撤销。</p>
                <input type="hidden" id="deleteTrainingId">
                <div class="flex justify-center space-x-3">
                    <button onclick="closeModal('deleteModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        取消
                    </button>
                    <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
                
                // 如果是编辑/新增模态框，清空表单
                if (modalId === 'addTrainingModal') {
                    document.getElementById('trainingForm').reset();
                    document.getElementById('trainingId').value = '';
                    document.getElementById('modalTitle').textContent = '新增培训记录';
                }
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 批量操作下拉菜单
            const batchDropdownBtn = document.getElementById('batchDropdownBtn');
            const batchDropdownMenu = document.getElementById('batchDropdownMenu');
            
            if (batchDropdownBtn && batchDropdownMenu) {
                batchDropdownBtn.addEventListener('click', function() {
                    batchDropdownMenu.classList.toggle('hidden');
                });
            }
            
            // 全选/取消全选
            const selectAllCheckbox = document.getElementById('selectAll');
            const recordCheckboxes = document.querySelectorAll('.record-checkbox');
            
            if (selectAllCheckbox && recordCheckboxes.length > 0) {
                selectAllCheckbox.addEventListener('change', function() {
                    recordCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
            }
            
            // 点击其他区域关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (batchDropdownMenu && !batchDropdownMenu.classList.contains('hidden') && 
                    !batchDropdownBtn.contains(event.target) && !batchDropdownMenu.contains(event.target)) {
                    batchDropdownMenu.classList.add('hidden');
                }
            });
            
            // 绑定表单提交事件
            const trainingForm = document.getElementById('trainingForm');
            if (trainingForm) {
                trainingForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const trainingId = document.getElementById('trainingId').value;
                    
                    if (trainingId) {
                        // 编辑操作
                        alert('培训记录更新成功！');
                    } else {
                        // 新增操作
                        alert('培训记录添加成功！');
                    }
                    
                    closeModal('addTrainingModal');
                    // 这里可以添加刷新列表的代码
                });
            }
        });

        // --- 培训记录操作函数 ---
        function viewTraining(id) {
            // 在实际应用中，这里会根据ID从服务器获取详细数据
            // 这里仅做演示用
            openModal('detailModal');
        }

        function editTraining(id) {
            // 在实际应用中，这里会根据ID从服务器获取数据并填充表单
            document.getElementById('modalTitle').textContent = '编辑培训记录';
            document.getElementById('trainingId').value = id;
            
            // 这里仅做演示用，实际应用中应该从服务器获取数据
            document.getElementById('formManagerName').value = '张明';
            document.getElementById('formWorkUnit').value = '宁波市科技信息研究院';
            document.getElementById('formTrainingTopic').value = '科技创新政策解读与应用';
            document.getElementById('formTrainingCategory').value = 'policy';
            document.getElementById('formTrainingInstitution').value = '浙江省科技管理干部学院';
            document.getElementById('formTrainingHours').value = '24';
            document.getElementById('formTrainingStartDate').value = '2024-01-15';
            document.getElementById('formTrainingEndDate').value = '2024-01-17';
            document.getElementById('formTrainingLocation').value = '宁波市科技大厦5楼会议室';
            document.getElementById('formTrainingCertificate').value = 'yes';
            document.getElementById('formTrainingContent').value = '本次培训主要围绕最新科技创新政策进行解读，包括国家和地方层面的科技扶持政策、科研项目申报流程、科技成果转化激励政策等内容。';
            document.getElementById('formTrainingRemark').value = '本次培训由浙江省科技厅主办，宁波市科技信息研究院组织相关人员参加。';
            
            openModal('addTrainingModal');
        }

        function editTrainingFromDetail() {
            closeModal('detailModal');
            editTraining(document.getElementById('trainingId').value || '1');
        }

        function deleteTraining(id) {
            document.getElementById('deleteTrainingId').value = id;
            document.getElementById('deleteConfirmText').textContent = '您确定要删除这条培训记录吗？此操作不可撤销。';
            openModal('deleteModal');
        }

        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请先选择要删除的记录');
                return;
            }
            
            document.getElementById('deleteTrainingId').value = ''; // 清空表示批量删除
            document.getElementById('deleteConfirmText').textContent = `您确定要删除选中的 ${checkedBoxes.length} 条培训记录吗？此操作不可撤销。`;
            openModal('deleteModal');
        }

        function confirmDelete() {
            const trainingId = document.getElementById('deleteTrainingId').value;
            
            if (trainingId) {
                // 单个删除
                alert('培训记录删除成功！');
            } else {
                // 批量删除
                alert('选中的培训记录已全部删除！');
            }
            
            closeModal('deleteModal');
            // 这里可以添加刷新列表的代码
        }
    </script>
</body>
</html>