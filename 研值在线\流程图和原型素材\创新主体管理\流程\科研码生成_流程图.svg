<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1500 1100" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="750" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研码生成流程图</text>

  <!-- 阶段一：权限校验与信息核对 -->
  <text x="750" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：权限校验与信息核对</text>
  
  <!-- 节点1: 用户发起请求 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户发起请求</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">科研码生成请求</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">界面操作触发</text>
  </g>

  <!-- 节点2: 权限校验 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限校验</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">创新主体管理权</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">或查看权验证</text>
  </g>

  <!-- 节点3: 信息完整性核对 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">信息完整性核对</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">主体信息状态检查</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">必填要素验证</text>
  </g>

  <!-- 阶段二：科研码生成与存储 -->
  <text x="750" y="300" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：科研码生成与存储</text>

  <!-- 节点4: 创建唯一标识 -->
  <g transform="translate(150, 340)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">创建唯一标识</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">生成科研码ID</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">确保全局唯一性</text>
  </g>

  <!-- 节点5: 码服务生成 -->
  <g transform="translate(400, 340)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">码服务生成</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">二维码图像</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">短链地址</text>
  </g>

  <!-- 节点6: 元数据写入 -->
  <g transform="translate(650, 340)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">元数据写入</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">生命周期信息</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">公开范围设置</text>
  </g>

  <!-- 节点7: 数据库存储 -->
  <g transform="translate(900, 340)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据库存储</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">写入科研码库</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">状态更新记录</text>
  </g>

  <!-- 阶段三：操作管理与访问控制 -->
  <text x="750" y="520" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：操作管理与访问控制</text>

  <!-- 节点8: 删除操作 -->
  <g transform="translate(200, 560)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除操作</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">引用关系检查</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">活跃期验证</text>
  </g>

  <!-- 节点9: 下载分享 -->
  <g transform="translate(450, 560)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下载分享</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">实时读取码图</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">生成分享链接</text>
  </g>

  <!-- 节点10: 公众访问 -->
  <g transform="translate(700, 560)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">公众访问</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">二维码/短链访问</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">权限范围筛选</text>
  </g>

  <!-- 阶段四：审计追踪与统计分析 -->
  <text x="750" y="740" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审计追踪与统计分析</text>

  <!-- 节点11: 操作日志记录 -->
  <g transform="translate(300, 780)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">审计追踪</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">操作历史</text>
  </g>

  <!-- 节点12: 访问统计更新 -->
  <g transform="translate(600, 780)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">访问统计更新</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">计数更新</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">使用分析</text>
  </g>

  <!-- 连接线 -->
  <!-- 阶段一内部连接 -->
  <path d="M 400 170 Q 450 170 500 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 170 Q 750 170 800 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 阶段一到阶段二 -->
  <path d="M 850 210 C 800 250, 400 300, 250 340" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 阶段二内部连接 -->
  <path d="M 350 380 Q 375 380 400 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 380 Q 625 380 650 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 380 Q 875 380 900 380" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 阶段二到阶段三 -->
  <path d="M 500 420 C 400 480, 350 520, 300 560" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 420 C 650 480, 600 520, 550 560" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 420 C 900 480, 850 520, 810 560" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 阶段三到阶段四 -->
  <path d="M 300 640 C 350 700, 350 740, 400 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 640 C 600 700, 650 740, 700 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 810 640 C 750 700, 700 740, 700 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 数据库存储到所有操作的连接 -->
  <path d="M 1000 420 C 1100 500, 1100 600, 920 600" stroke="#333" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />

  <!-- 标注文字 -->
  <text x="450" y="155" font-size="11" fill="#666">验证</text>
  <text x="750" y="155" font-size="11" fill="#666">核对</text>
  <text x="375" y="365" font-size="11" fill="#666">生成</text>
  <text x="625" y="365" font-size="11" fill="#666">配置</text>
  <text x="875" y="365" font-size="11" fill="#666">存储</text>
  <text x="350" y="500" font-size="11" fill="#666">管理</text>
  <text x="600" y="500" font-size="11" fill="#666">服务</text>
  <text x="850" y="500" font-size="11" fill="#666">展示</text>
  <text x="450" y="720" font-size="11" fill="#666">记录</text>
  <text x="700" y="720" font-size="11" fill="#666">统计</text>
  <text x="1050" y="550" font-size="10" fill="#666">状态同步</text>

</svg>