unit LoadingCloseThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingCloseFrm;

type
  TThreadCloseModel = class(TThread)
  private
    FLoadingCloseForm: TLoadingCloseForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingCloseForm: TLoadingCloseForm read FLoading<PERSON>loseForm
      write FLoadingCloseForm;

  end;

var
  LoadingCloseForm: TLoadingCloseForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadCloseModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadCloseModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
  // LoadingForm.RxGIFAnimator1.Animate := true;
  // LoadingForm.Update;
end;

procedure TThreadCloseModel.DoTerminate;
begin
  LoadingCloseForm.Close;
end;

end.
