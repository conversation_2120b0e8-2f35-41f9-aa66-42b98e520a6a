<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">仪器设备展示模块流程图</text>

  <!-- 阶段一：页面初始化与权限验证 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：页面初始化与权限验证</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">仪器设备展示模块</text>
  </g>

  <!-- 节点2: 权限验证与数据加载 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限验证与数据加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载设备主数据，计算聚合指标</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选查询与数据同步 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选查询与数据同步</text>

  <!-- 节点3: 筛选条件设置 -->
  <g transform="translate(200, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设置</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按来源、分类、状态筛选</text>
  </g>

  <!-- 节点4: 设备资产服务 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设备资产服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">处理过滤参数，返回列表数据</text>
  </g>

  <!-- 节点5: 统计图表更新 -->
  <g transform="translate(1000, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计图表更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新卡片指标与图表</text>
  </g>

  <!-- 连接线 权限验证 -> 筛选条件 -->
  <path d="M 650 320 C 550 350, 400 380, 300 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 筛选条件 -> 设备资产服务 -->
  <path d="M 400 455 C 450 455, 550 455, 600 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 设备资产服务 -> 统计图表更新 -->
  <path d="M 800 455 C 850 455, 950 455, 1000 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：详情查看与交互 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：详情查看与交互</text>
  
  <!-- 节点6: 详情数据拉取 -->
  <g transform="translate(200, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情数据拉取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">设备清册或单台详情</text>
  </g>

  <!-- 节点7: 侧栏交互 -->
  <g transform="translate(600, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">侧栏交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标签切换，资料下载</text>
  </g>

  <!-- 节点8: 可视化分析 -->
  <g transform="translate(1000, 620)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化分析</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时聚合数据生成图表</text>
  </g>

  <!-- 连接线 统计图表 -> 详情数据 -->
  <path d="M 1000 490 C 900 520, 400 550, 300 620" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情数据 -> 侧栏交互 -->
  <path d="M 400 655 C 450 655, 550 655, 600 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 侧栏交互 -> 可视化分析 -->
  <path d="M 800 655 C 850 655, 950 655, 1000 655" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：导出服务与日志记录 -->
  <text x="700" y="780" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：导出服务与日志记录</text>
  
  <!-- 节点9: 导出服务 -->
  <g transform="translate(400, 820)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出服务与日志记录</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">生成PDF/Excel文件，记录操作日志，满足审计需求</text>
  </g>

  <!-- 连接线 可视化分析 -> 导出服务 -->
  <path d="M 1000 690 C 900 750, 800 780, 700 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：指标刷新 -->
  <path d="M 1100 420 C 1150 350, 1150 300, 800 250" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="330" text-anchor="middle" font-size="11" fill="#666">指标刷新</text>

  <!-- 反馈循环：操作日志 -->
  <path d="M 500 820 C 100 750, 100 500, 200 490" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="650" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 150, 650)">操作日志记录</text>

</svg>