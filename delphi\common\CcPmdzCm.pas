unit CcPmdzCm;

interface

uses
  Classes;

type
  TCcPmdzCm = class
  private
    FCcPmdzCmid: Integer;
    FCcPmCmid: Integer;
    FKsbm: string;
    FCm: string;
    FZbbh: string;
    FZbnr: string;
    FZbksbm: string;
    FZbbcl: double;
    FZbsl: double;
    FXmbh: string;
    FXmbcl: double;
    FXmsl: double;
    FYmbh: string;
    FYmbcl: double;
    FYmsl: double;
    FYm1bh: string;
    FYm1bcl: double;
    FYm1sl: double;
    FTbbh: string;
    FTbbcl: double;
    FTb1bh: string;
    FTb1bcl: double;
    FTbsl: double;
    FNkxh: string;
    FNklb: string;
    FNkys: string;
    FNkdjyl: double;
    FDlxZs: string;
    FDlxZsdjyl: double;
    FDlxFs1: string;
    FDlxFs1djyl: double;
    FDlxFs2: string;
    FDlxFs2djyl: double;
    FDlxFs3: string;
    FDlxFs3djyl: double;
    FNlxGt: string;
    FNlxGtdjyl: double;
    FNlxDt: string;
    FNlxDtdjyl: double;
    FZxJx: double;
    FZxBh: string;
    FZxGgc: double;
    FZxGgk: double;
    FZxGgg: double;
    FZxMz: double;
    FZxJz: double;
    FTjJx: double;
    FTjBh: string;
    FTjGgc: double;
    FTjGgk: double;
    FTjGgg: double;
    FTjMz: double;
    FTjJz: double;
    FTmBh: string;
    FTmMxylB: double;
    FTmMxylS: double;
    FTmBcl: double;
    FXdrq: string;
    FDdh: string;
    FKz: string;
    FMl: string;
    FKs: string;
    FLb: string;
    FYs: string;
    FDds: double;
    FVicDds: double;
    FQldDds: double;
    FWaDds: double;
    FNzlDds: double;
    FAklDds: double;
    FNswDds: double;
    FXcmDds: double;
    FVicTj: double;
    FQldTj: double;
    FWaTj: double;
    FNzlTj: double;
    FNswTj: double;
    FAklTj: double;
    FZwpm: string;
    FYwpm: string;
    FBgbh: string;
    FYwbh: string;
    FTm: string;
    FKsbj: string;
    FKsbh: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
    FZxdms: string;
    FMjdj: double;
  public
    property CcPmdzCmid: Integer read FCcPmdzCmid write FCcPmdzCmid;
    property CcPmCmid: Integer read FCcPmCmid write FCcPmCmid;
    property Ksbm: string read FKsbm write FKsbm;
    property Cm: string read FCm write FCm;
    property Zbbh: string read FZbbh write FZbbh;
    property Zbnr: string read FZbnr write FZbnr;
    property Zbksbm: string read FZbksbm write FZbksbm;
    property Zbbcl: double read FZbbcl write FZbbcl;
    property Zbsl: double read FZbsl write FZbsl;
    property Xmbh: string read FXmbh write FXmbh;
    property Xmbcl: double read FXmbcl write FXmbcl;
    property Xmsl: double read FXmsl write FXmsl;
    property Ymbh: string read FYmbh write FYmbh;
    property Ymbcl: double read FYmbcl write FYmbcl;
    property Ymsl: double read FYmsl write FYmsl;
    property Ym1bh: string read FYm1bh write FYm1bh;
    property Ym1bcl: double read FYm1bcl write FYm1bcl;
    property Ym1sl: double read FYm1sl write FYm1sl;
    property Tbbh: string read FTbbh write FTbbh;
    property Tbbcl: double read FTbbcl write FTbbcl;
    property Tb1bh: string read FTb1bh write FTb1bh;
    property Tb1bcl: double read FTb1bcl write FTb1bcl;
    property Tbsl: double read FTbsl write FTbsl;
    property Nkxh: string read FNkxh write FNkxh;
    property Nklb: string read FNklb write FNklb;
    property Nkys: string read FNkys write FNkys;
    property Nkdjyl: double read FNkdjyl write FNkdjyl;
    property DlxZs: string read FDlxZs write FDlxZs;
    property DlxZsdjyl: double read FDlxZsdjyl write FDlxZsdjyl;
    property DlxFs1: string read FDlxFs1 write FDlxFs1;
    property DlxFs1djyl: double read FDlxFs1djyl write FDlxFs1djyl;
    property DlxFs2: string read FDlxFs2 write FDlxFs2;
    property DlxFs2djyl: double read FDlxFs2djyl write FDlxFs2djyl;
    property DlxFs3: string read FDlxFs3 write FDlxFs3;
    property DlxFs3djyl: double read FDlxFs3djyl write FDlxFs3djyl;
    property NlxGt: string read FNlxGt write FNlxGt;
    property NlxGtdjyl: double read FNlxGtdjyl write FNlxGtdjyl;
    property NlxDt: string read FNlxDt write FNlxDt;
    property NlxDtdjyl: double read FNlxDtdjyl write FNlxDtdjyl;
    property ZxJx: double read FZxJx write FZxJx;
    property ZxBh: string read FZxBh write FZxBh;
    property ZxGgc: double read FZxGgc write FZxGgc;
    property ZxGgk: double read FZxGgk write FZxGgk;
    property ZxGgg: double read FZxGgg write FZxGgg;
    property ZxMz: double read FZxMz write FZxMz;
    property ZxJz: double read FZxJz write FZxJz;
    property TjJx: double read FTjJx write FTjJx;
    property TjBh: string read FTjBh write FTjBh;
    property TjGgc: double read FTjGgc write FTjGgc;
    property TjGgk: double read FTjGgk write FTjGgk;
    property TjGgg: double read FTjGgg write FTjGgg;
    property TjMz: double read FTjMz write FTjMz;
    property TjJz: double read FTjJz write FTjJz;
    property TmBh: string read FTmBh write FTmBh;
    property TmMxylB: double read FTmMxylB write FTmMxylB;
    property TmMxylS: double read FTmMxylS write FTmMxylS;
    property TmBcl: double read FTmBcl write FTmBcl;
    property Xdrq: string read FXdrq write FXdrq;
    property Ddh: string read FDdh write FDdh;
    property Kz: string read FKz write FKz;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Lb: string read FLb write FLb;
    property Ys: string read FYs write FYs;
    property Dds: double read FDds write FDds;
    property VicDds: double read FVicDds write FVicDds;
    property QldDds: double read FQldDds write FQldDds;
    property WaDds: double read FWaDds write FWaDds;
    property NzlDds: double read FNzlDds write FNzlDds;
    property AklDds: double read FAklDds write FAklDds;
    property NswDds: double read FNswDds write FNswDds;
    property XcmDds: double read FXcmDds write FXcmDds;
    property VicTj: double read FVicTj write FVicTj;
    property QldTj: double read FQldTj write FQldTj;
    property WaTj: double read FWaTj write FWaTj;
    property NzlTj: double read FNzlTj write FNzlTj;
    property NswTj: double read FNswTj write FNswTj;
    property AklTj: double read FAklTj write FAklTj;
    property Zwpm: string read FZwpm write FZwpm;
    property Ywpm: string read FYwpm write FYwpm;
    property Bgbh: string read FBgbh write FBgbh;
    property Ywbh: string read FYwbh write FYwbh;
    property Tm: string read FTm write FTm;
    property Ksbj: string read FKsbj write FKsbj;
    property Ksbh: string read FKsbh write FKsbh;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
    property Zxdms: string read FZxdms write FZxdms;
    property Mjdj: double read FMjdj write FMjdj;
  end;

implementation

end.
