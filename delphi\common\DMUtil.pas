unit DMUtil;

interface

uses
  System.SysUtils, System.Classes, Data.DB, MemDS, DBAccess, Uni, Uni<PERSON><PERSON><PERSON>,
  MySQLUniProvider, <PERSON>z<PERSON><PERSON><PERSON>, Vcl.Forms;

type
  TDataModule1 = class(TDataModule)
    UniDataSource1: TUniDataSource;
    UniQuery1: TUniQuery;
    MySQLUniProvider1: TMySQLUniProvider;
    UniConnection1: TUniConnection;
    UniTransaction1: TUniTransaction;
    procedure DataModuleCreate(Sender: TObject);
    procedure DataModuleDestroy(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure initConfig();
  end;

var
  DataModule1: TDataModule1;
  Jqm: string;
  UserInfoNo: string;
  UserInfoName: string;
  ProviderName: string;
  Server: string;
  Port: integer;
  Username: string;
  Password: string;
  Database: string;

implementation

{ %CLASSGROUP 'System.Classes.TPersistent' }

{$R *.dfm}

procedure TD<PERSON><PERSON>odule1.DataModuleCreate(Sender: TObject);
begin
  initConfig;
  self.UniConnection1.ProviderName := ProviderName;
  self.UniConnection1.Server := Server;
  self.UniConnection1.Port := Port;
  self.UniConnection1.Username := Username;
  self.UniConnection1.Password := Password;
  self.UniConnection1.Database := Database;
end;

procedure TDataModule1.DataModuleDestroy(Sender: TObject);
begin
  self.UniConnection1.Free;
end;

procedure TDataModule1.initConfig;
var
  RzRegIniFile: TRzRegIniFile;
  StringList: TStringList;
  i: integer;
begin
  RzRegIniFile := TRzRegIniFile.Create(Application);

  RzRegIniFile.Path := ExtractFilePath(Application.ExeName) + '\' +
    'Config.ini';

  StringList := TStringList.Create;
  RzRegIniFile.ReadSections(StringList);

  begin
    for i := 0 to StringList.Count - 1 do
    begin
      if i = 0 then
      begin
        Jqm := RzRegIniFile.ReadString(StringList.Strings[i], 'Jqm', '');
        UserInfoNo := RzRegIniFile.ReadString(StringList.Strings[i],
          'UserInfoNo', '');
        UserInfoName := RzRegIniFile.ReadString(StringList.Strings[i],
          'UserInfoName', '');
      end;

      if i = 1 then
      begin
        ProviderName := RzRegIniFile.ReadString(StringList.Strings[i],
          'ProviderName', '');
        Server := RzRegIniFile.ReadString(StringList.Strings[i], 'Server', '');
        Port := StrToInt(RzRegIniFile.ReadString(StringList.Strings[i],
          'Port', ''));
        Username := RzRegIniFile.ReadString(StringList.Strings[i],
          'Username', '');
        Password := RzRegIniFile.ReadString(StringList.Strings[i],
          'Password', '');
        Database := RzRegIniFile.ReadString(StringList.Strings[i],
          'Database', '');
      end;

    end;
  end;

end;

end.
