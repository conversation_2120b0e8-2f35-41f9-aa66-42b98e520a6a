<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">项目总览分析业务流程</text>

  <!-- 阶段一：数据采集与同步 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据采集与同步</text>
  
  <!-- 节点1: 数据同步汇聚 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据同步汇聚</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">科技计划项目、创新主体自研项目</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">高新投资项目等全市范围数据</text>
  </g>

  <!-- 阶段二：数据处理与分析 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据处理与分析</text>

  <!-- 节点2: 结构化整理 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">结构化整理与分类</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">按项目类型、区域、单位类型</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">等维度进行分组与归档</text>
  </g>

  <!-- 节点3: 多维度统计分析 -->
  <g transform="translate(950, 320)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多维度统计分析</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">项目经费、产学研合作</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">进展状态等核心指标分析</text>
  </g>

  <!-- 阶段三：交互展示与操作 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：交互展示与操作</text>

  <!-- 节点4: 交互式展示 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">交互式展示</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">用户选择分析维度或筛选条件</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">实时刷新统计结果及图表</text>
  </g>

  <!-- 节点5: 下钻联动 -->
  <g transform="translate(575, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下钻联动</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">点击图表下钻到项目清册</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">关联企业、人才、成果信息</text>
  </g>

  <!-- 节点6: 数据导出 -->
  <g transform="translate(950, 520)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">导出分析结果、项目清册</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">或明细数据用于汇报归档</text>
  </g>

  <!-- 阶段四：闭环管理 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：闭环管理</text>
  
  <!-- 节点7: 全链路管理 -->
  <g transform="translate(500, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">全链路项目管理决策支持</text>
    <text x="200" y="55" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">数据驱动决策</tspan>
      <tspan dx="60">跨模块联动</tspan>
      <tspan dx="60">持续优化</tspan>
    </text>
  </g>

  <!-- 连接线 1 -> 2,3 (曲线) -->
  <path d="M 650 210 C 600 250, 400 280, 325 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 210 C 800 250, 1000 280, 1075 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 2,3 -> 4,5,6 (曲线) -->
  <path d="M 325 400 C 325 460, 325 480, 325 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 400 C 700 460, 700 480, 700 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1075 400 C 1075 460, 1075 480, 1075 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4,5,6 -> 7 (曲线) -->
  <path d="M 325 600 C 400 650, 500 680, 550 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 600 C 700 650, 700 680, 700 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1075 600 C 1000 650, 900 680, 850 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从全链路管理回到数据同步 -->
  <path d="M 500 760 C 200 820, 100 400, 200 200 C 300 100, 500 80, 650 130" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="850" font-size="12" fill="#666">持续优化反馈</text>

</svg>