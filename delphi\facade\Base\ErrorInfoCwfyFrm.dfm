object ErrorInfoCwfyForm: TErrorInfoCwfyForm
  Left = 330
  Top = 209
  BorderIcons = [biSystemMenu]
  BorderStyle = bsSingle
  ClientHeight = 292
  ClientWidth = 414
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = 12
  Font.Name = #23435#20307
  Font.Style = []
  OldCreateOrder = False
  Position = poScreenCenter
  OnShow = FormShow
  PixelsPerInch = 96
  TextHeight = 12
  object RzPanel1: TRzPanel
    Left = 0
    Top = 0
    Width = 414
    Height = 259
    Align = alClient
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = 12
    Font.Name = #23435#20307
    Font.Style = []
    GradientColorStyle = gcsCustom
    GradientColorStop = 15724527
    GradientDirection = gdDiagonalDown
    ParentFont = False
    TabOrder = 0
    VisualStyle = vsGradient
    object L_Info3: TRzLabel
      Left = 18
      Top = 72
      Width = 381
      Height = 25
      Caption = #35843#25972#39044#35745#31246#31649#36153#21518#65292#20250#37325#26032#35745#31639#21508#35746#21333#30340#21033#28070#25968#25454
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clRed
      Font.Height = -15
      Font.Name = #23435#20307
      Font.Style = [fsBold]
      ParentFont = False
      Transparent = True
      WordWrap = True
    end
    object RzPanel3: TRzPanel
      Left = 0
      Top = 0
      Width = 414
      Height = 49
      Align = alTop
      BorderOuter = fsNone
      Color = 13804661
      TabOrder = 0
      object Image1: TImage
        Left = 10
        Top = 9
        Width = 32
        Height = 32
        Picture.Data = {
          0A54504E474F626A65637489504E470D0A1A0A0000000D494844520000002000
          0000200806000000737A7AF4000007124944415478DABD577B7015D519FF9DB3
          8FBBF79D10BC89404878051B2A23A02D10884AAB8D9484C78C32ADA8631F334E
          5B674A2B5694D48A1607414767AA53B09D02E68F32D319D02AD3D241AA4EEB4C
          471D1B21E51149208428B924F726F7BD8FD36FEFAE4908B937D7FED1B3F3CDEE
          D93DE7FB7EE7F73DCE5986125ACD341F9A6F0DA1E5B63016CE05C2BE2C602549
          7480314008807B002980F63339B477026FBC9BC29FFF7E6552DDACB8612F9E78
          B00CF7DD2591318346CB24922B9C0CBB1A6C0076132609811234565838FF39F0
          CC3E1D6D47625F1EC0A6BBC2D8F5633FC27E52CA155A281997E44AC0580F3376
          2BB8560791AD254B3298720956AE1BCCF74F70FF21027BC206256C30E0F8A4CB
          8F7B1EEFC1F94BA9D200EC6DBD019B56A59C157132CCA5D93007B733EED9C8B4
          1A7AA19272DB0D69E469601E22C44B77329AFB8C48E8FF1778F049C2F697BC42
          72552C1340D3C3DD68FF34571CC0AE9FDD881F7D3B411F4CF76BF611D2B09DF9
          EABCD0A31099F3D4CD15804E6E512B4120011B88317C102CF0102D24264C1DF1
          94174D8F0EA1FD74746200CD2B23F8E3368DF4D8ABE2E4E8DC01A684BECB9842
          3177DA5E4A2931EB28F64C23A9A279674F8107EE84603DB02CB49FF7A3E9911E
          C487D2570308075574B4D523AC46C936B3E96C639ED0BD221723575F9E60B11E
          28D50FD28AAB903D79903C75CA017ED5180D52E82B30874E7732B57299303351
          580C3B0E0AECD8D7733580C71FA8C1D6BB0519A75532EBE74C5677C3CC90F16B
          53893C0D6D491BE4E91BF37D73F832627F5800AD363E0150053C340FD6D0B9E3
          50AF5B25F434E20960C1F7FB114F1AA3007A0FDD8290D44701ADD50A3DDAC1B5
          295E2B71B100BF12FCCD8374F38CBC1AD87B07D4C8BB4E6A8E6F8A1F4C0D5180
          661E22447B8461E0B1FD0CAF1CBEEC0058B9A812479E0AD3E4A49DC3FBB93778
          BF3974C1293213D9A798F0AF1F7452D36D83FBEE811C7C1D85329B0766503CF4
          F531395C2B4C23F7496F391A1E3EE38CDE7AEF4C6CDD40F42BCA5491EDBFC4D4
          8022328385238C723CB0816245098C32F0BB6628A1BFA170696154262A8885EC
          FDC4D26B90A7A07A53B733FAC8CEC56898FD19AD88FD8049E2552B3D80A24592
          EA43605D27B8AF7AE4D5959796408974149DC7BD6544B0F93AE15F07E6C79A67
          92CEE8B79EAE43C3DC3875C40102709F954DA068A3321B68FE085259FD0823D1
          DDB3A15CFF79D169C42CA067625082E5A4026B7E9D7000BCF9643556CC4D53A9
          F7BE0F2BB154E8D9E2F6E90A341D855CB9C2E9E792046026D4E999E2C0EDAA4A
          B58429E5116164FAD73CEB32F066EB0C34CC49DBD452E459D5B04C4CD67CAB5E
          835ABB3EFF6CC67A31F04A3D94AAC9E7E53731262D22341F373F9776016C9B81
          E5B3A823CC3E611955936B21008DBF8167FE03F967BDF7DF88B535525C955829
          99B484D8F8A86577CA05F0C44C2CAFA18EAC7D2872F1C5A5E8F07EFD2968376D
          CE3F67FE731489B736420A4ECE8010B60B42B45998175A9EFF22067E391FCB66
          D841C80E093DB9AE1400EA9CB5087CEB40FE79F8E84EE43A9EA52013A54CD599
          A78C361CCB6A7961C8CD82ED3763692545B0656E819E7CAE441AE15DD64A6784
          1092C7B683ABB192A641F6BD47B9D8C8BC61B4BCE80278ECEE5A6CF926AD9F6B
          F3CCE1DE33CC0E94490170A875EBF225367BF230812F11801AD84CDBF98B3C18
          C1AC9F9C7500ACB8B10A6FFC742A954ABBFAB1B729AD6E2FEE480BC1D57BE0A9
          773623237A0E037B56D2E6979E04344B3349ADA163437F47BC028DAD2747CB56
          F7DE0604323D76B568B452D17758B14A48F95CB1B997BCA08CBC1A78753D91F0
          CEC49BD108FDDA2E4AB547992461DB110BBFFD6BDFA8955F6C98832DAB386DBF
          F6B94DFEBDC80E7FAF2001744DF9E1FB90A7CE77FA74DA893E7F0B05764F31D0
          5DCCE35F48552B3164A858FC2B3A94A4C66CC761BF071FEF5C8440B68F149A54
          33E57F10888585F4491573E0BFBD95682F47F2BD97A1771DCD9FC82636CE935C
          0BDD26ACEC074CF561F771033B0F9F73BC3276DCEA9BAFC7FE4D610269FB9247
          849E7B1B4666414126DCE3386345DCC578128AB696C13A06854E5DB120D6BED4
          89F870CAFD3C762CC9752115CB667BF0D52A09F3225AD9D7A6F3036139DB5CD4
          4881A60BE9D3F62BEAC6B37DC31F76462D7C705147FBC51C32FA68C1B27F31AE
          698AC458242861D6140935E512FBC60DE1EF2C9F2E76041463E624FF326E8CB0
          D489A8F4F2F12EE3E953971289AE011317060DC4D3D635219AFFC7817DD00734
          F72EBB629323B977EEF7C8724BBDB6BAA9CED3B4B45ABEC9279915F94C618E2B
          4CF0CC89CB56C7B173C6B13FB5A70FF50CE60F93962BA67B375CB17F0EECAD33
          6703B00D0449CA49E8EF029E7120B8EB9DAB645A99E68BF8514179C387741EEF
          8E666286950F8AF1628D336EEFF57690D94567F80B17B0B1AB1D67F81A00F6BA
          ED933B777B360336B99640210063818C65454C1803FFCFC6BEF487FFB1152A90
          FF0539DEC83ECB6A3BA90000000049454E44AE426082}
      end
      object L_Info1: TRzLabel
        Left = 64
        Top = 18
        Width = 68
        Height = 14
        Caption = #25805#20316' '#25552#31034
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWhite
        Font.Height = 14
        Font.Name = #23435#20307
        Font.Style = [fsBold]
        ParentColor = False
        ParentFont = False
        Transparent = True
      end
    end
  end
  object RzPanel2: TRzPanel
    Left = 0
    Top = 259
    Width = 414
    Height = 33
    Align = alBottom
    BorderOuter = fsNone
    Color = 15724527
    TabOrder = 1
    object Btn_Save: TAdvGlowButton
      Left = 317
      Top = 2
      Width = 82
      Height = 28
      Caption = #30830'  '#23450
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -16
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      Spacing = 10
      TabOrder = 0
      OnClick = Btn_SaveClick
      Appearance.BorderColor = 12631218
      Appearance.BorderColorHot = 10079963
      Appearance.BorderColorDown = 4548219
      Appearance.ColorTo = 16316405
      Appearance.ColorChecked = 7915518
      Appearance.ColorCheckedTo = 11918331
      Appearance.ColorDisabled = 16316405
      Appearance.ColorDisabledTo = 16316405
      Appearance.ColorDown = 12631218
      Appearance.ColorDownTo = 12631218
      Appearance.ColorHot = 12631218
      Appearance.ColorHotTo = 12631218
      Appearance.ColorMirror = 16316405
      Appearance.ColorMirrorTo = 16316405
      Appearance.ColorMirrorHot = 12631218
      Appearance.ColorMirrorHotTo = 12631218
      Appearance.ColorMirrorDown = 12631218
      Appearance.ColorMirrorDownTo = 12631218
      Appearance.ColorMirrorChecked = 10480637
      Appearance.ColorMirrorCheckedTo = 5682430
      Appearance.ColorMirrorDisabled = 11974326
      Appearance.ColorMirrorDisabledTo = 15921906
      Appearance.GradientHot = ggVertical
      Appearance.GradientMirrorHot = ggVertical
      Appearance.GradientDown = ggVertical
      Appearance.GradientMirrorDown = ggVertical
      Appearance.GradientChecked = ggVertical
      Appearance.SystemFont = False
      Appearance.TextColorDown = clWhite
      Appearance.TextColorHot = clWhite
    end
  end
end
