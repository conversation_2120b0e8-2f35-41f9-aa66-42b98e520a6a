{"BFS处理步骤": [{"当前队列": ["(1, 6) 步数: 0", "(2, 1) 步数: 0", "(3, 3) 步数: 0", "(4, 2) 步数: 0", "(4, 3) 步数: 0"], "出队操作": ["(1,6) 步数: 0"], "地图状态": ["# # . . 1 H", "H . . . . 1", ". . H . # .", ". H H . . .", ". # # # . ."]}, {"当前队列": ["(2, 1) 步数: 0", "(3, 3) 步数: 0", "(4, 2) 步数: 0", "(4, 3) 步数: 0", "(2, 6) 步数: 1", "(1, 5) 步数: 1"], "出队操作": ["(2,1) 步数: 0"], "地图状态": ["# # . . 1 H", "H 1 . . . 1", "1 . H . # .", ". H H . . .", ". # # # . ."]}, {"当前队列": ["(3, 3) 步数: 0", "(4, 2) 步数: 0", "(4, 3) 步数: 0", "(2, 6) 步数: 1", "(1, 5) 步数: 1", "(2, 2) 步数: 1", "(3, 1) 步数: 1"], "出队操作": ["(3,3) 步数: 0"], "地图状态": ["# # . . 1 H", "H 1 1 . . 1", "1 1 H 1 # .", ". H H . . .", ". # # # . ."]}, {"当前队列": ["(4, 2) 步数: 0", "(4, 3) 步数: 0", "(2, 6) 步数: 1", "(1, 5) 步数: 1", "(2, 2) 步数: 1", "(3, 1) 步数: 1", "(3, 4) 步数: 1", "(3, 2) 步数: 1", "(2, 3) 步数: 1"], "出队操作": ["(4,2) 步数: 0"], "地图状态": ["# # . . 1 H", "H 1 1 . . 1", "1 1 H 1 # .", "1 H H . . .", ". # # # . ."]}, {"当前队列": ["(4, 3) 步数: 0", "(2, 6) 步数: 1", "(1, 5) 步数: 1", "(2, 2) 步数: 1", "(3, 1) 步数: 1", "(3, 4) 步数: 1", "(3, 2) 步数: 1", "(2, 3) 步数: 1", "(4, 1) 步数: 1"], "出队操作": ["(4,3) 步数: 0"], "地图状态": ["# # . . 1 H", "H 1 1 . . 1", "1 1 H 1 # .", "1 H H 1 . .", ". # # # . ."]}, {"当前队列": ["(2, 6) 步数: 1", "(1, 5) 步数: 1", "(2, 2) 步数: 1", "(3, 1) 步数: 1", "(3, 4) 步数: 1", "(3, 2) 步数: 1", "(2, 3) 步数: 1", "(4, 1) 步数: 1", "(4, 4) 步数: 1"], "出队操作": ["(2,6) 步数: 1"], "地图状态": ["# # . . 1 H", "H 1 1 . 1 1", "1 1 H 1 # 1", "1 H H 1 . .", ". # # # . ."]}, {"当前队列": ["(1, 5) 步数: 1", "(2, 2) 步数: 1", "(3, 1) 步数: 1", "(3, 4) 步数: 1", "(3, 2) 步数: 1", "(2, 3) 步数: 1", "(4, 1) 步数: 1", "(4, 4) 步数: 1", "(3, 6) 步数: 2", "(2, 5) 步数: 2"], "出队操作": ["(1,5) 步数: 1"], "地图状态": ["# # . 1 1 H", "H 1 1 . 1 1", "1 1 H 1 # 1", "1 H H 1 . .", ". # # # . ."]}, {"当前队列": ["(3, 4) 步数: 1", "(3, 2) 步数: 1", "(2, 3) 步数: 1", "(4, 1) 步数: 1", "(4, 4) 步数: 1", "(3, 6) 步数: 2", "(2, 5) 步数: 2", "(1, 4) 步数: 2"], "出队操作": ["(3,4) 步数: 1"], "地图状态": ["# # . 1 1 H", "H 1 1 1 1 1", "1 1 H 1 # 1", "1 H H 1 . .", ". # # # . ."]}, {"当前队列": ["(2, 3) 步数: 1", "(4, 1) 步数: 1", "(4, 4) 步数: 1", "(3, 6) 步数: 2", "(2, 5) 步数: 2", "(1, 4) 步数: 2", "(2, 4) 步数: 2"], "出队操作": ["(2,3) 步数: 1"], "地图状态": ["# # 1 1 1 H", "H 1 1 1 1 1", "1 1 H 1 # 1", "1 H H 1 . .", ". # # # . ."]}, {"当前队列": ["(4, 1) 步数: 1", "(4, 4) 步数: 1", "(3, 6) 步数: 2", "(2, 5) 步数: 2", "(1, 4) 步数: 2", "(2, 4) 步数: 2", "(1, 3) 步数: 2"], "出队操作": ["(4,1) 步数: 1"], "地图状态": ["# # 1 1 1 H", "H 1 1 1 1 1", "1 1 H 1 # 1", "1 H H 1 . .", "1 # # # . ."]}, {"当前队列": ["(4, 4) 步数: 1", "(3, 6) 步数: 2", "(2, 5) 步数: 2", "(1, 4) 步数: 2", "(2, 4) 步数: 2", "(1, 3) 步数: 2", "(5, 1) 步数: 2"], "出队操作": ["(4,4) 步数: 1"], "地图状态": ["# # 1 1 1 H", "H 1 1 1 1 1", "1 1 H 1 # 1", "1 H H 1 1 .", "1 # # # . ."]}]}