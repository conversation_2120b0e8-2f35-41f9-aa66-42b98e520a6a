unit Cc_LoadingSaveFpdFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingSaveFpdForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    RzLabel2: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingSaveFpdForm: TLoadingSaveFpdForm;

implementation

{$R *.dfm}

procedure TLoadingSaveFpdForm.FormCreate(Sender: TObject);
begin
  LoadingSaveFpdForm.left := (screen.width - LoadingSaveFpdForm.width) div 2;
  LoadingSaveFpdForm.top := (screen.height - LoadingSaveFpdForm.height) div 2;
end;

procedure TLoadingSaveFpdForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
