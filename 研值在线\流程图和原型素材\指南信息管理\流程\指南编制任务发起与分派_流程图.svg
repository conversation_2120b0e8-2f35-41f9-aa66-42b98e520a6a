<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">指南编制任务发起与分派业务流程</text>

  <!-- 阶段一：任务发布与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：任务发布与校验</text>
  
  <!-- 节点1: 主管部门发布 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主管部门发布</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">选择需求征集事项并填写任务信息</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">校验必填字段</text>
  </g>

  <!-- 节点3: 生成任务编号 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成任务编号</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态置为"待认领"并推送消息</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 320 165 Q 360 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：任务认领与分派 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：任务认领与分派</text>

  <!-- 节点4: 受理处室认领 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">受理处室认领</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">在待办中心打开任务</text>
  </g>

  <!-- 节点5: 分派子任务 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分派子任务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">选择负责人并拆分子任务</text>
  </g>

  <!-- 节点6: 生成子编号 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成子编号</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">记录负责人、截止日期</text>
  </g>

  <!-- 连接线 任务 -> 认领 -->
  <path d="M 810 200 C 810 250, 310 280, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 认领 -> 分派 -> 生成 -->
  <path d="M 420 345 Q 460 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 760 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：任务监控与提醒 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：任务监控与提醒</text>

  <!-- 节点7: 节点提醒 -->
  <g transform="translate(150, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">节点提醒</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按预设间隔推送提醒</text>
  </g>

  <!-- 节点8: 预警机制 -->
  <g transform="translate(400, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">预警机制</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">到期前48小时发送预警</text>
  </g>

  <!-- 节点9: 超时处理 -->
  <g transform="translate(650, 510)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">超时处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动标记"超时"并通知</text>
  </g>

  <!-- 节点10: 任务完成确认 -->
  <g transform="translate(900, 510)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务完成确认</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">处室负责人确认完成</text>
  </g>

  <!-- 连接线 监控流程 -->
  <path d="M 350 545 Q 375 545 400 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 545 Q 625 545 650 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 545 Q 875 545 900 545" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 垂直连接线：分派到监控 -->
  <path d="M 910 380 C 910 450, 250 480, 250 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：任务完成与归档 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：任务完成与归档</text>

  <!-- 节点11: 状态更新 -->
  <g transform="translate(200, 710)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">父任务状态更新为"已完成"</text>
  </g>

  <!-- 节点12: 完成通知 -->
  <g transform="translate(500, 710)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">完成通知</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">推送通知至主管部门</text>
  </g>

  <!-- 节点13: 任务归档 -->
  <g transform="translate(800, 710)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">归档任务数据</text>
  </g>

  <!-- 连接线 完成确认 -> 状态更新 -->
  <path d="M 1010 580 C 1010 650, 310 680, 310 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 归档流程 -->
  <path d="M 420 745 Q 460 745 500 745" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 745 Q 760 745 800 745" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 审计日志节点 -->
  <g transform="translate(1100, 850)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态变更与日志写入审计库</text>
  </g>

  <!-- 连接线：各阶段到审计 -->
  <path d="M 900 780 C 1000 820, 1100 840, 1100 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <path d="M 750 580 C 1050 650, 1150 800, 1150 850" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />

  <!-- 监控循环箭头 -->
  <path d="M 250 510 C 100 450, 100 380, 910 350" stroke="#CE93D8" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="500" y="420" text-anchor="middle" font-size="12" fill="#CE93D8">持续监控循环</text>

</svg>