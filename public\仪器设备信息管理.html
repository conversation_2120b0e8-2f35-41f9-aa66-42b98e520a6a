<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器设备信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        blue: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex-1 p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                <svg class="mr-2 h-6 w-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                </svg>
                仪器设备信息管理
            </h1>
            <p class="text-gray-600 mt-1">实验室与共享平台设备全生命周期管理</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="h-[calc(100vh-160px)] flex flex-col gap-6">
            <!-- 操作控制区 -->
            <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                    </svg>
                    查询与操作
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备名称</label>
                        <input type="text" placeholder="请输入设备名称" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规格型号</label>
                        <input type="text" placeholder="请输入规格型号" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">原值范围</label>
                        <select class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option>全部价值区间</option>
                            <option>0-10万元</option>
                            <option>10-50万元</option>
                            <option>50-100万元</option>
                            <option>100万元以上</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">维护状态</label>
                        <select class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option>全部状态</option>
                            <option>正常运行</option>
                            <option>维护中</option>
                            <option>停用</option>
                            <option>报废</option>
                        </select>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">所在地点</label>
                        <input type="text" placeholder="请输入地点信息" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">购置日期</label>
                        <input type="date" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">管理部门</label>
                        <select class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option>全部部门</option>
                            <option>化学系</option>
                            <option>物理系</option>
                            <option>生物系</option>
                            <option>材料系</option>
                        </select>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                            查询
                        </button>
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">重置</button>
                    </div>
                    
                    <div class="flex items-center gap-3">
                        <button onclick="downloadTemplate()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            下载模板
                        </button>
                        <button onclick="showBatchImport()" class="border border-blue-200 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                            批量导入
                        </button>
                        <button onclick="exportData()" class="border border-blue-200 text-blue-700 px-4 py-2 rounded-md hover:bg-blue-50 transition-colors flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3"/>
                            </svg>
                            导出数据
                        </button>
                        <button onclick="showAddEquipment()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                            </svg>
                            新增设备
                        </button>
                    </div>
                </div>
            </div>

            <!-- 结果列表区 -->
            <div class="bg-white rounded-lg shadow-md border border-blue-100 flex-1 flex flex-col">
                <div class="p-4 border-b border-blue-100">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-blue-800 flex items-center">
                            <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            设备列表
                        </h3>
                        <div class="flex items-center gap-3">
                            <span class="text-sm text-gray-600">已选择 <span id="selected-count">0</span> 条记录</span>
                            <button id="batch-export-btn" class="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 transition-colors disabled:opacity-50" disabled>
                                批量导出
                            </button>
                            <button id="batch-delete-btn" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors disabled:opacity-50" disabled>
                                批量删除
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="flex-1 overflow-auto">
                    <table class="min-w-full">
                        <thead class="bg-gray-50 sticky top-0">
                            <tr>
                                <th class="px-4 py-3 text-left">
                                    <input type="checkbox" id="select-all" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">原值(万元)</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在地点</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">维护状态</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">管理部门</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">购置日期</th>
                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-blue-50 transition-colors">
                                <td class="px-4 py-3">
                                    <input type="checkbox" class="equipment-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-blue-800">高精度液相色谱仪</td>
                                <td class="px-4 py-3 text-sm text-gray-900">LC-2030C Plus</td>
                                <td class="px-4 py-3 text-sm text-gray-900">45.8</td>
                                <td class="px-4 py-3 text-sm text-gray-900">化学楼A301</td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        正常运行
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">化学系</td>
                                <td class="px-4 py-3 text-sm text-gray-900">2022-03-15</td>
                                <td class="px-4 py-3 text-sm space-x-2">
                                    <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editEquipment('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteEquipment('1')" class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-blue-50 transition-colors">
                                <td class="px-4 py-3">
                                    <input type="checkbox" class="equipment-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-blue-800">扫描电子显微镜</td>
                                <td class="px-4 py-3 text-sm text-gray-900">JSM-7800F</td>
                                <td class="px-4 py-3 text-sm text-gray-900">285.0</td>
                                <td class="px-4 py-3 text-sm text-gray-900">材料楼B205</td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        维护中
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">材料系</td>
                                <td class="px-4 py-3 text-sm text-gray-900">2021-08-20</td>
                                <td class="px-4 py-3 text-sm space-x-2">
                                    <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editEquipment('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteEquipment('2')" class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-blue-50 transition-colors">
                                <td class="px-4 py-3">
                                    <input type="checkbox" class="equipment-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-blue-800">X射线衍射仪</td>
                                <td class="px-4 py-3 text-sm text-gray-900">D8 ADVANCE</td>
                                <td class="px-4 py-3 text-sm text-gray-900">128.5</td>
                                <td class="px-4 py-3 text-sm text-gray-900">物理楼C108</td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        正常运行
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">物理系</td>
                                <td class="px-4 py-3 text-sm text-gray-900">2023-01-10</td>
                                <td class="px-4 py-3 text-sm space-x-2">
                                    <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editEquipment('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteEquipment('3')" class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-blue-50 transition-colors">
                                <td class="px-4 py-3">
                                    <input type="checkbox" class="equipment-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                </td>
                                <td class="px-4 py-3 text-sm font-medium text-blue-800">原子力显微镜</td>
                                <td class="px-4 py-3 text-sm text-gray-900">Dimension Icon</td>
                                <td class="px-4 py-3 text-sm text-gray-900">156.2</td>
                                <td class="px-4 py-3 text-sm text-gray-900">生物楼D302</td>
                                <td class="px-4 py-3">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        停用
                                    </span>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-900">生物系</td>
                                <td class="px-4 py-3 text-sm text-gray-900">2020-11-05</td>
                                <td class="px-4 py-3 text-sm space-x-2">
                                    <button onclick="showDetail('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editEquipment('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteEquipment('4')" class="text-red-600 hover:text-red-900">删除</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页控件 -->
                <div class="px-4 py-3 border-t border-blue-100 bg-gray-50 flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示第 1-4 条，共 25 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 disabled:opacity-50" disabled>上一页</button>
                        <span class="px-3 py-1 text-sm bg-blue-500 text-white rounded">1</span>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">3</button>
                        <span class="px-2 text-sm text-gray-500">...</span>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">7</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备详情模态框 -->
    <div id="detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-lg">
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-xl font-semibold text-blue-800">设备详细信息</h3>
                    <button onclick="closeDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <!-- 选项卡 -->
                    <div class="border-b border-gray-200 mb-6">
                        <nav class="flex space-x-8" aria-label="Tabs">
                            <button class="detail-tab active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-600" data-tab="basic">
                                基本信息
                            </button>
                            <button class="detail-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="technical">
                                技术参数
                            </button>
                            <button class="detail-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="service">
                                服务信息
                            </button>
                            <button class="detail-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="files">
                                附件资料
                            </button>
                            <button class="detail-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="history">
                                历史版本
                            </button>
                        </nav>
                    </div>

                    <!-- 基本信息标签页 -->
                    <div id="basic-tab" class="detail-content">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">设备名称</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">高精度液相色谱仪</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">规格型号</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">LC-2030C Plus</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">设备编号</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">EQ-2022-001</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">原值(万元)</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">45.8</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">所在地点</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">化学楼A301</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">管理部门</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">化学系</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">购置日期</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">2022-03-15</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">维护状态</label>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            正常运行
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">设备描述</label>
                                    <p class="text-gray-900 bg-gray-50 p-3 rounded">
                                        高效液相色谱仪，适用于分离分析有机化合物、生物大分子等。具有高分离度、高灵敏度、高选择性等特点，广泛应用于药物分析、食品检测、环境监测等领域。
                                    </p>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">设备图片</label>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4">
                                        <img src="https://source.unsplash.com/400x300?laboratory,chromatography" alt="设备图片" class="w-full h-64 object-cover rounded-lg">
                                        <p class="text-sm text-gray-500 mt-2 text-center">平台水印: 科研设备管理平台 - 2024</p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">负责人</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">张教授</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                                        <p class="text-gray-900 bg-gray-50 p-2 rounded">138****5678</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 技术参数标签页 -->
                    <div id="technical-tab" class="detail-content hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <h4 class="text-lg font-medium text-blue-800">主要技术指标</h4>
                                <div class="space-y-3">
                                    <div class="flex justify-between border-b pb-2">
                                        <span class="text-gray-600">检测器类型</span>
                                        <span class="text-gray-900">UV-VIS检测器</span>
                                    </div>
                                    <div class="flex justify-between border-b pb-2">
                                        <span class="text-gray-600">波长范围</span>
                                        <span class="text-gray-900">190-700nm</span>
                                    </div>
                                    <div class="flex justify-between border-b pb-2">
                                        <span class="text-gray-600">流速范围</span>
                                        <span class="text-gray-900">0.001-10ml/min</span>
                                    </div>
                                    <div class="flex justify-between border-b pb-2">
                                        <span class="text-gray-600">压力上限</span>
                                        <span class="text-gray-900">66MPa</span>
                                    </div>
                                    <div class="flex justify-between border-b pb-2">
                                        <span class="text-gray-600">温度控制</span>
                                        <span class="text-gray-900">室温+10~85℃</span>
                                    </div>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <h4 class="text-lg font-medium text-blue-800">性能特点</h4>
                                <ul class="space-y-2 text-sm text-gray-700">
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        高精度分离，分离度优异
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        快速分析，分析时间短
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        自动化程度高，操作简便
                                    </li>
                                    <li class="flex items-start">
                                        <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        检测限低，灵敏度高
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 服务信息标签页 -->
                    <div id="service-tab" class="detail-content hidden">
                        <div class="space-y-6">
                            <div>
                                <h4 class="text-lg font-medium text-blue-800 mb-3">服务内容</h4>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-gray-700">
                                        提供有机化合物定性定量分析、药物成分检测、食品添加剂分析、环境污染物检测等服务。
                                        可进行梯度洗脱、等度洗脱等多种分离模式，满足不同样品的分析需求。
                                    </p>
                                </div>
                            </div>
                            <div>
                                <h4 class="text-lg font-medium text-blue-800 mb-3">收费标准</h4>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full bg-white border border-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-900">服务项目</th>
                                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-900">内部用户(元/样)</th>
                                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-900">外部用户(元/样)</th>
                                                <th class="px-4 py-2 border-b text-left text-sm font-medium text-gray-900">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td class="px-4 py-2 border-b text-sm">常规检测</td>
                                                <td class="px-4 py-2 border-b text-sm">50</td>
                                                <td class="px-4 py-2 border-b text-sm">80</td>
                                                <td class="px-4 py-2 border-b text-sm">单个组分</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 border-b text-sm">复杂样品分析</td>
                                                <td class="px-4 py-2 border-b text-sm">100</td>
                                                <td class="px-4 py-2 border-b text-sm">150</td>
                                                <td class="px-4 py-2 border-b text-sm">多组分分离</td>
                                            </tr>
                                            <tr>
                                                <td class="px-4 py-2 border-b text-sm">方法开发</td>
                                                <td class="px-4 py-2 border-b text-sm">200</td>
                                                <td class="px-4 py-2 border-b text-sm">300</td>
                                                <td class="px-4 py-2 border-b text-sm">包含优化</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 附件资料标签页 -->
                    <div id="files-tab" class="detail-content hidden">
                        <div class="space-y-4">
                            <h4 class="text-lg font-medium text-blue-800">附件下载</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center">
                                        <svg class="w-10 h-10 text-red-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900">操作手册</h5>
                                            <p class="text-sm text-gray-500">液相色谱仪使用说明书 (2.5MB)</p>
                                        </div>
                                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">下载</button>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center">
                                        <svg class="w-10 h-10 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900">检测报告</h5>
                                            <p class="text-sm text-gray-500">设备性能验证报告 (1.8MB)</p>
                                        </div>
                                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">下载</button>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center">
                                        <svg class="w-10 h-10 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900">维护记录</h5>
                                            <p class="text-sm text-gray-500">年度维护保养记录 (0.9MB)</p>
                                        </div>
                                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">下载</button>
                                    </div>
                                </div>
                                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                    <div class="flex items-center">
                                        <svg class="w-10 h-10 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"/>
                                        </svg>
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900">校准证书</h5>
                                            <p class="text-sm text-gray-500">计量校准证书 (1.2MB)</p>
                                        </div>
                                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">下载</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 历史版本标签页 -->
                    <div id="history-tab" class="detail-content hidden">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center">
                                <h4 class="text-lg font-medium text-blue-800">版本历史</h4>
                                <button onclick="showVersionCompare()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm">
                                    版本对比
                                </button>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="border border-gray-200 rounded-lg p-4 bg-blue-50">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-medium mr-3">当前版本</span>
                                                <span class="text-sm text-gray-600">版本 v2.1</span>
                                            </div>
                                            <p class="text-sm text-gray-700 mt-1">更新时间: 2024-01-15 14:30</p>
                                            <p class="text-sm text-gray-700">操作人: 张教授</p>
                                            <p class="text-sm text-gray-700">更新内容: 修改服务收费标准，新增复杂样品分析项目</p>
                                        </div>
                                        <input type="checkbox" class="version-checkbox" data-version="v2.1">
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-medium mr-3">历史版本</span>
                                                <span class="text-sm text-gray-600">版本 v2.0</span>
                                            </div>
                                            <p class="text-sm text-gray-700 mt-1">更新时间: 2023-12-10 09:20</p>
                                            <p class="text-sm text-gray-700">操作人: 李工程师</p>
                                            <p class="text-sm text-gray-700">更新内容: 设备维护状态更新，添加新的技术参数</p>
                                        </div>
                                        <input type="checkbox" class="version-checkbox" data-version="v2.0">
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="flex items-center">
                                                <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-medium mr-3">历史版本</span>
                                                <span class="text-sm text-gray-600">版本 v1.0</span>
                                            </div>
                                            <p class="text-sm text-gray-700 mt-1">创建时间: 2022-03-15 16:45</p>
                                            <p class="text-sm text-gray-700">操作人: 王管理员</p>
                                            <p class="text-sm text-gray-700">更新内容: 初始设备信息录入</p>
                                        </div>
                                        <input type="checkbox" class="version-checkbox" data-version="v1.0">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增设备模态框 -->
    <div id="add-equipment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto shadow-lg">
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-xl font-semibold text-blue-800">新增设备</h3>
                    <button onclick="closeAddEquipment()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <form class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">设备名称 *</label>
                            <input type="text" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">规格型号 *</label>
                            <input type="text" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">原值(万元) *</label>
                            <input type="number" step="0.1" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">所在地点 *</label>
                            <input type="text" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">管理部门 *</label>
                            <select class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                                <option value="">请选择部门</option>
                                <option>化学系</option>
                                <option>物理系</option>
                                <option>生物系</option>
                                <option>材料系</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">购置日期 *</label>
                            <input type="date" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">维护状态</label>
                            <select class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option>正常运行</option>
                                <option>维护中</option>
                                <option>停用</option>
                                <option>报废</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">设备描述</label>
                        <textarea rows="4" class="w-full px-3 py-2 border border-blue-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入设备详细描述..."></textarea>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">设备图片</label>
                        <div class="border-2 border-dashed border-blue-200 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <div class="mt-4">
                                <label class="cursor-pointer bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                                    选择图片文件
                                    <input type="file" class="hidden" accept="image/*">
                                </label>
                            </div>
                            <p class="text-sm text-gray-500 mt-2">支持JPG、PNG格式，文件大小不超过5MB</p>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeAddEquipment()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            保存设备
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="batch-import-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-2xl w-full shadow-lg">
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-xl font-semibold text-blue-800">批量导入设备</h3>
                    <button onclick="closeBatchImport()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6 space-y-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="text-sm font-medium text-blue-800 mb-2">导入步骤</h4>
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 下载标准Excel模板</li>
                            <li>2. 按照模板格式填写设备信息</li>
                            <li>3. 上传填写完成的Excel文件</li>
                            <li>4. 系统校验数据并显示结果</li>
                        </ol>
                    </div>
                    
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                        <div class="mt-4">
                            <label class="cursor-pointer bg-blue-500 text-white px-6 py-3 rounded hover:bg-blue-600">
                                选择Excel文件
                                <input type="file" class="hidden" accept=".xlsx,.xls">
                            </label>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">支持.xlsx、.xls格式，文件大小不超过10MB</p>
                    </div>
                    
                    <!-- 进度条 -->
                    <div id="import-progress" class="hidden">
                        <div class="mb-2 flex justify-between text-sm">
                            <span class="text-blue-600">数据校验中...</span>
                            <span class="text-blue-600">65%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 65%"></div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeBatchImport()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            取消
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            开始导入
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本对比模态框 -->
    <div id="version-compare-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto shadow-lg">
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-xl font-semibold text-blue-800">版本对比</h3>
                    <button onclick="closeVersionCompare()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-8">
                        <div>
                            <h4 class="text-lg font-medium text-blue-800 mb-4">版本 v2.1 (当前版本)</h4>
                            <div class="space-y-4 text-sm">
                                <div class="border-l-4 border-green-400 pl-4">
                                    <div class="font-medium text-gray-900">收费标准</div>
                                    <div class="text-green-600">常规检测: 50元/样 → 内部用户</div>
                                    <div class="text-green-600">复杂样品分析: 100元/样 → 内部用户 (新增)</div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">技术参数</div>
                                    <div class="text-gray-600">检测器类型: UV-VIS检测器</div>
                                    <div class="text-gray-600">波长范围: 190-700nm</div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <h4 class="text-lg font-medium text-blue-800 mb-4">版本 v2.0</h4>
                            <div class="space-y-4 text-sm">
                                <div class="border-l-4 border-red-400 pl-4">
                                    <div class="font-medium text-gray-900">收费标准</div>
                                    <div class="text-red-600">常规检测: 45元/样 → 内部用户</div>
                                    <div class="text-red-600">复杂样品分析: 未设置</div>
                                </div>
                                <div>
                                    <div class="font-medium text-gray-900">技术参数</div>
                                    <div class="text-gray-600">检测器类型: UV-VIS检测器</div>
                                    <div class="text-gray-600">波长范围: 190-700nm</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-8 bg-gray-50 p-4 rounded-lg">
                        <h5 class="font-medium text-gray-900 mb-2">主要变更总结</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                调整常规检测收费标准，从45元调整为50元
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                                新增复杂样品分析服务项目及收费标准
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                                操作人员: 张教授
                            </li>
                            <li class="flex items-center">
                                <span class="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
                                更新时间: 2024-01-15 14:30
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 详情模态框选项卡切换
        document.querySelectorAll('.detail-tab').forEach(button => {
            button.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 更新按钮状态
                document.querySelectorAll('.detail-tab').forEach(btn => {
                    btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                this.classList.add('active', 'border-blue-500', 'text-blue-600');
                this.classList.remove('border-transparent', 'text-gray-500');
                
                // 切换内容区域
                document.querySelectorAll('.detail-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(targetTab + '-tab').classList.remove('hidden');
            });
        });

        // 复选框功能
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.equipment-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateSelectedCount();
        });

        document.querySelectorAll('.equipment-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateSelectedCount);
        });

        function updateSelectedCount() {
            const selectedCount = document.querySelectorAll('.equipment-checkbox:checked').length;
            document.getElementById('selected-count').textContent = selectedCount;
            
            const batchExportBtn = document.getElementById('batch-export-btn');
            const batchDeleteBtn = document.getElementById('batch-delete-btn');
            
            if (selectedCount > 0) {
                batchExportBtn.disabled = false;
                batchDeleteBtn.disabled = false;
            } else {
                batchExportBtn.disabled = true;
                batchDeleteBtn.disabled = true;
            }
        }

        // 显示设备详情
        function showDetail(id) {
            document.getElementById('detail-modal').classList.remove('hidden');
        }

        // 关闭设备详情
        function closeDetail() {
            document.getElementById('detail-modal').classList.add('hidden');
        }

        // 显示新增设备
        function showAddEquipment() {
            document.getElementById('add-equipment-modal').classList.remove('hidden');
        }

        // 关闭新增设备
        function closeAddEquipment() {
            document.getElementById('add-equipment-modal').classList.add('hidden');
        }

        // 显示批量导入
        function showBatchImport() {
            document.getElementById('batch-import-modal').classList.remove('hidden');
        }

        // 关闭批量导入
        function closeBatchImport() {
            document.getElementById('batch-import-modal').classList.add('hidden');
        }

        // 显示版本对比
        function showVersionCompare() {
            document.getElementById('version-compare-modal').classList.remove('hidden');
        }

        // 关闭版本对比
        function closeVersionCompare() {
            document.getElementById('version-compare-modal').classList.add('hidden');
        }

        // 编辑设备
        function editEquipment(id) {
            alert('编辑设备功能 - ID: ' + id);
        }

        // 删除设备
        function deleteEquipment(id) {
            if (confirm('确定要删除这台设备吗？')) {
                alert('删除设备功能 - ID: ' + id);
            }
        }

        // 下载模板
        function downloadTemplate() {
            alert('正在下载设备信息导入模板...');
        }

        // 导出数据
        function exportData() {
            alert('正在导出设备数据...');
        }

        // 点击模态框背景关闭弹窗
        document.querySelectorAll('[id$="-modal"]').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.classList.add('hidden');
                }
            });
        });

        // 版本复选框功能
        document.querySelectorAll('.version-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const checkedBoxes = document.querySelectorAll('.version-checkbox:checked');
                if (checkedBoxes.length > 2) {
                    this.checked = false;
                    alert('最多只能选择两个版本进行对比');
                }
            });
        });
    </script>
</body>
</html>