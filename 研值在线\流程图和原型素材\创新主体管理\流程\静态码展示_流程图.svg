<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">静态码展示流程图</text>

  <!-- 阶段一：用户请求提交 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户请求提交</text>
  
  <!-- 节点1: 用户填写配置 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户填写生成配置</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">主体类型、信用代码、行业领域</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">科研活动类型等必要字段</text>
  </g>

  <!-- 阶段二：系统校验 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：系统校验</text>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(550, 300)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">主体合法性与字段完整性校验</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">发现缺失或冲突则返回错误</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 700 210 Q 700 235 700 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：代码计算和数据绑定 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：代码计算和数据绑定</text>

  <!-- 节点3: 科研码计算 -->
  <g transform="translate(200, 470)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科研码计算</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">根据编码规则计算科研码</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">调用内部数据整合服务</text>
  </g>

  <!-- 节点4: 数据绑定 -->
  <g transform="translate(950, 470)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据绑定</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">工商信息、科技成果、仪器设备</text>
    <text x="125" y="65" text-anchor="middle" font-size="12" fill="#555">与科研码绑定，标记为"待发布"</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 600 380 C 500 410, 400 440, 325 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 450 510 C 650 510, 850 510, 950 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="500" text-anchor="middle" font-size="12" fill="#555">数据整合</text>

  <!-- 阶段四：静态资源生成 -->
  <text x="700" y="620" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：静态资源生成</text>

  <!-- 节点5: 资源生成与存储 -->
  <g transform="translate(550, 650)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">静态资源生成与存储</text>
    <text x="150" y="50" text-anchor="middle" font-size="12" fill="#555">生成二维码与科研码文本</text>
    <text x="150" y="65" text-anchor="middle" font-size="12" fill="#555">加密签名处理，存储至文件服务</text>
  </g>

  <!-- 阶段五：任务状态更新 -->
  <text x="700" y="790" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：任务状态更新和前端展示</text>

  <!-- 节点6: 状态更新与展示 -->
  <g transform="translate(450, 820)" filter="url(#soft-shadow)">
    <rect width="500" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="250" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务状态更新与前端展示</text>
    <text x="250" y="50" text-anchor="middle" font-size="12" fill="#555">更新任务状态为"已生成"，推送二维码地址、科研码及摘要信息</text>
    <text x="250" y="65" text-anchor="middle" font-size="12" fill="#555">监听变更事件，自动刷新绑定数据及二维码内容</text>
  </g>

  <!-- 连接线 4 -> 5 -->
  <path d="M 1075 550 C 1075 600, 800 620, 700 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 700 730 Q 700 775 700 820" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>