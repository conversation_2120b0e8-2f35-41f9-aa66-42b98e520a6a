<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">指南数量统计与维度总览流程图</text>

  <!-- 阶段一：数据初始化与缓存 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与缓存</text>
  
  <!-- 节点1: 用户进入页面 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入功能页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">触发页面初始化</text>
  </g>

  <!-- 节点2: 调用指南库服务 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">调用指南库服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取最新数据</text>
  </g>

  <!-- 节点3: 聚合预处理 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">聚合预处理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按维度分组统计</text>
  </g>

  <!-- 节点4: 生成统计缓存 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成统计缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">提升查询性能</text>
  </g>
  
  <!-- 连接线 阶段一 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 165 Q 950 165 1000 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与动态更新 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与动态更新</text>

  <!-- 节点5: 调整筛选条件 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">调整筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">切换图表类型</text>
  </g>

  <!-- 节点6: 快速重算数据 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">快速重算数据</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">基于缓存计算</text>
  </g>

  <!-- 节点7: 刷新图表视图 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">刷新图表视图</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时展示结果</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 400 355 Q 450 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 355 Q 750 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：下钻分析与导航 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：下钻分析与导航</text>

  <!-- 节点8: 点击维度条目 -->
  <g transform="translate(150, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">点击维度条目</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">触发下钻事件</text>
  </g>

  <!-- 节点9: 记录点击事件 -->
  <g transform="translate(400, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">记录点击事件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推送事件日志</text>
  </g>

  <!-- 节点10: 更新下钻导航 -->
  <g transform="translate(650, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新下钻导航</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载细分统计</text>
  </g>

  <!-- 节点11: 跳转指南详情 -->
  <g transform="translate(900, 500)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">跳转指南详情</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">携带筛选上下文</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 350 535 Q 375 535 400 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 535 Q 625 535 650 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 535 Q 875 535 900 535" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：后台任务与审计 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：后台任务与审计</text>
  
  <!-- 节点12: 24小时定时任务 -->
  <g transform="translate(300, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">24小时定时任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">重新汇总数据</text>
  </g>

  <!-- 节点13: 更新统计缓存 -->
  <g transform="translate(600, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">更新统计缓存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">生成变动日志</text>
  </g>

  <!-- 节点14: 审计日志 -->
  <g transform="translate(900, 680)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">供后续审计使用</text>
  </g>

  <!-- 连接线 阶段四 -->
  <path d="M 500 715 Q 550 715 600 715" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 715 Q 850 715 900 715" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 缓存到筛选条件的循环 -->
  <path d="M 1100 200 C 1150 200, 1150 280, 1150 320 C 1150 360, 1100 360, 400 360" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1170" y="260" font-size="12" fill="#666">基于缓存</text>
  
  <!-- 定时任务到缓存的更新循环 -->
  <path d="M 400 680 C 350 680, 300 650, 300 600 C 300 550, 350 520, 1100 200" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="250" y="440" font-size="12" fill="#666" transform="rotate(-90, 250, 440)">定时更新</text>

</svg>