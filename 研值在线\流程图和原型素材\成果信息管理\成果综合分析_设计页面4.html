<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果综合分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">成果综合分析</h1>
            <p class="text-gray-600">多维度分析科技奖励、知识产权、论文期刊等成果数据，洞察区域创新实力与转化趋势</p>
        </div>

        <!-- 筛选控制区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            科技奖励
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            知识产权
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            论文期刊
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="valid">有效</option>
                        <option value="expired">已过期</option>
                        <option value="transferred">已转化</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统计口径</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="count">按数量统计</option>
                        <option value="value">按价值统计</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <select class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="2023">2023年</option>
                            <option value="2022">2022年</option>
                            <option value="2021">2021年</option>
                        </select>
                        <select class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="all">全年</option>
                            <option value="Q1">第一季度</option>
                            <option value="Q2">第二季度</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">地域范围</label>
                    <div class="flex space-x-2">
                        <select class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="zhejiang">浙江省</option>
                            <option value="jiangsu">江苏省</option>
                        </select>
                        <select class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="ningbo">宁波市</option>
                            <option value="hangzhou">杭州市</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3 mt-4">
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    分析
                </button>
                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置
                </button>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">成果总量</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">1,245</p>
                    </div>
                    <div class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        12.5%
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <!-- 迷你折线图占位 -->
                    <svg viewBox="0 0 100 30" class="w-full h-full">
                        <polyline points="0,25 20,15 40,20 60,10 80,15 100,5" stroke="#3b82f6" stroke-width="2" fill="none" />
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">年度新增</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">328</p>
                    </div>
                    <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        8.3%
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <!-- 迷你折线图占位 -->
                    <svg viewBox="0 0 100 30" class="w-full h-full">
                        <polyline points="0,20 20,25 40,15 60,20 80,10 100,15" stroke="#10b981" stroke-width="2" fill="none" />
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">转化率</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">32.6%</p>
                    </div>
                    <div class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        2.1%
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <!-- 迷你折线图占位 -->
                    <svg viewBox="0 0 100 30" class="w-full h-full">
                        <polyline points="0,15 20,20 40,10 60,15 80,5 100,10" stroke="#f59e0b" stroke-width="2" fill="none" />
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">交易金额(万)</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">4,568</p>
                    </div>
                    <div class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7m0 0l7 7m-7-7v18"></path>
                        </svg>
                        5.7%
                    </div>
                </div>
                <div class="mt-4 h-12">
                    <!-- 迷你折线图占位 -->
                    <svg viewBox="0 0 100 30" class="w-full h-full">
                        <polyline points="0,10 20,15 40,5 60,10 80,0 100,5" stroke="#ef4444" stroke-width="2" fill="none" />
                    </svg>
                </div>
            </div>
        </div>

        <!-- 分类统计区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    成果分类统计
                </h2>
                <div id="category-chart" class="w-full h-[400px]"></div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    成果类型占比
                </h2>
                <div id="type-chart" class="w-full h-[400px]"></div>
            </div>
        </div>

        <!-- 区域分布与主体分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                    </svg>
                    区域分布排名
                </h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排名</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区域</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果总量</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年度新增</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化率</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,245</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">328</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">32.6%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">杭州市</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,156</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">298</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">28.4%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">3</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">温州市</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">876</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">201</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">25.2%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">4</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">绍兴市</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">765</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">187</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">23.8%</td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">5</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">嘉兴市</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">654</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">165</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">22.1%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    创新主体排行榜
                </h2>
                <div id="organization-chart" class="w-full h-[400px]"></div>
            </div>
        </div>

        <!-- 转化交易与技术分布区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                转化交易与技术分布
            </h2>
            <div id="transaction-chart" class="w-full h-[400px]"></div>
        </div>

        <!-- 输出与分享区 -->
        <div class="flex justify-end space-x-3">
            <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
                导出Excel
            </button>
            <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                生成PDF报告
            </button>
            <button class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                复制图表链接
            </button>
        </div>
    </div>

    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    <script>
        // 分类统计柱状图
        const categoryChart = echarts.init(document.getElementById('category-chart'));
        const categoryOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['科技奖励', '知识产权', '论文期刊']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: ['宁波市', '杭州市', '温州市', '绍兴市', '嘉兴市'],
                axisLabel: {
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value'
            },
            series: [
                {
                    name: '科技奖励',
                    type: 'bar',
                    data: [320, 302, 301, 254, 190],
                    itemStyle: {
                        color: '#5470C6'
                    }
                },
                {
                    name: '知识产权',
                    type: 'bar',
                    data: [420, 382, 291, 234, 290],
                    itemStyle: {
                        color: '#91CC75'
                    }
                },
                {
                    name: '论文期刊',
                    type: 'bar',
                    data: [505, 472, 284, 277, 174],
                    itemStyle: {
                        color: '#FAC858'
                    }
                }
            ]
        };
        categoryChart.setOption(categoryOption);

        // 成果类型占比饼图
        const typeChart = echarts.init(document.getElementById('type-chart'));
        const typeOption = {
            tooltip: {
                trigger: 'item'
            },
            legend: {
                orient: 'vertical',
                right: 10,
                top: 'center',
                data: ['发明专利', '实用新型', '外观设计', '科技奖项', 'SCI论文', 'EI论文']
            },
            series: [
                {
                    name: '成果类型',
                    type: 'pie',
                    radius: ['40%', '70%'],
                    avoidLabelOverlap: false,
                    itemStyle: {
                        borderRadius: 10,
                        borderColor: '#fff',
                        borderWidth: 2
                    },
                    label: {
                        show: false,
                        position: 'center'
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: '18',
                            fontWeight: 'bold'
                        }
                    },
                    labelLine: {
                        show: false
                    },
                    data: [
                        { value: 335, name: '发明专利', itemStyle: { color: '#5470C6' } },
                        { value: 310, name: '实用新型', itemStyle: { color: '#91CC75' } },
                        { value: 234, name: '外观设计', itemStyle: { color: '#FAC858' } },
                        { value: 135, name: '科技奖项', itemStyle: { color: '#EE6666' } },
                        { value: 154, name: 'SCI论文', itemStyle: { color: '#73C0DE' } },
                        { value: 117, name: 'EI论文', itemStyle: { color: '#3BA272' } }
                    ]
                }
            ]
        };
        typeChart.setOption(typeOption);

        // 创新主体排行榜柱状图
        const orgChart = echarts.init(document.getElementById('organization-chart'));
        const orgOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: ['宁波大学', '中科院宁波材料所', '宁波吉利汽车', '宁波奥克斯', '宁波均胜电子', '宁波工程学院', '宁波诺丁汉大学']
            },
            series: [
                {
                    name: '成果数量',
                    type: 'bar',
                    data: [320, 302, 301, 254, 190, 174, 158],
                    itemStyle: {
                        color: function(params) {
                            const colorList = ['#5470C6', '#91CC75', '#FAC858', '#EE6666', '#73C0DE', '#3BA272', '#9A60B4'];
                            return colorList[params.dataIndex];
                        }
                    },
                    label: {
                        show: true,
                        position: 'right'
                    }
                }
            ]
        };
        orgChart.setOption(orgOption);

        // 转化交易与技术分布折线图
        const transChart = echarts.init(document.getElementById('transaction-chart'));
        const transOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            legend: {
                data: ['转化率', '交易次数', '交易金额(万)']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '转化率',
                    min: 0,
                    max: 100,
                    interval: 20,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                {
                    type: 'value',
                    name: '交易金额(万)',
                    min: 0,
                    max: 1000,
                    interval: 200
                }
            ],
            series: [
                {
                    name: '转化率',
                    type: 'line',
                    data: [20, 32, 41, 34, 50, 30, 45, 55, 60, 70, 65, 75],
                    itemStyle: {
                        color: '#5470C6'
                    }
                },
                {
                    name: '交易次数',
                    type: 'line',
                    data: [10, 15, 20, 18, 25, 22, 28, 35, 40, 45, 42, 50],
                    itemStyle: {
                        color: '#91CC75'
                    }
                },
                {
                    name: '交易金额(万)',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [120, 182, 191, 234, 290, 330, 310, 345, 420, 490, 470, 520],
                    itemStyle: {
                        color: '#EE6666'
                    }
                }
            ]
        };
        transChart.setOption(transOption);

        // 响应式调整
        window.addEventListener('resize', function() {
            categoryChart.resize();
            typeChart.resize();
            orgChart.resize();
            transChart.resize();
        });
    </script>
</body>
</html>