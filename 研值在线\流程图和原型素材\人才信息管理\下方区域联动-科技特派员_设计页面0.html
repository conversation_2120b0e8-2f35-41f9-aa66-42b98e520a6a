<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技特派员区域联动</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技特派员区域联动</h1>
            <p class="text-gray-600">全面展示宁波市科技特派员的空间分布与结构画像，支持多维度统计与可视化分析</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 宁波市地图区域 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        宁波市科技特派员分布
                    </h2>
                    <div class="grid grid-cols-3 gap-4">
                        <div class="col-span-2">
                            <!-- 地图占位 -->
                            <div class="bg-gray-100 rounded-md h-64 flex items-center justify-center">
                                <svg class="w-full h-full" viewBox="0 0 800 400" xmlns="http://www.w3.org/2000/svg">
                                    <!-- 宁波市地图简化轮廓 -->
                                    <path d="M100,100 L200,80 L300,120 L400,90 L500,130 L600,110 L700,150 L750,200 L700,250 L600,280 L500,260 L400,290 L300,250 L200,280 L100,250 Z" fill="#93c5fd" stroke="#1e40af" stroke-width="2"/>
                                    <!-- 区域热力点 -->
                                    <circle cx="150" cy="150" r="15" fill="#3b82f6" opacity="0.8"/>
                                    <circle cx="250" cy="130" r="20" fill="#3b82f6" opacity="0.8"/>
                                    <circle cx="350" cy="170" r="18" fill="#3b82f6" opacity="0.8"/>
                                    <circle cx="450" cy="140" r="12" fill="#3b82f6" opacity="0.8"/>
                                    <circle cx="550" cy="180" r="22" fill="#3b82f6" opacity="0.8"/>
                                    <circle cx="650" cy="200" r="25" fill="#3b82f6" opacity="0.8"/>
                                </svg>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">全市科技特派员总数</div>
                                    <div class="text-2xl font-bold text-blue-600">1,245</div>
                                </div>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">个人科技特派员</div>
                                    <div class="text-2xl font-bold text-green-600">856</div>
                                </div>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">团队科技特派员</div>
                                    <div class="text-2xl font-bold text-purple-600">289</div>
                                </div>
                            </div>
                            <div class="bg-yellow-50 p-4 rounded-lg">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">法人科技特派员</div>
                                    <div class="text-2xl font-bold text-yellow-600">100</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 结构分析图表区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        结构分析
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 类型分布饼图 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">科技特派员类型分布</h3>
                            <div class="flex items-center justify-center h-48">
                                <svg class="w-48 h-48" viewBox="0 0 100 100">
                                    <!-- 饼图 -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#3b82f6" stroke-width="20" stroke-dasharray="68 100" transform="rotate(-90 50 50)"/>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#8b5cf6" stroke-width="20" stroke-dasharray="23 100" stroke-dashoffset="-68" transform="rotate(-90 50 50)"/>
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f59e0b" stroke-width="20" stroke-dasharray="9 100" stroke-dashoffset="-91" transform="rotate(-90 50 50)"/>
                                    <!-- 图例 -->
                                    <text x="70" y="30" font-size="4" fill="#3b82f6">个人 68%</text>
                                    <text x="70" y="40" font-size="4" fill="#8b5cf6">团队 23%</text>
                                    <text x="70" y="50" font-size="4" fill="#f59e0b">法人 9%</text>
                                </svg>
                            </div>
                        </div>
                        <!-- 性别比例柱状图 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">个人科技特派员性别比例</h3>
                            <div class="flex items-end h-48 space-x-6">
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-blue-500 rounded-t" style="height: 80%"></div>
                                    <span class="text-xs mt-1">男性</span>
                                    <span class="text-xs">65%</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-8 bg-pink-500 rounded-t" style="height: 35%"></div>
                                    <span class="text-xs mt-1">女性</span>
                                    <span class="text-xs">35%</span>
                                </div>
                            </div>
                        </div>
                        <!-- 年龄结构柱状图 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">个人科技特派员年龄结构</h3>
                            <div class="flex items-end h-48 space-x-4">
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-green-500 rounded-t" style="height: 30%"></div>
                                    <span class="text-xs mt-1">30岁以下</span>
                                    <span class="text-xs">15%</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-green-500 rounded-t" style="height: 60%"></div>
                                    <span class="text-xs mt-1">30-40岁</span>
                                    <span class="text-xs">45%</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-green-500 rounded-t" style="height: 40%"></div>
                                    <span class="text-xs mt-1">40-50岁</span>
                                    <span class="text-xs">30%</span>
                                </div>
                                <div class="flex flex-col items-center">
                                    <div class="w-6 bg-green-500 rounded-t" style="height: 10%"></div>
                                    <span class="text-xs mt-1">50岁以上</span>
                                    <span class="text-xs">10%</span>
                                </div>
                            </div>
                        </div>
                        <!-- 业务环节统计 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">业务环节统计</h3>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-gray-600">需求征集</span>
                                    <div class="w-3/4 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 75%"></div>
                                    </div>
                                    <span class="text-xs font-medium">934</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-gray-600">申报</span>
                                    <div class="w-3/4 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
                                    </div>
                                    <span class="text-xs font-medium">747</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-xs text-gray-600">备案登记</span>
                                    <div class="w-3/4 bg-gray-200 rounded-full h-2">
                                        <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                    </div>
                                    <span class="text-xs font-medium">560</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科技特派员清册 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                科技特派员清册
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                        导出
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md border border-gray-300 p-2 hidden z-10">
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                    </div>
                                </div>
                                <input type="text" placeholder="搜索姓名或服务领域" class="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务区域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">性别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">年龄</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">张伟</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">个人</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">鄞州区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">农业信息化</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已备案</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">男</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">38</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">王芳</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-purple-100 text-purple-800 rounded-full">团队</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">海曙区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">农产品加工</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">申报中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">女</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">32</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市智慧农业研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">法人</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">江北区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">智慧农业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已备案</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">-</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">-</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">李强</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">个人</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">镇海区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">水产养殖</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs bg-red-100 text-red-800 rounded-full">未申报</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">男</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">45</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">1,245</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与筛选区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 区域筛选 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        区域筛选
                    </h2>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="region-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="region-all" class="ml-2 text-sm text-gray-700">全部区域</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="region-yz" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="region-yz" class="ml-2 text-sm text-gray-700">鄞州区 (356)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="region-hs" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="region-hs" class="ml-2 text-sm text-gray-700">海曙区 (289)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="region-jb" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="region-jb" class="ml-2 text-sm text-gray-700">江北区 (187)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="region-zh" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="region-zh" class="ml-2 text-sm text-gray-700">镇海区 (156)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="region-bl" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="region-bl" class="ml-2 text-sm text-gray-700">北仑区 (132)</label>
                        </div>
                    </div>
                </div>

                <!-- 类型筛选 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        类型筛选
                    </h2>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="type-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="type-all" class="ml-2 text-sm text-gray-700">全部类型</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="type-personal" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="type-personal" class="ml-2 text-sm text-gray-700">个人 (856)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="type-team" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="type-team" class="ml-2 text-sm text-gray-700">团队 (289)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="type-legal" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="type-legal" class="ml-2 text-sm text-gray-700">法人 (100)</label>
                        </div>
                    </div>
                </div>

                <!-- 服务领域筛选 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                        </svg>
                        服务领域
                    </h2>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="field-all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="field-all" class="ml-2 text-sm text-gray-700">全部领域</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="field-agri" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="field-agri" class="ml-2 text-sm text-gray-700">农业生产 (456)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="field-tech" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="field-tech" class="ml-2 text-sm text-gray-700">农业技术 (389)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="field-process" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="field-process" class="ml-2 text-sm text-gray-700">农产品加工 (278)</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="field-info" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="field-info" class="ml-2 text-sm text-gray-700">农业信息化 (122)</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 科技特派员详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">科技特派员详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 基本信息 -->
                        <div class="md:col-span-2 space-y-4">
                            <div class="flex items-center">
                                <div class="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                                    <svg class="w-10 h-10 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-xl font-bold text-gray-900">张伟</h4>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">个人科技特派员</span>
                                        <span class="text-sm text-gray-500">男 | 38岁</span>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">服务区域</label>
                                    <p class="text-sm text-gray-900">鄞州区</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">服务领域</label>
                                    <p class="text-sm text-gray-900">农业信息化</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">申报状态</label>
                                    <p class="text-sm text-gray-900"><span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">已备案</span></p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">备案日期</label>
                                    <p class="text-sm text-gray-900">2023-05-15</p>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">所属单位</label>
                                <p class="text-sm text-gray-900">宁波市农业科学研究院</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">专业技术职称</label>
                                <p class="text-sm text-gray-900">高级农艺师</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">服务简介</label>
                                <p class="text-sm text-gray-900">主要从事农业信息化技术推广与应用，擅长农业物联网、大数据分析等技术在农业生产中的应用，曾主持宁波市智慧农业示范项目。</p>
                            </div>
                        </div>
                        <!-- 服务记录 -->
                        <div class="md:col-span-1">
                            <h4 class="text-lg font-medium text-gray-900 mb-4">服务记录</h4>
                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4 py-1">
                                    <p class="text-sm font-medium text-gray-900">2023-06-10</p>
                                    <p class="text-xs text-gray-600">鄞州区姜山镇农业技术培训</p>
                                </div>
                                <div class="border-l-4 border-blue-500 pl-4 py-1">
                                    <p class="text-sm font-medium text-gray-900">2023-05-28</p>
                                    <p class="text-xs text-gray-600">智慧农业示范项目现场指导</p>
                                </div>
                                <div class="border-l-4 border-blue-500 pl-4 py-1">
                                    <p class="text-sm font-medium text-gray-900">2023-04-15</p>
                                    <p class="text-xs text-gray-600">农业物联网技术应用讲座</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 详情弹窗
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }
        
        // 点击模态框外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            const exportOptions = document.getElementById('exportOptions');
            if (!e.target.closest('.relative') && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 模拟点击查看详情
        document.querySelectorAll('tbody button').forEach(button => {
            button.addEventListener('click', openDetailModal);
        });
    </script>
</body>
</html>