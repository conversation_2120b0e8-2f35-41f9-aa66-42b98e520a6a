object CC_ScFpdDetailForm: TCC_ScFpdDetailForm
  Left = 0
  Top = 0
  ClientHeight = 664
  ClientWidth = 1108
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #24494#36719#38597#40657
  Font.Style = []
  OldCreateOrder = True
  Position = poScreenCenter
  PixelsPerInch = 96
  TextHeight = 17
  object MainPanel: TRzPanel
    Left = 0
    Top = 45
    Width = 1108
    Height = 434
    Align = alClient
    BorderOuter = fsNone
    BorderColor = clGradientActiveCaption
    BorderWidth = 1
    Color = clWhite
    TabOrder = 0
    object RightWhitePanel: TRzPanel
      Left = 1
      Top = 1
      Width = 1106
      Height = 12
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdBottom]
      BorderColor = clGradientActiveCaption
      BorderWidth = 1
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      ParentFont = False
      TabOrder = 0
    end
    object TablePanel: TRzPanel
      Left = 1
      Top = 13
      Width = 1106
      Height = 420
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      TabOrder = 1
      object GridPanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1106
        Height = 420
        Align = alClient
        BorderOuter = fsNone
        Color = clWhite
        TabOrder = 0
        object Label_NoRecordInfo: TRzLabel
          Left = 511
          Top = 80
          Width = 129
          Height = 28
          Caption = #26080' '#35746' '#21333' '#20449' '#24687
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clGray
          Font.Height = -21
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
        end
        object ScAdvStringGrid: TAdvStringGrid
          Left = 0
          Top = 0
          Width = 1106
          Height = 420
          Cursor = crDefault
          Align = alClient
          BevelInner = bvNone
          BevelOuter = bvNone
          BorderStyle = bsNone
          ColCount = 20
          Ctl3D = True
          DefaultRowHeight = 24
          DoubleBuffered = False
          DrawingStyle = gdsClassic
          FixedCols = 0
          FixedRows = 2
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing]
          ParentCtl3D = False
          ParentDoubleBuffered = False
          ParentFont = False
          PopupMenu = AdvPopupMenu2
          ScrollBars = ssBoth
          TabOrder = 0
          OnKeyDown = ScAdvStringGridKeyDown
          OnMouseDown = ScAdvStringGridMouseDown
          GridLineColor = 15855083
          GridFixedLineColor = 13745060
          HoverRowCells = [hcNormal, hcSelected]
          OnDblClickCell = ScAdvStringGridDblClickCell
          OnAnchorClick = ScAdvStringGridAnchorClick
          OnCellValidate = ScAdvStringGridCellValidate
          OnGetEditorType = ScAdvStringGridGetEditorType
          OnGetEditorProp = ScAdvStringGridGetEditorProp
          OnGetFloatFormat = ScAdvStringGridGetFloatFormat
          OnEditChange = ScAdvStringGridEditChange
          HighlightColor = clNone
          ActiveCellFont.Charset = DEFAULT_CHARSET
          ActiveCellFont.Color = clWindowText
          ActiveCellFont.Height = -12
          ActiveCellFont.Name = #24494#36719#38597#40657
          ActiveCellFont.Style = [fsBold]
          ActiveCellColor = 10344697
          ActiveCellColorTo = 6210033
          ControlLook.FixedGradientFrom = 16513526
          ControlLook.FixedGradientTo = 15260626
          ControlLook.FixedGradientHoverFrom = 15000287
          ControlLook.FixedGradientHoverTo = 14406605
          ControlLook.FixedGradientHoverMirrorFrom = 14406605
          ControlLook.FixedGradientHoverMirrorTo = 13813180
          ControlLook.FixedGradientHoverBorder = 12033927
          ControlLook.FixedGradientDownFrom = 14991773
          ControlLook.FixedGradientDownTo = 14991773
          ControlLook.FixedGradientDownMirrorFrom = 14991773
          ControlLook.FixedGradientDownMirrorTo = 14991773
          ControlLook.FixedGradientDownBorder = 14991773
          ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
          ControlLook.DropDownHeader.Font.Color = clWindowText
          ControlLook.DropDownHeader.Font.Height = -11
          ControlLook.DropDownHeader.Font.Name = 'Tahoma'
          ControlLook.DropDownHeader.Font.Style = []
          ControlLook.DropDownHeader.Visible = True
          ControlLook.DropDownHeader.Buttons = <>
          ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
          ControlLook.DropDownFooter.Font.Color = clWindowText
          ControlLook.DropDownFooter.Font.Height = -11
          ControlLook.DropDownFooter.Font.Name = 'Tahoma'
          ControlLook.DropDownFooter.Font.Style = []
          ControlLook.DropDownFooter.Visible = True
          ControlLook.DropDownFooter.Buttons = <>
          Filter = <>
          FilterDropDown.ColumnWidth = True
          FilterDropDown.Font.Charset = DEFAULT_CHARSET
          FilterDropDown.Font.Color = clWindowText
          FilterDropDown.Font.Height = -12
          FilterDropDown.Font.Name = #24494#36719#38597#40657
          FilterDropDown.Font.Style = []
          FilterDropDown.GlyphActive.Data = {
            36050000424D3605000000000000360400002800000010000000100000000100
            08000000000000010000530B0000530B00000001000000010000104A10001063
            100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
            63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
            8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
            FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
            1414020214141414141414141414141414030902141414141414141414141414
            030D090214141414141414141414141403130902141414141414141414141414
            0313090214141414141414141414141403130902141414141414141414141414
            0313090214141414141414141414141403130902141414141414141414141403
            130D09000214141414141414141403130D0D0501000214141414141414031311
            0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
            0806090909040100021403030303030303030303030303030303141414141414
            1414141414141414141414141414141414141414141414141414}
          FilterDropDown.Height = 200
          FilterDropDown.TextChecked = 'Checked'
          FilterDropDown.TextUnChecked = 'Unchecked'
          FilterDropDown.Width = 200
          FilterDropDownClear = #20840#37096
          FilterDropDownCheck = True
          FilterEdit.TypeNames.Strings = (
            'Starts with'
            'Ends with'
            'Contains'
            'Not contains'
            'Equal'
            'Not equal'
            'Clear')
          FixedFooters = 1
          FixedColWidth = 35
          FixedRowHeight = 24
          FixedRowAlways = True
          FixedFont.Charset = DEFAULT_CHARSET
          FixedFont.Color = clBlack
          FixedFont.Height = -12
          FixedFont.Name = #24494#36719#38597#40657
          FixedFont.Style = []
          FloatFormat = '%.2f'
          FloatingFooter.Visible = True
          GridImages = PngImageList1
          HoverButtons.Buttons = <>
          HoverButtons.Position = hbLeftFromColumnLeft
          HoverFixedCells = hfFixedRows
          HTMLSettings.ImageFolder = 'images'
          HTMLSettings.ImageBaseName = 'img'
          Look = glOffice2007
          PrintSettings.DateFormat = 'dd/mm/yyyy'
          PrintSettings.Font.Charset = DEFAULT_CHARSET
          PrintSettings.Font.Color = clWindowText
          PrintSettings.Font.Height = -11
          PrintSettings.Font.Name = 'Tahoma'
          PrintSettings.Font.Style = []
          PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
          PrintSettings.FixedFont.Color = clWindowText
          PrintSettings.FixedFont.Height = -11
          PrintSettings.FixedFont.Name = 'Tahoma'
          PrintSettings.FixedFont.Style = []
          PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
          PrintSettings.HeaderFont.Color = clWindowText
          PrintSettings.HeaderFont.Height = -11
          PrintSettings.HeaderFont.Name = 'Tahoma'
          PrintSettings.HeaderFont.Style = []
          PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
          PrintSettings.FooterFont.Color = clWindowText
          PrintSettings.FooterFont.Height = -11
          PrintSettings.FooterFont.Name = 'Tahoma'
          PrintSettings.FooterFont.Style = []
          PrintSettings.PageNumSep = '/'
          ScrollProportional = True
          ScrollSynch = True
          SearchFooter.Color = 16513526
          SearchFooter.ColorTo = clNone
          SearchFooter.FindNextCaption = 'Find &next'
          SearchFooter.FindPrevCaption = 'Find &previous'
          SearchFooter.Font.Charset = DEFAULT_CHARSET
          SearchFooter.Font.Color = clWindowText
          SearchFooter.Font.Height = -11
          SearchFooter.Font.Name = 'Tahoma'
          SearchFooter.Font.Style = []
          SearchFooter.HighLightCaption = 'Highlight'
          SearchFooter.HintClose = 'Close'
          SearchFooter.HintFindNext = 'Find next occurrence'
          SearchFooter.HintFindPrev = 'Find previous occurrence'
          SearchFooter.HintHighlight = 'Highlight occurrences'
          SearchFooter.MatchCaseCaption = 'Match case'
          SelectionColor = 6210033
          SortSettings.DefaultFormat = ssAutomatic
          URLUnderlineOnHover = True
          UseInternalHintClass = False
          VAlignment = vtaCenter
          Version = '8.1.3.0'
          ColWidths = (
            35
            51
            61
            59
            51
            54
            50
            51
            58
            54
            64
            64
            64
            64
            64
            64
            64
            64
            64
            64)
        end
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 1108
    Height = 45
    Align = alTop
    BorderOuter = fsNone
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    GradientColorStyle = gcsCustom
    ParentFont = False
    TabOrder = 1
    VisualStyle = vsGradient
    object TopMainPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1108
      Height = 45
      Align = alClient
      BorderOuter = fsNone
      BorderColor = 14671839
      BorderWidth = 1
      Color = 15849925
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object RzLabel4: TRzLabel
        Left = 584
        Top = 13
        Width = 57
        Height = 26
        Caption = #19968#36710#38388
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object RzLabel5: TRzLabel
        Left = 670
        Top = 20
        Width = 34
        Height = 19
        Caption = #25490' '#21333
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -15
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
      object Btn_Refresh: TAdvGlowButton
        Left = 845
        Top = 7
        Width = 100
        Height = 33
        BorderStyle = bsNone
        Caption = #31579#36873' '#22238#36864
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clGray
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        NotesFont.Charset = DEFAULT_CHARSET
        NotesFont.Color = clWindowText
        NotesFont.Height = -11
        NotesFont.Name = 'Tahoma'
        NotesFont.Style = []
        ParentFont = False
        Picture.Data = {
          89504E470D0A1A0A0000000D4948445200000020000000200806000000737A7A
          F4000006F3494441545885B5966B8C55D515C77F6BEF739F738779C00C333C94
          41405140D1B41F5462B4B652DBA6CFD87E30691362629A6A23681FB1B168AD5A
          1FD81062A345FB481B4CB4C45A8AADA6452C68AC2D940E08850A8CCA8CA30CDC
          B9F3B877EE3D7BAD7EB87798194405B42B5939FBECACBDD67FFDD7FF9C6CE114
          ECCBB736F0951B171197A205D9E4B43592EDF9FA4D97FCBDAB6BE7E0A9A49960
          D1C9049DB3C4F3BD758BC8A6A7E0CA4D6B5D545EF67AFF065AD333BB7CE2B46B
          9F1C80EFAE9FCEC59F3E8FE123EE8B513CF5B76F0F6FC994E23C91AFBB6768A8
          8FFDDB4FBFFBF70570F9B20CDF5ABD98F240A639E4A7AE8F43F7653D85DF13B9
          34E252245272FBB38F7CB8E22704D0D0062B9F99CB99B36753C9A76E49B8BA7B
          DFEC7F1643F13E030AA00F8BB7D29377143E340019FF72ED4F9AF8EAF20B28BC
          ED1666FCCCA78F0C77CE1A2CEF475C0A510171A82991ABBBA83175CEB6FA5678
          ED5F47D9F38F5E763CDFCBB68D03548AA701604A07DCBFF57CB2C9165CB9F991
          582BD7750F6CC2B914CE22546A816A0402CDE9731189702EBD2D93A87FC92713
          9BCA766463321315BBF6F5F2DC2FF6F3DCA36F513E890909C003AFCCA57DC6CC
          8F65DC199B7B0A5B3323F111BC4BA380A881505B3B54C0428C61A809A0442E47
          43E62C1CC91DD954D3AFCA927F38D390187EE9E97DFCFA07AFF2CE6BFAFE00D6
          742EA47572C7E01BF917EA2297C1C4216AB5CE1DAA2002AA86C8E8BB613540A8
          A266A819184CCECD27E51B7F67AEF49D4C63B4FF85F57B7968D96EB0F700B0BA
          F33C5275561A2E1F4D39936AE15A72C16A05DD3100A654993043D461E3C199A1
          4109C434A63BC8A5DA7F13CBE0375C3284954B377368576502000730383040C5
          F29F03C1CCD5DC6AEEC01CC10C706830020641C0044508410041836041401208
          69F243DD1CEC7BF9DA788442B1C0F9773E7F25B32ECABE9B81E6199E3BB7B653
          19B14BE28A6C119177D16E6AA838CCAABAB0717B6260A37B66A8B9B11833E210
          98946EC3B9F4925C8BDBB262E12686FA6C0C00402225ACDA3D9D50617E8865A7
          A9B8EA28AA89D4C6AFABF3370151C10C4CAA6C99569F2A861C63D21187985CAA
          051745D3FABA077AEEFFCCBFC74600501931BE3DEF104305DBED2266056C8830
          8EE231BA6F067746A6C12D4ED7C9D53E2337E1655D08BE1082A0781430F5C40A
          B17A54C148921FEEA35CD6A79A66A5693F37339181F176FB8B33A86F8E1ACB23
          EC55A545C677E7DC63A162CBEEBDAA8B190B52CCBA30CBD99765397371966241
          2F1A297287995C6D26C7BAAF7E1D0ED56AB928152DDAFEF4E1CE3FDDDD7D6200
          00B76C98CED4B393C9F220AF9ADA59666E94E643D9263FE3D685FB18191EFBAE
          24824FDDD8C215D74FA170442FD5B26C3293C86C4C0BC79AF06E55FF5BF18A47
          BFB6676C04C7DB7D9F3DC49EE787CBC9ACCC51DC7645501534C8F472D126CDBF
          A27E42BCC5F0E755EF70DB057B18EEB72DE2655EAC428C10AB108F8EC43CA122
          97379F999EA88113D9DAEB7AD9F99722A97A7FA106F9AB068F2254CA5C39EFB2
          DC09CF8C0C1B0F5EB5974A850320CB2DF69882A9605A5DC7EAE6E2FD070300F8
          E5377BD8FCF3A3649AFD271479421542EC97D64D7EFFABC4E3CB5F27CA460FC6
          0671EC093155578FC5E446F5F0810000FE78DF6136DC7D98BAC9FE1A33F75333
          C9BEA7786AF6C6B62186F28A99DF6556A53E363F0AA23F8C0AF2640000FCEDB1
          3EF23D1596DEDC761328F99EF081678A8342AA4E8AAAA32284601E8CCE62BF9D
          1A0080CE670A743E73729790D6F975D4B524290DE86235C3CC577560E022D970
          F8C0F0A90338155BFAC339140BF6790B78358F618020025152D6FC77D391FF1F
          80A52BE791694A5129EA5A004490B15FCE8F7CCA0FED78A2073849119E6AF18E
          4B9B894BE159119922E2C6FFEDFE99AAF7B7BDF8B3AE631B1F1903E2856BD62E
          A2697A9AF250D808EE93C785BC1265DCC77B5F1D60FBE3DD63E73E8AE20BBED0
          CA921B66139774AAC6B6059803D4E66E18764F22E3BFDFD359E00F2BFE33E1EC
          0406D2E93422427B7B3BAA8AAAD2D6D68673279894814FC3FCEB13245ACA9407
          C27284079480A128016789A79CF91B7251E39B873615D9787717E9549AD24869
          8C81BBEEBA8BD9B36793CBE550D56337A15153D551A62680555386A42FBCEC1F
          FFD2A01D5E8DB3F66031F5A16D7B6B98F3647DDCFA503634E773369994D5E313
          42321B212238E728954A1C3C781059B76E5D3A84701E301D68A9791A680272D5
          5EC900CDE301388BF440F2C573F3D15BB31A2BD3F293B4ADB7393EE3B5885449
          0991890E185A324C101B048E0225E09D9A1FF2DEEF8A42080DC0526021300D98
          0434D43A4ED57C542B5603E75462E9285FAC32E24610CD2AD6612E745428968E
          8B1FA9790CF40305A01BE80C21BC1901BDC08F015F4B9E00B2B524999A8F176B
          632D0695F878195780FC44A550ACB901C3B598121000FE077525A4433CEE124B
          0000000049454E44AE426082}
        Transparent = True
        TabOrder = 0
        OnClick = Btn_RefreshClick
        Appearance.Color = clSilver
        Appearance.ColorTo = 16316405
        Appearance.ColorChecked = 16316405
        Appearance.ColorCheckedTo = 16316405
        Appearance.ColorDisabled = 16316405
        Appearance.ColorDisabledTo = 16316405
        Appearance.ColorDown = 16316405
        Appearance.ColorDownTo = 16316405
        Appearance.ColorHot = 16316405
        Appearance.ColorHotTo = 16316405
        Appearance.ColorMirror = 16316405
        Appearance.ColorMirrorTo = 16316405
        Appearance.ColorMirrorHot = 16316405
        Appearance.ColorMirrorHotTo = 16316405
        Appearance.ColorMirrorDown = 16316405
        Appearance.ColorMirrorDownTo = 16316405
        Appearance.ColorMirrorChecked = 16316405
        Appearance.ColorMirrorCheckedTo = 16316405
        Appearance.ColorMirrorDisabled = 16316405
        Appearance.ColorMirrorDisabledTo = 16316405
        Layout = blGlyphLeftAdjusted
      end
    end
  end
  object ButtomPanel: TRzPanel
    Left = 0
    Top = 479
    Width = 1108
    Height = 185
    Align = alBottom
    BorderOuter = fsNone
    BorderColor = clGradientActiveCaption
    BorderWidth = 1
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    ParentFont = False
    TabOrder = 2
    object ButtomMainPanel: TRzPanel
      Left = 1
      Top = 1
      Width = 1106
      Height = 183
      Align = alClient
      BorderOuter = fsNone
      Color = clWhite
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      object ButtomLeftPanel: TRzPanel
        Left = 0
        Top = 0
        Width = 10
        Height = 183
        Align = alLeft
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        Transparent = True
      end
      object ButtomClientPanel: TRzPanel
        Left = 10
        Top = 0
        Width = 966
        Height = 183
        Align = alClient
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 1
        Transparent = True
        object AdvSmoothImageListBox1: TAdvSmoothImageListBox
          Left = 0
          Top = 0
          Width = 966
          Height = 183
          ScrollType = stNormal
          AnimationFactor = 1
          ZoomAnimationFactor = 1.500000000000000000
          SelectedItemIndex = 0
          Items = <>
          TopLayerItems = <
            item
              Top = 250
              Left = 250
              HTMLText.Location = cpCenterCenter
              HTMLText.Font.Charset = DEFAULT_CHARSET
              HTMLText.Font.Color = clWindowText
              HTMLText.Font.Height = -21
              HTMLText.Font.Name = 'Tahoma'
              HTMLText.Font.Style = []
              Fill.Color = clWhite
              Fill.ColorTo = clWhite
              Fill.ColorMirror = clNone
              Fill.ColorMirrorTo = clNone
              Fill.GradientType = gtVertical
              Fill.GradientMirrorType = gtSolid
              Fill.PictureLeft = 10
              Fill.PictureTop = 10
              Fill.PictureSize = psCustom
              Fill.PictureWidth = 75
              Fill.PictureHeight = 75
              Fill.Opacity = 114
              Fill.OpacityTo = 194
              Fill.BorderColor = clBlack
              Fill.Rounding = 20
              Fill.RoundingType = rtNone
              Fill.ShadowOffset = 0
              Fill.Glow = gmNone
              Width = 0
              Height = 0
              Tag = 0
            end>
          ItemAppearance.AutoSize = True
          ItemAppearance.TextVisible = True
          ItemAppearance.TextTop = 5
          ItemAppearance.TextHeight = 15
          ItemAppearance.ItemWidth = 137
          ItemAppearance.ItemHeight = 152
          ItemAppearance.Fill.Color = 16773091
          ItemAppearance.Fill.ColorTo = 16768452
          ItemAppearance.Fill.ColorMirror = 16765357
          ItemAppearance.Fill.ColorMirrorTo = 16767936
          ItemAppearance.Fill.GradientType = gtVertical
          ItemAppearance.Fill.GradientMirrorType = gtVertical
          ItemAppearance.Fill.BorderColor = 16765357
          ItemAppearance.Fill.Rounding = 0
          ItemAppearance.Fill.ShadowOffset = 0
          ItemAppearance.Fill.Glow = gmNone
          ItemAppearance.SelectedFill.Color = 11196927
          ItemAppearance.SelectedFill.ColorTo = 7257087
          ItemAppearance.SelectedFill.ColorMirror = 4370174
          ItemAppearance.SelectedFill.ColorMirrorTo = 8053246
          ItemAppearance.SelectedFill.GradientType = gtVertical
          ItemAppearance.SelectedFill.GradientMirrorType = gtVertical
          ItemAppearance.SelectedFill.BorderColor = 13611928
          ItemAppearance.SelectedFill.BorderWidth = 10
          ItemAppearance.SelectedFill.Rounding = 0
          ItemAppearance.SelectedFill.ShadowColor = clNone
          ItemAppearance.SelectedFill.ShadowOffset = 0
          ItemAppearance.SelectedFill.ShadowType = stSurround
          ItemAppearance.SelectedFill.Glow = gmNone
          ItemAppearance.DisabledFill.Color = 15921906
          ItemAppearance.DisabledFill.ColorTo = 11974326
          ItemAppearance.DisabledFill.ColorMirror = 11974326
          ItemAppearance.DisabledFill.ColorMirrorTo = 15921906
          ItemAppearance.DisabledFill.GradientType = gtVertical
          ItemAppearance.DisabledFill.GradientMirrorType = gtVertical
          ItemAppearance.DisabledFill.BorderColor = 16765357
          ItemAppearance.DisabledFill.Rounding = 0
          ItemAppearance.DisabledFill.ShadowOffset = 0
          ItemAppearance.DisabledFill.Glow = gmNone
          ItemAppearance.HoverFill.Color = 15465983
          ItemAppearance.HoverFill.ColorTo = 11332863
          ItemAppearance.HoverFill.ColorMirror = 5888767
          ItemAppearance.HoverFill.ColorMirrorTo = 10807807
          ItemAppearance.HoverFill.GradientType = gtVertical
          ItemAppearance.HoverFill.GradientMirrorType = gtVertical
          ItemAppearance.HoverFill.BorderColor = 10079963
          ItemAppearance.HoverFill.Rounding = 0
          ItemAppearance.HoverFill.ShadowOffset = 0
          ItemAppearance.HoverFill.Glow = gmNone
          ItemAppearance.HoverSize = 20
          ItemAppearance.Splitter.Fill.Color = 11196927
          ItemAppearance.Splitter.Fill.ColorTo = 7257087
          ItemAppearance.Splitter.Fill.ColorMirror = clNone
          ItemAppearance.Splitter.Fill.ColorMirrorTo = clNone
          ItemAppearance.Splitter.Fill.GradientType = gtHorizontal
          ItemAppearance.Splitter.Fill.GradientMirrorType = gtSolid
          ItemAppearance.Splitter.Fill.BorderColor = 16765357
          ItemAppearance.Splitter.Fill.Rounding = 0
          ItemAppearance.Splitter.Fill.ShadowOffset = 0
          ItemAppearance.Splitter.Fill.Glow = gmNone
          ItemAppearance.Splitter.TextLocation = cpBottomCenter
          ItemAppearance.Splitter.TextFont.Charset = DEFAULT_CHARSET
          ItemAppearance.Splitter.TextFont.Color = clWindowText
          ItemAppearance.Splitter.TextFont.Height = -11
          ItemAppearance.Splitter.TextFont.Name = 'Tahoma'
          ItemAppearance.Splitter.TextFont.Style = []
          ItemAppearance.Splitter.ExpanderColor = 16773091
          ItemAppearance.Splitter.ExpanderDownColor = 7257087
          ItemAppearance.Splitter.ExpanderHoverColor = 11196927
          Header.Caption = 'Header'
          Header.Font.Charset = DEFAULT_CHARSET
          Header.Font.Color = 7485192
          Header.Font.Height = -13
          Header.Font.Name = 'Tahoma'
          Header.Font.Style = []
          Header.Fill.Color = 16773091
          Header.Fill.ColorTo = 16765615
          Header.Fill.ColorMirror = clNone
          Header.Fill.ColorMirrorTo = clNone
          Header.Fill.GradientType = gtVertical
          Header.Fill.GradientMirrorType = gtSolid
          Header.Fill.BorderColor = 16765615
          Header.Fill.Rounding = 0
          Header.Fill.ShadowOffset = 0
          Header.Fill.Glow = gmNone
          Header.Visible = False
          Header.Navigator.Visible = False
          Header.Navigator.Color = 16773091
          Header.Navigator.HintNext = 'Next Item'
          Header.Navigator.HintPrevious = 'Previous Item'
          Header.Navigator.HintNextPage = 'Next Page'
          Header.Navigator.HintPreviousPage = 'Previous Page'
          Header.Navigator.DisabledColor = clGray
          Header.Navigator.HoverColor = 11196927
          Header.Navigator.DownColor = 7257087
          Header.Navigator.BorderColor = clBlack
          Footer.Caption = 'Footer'
          Footer.Font.Charset = DEFAULT_CHARSET
          Footer.Font.Color = 7485192
          Footer.Font.Height = -13
          Footer.Font.Name = 'Tahoma'
          Footer.Font.Style = []
          Footer.Fill.Color = 16773091
          Footer.Fill.ColorTo = 16765615
          Footer.Fill.ColorMirror = clNone
          Footer.Fill.ColorMirrorTo = clNone
          Footer.Fill.GradientType = gtVertical
          Footer.Fill.GradientMirrorType = gtSolid
          Footer.Fill.BorderColor = 16765615
          Footer.Fill.Rounding = 0
          Footer.Fill.ShadowOffset = 0
          Footer.Fill.Glow = gmNone
          Footer.Visible = False
          Footer.Navigator.Visible = True
          Footer.Navigator.Color = 16773091
          Footer.Navigator.HintNext = 'Next Item'
          Footer.Navigator.HintPrevious = 'Previous Item'
          Footer.Navigator.HintNextPage = 'Next Page'
          Footer.Navigator.HintPreviousPage = 'Previous Page'
          Footer.Navigator.DisabledColor = clGray
          Footer.Navigator.HoverColor = 11196927
          Footer.Navigator.DownColor = 7257087
          Footer.Navigator.BorderColor = clBlack
          Fill.Color = clNone
          Fill.ColorTo = clNone
          Fill.ColorMirror = clNone
          Fill.ColorMirrorTo = clNone
          Fill.GradientType = gtVertical
          Fill.GradientMirrorType = gtSolid
          Fill.HatchStyle = HatchStyleBackwardDiagonal
          Fill.HatchStyleMirror = HatchStyleBackwardDiagonal
          Fill.BackGroundPicturePosition = ppStretched
          Fill.OpacityTo = 138
          Fill.BorderColor = clBlack
          Fill.BorderOpacity = 151
          Fill.BorderWidth = 0
          Fill.Rounding = 0
          Fill.ShadowOffset = 0
          Fill.Glow = gmNone
          DefaultHTMLText.Location = cpTopLeft
          DefaultHTMLText.Font.Charset = DEFAULT_CHARSET
          DefaultHTMLText.Font.Color = clWindowText
          DefaultHTMLText.Font.Height = -11
          DefaultHTMLText.Font.Name = 'Tahoma'
          DefaultHTMLText.Font.Style = []
          Rows = 1
          ZoomOnDblClick = False
          ZoomMode = zmAspectRatio
          ShowScrollBar = False
          OnItemSelect = AdvSmoothImageListBox1ItemSelect
          OnItemDblClick = AdvSmoothImageListBox1ItemDblClick
          Align = alClient
          TabOrder = 0
          ParentShowHint = False
          ShowHint = True
          TMSStyle = 0
        end
      end
      object ButtomRightPanel: TRzPanel
        Left = 976
        Top = 0
        Width = 130
        Height = 183
        Align = alRight
        BorderOuter = fsNone
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 2
        Transparent = True
        object Btn_MultiSelectOk: TAdvGlowButton
          Left = 21
          Top = 82
          Width = 72
          Height = 28
          Caption = #30830'  '#23450
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlack
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 0
          OnClick = Btn_MultiSelectOkClick
          Appearance.BorderColor = 12631218
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 7915518
          Appearance.ColorCheckedTo = 11918331
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = 12631218
          Appearance.ColorHotTo = 12631218
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 12631218
          Appearance.ColorMirrorHotTo = 12631218
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 10480637
          Appearance.ColorMirrorCheckedTo = 5682430
          Appearance.ColorMirrorDisabled = 11974326
          Appearance.ColorMirrorDisabledTo = 15921906
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.GradientChecked = ggVertical
          Appearance.SystemFont = False
          Appearance.TextColorDown = clWhite
          Appearance.TextColorHot = clWhite
        end
        object Btn_MultiSelect: TAdvGlowButton
          Left = 21
          Top = 34
          Width = 72
          Height = 28
          Caption = #22810#36873#22270#29255
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clBlue
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          Spacing = 10
          TabOrder = 1
          OnClick = Btn_MultiSelectClick
          Appearance.BorderColor = 12631218
          Appearance.BorderColorHot = 10079963
          Appearance.BorderColorDown = 4548219
          Appearance.ColorTo = 16316405
          Appearance.ColorChecked = 7915518
          Appearance.ColorCheckedTo = 11918331
          Appearance.ColorDisabled = 16316405
          Appearance.ColorDisabledTo = 16316405
          Appearance.ColorDown = 12631218
          Appearance.ColorDownTo = 12631218
          Appearance.ColorHot = 12631218
          Appearance.ColorHotTo = 12631218
          Appearance.ColorMirror = 16316405
          Appearance.ColorMirrorTo = 16316405
          Appearance.ColorMirrorHot = 12631218
          Appearance.ColorMirrorHotTo = 12631218
          Appearance.ColorMirrorDown = 12631218
          Appearance.ColorMirrorDownTo = 12631218
          Appearance.ColorMirrorChecked = 10480637
          Appearance.ColorMirrorCheckedTo = 5682430
          Appearance.ColorMirrorDisabled = 11974326
          Appearance.ColorMirrorDisabledTo = 15921906
          Appearance.GradientHot = ggVertical
          Appearance.GradientMirrorHot = ggVertical
          Appearance.GradientDown = ggVertical
          Appearance.GradientMirrorDown = ggVertical
          Appearance.GradientChecked = ggVertical
          Appearance.SystemFont = False
          Appearance.TextColorDown = clWhite
          Appearance.TextColorHot = clWhite
        end
      end
    end
  end
  object AdvPopupMenu2: TAdvPopupMenu
    AutoHotkeys = maManual
    Version = '2.6.2.1'
    Left = 616
    Top = 232
    object CopyRecord: TMenuItem
      Caption = #36873#39033' '#22797#21046
      OnClick = CopyRecordClick
    end
  end
  object ActionList1: TActionList
    Left = 668
    Top = 164
    object actSc: TAction
      Category = 'Samples'
      Caption = 'Welcome'
      Checked = True
      ImageIndex = 44
      OnExecute = actScExecute
    end
  end
  object PngImageList1: TPngImageList
    PngImages = <
      item
        Background = clWindow
        Name = 'filter'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          61000000854944415478DA6364A010300E230316CE9FBFFAFCF9F321C4683234
          345C139F98188A62C0F4E9D3C56E5EBFFE921803D43535C53333335F617861C1
          DCB961172E5E5C894FB3BEA161586262E26A9C6180CF2BC84EC769003EAF203B
          1DA701B8BC82EE74BC06A07B059BD3091A80EC156C4E276800CC2BFF9998FE63
          733A5106100306DE0000FDD14611086D579B0000000049454E44AE426082}
      end
      item
        Background = clWindow
        Name = 'filterdown'
        PngImage.Data = {
          89504E470D0A1A0A0000000D49484452000000100000001008060000001FF3FF
          610000001974455874536F6674776172650041646F626520496D616765526561
          647971C9653C000002834944415478DAA5925F6852511CC77FF77AF57A9D7F33
          1B9A10C1A897D120073E864205494ECAA4A78D609B5B4F238A5583465039FAF3
          B0070B31A88788A811AC6D3E16582CC4EE8314D1838E25D80AFC537AFD37B5DB
          EF5E6D69592F1DF871EE39F77C3FE77BBEE7103CCFC3FF344200B85CAEA38944
          C2188FC7A152A908F314968E244935C330A0542AEB6AB5BAAAD56AABD85770BC
          8EFF16DB01E3C1603050AFD74540B95C068EAB423A5D029294824AA5028D4623
          808061E4E0F57A4E23E04E07001D80DFEF0F140A0514735845C86639A02819E0
          AEA0D3E94488CF77690A419B1D0087C301C964723C140A0532990C0AB3E86413
          36363890C918D0EBF560301840ABD5C0E4E4C9298542314F51D4AF0CAC562B94
          4AA53ECCC01E894402F97C1EC71548A5BE815CAE84DEDE1D28D6C1C484E73CBA
          599448241F3A426C0100017D98813D1A8D0638AE84AED2D0D3A30393C9082323
          CE590C710101EF11007F030821F6E1FCA1D5D5D7FEB5B5CF787E038C8D1DBF86
          C247643FFDF6CB010EBE1A8B00C2F517BF770508E0BD58EE959557573C9EC3D7
          31B487D43E26961A4521817F84A7D30E309BCD50ABD52097CB897DABCDFC04D0
          343D9DBD4DA29808E0BC058B4580770B401044B747B605A83BA4D355079E5B42
          BC193D72CA7237748F85063F0875FE9F808B08B88A80B9E23CBD0DAD0B3BC309
          A7DBF2E4D9022BAEE081FD03D0F67D7679F9E50DB7FBE06CF926EDB41DB35B7E
          DFE1C5D3E76C5707F8CA84EECCD252F896CB65BB509B6376E36E22A0DF336079
          F738D67440743A102E5785C54033EBE17038E6B3D9F6CF341A8DFBA2E0B23264
          1ADE35F0E9C14716A4C42050444706522C23D6F61660A87922228A6B52226048
          7A0E57EC417114C55ED849C30F2F303B69DD97E65D0000000049454E44AE4260
          82}
      end>
    Left = 816
    Top = 232
    Bitmap = {}
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 1
    OnTimer = Timer1Timer
    Left = 720
    Top = 240
  end
  object Timer2: TTimer
    Enabled = False
    Interval = 1
    Left = 552
    Top = 256
  end
end
