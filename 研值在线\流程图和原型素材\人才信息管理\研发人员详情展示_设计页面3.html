<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>研发人员详情展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题和操作按钮 -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">研发人员详情</h1>
                <p class="mt-2 text-gray-600">全面展示研发人员的全生命周期信息</p>
            </div>
            <div class="flex space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出简历
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    编辑信息
                </button>
            </div>
        </div>

        <!-- 基础信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-col md:flex-row gap-6">
                <!-- 头像区 -->
                <div class="w-full md:w-1/4 flex flex-col items-center">
                    <div class="w-32 h-32 rounded-full bg-gray-200 mb-4 overflow-hidden">
                        <img src="https://source.unsplash.com/300x300/?portrait" alt="研发人员头像" class="w-full h-full object-cover">
                    </div>
                    <div class="text-center">
                        <h2 class="text-xl font-bold text-gray-900">张明远</h2>
                        <p class="text-gray-600">高级研究员</p>
                        <p class="text-sm text-gray-500 mt-2">宁波市智能科技研究院</p>
                    </div>
                </div>
                
                <!-- 基础信息详情 -->
                <div class="w-full md:w-3/4">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">性别</p>
                            <p class="font-medium">男</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">年龄</p>
                            <p class="font-medium">38岁</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">出生日期</p>
                            <p class="font-medium">1985-06-15</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">学历</p>
                            <p class="font-medium">博士</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">学位</p>
                            <p class="font-medium">工学博士</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">毕业院校</p>
                            <p class="font-medium">浙江大学</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">技术职称</p>
                            <p class="font-medium">正高级工程师</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">技术领域</p>
                            <p class="font-medium">人工智能、机器学习</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">联系方式</p>
                            <p class="font-medium"><EMAIL></p>
                        </div>
                    </div>
                    
                    <!-- 个人简介 -->
                    <div class="mt-6">
                        <p class="text-sm text-gray-500">个人简介</p>
                        <p class="text-gray-700 mt-1">
                            张明远博士长期从事人工智能与机器学习领域研究，在计算机视觉和自然语言处理方向有深厚造诣。主持国家自然科学基金项目2项，省重点研发计划3项，发表SCI/EI论文40余篇，授权发明专利15项。现任宁波市智能科技研究院人工智能实验室主任，宁波市人工智能学会常务理事。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 成果信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">科技成果</h3>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>全部类型</option>
                        <option>专利</option>
                        <option>论文</option>
                        <option>软件著作权</option>
                    </select>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                        导出列表
                    </button>
                </div>
            </div>
            
            <!-- 专利成果 -->
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    专利成果 (15项)
                </h4>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利名称</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利号</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">基于深度学习的图像识别方法及系统</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202110123456.7</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-03-15</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                    <button class="hover:underline">查看详情</button>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">一种智能语音交互装置</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202098765432.1</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-12-20</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                    <button class="hover:underline">查看详情</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 论文成果 -->
            <div>
                <h4 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    论文成果 (28篇)
                </h4>
                <div class="space-y-4">
                    <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div class="flex justify-between">
                            <h5 class="text-md font-medium text-gray-900">Deep Learning for Image Recognition: A Comprehensive Review</h5>
                            <span class="text-sm text-gray-500">2022-05-15</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">IEEE Transactions on Pattern Analysis and Machine Intelligence (IF: 24.314)</p>
                        <div class="flex items-center mt-2">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">SCI</span>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Q1</span>
                        </div>
                    </div>
                    <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <div class="flex justify-between">
                            <h5 class="text-md font-medium text-gray-900">A Novel Approach to Natural Language Processing</h5>
                            <span class="text-sm text-gray-500">2021-11-30</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">Journal of Artificial Intelligence Research (IF: 6.789)</p>
                        <div class="flex items-center mt-2">
                            <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">SCI</span>
                            <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Q2</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 奖励信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">奖励荣誉</h3>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>全部级别</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                    </select>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                        导出列表
                    </button>
                </div>
            </div>
            
            <!-- 时间轴展示 -->
            <div class="relative">
                <!-- 时间轴线 -->
                <div class="absolute left-4 h-full w-0.5 bg-gray-200"></div>
                
                <!-- 奖励条目 -->
                <div class="relative pl-12 pb-8">
                    <div class="absolute left-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m8-8v7m-8-5v5m-8-8v7m8-12v3m0 0h8m-8 0H4" />
                        </svg>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <h4 class="text-lg font-medium text-gray-900">国家科学技术进步奖</h4>
                            <span class="text-sm text-gray-500">2022年</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">二等奖 - 基于人工智能的智能制造系统</p>
                        <p class="text-sm text-gray-500 mt-2">授奖单位：中华人民共和国科学技术部</p>
                    </div>
                </div>
                
                <div class="relative pl-12 pb-8">
                    <div class="absolute left-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m8-8v7m-8-5v5m-8-8v7m8-12v3m0 0h8m-8 0H4" />
                        </svg>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <h4 class="text-lg font-medium text-gray-900">浙江省科学技术奖</h4>
                            <span class="text-sm text-gray-500">2020年</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">一等奖 - 智能语音交互关键技术研究与应用</p>
                        <p class="text-sm text-gray-500 mt-2">授奖单位：浙江省科学技术厅</p>
                    </div>
                </div>
                
                <div class="relative pl-12">
                    <div class="absolute left-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m8-8v7m-8-5v5m-8-8v7m8-12v3m0 0h8m-8 0H4" />
                        </svg>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <h4 class="text-lg font-medium text-gray-900">宁波市科技创新奖</h4>
                            <span class="text-sm text-gray-500">2019年</span>
                        </div>
                        <p class="text-sm text-gray-600 mt-1">特等奖 - 人工智能在智能制造中的应用</p>
                        <p class="text-sm text-gray-500 mt-2">授奖单位：宁波市科学技术局</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 教育与工作经历区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">教育与工作经历</h3>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    导出简历
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 教育经历 -->
                <div>
                    <h4 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path d="M12 14l9-5-9-5-9 5 9 5z" />
                            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                        </svg>
                        教育经历
                    </h4>
                    <div class="space-y-4">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between">
                                <h5 class="font-medium text-gray-900">浙江大学</h5>
                                <span class="text-sm text-gray-500">2008-2013</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">计算机科学与技术 - 博士</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between">
                                <h5 class="font-medium text-gray-900">浙江大学</h5>
                                <span class="text-sm text-gray-500">2004-2008</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">计算机科学与技术 - 学士</p>
                        </div>
                    </div>
                </div>
                
                <!-- 工作经历 -->
                <div>
                    <h4 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        工作经历
                    </h4>
                    <div class="space-y-4">
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between">
                                <h5 class="font-medium text-gray-900">宁波市智能科技研究院</h5>
                                <span class="text-sm text-gray-500">2018-至今</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">人工智能实验室主任</p>
                        </div>
                        <div class="p-4 border border-gray-200 rounded-lg">
                            <div class="flex justify-between">
                                <h5 class="font-medium text-gray-900">宁波大学</h5>
                                <span class="text-sm text-gray-500">2013-2018</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">计算机科学与技术学院副教授</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 称号认定信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">称号认定</h3>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    导出列表
                </button>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex justify-between">
                        <h4 class="font-medium text-gray-900">国家"万人计划"</h4>
                        <span class="text-sm text-gray-500">2021年</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">科技领军人才</p>
                    <p class="text-xs text-gray-500 mt-2">认定单位：中共中央组织部</p>
                </div>
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex justify-between">
                        <h4 class="font-medium text-gray-900">浙江省"151人才工程"</h4>
                        <span class="text-sm text-gray-500">2019年</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">第一层次</p>
                    <p class="text-xs text-gray-500 mt-2">认定单位：浙江省人力资源和社会保障厅</p>
                </div>
                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div class="flex justify-between">
                        <h4 class="font-medium text-gray-900">宁波市"3315计划"</h4>
                        <span class="text-sm text-gray-500">2018年</span>
                    </div>
                    <p class="text-sm text-gray-600 mt-1">创新人才</p>
                    <p class="text-xs text-gray-500 mt-2">认定单位：宁波市人才工作领导小组</p>
                </div>
            </div>
        </div>

        <!-- 科研项目区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">科研项目</h3>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>全部状态</option>
                        <option>在研</option>
                        <option>结题</option>
                    </select>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                        导出列表
                    </button>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费(万元)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目角色</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">基于深度学习的智能制造关键技术研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">62133003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家自然科学基金重点项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">280</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">负责人</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">在研</span>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">智能语音交互关键技术研究与应用</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020C01001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">浙江省重点研发计划</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">150</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">负责人</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">结题</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 合作关系网络区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">合作关系网络</h3>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    导出图表
                </button>
            </div>
            
            <div class="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                <div class="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                    </svg>
                    <p class="mt-2 text-gray-500">合作关系网络图</p>
                    <p class="text-sm text-gray-400 mt-1">点击可查看合作者详情及合作项目</p>
                </div>
            </div>
            
            <!-- 合作统计 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-500">合作人员</p>
                    <p class="text-2xl font-bold text-gray-900">28</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-500">合作单位</p>
                    <p class="text-2xl font-bold text-gray-900">12</p>
                </div>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <p class="text-sm text-gray-500">合作项目</p>
                    <p class="text-2xl font-bold text-gray-900">15</p>
                </div>
            </div>
        </div>

        <!-- 政策享受信息区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">政策享受</h3>
                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">
                    导出列表
                </button>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">政策名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">补助类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">资助金额(万元)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">受理年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">办理状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市"3315计划"人才政策</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">创业资助</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">200</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已兑现</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市高层次人才安家补助</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">安家补助</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">80</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已兑现</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">浙江省"151人才工程"培养资助</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研资助</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">50</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已兑现</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 合作关系网络图
        document.addEventListener('DOMContentLoaded', function() {
            const networkCtx = document.createElement('canvas');
            networkCtx.id = 'networkChart';
            document.querySelector('.bg-gray-100').appendChild(networkCtx);
            
            const ctx = networkCtx.getContext('2d');
            networkCtx.width = document.querySelector('.bg-gray-100').clientWidth;
            networkCtx.height = document.querySelector('.bg-gray-100').clientHeight;
            
            // 模拟网络图数据
            const data = {
                nodes: [
                    {id: 1, label: '张明远', color: '#3B82F6', size: 30},
                    {id: 2, label: '李研究员', color: '#10B981', size: 20},
                    {id: 3, label: '王教授', color: '#10B981', size: 20},
                    {id: 4, label: '宁波大学', color: '#F59E0B', size: 25},
                    {id: 5, label: '宁波市智能科技研究院', color: '#F59E0B', size: 25},
                    {id: 6, label: '浙江大学', color: '#F59E0B', size: 25},
                ],
                edges: [
                    {from: 1, to: 2, color: '#E5E7EB'},
                    {from: 1, to: 3, color: '#E5E7EB'},
                    {from: 1, to: 4, color: '#E5E7EB'},
                    {from: 1, to: 5, color: '#E5E7EB'},
                    {from: 1, to: 6, color: '#E5E7EB'},
                    {from: 2, to: 5, color: '#E5E7EB'},
                    {from: 3, to: 4, color: '#E5E7EB'},
                    {from: 3, to: 6, color: '#E5E7EB'},
                ]
            };
            
            // 这里应该是实际的网络图绘制代码，此处仅作示意
            ctx.fillStyle = '#F3F4F6';
            ctx.fillRect(0, 0, networkCtx.width, networkCtx.height);
            
            // 模拟绘制节点
            data.nodes.forEach(node => {
                ctx.beginPath();
                ctx.arc(
                    Math.random() * networkCtx.width, 
                    Math.random() * networkCtx.height, 
                    node.size/2, 
                    0, 
                    2 * Math.PI
                );
                ctx.fillStyle = node.color;
                ctx.fill();
                
                // 节点标签
                ctx.fillStyle = '#111827';
                ctx.font = '12px Arial';
                ctx.fillText(node.label, 
                    Math.random() * networkCtx.width, 
                    Math.random() * networkCtx.height
                );
            });
            
            // 模拟绘制边
            data.edges.forEach(edge => {
                ctx.beginPath();
                ctx.moveTo(
                    Math.random() * networkCtx.width, 
                    Math.random() * networkCtx.height
                );
                ctx.lineTo(
                    Math.random() * networkCtx.width, 
                    Math.random() * networkCtx.height
                );
                ctx.strokeStyle = edge.color;
                ctx.lineWidth = 1;
                ctx.stroke();
            });
        });
    </script>
</body>
</html>