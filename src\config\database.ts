import { DataSourceConfig } from './types';

const databaseConfig: DataSourceConfig = {
  default: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME || 'ipo_dw',
    ssl: null,
    connectTimeout: 60000,
    debug: ['ComQueryPacket']
  },
  // 可以添加其他数据源配置
  // dataSource2: {
  //   host: process.env.DS2_HOST,
  //   port: parseInt(process.env.DS2_PORT || '3306'),
  //   ...
  // }
};

export const getDbConfig = (source: string = 'default'): DatabaseConfig => {
  const config = databaseConfig[source];
  if (!config) {
    throw new Error(`数据源 "${source}" 配置不存在`);
  }
  return config;
};

export const logDbConfig = (config: DatabaseConfig): void => {
  console.log("数据库连接参数:");
  console.log(`Host: ${config.host}`);
  console.log(`Port: ${config.port}`);
  console.log(`User: ${config.user}`);
  console.log(`Password length: ${config.password ? config.password.length : 0}`);
  console.log(`Database: ${config.database}`);
}; 