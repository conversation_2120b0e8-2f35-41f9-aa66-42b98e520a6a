<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软著管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">软著管理</h1>
            <p class="text-gray-600">软件著作权登记信息的全生命周期管理，为成果合规审计、市场化运营和创新评价提供可信的数据基础</p>
        </div>

        <!-- 筛选检索区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                </svg>
                条件筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">软件全称/简称</label>
                    <input type="text" placeholder="请输入软件名称关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">登记日期</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">分类号</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="01">01-系统软件</option>
                        <option value="02">02-应用软件</option>
                        <option value="03">03-嵌入式软件</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">著作权人</label>
                    <input type="text" placeholder="请输入著作权人名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="valid">有效</option>
                        <option value="expired">已到期</option>
                        <option value="pending">待续展</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-between mt-4">
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    高级筛选
                </button>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">软著总量</div>
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mt-2 flex items-end justify-between">
                    <div class="text-2xl font-bold text-gray-900">156</div>
                    <div class="text-xs text-green-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        12.5%
                    </div>
                </div>
                <div class="mt-2 h-1 w-full bg-gray-200 rounded-full">
                    <div class="h-1 bg-blue-600 rounded-full" style="width: 70%"></div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">年度新增</div>
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <div class="mt-2 flex items-end justify-between">
                    <div class="text-2xl font-bold text-gray-900">42</div>
                    <div class="text-xs text-green-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        8.3%
                    </div>
                </div>
                <div class="mt-2 h-1 w-full bg-gray-200 rounded-full">
                    <div class="h-1 bg-green-600 rounded-full" style="width: 45%"></div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">有效软著</div>
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mt-2 flex items-end justify-between">
                    <div class="text-2xl font-bold text-gray-900">128</div>
                    <div class="text-xs text-green-500">82.1%</div>
                </div>
                <div class="mt-2 h-1 w-full bg-gray-200 rounded-full">
                    <div class="h-1 bg-blue-600 rounded-full" style="width: 82%"></div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">即将到期</div>
                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="mt-2 flex items-end justify-between">
                    <div class="text-2xl font-bold text-gray-900">14</div>
                    <div class="text-xs text-red-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                        3.2%
                    </div>
                </div>
                <div class="mt-2 h-1 w-full bg-gray-200 rounded-full">
                    <div class="h-1 bg-yellow-600 rounded-full" style="width: 14%"></div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-4 hover:shadow-lg transition-shadow cursor-pointer">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">关联主体覆盖率</div>
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <div class="mt-2 flex items-end justify-between">
                    <div class="text-2xl font-bold text-gray-900">76.5%</div>
                    <div class="text-xs text-green-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        5.8%
                    </div>
                </div>
                <div class="mt-2 h-1 w-full bg-gray-200 rounded-full">
                    <div class="h-1 bg-green-600 rounded-full" style="width: 76%"></div>
                </div>
            </div>
        </div>

        <!-- 数据表格区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        软著列表
                    </h2>
                    <div class="flex space-x-2">
                        <div class="relative" id="exportDropdown">
                            <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                导出
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                </div>
                            </div>
                        </div>
                        <button onclick="openImportPanel()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导入
                        </button>
                        <button onclick="openSoftModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增软著
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">登记号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">软件全称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">著作权人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">登记日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2024SR012345</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">宁波市智慧港口管理系统</div>
                                <div class="text-sm text-gray-500">智慧港口</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">V2.1.0</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">02-应用软件</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市港口信息科技有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024-03-15</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewSoft('soft1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editSoft('soft1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteSoft('soft1')" class="text-red-600 hover:text-red-900">删除</button>
                                    <button onclick="linkEntity('soft1')" class="text-green-600 hover:text-green-900">关联主体</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2023SR987654</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">宁波新材料产业数据分析平台</div>
                                <div class="text-sm text-gray-500">新材料分析</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">V1.2.3</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">02-应用软件</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市新材料研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2023-11-20</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">即将到期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewSoft('soft2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editSoft('soft2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteSoft('soft2')" class="text-red-600 hover:text-red-900">删除</button>
                                    <button onclick="linkEntity('soft2')" class="text-green-600 hover:text-green-900">关联主体</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2024SR056789</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">智能制造生产执行系统</div>
                                <div class="text-sm text-gray-500">MES系统</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">V3.0.1</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">02-应用软件</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波智能制造有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2024-02-05</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewSoft('soft3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editSoft('soft3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteSoft('soft3')" class="text-red-600 hover:text-red-900">删除</button>
                                    <button onclick="linkEntity('soft3')" class="text-green-600 hover:text-green-900">关联主体</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">2022SR123456</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm font-medium text-gray-900">智慧城市公共服务平台</div>
                                <div class="text-sm text-gray-500">智慧城市</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">V1.5.2</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">02-应用软件</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">宁波市智慧城市建设有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">2022-08-12</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已到期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewSoft('soft4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editSoft('soft4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteSoft('soft4')" class="text-red-600 hover:text-red-900">删除</button>
                                    <button onclick="linkEntity('soft4')" class="text-green-600 hover:text-green-900">关联主体</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">156</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析区 -->
        <div class="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">分类号分布</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm">查看全部</button>
                </div>
                <div class="flex justify-center">
                    <svg class="w-64 h-64" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="40" fill="#3b82f6" stroke="white" stroke-width="2" stroke-dasharray="25,75" stroke-dashoffset="25" />
                        <circle cx="50" cy="50" r="40" fill="#10b981" stroke="white" stroke-width="2" stroke-dasharray="40,60" stroke-dashoffset="65" />
                        <circle cx="50" cy="50" r="40" fill="#f59e0b" stroke="white" stroke-width="2" stroke-dasharray="35,65" stroke-dashoffset="105" />
                        <text x="50" y="45" text-anchor="middle" font-size="10" fill="white">系统软件</text>
                        <text x="50" y="55" text-anchor="middle" font-size="10" fill="white">25%</text>
                    </svg>
                </div>
                <div class="mt-4 space-y-2">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-blue-600 mr-2"></div>
                        <span class="text-sm text-gray-700">系统软件 (25%)</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                        <span class="text-sm text-gray-700">应用软件 (40%)</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span class="text-sm text-gray-700">嵌入式软件 (35%)</span>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">年度登记趋势</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                </div>
                <div class="h-64 flex items-end space-x-1">
                    <div class="w-4 bg-blue-200 rounded-t" style="height: 30%"></div>
                    <div class="w-4 bg-blue-300 rounded-t" style="height: 50%"></div>
                    <div class="w-4 bg-blue-400 rounded-t" style="height: 40%"></div>
                    <div class="w-4 bg-blue-500 rounded-t" style="height: 70%"></div>
                    <div class="w-4 bg-blue-600 rounded-t" style="height: 100%"></div>
                    <div class="w-4 bg-blue-500 rounded-t" style="height: 80%"></div>
                    <div class="w-4 bg-blue-400 rounded-t" style="height: 60%"></div>
                </div>
                <div class="mt-2 flex justify-between text-xs text-gray-500">
                    <span>2020</span>
                    <span>2021</span>
                    <span>2022</span>
                    <span>2023</span>
                    <span>2024</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">到期提醒</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm">设置提醒</button>
                </div>
                <div class="h-64">
                    <svg class="w-full h-full" viewBox="0 0 100 100">
                        <path d="M10,90 L20,60 L30,70 L40,40 L50,50 L60,30 L70,20 L80,10 L90,5" stroke="#f59e0b" stroke-width="2" fill="none" />
                        <path d="M10,90 L20,60 L30,70 L40,40 L50,50 L60,30 L70,20 L80,10 L90,5" stroke="#ef4444" stroke-width="2" fill="none" stroke-dasharray="5,5" />
                    </svg>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                    <div class="flex items-center">
                        <div class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span>未来6个月到期</span>
                    </div>
                    <div class="flex items-center mt-1">
                        <div class="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                        <span>已到期</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入面板 -->
    <div id="importPanel" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入软著</h3>
                    <button onclick="closeImportPanel()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div class="flex items-center justify-between">
                            <a href="#" class="text-sm text-blue-600 hover:text-blue-800 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                下载导入模板
                            </a>
                            <span class="text-xs text-gray-500">支持.xlsx格式</span>
                        </div>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">最大支持10MB</p>
                        </div>
                        <div id="uploadProgress" class="hidden">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span class="text-sm font-medium text-gray-700">75%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div id="validationResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-800">发现2条数据存在问题</p>
                                    <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                        <li>第3行：登记号已存在</li>
                                        <li>第5行：分类号为必填项</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeImportPanel()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 软著编辑弹窗 -->
    <div id="softModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">软著信息编辑</h3>
                    <button onclick="closeSoftModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">登记号 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入登记号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">软件全称 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入软件全称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">软件简称</label>
                                <input type="text" placeholder="请输入软件简称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">版本号 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入版本号" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">分类号 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择分类号</option>
                                    <option value="01">01-系统软件</option>
                                    <option value="02">02-应用软件</option>
                                    <option value="03">03-嵌入式软件</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">著作权人 <span class="text-red-500">*</span></label>
                                <input type="text" placeholder="请输入著作权人" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">首次发表日期</label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">登记日期 <span class="text-red-500">*</span></label>
                                <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <!-- 证书扫描件 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">证书扫描件</label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>上传文件</span>
                                            <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                        </label>
                                        <p class="pl-1">或拖拽文件到此处</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, PDF 格式，最大10MB</p>
                                </div>
                            </div>
                        </div>

                        <!-- 关联主体 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">关联创新主体</label>
                            <div class="mt-1">
                                <select multiple class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="1">宁波市港口信息科技有限公司</option>
                                    <option value="2">宁波市新材料研究院</option>
                                    <option value="3">宁波智能制造有限公司</option>
                                    <option value="4">宁波市智慧城市建设有限公司</option>
                                </select>
                            </div>
                        </div>

                        <!-- 状态 -->
                        <div>
                            <label class="flex items-center">
                                <span class="mr-3 text-sm font-medium text-gray-700">生效状态:</span>
                                <div class="relative inline-block w-10 mr-2 align-middle select-none">
                                    <input type="checkbox" id="statusToggle" class="sr-only">
                                    <div class="block h-6 bg-gray-300 rounded-full w-12"></div>
                                    <div class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                                </div>
                                <span id="statusText" class="text-sm text-gray-700">已停用</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeSoftModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/2 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">软著详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6 overflow-y-auto flex-1">
                <div class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">登记号</label>
                            <p class="text-sm text-gray-900">2024SR012345</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">软件全称</label>
                            <p class="text-sm text-gray-900">宁波市智慧港口管理系统</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">软件简称</label>
                            <p class="text-sm text-gray-900">智慧港口</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                            <p class="text-sm text-gray-900">V2.1.0</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">分类号</label>
                            <p class="text-sm text-gray-900">02-应用软件</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">著作权人</label>
                            <p class="text-sm text-gray-900">宁波市港口信息科技有限公司</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">首次发表日期</label>
                            <p class="text-sm text-gray-900">2024-01-10</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">登记日期</label>
                            <p class="text-sm text-gray-900">2024-03-15</p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">证书扫描件</label>
                        <div class="border border-gray-300 rounded-md p-4">
                            <div class="flex justify-center">
                                <svg class="w-32 h-32 bg-gray-200 text-gray-500 rounded-md" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                    <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle">证书预览</text>
                                </svg>
                            </div>
                            <div class="mt-4 text-center">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看大图</button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">关联创新主体</label>
                        <div class="bg-gray-50 p-4 rounded-md">
                            <div class="flex items-center justify-between mb-2">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">宁波市港口信息科技有限公司</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                    <span class="text-sm font-medium text-gray-900">宁波市智慧城市建设有限公司</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-6 border-t bg-gray-50 flex-shrink-0">
                <button onclick="closeDetailDrawer()" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 批量导入面板
        function openImportPanel() {
            document.getElementById('importPanel').classList.remove('hidden');
        }
        
        function closeImportPanel() {
            document.getElementById('importPanel').classList.add('hidden');
        }
        
        // 软著编辑弹窗
        function openSoftModal() {
            document.getElementById('softModal').classList.remove('hidden');
        }
        
        function closeSoftModal() {
            document.getElementById('softModal').classList.add('hidden');
        }
        
        // 详情抽屉
        function openDetailDrawer() {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
        }
        
        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }
        
        // 状态开关
        document.getElementById('statusToggle').addEventListener('change', function() {
            const statusText = document.getElementById('statusText');
            if (this.checked) {
                statusText.textContent = '已启用';
                document.querySelector('.dot').classList.add('transform', 'translate-x-6');
            } else {
                statusText.textContent = '已停用';
                document.querySelector('.dot').classList.remove('transform', 'translate-x-6');
            }
        });
        
        // 模拟文件上传进度
        document.getElementById('file-upload').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                setTimeout(function() {
                    document.getElementById('validationResult').classList.remove('hidden');
                }, 2000);
            }
        });
        
        // 其他功能函数
        function viewSoft(softId) {
            openDetailDrawer();
            console.log('查看软著:', softId);
        }
        
        function editSoft(softId) {
            openSoftModal();
            console.log('编辑软著:', softId);
        }
        
        function deleteSoft(softId) {
            if (confirm('确定要删除这条软著记录吗？此操作不可恢复！')) {
                console.log('删除软著:', softId);
            }
        }
        
        function linkEntity(softId) {
            console.log('关联主体:', softId);
        }
        
        // 点击模态框外部关闭
        document.getElementById('importPanel').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportPanel();
            }
        });
        
        document.getElementById('softModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSoftModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
        
        // 自定义样式
        document.addEventListener('DOMContentLoaded', function() {
            // 状态开关样式
            const style = document.createElement('style');
            style.textContent = `
                #statusToggle:checked + .block {
                    background-color: #2563eb;
                }
                #statusToggle:checked ~ .dot {
                    transform: translateX(100%);
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>