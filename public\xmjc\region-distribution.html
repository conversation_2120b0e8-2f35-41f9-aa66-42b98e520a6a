<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域分布 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .region-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .region-card:hover {
            transform: scale(1.02);
            border-color: #3B82F6;
        }
        .region-active {
            border-color: #3B82F6;
            background: linear-gradient(135deg, #EBF8FF, #F0F9FF);
        }
        .rank-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }
        .rank-1 { background: #FFD700; color: #B45309; }
        .rank-2 { background: #C0C0C0; color: #374151; }
        .rank-3 { background: #CD7F32; color: white; }
        .rank-other { background: #6B7280; }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-map text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">区域分布</h1>
                        <p class="text-sm text-gray-600">Regional Distribution</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-cyan-500 text-white px-4 py-2 rounded-lg hover:bg-cyan-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        区域报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 视图切换器 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">视图模式</h3>
                <div class="flex space-x-4">
                    <button id="view-map" class="bg-cyan-500 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-map mr-2"></i>地图视图
                    </button>
                    <button id="view-chart" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        <i class="fas fa-chart-bar mr-2"></i>图表视图
                    </button>
                    <button id="view-table" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        <i class="fas fa-table mr-2"></i>表格视图
                    </button>
                </div>
            </div>
            
            <!-- 筛选器 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="text-sm font-medium text-gray-700 mb-2 block">统计维度</label>
                    <select class="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>项目数量</option>
                        <option>投资金额</option>
                        <option>平均金额</option>
                        <option>增长率</option>
                    </select>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700 mb-2 block">项目类型</label>
                    <select class="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>全部项目</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                    </select>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700 mb-2 block">时间范围</label>
                    <select class="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>2024年</option>
                        <option>2023年</option>
                        <option>近三年</option>
                        <option>近五年</option>
                    </select>
                </div>
                <div>
                    <label class="text-sm font-medium text-gray-700 mb-2 block">技术领域</label>
                    <select class="w-full bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>全部领域</option>
                        <option>新一代信息技术</option>
                        <option>高端装备制造</option>
                        <option>新材料</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 地图视图 -->
        <div id="map-view" class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 地图区域 -->
            <div class="lg:col-span-2 bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                        <i class="fas fa-map-marked-alt text-cyan-500 mr-2"></i>
                        宁波市区县分布图
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-xs bg-cyan-500 text-white px-3 py-1 rounded-lg">热力图</button>
                        <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">标记图</button>
                    </div>
                </div>
                
                <!-- 地图区域 -->
                <div class="h-96 bg-gradient-to-r from-cyan-50 to-cyan-100 rounded-lg chart-placeholder flex items-center justify-center mb-4">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-map text-5xl mb-4"></i>
                        <p class="text-xl">宁波市项目分布地图</p>
                        <p class="text-sm mt-2">点击区域查看详细信息</p>
                        <div class="grid grid-cols-2 gap-4 mt-4">
                            <div class="bg-white p-3 rounded-lg">
                                <p class="text-xs text-gray-600">覆盖区县</p>
                                <p class="text-lg font-bold text-cyan-600">10个</p>
                            </div>
                            <div class="bg-white p-3 rounded-lg">
                                <p class="text-xs text-gray-600">热点区域</p>
                                <p class="text-lg font-bold text-orange-600">3个</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图例 -->
                <div class="flex items-center justify-between text-sm">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-red-500 rounded"></div>
                            <span>500项以上</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-yellow-500 rounded"></div>
                            <span>200-500项</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-green-500 rounded"></div>
                            <span>100-200项</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span>100项以下</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 区域排名 -->
            <div class="space-y-6">
                <!-- TOP5区域 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                    <h4 class="font-semibold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                        项目数量TOP5
                    </h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-500">
                            <div>
                                <span class="text-sm font-medium text-gray-900">海曙区</span>
                                <p class="text-xs text-gray-600">Haishu District</p>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-yellow-600">486项</span>
                                <p class="text-xs text-gray-500">34.2亿元</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg border-l-4 border-gray-400">
                            <div>
                                <span class="text-sm font-medium text-gray-900">江北区</span>
                                <p class="text-xs text-gray-600">Jiangbei District</p>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-gray-600">423项</span>
                                <p class="text-xs text-gray-500">28.9亿元</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                            <div>
                                <span class="text-sm font-medium text-gray-900">镇海区</span>
                                <p class="text-xs text-gray-600">Zhenhai District</p>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-orange-600">378项</span>
                                <p class="text-xs text-gray-500">25.6亿元</p>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm">北仑区</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-blue-600">342项</span>
                                <span class="text-xs text-gray-500 block">22.8亿元</span>
                            </div>
                        </div>
                        
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm">鄞州区</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-green-600">298项</span>
                                <span class="text-xs text-gray-500 block">18.9亿元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 区域对比 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                    <h4 class="font-semibold text-gray-900 mb-4">区域发展指数</h4>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>项目密度</span>
                                <span>85%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>投资强度</span>
                                <span>78%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 78%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span>创新活跃度</span>
                                <span>92%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: 92%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表视图 -->
        <div id="chart-view" class="hidden grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 柱状图 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-chart-column text-blue-500 mr-2"></i>
                    区域项目数量对比
                </h3>
                
                <div class="h-80 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg chart-placeholder flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-column text-4xl mb-4"></i>
                        <p class="text-lg">区域对比柱状图</p>
                        <p class="text-sm mt-2">各区县项目数量横向对比</p>
                    </div>
                </div>
            </div>

            <!-- 饼图 -->
            <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-chart-pie text-green-500 mr-2"></i>
                    投资金额占比分布
                </h3>
                
                <div class="h-80 bg-gradient-to-r from-green-50 to-green-100 rounded-lg chart-placeholder flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-pie text-4xl mb-4"></i>
                        <p class="text-lg">投资占比饼图</p>
                        <p class="text-sm mt-2">各区县投资金额比例</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细区域列表 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">详细区域信息</h3>
                <div class="flex space-x-2">
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-filter mr-1"></i>筛选
                    </button>
                    <button class="text-sm bg-cyan-500 text-white px-3 py-1 rounded-lg hover:bg-cyan-600 transition-colors">
                        <i class="fas fa-sort mr-1"></i>排序
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 海曙区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="海曙区">
                    <div class="rank-badge rank-1">1</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">海曙区</h4>
                            <p class="text-sm text-gray-600">Haishu District</p>
                        </div>
                        <i class="fas fa-building text-blue-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">486项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">34.2亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">703万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+18.5%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">156</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">234</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">96</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>

                <!-- 江北区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="江北区">
                    <div class="rank-badge rank-2">2</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">江北区</h4>
                            <p class="text-sm text-gray-600">Jiangbei District</p>
                        </div>
                        <i class="fas fa-industry text-green-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">423项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">28.9亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">683万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+15.8%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">132</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">198</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">93</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>

                <!-- 镇海区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="镇海区">
                    <div class="rank-badge rank-3">3</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">镇海区</h4>
                            <p class="text-sm text-gray-600">Zhenhai District</p>
                        </div>
                        <i class="fas fa-ship text-orange-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">378项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">25.6亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">677万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+12.3%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">98</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">156</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">124</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>

                <!-- 北仑区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="北仑区">
                    <div class="rank-badge rank-other">4</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">北仑区</h4>
                            <p class="text-sm text-gray-600">Beilun District</p>
                        </div>
                        <i class="fas fa-anchor text-blue-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">342项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">22.8亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">667万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+14.2%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">89</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">145</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">108</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>

                <!-- 鄞州区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="鄞州区">
                    <div class="rank-badge rank-other">5</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">鄞州区</h4>
                            <p class="text-sm text-gray-600">Yinzhou District</p>
                        </div>
                        <i class="fas fa-microchip text-purple-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">298项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">18.9亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">634万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+16.7%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">76</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">134</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">88</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>

                <!-- 奉化区 -->
                <div class="region-card relative bg-white p-6 rounded-lg border-2 border-gray-200" data-region="奉化区">
                    <div class="rank-badge rank-other">6</div>
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h4 class="font-medium text-gray-900">奉化区</h4>
                            <p class="text-sm text-gray-600">Fenghua District</p>
                        </div>
                        <i class="fas fa-leaf text-green-500 text-2xl"></i>
                    </div>
                    
                    <div class="space-y-3 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">项目数量：</span>
                            <span class="font-medium">156项</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">投资金额：</span>
                            <span class="font-medium">9.8亿元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">平均金额：</span>
                            <span class="font-medium">628万元</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">增长率：</span>
                            <span class="font-medium text-green-600">+13.5%</span>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-2 text-xs">
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="font-medium text-blue-600">32</p>
                            <p class="text-gray-600">国家级</p>
                        </div>
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="font-medium text-green-600">78</p>
                            <p class="text-gray-600">省级</p>
                        </div>
                        <div class="text-center p-2 bg-purple-50 rounded">
                            <p class="font-medium text-purple-600">46</p>
                            <p class="text-gray-600">市级</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 视图切换
            const viewButtons = document.querySelectorAll('#view-map, #view-chart, #view-table');
            const mapView = document.getElementById('map-view');
            const chartView = document.getElementById('chart-view');

            function switchView(viewType) {
                viewButtons.forEach(btn => {
                    btn.className = 'bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors';
                });

                if (viewType === 'map') {
                    document.getElementById('view-map').className = 'bg-cyan-500 text-white px-4 py-2 rounded-lg font-medium transition-colors';
                    mapView.className = 'grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8';
                    chartView.className = 'hidden grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8';
                } else if (viewType === 'chart') {
                    document.getElementById('view-chart').className = 'bg-cyan-500 text-white px-4 py-2 rounded-lg font-medium transition-colors';
                    mapView.className = 'hidden grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8';
                    chartView.className = 'grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8';
                }
            }

            document.getElementById('view-map').addEventListener('click', () => switchView('map'));
            document.getElementById('view-chart').addEventListener('click', () => switchView('chart'));

            // 区域卡片点击
            document.addEventListener('click', function(e) {
                const card = e.target.closest('.region-card');
                if (card) {
                    // 移除其他卡片的激活状态
                    document.querySelectorAll('.region-card').forEach(c => {
                        c.classList.remove('region-active');
                    });
                    // 激活当前卡片
                    card.classList.add('region-active');
                    
                    const regionName = card.dataset.region;
                    console.log('查看区域详情:', regionName);
                }
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 