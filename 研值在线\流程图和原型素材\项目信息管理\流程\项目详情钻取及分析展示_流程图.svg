<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">项目详情钻取及分析展示业务流程</text>

  <!-- 阶段一：项目选择与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：项目选择与数据加载</text>
  
  <!-- 节点1: 项目选择 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">项目选择</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">通过项目列表或分析视图选择目标项目</text>
  </g>

  <!-- 节点2: 基础信息加载 -->
  <g transform="translate(550, 250)" filter="url(#soft-shadow)">
    <rect width="300" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">基础信息加载</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">系统加载项目全部基础信息与相关数据</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：关联信息整合 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：关联信息整合</text>

  <!-- 节点3: 团队信息关联 -->
  <g transform="translate(200, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">团队信息关联</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">项目团队、合作伙伴</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">利益相关者、历史变更记录</text>
  </g>

  <!-- 节点4: 成果信息关联 -->
  <g transform="translate(575, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果信息关联</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">论文、专利、产品、标准</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">形成项目与成果多维映射</text>
  </g>

  <!-- 节点5: 数据分析处理 -->
  <g transform="translate(950, 420)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据分析处理</text>
    <text x="125" y="50" text-anchor="middle" font-size="12" fill="#555">按统计维度分析</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">生成可视化图表</text>
  </g>

  <!-- 连接线 基础信息 -> 关联信息 -->
  <path d="M 600 320 C 500 350, 400 380, 325 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 320 Q 700 370 700 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 320 C 900 350, 1000 380, 1075 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：交互分析与展示 -->
  <text x="700" y="560" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：交互分析与展示</text>

  <!-- 节点6: 维度切换 -->
  <g transform="translate(150, 610)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">维度切换</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">自由切换统计维度</text>
    <text x="110" y="70" text-anchor="middle" font-size="12" fill="#555">筛选成果类型</text>
  </g>

  <!-- 节点7: 动态更新 -->
  <g transform="translate(450, 610)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态更新</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">实时更新图表</text>
    <text x="110" y="70" text-anchor="middle" font-size="12" fill="#555">支持数据导出</text>
  </g>

  <!-- 节点8: 深度钻取 -->
  <g transform="translate(750, 610)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">深度钻取</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">钻取成果或成员详情</text>
    <text x="110" y="70" text-anchor="middle" font-size="12" fill="#555">展示关联信息及履历</text>
  </g>

  <!-- 连接线 关联信息 -> 交互分析 -->
  <path d="M 325 500 C 300 530, 250 570, 260 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 500 C 650 530, 600 570, 560 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1075 500 C 1000 530, 900 570, 860 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 交互分析内部 -->
  <path d="M 370 650 Q 410 650 450 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 650 Q 710 650 750 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：全链路视图构建 -->
  <text x="700" y="760" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：全链路视图构建</text>
  
  <!-- 节点9: 全链路视图 -->
  <g transform="translate(450, 800)" filter="url(#soft-shadow)">
      <rect width="500" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="250" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">项目—团队—成果—合作全链路视图</text>
      <text x="250" y="55" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-150">多维度关联分析</tspan>
        <tspan dx="80">可视化展示</tspan>
        <tspan dx="80">深度洞察</tspan>
      </text>
  </g>

  <!-- 连接线 交互分析 -> 全链路视图 -->
  <path d="M 260 690 C 300 730, 400 770, 500 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 560 690 C 600 730, 650 770, 700 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 860 690 C 850 730, 800 770, 750 800" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从全链路视图回到项目选择 -->
  <path d="M 450 840 C 200 860, 100 500, 200 200 C 300 150, 450 150, 550 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="150" y="520" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 150, 520)">持续分析循环</text>

</svg>