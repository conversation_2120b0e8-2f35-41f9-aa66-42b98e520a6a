import mysql from 'mysql2/promise'
import { getDbConfig } from '@/config/database'

let pool: mysql.Pool | null = null

export function getDbPool(): mysql.Pool {
  if (!pool) {
    const cfg = getDbConfig('default')
    pool = mysql.createPool({
      host: cfg.host,
      port: cfg.port,
      user: cfg.user,
      password: cfg.password,
      database: cfg.database,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0,
      connectTimeout: cfg.connectTimeout,
    })
  }
  return pool
}

export async function query<T = any>(sql: string, params: any[] = []): Promise<T[]> {
  // 调试输出：SQL 与参数（仅在开发环境）
  if (process.env.NODE_ENV !== 'production') {
    console.log('[DB] SQL =>', sql.replace(/\s+/g, ' ').trim())
    console.log('[DB] Params =>', JSON.stringify(params))
  }
  const [rows] = await getDbPool().execute(sql, params)
  if (process.env.NODE_ENV !== 'production') {
    console.log('[DB] Rows =>', Array.isArray(rows) ? rows.length : 0)
  }
  return rows as T[]
}


