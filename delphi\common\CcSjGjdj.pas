unit CcSjGjdj;

interface

uses
  Classes;

type
  TCcSjGjdj = class
  private
    FSjGjdjid: Integer;
    FDdid: Integer;
    FXgj: double;
    FCj1: Double;
    FCjxs: Double;
    FCaij: Double;
    FBaoz: Double;
    FHjl: Double;
    FZhj: Double;
    FXl: Double;
    FMjdw: Double;
    FThmj: Double;
    FTangbt: Double;
    FBtdw: Double;
    FTiebt: Double;
    FTxmj: Double;
    FTjsd: Double;
    FTangd: Double;
    FDdw: Double;
    FTdk: Double;
    FThp: Double;
    FLdw: Double;
    FJxyp: Double;
    FJxxp: Double;
    FJsb: Double;
    FFlw: Double;
    FTzc: Double;
    FHsl1: Double;
    FKxl: Double;
    FXsl: Double;
    FFl: Double;
    FKxxl: Double;
    FXlsy: Double;
    FHsl2: Double;
    FCxl: Double;
    FTxk: Double;
    FThdg: Double;
    FXdg: Double;
    FKdk: Double;
    FJj: Double;
    FPxj: Double;
    FKpk: Double;
    FPxpk: Double;
    FLxk: Double;
    FSx: Double;
    FKmj: Double;
    FKbld: Double;
    FSlw: Double;
    FKhq: Double;
    FPhp: Double;
    FKxq: Double;
    FPqx: Double;
    FYdk: Double;
    FTd: Double;
    FPdk: Double;
    FPmj: Double;
    FDxcm: Double;
    FPsd: Double;
    FSmj: Double;
    FSl: Double;
    FYmj: Double;
    FBl: Double;
    FYlj: Double;
    FDsbx: Double;
    FTied: Double;
    FDtb: Double;
    FPdsp: Double;
    FZc: Double;
    FSt: Double;
    FDcw: Double;
    FPlw: Double;
    FJsl: Double;
    FYazk1: Double;
    FYxl: Double;
    FSjl: Double;
    FYazk2: Double;
    FLfk: Double;
    FDsd: Double;
    FJdg: Double;
    FYdg: Double;
    FZxdg: Double;
    FTdg: Double;
    FPcwb: Double;
    FYj: Double;
    FYzk1: Double;
    FYzk2: Double;
    FYzk3: Double;
    FYxk: Double;
    FYx: Double;
    FKds: Double;
    FJdb: Double;
    FJxk: Double;
    FYlw: Double;
    FYzk: Double;
    FLlgt: Double;
    FDy: Double;
    FDw: Double;
    FDn: Double;
    FJxt: Double;
    FJy: Double;
    FTj: Double;
    FCjgj: Double;
    FTp: Double;
    FWb: Double;
    FMjzc: Double;
    FLc: Double;
    FSzl: Double;
    FC25: Double;
    FC50: Double;
    FCjhj: Double;
    FDtgj: Double;
    FBzgj: Double;
    FDjbz: Double;
    FCz: Double;
    FBzhj: Double;
    FCl: Double;
    FCxb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
    FDdh: string;
    FPort: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
  public
    property SjGjdjid: Integer read FSjGjdjid write FSjGjdjid;
    property Ddid: Integer read FDdid write FDdid;
    property Xgj: double read FXgj write FXgj;
    property Cj1: Double read FCj1 write FCj1;
    property Cjxs: Double read FCjxs write FCjxs;
    property Caij: Double read FCaij write FCaij;
    property Baoz: Double read FBaoz write FBaoz;
    property Hjl: Double read FHjl write FHjl;
    property Zhj: Double read FZhj write FZhj;
    property Xl: Double read FXl write FXl;
    property Mjdw: Double read FMjdw write FMjdw;
    property Thmj: Double read FThmj write FThmj;
    property Tangbt: Double read FTangbt write FTangbt;
    property Btdw: Double read FBtdw write FBtdw;
    property Tiebt: Double read FTiebt write FTiebt;
    property Txmj: Double read FTxmj write FTxmj;
    property Tjsd: Double read FTjsd write FTjsd;
    property Tangd: Double read FTangd write FTangd;
    property Ddw: Double read FDdw write FDdw;
    property Tdk: Double read FTdk write FTdk;
    property Thp: Double read FThp write FThp;
    property Ldw: Double read FLdw write FLdw;
    property Jxyp: Double read FJxyp write FJxyp;
    property Jxxp: Double read FJxxp write FJxxp;
    property Jsb: Double read FJsb write FJsb;
    property Flw: Double read FFlw write FFlw;
    property Tzc: Double read FTzc write FTzc;
    property Hsl1: Double read FHsl1 write FHsl1;
    property Kxl: Double read FKxl write FKxl;
    property Xsl: Double read FXsl write FXsl;
    property Fl: Double read FFl write FFl;
    property Kxxl: Double read FKxxl write FKxxl;
    property Xlsy: Double read FXlsy write FXlsy;
    property Hsl2: Double read FHsl2 write FHsl2;
    property Cxl: Double read FCxl write FCxl;
    property Txk: Double read FTxk write FTxk;
    property Thdg: Double read FThdg write FThdg;
    property Xdg: Double read FXdg write FXdg;
    property Kdk: Double read FKdk write FKdk;
    property Jj: Double read FJj write FJj;
    property Pxj: Double read FPxj write FPxj;
    property Kpk: Double read FKpk write FKpk;
    property Pxpk: Double read FPxpk write FPxpk;
    property Lxk: Double read FLxk write FLxk;
    property Sx: Double read FSx write FSx;
    property Kmj: Double read FKmj write FKmj;
    property Kbld: Double read FKbld write FKbld;
    property Slw: Double read FSlw write FSlw;
    property Khq: Double read FKhq write FKhq;
    property Php: Double read FPhp write FPhp;
    property Kxq: Double read FKxq write FKxq;
    property Pqx: Double read FPqx write FPqx;
    property Ydk: Double read FYdk write FYdk;
    property Td: Double read FTd write FTd;
    property Pdk: Double read FPdk write FPdk;
    property Pmj: Double read FPmj write FPmj;
    property Dxcm: Double read FDxcm write FDxcm;
    property Psd: Double read FPsd write FPsd;
    property Smj: Double read FSmj write FSmj;
    property Sl: Double read FSl write FSl;
    property Ymj: Double read FYmj write FYmj;
    property Bl: Double read FBl write FBl;
    property Ylj: Double read FYlj write FYlj;
    property Dsbx: Double read FDsbx write FDsbx;
    property Tied: Double read FTied write FTied;
    property Dtb: Double read FDtb write FDtb;
    property Pdsp: Double read FPdsp write FPdsp;
    property Zc: Double read FZc write FZc;
    property St: Double read FSt write FSt;
    property Dcw: Double read FDcw write FDcw;
    property Plw: Double read FPlw write FPlw;
    property Jsl: Double read FJsl write FJsl;
    property Yazk1: Double read FYazk1 write FYazk1;
    property Yxl: Double read FYxl write FYxl;
    property Sjl: Double read FSjl write FSjl;
    property Yazk2: Double read FYazk2 write FYazk2;
    property Lfk: Double read FLfk write FLfk;
    property Dsd: Double read FDsd write FDsd;
    property Jdg: Double read FJdg write FJdg;
    property Ydg: Double read FYdg write FYdg;
    property Zxdg: Double read FZxdg write FZxdg;
    property Tdg: Double read FTdg write FTdg;
    property Pcwb: Double read FPcwb write FPcwb;
    property Yj: Double read FYj write FYj;
    property Yzk1: Double read FYzk1 write FYzk1;
    property Yzk2: Double read FYzk2 write FYzk2;
    property Yzk3: Double read FYzk3 write FYzk3;
    property Yxk: Double read FYxk write FYxk;
    property Yx: Double read FYx write FYx;
    property Kds: Double read FKds write FKds;
    property Jdb: Double read FJdb write FJdb;
    property Jxk: Double read FJxk write FJxk;
    property Ylw: Double read FYlw write FYlw;
    property Yzk: Double read FYzk write FYzk;
    property Llgt: Double read FLlgt write FLlgt;
    property Dy: Double read FDy write FDy;
    property Dw: Double read FDw write FDw;
    property Dn: Double read FDn write FDn;
    property Jxt: Double read FJxt write FJxt;
    property Jy: Double read FJy write FJy;
    property Tj: Double read FTj write FTj;
    property Cjgj: Double read FCjgj write FCjgj;
    property Tp: Double read FTp write FTp;
    property Wb: Double read FWb write FWb;
    property Mjzc: Double read FMjzc write FMjzc;
    property Lc: Double read FLc write FLc;
    property Szl: Double read FSzl write FSzl;
    property C25: Double read FC25 write FC25;
    property C50: Double read FC50 write FC50;
    property Cjhj: Double read FCjhj write FCjhj;
    property Dtgj: Double read FDtgj write FDtgj;
    property Bzgj: Double read FBzgj write FBzgj;
    property Djbz: Double read FDjbz write FDjbz;
    property Cz: Double read FCz write FCz;
    property Bzhj: Double read FBzhj write FBzhj;
    property Cl: Double read FCl write FCl;
    property Cxb: Double read FCxb write FCxb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
    property Ddh: string read FDdh write FDdh;
    property Port: string read FPort write FPort;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
  end;

implementation

end.
