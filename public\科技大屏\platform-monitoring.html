<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>重点平台监测 - 科技创新监测平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white border-b border-gray-200 card-shadow sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-layer-group text-blue-500 mr-3"></i>
                        重点平台监测
                    </h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-calendar-alt"></i>
                        <span>数据时间：2025年1~6月</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出平台清单
                    </button>
                    <button onclick="window.open('index.html', '_self')" class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回总览
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="gradient-bg min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            
            <!-- 统计概览 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">平台总数</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">198</p>
                            <p class="text-xs text-blue-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+6.8%
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-layer-group text-blue-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">国家级平台</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">12</p>
                            <p class="text-xs text-green-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>+2个
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-crown text-green-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">有在研项目</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">42</p>
                            <p class="text-xs text-purple-600 mt-1">
                                <i class="fas fa-project-diagram mr-1"></i>活跃
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-flask text-purple-600"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl card-shadow p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">异常平台</p>
                            <p class="text-2xl font-bold text-gray-900 mt-1">2</p>
                            <p class="text-xs text-red-600 mt-1">
                                <i class="fas fa-exclamation-triangle mr-1"></i>需关注
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选区域 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">平台筛选与分析</h3>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-sm">列表视图</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-lg text-sm hover:bg-gray-200">分布图</button>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">平台级别</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部级别</option>
                            <option value="national">国家级</option>
                            <option value="provincial">省级</option>
                            <option value="municipal">市级</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">平台类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部类型</option>
                            <option value="lab">重点实验室</option>
                            <option value="center">工程技术中心</option>
                            <option value="innovation">创新中心</option>
                            <option value="incubator">孵化器</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">"510"领域</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部领域</option>
                            <option value="smart">智能制造</option>
                            <option value="bio">生物医药</option>
                            <option value="material">新材料</option>
                            <option value="energy">新能源</option>
                            <option value="digital">数字经济</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">依托单位</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                            <option value="">全部单位</option>
                            <option value="university">高等院校</option>
                            <option value="institute">科研院所</option>
                            <option value="enterprise">企业</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 平台分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 级别分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">平台级别分布</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-yellow-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">国家级平台</span>
                                    <p class="text-xs text-gray-600">包括国家重点实验室、工程中心等</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-yellow-600">12</span>
                                <span class="text-xs text-gray-500 block">6.1%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-blue-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">省级平台</span>
                                    <p class="text-xs text-gray-600">省级重点实验室、技术中心</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-blue-600">86</span>
                                <span class="text-xs text-gray-500 block">43.4%</span>
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between p-4 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                                <div>
                                    <span class="text-sm font-medium text-gray-900">市级平台</span>
                                    <p class="text-xs text-gray-600">市级重点实验室、工程中心</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="text-lg font-bold text-green-600">100</span>
                                <span class="text-xs text-gray-500 block">50.5%</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- "510"领域分布 -->
                <div class="bg-white rounded-xl card-shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">"510"领域平台布局</h3>
                    <div class="h-64 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg flex items-center justify-center relative">
                        <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop&crop=entropy" 
                             alt="平台领域分布图" 
                             class="w-full h-full object-cover rounded-lg opacity-70">
                        <div class="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                            <div class="text-center text-blue-800">
                                <i class="fas fa-chart-pie text-3xl mb-2"></i>
                                <p class="font-semibold">"510"领域分布图</p>
                                <p class="text-sm mt-1">覆盖率: 92.4%</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4 text-center">
                        <span class="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                            <i class="fas fa-info-circle mr-1"></i>
                            平台布局覆盖所有重点领域
                        </span>
                    </div>
                </div>
            </div>

            <!-- 重点平台清单 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">重点平台清单</h3>
                    <div class="flex items-center space-x-2">
                        <input type="text" placeholder="搜索平台名称..." 
                               class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 text-sm">
                        <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm">
                            <i class="fas fa-search mr-1"></i>搜索
                        </button>
                    </div>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="bg-gray-50 border-b border-gray-200">
                                <th class="px-4 py-3 text-left font-medium text-gray-700">平台名称</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">级别</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">类型</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">依托单位</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">"510"领域</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">在研项目</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">状态</th>
                                <th class="px-4 py-3 text-left font-medium text-gray-700">操作</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-crown text-yellow-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">先进制造技术国家重点实验室</p>
                                            <p class="text-xs text-gray-500">成立时间: 2018年</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">国家级</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">重点实验室</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波大学</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">智能制造</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">18</span>
                                        <div class="w-12 bg-gray-200 rounded-full h-2">
                                            <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">活跃</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-flask text-blue-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">浙江省生物医药技术重点实验室</p>
                                            <p class="text-xs text-gray-500">成立时间: 2020年</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">省级</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs">重点实验室</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">中科院宁波材料所</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-pink-100 text-pink-700 rounded-full text-xs">生物医药</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">12</span>
                                        <div class="w-12 bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-500 h-2 rounded-full" style="width: 60%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">正常</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-cogs text-green-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">宁波市新材料工程技术中心</p>
                                            <p class="text-xs text-gray-500">成立时间: 2019年</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">市级</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">工程中心</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波新材料科技城</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">新材料</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-gray-900 mr-2">8</span>
                                        <div class="w-12 bg-gray-200 rounded-full h-2">
                                            <div class="bg-orange-500 h-2 rounded-full" style="width: 40%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs">关注</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-gray-400 hover:text-gray-600 text-xs">监测</button>
                                </td>
                            </tr>
                            
                            <tr class="hover:bg-gray-50 bg-red-50">
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-exclamation-triangle text-red-600 text-xs"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">智能装备创新中心</p>
                                            <p class="text-xs text-gray-500">成立时间: 2021年</p>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">市级</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-teal-100 text-teal-700 rounded-full text-xs">创新中心</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="text-sm text-gray-900">宁波智能制造研究院</span>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs">智能制造</span>
                                </td>
                                <td class="px-4 py-3">
                                    <div class="flex items-center">
                                        <span class="text-sm font-medium text-red-600 mr-2">0</span>
                                        <div class="w-12 bg-gray-200 rounded-full h-2">
                                            <div class="bg-red-500 h-2 rounded-full" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    <span class="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs">异常</span>
                                </td>
                                <td class="px-4 py-3">
                                    <button class="text-blue-600 hover:text-blue-800 text-xs mr-2">详情</button>
                                    <button class="text-red-600 hover:text-red-800 text-xs">处理</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div class="flex items-center justify-between mt-6">
                    <div class="text-sm text-gray-500">
                        显示 1-10 条，共 198 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-500">上一页</button>
                        <button class="px-3 py-1 bg-blue-500 text-white rounded text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <span class="px-2 text-gray-400">...</span>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">20</button>
                        <button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>

            <!-- 异常平台提醒 -->
            <div class="bg-white rounded-xl card-shadow p-6 border-l-4 border-red-500">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-medium text-red-800">异常平台预警</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>发现 <strong>2个</strong> 平台存在异常情况，需要及时关注和处理：</p>
                            <ul class="list-disc list-inside mt-2 space-y-1">
                                <li>智能装备创新中心：6个月内无新增在研项目</li>
                                <li>材料技术研发中心：平台负责人变更未及时报备</li>
                            </ul>
                        </div>
                        <div class="mt-4">
                            <button class="bg-red-100 text-red-800 px-4 py-2 rounded-lg text-sm hover:bg-red-200 transition-colors">
                                <i class="fas fa-eye mr-1"></i>
                                查看详细预警信息
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技创新监测平台. 保留所有权利.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>数据更新频率：实时</span>
                    <span>|</span>
                    <span>监测平台总数：198个</span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html> 