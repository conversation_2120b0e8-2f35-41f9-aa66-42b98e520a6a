unit CcYsFldj;

interface

uses
  Classes;

type
  TCcYsFldj = class
  private

    FYsfldjid: Integer;
    FYsid: Integer;
    FSbsys: Double;
    FSbdj: Double;
    FSbje: Double;
    FDpsys: Double;
    FDpdj: Double;
    FDpje: Double;
    FNksys: Double;
    FNkdj: Double;
    FNkje: Double;
    FNhcsys: Double;
    FNhcdj: Double;
    FNhcje: Double;
    FXsys: Double;
    FXdj: Double;
    FXje: Double;
    FLlsys: Double;
    FLldj: Double;
    FLlje: Double;
    FJdggid: string;
    FJdsys: Double;
    FJddj: Double;
    FJdje: Double;
    FTxmsys: Double;
    FTxmdj: Double;
    FTxmje: Double;
    FCzsys: Double;
    FCzdj: Double;
    FCzje: Double;
    FCbsys: Double;
    FCbdj: Double;
    FCbje: Double;
    FBzqtje: Double;
    FZxsys: Double;
    FZxdj: Double;
    FZxje: Double;
    FFgdsys: Double;
    FFgddj: Double;
    FFgdje: Double;
    FPmbmid_a: string;
    FQtsl_a: Double;
    FQtdj_a: Double;
    FQtje_a: Double;
    FPmbmid_b: string;
    FQtsl_b: Double;
    FQtdj_b: Double;
    FQtje_b: Double;
    FBz: string;
    FXjdsys: Double;
    FXjddj: Double;
    FXjdje: Double;
    FZdsys: Double;
    FZddj: Double;
    FZdje: Double;
    FLldsys: Double;
    FLlddj: Double;
    FLldje: Double;
    FPmbmid_c: string;
    FQtsl_c: Double;
    FQtdj_c: Double;
    FQtje_c: Double;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Ysfldjid: Integer read FYsfldjid write FYsfldjid;
    property Ysid: Integer read FYsid write FYsid;
    property Sbsys: Double read FSbsys write FSbsys;
    property Sbdj: Double read FSbdj write FSbdj;
    property Sbje: Double read FSbje write FSbje;
    property Dpsys: Double read FDpsys write FDpsys;
    property Dpdj: Double read FDpdj write FDpdj;
    property Dpje: Double read FDpje write FDpje;
    property Nksys: Double read FNksys write FNksys;
    property Nkdj: Double read FNkdj write FNkdj;
    property Nkje: Double read FNkje write FNkje;
    property Nhcsys: Double read FNhcsys write FNhcsys;
    property Nhcdj: Double read FNhcdj write FNhcdj;
    property Nhcje: Double read FNhcje write FNhcje;
    property Xsys: Double read FXsys write FXsys;
    property Xdj: Double read FXdj write FXdj;
    property Xje: Double read FXje write FXje;
    property Llsys: Double read FLlsys write FLlsys;

    property Lldj: Double read FLldj write FLldj;
    property Llje: Double read FLlje write FLlje;
    property Jdggid: string read FJdggid write FJdggid;
    property Jdsys: Double read FJdsys write FJdsys;
    property Jddj: Double read FJddj write FJddj;
    property Jdje: Double read FJdje write FJdje;
    property Txmsys: Double read FTxmsys write FTxmsys;
    property Txmdj: Double read FTxmdj write FTxmdj;
    property Txmje: Double read FTxmje write FTxmje;
    property Czsys: Double read FCzsys write FCzsys;
    property Czdj: Double read FCzdj write FCzdj;
    property Czje: Double read FCzje write FCzje;
    property Cbsys: Double read FCbsys write FCbsys;

    property Cbdj: Double read FCbdj write FCbdj;
    property Cbje: Double read FCbje write FCbje;
    property Bzqtje: Double read FBzqtje write FBzqtje;

    property Zxsys: Double read FZxsys write FZxsys;
    property Zxdj: Double read FZxdj write FZxdj;
    property Zxje: Double read FZxje write FZxje;

    property Fgdsys: Double read FFgdsys write FFgdsys;
    property Fgddj: Double read FFgddj write FFgddj;
    property Fgdje: Double read FFgdje write FFgdje;

    property Pmbmid_a: string read FPmbmid_a write FPmbmid_a;
    property Qtsl_a: Double read FQtsl_a write FQtsl_a;
    property Qtdj_a: Double read FQtdj_a write FQtdj_a;
    property Qtje_a: Double read FQtje_a write FQtje_a;
    property Pmbmid_b: string read FPmbmid_b write FPmbmid_b;
    property Qtsl_b: Double read FQtsl_b write FQtsl_b;
    property Qtdj_b: Double read FQtdj_b write FQtdj_b;
    property Qtje_b: Double read FQtje_b write FQtje_b;

    property Xjdsys: Double read FXjdsys write FXjdsys;
    property Xjddj: Double read FXjddj write FXjddj;
    property Xjdje: Double read FXjdje write FXjdje;

    property Zdsys: Double read FZdsys write FZdsys;
    property Zddj: Double read FZddj write FZddj;
    property Zdje: Double read FZdje write FZdje;

    property Lldsys: Double read FLldsys write FLldsys;
    property Llddj: Double read FLlddj write FLlddj;
    property Lldje: Double read FLldje write FLldje;
    property Pmbmid_c: string read FPmbmid_c write FPmbmid_c;
    property Qtsl_c: Double read FQtsl_c write FQtsl_c;
    property Qtdj_c: Double read FQtdj_c write FQtdj_c;
    property Qtje_c: Double read FQtje_c write FQtje_c;

    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
