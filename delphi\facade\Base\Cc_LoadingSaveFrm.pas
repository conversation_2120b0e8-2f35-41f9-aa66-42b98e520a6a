unit Cc_LoadingSaveFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingSaveForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingSaveForm: TLoadingSaveForm;

implementation

{$R *.dfm}

procedure TLoadingSaveForm.FormCreate(Sender: TObject);
begin
  LoadingSaveForm.left := (screen.width - LoadingSaveForm.width) div 2;
  LoadingSaveForm.top := (screen.height - LoadingSaveForm.height) div 2;
end;

procedure TLoadingSaveForm.FormShow(Sender: TObject);
begin
  // self.RxGIFAnimator1.Animate := true;
end;

end.
