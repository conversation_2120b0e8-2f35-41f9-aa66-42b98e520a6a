unit CcSjLwdj;

interface
uses
  Classes;

type
  TCcSjLwdj = class
  private

    FSjlwdjid: Integer;
    FDdid: Integer;
    FLwgysid: string;
    FTgs: Double;
    FPbj: Double;
    FLwcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjlwdjid: integer read FSjlwdjid write FSjlwdjid;
    property Ddid: integer read FDdid write FDdid;
    property Lwgysid: string read FLwgysid write FLwgysid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Lwcb: double read FLwcb write FLwcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

