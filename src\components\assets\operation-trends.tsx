'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'

// 模拟趋势数据
const trendData = [
  {
    date: '03-22',
    '故障处理': 18,
    '变更管理': 12,
    '发布部署': 8,
    '日常维护': 15
  },
  {
    date: '03-23',
    '故障处理': 15,
    '变更管理': 14,
    '发布部署': 10,
    '日常维护': 13
  },
  {
    date: '03-24',
    '故障处理': 20,
    '变更管理': 15,
    '发布部署': 12,
    '日常维护': 16
  },
  {
    date: '03-25',
    '故障处理': 16,
    '变更管理': 13,
    '发布部署': 9,
    '日常维护': 14
  },
  {
    date: '03-26',
    '故障处理': 22,
    '变更管理': 16,
    '发布部署': 11,
    '日常维护': 18
  },
  {
    date: '03-27',
    '故障处理': 19,
    '变更管理': 15,
    '发布部署': 13,
    '日常维护': 15
  },
  {
    date: '03-28',
    '故障处理': 24,
    '变更管理': 18,
    '发布部署': 15,
    '日常维护': 20
  }
]

const colorMap = {
  '故障处理': '#1664FF',
  '变更管理': '#00B679',
  '发布部署': '#FF6B00',
  '日常维护': '#8E33FF'
}

export function OperationTrends() {
  return (
    <Card className="border-[#E5E9EF]">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>运维趋势</CardTitle>
          <p className="text-sm text-gray-500 mt-1">近7天运维工作量趋势</p>
        </div>
        <div className="flex items-center gap-4">
          <Select defaultValue="7days">
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="时间范围" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">近7天</SelectItem>
              <SelectItem value="14days">近14天</SelectItem>
              <SelectItem value="30days">近30天</SelectItem>
              <SelectItem value="90days">近90天</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mt-2 space-y-4">
          {/* 图例标签 */}
          <div className="flex items-center gap-4 px-2">
            {Object.entries(colorMap).map(([key, color]) => (
              <Badge 
                key={key}
                variant="outline" 
                className="flex items-center gap-1.5"
              >
                <div 
                  className="w-2 h-2 rounded-full" 
                  style={{ backgroundColor: color }}
                />
                {key}
              </Badge>
            ))}
          </div>
          
          {/* 趋势图表 */}
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={trendData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E9EF" />
                <XAxis 
                  dataKey="date" 
                  stroke="#666"
                  fontSize={12}
                />
                <YAxis 
                  stroke="#666"
                  fontSize={12}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: '#fff',
                    border: '1px solid #E5E9EF',
                    borderRadius: '6px',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
                  }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="故障处理"
                  stroke={colorMap['故障处理']}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line
                  type="monotone"
                  dataKey="变更管理"
                  stroke={colorMap['变更管理']}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line
                  type="monotone"
                  dataKey="发布部署"
                  stroke={colorMap['发布部署']}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
                <Line
                  type="monotone"
                  dataKey="日常维护"
                  stroke={colorMap['日常维护']}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* 统计摘要 */}
          <div className="grid grid-cols-4 gap-4 mt-4">
            {Object.entries(colorMap).map(([key, color]) => (
              <div key={key} className="p-3 bg-gray-50 rounded-lg">
                <div className="text-sm text-gray-500">{key}</div>
                <div className="text-xl font-semibold mt-1" style={{ color }}>
                  {trendData.reduce((sum, item) => sum + item[key], 0)}
                </div>
                <div className="text-xs text-gray-400 mt-1">7天总量</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 