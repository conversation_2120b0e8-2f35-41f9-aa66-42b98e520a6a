<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>政策匹配规则管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">政策匹配规则管理</h1>
                    <p class="mt-2 text-sm text-gray-600">为政策智能匹配体系提供可配置、可版本化的规则库支撑，确保匹配结果的准确性与可追溯性</p>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- 版本管理下拉 -->
                    <div class="relative">
                        <button onclick="toggleVersionDropdown()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            版本管理
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="versionDropdown" class="hidden absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border z-50">
                            <div class="p-4">
                                <h3 class="text-lg font-semibold text-gray-900 mb-3">规则版本历史</h3>
                                <div class="space-y-3 max-h-80 overflow-y-auto">
                                    <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                                        <div>
                                            <div class="font-medium text-gray-900">高新技术企业匹配规则 v3.2.1</div>
                                            <div class="text-sm text-gray-600">创建人：张三 | 2024-01-15 14:30</div>
                                            <div class="text-xs text-green-600">当前生效版本</div>
                                        </div>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800 p-1" title="对比差异">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">高新技术企业匹配规则 v3.2.0</div>
                                            <div class="text-sm text-gray-600">创建人：李四 | 2024-01-10 09:15</div>
                                            <div class="text-xs text-gray-500">历史版本</div>
                                        </div>
                                        <div class="flex space-x-1">
                                            <button class="text-green-600 hover:text-green-800 p-1" title="设为生效">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                            <button class="text-blue-600 hover:text-blue-800 p-1" title="对比差异">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                                                </svg>
                                            </button>
                                            <button class="text-orange-600 hover:text-orange-800 p-1" title="回滚">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">科技型中小企业匹配规则 v2.1.5</div>
                                            <div class="text-sm text-gray-600">创建人：王五 | 2024-01-08 16:45</div>
                                            <div class="text-xs text-gray-500">历史版本</div>
                                        </div>
                                        <div class="flex space-x-1">
                                            <button class="text-green-600 hover:text-green-800 p-1" title="设为生效">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                            </button>
                                            <button class="text-blue-600 hover:text-blue-800 p-1" title="对比差异">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"></path>
                                                </svg>
                                            </button>
                                            <button class="text-orange-600 hover:text-orange-800 p-1" title="回滚">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button onclick="openRuleModal('create')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        新建规则
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">条件筛选</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">规则名称</label>
                    <input type="text" placeholder="请输入规则名称" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">版本号</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部版本</option>
                        <option value="v3.2.1">v3.2.1</option>
                        <option value="v3.2.0">v3.2.0</option>
                        <option value="v2.1.5">v2.1.5</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">创建人</label>
                    <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部创建人</option>
                        <option value="张三">张三</option>
                        <option value="李四">李四</option>
                        <option value="王五">王五</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                    <input type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm font-medium text-gray-700">生效状态：</span>
                    <label class="flex items-center">
                        <input type="checkbox" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">已生效</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">未生效</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">停用</span>
                    </label>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        重置
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 规则列表区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">规则列表</h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规则名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">版本号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配条件摘要</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">生效状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">引用次数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后更新</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">高新技术企业匹配规则</div>
                                <div class="text-sm text-gray-500">针对高新技术企业的政策匹配</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">v3.2.1</td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">企业类型=高新技术企业 AND 研发投入≥10% AND 发明专利≥5件</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">已生效</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,234</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewRule(1)" class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openRuleModal('edit', 1)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="copyRule(1)" class="text-green-600 hover:text-green-900">复制</button>
                                    <button onclick="toggleRuleStatus(1, 'disable')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                    <button onclick="deleteRule(1, '高新技术企业匹配规则')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">科技型中小企业匹配规则</div>
                                <div class="text-sm text-gray-500">针对科技型中小企业的政策匹配</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">v2.1.5</td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">企业类型=科技型中小企业 AND 年营收≤5000万 AND 研发人员≥10%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">已生效</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">856</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-08 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewRule(2)" class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openRuleModal('edit', 2)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="copyRule(2)" class="text-green-600 hover:text-green-900">复制</button>
                                    <button onclick="toggleRuleStatus(2, 'disable')" class="text-yellow-600 hover:text-yellow-900">停用</button>
                                    <button onclick="deleteRule(2, '科技型中小企业匹配规则')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">制造业企业匹配规则</div>
                                <div class="text-sm text-gray-500">针对制造业企业的政策匹配</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">v1.8.3</td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">行业代码=制造业 AND 年营收≥1亿 AND 技术改造投入≥5%</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">未生效</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05 10:20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewRule(3)" class="text-blue-600 hover:text-blue-900">查看</button>
                                    <button onclick="openRuleModal('edit', 3)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="copyRule(3)" class="text-green-600 hover:text-green-900">复制</button>
                                    <button onclick="toggleRuleStatus(3, 'enable')" class="text-green-600 hover:text-green-900">启用</button>
                                    <button onclick="deleteRule(3, '制造业企业匹配规则')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        上一页
                    </button>
                    <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下一页
                    </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条记录
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">上一页</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <button class="bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                1
                            </button>
                            <button class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                2
                            </button>
                            <button class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                                3
                            </button>
                            <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">下一页</span>
                                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 规则编辑弹窗 -->
    <div id="ruleModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-6xl shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 id="modalTitle" class="text-xl font-semibold text-gray-900">新建匹配规则</h3>
                <button onclick="closeRuleModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 基本信息 -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则名称 <span class="text-red-500">*</span></label>
                        <input type="text" id="ruleName" name="ruleName" required placeholder="请输入规则名称" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">版本号 <span class="text-red-500">*</span></label>
                        <input type="text" id="ruleVersion" name="ruleVersion" required placeholder="如：v1.0.0" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">生效时间</label>
                        <input type="datetime-local" id="effectiveTime" name="effectiveTime" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">规则说明</label>
                        <textarea id="ruleDescription" name="ruleDescription" rows="4" placeholder="请输入规则说明" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                    </div>
                </div>
                
                <!-- 匹配条件编辑器 -->
                <div>
                    <h4 class="text-lg font-medium text-gray-900 mb-4">匹配条件配置</h4>
                    <div class="space-y-4">
                        <!-- 条件组1 -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-3">
                                <h5 class="font-medium text-gray-900">条件组 1</h5>
                                <button class="text-red-600 hover:text-red-800">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="grid grid-cols-3 gap-3 mb-3">
                                <select class="border border-gray-300 rounded px-3 py-2">
                                    <option>企业类型</option>
                                    <option>年营业收入</option>
                                    <option>研发投入占比</option>
                                    <option>发明专利数量</option>
                                    <option>行业代码</option>
                                </select>
                                <select class="border border-gray-300 rounded px-3 py-2">
                                    <option>等于</option>
                                    <option>大于</option>
                                    <option>大于等于</option>
                                    <option>小于</option>
                                    <option>小于等于</option>
                                    <option>包含</option>
                                </select>
                                <input type="text" placeholder="条件值" class="border border-gray-300 rounded px-3 py-2">
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">+ 添加条件</button>
                                <select class="text-sm border border-gray-300 rounded px-2 py-1">
                                    <option>AND</option>
                                    <option>OR</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 条件组2 -->
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-center mb-3">
                                <h5 class="font-medium text-gray-900">条件组 2</h5>
                                <button class="text-red-600 hover:text-red-800">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                            <div class="grid grid-cols-3 gap-3 mb-3">
                                <select class="border border-gray-300 rounded px-3 py-2">
                                    <option>研发投入占比</option>
                                    <option>企业类型</option>
                                    <option>年营业收入</option>
                                    <option>发明专利数量</option>
                                    <option>行业代码</option>
                                </select>
                                <select class="border border-gray-300 rounded px-3 py-2">
                                    <option>大于等于</option>
                                    <option>等于</option>
                                    <option>大于</option>
                                    <option>小于</option>
                                    <option>小于等于</option>
                                    <option>包含</option>
                                </select>
                                <input type="text" placeholder="10%" value="10%" class="border border-gray-300 rounded px-3 py-2">
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="text-blue-600 hover:text-blue-800 text-sm">+ 添加条件</button>
                                <select class="text-sm border border-gray-300 rounded px-2 py-1">
                                    <option>AND</option>
                                    <option>OR</option>
                                </select>
                            </div>
                        </div>
                        
                        <button class="w-full border-2 border-dashed border-gray-300 rounded-lg p-4 text-gray-500 hover:border-blue-500 hover:text-blue-500">
                            + 添加条件组
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 条件测试区 -->
            <div class="mt-6">
                <button onclick="toggleTestPanel()" class="flex items-center text-blue-600 hover:text-blue-800 font-medium">
                    <svg id="testPanelIcon" class="w-4 h-4 mr-2 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    条件测试
                </button>
                <div id="testPanel" class="hidden mt-4 border border-gray-200 rounded-lg p-4">
                    <h5 class="font-medium text-gray-900 mb-3">测试数据输入</h5>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm text-gray-700 mb-1">企业类型</label>
                            <select class="w-full border border-gray-300 rounded px-3 py-2">
                                <option>高新技术企业</option>
                                <option>科技型中小企业</option>
                                <option>一般企业</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm text-gray-700 mb-1">年营业收入（万元）</label>
                            <input type="number" placeholder="12000" class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-700 mb-1">研发投入占比（%）</label>
                            <input type="number" placeholder="12" class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>
                        <div>
                            <label class="block text-sm text-gray-700 mb-1">发明专利数量（件）</label>
                            <input type="number" placeholder="15" class="w-full border border-gray-300 rounded px-3 py-2">
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <button onclick="runTest()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                            运行测试
                        </button>
                        <div id="testResult" class="hidden">
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                匹配成功 - 得分：95分
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                <button type="button" onclick="closeRuleModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                    取消
                </button>
                <button type="button" onclick="saveRule()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                    保存规则
                </button>
            </div>
        </div>
    </div>

    <script>
        // 版本管理下拉菜单
        function toggleVersionDropdown() {
            const dropdown = document.getElementById('versionDropdown');
            dropdown.classList.toggle('hidden');
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('versionDropdown');
            const button = event.target.closest('button');
            if (!button || !button.onclick || button.onclick.toString().indexOf('toggleVersionDropdown') === -1) {
                dropdown.classList.add('hidden');
            }
        });

        // 打开规则编辑弹窗
        function openRuleModal(mode, ruleId = null) {
            const modal = document.getElementById('ruleModal');
            const title = document.getElementById('modalTitle');
            
            if (mode === 'create') {
                title.textContent = '新建匹配规则';
                clearRuleForm();
            } else if (mode === 'edit') {
                title.textContent = '编辑匹配规则';
                fillRuleForm(ruleId);
            }
            
            modal.classList.remove('hidden');
        }

        // 关闭规则编辑弹窗
        function closeRuleModal() {
            document.getElementById('ruleModal').classList.add('hidden');
        }

        // 清空表单
        function clearRuleForm() {
            document.getElementById('ruleName').value = '';
            document.getElementById('ruleVersion').value = '';
            document.getElementById('effectiveTime').value = '';
            document.getElementById('ruleDescription').value = '';
        }

        // 填充表单数据（编辑模式）
        function fillRuleForm(ruleId) {
            // 模拟从服务器获取数据
            const mockData = {
                1: {
                    name: '高新技术企业匹配规则',
                    version: 'v3.2.1',
                    effectiveTime: '2024-01-15T14:30',
                    description: '针对高新技术企业的政策匹配规则，包含企业类型、研发投入、知识产权等多维度条件'
                },
                2: {
                    name: '科技型中小企业匹配规则',
                    version: 'v2.1.5',
                    effectiveTime: '2024-01-08T16:45',
                    description: '针对科技型中小企业的政策匹配规则，重点关注企业规模和研发能力'
                }
            };
            
            const data = mockData[ruleId];
            if (data) {
                document.getElementById('ruleName').value = data.name;
                document.getElementById('ruleVersion').value = data.version;
                document.getElementById('effectiveTime').value = data.effectiveTime;
                document.getElementById('ruleDescription').value = data.description;
            }
        }

        // 保存规则
        function saveRule() {
            // 表单验证
            const ruleName = document.getElementById('ruleName').value;
            const ruleVersion = document.getElementById('ruleVersion').value;
            
            if (!ruleName || !ruleVersion) {
                alert('请填写必填字段！');
                return;
            }
            
            // 模拟保存过程
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '保存中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                alert('规则保存成功！');
                closeRuleModal();
                // 这里可以刷新列表
            }, 2000);
        }

        // 查看规则详情
        function viewRule(ruleId) {
            alert(`查看规则详情：规则ID ${ruleId}`);
        }

        // 复制规则
        function copyRule(ruleId) {
            if (confirm('确定要复制这个规则吗？')) {
                alert(`规则 ${ruleId} 已复制，新版本号为 v${Math.random().toFixed(1)}`);
            }
        }

        // 切换规则状态
        function toggleRuleStatus(ruleId, action) {
            const actionText = action === 'enable' ? '启用' : '停用';
            if (confirm(`确定要${actionText}这个规则吗？`)) {
                alert(`规则 ${ruleId} 已${actionText}`);
            }
        }

        // 删除规则
        function deleteRule(ruleId, ruleName) {
            if (confirm(`确定要删除规则"${ruleName}"吗？此操作不可撤销。`)) {
                alert(`规则"${ruleName}"已删除`);
            }
        }

        // 切换测试面板
        function toggleTestPanel() {
            const panel = document.getElementById('testPanel');
            const icon = document.getElementById('testPanelIcon');
            
            panel.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }

        // 运行测试
        function runTest() {
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = '测试中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = originalText;
                button.disabled = false;
                document.getElementById('testResult').classList.remove('hidden');
            }, 1500);
        }

        // 点击模态框外部关闭
        document.getElementById('ruleModal').addEventListener('click', function(event) {
            if (event.target === this) {
                closeRuleModal();
            }
        });
    </script>
</body>
</html>