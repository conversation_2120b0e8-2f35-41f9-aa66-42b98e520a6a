'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertCircle,
  Clock,
  Users,
  BarChart3,
  PieChart,
  Activity,
  Target,
  TrendingUp,
  Calendar,
  Building2
} from "lucide-react"

// 部门运维分布数据
const departmentData = [
  { name: "技术部", count: 486, percent: 37.8 },
  { name: "运营部", count: 325, percent: 25.3 },
  { name: "产品部", count: 248, percent: 19.3 },
  { name: "市场部", count: 156, percent: 12.1 },
  { name: "其他部门", count: 71, percent: 5.5 }
]

// 运维类型分布
const typeDistribution = [
  { type: "故障处理", count: 385, percent: 29.9 },
  { type: "变更管理", count: 312, percent: 24.3 },
  { type: "发布部署", count: 286, percent: 22.2 },
  { type: "日常维护", count: 198, percent: 15.4 },
  { type: "其他工作", count: 105, percent: 8.2 }
]

// 处理团队效能
const teamPerformance = [
  {
    team: "平台组",
    metrics: {
      workload: 386,
      avgResponse: "4.8分钟",
      satisfaction: "96.5%",
      completion: "98.2%"
    }
  },
  {
    team: "应用组",
    metrics: {
      workload: 425,
      avgResponse: "5.2分钟",
      satisfaction: "95.8%",
      completion: "97.5%"
    }
  },
  {
    team: "网络组",
    metrics: {
      workload: 245,
      avgResponse: "6.5分钟",
      satisfaction: "94.2%",
      completion: "96.8%"
    }
  },
  {
    team: "安全组",
    metrics: {
      workload: 230,
      avgResponse: "4.2分钟",
      satisfaction: "97.1%",
      completion: "99.1%"
    }
  }
]

export function OperationAnalysis() {
  return (
    <Card className="border-[#E5E9EF]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>运维分析</CardTitle>
            <p className="text-sm text-gray-500 mt-1">多维度运维数据分析</p>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="distribution">
          <TabsList className="mb-4">
            <TabsTrigger value="distribution">分布分析</TabsTrigger>
            <TabsTrigger value="performance">效能分析</TabsTrigger>
            <TabsTrigger value="trends">趋势分析</TabsTrigger>
          </TabsList>

          <TabsContent value="distribution">
            <div className="grid grid-cols-2 gap-6">
              {/* 部门分布 */}
              <div>
                <h3 className="text-sm font-medium mb-4 flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-gray-400" />
                  部门工单分布
                </h3>
                <div className="space-y-3">
                  {departmentData.map((dept, index) => (
                    <div key={index} className="flex items-center gap-4">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-600">{dept.name}</span>
                          <span className="text-sm font-medium">{dept.count}条</span>
                        </div>
                        <div className="w-full h-2 bg-gray-100 rounded-full">
                          <div 
                            className="h-full bg-blue-500 rounded-full"
                            style={{ width: `${dept.percent}%` }}
                          />
                        </div>
                      </div>
                      <div className="w-16 text-right">
                        <span className="text-sm text-gray-500">{dept.percent}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* 类型分布 */}
              <div>
                <h3 className="text-sm font-medium mb-4 flex items-center gap-2">
                  <PieChart className="h-4 w-4 text-gray-400" />
                  运维类型分布
                </h3>
                <div className="space-y-3">
                  {typeDistribution.map((type, index) => (
                    <div key={index} className="flex items-center gap-4">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm text-gray-600">{type.type}</span>
                          <span className="text-sm font-medium">{type.count}条</span>
                        </div>
                        <div className="w-full h-2 bg-gray-100 rounded-full">
                          <div 
                            className="h-full bg-green-500 rounded-full"
                            style={{ width: `${type.percent}%` }}
                          />
                        </div>
                      </div>
                      <div className="w-16 text-right">
                        <span className="text-sm text-gray-500">{type.percent}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="performance">
            <div className="space-y-6">
              {teamPerformance.map((team, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-500" />
                      <h3 className="font-medium">{team.team}</h3>
                    </div>
                    <Badge variant="outline" className="bg-blue-50 text-blue-600">
                      工作量: {team.metrics.workload}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="p-3 bg-white rounded-lg">
                      <div className="text-sm text-gray-500">平均响应</div>
                      <div className="text-lg font-semibold mt-1">{team.metrics.avgResponse}</div>
                    </div>
                    <div className="p-3 bg-white rounded-lg">
                      <div className="text-sm text-gray-500">满意度</div>
                      <div className="text-lg font-semibold mt-1">{team.metrics.satisfaction}</div>
                    </div>
                    <div className="p-3 bg-white rounded-lg">
                      <div className="text-sm text-gray-500">完成率</div>
                      <div className="text-lg font-semibold mt-1">{team.metrics.completion}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="trends">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Badge variant="outline" className="bg-blue-50 text-blue-600">
                    <Activity className="h-3 w-3 mr-1" />
                    工单量: 趋势上升
                  </Badge>
                  <Badge variant="outline" className="bg-green-50 text-green-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    效率: 持续提升
                  </Badge>
                  <Badge variant="outline" className="bg-purple-50 text-purple-600">
                    <Target className="h-3 w-3 mr-1" />
                    达标率: 98.5%
                  </Badge>
                </div>
              </div>
              
              <div className="h-[300px] bg-gray-50 rounded-lg flex items-center justify-center text-gray-400">
                趋势图表区域
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
} 