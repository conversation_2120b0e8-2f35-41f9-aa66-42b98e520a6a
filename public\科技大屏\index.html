<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域监测总览 - 科技创新监测平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }
        .hover-lift {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
        }
        .tab-active {
            background: linear-gradient(135deg, #1664FF, #0F52CC);
            color: white;
        }
        .metric-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid #E5E9EF;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white border-b border-gray-200 card-shadow sticky top-0 z-50">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h1 class="text-2xl font-bold text-gray-900 flex items-center">
                        <i class="fas fa-chart-area text-blue-500 mr-3"></i>
                        区域监测总览
                    </h1>
                    <div class="flex items-center space-x-2 text-sm text-gray-500">
                        <i class="fas fa-calendar-alt"></i>
                        <span>最后更新：2024年12月</span>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报表
                    </button>
                    <button class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-cog mr-2"></i>
                        设置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="gradient-bg min-h-screen p-6">
        <div class="max-w-7xl mx-auto space-y-6">
            
            <!-- 顶部控制区域 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- 区域类型选择 -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">区域类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="country">全国</option>
                            <option value="province">全省</option>
                            <option value="ningbo" selected>宁波市</option>
                            <option value="yongjiang">甬江科创区</option>
                            <option value="district">区（县、市）</option>
                            <option value="hitech">高新区</option>
                        </select>
                    </div>

                    <!-- 时间周期 -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">统计周期</label>
                        <div class="flex bg-gray-100 rounded-lg p-1">
                            <button class="flex-1 px-3 py-2 text-sm rounded-md bg-white text-gray-900 shadow-sm">月度</button>
                            <button class="flex-1 px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900">季度</button>
                            <button class="flex-1 px-3 py-2 text-sm rounded-md text-gray-600 hover:text-gray-900">年度</button>
                        </div>
                    </div>

                    <!-- 指标类型 -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">指标类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="rd" selected>研发投入</option>
                            <option value="industry">产业结构</option>
                            <option value="innovation">创新能力</option>
                            <option value="talent">人才集聚</option>
                        </select>
                    </div>

                    <!-- 时间范围 -->
                    <div class="space-y-2">
                        <label class="text-sm font-medium text-gray-700">时间范围</label>
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="px-3 py-2 bg-blue-50 text-blue-700 rounded-lg border border-blue-200">
                                2025年1~6月
                            </span>
                            <button class="p-2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-calendar-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 指标配置提示 -->
                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        <span class="text-sm text-yellow-800">
                            <strong>配置提示：</strong>列出最新月度小册子的指标，可从指标库中自由配置
                        </span>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                
                <!-- 左侧地图区域 -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-xl card-shadow p-6 h-96">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">宁波市区域分布</h3>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">热力图</button>
                                <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">行政区</button>
                            </div>
                        </div>
                        <div class="h-80 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center relative overflow-hidden">
                            <!-- 地图容器 -->
                            <img src="https://images.unsplash.com/photo-1551434678-e076c223a692?w=800&h=600&fit=crop&crop=entropy" 
                                 alt="宁波市地图" 
                                 class="w-full h-full object-cover rounded-lg opacity-80">
                            <div class="absolute inset-0 bg-blue-500 bg-opacity-10"></div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white">
                                    <i class="fas fa-map-marked-alt text-4xl mb-2 text-blue-600"></i>
                                    <p class="text-lg font-semibold text-blue-800">宁波市行政区划图</p>
                                    <p class="text-sm text-blue-600 mt-1">支持点击区域下钻查看详情</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧指标情况 -->
                <div class="space-y-6">
                    <div class="bg-white rounded-xl card-shadow p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
                            指标情况
                        </h3>
                        <div class="space-y-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <p class="text-sm text-gray-600 mb-1">当前指标</p>
                                <p class="text-xl font-bold text-blue-600">研发费用</p>
                            </div>
                            
                            <div class="grid grid-cols-1 gap-3">
                                <div class="metric-card p-4 rounded-lg">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">绝对值</span>
                                        <span class="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">第2位</span>
                                    </div>
                                    <p class="text-2xl font-bold text-gray-900 mt-1">158.6亿元</p>
                                </div>
                                
                                <div class="metric-card p-4 rounded-lg">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">占营业收入比重</span>
                                        <span class="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">第3位</span>
                                    </div>
                                    <p class="text-2xl font-bold text-gray-900 mt-1">2.84%</p>
                                </div>
                                
                                <div class="metric-card p-4 rounded-lg">
                                    <div class="flex justify-between items-center">
                                        <span class="text-sm text-gray-600">增速</span>
                                        <span class="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">第8位</span>
                                    </div>
                                    <p class="text-2xl font-bold text-gray-900 mt-1">+12.3%</p>
                                </div>
                            </div>

                            <div class="pt-3 border-t border-gray-200">
                                <button class="w-full py-2 px-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all">
                                    <i class="fas fa-chart-bar mr-2"></i>
                                    查看详细分析
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 四象限重点监测板块 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                
                <!-- 重点企业 -->
                <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-building text-green-500 mr-2"></i>
                            重点企业
                        </h3>
                        <button onclick="window.open('enterprise-monitoring.html', '_blank')" 
                                class="px-4 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            下钻到企业监测页面
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-700">"510"领域布局情况</span>
                            <span class="text-sm font-semibold text-green-600">86.2%</span>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <p class="text-lg font-bold text-blue-600">128</p>
                                <p class="text-xs text-gray-600">研发费用500强</p>
                            </div>
                            <div class="text-center p-3 bg-purple-50 rounded-lg">
                                <p class="text-lg font-bold text-purple-600">86</p>
                                <p class="text-xs text-gray-600">"研值"前100</p>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg">
                                <option>企业储备库类型</option>
                                <option>高新技术企业</option>
                                <option>科技型中小企业</option>
                                <option>瞪羚之星企业</option>
                                <option>科技领军企业</option>
                            </select>
                            <div class="flex justify-between text-xs text-gray-600">
                                <span>天使/创投已投企业：64家</span>
                                <span class="text-red-600">异常企业：3家</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 重点平台 -->
                <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-layer-group text-blue-500 mr-2"></i>
                            重点平台
                        </h3>
                        <button onclick="window.open('platform-monitoring.html', '_blank')" 
                                class="px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            下钻到平台监测页面
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-700">"510"领域平台布局</span>
                            <span class="text-sm font-semibold text-blue-600">92.4%</span>
                        </div>
                        <div class="space-y-2">
                            <label class="text-sm font-medium text-gray-700">重点实验室</label>
                            <select class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg">
                                <option>全部级别</option>
                                <option>国家级</option>
                                <option>省级</option>
                                <option>市级</option>
                            </select>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <p class="text-lg font-bold text-green-600">42</p>
                                <p class="text-xs text-gray-600">有在研项目平台</p>
                            </div>
                            <div class="text-center p-3 bg-red-50 rounded-lg">
                                <p class="text-lg font-bold text-red-600">2</p>
                                <p class="text-xs text-gray-600">异常平台</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 重点人才 -->
                <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-users text-purple-500 mr-2"></i>
                            重点人才
                        </h3>
                        <button onclick="window.open('talent-monitoring.html', '_blank')" 
                                class="px-4 py-2 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            下钻到人才监测页面
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-700">"510"领域人才团队</span>
                            <span class="text-sm font-semibold text-purple-600">156个</span>
                        </div>
                        <div class="grid grid-cols-2 gap-3">
                            <div class="text-center p-3 bg-orange-50 rounded-lg">
                                <p class="text-lg font-bold text-orange-600">89</p>
                                <p class="text-xs text-gray-600">科技副总</p>
                            </div>
                            <div class="text-center p-3 bg-teal-50 rounded-lg">
                                <p class="text-lg font-bold text-teal-600">234</p>
                                <p class="text-xs text-gray-600">产业服务教授</p>
                            </div>
                        </div>
                        <div class="space-y-2">
                            <div class="text-sm text-gray-700">科技计划项目活跃人才</div>
                            <div class="flex justify-between text-xs">
                                <span class="text-green-600">承担项目多：125人</span>
                                <span class="text-blue-600">成果显著：78人</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 重大项目 -->
                <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                            <i class="fas fa-project-diagram text-orange-500 mr-2"></i>
                            重大项目
                        </h3>
                        <button onclick="window.open('project-monitoring.html', '_blank')" 
                                class="px-4 py-2 bg-orange-50 text-orange-600 rounded-lg hover:bg-orange-100 transition-colors text-sm">
                            <i class="fas fa-external-link-alt mr-1"></i>
                            下钻到项目监测页面
                        </button>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <span class="text-sm text-gray-700">各类项目"510"领域分布</span>
                            <span class="text-sm font-semibold text-orange-600">78.6%</span>
                        </div>
                        <div class="space-y-2">
                            <div class="text-sm font-medium text-gray-700">高新投资项目 TOP10</div>
                            <div class="space-y-1">
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-600">智能制造项目</span>
                                    <span class="font-semibold">3.2亿</span>
                                </div>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-600">新材料研发</span>
                                    <span class="font-semibold">2.8亿</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between text-xs">
                            <span class="text-yellow-600">项目延期：12个</span>
                            <span class="text-red-600">验收预警：8个</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部统计信息 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 text-center">
                    <div class="space-y-2">
                        <p class="text-2xl font-bold text-blue-600">1,286</p>
                        <p class="text-sm text-gray-600">监测企业总数</p>
                    </div>
                    <div class="space-y-2">
                        <p class="text-2xl font-bold text-green-600">198</p>
                        <p class="text-sm text-gray-600">重点平台数量</p>
                    </div>
                    <div class="space-y-2">
                        <p class="text-2xl font-bold text-purple-600">2,456</p>
                        <p class="text-sm text-gray-600">人才总数</p>
                    </div>
                    <div class="space-y-2">
                        <p class="text-2xl font-bold text-orange-600">486</p>
                        <p class="text-sm text-gray-600">在研项目数</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技创新监测平台. 保留所有权利.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>数据更新频率：实时</span>
                    <span>|</span>
                    <span>系统版本：v2.1.0</span>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // Tab切换功能
            const tabButtons = document.querySelectorAll('button[data-tab]');
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有active类
                    tabButtons.forEach(btn => btn.classList.remove('tab-active'));
                    // 添加active类到当前按钮
                    this.classList.add('tab-active');
                });
            });

            // 区域选择变化时的模拟数据更新
            const regionSelect = document.querySelector('select');
            regionSelect.addEventListener('change', function() {
                console.log('区域切换至:', this.value);
                // 这里可以添加数据更新逻辑
            });
        });
    </script>
</body>
</html> 