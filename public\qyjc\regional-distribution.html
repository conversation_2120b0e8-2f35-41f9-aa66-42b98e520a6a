<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域分布 - 企业检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .hover-lift {
            transition: transform 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
        }
        .chart-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center space-x-4">
                    <button onclick="history.back()" class="text-blue-600 hover:text-blue-800 transition-colors">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h1 class="text-xl font-bold text-gray-900">区域分布模块</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>导出报告
                    </button>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>保存配置
                    </button>
                </div>
            </div>
        </div>
    </header>

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 gradient-bg min-h-screen">
        <!-- 筛选控制区 -->
        <div class="bg-white rounded-xl card-shadow p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-filter text-blue-600 mr-2"></i>筛选条件
                </h2>
                <div class="flex space-x-3">
                    <button class="text-blue-600 hover:text-blue-800 font-medium">
                        <i class="fas fa-undo mr-1"></i>重置
                    </button>
                    <button class="bg-yellow-500 text-white px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors">
                        <i class="fas fa-bookmark mr-2"></i>保存为模板
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">分析维度</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="industry">361产业分布</option>
                        <option value="technology">510技术领域</option>
                        <option value="both">综合分析</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">区域范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">全部区域</option>
                        <option value="downtown">市区</option>
                        <option value="county">各区县</option>
                        <option value="park">产业园区</option>
                        <option value="hitech">高新区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">企业类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="all">全部企业</option>
                        <option value="hitech">高新技术企业</option>
                        <option value="sme">科技型中小企业</option>
                        <option value="gazelle">瞪羚企业</option>
                        <option value="leader">科技领军企业</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">统计周期</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="current">当前年度</option>
                        <option value="quarterly">季度对比</option>
                        <option value="yearly">年度趋势</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 分布概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">覆盖区域总数</p>
                        <p class="text-2xl font-bold text-blue-600">128</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up"></i> +5.2%
                        </p>
                    </div>
                    <div class="bg-blue-100 rounded-full p-3">
                        <i class="fas fa-map-marked-alt text-blue-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">产业领域数量</p>
                        <p class="text-2xl font-bold text-green-600">361</p>
                        <p class="text-xs text-blue-600 mt-1">
                            <i class="fas fa-industry"></i> 全覆盖
                        </p>
                    </div>
                    <div class="bg-green-100 rounded-full p-3">
                        <i class="fas fa-industry text-green-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">技术领域数量</p>
                        <p class="text-2xl font-bold text-purple-600">510</p>
                        <p class="text-xs text-purple-600 mt-1">
                            <i class="fas fa-flask"></i> 全覆盖
                        </p>
                    </div>
                    <div class="bg-purple-100 rounded-full p-3">
                        <i class="fas fa-flask text-purple-600"></i>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl card-shadow p-6 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">集中度指数</p>
                        <p class="text-2xl font-bold text-orange-600">0.72</p>
                        <p class="text-xs text-orange-600 mt-1">
                            <i class="fas fa-chart-line"></i> 较高集中
                        </p>
                    </div>
                    <div class="bg-orange-100 rounded-full p-3">
                        <i class="fas fa-chart-line text-orange-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 361产业分布 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-industry text-blue-600 mr-2"></i>361产业分布图表
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-600 hover:text-blue-600 p-2 rounded-lg hover:bg-blue-50">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="text-gray-600 hover:text-green-600 p-2 rounded-lg hover:bg-green-50">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>

                <!-- 产业分布地图模拟 -->
                <div class="chart-container rounded-lg p-4 mb-4">
                    <div class="h-64 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg flex items-center justify-center border border-blue-200">
                        <div class="text-center">
                            <i class="fas fa-map text-4xl text-blue-600 mb-3"></i>
                            <p class="text-blue-700 font-medium">361产业空间分布热力图</p>
                            <p class="text-sm text-blue-600 mt-2">点击区域查看详细分布</p>
                        </div>
                    </div>
                </div>

                <!-- 产业Top10统计 -->
                <div class="space-y-3">
                    <h4 class="font-medium text-gray-800 mb-3">产业集中度排名 Top 10</h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center mr-3">1</span>
                                <span class="font-medium">电子信息制造业</span>
                            </div>
                            <div class="text-right">
                                <span class="text-blue-600 font-bold">2,458</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-green-600 text-white text-xs rounded-full flex items-center justify-center mr-3">2</span>
                                <span class="font-medium">先进制造与自动化</span>
                            </div>
                            <div class="text-right">
                                <span class="text-green-600 font-bold">1,892</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-yellow-600 text-white text-xs rounded-full flex items-center justify-center mr-3">3</span>
                                <span class="font-medium">新材料技术</span>
                            </div>
                            <div class="text-right">
                                <span class="text-yellow-600 font-bold">1,563</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button class="text-blue-600 hover:text-blue-800 font-medium">
                                查看完整排名 <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 510技术领域分布 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-flask text-purple-600 mr-2"></i>510技术领域分布图表
                    </h3>
                    <div class="flex space-x-2">
                        <button class="text-gray-600 hover:text-blue-600 p-2 rounded-lg hover:bg-blue-50">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="text-gray-600 hover:text-green-600 p-2 rounded-lg hover:bg-green-50">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>

                <!-- 技术领域分布图模拟 -->
                <div class="chart-container rounded-lg p-4 mb-4">
                    <div class="h-64 bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg flex items-center justify-center border border-purple-200">
                        <div class="text-center">
                            <i class="fas fa-microscope text-4xl text-purple-600 mb-3"></i>
                            <p class="text-purple-700 font-medium">510技术领域聚集度分析</p>
                            <p class="text-sm text-purple-600 mt-2">点击查看技术详情</p>
                        </div>
                    </div>
                </div>

                <!-- 技术Top10统计 -->
                <div class="space-y-3">
                    <h4 class="font-medium text-gray-800 mb-3">技术活跃度排名 Top 10</h4>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-purple-600 text-white text-xs rounded-full flex items-center justify-center mr-3">1</span>
                                <span class="font-medium">人工智能技术</span>
                            </div>
                            <div class="text-right">
                                <span class="text-purple-600 font-bold">1,256</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-indigo-600 text-white text-xs rounded-full flex items-center justify-center mr-3">2</span>
                                <span class="font-medium">生物医药技术</span>
                            </div>
                            <div class="text-right">
                                <span class="text-indigo-600 font-bold">987</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <span class="w-6 h-6 bg-pink-600 text-white text-xs rounded-full flex items-center justify-center mr-3">3</span>
                                <span class="font-medium">新能源技术</span>
                            </div>
                            <div class="text-right">
                                <span class="text-pink-600 font-bold">823</span>
                                <span class="text-sm text-gray-600 ml-1">家企业</span>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button class="text-purple-600 hover:text-purple-800 font-medium">
                                查看完整排名 <i class="fas fa-chevron-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 综合分析区域 -->
        <div class="mt-8 bg-white rounded-xl card-shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">
                <i class="fas fa-chart-bar text-blue-600 mr-2"></i>区域分布综合分析
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 集中度分析 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-bullseye text-orange-600 mr-2"></i>产业集中度分析
                    </h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">高度集中区域</span>
                            <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">15个</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">中度集中区域</span>
                            <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">42个</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">分散分布区域</span>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">71个</span>
                        </div>
                        <div class="mt-4 pt-3 border-t border-gray-200">
                            <button class="text-orange-600 hover:text-orange-800 font-medium text-sm">
                                详细分析报告 <i class="fas fa-external-link-alt ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 发展趋势 -->
                <div class="border border-gray-200 rounded-lg p-4">
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-trending-up text-green-600 mr-2"></i>发展趋势分析
                    </h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">快速增长领域</span>
                            <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">86个</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">稳定发展领域</span>
                            <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">164个</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">待突破领域</span>
                            <span class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">121个</span>
                        </div>
                        <div class="mt-4 pt-3 border-t border-gray-200">
                            <button class="text-green-600 hover:text-green-800 font-medium text-sm">
                                趋势预测分析 <i class="fas fa-chart-line ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- AI智能建议 -->
                <div class="border border-blue-200 rounded-lg p-4 bg-blue-50">
                    <h4 class="font-medium text-gray-800 mb-3">
                        <i class="fas fa-robot text-blue-600 mr-2"></i>AI分析建议
                    </h4>
                    <div class="space-y-3 text-sm text-gray-700">
                        <div class="flex items-start">
                            <i class="fas fa-lightbulb text-yellow-500 mr-2 mt-1"></i>
                            <span>建议重点关注人工智能和生物医药产业在甬江科创区的布局优化</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-orange-500 mr-2 mt-1"></i>
                            <span>新材料技术存在区域分布不均，建议加强薄弱区域扶持</span>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-arrow-up text-green-500 mr-2 mt-1"></i>
                            <span>海曙区在电子信息领域具备产业集聚优势，可进一步强化</span>
                        </div>
                        <div class="mt-4 pt-3 border-t border-blue-200">
                            <button class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                查看完整建议 <i class="fas fa-arrow-right ml-1"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 导出操作区域 -->
        <div class="mt-8 bg-gray-50 rounded-xl p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="font-medium text-gray-800">数据导出与分享</h4>
                    <p class="text-sm text-gray-600 mt-1">支持多种格式的数据导出和报告生成</p>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>导出Excel
                    </button>
                    <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-file-pdf mr-2"></i>生成PDF报告
                    </button>
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-share-alt mr-2"></i>分享链接
                    </button>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 筛选条件变化时的处理
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    console.log('筛选条件变化:', this.value);
                    // 这里可以添加实际的数据更新逻辑
                });
            });

            // 悬停效果
            const hoverElements = document.querySelectorAll('.hover-lift');
            hoverElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });
                element.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 