unit CC_ScjhWinFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, RzPanel,
  CC_Ccgk_ScYjzqAllFrm;

type
  TCC_ScjhWinForm = class(TForm)
    RzPanel1: TRzPanel;
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    FCC_Ccgk_ScYjzqAllFrame: TCC_Ccgk_ScYjzqAllFrame;
  end;

var
  CC_ScjhWinForm: TCC_ScjhWinForm;

implementation

{$R *.dfm}

procedure TCC_ScjhWinForm.FormShow(Sender: TObject);
begin
  if (FCC_Ccgk_ScYjzqAllFrame <> nil) then
  begin
    FCC_Ccgk_ScYjzqAllFrame.Free;
    FCC_Ccgk_ScYjzqAllFrame := nil;
  end;

  if (FCC_Ccgk_ScYjzqAllFrame = nil) then
  begin
    FCC_Ccgk_ScYjzqAllFrame := TCC_Ccgk_ScYjzqAllFrame.Create(self);
    FCC_Ccgk_ScYjzqAllFrame.Parent := self.RzPanel1;
    FCC_Ccgk_ScYjzqAllFrame.Align := alClient;
    FCC_Ccgk_ScYjzqAllFrame.Init();
  end;
end;

end.
