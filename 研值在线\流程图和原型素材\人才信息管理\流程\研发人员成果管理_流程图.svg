<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员成果管理业务流程</text>

  <!-- 阶段一：数据归集与标准化 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据归集与标准化</text>
  
  <!-- 节点1: 数据归集 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多渠道数据归集</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">定期同步、标准化处理</text>
  </g>

  <!-- 节点2: 关联建立 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">精准关联建立</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人员、单位、项目关联</text>
  </g>

  <!-- 节点3: 全链路打通 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">全链路数据打通</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">成果、项目、奖励联动</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与筛选 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与筛选</text>

  <!-- 节点4: 页面进入 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">成果管理界面</text>
  </g>

  <!-- 节点5: 条件筛选 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设置筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">类型、人员、年度等</text>
  </g>

  <!-- 节点6: 列表渲染 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时检索渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">成果信息列表</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 400 345 Q 430 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 345 Q 730 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：成果操作与管理 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：成果操作与管理</text>

  <!-- 节点7: 详情钻取 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情钻取</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">查看关键信息</text>
  </g>

  <!-- 节点8: 新增编辑 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增编辑</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">弹窗表单录入</text>
  </g>

  <!-- 节点9: 删除操作 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">确认提示、原因填写</text>
  </g>

  <!-- 节点10: 批量导出 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导出</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">多选记录导出</text>
  </g>

  <!-- 阶段四：数据管理与监控 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据管理与监控</text>
  
  <!-- 节点11: 系统校验 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动校验归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数据校验、即时刷新</text>
  </g>

  <!-- 节点12: 操作日志 -->
  <g transform="translate(600, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全程监控、可追溯性</text>
  </g>

  <!-- 从阶段二到阶段三的连接线 -->
  <path d="M 300 380 C 250 420, 200 450, 190 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 380 C 500 420, 450 450, 440 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 380 C 700 420, 690 450, 690 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 380 C 950 420, 950 450, 940 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从阶段三到阶段四的连接线 -->
  <path d="M 440 560 C 420 600, 410 640, 400 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 690 560 C 710 600, 700 640, 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从阶段一到阶段二的连接线 -->
  <path d="M 560 200 C 560 230, 300 250, 300 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从操作日志回到数据归集 -->
  <path d="M 600 670 C 100 650, 50 400, 50 200 C 50 150, 100 130, 150 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="400" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 50, 400)">监控反馈</text>

  <!-- 反馈循环：从数据导出回到条件筛选 -->
  <path d="M 940 490 C 1100 400, 1100 350, 1000 345 C 950 345, 750 345, 700 345" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="370" text-anchor="middle" font-size="11" fill="#666">数据应用反馈</text>

</svg>