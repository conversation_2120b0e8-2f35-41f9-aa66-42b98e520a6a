<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申报管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/@tinymce/tinymce-webcomponent@2/dist/tinymce-webcomponent.min.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="py-6">
                <h1 class="text-3xl font-bold text-gray-900">申报管理</h1>
                <p class="mt-2 text-sm text-gray-600">科技特派员选派通知发布与申报管理</p>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 操作栏 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex space-x-4">
                    <button id="addNoticeBtn" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        <span>新增通知</span>
                    </button>
                    <button class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>批量导出</span>
                    </button>
                </div>
                
                <!-- 查询筛选区 -->
                <div class="flex space-x-3">
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option>全部年度</option>
                        <option>2024年</option>
                        <option>2023年</option>
                        <option>2022年</option>
                    </select>
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option>全部类型</option>
                        <option>个人特派员</option>
                        <option>团队特派员</option>
                    </select>
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option>全部状态</option>
                        <option>已发布</option>
                        <option>未发布</option>
                        <option>已撤销</option>
                    </select>
                    <div class="relative">
                        <input type="text" placeholder="搜索通知标题..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <svg class="absolute left-3 top-2.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 申报通知列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="px-6 py-4 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">申报通知列表</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选派年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">对象类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">通知标题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申报时间段</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">是否启用</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2024年</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">个人特派员</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">2024年度农业技术科技特派员选派通知</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-03-01 至 2024-03-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-02-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                <button class="text-green-600 hover:text-green-900">查看详情</button>
                                <button class="text-red-600 hover:text-red-900">撤销</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2024年</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">团队特派员</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">2024年度水产养殖团队科技特派员选派通知</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-04-01 至 2024-04-30</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">草稿</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">启用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024-03-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                <button class="text-green-600 hover:text-green-900">查看详情</button>
                                <button class="text-blue-600 hover:text-blue-900">发布</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2023年</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">个人特派员</span>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-900">2023年度林业技术科技特派员选派通知</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-05-01 至 2023-05-31</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">已撤销</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">禁用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023-04-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button class="text-blue-600 hover:text-blue-900">编辑</button>
                                <button class="text-green-600 hover:text-green-900">查看详情</button>
                                <button class="text-blue-600 hover:text-blue-900">重新发布</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</a>
                    <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</a>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">25</span> 条记录</p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">上一页</a>
                            <a href="#" class="bg-blue-50 border-blue-500 text-blue-600 relative inline-flex items-center px-4 py-2 border text-sm font-medium">1</a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">2</a>
                            <a href="#" class="bg-white border-gray-300 text-gray-500 hover:bg-gray-50 relative inline-flex items-center px-4 py-2 border text-sm font-medium">3</a>
                            <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">下一页</a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 申报通知配置模态框 -->
    <div id="noticeModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <!-- 模态框标题 -->
                <div class="flex items-center justify-between pb-4 border-b">
                    <h3 class="text-lg font-semibold text-gray-900">新增申报通知</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-gray-600">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                
                <!-- 选项卡 -->
                <div class="mt-6">
                    <div class="border-b border-gray-200">
                        <nav class="-mb-px flex space-x-8">
                            <button class="tab-btn active border-blue-500 text-blue-600 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-tab="basic">
                                基本信息
                            </button>
                            <button class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-tab="form">
                                申报表单配置
                            </button>
                            <button class="tab-btn border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm" data-tab="content">
                                详细说明
                            </button>
                        </nav>
                    </div>
                </div>
                
                <!-- 基本信息选项卡内容 -->
                <div id="basic-tab" class="tab-content mt-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">选派年度 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option>请选择年度</option>
                                <option>2024年</option>
                                <option>2023年</option>
                                <option>2022年</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">对象类型 <span class="text-red-500">*</span></label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                <option>请选择类型</option>
                                <option>个人特派员</option>
                                <option>团队特派员</option>
                            </select>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">通知标题 <span class="text-red-500">*</span></label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="请输入通知标题">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">申报开始时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">申报结束时间 <span class="text-red-500">*</span></label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">选派条件</label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="请输入选派条件和要求"></textarea>
                        </div>
                        
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">对接需求</label>
                            <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500" placeholder="请输入对接需求说明"></textarea>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label class="ml-2 block text-sm text-gray-900">立即启用</label>
                        </div>
                        
                        <div class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            <label class="ml-2 block text-sm text-gray-900">自动发布</label>
                        </div>
                    </div>
                </div>
                
                <!-- 申报表单配置选项卡内容 -->
                <div id="form-tab" class="tab-content mt-6 hidden">
                    <div class="space-y-6">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">项目基本信息字段</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">项目名称</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">统一社会信用代码</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">归口管理部门</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">申报单位</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">所属区域</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">申请人信息字段</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">姓名</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">身份证号</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">联系方式</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">职务职称</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">选填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">工作单位</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">专业领域</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-medium text-gray-900 mb-4">服务单位信息字段</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">单位名称</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">联系人</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">单位地址</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">服务内容</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" checked class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">必填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">服务期限</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">选填</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-gray-700">预期成效</span>
                                    <div class="flex items-center space-x-2">
                                        <input type="checkbox" class="h-4 w-4 text-blue-600">
                                        <span class="text-xs text-gray-500">选填</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细说明选项卡内容 -->
                <div id="content-tab" class="tab-content mt-6 hidden">
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">科技特派员任务说明</label>
                            <div class="border border-gray-300 rounded-lg">
                                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300 flex items-center space-x-2">
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2l3 6 5.5-3-3.5 6.5 6.5 2.5-7 4-3-6-5.5 3 3.5-6.5L0 4.5l7-4 3 6z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <textarea rows="8" class="w-full px-3 py-2 border-0 focus:ring-0 resize-none" placeholder="请输入科技特派员任务说明，支持富文本格式..."></textarea>
                                <div class="px-3 py-2 bg-gray-50 border-t border-gray-300 text-right">
                                    <span class="text-xs text-gray-500">0/1000 字</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">预期成效描述</label>
                            <div class="border border-gray-300 rounded-lg">
                                <div class="bg-gray-50 px-3 py-2 border-b border-gray-300 flex items-center space-x-2">
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 2l3 6 5.5-3-3.5 6.5 6.5 2.5-7 4-3-6-5.5 3 3.5-6.5L0 4.5l7-4 3 6z"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                                        </svg>
                                    </button>
                                    <button class="p-1 text-gray-600 hover:text-gray-800">
                                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </button>
                                </div>
                                <textarea rows="6" class="w-full px-3 py-2 border-0 focus:ring-0 resize-none" placeholder="请输入预期成效描述..."></textarea>
                                <div class="px-3 py-2 bg-gray-50 border-t border-gray-300 text-right">
                                    <span class="text-xs text-gray-500">0/1000 字</span>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">附件上传</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="mt-4">
                                    <label class="cursor-pointer">
                                        <span class="mt-2 block text-sm font-medium text-gray-900">点击上传文件</span>
                                        <input type="file" class="sr-only" multiple>
                                    </label>
                                    <p class="mt-2 text-xs text-gray-500">支持 PDF、DOC、DOCX 格式，单个文件不超过 10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 模态框底部按钮 -->
                <div class="mt-8 flex items-center justify-end space-x-3 pt-6 border-t">
                    <button id="cancelBtn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                        保存草稿
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        保存并发布
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模态框控制
        const modal = document.getElementById('noticeModal');
        const addBtn = document.getElementById('addNoticeBtn');
        const closeBtn = document.getElementById('closeModal');
        const cancelBtn = document.getElementById('cancelBtn');
        
        addBtn.addEventListener('click', () => {
            modal.classList.remove('hidden');
        });
        
        closeBtn.addEventListener('click', () => {
            modal.classList.add('hidden');
        });
        
        cancelBtn.addEventListener('click', () => {
            modal.classList.add('hidden');
        });
        
        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
        
        // 选项卡切换
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const targetTab = btn.getAttribute('data-tab');
                
                // 移除所有活动状态
                tabBtns.forEach(b => {
                    b.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    b.classList.add('border-transparent', 'text-gray-500');
                });
                
                // 添加当前按钮活动状态
                btn.classList.add('active', 'border-blue-500', 'text-blue-600');
                btn.classList.remove('border-transparent', 'text-gray-500');
                
                // 隐藏所有选项卡内容
                tabContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // 显示目标选项卡内容
                document.getElementById(targetTab + '-tab').classList.remove('hidden');
            });
        });
        
        // 富文本编辑器字数统计
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            const parent = textarea.closest('.border');
            if (parent) {
                const counter = parent.querySelector('.text-xs.text-gray-500');
                if (counter && counter.textContent.includes('/1000')) {
                    textarea.addEventListener('input', () => {
                        const length = textarea.value.length;
                        counter.textContent = `${length}/1000 字`;
                        
                        if (length > 1000) {
                            counter.classList.add('text-red-500');
                            counter.classList.remove('text-gray-500');
                        } else {
                            counter.classList.add('text-gray-500');
                            counter.classList.remove('text-red-500');
                        }
                    });
                }
            }
        });
        
        // 表格行操作事件
        document.querySelectorAll('tbody tr').forEach(row => {
            const buttons = row.querySelectorAll('button');
            buttons.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.textContent.trim();
                    const title = row.querySelector('td:nth-child(3)').textContent;
                    
                    switch(action) {
                        case '编辑':
                            modal.classList.remove('hidden');
                            document.querySelector('h3').textContent = '编辑申报通知';
                            break;
                        case '查看详情':
                            alert(`查看通知详情: ${title}`);
                            break;
                        case '发布':
                        case '重新发布':
                            if(confirm(`确认发布通知: ${title}?`)) {
                                alert('发布成功!');
                            }
                            break;
                        case '撤销':
                            if(confirm(`确认撤销通知: ${title}?`)) {
                                alert('撤销成功!');
                            }
                            break;
                        case '删除':
                            if(confirm(`确认删除通知: ${title}?`)) {
                                alert('删除成功!');
                            }
                            break;
                    }
                });
            });
        });
        
        // 文件上传处理
        const fileInput = document.querySelector('input[type="file"]');
        const uploadArea = fileInput.closest('.border-dashed');
        
        fileInput.addEventListener('change', (e) => {
            const files = e.target.files;
            if (files.length > 0) {
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                uploadArea.querySelector('.text-sm').textContent = `已选择文件: ${fileNames}`;
            }
        });
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('border-blue-500', 'bg-blue-50');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                const fileNames = Array.from(files).map(file => file.name).join(', ');
                uploadArea.querySelector('.text-sm').textContent = `已选择文件: ${fileNames}`;
            }
        });
    </script>
</body>
</html>