<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校重点学科展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">高等院校重点学科展示</h1>
        
        <!-- 学科概况区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-blue-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">重点学科总数</p>
                        <p class="text-2xl font-semibold text-gray-900">48</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-red-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">国家级重点学科</p>
                        <p class="text-2xl font-semibold text-gray-900">12</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-green-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">省级重点学科</p>
                        <p class="text-2xl font-semibold text-gray-900">24</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0 bg-purple-100 p-3 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">近三年新增</p>
                        <p class="text-2xl font-semibold text-gray-900">8</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 优势领域标签 -->
        <div class="mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">优势领域</h2>
            <div class="flex flex-wrap gap-2">
                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">材料科学与工程</span>
                <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">机械工程</span>
                <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">计算机科学与技术</span>
                <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">生物医学工程</span>
                <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">化学工程与技术</span>
            </div>
        </div>

        <!-- 学科分类分布区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">学科分类分布</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">全部</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm">国家级</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm">省级</button>
                </div>
            </div>
            <div class="h-80">
                <canvas id="disciplineChart"></canvas>
            </div>
        </div>

        <!-- 学科特色雷达对比区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-900">学科特色对比</h2>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>选择学科1</option>
                        <option>材料科学与工程</option>
                        <option>机械工程</option>
                        <option>计算机科学与技术</option>
                    </select>
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                        <option>选择学科2</option>
                        <option>材料科学与工程</option>
                        <option>机械工程</option>
                        <option>计算机科学与技术</option>
                    </select>
                </div>
            </div>
            <div class="h-80">
                <canvas id="radarChart"></canvas>
            </div>
        </div>

        <!-- 学科关系网络区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-900 mb-6">学科协作网络</h2>
            <div class="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
                <div class="text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <p class="mt-2 text-gray-500">学科协作网络图</p>
                </div>
            </div>
        </div>

        <!-- 学科明细列表 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl font-semibold text-gray-900">重点学科列表</h2>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            导出数据
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                            新增学科
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">学科代码</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属学院</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">立项年度</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">材料科学与工程</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0805</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">材料科学与工程学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900 mr-3">特色介绍</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">机械工程</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0802</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">机械工程学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900 mr-3">特色介绍</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">计算机科学与技术</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0812</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">计算机科学与技术学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900 mr-3">特色介绍</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">生物医学工程</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">0831</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">市级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900 mr-3">特色介绍</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-4 条，共 48 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 学科详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">材料科学与工程 - 特色介绍</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="md:col-span-2">
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">学科特色</h4>
                            <p class="text-sm text-gray-700">本学科聚焦新型功能材料、高性能结构材料及先进制备技术研究，形成了以纳米材料、复合材料、生物医用材料为特色的研究方向。学科团队在材料设计与计算模拟、材料表征与性能测试、材料制备与加工等领域具有显著优势。</p>
                        </div>
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">代表性成果</h4>
                            <ul class="list-disc pl-5 space-y-1 text-sm text-gray-700">
                                <li>发表SCI论文200余篇，其中Nature Materials、Advanced Materials等顶级期刊论文30篇</li>
                                <li>获国家技术发明二等奖1项，省部级科技奖励5项</li>
                                <li>授权发明专利80余项，其中PCT专利10项</li>
                                <li>主持国家重点研发计划项目3项，国家自然科学基金重点项目5项</li>
                            </ul>
                        </div>
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-2">科研团队</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="font-medium text-gray-900">张教授</p>
                                    <p class="text-gray-600">学科带头人，国家杰出青年科学基金获得者</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">李研究员</p>
                                    <p class="text-gray-600">青年千人计划入选者</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">王副教授</p>
                                    <p class="text-gray-600">浙江省151人才工程第一层次</p>
                                </div>
                                <div>
                                    <p class="font-medium text-gray-900">赵副教授</p>
                                    <p class="text-gray-600">浙江省高校中青年学科带头人</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="mb-6">
                            <h4 class="text-md font-semibold text-gray-900 mb-2">开放共享资源</h4>
                            <div class="space-y-3 text-sm">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <span>材料制备与加工实验室</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <span>材料表征与分析中心</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                    </svg>
                                    <span>计算材料学平台</span>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-semibold text-gray-900 mb-2">合作企业</h4>
                            <div class="space-y-2 text-sm">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span>宁波材料科技有限公司</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span>浙江高分子材料有限公司</span>
                                </div>
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span>宁波生物医用材料研究院</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 学科分类分布图表
            const disciplineCtx = document.getElementById('disciplineChart').getContext('2d');
            new Chart(disciplineCtx, {
                type: 'bar',
                data: {
                    labels: ['理学', '工学', '医学', '文学', '管理学'],
                    datasets: [
                        {
                            label: '国家级',
                            data: [2, 8, 1, 0, 1],
                            backgroundColor: 'rgba(239, 68, 68, 0.8)',
                        },
                        {
                            label: '省级',
                            data: [4, 12, 3, 2, 3],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        },
                        {
                            label: '市级',
                            data: [3, 5, 2, 1, 2],
                            backgroundColor: 'rgba(245, 158, 11, 0.8)',
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                        }
                    },
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // 学科特色雷达对比图
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['师资力量', '科研项目经费', '成果产出', '人才培养质量', '国际合作'],
                    datasets: [
                        {
                            label: '材料科学与工程',
                            data: [95, 90, 85, 80, 70],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 2
                        },
                        {
                            label: '机械工程',
                            data: [80, 85, 75, 85, 60],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal();
                }
            });
        });
    </script>
</body>
</html>