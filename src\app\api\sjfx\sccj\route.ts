import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// 生产车间分解统计（参考 Delphi FillGridSccj），返回 Top 99 的 ddks 维度和各车间分解
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const year = searchParams.get('year') || '2025'
    const kh = searchParams.get('kh') || 'J B'
    const debug = searchParams.get('debug') === '1'
    const beginTime = `${year}-01-01`
    const endTime = `${year}-12-31`
    
    // 基础 Top 99 维度（ddksddlb）
    const sql = `
      SELECT b.ddksddlb AS Sccj_Xl2,
             b.dds       AS zj_xl2,
             SUM(CASE WHEN a.norm = '1车间' THEN a.dds ELSE 0 END) AS sccj1,
             SUM(CASE WHEN a.norm = '2 水琴' THEN a.dds ELSE 0 END) AS sccj2,
             SUM(CASE WHEN a.norm = '3 亚平' THEN a.dds ELSE 0 END) AS sccj3,
             SUM(CASE WHEN a.norm = '6车间' THEN a.dds ELSE 0 END) AS sccj4,
             SUM(CASE WHEN a.norm = '7车间' THEN a.dds ELSE 0 END) AS sccj5,
             SUM(CASE WHEN a.norm = '其他' THEN a.dds ELSE 0 END)   AS sccj6
      FROM (
        SELECT SUM(xx_scdd.dds) AS dds,
               CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks) AS ddksddlb
        FROM xx_scdd
        JOIN cc_sj_scdj ON cc_sj_scdj.scid = xx_scdd.scid
        WHERE xx_scdd.kh = ? AND xx_scdd.xdrq >= ? AND xx_scdd.xdrq <= ?
        GROUP BY CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks)
        ORDER BY dds DESC
        LIMIT 99
      ) b
      LEFT JOIN (
        SELECT CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks) AS ddksddlb,
               SUM(xx_scdd.dds) AS dds,
               CASE
                 WHEN xx_scdd.sccj = '1车间' THEN '1车间'
                 WHEN xx_scdd.sccj = '2 水琴' THEN '2水琴'
                 WHEN xx_scdd.sccj = '3 亚平' THEN '3亚平'
                 WHEN xx_scdd.sccj = '6车间' THEN '6车间'
                 WHEN xx_scdd.sccj = '7车间' THEN '7车间'
                 ELSE '其他'
               END AS norm
        FROM xx_scdd
        JOIN cc_sj_scdj ON cc_sj_scdj.scid = xx_scdd.scid
        WHERE xx_scdd.kh = ? AND xx_scdd.xdrq >= ? AND xx_scdd.xdrq <= ?
        GROUP BY ddksddlb, norm
      ) a ON a.ddksddlb = b.ddksddlb
      GROUP BY b.ddksddlb, b.dds
      ORDER BY b.dds DESC
    `

    const rows = await query(sql, [kh, beginTime, endTime, kh, beginTime, endTime])

    const data = rows.map((r: any) => ({
      Sccj_Xl2: r.Sccj_Xl2 as string,
      zj_xl2: Number(r.zj_xl2) || 0,
      sccj1: Number(r.sccj1) || 0,
      sccj2: Number(r.sccj2) || 0,
      sccj3: Number(r.sccj3) || 0,
      sccj4: Number(r.sccj4) || 0,
      sccj5: Number(r.sccj5) || 0,
      sccj6: Number(r.sccj6) || 0,
    }))

    if (debug) {
      console.log('[API sccj] year=', year, 'kh=', kh, 'rows=', rows.length)
      if (rows.length > 0) {
        console.log('[API sccj] sample=', rows[0])
      }
    }
    return NextResponse.json({ success: true, data, meta: debug ? { rows: rows.length, year, kh } : undefined })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e?.message || '查询失败' }, { status: 500 })
  }
}


