'use client'

import { useState } from 'react'
import { ChevronDownIcon, ChevronRightIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface TreeNode {
  id: string
  name: string
  icon?: any
  children?: TreeNode[]
}

interface TreeViewProps {
  data: TreeNode[]
  defaultExpanded?: string[]
  onNodeSelect?: (node: TreeNode) => void
}

export function TreeView({ data, defaultExpanded = [], onNodeSelect }: TreeViewProps) {
  const [expanded, setExpanded] = useState<Set<string>>(new Set(defaultExpanded))

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expanded)
    if (expanded.has(nodeId)) {
      newExpanded.delete(nodeId)
    } else {
      newExpanded.add(nodeId)
    }
    setExpanded(newExpanded)
  }

  const renderNode = (node: TreeNode, level: number = 0) => {
    const hasChildren = node.children && node.children.length > 0
    const isExpanded = expanded.has(node.id)
    const Icon = node.icon

    return (
      <div key={node.id}>
        <div
          className={cn(
            "flex items-center py-2 px-2 rounded-md hover:bg-gray-100 cursor-pointer",
            "text-sm"
          )}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => {
            if (hasChildren) {
              toggleNode(node.id)
            }
            onNodeSelect?.(node)
          }}
        >
          {hasChildren ? (
            <div className="w-4 h-4 mr-1">
              {isExpanded ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
            </div>
          ) : (
            <div className="w-4 h-4 mr-1" />
          )}
          {Icon && <Icon className="w-4 h-4 mr-2" />}
          <span>{node.name}</span>
        </div>
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map((child) => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="select-none">
      {data.map((node) => renderNode(node))}
    </div>
  )
} 