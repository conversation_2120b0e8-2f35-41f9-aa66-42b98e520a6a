import { NextRequest, NextResponse } from 'next/server';

// Dify配置
const DIFY_API_KEY = "app-plE9pju9RGzpYzEXqT97242P";
const DIFY_BASE_URL = "http://111.229.163.150/v1";
// const DIFY_API_KEY = "app-hXBah6VG2H7FcwuHQ6iSiI4A";
// const DIFY_BASE_URL = "http://10.26.37.118:8081/v1";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('conversation_id');
  const user = searchParams.get('user') || 'user-123';
  const limit = searchParams.get('limit') || '50';
  const firstId = searchParams.get('first_id');
  
  if (!conversationId) {
    return NextResponse.json(
      { error: '缺少会话ID参数', success: false },
      { status: 400 }
    );
  }
  
  try {
    // 构建查询参数
    const params = new URLSearchParams({
      conversation_id: conversationId,
      user: user,
      limit: limit
    });
    
    if (firstId) {
      params.append('first_id', firstId);
    }
    
    const messagesUrl = `${DIFY_BASE_URL}/messages?${params.toString()}`;
    console.log('调用历史消息API:', messagesUrl);
    
    const response = await fetch(messagesUrl, {
      headers: {
        'Authorization': `Bearer ${DIFY_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`获取消息失败: ${response.status}`, errorText);
      throw new Error(`获取消息失败: ${response.status}`);
    }

    const data = await response.json();
    console.log('历史消息响应:', data);
    return NextResponse.json(data);
    
  } catch (error) {
    console.error('获取会话消息错误:', error);
    return NextResponse.json(
      { error: '获取会话消息失败', success: false },
      { status: 500 }
    );
  }
} 