'use client'

import { useEffect, useMemo, useState } from 'react'
import { Button } from '@/components/ui/button'
import { BarChart3, Calendar } from 'lucide-react'
import { useRef } from 'react'

interface YearRow { ddlb: string; ddml: string; ddks: string; value: number; sccj1?: number; sccj2?: number; sccj3?: number; sccj4?: number; sccj5?: number; sccj6?: number }
interface MonthRow { rank: number; key: string; monthValue: number; avgValue: number; delta: number }

type ViewMode = 'year' | 'month'

export default function SJFXPage() {
  const [view, setView] = useState<ViewMode>('year')
  const [year, setYear] = useState<string>('2025')
  const [month, setMonth] = useState<string>('05')
  const [loading, setLoading] = useState(false)
  const [yearData, setYearData] = useState<YearRow[]>([])
  const [monthData, setMonthData] = useState<MonthRow[]>([])
  const scrollRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        if (view === 'year') {
          const [resAll, resSccj] = await Promise.all([
            fetch(`/api/sjfx/all?year=${year}&debug=1`),
            fetch(`/api/sjfx/sccj?year=${year}&debug=1`)
          ])
          const [jsonAll, jsonSccj] = await Promise.all([resAll.json(), resSccj.json()])
          console.log('🔍 [前端] API响应 - jsonAll:', jsonAll)
          console.log('🔍 [前端] API响应 - jsonSccj:', jsonSccj)
          if (jsonAll.success) {
            let list: YearRow[] = jsonAll.data
            console.log('🔍 [前端] 解析后的list数据:', list.slice(0, 3))
            if (jsonSccj.success) {
              const map: Record<string, any> = {}
              for (const r of jsonSccj.data as any[]) {
                map[r.Sccj_Xl2] = r
              }
              list = list.map(it => {
                const key = `${it.ddlb} ${it.ddml} ${it.ddks}`
                const m = map[key]
                if (m) {
                  return {
                    ...it,
                    sccj1: m.sccj1,
                    sccj2: m.sccj2,
                    sccj3: m.sccj3,
                    sccj4: m.sccj4,
                    sccj5: m.sccj5,
                    sccj6: m.sccj6,
                  }
                }
                return it
              })
            }
            console.log('🔍 [前端] 最终设置的yearData:', list.slice(0, 3))
            setYearData(list)
          }
        } else {
          const [resMonth, resSccjMonth] = await Promise.all([
            fetch(`/api/sjfx/month?year=${year}&month=${month}&debug=1`),
            fetch(`/api/sjfx/sccj-month?year=${year}&month=${month}&debug=1`)
          ])
          const [jsonMonth, jsonSccjMonth] = await Promise.all([resMonth.json(), resSccjMonth.json()])
          if (jsonMonth.success) setMonthData(jsonMonth.data)
          // 右表月度若也需展示，可在此按需要 merge jsonSccjMonth
        }
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [view, year, month])

  const valuesWan = useMemo(() => {
    if (view === 'year') return yearData.map(d => Math.max(0, d.value) / 10000)
    return monthData.map(d => Math.max(0, d.monthValue) / 10000)
  }, [view, yearData, monthData])

  const maxWan = Math.max(0.0001, ...valuesWan)

  return (
    <div className="min-h-screen w-full bg-[#0a39c6] text-white">
      {/* 顶部栏 */}
      <div className="flex items-center gap-3 px-4 py-3">
        <div className="flex items-center gap-2">
          <button
            onClick={() => setView('year')}
            className={`${view==='year' ? 'bg-[#35f5ff] text-[#0037c9]' : 'bg-[#0732b8] text-white/90 hover:bg-[#0b3bd3]'} px-4 py-1.5 rounded shadow border border-white/20 text-sm font-semibold`}
          >
            款式分配
          </button>
          <button
            onClick={() => setView('month')}
            className={`${view==='month' ? 'bg-[#35f5ff] text-[#0037c9]' : 'bg-[#0732b8] text-white/90 hover:bg-[#0b3bd3]'} px-4 py-1.5 rounded shadow border border-white/20 text-sm font-semibold`}
          >
            汇总
          </button>
        </div>
        <div className="ml-4 flex items-center gap-2 text-sm">
          <span className="opacity-90">年份</span>
          <select className="bg-transparent border border-white/50 rounded px-2 py-0.5" value={year} onChange={e => setYear(e.target.value)}>
            {['2022','2023','2024','2025'].map(y => <option className="text-black" key={y} value={y}>{y}</option>)}
          </select>
          {view === 'month' && (
            <>
              <span className="opacity-90">月份</span>
              <select className="bg-transparent border border-white/50 rounded px-2 py-0.5" value={month} onChange={e => setMonth(e.target.value)}>
                {Array.from({ length: 12 }).map((_, i) => {
                  const m = String(i + 1).padStart(2, '0')
                  return <option className="text-black" key={m} value={m}>{m}</option>
                })}
              </select>
            </>
          )}
        </div>
        <div className="ml-6 text-lg font-bold">JB单 款式分配</div>
        <div className="ml-auto text-sm opacity-90">{loading ? '加载中…' : (view === 'year' ? `${year} 年` : `${year}-${month}`)}</div>
      </div>

      {/* 主体：统一滚动容器，内部各列不再单独滚动，实现同步滚动且仅出现一个滚动条 */}
      <div ref={scrollRef} className="grid grid-cols-[340px_minmax(480px,1fr)_60px_minmax(560px,1fr)] gap-4 px-4 pb-6 max-h-[calc(100vh-96px)] overflow-auto">
        {/* 左侧文本列表（两列合并渲染） */}
        <div className="pr-2 pt-[30px]">
          {view === 'year' ? (
            yearData.map((d, idx) => (
              <div key={idx} className="grid grid-cols-[32px_1fr_7rem] items-center h-[22px] text-[12px]">
                <span className="opacity-90">{idx+1}.</span>
                <span className="truncate pl-1">{d.ddlb}&nbsp;&nbsp;{d.ddml}</span>
                <span className="text-left pl-2 opacity-90 truncate">{d.ddks}</span>
              </div>
            ))
          ) : (
            monthData.map((d) => (
              <div key={d.rank} className="grid grid-cols-[32px_1fr_2rem] items-center h-[22px] text-[12px]">
                <span className="opacity-90">{d.rank}.</span>
                <span className="truncate pl-1">{d.key}</span>
                <span className={`text-right ${d.delta >= 0 ? 'text-green-300' : 'text-red-300'}`}>{d.delta >= 0 ? '↑' : '↓'}</span>
              </div>
            ))
          )}
        </div>

        {/* 中部简化横向柱图（相对容器，贴顶悬浮年份不占位） */}
        <div className="py-1 pt-[30px] relative">
          {/* 悬浮的年份胶囊，绝对定位，不影响布局 */}
          <div className="absolute -top-7 left-2 px-3 py-0.5 border border-white/70 rounded text-[12px] tracking-widest font-semibold bg-[#0a39c6]/60 backdrop-blur">
            {year} 年
          </div>
          {valuesWan.map((v, i) => {
            const widthPct = Math.min(100, (v / maxWan) * 100)
            return (
              <div key={i} className="flex items-center h-[22px] gap-2">
                <div className="flex-1 bg-white/10 rounded">
                  <div
                    className="h-[18px] rounded bg-gradient-to-r from-cyan-300 to-blue-200"
                    style={{ width: `${widthPct}%` }}
                  />
                </div>
                <div className="w-12 text-right text-[12px] opacity-90">{v.toFixed(2)}</div>
              </div>
            )
          })}
        </div>

        {/* 单位列（标题2：万件，列内靠顶，竖排） */}
        <div className="flex items-start justify-center pt-[30px]">
          <div className="mt-1 text-sm tracking-widest opacity-90" style={{ writingMode: 'vertical-rl' }}>
            万件
          </div>
        </div>

         {/* 右侧表格（按 Delphi 口径填充各车间） */}
        <div>
          <table className="w-full text-[12px] border-separate border-spacing-0">
            <thead className="sticky top-0 bg-[#0a39c6] z-10">
              <tr>
                <th className="border border-white/20 px-2 py-1 text-left h-[30px]">排名</th>
                <th className="border border-white/20 px-2 py-1">1车间</th>
                <th className="border border-white/20 px-2 py-1">2 水琴</th>
                <th className="border border-white/20 px-2 py-1">3 亚平</th>
                <th className="border border-white/20 px-2 py-1">6车间</th>
                <th className="border border-white/20 px-2 py-1">7车间</th>
                <th className="border border-white/20 px-2 py-1">其他</th>
              </tr>
            </thead>
            <tbody>
              {view === 'year'
                ? yearData.map((row, idx) => (
                    <tr key={idx} className="odd:bg-white/5">
                      <td className="border border-white/10 px-2 h-[22px]">{idx + 1}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj1 ? (row.sccj1/10000).toFixed(2) : '-'}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj2 ? (row.sccj2/10000).toFixed(2) : '-'}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj3 ? (row.sccj3/10000).toFixed(2) : '-'}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj4 ? (row.sccj4/10000).toFixed(2) : '-'}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj5 ? (row.sccj5/10000).toFixed(2) : '-'}</td>
                      <td className="border border-white/10 px-2 text-right">{row.sccj6 ? (row.sccj6/10000).toFixed(2) : '-'}</td>
                    </tr>
                  ))
                : monthData.map((r) => (
                    <tr key={r.rank} className="odd:bg-white/5">
                      <td className="border border-white/10 px-2 h-[22px]">{r.rank}</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                      <td className="border border-white/10 px-2 text-right">-</td>
                    </tr>
                  ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}


