<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">下方区域联动 - 科技特派员业务流程</text>

  <!-- 阶段一：数据初始化与地图生成 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据初始化与地图生成</text>
  
  <!-- 节点1: 系统启动 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统自动加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全市科技特派员基础数据</text>
  </g>

  <!-- 节点2: 地图初始化 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">区域分布地图</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">初始化生成</text>
  </g>

  <!-- 节点3: 图表生成 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多维度分析图表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">结构分析与统计</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 300 165 Q 350 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 600 165 Q 650 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户交互与类型选择 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户交互与类型选择</text>

  <!-- 节点4: 用户选择 -->
  <g transform="translate(250, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户类型选择</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">选择"科技特派员"</text>
  </g>

  <!-- 节点5: 系统刷新 -->
  <g transform="translate(550, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统刷新展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">地图及统计图表</text>
  </g>

  <!-- 节点6: 数据展现 -->
  <g transform="translate(850, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">直观数据展现</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">数量、类型、业务环节</text>
  </g>

  <!-- 连接线 阶段一 -> 阶段二 -->
  <path d="M 500 200 C 500 230, 350 270, 350 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 450 335 Q 500 335 550 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 750 335 Q 800 335 850 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：联动展示与清册管理 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：联动展示与清册管理</text>

  <!-- 节点7: 交互操作 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">鼠标交互操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">悬停地图/点击图表</text>
  </g>

  <!-- 节点8: 联动加载 -->
  <g transform="translate(450, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">联动加载展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">区域/分组特派员清册</text>
  </g>

  <!-- 节点9: 清册操作 -->
  <g transform="translate(750, 470)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">清册列表操作</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">检索、筛选、排序</text>
  </g>

  <!-- 连接线 阶段二 -> 阶段三 -->
  <path d="M 650 370 C 650 400, 250 440, 250 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 7 -> 8 -->
  <path d="M 350 505 Q 400 505 450 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 650 505 Q 700 505 750 505" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：业务应用与系统监控 -->
  <text x="700" y="610" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：业务应用与系统监控</text>

  <!-- 节点10: 批量导出 -->
  <g transform="translate(200, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导出名单</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">绩效考核、服务对接</text>
  </g>

  <!-- 节点11: 操作记录 -->
  <g transform="translate(500, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作行为记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">合规留痕、数据追溯</text>
  </g>

  <!-- 节点12: 动态更新 -->
  <g transform="translate(800, 640)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态更新同步</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">实时性、准确性保障</text>
  </g>

  <!-- 连接线 阶段三 -> 阶段四 -->
  <path d="M 550 540 C 550 570, 300 610, 300 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 540 C 750 570, 600 610, 600 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 540 C 850 570, 900 610, 900 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 从动态更新回到数据加载 -->
  <path d="M 900 640 C 1100 640, 1100 100, 200 100 C 200 120, 200 130, 200 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="370" text-anchor="middle" font-size="12" fill="#666">数据同步反馈</text>

  <!-- 反馈循环2: 从操作记录回到用户交互 -->
  <path d="M 500 640 C 50 640, 50 300, 150 300 C 150 320, 150 330, 150 330" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="470" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 100, 470)">行为追溯</text>

</svg>