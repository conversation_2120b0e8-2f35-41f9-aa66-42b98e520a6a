unit ErrorInfoShowFrm;

interface

uses
  Windows, Messages, SysUtils, Variants, Classes, Graphics, Controls, Forms,
  Dialogs, RzButton, ExtCtrls, RzPanel, StdCtrls, RzLabel, pngimage,
  PngFunctions, HtImage, AdvGlowButton, frxClass;

type
  TErrorInfoShowForm = class(TForm)
    RzPanel1: TRzPanel;
    RzPanel2: TRzPanel;
    RzPanel3: TRzPanel;
    Image1: TImage;
    L_Info1: TRzLabel;
    L_Info3: TRzLabel;
    Btn_Save: TAdvGlowButton;
    procedure FormShow(Sender: TObject);
    procedure Btn_SaveClick(Sender: TObject);
    procedure HTImage2Click(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
    procedure Init(infoTitle, infoErrorDes, infoDetail: string);
  end;

var
  ErrorInfoShowForm: TErrorInfoShowForm;
  FInfoTitle, FInfoErrorDes, FInfoDetail: string;

implementation

{$R *.dfm}

procedure TErrorInfoShowForm.Init(infoTitle, infoErrorDes, infoDetail: string);
begin
  FInfoTitle := infoTitle;
  FInfoErrorDes := infoErrorDes;
  FInfoDetail := infoDetail;
end;

procedure TErrorInfoShowForm.Btn_SaveClick(Sender: TObject);
begin
  self.Close;
end;

procedure TErrorInfoShowForm.FormShow(Sender: TObject);
begin
  L_Info1.Caption := FInfoTitle;
  L_Info3.Caption := FInfoDetail;
end;

procedure TErrorInfoShowForm.HTImage2Click(Sender: TObject);
begin
  self.Close;
end;

end.
