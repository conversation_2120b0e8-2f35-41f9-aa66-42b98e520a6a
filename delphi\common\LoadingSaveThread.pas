unit LoadingSaveThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingSaveFrm;

type
  TThreadSaveModel = class(TThread)
  private
    FLoadingSaveForm: TLoadingSaveForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingSaveForm: TLoadingSaveForm read FLoadingSaveForm
      write FLoadingSaveForm;

  end;

var
  LoadingSaveForm: TLoadingSaveForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadSaveModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadSaveModel.CallVclMethod;
begin
  // LoadingForm.Show;
  // LoadingForm.Update;
  // LoadingForm.RxGIFAnimator1.Animate := true;
  // LoadingForm.Update;
end;

procedure TThreadSaveModel.DoTerminate;
begin
  LoadingSaveForm.Close;
end;

end.
