<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">征集内容汇总上报业务流程</text>

  <!-- 阶段一：表单初始化与加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：表单初始化与加载</text>
  
  <!-- 节点1: 页面加载 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">进入征集内容汇总上报页面</text>
  </g>

  <!-- 节点2: 模板加载 -->
  <g transform="translate(600, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模板加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">农业农村技术服务需求填报表单</text>
  </g>

  <!-- 连接线 页面加载 -> 模板加载 -->
  <path d="M 700 200 Q 700 225 700 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：内容编辑与草稿管理 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：内容编辑与草稿管理</text>

  <!-- 节点3: 填写需求信息 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">填写需求信息</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">用户填写详细需求内容</text>
  </g>

  <!-- 节点4: 数据校验 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">系统自动校验数据完整性</text>
  </g>

  <!-- 节点5: 草稿保存 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">草稿保存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">支持中断与恢复编辑</text>
  </g>

  <!-- 连接线 模板加载 -> 填写需求信息 -->
  <path d="M 650 320 C 550 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 填写需求信息 -> 数据校验 -->
  <path d="M 500 455 C 550 455, 550 455, 600 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据校验 -> 草稿保存 -->
  <path d="M 800 455 C 850 455, 850 455, 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：归类上报与状态管理 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：归类上报与状态管理</text>

  <!-- 节点6: 需求归类 -->
  <g transform="translate(250, 630)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">需求归类</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">选择归类标签</text>
  </g>

  <!-- 节点7: 上报操作 -->
  <g transform="translate(480, 630)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">上报操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">确认并发起上报</text>
  </g>

  <!-- 节点8: 状态标记 -->
  <g transform="translate(710, 630)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态标记</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">标记为"已上报"</text>
  </g>

  <!-- 节点9: 数据归档 -->
  <g transform="translate(940, 630)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据归档</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">自动归档上报内容</text>
  </g>

  <!-- 连接线 数据校验 -> 需求归类 -->
  <path d="M 650 490 C 550 530, 450 570, 340 630" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 需求归类 -> 上报操作 -> 状态标记 -> 数据归档 -->
  <path d="M 430 665 C 450 665, 460 665, 480 665" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 660 665 C 680 665, 690 665, 710 665" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 890 665 C 910 665, 920 665, 940 665" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据联动与全周期管理 -->
  <text x="700" y="780" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据联动与全周期管理</text>

  <!-- 节点10: 跨部门联动 -->
  <g transform="translate(300, 810)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">跨部门联动</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">推送至相关业务归集模块</text>
  </g>

  <!-- 节点11: 操作记录 -->
  <g transform="translate(600, 810)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">全流程透明可控</text>
  </g>

  <!-- 节点12: 下游应用 -->
  <g transform="translate(900, 810)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">下游应用</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">需求分析、项目分派、政策支持</text>
  </g>

  <!-- 连接线 数据归档 -> 跨部门联动 -->
  <path d="M 980 700 C 900 730, 500 760, 400 810" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 跨部门联动 -> 操作记录 -> 下游应用 -->
  <path d="M 500 845 C 550 845, 550 845, 600 845" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 845 C 850 845, 850 845, 900 845" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环1: 操作记录 -> 草稿保存 -->
  <path d="M 650 810 C 600 750, 950 650, 1000 490" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="820" y="650" text-anchor="middle" font-size="11" fill="#666">操作追溯</text>

  <!-- 反馈循环2: 下游应用 -> 模板加载 -->
  <path d="M 950 810 C 1100 700, 1200 400, 800 285" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="550" text-anchor="middle" font-size="11" fill="#666">需求反馈</text>

</svg>