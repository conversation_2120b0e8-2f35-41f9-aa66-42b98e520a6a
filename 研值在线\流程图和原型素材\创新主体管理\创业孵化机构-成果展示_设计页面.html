<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创业孵化机构-成果展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">成果展示</h1>
            <p class="text-gray-600">集中呈现专利、商标等自主知识产权及相关成果的全生命周期状态</p>
        </div>

        <!-- 成果概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">专利总数</p>
                        <p class="text-2xl font-bold text-gray-900">1,245</p>
                    </div>
                    <div class="w-24 h-16">
                        <canvas id="patentTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">有效专利数</p>
                        <p class="text-2xl font-bold text-gray-900">856</p>
                    </div>
                    <div class="w-24 h-16">
                        <canvas id="validPatentChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">商标总数</p>
                        <p class="text-2xl font-bold text-gray-900">523</p>
                    </div>
                    <div class="w-24 h-16">
                        <canvas id="trademarkTrendChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm font-medium text-gray-500 mb-1">已转化成果数</p>
                        <p class="text-2xl font-bold text-gray-900">387</p>
                    </div>
                    <div class="w-24 h-16">
                        <canvas id="convertedChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类型</label>
                    <div class="flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-sm border border-blue-500 text-blue-500 rounded-md hover:bg-blue-50">专利</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">商标</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">著作权</button>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">法律状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部状态</option>
                        <option>申请中</option>
                        <option>已授权</option>
                        <option>已失效</option>
                        <option>已质押</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部领域</option>
                        <option>人工智能</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>新能源</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申请年度</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" placeholder="起始" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span>至</span>
                        <input type="number" placeholder="结束" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">权属人</label>
                    <input type="text" placeholder="请输入权属人名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="pledged" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                    <label for="pledged" class="ml-2 text-sm text-gray-700">仅显示已质押成果</label>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 成果列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <div class="text-sm text-gray-600">
                            共找到 <span class="font-medium text-gray-900">1,245</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">法律状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号/注册号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">一种基于人工智能的图像识别方法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310123456.7</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailDrawer('1')" class="text-blue-600 hover:text-blue-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市XX科技商标</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">商标</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已注册</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">TM2022123456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-05-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">商业服务</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailDrawer('2')" class="text-blue-600 hover:text-blue-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">一种新型生物医药制备方法</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">申请中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202310654321.0</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailDrawer('3')" class="text-blue-600 hover:text-blue-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">新能源电池管理系统</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">CN202320123456.X</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02-05</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailDrawer('4')" class="text-blue-600 hover:text-blue-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">XX软件著作权</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">著作权</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已登记</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">SR2023123456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04-18</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">信息技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailDrawer('5')" class="text-blue-600 hover:text-blue-900">查看清册</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 1,245 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成果分析图表区 -->
            <div class="w-full lg:w-1/3">
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">授权率对比</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">展开</button>
                    </div>
                    <div class="h-64">
                        <canvas id="approvalRateChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">技术领域分布</h3>
                        <button class="text-blue-600 hover:text-blue-800 text-sm">展开</button>
                    </div>
                    <div class="h-64">
                        <canvas id="techFieldChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 清册下钻抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full lg:w-1/2 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="h-full overflow-y-auto">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">成果清册详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <!-- 年度分布 -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">年度分布</h4>
                    <div class="h-48">
                        <canvas id="yearlyDistributionChart"></canvas>
                    </div>
                </div>
                
                <!-- 详细清单 -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">详细清单</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">编号</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">一种基于人工智能的图像识别方法</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">CN202310123456.7</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">2023-01-15</td>
                                </tr>
                                <tr>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">一种改进的图像识别算法</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">CN202210987654.3</td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">2022-11-20</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 法律状态变更记录 -->
                <div class="mb-8">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">法律状态变更记录</h4>
                    <div class="space-y-4">
                        <div class="border-l-4 border-blue-500 pl-4 py-1">
                            <div class="text-sm font-medium text-gray-900">2023-06-15</div>
                            <div class="text-sm text-gray-500">专利授权</div>
                        </div>
                        <div class="border-l-4 border-blue-500 pl-4 py-1">
                            <div class="text-sm font-medium text-gray-900">2023-01-15</div>
                            <div class="text-sm text-gray-500">专利申请</div>
                        </div>
                    </div>
                </div>
                
                <!-- 质押与许可详情 -->
                <div>
                    <h4 class="text-md font-semibold text-gray-900 mb-4">质押与许可详情</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-900 mb-2">质押状态</div>
                            <div class="text-sm text-gray-500">未质押</div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-gray-900 mb-2">许可状态</div>
                            <div class="text-sm text-gray-500">已许可给宁波市XX科技</div>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="mt-8 flex space-x-3">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        下载清册PDF
                    </button>
                    <button onclick="closeDetailDrawer()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                        返回列表定位
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openDetailDrawer(id) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
            // 这里可以加载对应id的数据
        }

        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有图表
            const initSmallChart = (id, data) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            data: data,
                            borderColor: '#3B82F6',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: false,
                            pointRadius: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { display: false }, y: { display: false } }
                    }
                });
            };

            // 初始化小趋势图
            initSmallChart('patentTrendChart', [12, 19, 3, 5, 2, 3]);
            initSmallChart('validPatentChart', [5, 8, 4, 3, 2, 1]);
            initSmallChart('trademarkTrendChart', [8, 5, 6, 7, 4, 5]);
            initSmallChart('convertedChart', [3, 5, 2, 4, 3, 2]);

            // 授权率对比图表
            const approvalCtx = document.getElementById('approvalRateChart').getContext('2d');
            new Chart(approvalCtx, {
                type: 'bar',
                data: {
                    labels: ['发明专利', '实用新型', '外观设计', '商标'],
                    datasets: [{
                        label: '申请数',
                        data: [120, 85, 60, 90],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }, {
                        label: '授权数',
                        data: [75, 68, 52, 78],
                        backgroundColor: 'rgba(16, 185, 129, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { y: { beginAtZero: true } }
                }
            });

            // 技术领域分布图表
            const techFieldCtx = document.getElementById('techFieldChart').getContext('2d');
            new Chart(techFieldCtx, {
                type: 'radar',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '信息技术'],
                    datasets: [{
                        label: '成果数量',
                        data: [65, 59, 90, 81, 56],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: '#3B82F6',
                        pointBackgroundColor: '#3B82F6'
                    }, {
                        label: '转化指数',
                        data: [28, 48, 40, 19, 36],
                        backgroundColor: 'rgba(16, 185, 129, 0.2)',
                        borderColor: '#10B981',
                        pointBackgroundColor: '#10B981'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 清册中的年度分布图表
            const yearlyCtx = document.getElementById('yearlyDistributionChart').getContext('2d');
            new Chart(yearlyCtx, {
                type: 'bar',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '申请数',
                        data: [12, 19, 15, 8],
                        backgroundColor: 'rgba(59, 130, 246, 0.7)'
                    }, {
                        label: '授权数',
                        data: [5, 8, 6, 3],
                        backgroundColor: 'rgba(16, 185, 129, 0.7)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } },
                    scales: { y: { beginAtZero: true } }
                }
            });

            // 点击抽屉外部关闭
            document.getElementById('detailDrawer').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailDrawer();
                }
            });

            // ESC键关闭抽屉
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailDrawer').classList.contains('translate-x-full')) {
                    closeDetailDrawer();
                }
            });
        });
    </script>
</body>
</html>