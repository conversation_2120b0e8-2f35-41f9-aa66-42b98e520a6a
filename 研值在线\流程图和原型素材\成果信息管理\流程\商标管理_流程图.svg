<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">商标管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">商标管理模块入口</text>
  </g>

  <!-- 节点2: 权限加载与数据初始化 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限加载</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">加载可见数据与指标概览</text>
  </g>

  <!-- 节点3: 任务状态更新 -->
  <g transform="translate(900, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">任务状态置为\"已加载\"</text>
  </g>
  
  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 500 165 Q 550 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 165 Q 850 165 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点4: 用户提交筛选条件 -->
  <g transform="translate(150, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">提交筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">用户输入查询参数</text>
  </g>

  <!-- 节点5: 商标检索服务 -->
  <g transform="translate(400, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">商标检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取符合条件的数据</text>
  </g>

  <!-- 节点6: 主体关联服务 -->
  <g transform="translate(650, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主体关联服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取创新主体信息</text>
  </g>

  <!-- 节点7: 状态更新与展示 -->
  <g transform="translate(900, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">\"查询中\" -> \"已完成\"</text>
  </g>

  <!-- 节点8: 数据整合与展示 -->
  <g transform="translate(1150, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据整合展示</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">刷新表格与图表</text>
  </g>

  <!-- 连接线 4 -> 5 -> 7 -> 8 -->
  <path d="M 350 345 Q 375 345 400 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 345 Q 775 345 900 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1100 345 Q 1125 345 1150 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 并行连接线 5 -> 6 -->
  <path d="M 500 320 C 550 300, 600 300, 650 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="575" y="295" text-anchor="middle" font-size="12" fill="#555">并行触发</text>

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点9: 新增编辑操作 -->
  <g transform="translate(100, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增编辑</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">字段校验与数据写入</text>
  </g>

  <!-- 节点10: 批量导入 -->
  <g transform="translate(320, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">文件校验与异步解析</text>
  </g>

  <!-- 节点11: 数据导出 -->
  <g transform="translate(540, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">Excel封装与下载</text>
  </g>

  <!-- 节点12: 缓存刷新 -->
  <g transform="translate(760, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存刷新</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">清空缓存重载数据</text>
  </g>

  <!-- 节点13: 通知推送 -->
  <g transform="translate(980, 510)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">通知推送</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">结果报告与下载链接</text>
  </g>

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>
  
  <!-- 节点14: 定时同步任务 -->
  <g transform="translate(300, 710)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步任务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">国家知识产权局</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">地方知识产权数据源</text>
  </g>

  <!-- 节点15: 数据比对更新 -->
  <g transform="translate(650, 710)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据比对更新</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">商标状态、法律事件</text>
    <text x="125" y="70" text-anchor="middle" font-size="12" fill="#555">到期提醒比对更新</text>
  </g>

  <!-- 连接线 14 -> 15 -->
  <path d="M 550 750 Q 600 750 650 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从阶段一到阶段二的连接 -->
  <path d="M 700 200 C 700 240, 250 270, 250 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从阶段二到阶段三的连接 -->
  <path d="M 500 380 C 400 420, 300 450, 190 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 380 C 500 420, 400 450, 410 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 750 380 C 650 420, 600 450, 630 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 850 380 C 750 420, 700 450, 850 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 1000 380 C 900 420, 850 450, 1070 510" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 操作结果到通知推送的连接 -->
  <path d="M 410 580 C 600 620, 800 620, 1070 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <path d="M 630 580 C 750 620, 850 620, 1070 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />

  <!-- 数据同步反馈循环 -->
  <path d="M 775 710 C 1100 650, 1200 400, 1200 200 C 1200 150, 1150 130, 1100 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="400" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1250, 400)">数据同步反馈</text>

</svg>