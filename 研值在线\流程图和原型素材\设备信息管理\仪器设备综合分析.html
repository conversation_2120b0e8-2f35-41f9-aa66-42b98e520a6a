<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪器设备综合分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">仪器设备综合分析</h1>
                    <p class="mt-2 text-sm text-gray-600">对全市科研仪器设备进行多维度、可视化的综合统计和分布分析</p>
                </div>
                <div class="flex space-x-3">
                    <!-- 数据导出按钮 -->
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        数据导出
                    </button>
                    <!-- 刷新数据按钮 -->
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 数据概览卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">设备总数</p>
                        <p class="text-2xl font-semibold text-gray-900">12,847</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">在用设备</p>
                        <p class="text-2xl font-semibold text-gray-900">10,234</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">维修中</p>
                        <p class="text-2xl font-semibold text-gray-900">1,523</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-100 rounded-md flex items-center justify-center">
                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">闲置设备</p>
                        <p class="text-2xl font-semibold text-gray-900">1,090</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要分析区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 分类统计区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">设备分类统计</h3>
                    <div class="flex space-x-2">
                        <button id="categoryChartType" class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200">柱状图</button>
                        <button onclick="switchCategoryChart('pie')" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">饼图</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="categoryChart"></canvas>
                </div>
                <div class="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div class="flex justify-between">
                        <span class="text-gray-600">光谱仪器</span>
                        <span class="font-medium">3,245台</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">色谱仪器</span>
                        <span class="font-medium">2,876台</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">质谱仪器</span>
                        <span class="font-medium">1,987台</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">电化学仪器</span>
                        <span class="font-medium">1,654台</span>
                    </div>
                </div>
            </div>

            <!-- 使用状态分布区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">使用状态分布</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">全部</button>
                        <button class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">本月</button>
                    </div>
                </div>
                <div class="h-80">
                    <canvas id="statusChart"></canvas>
                </div>
                <div class="mt-4 space-y-2">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">正常使用</span>
                        </div>
                        <span class="text-sm font-medium">79.6%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">维修中</span>
                        </div>
                        <span class="text-sm font-medium">11.9%</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                            <span class="text-sm text-gray-600">闲置</span>
                        </div>
                        <span class="text-sm font-medium">8.5%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 区域分布分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-semibold text-gray-900">区域分布分析</h3>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                        <option>全市</option>
                        <option>市辖区</option>
                        <option>县级市</option>
                    </select>
                    <button class="px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md">热力图</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 地图区域 -->
                <div class="lg:col-span-2">
                    <div class="h-96 bg-gray-100 rounded-lg flex items-center justify-center relative overflow-hidden">
                        <img src="https://source.unsplash.com/800x400?map,city" alt="区域分布地图" class="w-full h-full object-cover rounded-lg">
                        <div class="absolute inset-0 bg-blue-500 bg-opacity-20"></div>
                        <div class="absolute top-4 left-4 bg-white rounded-lg p-3 shadow-md">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">图例</h4>
                            <div class="space-y-1 text-xs">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                    <span>高密度 (>500台)</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                                    <span>中密度 (100-500台)</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                    <span>低密度 (<100台)</span>
                                </div>
                            </div>
                        </div>
                        <!-- 模拟数据点 -->
                        <div class="absolute top-1/4 left-1/3 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                        <div class="absolute top-1/2 left-1/2 w-3 h-3 bg-yellow-500 rounded-full animate-pulse"></div>
                        <div class="absolute top-3/4 right-1/3 w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    </div>
                </div>
                <!-- 区域统计 -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">各区域设备数量</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">高新技术开发区</p>
                                <p class="text-xs text-gray-500">23个单位</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">3,245台</p>
                                <p class="text-xs text-gray-500">25.3%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">经济技术开发区</p>
                                <p class="text-xs text-gray-500">18个单位</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">2,876台</p>
                                <p class="text-xs text-gray-500">22.4%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">市中心区</p>
                                <p class="text-xs text-gray-500">31个单位</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">2,134台</p>
                                <p class="text-xs text-gray-500">16.6%</p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="text-sm font-medium text-gray-900">其他区域</p>
                                <p class="text-xs text-gray-500">45个单位</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-semibold text-gray-900">4,592台</p>
                                <p class="text-xs text-gray-500">35.7%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 综合分析说明区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">综合分析说明</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-blue-900 mb-2">统计逻辑说明</h4>
                    <p class="text-sm text-blue-700">数据统计基于全市科研院所、高等院校、企业研发中心等单位上报的仪器设备信息，按照国家标准分类体系进行归类统计。</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-green-900 mb-2">数据更新周期</h4>
                    <p class="text-sm text-green-700">系统每日凌晨2点自动更新数据，设备状态信息实时同步。最后更新时间：2024年1月15日 02:00。</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <h4 class="text-md font-medium text-yellow-900 mb-2">数据来源</h4>
                    <p class="text-sm text-yellow-700">数据来源包括科技管理系统、设备管理平台、第三方数据接口等多个渠道，确保数据的完整性和准确性。</p>
                </div>
            </div>
        </div>

        <!-- 数据明细联动区 -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">设备明细列表</h3>
                    <div class="flex space-x-2">
                        <input type="text" placeholder="搜索设备名称或编号" class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                        <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                            <option>全部分类</option>
                            <option>光谱仪器</option>
                            <option>色谱仪器</option>
                            <option>质谱仪器</option>
                        </select>
                        <select class="px-3 py-1 text-sm border border-gray-300 rounded-md">
                            <option>全部状态</option>
                            <option>正常使用</option>
                            <option>维修中</option>
                            <option>闲置</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">设备编号</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">归属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所在区域</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">高分辨率质谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MS-2024-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">质谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市科学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500">正常使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">液相色谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">LC-2024-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">色谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市第一医院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500">维修中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">原子吸收光谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AAS-2024-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市环保局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">经开区</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500">正常使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">电化学工作站</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">EC-2024-004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">电化学仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新区</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500">闲置</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">红外光谱仪</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">IR-2024-005</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">光谱仪器</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市检测中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市中心区</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-gray-500">正常使用</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-900">
                                <button class="hover:underline">查看详情</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 12,847 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分类统计图表
        const categoryCtx = document.getElementById('categoryChart').getContext('2d');
        let categoryChart = new Chart(categoryCtx, {
            type: 'bar',
            data: {
                labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],
                datasets: [{
                    label: '设备数量',
                    data: [3245, 2876, 1987, 1654, 1432, 1653],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(139, 92, 246, 0.8)',
                        'rgba(107, 114, 128, 0.8)'
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(107, 114, 128, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 使用状态图表
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['正常使用', '维修中', '闲置'],
                datasets: [{
                    data: [10234, 1523, 1090],
                    backgroundColor: [
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)'
                    ],
                    borderColor: [
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(239, 68, 68, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });

        // 切换分类图表类型
        function switchCategoryChart(type) {
            categoryChart.destroy();
            
            const config = {
                type: type,
                data: {
                    labels: ['光谱仪器', '色谱仪器', '质谱仪器', '电化学仪器', '显微镜', '其他'],
                    datasets: [{
                        label: '设备数量',
                        data: [3245, 2876, 1987, 1654, 1432, 1653],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(139, 92, 246, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(239, 68, 68, 1)',
                            'rgba(139, 92, 246, 1)',
                            'rgba(107, 114, 128, 1)'
                        ],
                        borderWidth: type === 'pie' ? 2 : 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: type === 'pie'
                        }
                    },
                    scales: type === 'bar' ? {
                        y: {
                            beginAtZero: true
                        }
                    } : {}
                }
            };
            
            categoryChart = new Chart(categoryCtx, config);
            
            // 更新按钮状态
            document.querySelectorAll('#categoryChartType, [onclick*="switchCategoryChart"]').forEach(btn => {
                btn.className = 'px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200';
            });
            
            if (type === 'bar') {
                document.getElementById('categoryChartType').className = 'px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200';
                document.getElementById('categoryChartType').textContent = '柱状图';
            } else {
                event.target.className = 'px-3 py-1 text-sm bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200';
            }
        }

        // 设置默认柱状图按钮状态
        document.getElementById('categoryChartType').onclick = () => switchCategoryChart('bar');
    </script>
</body>
</html>