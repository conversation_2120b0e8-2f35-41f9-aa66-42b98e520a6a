unit CcYsAll;

interface

uses
  Classes;

type
  TCcYsAll = class
  private
    FYsid: Integer;

    FSsScb_a: double;
    FSsScb_b: double;
    FSsScb_c: double;
    FSsScb_d: double;
    FPbcb_a: double;
    FPbcb_b: double;
    FPbcb_c: double;
    FPbcb_d: double;
    FSzbcb: double;
    FLwcb: double;
    FHjlcb: double;
    FGtbcb: double;
    FMsje: double;
    FRzdcb: double;
    FmQtje_a: double;
    FmQtje_b: double;
    FmQtje_c: double;
    FSbje: double;
    FDpje: double;
    FXje: double;
    FNkje: double;
    FLlje: double;
    FXjdje: double;
    FNhcje: double;
    FQtje_a: double;
    FQtje_b: double;
    FQtje_c: double;
    FYhje: double;
    FXhje: double;
    FCyrxCb: double;
    FCysxCb: double;
    FCyyhCb: double;
    FTxmje: double;
    FCzje: double;
    FCbje: double;
    FJdje: double;
    FZxje: double;
    FBzqtje: double;
    FGj: double;
  public
    property Ysid: Integer read FYsid write FYsid;
    property SsScb_a: double read FSsScb_a write FSsScb_a;
    property SsScb_b: double read FSsScb_b write FSsScb_b;
    property SsScb_c: double read FSsScb_c write FSsScb_c;
    property SsScb_d: double read FSsScb_d write FSsScb_d;
    property Pbcb_a: double read FPbcb_a write FPbcb_a;
    property Pbcb_b: double read FPbcb_b write FPbcb_b;
    property Pbcb_c: double read FPbcb_c write FPbcb_c;
    property Pbcb_d: double read FPbcb_d write FPbcb_d;

    property Szbcb: double read FSzbcb write FSzbcb;
    property Lwcb: double read FLwcb write FLwcb;
    property Hjlcb: double read FHjlcb write FHjlcb;
    property Gtbcb: double read FGtbcb write FGtbcb;
    property Msje: double read FMsje write FMsje;
    property Rzdcb: double read FRzdcb write FRzdcb;

    property mQtje_a: double read FmQtje_a write FmQtje_a;
    property mQtje_b: double read FmQtje_b write FmQtje_b;
    property mQtje_c: double read FmQtje_c write FmQtje_c;

    property Sbje: double read FSbje write FSbje;
    property Dpje: double read FDpje write FDpje;
    property Xje: double read FXje write FXje;
    property Nkje: double read FNkje write FNkje;
    property Llje: double read FLlje write FLlje;
    property Xjdje: double read FXjdje write FXjdje;
    property Nhcje: double read FNhcje write FNhcje;

    property Qtje_a: double read FQtje_a write FQtje_a;
    property Qtje_b: double read FQtje_b write FQtje_b;
    property Qtje_c: double read FQtje_c write FQtje_c;

    property Yhje: double read FYhje write FYhje;
    property Xhje: double read FXhje write FXhje;
    property CyrxCb: double read FCyrxCb write FCyrxCb;
    property CysxCb: double read FCysxCb write FCysxCb;
    property CyyhCb: double read FCyyhCb write FCyyhCb;
    property Txmje: double read FTxmje write FTxmje;
    property Czje: double read FCzje write FCzje;
    property Cbje: double read FCbje write FCbje;
    property Jdje: double read FJdje write FJdje;
    property Zxje: double read FZxje write FZxje;
    property Bzqtje: double read FBzqtje write FBzqtje;
    property Gj: double read FGj write FGj;
  end;

implementation

end.
