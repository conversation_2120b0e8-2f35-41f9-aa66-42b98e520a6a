<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机构详细信息分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 顶部概览区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6 border-l-4 border-blue-600">
            <div class="flex justify-between items-start">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">宁波市智能科技有限公司</h1>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="text-sm text-gray-600">统一社会信用代码: 91330201MA2B123456</span>
                        <span class="text-sm text-gray-600">成立日期: 2015-08-12</span>
                        <span class="text-sm text-gray-600">人员规模: 中型(256人)</span>
                        <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">低风险</span>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        编辑信息
                    </button>
                    <button class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 基本信息统计区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        基础信息
                    </h2>
                    <span class="text-xs text-gray-500">最后更新: 2023-06-15</span>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">注册地址</label>
                        <p class="text-sm text-gray-800">宁波市鄞州区科技大道88号</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">法定代表人</label>
                        <p class="text-sm text-gray-800">张三</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">注册资本</label>
                        <p class="text-sm text-gray-800">5000万元人民币</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">机构类型</label>
                        <p class="text-sm text-gray-800">高新技术企业</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">主营业务</label>
                        <p class="text-sm text-gray-800">智能装备制造、工业软件开发</p>
                    </div>
                    <div>
                        <label class="block text-xs font-medium text-gray-500 mb-1">技术热词</label>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">人工智能</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">工业4.0</span>
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">智能制造</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                        数据完整度
                    </h2>
                    <span class="text-xs text-gray-500">完整度: 85%</span>
                </div>
                <div class="flex items-center justify-center h-40">
                    <div id="completeness-chart" class="w-full h-full"></div>
                </div>
            </div>
        </div>

        <!-- 层级结构分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                    层级结构分析
                </h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    导出结构图
                </button>
            </div>
            <div id="hierarchy-chart" class="w-full h-96"></div>
        </div>

        <!-- 荣誉与资质评估区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    荣誉与资质评估
                </h2>
                <div class="flex space-x-2">
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部状态</option>
                        <option>有效</option>
                        <option>即将到期</option>
                        <option>已过期</option>
                    </select>
                    <select class="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>全部等级</option>
                        <option>国家级</option>
                        <option>省级</option>
                        <option>市级</option>
                    </select>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600">证书总数</p>
                            <p class="text-2xl font-bold text-blue-600">28</p>
                        </div>
                        <div class="w-16 h-16">
                            <div id="certificate-total-chart" class="w-full h-full"></div>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600">有效证书</p>
                            <p class="text-2xl font-bold text-green-600">22</p>
                        </div>
                        <div class="w-16 h-16">
                            <div id="certificate-valid-chart" class="w-full h-full"></div>
                        </div>
                    </div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600">即将到期</p>
                            <p class="text-2xl font-bold text-yellow-600">4</p>
                        </div>
                        <div class="w-16 h-16">
                            <div id="certificate-expiring-chart" class="w-full h-full"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="certificate-type-chart" class="w-full h-64"></div>
        </div>

        <!-- 经营与财务状况区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                经营与财务状况
            </h2>
            <div id="financial-chart" class="w-full h-96 mb-4"></div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-700 mb-2">经营摘要</h3>
                <p class="text-sm text-gray-600">宁波市智能科技有限公司近三年营业收入保持稳定增长，年均增长率达15.6%。2022年实现营业收入2.8亿元，同比增长12.3%；净利润3200万元，同比增长8.5%。资产负债率维持在45%左右，财务状况稳健。2022年纳税总额达到1860万元，较上年增长10.2%。</p>
            </div>
        </div>

        <!-- 研发投入与产出分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                </svg>
                研发投入与产出分析
            </h2>
            <div id="rd-chart" class="w-full h-96 mb-4"></div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600">研发强度</p>
                            <p class="text-2xl font-bold text-blue-600">8.2%</p>
                        </div>
                        <svg class="w-10 h-10 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center">
                        <div>
                            <p class="text-sm text-gray-600">专利产出效率</p>
                            <p class="text-2xl font-bold text-green-600">1.8</p>
                        </div>
                        <svg class="w-10 h-10 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据质量与预警区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 flex items-center mb-4">
                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
                数据质量与预警
            </h2>
            <div class="space-y-3">
                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full mr-3">高</span>
                        <span class="text-sm text-gray-700">2022年Q4财务数据异常波动</span>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">去修复</button>
                </div>
                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full mr-3">中</span>
                        <span class="text-sm text-gray-700">3项资质证书将在30天内到期</span>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">去修复</button>
                </div>
                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full mr-3">中</span>
                        <span class="text-sm text-gray-700">研发投入数据缺失2021年Q2</span>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">去修复</button>
                </div>
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full mr-3">低</span>
                        <span class="text-sm text-gray-700">5个子公司联系方式缺失</span>
                    </div>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">去修复</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@latest/dist/echarts.min.js"></script>
    
    <script>
        // 数据完整度图表
        const completenessChart = echarts.init(document.getElementById('completeness-chart'));
        completenessChart.setOption({
            series: [{
                type: 'pie',
                radius: ['70%', '90%'],
                avoidLabelOverlap: false,
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '18',
                        fontWeight: 'bold',
                        formatter: '{c}%'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 85, name: '完整度', itemStyle: { color: '#3b82f6' } },
                    { value: 15, name: '缺失', itemStyle: { color: '#e5e7eb' } }
                ]
            }]
        });

        // 层级结构图表
        const hierarchyChart = echarts.init(document.getElementById('hierarchy-chart'));
        hierarchyChart.setOption({
            tooltip: {
                trigger: 'item',
                triggerOn: 'mousemove'
            },
            series: [{
                type: 'tree',
                data: [{
                    name: '宁波市智能科技有限公司',
                    children: [
                        {
                            name: '研发中心',
                            children: [
                                { name: '人工智能实验室' },
                                { name: '工业软件研发部' },
                                { name: '智能制造研究所' }
                            ]
                        },
                        {
                            name: '生产制造',
                            children: [
                                { name: '宁波生产基地' },
                                { name: '杭州分厂' }
                            ]
                        },
                        {
                            name: '市场营销',
                            children: [
                                { name: '国内市场部' },
                                { name: '国际业务部' }
                            ]
                        }
                    ]
                }],
                symbolSize: 7,
                label: {
                    position: 'left',
                    verticalAlign: 'middle',
                    align: 'right',
                    fontSize: 12
                },
                leaves: {
                    label: {
                        position: 'right',
                        verticalAlign: 'middle',
                        align: 'left'
                    }
                },
                emphasis: {
                    focus: 'descendant'
                },
                expandAndCollapse: true,
                animationDuration: 550,
                animationDurationUpdate: 750
            }]
        });

        // 证书总数图表
        const certificateTotalChart = echarts.init(document.getElementById('certificate-total-chart'));
        certificateTotalChart.setOption({
            series: [{
                type: 'pie',
                radius: ['70%', '90%'],
                avoidLabelOverlap: false,
                label: { show: false },
                labelLine: { show: false },
                data: [
                    { value: 28, itemStyle: { color: '#3b82f6' } }
                ]
            }]
        });

        // 有效证书图表
        const certificateValidChart = echarts.init(document.getElementById('certificate-valid-chart'));
        certificateValidChart.setOption({
            series: [{
                type: 'pie',
                radius: ['70%', '90%'],
                avoidLabelOverlap: false,
                label: { show: false },
                labelLine: { show: false },
                data: [
                    { value: 22, itemStyle: { color: '#10b981' } }
                ]
            }]
        });

        // 即将到期证书图表
        const certificateExpiringChart = echarts.init(document.getElementById('certificate-expiring-chart'));
        certificateExpiringChart.setOption({
            series: [{
                type: 'pie',
                radius: ['70%', '90%'],
                avoidLabelOverlap: false,
                label: { show: false },
                labelLine: { show: false },
                data: [
                    { value: 4, itemStyle: { color: '#f59e0b' } }
                ]
            }]
        });

        // 证书类型图表
        const certificateTypeChart = echarts.init(document.getElementById('certificate-type-chart'));
        certificateTypeChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['证书数量', '即将到期']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'value'
            },
            yAxis: {
                type: 'category',
                data: ['高新技术企业', 'ISO9001', '专利证书', '软件著作权', '行业认证']
            },
            series: [
                {
                    name: '证书数量',
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: true
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: [3, 5, 8, 7, 5],
                    itemStyle: { color: '#3b82f6' }
                },
                {
                    name: '即将到期',
                    type: 'bar',
                    stack: 'total',
                    label: {
                        show: true
                    },
                    emphasis: {
                        focus: 'series'
                    },
                    data: [0, 1, 2, 1, 0],
                    itemStyle: { color: '#f59e0b' }
                }
            ]
        });

        // 财务图表
        const financialChart = echarts.init(document.getElementById('financial-chart'));
        financialChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            legend: {
                data: ['营业收入', '净利润', '纳税总额', '资产负债率']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    data: ['2020', '2021', '2022'],
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '金额(万元)',
                    min: 0,
                    max: 30000,
                    interval: 5000,
                    axisLabel: {
                        formatter: '{value}'
                    }
                },
                {
                    type: 'value',
                    name: '资产负债率(%)',
                    min: 0,
                    max: 100,
                    interval: 20,
                    axisLabel: {
                        formatter: '{value}%'
                    }
                }
            ],
            series: [
                {
                    name: '营业收入',
                    type: 'bar',
                    data: [22000, 24750, 28000],
                    itemStyle: { color: '#3b82f6' }
                },
                {
                    name: '净利润',
                    type: 'bar',
                    data: [2500, 2950, 3200],
                    itemStyle: { color: '#10b981' }
                },
                {
                    name: '纳税总额',
                    type: 'bar',
                    data: [1500, 1680, 1860],
                    itemStyle: { color: '#6366f1' }
                },
                {
                    name: '资产负债率',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [48, 46, 45],
                    itemStyle: { color: '#f59e0b' }
                }
            ]
        });

        // 研发图表
        const rdChart = echarts.init(document.getElementById('rd-chart'));
        rdChart.setOption({
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    crossStyle: {
                        color: '#999'
                    }
                }
            },
            legend: {
                data: ['研发投入', '研发人员', '专利授权量', '投入产出比']
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: [
                {
                    type: 'category',
                    data: ['2018', '2019', '2020', '2021', '2022'],
                    axisPointer: {
                        type: 'shadow'
                    }
                }
            ],
            yAxis: [
                {
                    type: 'value',
                    name: '金额(万元)/人数',
                    min: 0,
                    max: 3000,
                    interval: 500
                },
                {
                    type: 'value',
                    name: '专利数/投入产出比',
                    min: 0,
                    max: 100,
                    interval: 20
                }
            ],
            series: [
                {
                    name: '研发投入',
                    type: 'bar',
                    data: [1200, 1500, 1800, 2100, 2300],
                    itemStyle: { color: '#3b82f6' }
                },
                {
                    name: '研发人员',
                    type: 'bar',
                    data: [45, 68, 92, 115, 142],
                    itemStyle: { color: '#10b981' }
                },
                {
                    name: '专利授权量',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [12, 18, 25, 32, 42],
                    itemStyle: { color: '#f59e0b' }
                },
                {
                    name: '投入产出比',
                    type: 'line',
                    yAxisIndex: 1,
                    data: [1.0, 1.2, 1.4, 1.5, 1.8],
                    itemStyle: { color: '#ef4444' }
                }
            ]
        });

        // 响应式调整
        window.addEventListener('resize', function() {
            completenessChart.resize();
            hierarchyChart.resize();
            certificateTotalChart.resize();
            certificateValidChart.resize();
            certificateExpiringChart.resize();
            certificateTypeChart.resize();
            financialChart.resize();
            rdChart.resize();
        });
    </script>
</body>
</html>