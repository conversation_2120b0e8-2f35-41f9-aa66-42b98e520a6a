# 聊天界面调整说明

## 概述
根据参考图片和DifyChat组件的设计，对聊天界面进行了以下调整：

## 主要调整内容

### 1. 头像设计
- **提问人头像**: 蓝色背景 (`bg-blue-500`) + 白色图标，带蓝色边框
- **助手头像**: 灰色背景 (`bg-gray-100`) + 灰色图标，带灰色边框
- 头像大小: 8x8 (32px)，使用 User 和 Bot 图标

### 2. 消息内容区域
- **用户消息**: 保持蓝色背景 (`bg-primary`)
- **助手消息**: 灰色背景容器 (`bg-muted`) + 白色内容卡片 (`bg-white`)
- 消息内容使用白色卡片显示，提供更好的可读性
- 支持思考过程的折叠显示

### 3. 底部操作按钮
- **时间显示**: 灰色文字 (`text-gray-500`)，增加间距
- **操作按钮**: 
  - 复制按钮
  - 点赞/点踩按钮
  - 重新生成按钮
  - 添加备注按钮
- **按钮样式**: 
  - 大小调整为 7x7 (28px)
  - 默认灰色 (`text-gray-500`)
  - 悬停时变深色 (`hover:text-gray-700`)
  - 激活状态有对应颜色 (绿色/红色)
- **复制成功提示**: 绿色文字 + 加粗显示

### 4. Token使用信息
- 显示在右侧，包含：
  - Token数量徽章
  - 耗时信息徽章
- 使用不同颜色区分信息类型

## 文件结构

```
src/app/agent/chat/
├── page.tsx          # 主聊天页面
└── README.md         # 说明文档

src/app/agent/difyznwd/DifyChat/
├── ChatMessage.tsx   # 消息组件 (已调整)
├── DifyChat.tsx      # 主聊天组件
├── CustomizableChat.tsx # 可定制聊天组件
└── ...
```

## 使用方法

1. 访问 `/agent/chat` 页面查看调整后的聊天界面
2. 配置正确的 `chatConfig` 参数：
   - `apiKey`: Dify应用的API密钥
   - `baseURL`: Dify服务的基础URL
   - `user`: 用户标识

## 特性

- ✅ 用户和助手头像区分
- ✅ 消息内容灰色底色 + 白色卡片
- ✅ 底部时间和操作按钮
- ✅ 复制、点赞、重新生成功能
- ✅ Token使用信息显示
- ✅ 思考过程折叠显示
- ✅ 响应式设计
- ✅ 无障碍支持

## 技术栈

- React 18
- TypeScript
- Tailwind CSS
- Radix UI 组件
- Lucide React 图标
