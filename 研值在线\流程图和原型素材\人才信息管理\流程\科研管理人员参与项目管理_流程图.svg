<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="24" text-anchor="middle" font-weight="600" fill="#333">科研管理人员参与项目管理流程</text>

  <!-- 阶段一：数据加载与展示 -->
  <text x="700" y="100" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据加载与展示</text>
  
  <!-- 节点1: 模块进入 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模块进入</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">用户访问项目管理</text>
  </g>

  <!-- 节点2: 数据加载 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据加载</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">系统加载全部记录</text>
  </g>

  <!-- 节点3: 主列表展示 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">主列表展示</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">参与项目记录</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 480 165 Q 515 165 550 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 730 165 Q 765 165 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：筛选与检索 -->
  <text x="700" y="270" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段二：筛选与检索</text>

  <!-- 节点4: 筛选条件设置 -->
  <g transform="translate(400, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选条件设置</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">名称、级别、类型、时间</text>
  </g>

  <!-- 节点5: 实时刷新 -->
  <g transform="translate(650, 300)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时刷新</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">符合条件的项目数据</text>
  </g>

  <!-- 连接线 主列表 -> 筛选 -->
  <path d="M 890 200 C 890 230, 600 250, 500 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 600 335 Q 625 335 650 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：项目操作管理 -->
  <text x="700" y="440" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段三：项目操作管理</text>

  <!-- 节点6: 新增项目 -->
  <g transform="translate(100, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增项目</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">填写提交信息</text>
  </g>

  <!-- 节点7: 编辑项目 -->
  <g transform="translate(300, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑项目</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">信息调整更新</text>
  </g>

  <!-- 节点8: 移除项目 -->
  <g transform="translate(500, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">移除项目</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">确认后逻辑删除</text>
  </g>

  <!-- 节点9: 详情查看 -->
  <g transform="translate(700, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">详情查看</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">项目全貌展示</text>
  </g>

  <!-- 节点10: 数据导出 -->
  <g transform="translate(900, 470)" filter="url(#soft-shadow)">
    <rect width="160" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="80" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="80" y="50" text-anchor="middle" font-size="12" fill="#555">标准文件生成</text>
  </g>

  <!-- 连接线 实时刷新 -> 各操作 -->
  <path d="M 700 370 C 600 400, 300 430, 180 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 370 C 650 400, 450 430, 380 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 740 370 C 720 400, 650 430, 580 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 760 370 C 760 400, 780 430, 780 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 780 370 C 820 400, 900 430, 980 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：系统处理与反馈 -->
  <text x="700" y="610" font-size="18" text-anchor="middle" font-weight="500" fill="#555">阶段四：系统处理与反馈</text>

  <!-- 节点11: 数据校验入库 -->
  <g transform="translate(200, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验入库</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">新增编辑处理</text>
  </g>

  <!-- 节点12: 分层级下钻 -->
  <g transform="translate(450, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">分层级下钻</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">详细信息展示</text>
  </g>

  <!-- 节点13: 业务归档汇报 -->
  <g transform="translate(700, 640)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">业务归档汇报</text>
    <text x="90" y="50" text-anchor="middle" font-size="12" fill="#555">导出文件应用</text>
  </g>

  <!-- 连接线 新增编辑 -> 校验入库 -->
  <path d="M 180 540 C 180 580, 250 600, 270 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 380 540 C 380 580, 320 600, 310 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 详情查看 -> 分层级下钻 -->
  <path d="M 780 540 C 780 580, 580 600, 540 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据导出 -> 业务归档 -->
  <path d="M 980 540 C 980 580, 830 600, 790 640" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从业务归档回到数据加载 -->
  <path d="M 790 640 C 1100 600, 1150 300, 1100 165 C 1050 165, 800 165, 730 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="400" text-anchor="middle" font-size="11" fill="#666">业务反馈</text>

  <!-- 反馈循环：从分层级下钻回到筛选条件 -->
  <path d="M 450 675 C 350 675, 300 600, 300 400 C 300 380, 350 360, 400 335" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="550" text-anchor="middle" font-size="11" fill="#666">深度查询</text>

</svg>