<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研院所-下属科研平台建设展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">下属科研平台建设展示</h1>

        <!-- 平台概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">重点实验室</p>
                        <p class="text-2xl font-bold text-gray-900">24</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="labChart"></canvas>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">较去年增长 <span class="text-green-500">+12%</span></div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">研究中心</p>
                        <p class="text-2xl font-bold text-gray-900">36</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="centerChart"></canvas>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">较去年增长 <span class="text-green-500">+8%</span></div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">工程实验室</p>
                        <p class="text-2xl font-bold text-gray-900">18</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="engChart"></canvas>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">较去年增长 <span class="text-green-500">+15%</span></div>
            </div>
            <div class="bg-white shadow-md rounded-lg p-4">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">工程技术中心</p>
                        <p class="text-2xl font-bold text-gray-900">22</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="techChart"></canvas>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-500">较去年增长 <span class="text-green-500">+10%</span></div>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">平台类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            重点实验室
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            研究中心
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            工程实验室
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            工程技术中心
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">研究领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="material">材料科学</option>
                        <option value="bio">生物医药</option>
                        <option value="info">信息技术</option>
                        <option value="env">环境科学</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">建设级别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="radio" name="level" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            国家级
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="radio" name="level" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            省级
                        </label>
                        <label class="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm">
                            <input type="radio" name="level" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            市级
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">挂牌年份</label>
                    <div class="flex items-center space-x-2">
                        <input type="number" placeholder="起始年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="text-gray-500">至</span>
                        <input type="number" placeholder="结束年份" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">平台名称/依托单位</label>
                    <input type="text" placeholder="输入关键词搜索" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 平台列表区 -->
            <div class="flex-1 lg:w-3/4">
                <div class="bg-white shadow-md rounded-lg">
                    <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                        <h2 class="text-lg font-medium text-gray-800">平台列表</h2>
                        <div class="flex space-x-2">
                            <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">研究方向</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建设级别</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">挂牌年份</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">依托单位</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">运行状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市材料科学重点实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新材料研发与应用</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市生物医药研究中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">研究中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">创新药物研发</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市第一医院</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市智能制造工程实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波工程学院</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市环境工程技术研究中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">工程技术中心</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">环境治理技术</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市环保研究院</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">建设中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市信息技术重点实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">重点实验室</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能与大数据</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波理工学院</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">运行中</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openDetailModal('5')" class="text-blue-600 hover:text-blue-900">查看简介</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">24</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 平台分析图表区 -->
            <div class="lg:w-1/4 space-y-4">
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">建设级别分布</h3>
                    <div class="h-64">
                        <canvas id="levelChart"></canvas>
                    </div>
                </div>
                <div class="bg-white shadow-md rounded-lg p-4">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">研究领域对比</h3>
                    <div class="h-64">
                        <canvas id="fieldCompareChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 平台详情抽屉 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 w-full max-w-4xl">
            <div class="bg-white rounded-lg shadow-xl">
                <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-xl font-semibold text-gray-900">平台详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto max-h-[70vh]">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <!-- 基本信息 -->
                        <div class="lg:col-span-2">
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">基本信息</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500">平台名称：</span>
                                        <span class="font-medium text-gray-900">宁波市材料科学重点实验室</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">平台类型：</span>
                                        <span class="font-medium text-gray-900">重点实验室</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">建设级别：</span>
                                        <span class="font-medium text-gray-900">省级</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">挂牌年份：</span>
                                        <span class="font-medium text-gray-900">2018年</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">依托单位：</span>
                                        <span class="font-medium text-gray-900">宁波大学</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">运行状态：</span>
                                        <span class="font-medium text-gray-900">运行中</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 研究方向与特色 -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">研究方向与特色</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p>本实验室主要研究方向包括新型功能材料的设计与制备、材料性能表征与优化、材料应用技术开发等。实验室在纳米材料、复合材料等领域具有显著特色，已形成从基础研究到应用开发的全链条创新体系。</p>
                                    <p>实验室特色研究方向：</p>
                                    <ul class="list-disc pl-5">
                                        <li>高性能纳米材料制备技术</li>
                                        <li>环境友好型复合材料研发</li>
                                        <li>材料表面改性技术</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <!-- 软硬件设施 -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">软硬件设施</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p>实验室拥有总价值超过3000万元的先进仪器设备，包括：</p>
                                    <ul class="list-disc pl-5">
                                        <li>场发射扫描电子显微镜（FE-SEM）</li>
                                        <li>X射线衍射仪（XRD）</li>
                                        <li>原子力显微镜（AFM）</li>
                                        <li>热分析系统（TGA/DSC）</li>
                                    </ul>
                                    <p>实验室还建立了完善的材料计算模拟平台和材料数据库系统，为研究工作提供全方位支持。</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧信息 -->
                        <div>
                            <!-- 项目与成果代表 -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">项目与成果代表</h4>
                                <div class="text-sm text-gray-700 space-y-3">
                                    <div>
                                        <p class="font-medium">国家自然科学基金重点项目</p>
                                        <p class="text-gray-500">新型纳米材料的设计与性能调控（2020-2023）</p>
                                    </div>
                                    <div>
                                        <p class="font-medium">浙江省重点研发计划</p>
                                        <p class="text-gray-500">高性能复合材料关键技术研究（2019-2021）</p>
                                    </div>
                                    <div>
                                        <p class="font-medium">代表性论文</p>
                                        <p class="text-gray-500">Advanced Materials, 2022, 34(12): 2105678</p>
                                    </div>
                                    <div>
                                        <p class="font-medium">授权专利</p>
                                        <p class="text-gray-500">一种新型纳米复合材料及其制备方法（ZL202010123456.7）</p>
                                    </div>
                                    <button class="w-full mt-3 px-3 py-2 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700">
                                        查看项目一览
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 对外开放服务 -->
                            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">对外开放服务</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p>实验室面向社会开放，提供以下服务：</p>
                                    <ul class="list-disc pl-5">
                                        <li>材料性能测试与分析</li>
                                        <li>材料制备技术咨询</li>
                                        <li>联合研发与技术攻关</li>
                                        <li>人才培养与培训</li>
                                    </ul>
                                    <p>服务时间：周一至周五 9:00-17:00</p>
                                </div>
                            </div>
                            
                            <!-- 联系人信息 -->
                            <div class="bg-gray-50 rounded-lg p-4">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">联系人信息</h4>
                                <div class="text-sm text-gray-700 space-y-2">
                                    <p><span class="font-medium">联系人：</span>张教授</p>
                                    <p><span class="font-medium">电话：</span>0574-87654321</p>
                                    <p><span class="font-medium">邮箱：</span><EMAIL></p>
                                    <p><span class="font-medium">地址：</span>宁波市江北区风华路818号</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6 border-t border-gray-200 flex justify-end space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        下载简介PDF
                    </button>
                    <button onclick="closeDetailModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        返回列表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览区小图表
            const initMiniChart = (id, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['2021', '2022', '2023'],
                        datasets: [{
                            data: [12, 18, 24],
                            borderColor: color,
                            borderWidth: 2,
                            tension: 0.4,
                            pointRadius: 0,
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { display: false }, y: { display: false } }
                    }
                });
            };

            initMiniChart('labChart', '#3B82F6');
            initMiniChart('centerChart', '#10B981');
            initMiniChart('engChart', '#F59E0B');
            initMiniChart('techChart', '#6366F1');

            // 建设级别分布图表
            const levelCtx = document.getElementById('levelChart').getContext('2d');
            new Chart(levelCtx, {
                type: 'bar',
                data: {
                    labels: ['国家级', '省级', '市级'],
                    datasets: [{
                        label: '数量',
                        data: [5, 12, 7],
                        backgroundColor: [
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: { y: { beginAtZero: true } }
                }
            });

            // 研究领域对比图表
            const fieldCtx = document.getElementById('fieldCompareChart').getContext('2d');
            new Chart(fieldCtx, {
                type: 'radar',
                data: {
                    labels: ['材料科学', '生物医药', '信息技术', '环境科学', '能源技术'],
                    datasets: [
                        {
                            label: '平台数量',
                            data: [8, 6, 5, 4, 3],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: '#3B82F6',
                            pointBackgroundColor: '#3B82F6',
                            pointBorderColor: '#fff'
                        },
                        {
                            label: '项目承载',
                            data: [12, 8, 7, 5, 4],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: '#10B981',
                            pointBackgroundColor: '#10B981',
                            pointBorderColor: '#fff'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { position: 'bottom' } }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>