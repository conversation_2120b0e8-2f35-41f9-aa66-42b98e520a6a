unit CcSjScdd;

interface

uses
  Classes;

type
  TCcSjScdd = class
  private
    FSjscddid: Integer;
    FDdid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;

    FCjs: Integer;
    FSl1: Integer;
    FSl2: Integer;
    FSl3: Integer;
    FSl4: Integer;
    FSl5: Integer;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Sjscddid: Integer read FSjscddid write FSjscddid;
    property Ddid: Integer read FDdid write FDdid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;

    property Cjs: Integer read FCjs write FCjs;
    property Sl1: Integer read FSl1 write FSl1;
    property Sl2: Integer read FSl2 write FSl2;
    property Sl3: Integer read FSl3 write FSl3;
    property Sl4: Integer read FSl4 write FSl4;
    property Sl5: Integer read FSl5 write FSl5;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
