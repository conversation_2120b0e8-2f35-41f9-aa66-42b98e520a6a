<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家获奖情况管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">专家获奖情况管理</h1>
            <p class="text-gray-600">为各类科技专家建立系统化的获奖记录与管理平台，全面支持专家与奖项的高效关联、灵活维护与多维分析</p>
        </div>

        <div class="space-y-6">
            <!-- 条件筛选区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    条件筛选
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">专家姓名</label>
                        <input type="text" placeholder="请输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">所属单位</label>
                        <input type="text" placeholder="请输入所属单位" class="w-full px-3 py-2 border border关联-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">奖励类型</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部类型</option>
                            <option value="scientific">科学技术奖</option>
                            <option value="invention">发明奖</option>
                            <option value="progress">科技进步奖</option>
                            <option value="patent">专利奖</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">奖励级别</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部级别</option>
                            <option value="national">国家级</option>
                            <option value="provincial">省级</option>
                            <option value="municipal">市级</option>
                            <option value="district">区级</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">获奖年度</label>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部年度</option>
                            <option value="2024">2024年</option>
                            <option value="2023">2023年</option>
                            <option value="2022">2022年</option>
                            <option value="2021">2021年</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">奖项名称</label>
                        <input type="text" placeholder="请输入奖项名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="flex space-x-3 mt-4">
                    <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        查询
                    </button>
                    <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        重置
                    </button>
                </div>
            </div>

            <!-- 获奖情况列表区 -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            专家获奖情况列表
                        </h2>
                        <div class="flex space-x-2">
                            <div class="relative" id="exportDropdown">
                                <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                    </svg>
                                    导出
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>
                                <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                    <div class="py-1">
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                        <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为PDF</a>
                                    </div>
                                </div>
                            </div>
                            <button onclick="openAwardModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                关联奖项
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专家姓名</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖项目</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励级别</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年度</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">张伟</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市智能科技研究院</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">智能港口自动化系统</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">科技进步奖</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">省级</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewAwardDetail('award1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editAward('award1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="removeAward('award1')" class="text-red-600 hover:text-red-900">移除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">王芳</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波大学</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">新型纳米材料研发</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">发明奖</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">国家级</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewAwardDetail('award2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editAward('award2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="removeAward('award2')" class="text-red-600 hover:text-red-900">移除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">李强</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">宁波市新材料科技有限公司</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">高性能复合材料制备技术</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">专利奖</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">市级</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewAwardDetail('award3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                        <button onclick="editAward('award3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                        <button onclick="removeAward('award3')" class="text-red-600 hover:text-red-900">移除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">28</span> 条记录
                        </div>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                            <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                            <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联奖项弹窗 -->
    <div id="awardModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">关联奖项</h3>
                    <button onclick="closeAwardModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 搜索区 -->
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="flex items-center space-x-2">
                                <input type="text" placeholder="搜索奖项名称、编号或关键词" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    搜索
                                </button>
                            </div>
                        </div>

                        <!-- 奖项列表 -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h4 class="text-sm font-medium text-gray-700">选择要关联的奖项</h4>
                                <span class="text-xs text-gray-500">已选择 <span class="font-medium">0</span> 项</span>
                            </div>
                            <div class="space-y-2">
                                <div class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">宁波市科技进步一等奖</div>
                                        <div class="text-xs text-gray-500">编号: NBKJ2023001 | 2023年 | 宁波市科技局</div>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">浙江省发明奖二等奖</div>
                                        <div class="text-xs text-gray-500">编号: ZJFM2022002 | 2022年 | 浙江省科技厅</div>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded">
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">国家科学技术进步奖</div>
                                        <div class="text-xs text-gray-500">编号: GJKJ2021003 | 2021年 | 科技部</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 关联信息 -->
                        <div class="border-t border-gray-200 pt-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-3">关联信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">专家姓名</label>
                                    <input type="text" placeholder="请输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">主要贡献</label>
                                    <input type="text" placeholder="请输入专家主要贡献" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">附件上传</label>
                                    <div class="border-2 border-dashed border-gray-300 rounded-md p-4 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                        </svg>
                                        <div class="mt-2">
                                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                                <span>点击上传文件</span>
                                                <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                            </label>
                                            <p class="text-xs text-gray-500">支持PDF、JPG、PNG格式，最大10MB</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeAwardModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        确认关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 奖项详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">奖项详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <!-- 基本信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">基本信息</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-gray-500">奖项名称</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">宁波市科技进步一等奖</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">奖项编号</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">NBKJ2023001</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">奖励类型</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">科技进步奖</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">奖励级别</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">市级</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">获奖年度</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">2023年</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">颁奖单位</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">宁波市科技局</p>
                                </div>
                            </div>
                        </div>

                        <!-- 项目信息 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">项目信息</h4>
                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-500">项目名称</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">智能港口自动化系统</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">项目简介</p>
                                    <p class="text-sm text-gray-900 mt-1">本项目针对宁波港口的实际需求，研发了一套智能自动化系统，实现了港口作业的智能化、高效化和安全化...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 专家贡献 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">专家贡献</h4>
                            <div class="space-y-2">
                                <div>
                                    <p class="text-xs text-gray-500">专家姓名</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">张伟</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">所属单位</p>
                                    <p class="text-sm font-medium text-gray-900 mt-1">宁波市智能科技研究院</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-500">主要贡献</p>
                                    <p class="text-sm text-gray-900 mt-1">负责系统架构设计和核心算法研发，提出了基于深度学习的智能调度算法，显著提升了系统性能...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 附件 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-3">相关附件</h4>
                            <div class="flex flex-wrap gap-3">
                                <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                    <div class="ml-2">
                                        <p class="text-xs font-medium text-gray-900">获奖证书.pdf</p>
                                        <p class="text-xs text-gray-500">2.4MB</p>
                                    </div>
                                </div>
                                <div class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                    <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <div class="ml-2">
                                        <p class="text-xs font-medium text-gray-900">颁奖照片.jpg</p>
                                        <p class="text-xs text-gray-500">1.8MB</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }
        
        // 关联奖项弹窗
        function openAwardModal() {
            document.getElementById('awardModal').classList.remove('hidden');
        }
        
        function closeAwardModal() {
            document.getElementById('awardModal').classList.add('hidden');
        }
        
        // 奖项详情弹窗
        function viewAwardDetail(awardId) {
            document.getElementById('detailModal').classList.remove('hidden');
        }
        
        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }
        
        // 编辑奖项
        function editAward(awardId) {
            openAwardModal();
            console.log('编辑奖项:', awardId);
        }
        
        // 移除奖项
        function removeAward(awardId) {
            if (confirm('确定要移除该奖项关联吗？此操作不可恢复！')) {
                console.log('移除奖项:', awardId);
            }
        }
        
        // 点击模态框外部关闭
        document.getElementById('awardModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAwardModal();
            }
        });
        
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });
        
        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>