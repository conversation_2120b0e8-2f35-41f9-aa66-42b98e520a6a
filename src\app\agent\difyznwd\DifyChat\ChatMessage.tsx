'use client';

import React, { useState, useRef, useLayoutEffect, useEffect, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Message, MessageFile } from './types';
import { MessageContent } from './MessageContent';
import { WorkflowSteps } from './WorkflowSteps';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { User, Bot, AlertCircle, Loader2, Copy, ThumbsUp, ThumbsDown, RotateCcw, MessageSquare, BookOpen, Image, FileText, ChevronDown, ChevronRight, ChevronLeft, FileDown, Brain, Download, ExternalLink, File, X, ChevronUp, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

interface ChatMessageProps {
  message: Message;
  isLatest?: boolean;
  isTyping?: boolean;
  onRetry?: () => void;
  onRating?: (messageId: string, rating: 'up' | 'down') => void;
  onAddNote?: (messageId: string) => void;
  config?: { baseURL?: string };
  showWorkflow?: boolean;
}

// 工具函数：解析<think>标签
function parseThinkBlock(content: string): { think: string | null, result: string } {
  const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/i);
  if (thinkMatch) {
    const think = thinkMatch[1].trim();
    // 移除<think>块后剩余内容为最终结果
    const result = content.replace(thinkMatch[0], '').trim();
    return { think, result };
  }
  return { think: null, result: content };
}

// 文件类型图标映射
const getFileIcon = (type: string) => {
  const fileType = type.toLowerCase();
  if (fileType.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(fileType)) 
    return <Image className="h-4 w-4" />;
  if (fileType.includes('pdf') || fileType === 'pdf') 
    return <FileText className="h-4 w-4" />;
  if (['doc', 'docx', 'document'].includes(fileType)) 
    return <FileText className="h-4 w-4" />;
  if (['xls', 'xlsx', 'sheet', 'excel'].includes(fileType)) 
    return <FileText className="h-4 w-4" />;
  return <File className="h-4 w-4" />;
};

// 格式化文件大小
const formatFileSize = (size?: number) => {
  if (!size) return '';
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
  return `${(size / (1024 * 1024)).toFixed(1)} MB`;
};

export function ChatMessage({ 
  message: originalMessage,
  isLatest,
  isTyping = false,
  onRetry,
  onRating,
  onAddNote,
  config,
  showWorkflow = true
}: ChatMessageProps) {
  // Data-driven approach to control workflow visibility
  // This is safer than conditionally rendering complex JSX.
  const message = useMemo(() => {
    if (showWorkflow) {
      return originalMessage;
    }
    // If workflow is hidden, return a new message object without workflow data.
    // The rest of the component's rendering logic will naturally hide the
    // workflow sections because the data will be missing.
    const { workflowExecution, workflowNodes, ...rest } = originalMessage;
    return rest as Message; // Cast back to Message type
  }, [originalMessage, showWorkflow]);

  const [copied, setCopied] = useState(false);
  const [rating, setRating] = useState<'up' | 'down' | null>(null);
  const [referencesOpen, setReferencesOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [activeDoc, setActiveDoc] = useState<string | null>(null);
  const [showThought, setShowThought] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const cardRef = useRef<HTMLDivElement>(null);
  const messageRef = useRef<HTMLDivElement>(null);
  const [cardPosition, setCardPosition] = useState({ left: 0, top: 0 });
  const [showAllResources, setShowAllResources] = useState(false);
  const [previewingFile, setPreviewingFile] = useState<string | null>(null);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewLoading, setPreviewLoading] = useState(false);

  const isUser = message.role === 'user';
  const isError = message.status === 'error';
  const isSending = message.status === 'sending';

  // 思考过程与最终结果分区（自动解析<think>标签）
  const { think, result } = parseThinkBlock(message.content || '');
  const hasThought = !!think;

  // 实时更新时间，用于动态显示思考时间
  useEffect(() => {
    // 只在思考阶段且没有确定的思考时间时启动定时器
    if (hasThought && !message.metadata?.thinking_time && message.status === 'sending') {
      const timer = setInterval(() => {
        setCurrentTime(Date.now());
      }, 100); // 每100ms更新一次
      
      return () => clearInterval(timer);
    }
  }, [hasThought, message.metadata?.thinking_time, message.status]);

  // 复制消息内容
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 处理评分
  const handleRating = (newRating: 'up' | 'down') => {
    setRating(newRating);
    onRating?.(message.id, newRating);
  };

  // 格式化时间
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handlePrev = (total: number) => setPage((p) => (p - 1 + total) % total);
  const handleNext = (total: number) => setPage((p) => (p + 1) % total);

  // 处理文件下载
  const handleFileDownload = async (url: string, filename: string) => {
      // 构建正确的文件下载URL
      let downloadUrl = url;
      
      // 如果URL是相对路径，添加正确的Dify服务器基础路径
      if (url.startsWith('/')) {
        const difyBaseURL = config?.baseURL || 'http://10.26.37.118:8081';
        // 移除/v1后缀，因为文件URL通常不包含API版本
        const baseURL = difyBaseURL.replace('/v1', '');
        downloadUrl = `${baseURL}${url}`;
      }
      
    try {
      // 使用fetch下载文件并创建blob
        const response = await fetch(downloadUrl, {
          method: 'GET',
          mode: 'cors',
        });
        
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status}`);
        }
        
        const blob = await response.blob();
        
        // 创建临时URL并触发下载
        const blobUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = filename || 'download';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 清理临时URL
        window.URL.revokeObjectURL(blobUrl);
        
      } catch (fetchError) {
        console.warn('Fetch下载失败，尝试直接链接下载:', fetchError);
        
        // 降级方案：直接使用链接下载（适用于同源文件）
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = filename || 'download';
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
        
        // 添加下载属性强制下载
        link.setAttribute('download', filename || 'download');
        
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // 计算思考耗时（从metadata中获取）
  const getThinkingTime = () => {
    // 优先使用已保存的纯思考时间
    if (message.metadata?.thinking_time) {
      return message.metadata.thinking_time.toFixed(1);
    }
    
    // 如果正在思考阶段（有思考内容但没有thinking_time），显示估算时间
    if (hasThought && !message.metadata?.thinking_time && message.status === 'sending') {
      // 使用currentTime状态来计算实时的思考时间
      const estimatedTime = (currentTime - message.timestamp) / 1000;
      // 限制显示的最大估算时间为合理范围
      return Math.min(estimatedTime, 30).toFixed(1);
    }
    
    // 备用方案：使用总的elapsed_time
    if (message.metadata?.elapsed_time) {
      return (message.metadata.elapsed_time / 1000).toFixed(1);
    }
    
    // 再备用方案：使用usage中的latency
    const usage = message.metadata?.usage as any;
    if (typeof usage?.latency === 'number') {
      return usage.latency.toFixed(1);
    }
    
    return '0.0';
  };

  // 处理Word文档预览
  const handleFilePreview = async (url: string, filename: string) => {
    // 扩展支持的文档类型
    if (!filename.toLowerCase().match(/\.(doc|docx|pdf|md|txt)$/)) {
      return; // 支持Word、PDF、Markdown和文本文档
    }
    
    setPreviewLoading(true);
    setPreviewingFile(filename);
    
    try {
      // 对于Word文档，先尝试直接在iframe中加载，就像浏览器中打开一样
      if (filename.toLowerCase().match(/\.(doc|docx)$/)) {
        // 构建完整的文件URL
        let directUrl = url;
        if (url.startsWith('/')) {
          const difyBaseURL = config?.baseURL || 'http://10.26.37.118:8081';
          const baseURL = difyBaseURL.replace(/\/v1$/, '').replace(/\/api.*$/, '');
          directUrl = `${baseURL}${url}`;
        }
        
        console.log('直接预览Word文档URL:', directUrl);
        
        // 设置为直接URL模式，让iframe加载
        setPreviewContent(`__DIRECT_URL__${directUrl}`);
        setPreviewLoading(false);
        return;
      }
      
      // 对于其他类型文档，使用API代理
      let previewUrl = `/api/dify/preview-file?url=${encodeURIComponent(url)}`;
      
      // 获取文件内容
      const response = await fetch(previewUrl, {
        method: 'GET',
        headers: {
          'Accept': 'text/plain, text/markdown, text/html, application/json',
          'Cache-Control': 'no-cache',
        },
      });
      
      if (!response.ok) {
        if (response.status === 403) {
          throw new Error('文件访问被拒绝，可能需要特殊权限或文件已过期');
        } else if (response.status === 404) {
          throw new Error('文件不存在或已被删除');
        } else {
          throw new Error(`预览失败: HTTP ${response.status}`);
        }
      }
      
      // 检查Content-Type
      const contentType = response.headers.get('content-type') || '';
      console.log('文件Content-Type:', contentType);
      
      // 获取内容
      const content = await response.text();
      
      if (!content || content.trim().length === 0) {
        throw new Error('文件内容为空或无法读取');
      }
      
      // 检查是否为Word文档的友好错误信息
      if (content.includes('📄 Word文档预览') || content.includes('Word文档格式(.docx)')) {
        // 显示Word文档的特殊说明
        setPreviewContent(content);
      } else if (content.includes('❌ 预览失败')) {
        // 显示其他预览失败信息
        setPreviewContent(content);
      } else {
        // 正常内容处理
        let processedContent = content;
        if (filename.toLowerCase().endsWith('.md')) {
          // Markdown文档，保持原格式用于渲染
          processedContent = content;
        } else {
          // 纯文本文档
          processedContent = content;
        }
        
        setPreviewContent(processedContent);
      }
    } catch (error) {
      console.error('文档预览失败:', error);
      
      // 提供更友好的错误信息和解决方案
      let errorMessage = `❌ 预览失败: ${error instanceof Error ? error.message : '未知错误'}`;
      
      if (error instanceof Error && error.message.includes('403')) {
        errorMessage += '\n\n💡 解决方案:\n• 文件可能需要特殊权限访问\n• 请尝试直接下载文件查看完整内容\n• 或联系管理员检查文件访问权限';
      } else if (filename.toLowerCase().includes('doc')) {
        errorMessage += '\n\n💡 解决方案:\n• Word文档可能为原始二进制格式\n• 建议点击下载按钮保存到本地\n• 使用Microsoft Word或兼容软件打开\n• 或者将文档转换为PDF格式后重新上传';
      } else if (error instanceof Error && error.message.includes('Failed to fetch')) {
        errorMessage += '\n\n💡 解决方案:\n• 网络连接问题或服务器暂时不可用\n• 请检查网络连接后重试\n• 可以尝试下载文件到本地查看';
      }
      
      errorMessage += `\n\n📄 文件信息:\n• 文件名: ${filename}\n• 文件类型: ${filename.split('.').pop()?.toUpperCase()}\n• 访问URL: ${url}`;
      
      setPreviewContent(errorMessage);
    } finally {
      setPreviewLoading(false);
    }
  };

  return (
    <>
    <div 
      className={cn(
        'group mb-6 flex gap-3 max-w-full',
      isUser ? 'flex-row-reverse' : 'flex-row'
      )}
      ref={messageRef}
    >
      {/* 头像 */}
      <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
        <AvatarFallback className={cn(
          'text-sm font-medium border',
          isUser
            ? 'bg-blue-500 text-white border-blue-600'
            : 'bg-gray-100 text-gray-600 border-gray-200'
        )}>
          {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
        </AvatarFallback>
      </Avatar>

      {/* 消息内容容器 */}
      <div className={cn(
        "flex-1 min-w-0 max-w-[85%] relative",
        isUser && "flex flex-col items-end" // 用户消息靠右对齐
      )}>
      {/* 消息内容 - 只有在有内容时才显示容器 */}
      {(message.content || hasThought || isSending || isError || (message.files && message.files.length > 0)) && (
        <div className={cn(
          "rounded-lg p-4",
          "transition-all duration-200 ease-in-out",
          "word-wrap message-content",
          isUser 
            ? "bg-primary text-primary-foreground" 
            : "bg-muted"
        )}>
            {/* 状态指示器 - 只在发送中时显示 */}
            {isSending && (
              <div className="mb-2 flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>正在思考...</span>
              </div>
            )}

            {isError && (
              <div className="mb-2 flex items-center gap-2 text-sm text-destructive">
                <AlertCircle className="h-3 w-3" />
                <span>发送失败</span>
              </div>
            )}

            {/* 思考过程（收起时只显示蓝色标签，展开后显示内容） */}
            {hasThought && (
              <Collapsible 
                open={showThought} 
                onOpenChange={(open) => {
                  setShowThought(open);
                  // 当思考区域折叠/展开时，延迟触发滚动调整
                  setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('chatContentHeightChanged'));
                  }, 200); // 等待动画完成
                }}
              >
                <div className="mb-3">
                  <CollapsibleTrigger asChild>
                    <button className="inline-flex items-center gap-1.5 text-xs bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1 rounded-full shadow-sm hover:shadow-md transition-all font-medium">
                      <Brain className="h-3.5 w-3.5" />
                      <span>已深度思考 ({getThinkingTime()}s)</span>
                      <ChevronDown className={cn("h-3.5 w-3.5 transition-transform", showThought && "rotate-180")} />
                    </button>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <div className="bg-white border border-blue-200 rounded-lg p-3 mt-2 text-sm text-gray-900 max-h-72 overflow-y-auto shadow-sm">
                      <div className="whitespace-pre-wrap leading-relaxed">{think}</div>
                      
                      {/* 工作流步骤显示 - 在思考区域内部显示 */}
                      {showWorkflow && (message.workflowExecution || (message.workflowNodes && message.workflowNodes.length > 0)) && (
                        <div className="mt-4 pt-3 border-t border-blue-100">
                          <WorkflowSteps 
                            workflowExecution={message.workflowExecution}
                            workflowNodes={message.workflowNodes}
                            className="bg-blue-50/50 border-blue-200"
                          />
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            )}

            {/* 如果没有思考过程但有工作流，单独显示工作流 */}
            {!hasThought && (message.workflowExecution || message.workflowNodes) && (
              <div className="mb-3">
                <div className="bg-white border border-blue-200 rounded-lg p-3 shadow-sm">
                  <WorkflowSteps 
                    workflowExecution={message.workflowExecution}
                    workflowNodes={message.workflowNodes}
                    className="bg-blue-50/50 border-blue-200"
                  />
                </div>
              </div>
            )}

            {/* 最终结果（白色卡片，常规字体） */}
            {result && (
              <div className={cn(isUser && 'text-primary-foreground', 'break-words overflow-wrap-anywhere bg-white rounded-lg p-3 shadow-sm text-gray-900')}> 
                <MessageContent 
                  content={result} 
                  isUser={isUser}
                  retrieverResources={message.retrieverResources}
                  hasFiles={message.files && message.files.length > 0} 
                  metadata={message.metadata}
                />
              </div>
            )}
            
            {/* 显示消息关联的文件 */}
            {message.files && message.files.length > 0 && (
              <div className="mt-3 space-y-2">
                <div className="text-xs text-blue-600 font-medium mb-2">📎 附件文件</div>
                <div className="flex flex-wrap gap-2">
                  {message.files.map((file) => {
                    // 获取文件名，优先使用filename属性
                    const filename = (file as any).filename || (file as any).name || file.id.split('-')[0];
                    // 获取文件类型，兼容多种格式
                    const fileType = file.type || (file as any).mime_type?.split('/')[1] || 'document';
                    // 获取文件大小
                    const fileSize = (file as any).size;
                    
                    return (
                      <div 
                        key={file.id}
                        className="flex items-center gap-2 bg-white border border-blue-200 rounded-lg p-2 pr-3 shadow-sm hover:shadow-md transition-all cursor-pointer hover:border-blue-300 hover:bg-blue-50/30"
                        onClick={() => handleFileDownload(file.url, filename)}
                        title={`点击下载 ${filename}`}
                      >
                        <div className="w-8 h-8 bg-blue-50 rounded flex items-center justify-center text-blue-600 border border-blue-200">
                          {getFileIcon(fileType)}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="text-sm font-medium truncate max-w-[150px] text-gray-900">
                            {filename}
                          </div>
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <span className="uppercase">{fileType}</span>
                            {fileSize && <span>· {formatFileSize(fileSize)}</span>}
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          {/* Word文档预览按钮 */}
                          {filename.toLowerCase().includes('doc') && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleFilePreview(file.url, filename);
                              }}
                              className="p-1 text-blue-500 hover:text-blue-600 hover:bg-blue-100 rounded transition-colors"
                              title="预览文档"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                          )}
                          <Download className="h-4 w-4 text-blue-500 flex-shrink-0" />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
        </div>
      )}

        {/* 引用资料和文件 - 悬浮卡片显示在按钮上方 */}
        {message.role === "assistant" && message.retrieverResources && message.retrieverResources.length > 0 && (() => {
          // 按文档名称分组
          const resourcesByDocument = message.retrieverResources.reduce((groups: Record<string, any[]>, resource) => {
            const docName = resource.documentName || resource.document_name || '未知文档';
            if (!groups[docName]) groups[docName] = [];
            groups[docName].push(resource);
            return groups;
          }, {});
          const docNames = Object.keys(resourcesByDocument);

          // 确保有分组结果才渲染
          if (docNames.length === 0) {
            return null;
          }

          const handleOpenDoc = (doc: string) => {
            if (activeDoc === doc) {
              setActiveDoc(null);
            } else {
              setActiveDoc(doc);
              setPage(0);
            }
          };

          const handlePrev = (total: number) => setPage((p) => (p - 1 + total) % total);
          const handleNext = (total: number) => setPage((p) => (p + 1) % total);

          // 控制显示的文档数量
          const maxVisible = 3;
          const visibleDocs = showAllResources ? docNames : docNames.slice(0, maxVisible);
          const hiddenCount = Math.max(0, docNames.length - maxVisible);

          return (
            <div className="mt-3">
              <div className="text-xs text-blue-600 font-medium mb-2">📚 参考资料</div>
              {/* 文件卡片列表 - 移除滚动，改为限制显示数量 */}
              <div className="relative">
                <div className="flex flex-wrap gap-2">
                  {visibleDocs.map((doc, idx) => {
                    return (
                    <div key={doc} className="relative inline-block overflow-visible">
                      <button
                        className={cn(
                          'inline-flex items-center gap-1 px-3 py-1.5 rounded-lg border border-blue-200 bg-blue-50/80 text-sm font-medium transition-all whitespace-nowrap hover:bg-blue-100 hover:border-blue-300 hover:shadow-sm',
                          activeDoc === doc ? 'border-blue-500 bg-blue-100 text-blue-700 shadow-sm' : 'text-blue-600'
                        )}
                        onClick={() => handleOpenDoc(doc)}
                        title={`点击查看${doc}的引用内容`}
                      >
                        <FileText className="h-3.5 w-3.5 text-blue-500" />
                        <span className="truncate max-w-[120px]">{doc}</span>
                        <span className="ml-1 text-xs text-blue-500 bg-blue-200 px-1.5 py-0.5 rounded-full">
                          {resourcesByDocument[doc].length}
                        </span>
                      </button>
                      
                      {/* 悬浮卡片 */}
                      {activeDoc === doc && resourcesByDocument[doc].length > 0 && (
                        <>
                          {/* 背景遮罩 */}
                          <div
                            className="fixed inset-0 bg-black bg-opacity-25 z-40"
                            onClick={() => setActiveDoc(null)}
                            style={{ zIndex: 9998 }}
                          />
                          
                          {/* 悬浮卡片 */}
                          <div
                            ref={cardRef}
                            className="fixed bg-white border border-border rounded-lg shadow-xl overflow-hidden flex flex-col"
                            style={{
                              left: '50%',
                              top: '50%',
                              transform: 'translate(-50%, -50%)',
                              minWidth: '350px',
                              maxWidth: 'min(600px, 90vw)',
                              width: '90vw',
                              zIndex: 9999,
                              height: '80vh',
                              maxHeight: '80vh'
                            }}
                          >
                            {/* 关闭按钮 */}
                            <button
                              onClick={() => setActiveDoc(null)}
                              className="absolute top-2 right-2 p-1 rounded-full hover:bg-gray-100 transition-colors z-10"
                              title="关闭"
                            >
                              <X className="h-4 w-4" />
                            </button>
                            
                            {/* 头部区域 - 固定不滚动 */}
                            <div className="flex-shrink-0 p-4 pb-0">
                              {/* 页码指示器 */}
                              <div className="flex items-center justify-between mb-3 pb-2 border-b border-border pr-8">
                                <div className="text-sm font-medium text-gray-900">{doc}</div>
                                <div className="text-xs text-muted-foreground">
                                  {page + 1} / {resourcesByDocument[doc].length}
                                </div>
                              </div>
                              
                              {/* 分页控制 */}
                              {resourcesByDocument[doc].length > 1 && (
                                <div className="flex items-center justify-center gap-2 mb-3">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handlePrev(resourcesByDocument[doc].length)}
                                    disabled={page === 0}
                                    className="h-7 px-2"
                                  >
                                    <ChevronLeft className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleNext(resourcesByDocument[doc].length)}
                                    disabled={page === resourcesByDocument[doc].length - 1}
                                    className="h-7 px-2"
                                  >
                                    <ChevronRight className="h-3 w-3" />
                                  </Button>
                                </div>
                              )}
                            </div>

                            {/* 内容区域 - 可滚动 */}
                            <div className="flex-1 overflow-y-auto px-4">
                              <div className="text-sm leading-relaxed text-gray-800 whitespace-pre-wrap break-words">
                                {resourcesByDocument[doc][page]?.content || '暂无内容'}
                              </div>
                            </div>

                            {/* 底部区域 - 固定不滚动 */}
                            <div className="flex-shrink-0 px-4 py-3 bg-gray-50 border-t border-border">
                              <div className="text-xs text-muted-foreground space-y-1">
                                <div className="flex items-center justify-between">
                                  <span>评分: {resourcesByDocument[doc][page]?.score?.toFixed(3) || 'N/A'}</span>
                                  <span>字数: {resourcesByDocument[doc][page]?.word_count || 0}</span>
                                </div>
                                <div>来源: {resourcesByDocument[doc][page]?.dataset_name || '未知数据集'}</div>
                              </div>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                    );
                  })}
                  
                  {/* "查看更多"按钮 */}
                  {hiddenCount > 0 && (
                    <button
                      className={cn(
                        'inline-flex items-center gap-1 px-3 py-1.5 rounded-lg border border-dashed border-blue-300 bg-blue-50/60 text-sm font-medium transition-all hover:bg-blue-100 hover:border-blue-400 text-blue-600',
                        showAllResources && 'bg-blue-100 border-blue-400'
                      )}
                      onClick={() => setShowAllResources(!showAllResources)}
                      title={showAllResources ? '收起更多资料' : `查看更多 ${hiddenCount} 个资料`}
                    >
                      <FileText className="h-3.5 w-3.5 text-blue-500" />
                      {showAllResources ? (
                        <>
                          <span>收起</span>
                          <ChevronUp className="h-3.5 w-3.5" />
                        </>
                      ) : (
                        <>
                          <span>+{hiddenCount}</span>
                          <ChevronDown className="h-3.5 w-3.5" />
                        </>
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          );
        })()}

        {/* 消息元信息和操作 */}
        <div className={cn(
          'flex items-center gap-2 text-xs text-muted-foreground mt-1 px-1',
          isUser ? 'justify-start' : 'justify-between' // 改为justify-between以便左右分布
        )}>
          <div className="flex items-center gap-2">
            <span>{formatTime(message.timestamp)}</span>
            
            {/* 消息操作按钮 - 仅对AI消息显示 */}
            {!isUser && message.content && !isSending && (
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {/* 复制按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={handleCopy}
                  title="复制消息"
                >
                  <Copy className="h-3 w-3" />
                </Button>

                {/* 点赞按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-6 w-6 p-0",
                    rating === 'up' && "text-green-600 bg-green-50"
                  )}
                  onClick={() => handleRating('up')}
                  title="点赞"
                >
                  <ThumbsUp className="h-3 w-3" />
                </Button>

                {/* 点踩按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-6 w-6 p-0",
                    rating === 'down' && "text-red-600 bg-red-50"
                  )}
                  onClick={() => handleRating('down')}
                  title="点踩"
                >
                  <ThumbsDown className="h-3 w-3" />
                </Button>

                {/* 重新生成按钮 */}
                {onRetry && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={onRetry}
                    title="重新生成"
                  >
                    <RotateCcw className="h-3 w-3" />
                  </Button>
                )}

                {/* 添加备注按钮 */}
                {onAddNote && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0"
                    onClick={() => onAddNote(message.id)}
                    title="添加备注"
                  >
                    <MessageSquare className="h-3 w-3" />
                  </Button>
                )}
              </div>
            )}

            {/* 复制成功提示 */}
            {copied && (
              <span className="text-green-600 text-xs">已复制</span>
            )}
          </div>

          {/* Token使用信息 - 右侧显示，始终可见，仅AI回复显示 */}
          {!isUser && (
            <div className="flex items-center gap-2 flex-shrink-0">
              {message.metadata?.usage ? (
                <>
                  {message.metadata.usage.total_tokens !== undefined && (
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 font-normal text-xs h-5 px-2">
                      Tokens: {message.metadata.usage.total_tokens}
                    </Badge>
                  )}
                  {/* {message.metadata.usage.total_price !== undefined && (
                    <Badge variant="outline" className="bg-green-50 text-green-700 font-normal text-xs h-5 px-2">
                      费用: {message.metadata.usage.total_price} {message.metadata.usage.currency || 'USD'}
                    </Badge>
                  )} */}
                  {(() => {
                    const usage = message.metadata?.usage as any;
                    const latency = usage?.latency;
                    const elapsed = message.metadata?.elapsed_time;
                    
                    if (typeof latency === 'number') {
                      return (
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 font-normal text-xs h-5 px-2">
                          耗时: {latency.toFixed(2)}s
                        </Badge>
                      );
                    } else if (typeof elapsed === 'number') {
                      return (
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 font-normal text-xs h-5 px-2">
                          耗时: {(elapsed / 1000).toFixed(2)}s
                        </Badge>
                      );
                    }
                    return null;
                  })()}
                </>
              ) : (
                // 历史会话可能没有token信息
                message.messageId && (
                  <Badge variant="outline" className="bg-gray-50 text-gray-500 font-normal text-xs h-5 px-2" title="历史会话的Token使用信息暂不可用">
                    历史会话
                  </Badge>
                )
              )}
            </div>
          )}
        </div>
      </div>
    </div>
    
    {/* Word文档预览弹窗 - 使用Portal渲染到body */}
    {previewingFile && typeof window !== 'undefined' && createPortal(
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
        style={{ zIndex: 99999 }}
        onClick={() => setPreviewingFile(null)}
      >
        <div 
          className="bg-white rounded-lg shadow-2xl flex flex-col border max-w-full max-h-full"
          style={{ 
            width: '90vw',
            height: '90vh',
            maxWidth: '1400px',
            minWidth: '800px'
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* 顶部工具栏 */}
          <div className="bg-white border-b shadow-sm flex items-center justify-between px-6 py-3 flex-shrink-0">
            <div className="flex items-center gap-4">
              <h3 className="text-lg font-semibold text-gray-900">{previewingFile}</h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    if (previewContent.startsWith('__DIRECT_URL__')) {
                      const directUrl = previewContent.replace('__DIRECT_URL__', '');
                      window.open(directUrl, '_blank');
                    }
                  }}
                  className="px-3 py-1.5 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors flex items-center gap-1"
                  disabled={!previewContent.startsWith('__DIRECT_URL__')}
                  title="在新窗口打开"
                >
                  <ExternalLink className="h-4 w-4" />
                  新窗口打开
                </button>
                <button
                  onClick={() => {
                    if (previewContent.startsWith('__DIRECT_URL__')) {
                      const directUrl = previewContent.replace('__DIRECT_URL__', '');
                      const link = document.createElement('a');
                      link.href = directUrl;
                      link.download = previewingFile || 'document.docx';
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                    }
                  }}
                  className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors flex items-center gap-1"
                  disabled={!previewContent.startsWith('__DIRECT_URL__')}
                  title="下载文档"
                >
                  <Download className="h-4 w-4" />
                  下载文档
                </button>
              </div>
            </div>
            <button 
              onClick={() => setPreviewingFile(null)}
              className="text-gray-500 hover:text-gray-700 transition-colors p-2 rounded-full hover:bg-gray-100"
              title="关闭预览"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          {/* 主要预览区域 - 占满剩余空间 */}
          <div className="flex-1 bg-white overflow-hidden rounded-b-lg">
            {previewLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center gap-3">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                  <span className="text-gray-600 text-lg">加载中...</span>
                </div>
              </div>
            ) : (
              <div className="h-full">
                {/* 检查是否为直接URL模式（Word文档） */}
                {previewContent.startsWith('__DIRECT_URL__') ? (
                  <iframe
                    src={previewContent.replace('__DIRECT_URL__', '')}
                    className="w-full h-full border-0 rounded-b-lg"
                    title="文档预览"
                    onLoad={() => {
                      console.log('Word文档iframe加载完成');
                    }}
                    onError={(e) => {
                      console.error('Word文档iframe加载失败:', e);
                      setPreviewContent(`❌ 直接预览失败

💡 当前状态:
• 尝试直接在iframe中加载Word文档
• 可能是浏览器安全策略或文档格式问题

🔧 解决方案:
• 点击顶部"新窗口打开"按钮查看文档
• 或者点击"下载文档"按钮保存到本地
• 使用Microsoft Word或兼容软件打开

📄 文件信息:
• 文件名: ${previewingFile}
• 文件类型: Microsoft Word文档 (.docx)
• 直接访问: ${previewContent.replace('__DIRECT_URL__', '')}`);
                    }}
                  />
                ) : previewContent.includes('❌ 预览失败') || previewContent.includes('📄 Word文档预览') ? (
                  <div className="h-full overflow-auto p-8 bg-gray-50 rounded-b-lg">
                    <div className="max-w-4xl mx-auto">
                      <pre className="whitespace-pre-wrap text-gray-700 font-sans leading-relaxed text-base">
                        {previewContent}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <div className="h-full overflow-auto">
                    {/* 检查是否为HTML内容 */}
                    {previewContent.includes('<html>') || previewContent.includes('<!DOCTYPE') || previewContent.includes('<body>') ? (
                      <iframe
                        srcDoc={previewContent}
                        className="w-full h-full border-0 rounded-b-lg"
                        sandbox="allow-same-origin"
                        title="文档预览"
                      />
                    ) : (
                      // 纯文本或Markdown内容
                      <div className="h-full overflow-auto p-8 bg-white rounded-b-lg">
                        <div className="max-w-4xl mx-auto">
                          {previewingFile?.toLowerCase().endsWith('.md') ? (
                            // 简单的Markdown渲染
                            <div className="markdown-content prose prose-lg max-w-none">
                              {previewContent.split('\n').map((line, index) => {
                                if (line.startsWith('# ')) {
                                  return <h1 key={index} className="text-3xl font-bold mt-8 mb-4">{line.substring(2)}</h1>;
                                } else if (line.startsWith('## ')) {
                                  return <h2 key={index} className="text-2xl font-bold mt-6 mb-3">{line.substring(3)}</h2>;
                                } else if (line.startsWith('### ')) {
                                  return <h3 key={index} className="text-xl font-bold mt-4 mb-2">{line.substring(4)}</h3>;
                                } else if (line.includes('**')) {
                                  return <p key={index} className="mb-3" dangerouslySetInnerHTML={{
                                    __html: line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                                  }} />;
                                } else if (line.trim() === '') {
                                  return <br key={index} />;
                                } else {
                                  return <p key={index} className="mb-3">{line}</p>;
                                }
                              })}
                            </div>
                          ) : (
                            // 纯文本内容
                            <pre className="whitespace-pre-wrap font-mono text-sm leading-relaxed text-gray-800">
                              {previewContent}
                            </pre>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>,
      document.body
    )}
  </>
  );
} 