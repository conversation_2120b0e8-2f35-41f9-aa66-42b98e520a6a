<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">诚信记录维护与标签关联流程图</text>

  <!-- 阶段一：记录提交与初始校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：记录提交与初始校验</text>
  
  <!-- 节点1: 用户提交 -->
  <g transform="translate(500, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">新增或批量导入</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(500, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">必填字段与日期格式</text>
  </g>

  <!-- 节点3: 生成初始档案 -->
  <g transform="translate(800, 250)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成初始档案</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标记为"待审核"</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 600 200 Q 600 225 600 250" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 700 285 Q 750 285 800 285" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：冲突检查与审核 -->
  <text x="700" y="380" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：冲突检查与审核</text>

  <!-- 节点4: 自动校验规则 -->
  <g transform="translate(300, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动校验规则</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">重复失信记录检查</text>
  </g>

  <!-- 节点5: 管理员确认 -->
  <g transform="translate(600, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员确认</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">冲突提示处理</text>
  </g>

  <!-- 节点6: 正式入库 -->
  <g transform="translate(900, 420)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">正式入库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录生效</text>
  </g>

  <!-- 连接线 初始档案 -> 自动校验 -->
  <path d="M 850 320 C 750 350, 450 380, 400 420" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 自动校验 -> 管理员确认 -->
  <path d="M 500 455 Q 550 455 600 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 管理员确认 -> 正式入库 -->
  <path d="M 800 455 Q 850 455 900 455" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：标签管理与日志记录 -->
  <text x="700" y="550" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：标签管理与日志记录</text>

  <!-- 节点7: 标签绑定调整 -->
  <g transform="translate(400, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标签绑定调整</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">详情页操作</text>
  </g>

  <!-- 节点8: 同步更新 -->
  <g transform="translate(700, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">同步更新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标签表反向引用</text>
  </g>

  <!-- 节点9: 操作日志 -->
  <g transform="translate(1000, 590)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录变更历史</text>
  </g>

  <!-- 连接线 入库 -> 标签绑定 -->
  <path d="M 950 490 C 850 520, 650 550, 500 590" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 标签绑定 -> 同步更新 -->
  <path d="M 600 625 Q 650 625 700 625" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 同步更新 -> 操作日志 -->
  <path d="M 900 625 Q 950 625 1000 625" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：版本控制与删除管理 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：版本控制与删除管理</text>

  <!-- 节点10: 版本控制 -->
  <g transform="translate(200, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">版本控制</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动生成版本号</text>
  </g>

  <!-- 节点11: 删除检查 -->
  <g transform="translate(500, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">删除检查</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">标签关联检查</text>
  </g>

  <!-- 节点12: 逻辑删除 -->
  <g transform="translate(800, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">逻辑删除</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">归档历史数据</text>
  </g>

  <!-- 节点13: 定时扫描 -->
  <g transform="translate(1100, 760)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时扫描</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">到期记录处理</text>
  </g>

  <!-- 连接线 操作日志 -> 版本控制 -->
  <path d="M 1050 660 C 950 690, 450 720, 300 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 版本控制 -> 删除检查 -->
  <path d="M 400 795 Q 450 795 500 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 删除检查 -> 逻辑删除 -->
  <path d="M 700 795 Q 750 795 800 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 逻辑删除 -> 定时扫描 -->
  <path d="M 1000 795 Q 1050 795 1100 795" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线：定时扫描 -> 标签绑定调整 -->
  <path d="M 1150 760 C 1250 700, 1250 600, 1150 625 C 1050 625, 650 625, 600 625" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1250" y="680" text-anchor="middle" font-size="11" fill="#666">批量解除/更新</text>

</svg>