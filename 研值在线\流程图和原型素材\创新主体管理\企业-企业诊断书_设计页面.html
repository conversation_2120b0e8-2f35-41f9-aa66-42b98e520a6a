<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业诊断书</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">企业诊断书</h1>

        <!-- 指标概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">当年诊断次数</p>
                        <p class="text-2xl font-bold">12</p>
                    </div>
                    <span class="text-green-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        15%
                    </span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">累计诊断次数</p>
                        <p class="text-2xl font-bold">48</p>
                    </div>
                    <span class="text-green-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        8%
                    </span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">服务机构数量</p>
                        <p class="text-2xl font-bold">6</p>
                    </div>
                    <span class="text-gray-500 text-sm">同比持平</span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">服务专家数量</p>
                        <p class="text-2xl font-bold">18</p>
                    </div>
                    <span class="text-green-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        20%
                    </span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">平均诊断间隔</p>
                        <p class="text-2xl font-bold">45天</p>
                    </div>
                    <span class="text-red-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                        5天
                    </span>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500">培训参与率</p>
                        <p class="text-2xl font-bold">85%</p>
                    </div>
                    <span class="text-green-500 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                        </svg>
                        12%
                    </span>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">诊断年份</label>
                    <div class="flex space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                        </select>
                        <span class="flex items-center text-gray-500">至</span>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <option>2023</option>
                            <option>2022</option>
                            <option>2021</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">服务机构</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部机构</option>
                        <option>宁波市科技创新服务中心</option>
                        <option>宁波市企业服务联盟</option>
                        <option>宁波市生产力促进中心</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">服务专家</label>
                    <input type="text" placeholder="输入专家姓名" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">诊断类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部类型</option>
                        <option>技术创新诊断</option>
                        <option>管理提升诊断</option>
                        <option>市场拓展诊断</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">关键字搜索</label>
                    <input type="text" placeholder="输入诊断主题或意见关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 text-sm">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    查询
                </button>
            </div>
        </div>

        <!-- 诊断记录列表区 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">诊断记录列表</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        导出
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        新增诊断
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">诊断时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务机构</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务专家</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">诊断主题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主要诊断意见</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">培训参与</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技创新服务中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术创新能力提升</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">建议加强研发投入，建立产学研合作机制，提升专利质量...</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">已参加</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                <button class="text-gray-600 hover:text-gray-900">下钻</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-08</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市企业服务联盟</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">李博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">管理流程优化</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">建议优化组织架构，引入数字化管理系统，提升运营效率...</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">部分参加</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                <button class="text-gray-600 hover:text-gray-900">下钻</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-07-22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市生产力促进中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">王专家</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市场拓展策略</td>
                            <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">建议细分目标市场，优化营销渠道，加强品牌建设...</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">未参加</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal()" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">下载</button>
                                <button class="text-gray-600 hover:text-gray-900">下钻</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 48 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mt-6">
            <h2 class="text-lg font-medium text-gray-800 mb-6">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">年度诊断次数分布</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">柱状图</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs">折线图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="diagnosisChart"></canvas>
                    </div>
                </div>
                <div>
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-md font-medium text-gray-700">诊断意见类别分布</h3>
                        <div class="flex space-x-2">
                            <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">雷达图</button>
                            <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-xs">饼图</button>
                        </div>
                    </div>
                    <div class="h-64">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 诊断详情侧栏 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="absolute right-0 top-0 h-full w-full max-w-2xl bg-white shadow-xl overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold text-gray-900">诊断详情</h2>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <!-- 基本信息 -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-800 mb-4">基本信息</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <p class="text-sm text-gray-500">诊断时间</p>
                            <p class="text-sm font-medium text-gray-900">2023-11-15</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">服务机构</p>
                            <p class="text-sm font-medium text-gray-900">宁波市科技创新服务中心</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">服务专家</p>
                            <p class="text-sm font-medium text-gray-900">张教授</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">诊断类型</p>
                            <p class="text-sm font-medium text-gray-900">技术创新能力提升</p>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <button class="border-b-2 border-blue-500 text-blue-600 px-1 py-4 text-sm font-medium">诊断意见</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">问题清单</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">改进建议</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">提升记录</button>
                        <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-1 py-4 text-sm font-medium">培训情况</button>
                    </nav>
                </div>

                <!-- 诊断意见内容 -->
                <div class="mb-8">
                    <h4 class="text-md font-medium text-gray-800 mb-3">诊断意见全文</h4>
                    <div class="prose max-w-none text-sm text-gray-700">
                        <p>通过对贵企业的全面诊断，我们发现企业在技术创新方面存在以下主要问题：</p>
                        <ul class="list-disc pl-5 space-y-2 mt-2">
                            <li>研发投入占营业收入比例仅为3.2%，低于行业平均水平5.8%</li>
                            <li>产学研合作项目数量较少，缺乏高校资源对接渠道</li>
                            <li>专利质量有待提升，发明专利占比不足30%</li>
                            <li>研发人员激励机制不完善，核心技术人员流失率较高</li>
                        </ul>
                        <p class="mt-4">建议采取以下改进措施：</p>
                        <ul class="list-disc pl-5 space-y-2 mt-2">
                            <li>将研发投入比例提升至5%以上，重点投向核心技术领域</li>
                            <li>与宁波大学、中科院宁波材料所建立战略合作关系</li>
                            <li>引入专利质量评估体系，优化知识产权布局</li>
                            <li>建立研发人员股权激励计划，稳定核心团队</li>
                        </ul>
                    </div>
                </div>

                <!-- 附件信息 -->
                <div class="mb-8">
                    <h4 class="text-md font-medium text-gray-800 mb-3">诊断附件</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-700">诊断报告-20231115.pdf</span>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                        </div>
                        <div class="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm font-medium text-gray-700">问题清单-20231115.xlsx</span>
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 text-sm">下载</button>
                        </div>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex space-x-3 mt-8">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        导出PDF
                    </button>
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        打印预览
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal() {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化年度诊断次数图表
            const diagnosisCtx = document.getElementById('diagnosisChart').getContext('2d');
            new Chart(diagnosisCtx, {
                type: 'bar',
                data: {
                    labels: ['2021', '2022', '2023'],
                    datasets: [{
                        label: '诊断次数',
                        data: [12, 24, 12],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 初始化诊断意见类别雷达图
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            new Chart(categoryCtx, {
                type: 'radar',
                data: {
                    labels: ['技术创新', '管理优化', '市场拓展', '人才建设', '资金管理'],
                    datasets: [{
                        label: '诊断意见分布',
                        data: [85, 60, 45, 70, 30],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>