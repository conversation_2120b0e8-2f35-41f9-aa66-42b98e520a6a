unit Cc_LoadingFrm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants,
  System.Classes, Vcl.Graphics,
  Vcl.Controls, Vcl.Forms, Vcl.Dialogs, RxAnimate, RxGIFCtrl, RxGIF,
  Vcl.ExtCtrls, Vcl.Imaging.jpeg, Vcl.StdCtrls, RzLabel, Vcl.Imaging.pngimage;

type
  TLoadingForm = class(TForm)
    Image1: TImage;
    RzLabel1: TRzLabel;
    procedure FormCreate(Sender: TObject);
    procedure FormShow(Sender: TObject);
  private
    { Private declarations }
  public
    { Public declarations }
  end;

var
  LoadingForm: TLoadingForm;

implementation

{$R *.dfm}

procedure TLoadingForm.FormCreate(Sender: TObject);
begin
  LoadingForm.left := (screen.width - LoadingForm.width) div 2;
  LoadingForm.top := (screen.height - LoadingForm.height) div 2;
end;

procedure TLoadingForm.FormShow(Sender: TObject);
begin
//  self.RxGIFAnimator1.Animate := true;
end;

end.
