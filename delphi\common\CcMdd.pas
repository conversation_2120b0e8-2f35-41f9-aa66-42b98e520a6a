unit CcMdd;

interface

uses
  Classes;

type
  TCcMdd = class
  private
    FMddid: integer;
    FMddname: string;
    FMddtype: integer;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Mddid: integer read FMddid write FMddid;
    property Mddname: string read FMddname write FMddname;
    property Mddtype: integer read FMddtype write FMddtype;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
