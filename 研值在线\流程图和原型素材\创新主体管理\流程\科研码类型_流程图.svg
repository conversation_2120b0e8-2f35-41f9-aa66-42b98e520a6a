<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研码类型管理流程图</text>

  <!-- 阶段一：系统初始化与列表展示 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与列表展示</text>
  
  <!-- 节点1: 管理员进入页面 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员进入页面</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">科研码类型管理</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">触发初始化</text>
  </g>

  <!-- 节点2: 系统加载列表 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统加载列表</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">显示现有类型</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">支持多条件搜索</text>
  </g>

  <!-- 阶段二：类型管理操作 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：类型管理操作</text>

  <!-- 节点3: 新增类型 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增类型</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">填写名称、范围</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">校验唯一性</text>
  </g>

  <!-- 节点4: 修改类型 -->
  <g transform="translate(350, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">修改类型</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">调整字段信息</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">记录修改日志</text>
  </g>

  <!-- 节点5: 停用删除 -->
  <g transform="translate(600, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">停用删除</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">校验引用关系</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">风险提示</text>
  </g>

  <!-- 节点6: 批量操作 -->
  <g transform="translate(850, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量操作</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">启用/停用/删除</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">导出筛选结果</text>
  </g>

  <!-- 阶段三：数据同步与更新 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据同步与更新</text>

  <!-- 节点7: 实例同步更新 -->
  <g transform="translate(250, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实例同步更新</text>
    <text x="110" y="45" text-anchor="middle" font-size="12" fill="#555">更新被引用的</text>
    <text x="110" y="60" text-anchor="middle" font-size="12" fill="#555">科研码实例展示</text>
  </g>

  <!-- 节点8: 数据验证 -->
  <g transform="translate(550, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据验证</text>
    <text x="100" y="45" text-anchor="middle" font-size="12" fill="#555">引用关系检查</text>
    <text x="100" y="60" text-anchor="middle" font-size="12" fill="#555">完整性校验</text>
  </g>

  <!-- 阶段四：审计日志与合规 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审计日志与合规</text>

  <!-- 节点9: 审计日志记录 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="25" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志记录</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">操作记录</text>
    <text x="200" y="50" text-anchor="middle" font-size="12" fill="#555">字段变更</text>
    <text x="300" y="50" text-anchor="middle" font-size="12" fill="#555">可追溯性</text>
    <text x="200" y="65" text-anchor="middle" font-size="12" fill="#555">确保管理过程合规性</text>
  </g>

  <!-- 连接线 -->
  <!-- 管理员进入 -> 系统加载 -->
  <path d="M 500 170 Q 550 170 600 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 系统加载 -> 各种操作 -->
  <path d="M 650 210 C 600 250, 300 280, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 680 210 C 650 250, 500 280, 450 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 210 Q 700 265 700 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 210 C 750 250, 850 280, 950 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 修改操作 -> 实例同步 -->
  <path d="M 400 400 C 380 450, 370 480, 360 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 停用删除 -> 数据验证 -->
  <path d="M 650 400 C 650 450, 650 480, 650 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 所有操作 -> 审计日志 -->
  <path d="M 200 400 C 200 600, 450 680, 500 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 450 400 C 450 600, 550 680, 580 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 400 C 700 600, 650 680, 620 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 950 400 C 950 600, 750 680, 700 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 实例同步和数据验证 -> 审计日志 -->
  <path d="M 360 600 C 400 650, 500 680, 550 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 650 600 C 650 650, 650 680, 650 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 标注文字 -->
  <text x="550" y="155" font-size="11" fill="#666">加载</text>
  <text x="300" y="265" font-size="11" fill="#666">新增</text>
  <text x="500" y="265" font-size="11" fill="#666">编辑</text>
  <text x="700" y="265" font-size="11" fill="#666">管理</text>
  <text x="850" y="265" font-size="11" fill="#666">批量</text>
  <text x="380" y="460" font-size="11" fill="#666">同步</text>
  <text x="650" y="460" font-size="11" fill="#666">验证</text>
  <text x="350" y="650" font-size="11" fill="#666">记录</text>
  <text x="650" y="650" font-size="11" fill="#666">审计</text>

</svg>