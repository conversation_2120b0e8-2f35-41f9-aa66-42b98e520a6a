<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员获奖情况管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js" defer></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900">科研管理人员获奖情况管理</h1>
            <p class="text-gray-600 text-sm">全面支持获奖情况的归集、标准化、动态管理和多维分析</p>
        </div>

        <!-- 主要操作按钮 -->
        <div class="flex justify-between items-center mb-6">
            <div class="flex space-x-3">
                <button onclick="openModal('addAwardModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增获奖记录
                </button>
                <button id="batchExportBtn" class="px-4 py-2 border border-gray-300 bg-white text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                    </svg>
                    批量导出
                </button>
                <button id="batchDeleteBtn" class="px-4 py-2 border border-red-300 bg-white text-red-600 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 flex items-center transition-colors opacity-50 cursor-not-allowed">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    批量移除
                </button>
            </div>
            <div class="relative">
                <input type="text" placeholder="全局搜索..." class="pl-9 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-64">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
        </div>

        <!-- 查询筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-5 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                查询与筛选
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <div>
                    <label for="awardProject" class="block text-sm font-medium text-gray-700 mb-1">获奖项目</label>
                    <input type="text" id="awardProject" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖项目名称">
                </div>
                <div>
                    <label for="awardType" class="block text-sm font-medium text-gray-700 mb-1">奖励类型</label>
                    <select id="awardType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="science">科学技术奖</option>
                        <option value="innovation">创新创业奖</option>
                        <option value="talent">人才专项奖</option>
                        <option value="management">管理创新奖</option>
                        <option value="other">其他奖项</option>
                    </select>
                </div>
                <div>
                    <label for="awardLevel" class="block text-sm font-medium text-gray-700 mb-1">奖励级别</label>
                    <select id="awardLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                        <option value="district">区级</option>
                        <option value="institutional">机构级</option>
                    </select>
                </div>
                <div>
                    <label for="awardYear" class="block text-sm font-medium text-gray-700 mb-1">奖励年度</label>
                    <select id="awardYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部年度</option>
                        <option value="2024">2024年</option>
                        <option value="2023">2023年</option>
                        <option value="2022">2022年</option>
                        <option value="2021">2021年</option>
                        <option value="2020">2020年</option>
                    </select>
                </div>
                <div>
                    <label for="winnerName" class="block text-sm font-medium text-gray-700 mb-1">获奖人员姓名</label>
                    <input type="text" id="winnerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖人员姓名">
                </div>
                <div>
                    <label for="workUnit" class="block text-sm font-medium text-gray-700 mb-1">工作单位</label>
                    <input type="text" id="workUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                </div>
                <div>
                    <label for="awardingBody" class="block text-sm font-medium text-gray-700 mb-1">颁奖单位</label>
                    <input type="text" id="awardingBody" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入颁奖单位">
                </div>
                <div>
                    <label for="awardDateRange" class="block text-sm font-medium text-gray-700 mb-1">获奖时间</label>
                    <div class="flex space-x-2">
                        <input type="date" id="startDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" id="endDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
            </div>
            <div class="mt-5 flex justify-end space-x-3">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                    查询
                </button>
            </div>
        </div>

        <!-- 获奖情况列表区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="p-5 border-b border-gray-200">
                <h2 class="text-lg font-medium text-gray-800 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    获奖情况列表
                </h2>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-10">
                                <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖人员</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖项目</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励级别</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励年度</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">颁奖单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="1">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">张明华</div>
                                <div class="text-xs text-gray-500">宁波市科技创新研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">新型纳米材料制备技术研究</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">科学技术奖</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">省级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2024</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省科技厅</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewAwardDetail('1')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                <button onclick="editAward('1')" class="text-indigo-600 hover:text-indigo-900 transition-colors">修改</button>
                                <button onclick="deleteAward('1')" class="text-red-600 hover:text-red-900 transition-colors">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="2">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">李建国</div>
                                <div class="text-xs text-gray-500">宁波市智能制造有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">工业机器人智能控制系统开发</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">创新创业奖</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">市级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市经信局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewAwardDetail('2')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                <button onclick="editAward('2')" class="text-indigo-600 hover:text-indigo-900 transition-colors">修改</button>
                                <button onclick="deleteAward('2')" class="text-red-600 hover:text-red-900 transition-colors">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="3">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王丽娟</div>
                                <div class="text-xs text-gray-500">宁波大学科研处</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">海洋生物技术创新团队建设</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">人才专项奖</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">国家级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家人力资源和社会保障部</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-09-05</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewAwardDetail('3')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                <button onclick="editAward('3')" class="text-indigo-600 hover:text-indigo-900 transition-colors">修改</button>
                                <button onclick="deleteAward('3')" class="text-red-600 hover:text-red-900 transition-colors">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="4">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">赵志强</div>
                                <div class="text-xs text-gray-500">宁波市科技局</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">科技创新政策体系优化研究</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">管理创新奖</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">区级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">鄞州区人民政府</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-12-18</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewAwardDetail('4')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                <button onclick="editAward('4')" class="text-indigo-600 hover:text-indigo-900 transition-colors">修改</button>
                                <button onclick="deleteAward('4')" class="text-red-600 hover:text-red-900 transition-colors">移除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="record-checkbox rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50" data-id="5">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">陈思思</div>
                                <div class="text-xs text-gray-500">宁波市数字经济研究院</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">大数据驱动的产业升级路径研究</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">科学技术奖</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">市级</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市科技局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-07-30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="viewAwardDetail('5')" class="text-blue-600 hover:text-blue-900 transition-colors">详情</button>
                                <button onclick="editAward('5')" class="text-indigo-600 hover:text-indigo-900 transition-colors">修改</button>
                                <button onclick="deleteAward('5')" class="text-red-600 hover:text-red-900 transition-colors">移除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- 分页控制 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">24</span> 条记录
                </div>
                <div class="flex space-x-1">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" disabled>上一页</button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">4</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">5</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑获奖记录弹窗 -->
    <div id="addAwardModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-medium text-gray-900" id="awardModalTitle">新增获奖记录</h3>
                    <button onclick="closeModal('addAwardModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <form id="awardForm" class="space-y-6">
                    <input type="hidden" id="awardId">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 获奖人员信息 -->
                        <div>
                            <label for="awardWinnerName" class="block text-sm font-medium text-gray-700 mb-1">获奖人员姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="awardWinnerName" name="winnerName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖人员姓名">
                        </div>
                        <div>
                            <label for="awardWinnerUnit" class="block text-sm font-medium text-gray-700 mb-1">工作单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="awardWinnerUnit" name="winnerUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入工作单位">
                        </div>
                        <div>
                            <label for="awardWinnerPosition" class="block text-sm font-medium text-gray-700 mb-1">职务/职称</label>
                            <input type="text" id="awardWinnerPosition" name="winnerPosition" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入职务/职称">
                        </div>
                        <div>
                            <label for="awardWinnerPhone" class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                            <input type="tel" id="awardWinnerPhone" name="winnerPhone" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入联系电话">
                        </div>
                        
                        <!-- 奖项基本信息 -->
                        <div>
                            <label for="awardName" class="block text-sm font-medium text-gray-700 mb-1">奖项名称 <span class="text-red-500">*</span></label>
                            <input type="text" id="awardName" name="awardName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入奖项名称">
                        </div>
                        <div>
                            <label for="awardProject" class="block text-sm font-medium text-gray-700 mb-1">获奖项目 <span class="text-red-500">*</span></label>
                            <input type="text" id="awardProject" name="awardProject" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖项目名称">
                        </div>
                        <div>
                            <label for="awardType" class="block text-sm font-medium text-gray-700 mb-1">奖励类型 <span class="text-red-500">*</span></label>
                            <select id="awardTypeSelect" name="awardType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择奖励类型</option>
                                <option value="science">科学技术奖</option>
                                <option value="innovation">创新创业奖</option>
                                <option value="talent">人才专项奖</option>
                                <option value="management">管理创新奖</option>
                                <option value="other">其他奖项</option>
                            </select>
                        </div>
                        <div>
                            <label for="awardLevel" class="block text-sm font-medium text-gray-700 mb-1">奖励级别 <span class="text-red-500">*</span></label>
                            <select id="awardLevelSelect" name="awardLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择奖励级别</option>
                                <option value="national">国家级</option>
                                <option value="provincial">省级</option>
                                <option value="municipal">市级</option>
                                <option value="district">区级</option>
                                <option value="institutional">机构级</option>
                            </select>
                        </div>
                        <div>
                            <label for="awardYear" class="block text-sm font-medium text-gray-700 mb-1">奖励年度 <span class="text-red-500">*</span></label>
                            <select id="awardYearSelect" name="awardYear" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">请选择奖励年度</option>
                                <option value="2024">2024年</option>
                                <option value="2023">2023年</option>
                                <option value="2022">2022年</option>
                                <option value="2021">2021年</option>
                                <option value="2020">2020年</option>
                            </select>
                        </div>
                        <div>
                            <label for="awardDate" class="block text-sm font-medium text-gray-700 mb-1">获奖时间 <span class="text-red-500">*</span></label>
                            <input type="date" id="awardDate" name="awardDate" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="awardingBody" class="block text-sm font-medium text-gray-700 mb-1">颁奖单位 <span class="text-red-500">*</span></label>
                            <input type="text" id="awardingBodyInput" name="awardingBody" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入颁奖单位">
                        </div>
                        <div>
                            <label for="certificateNumber" class="block text-sm font-medium text-gray-700 mb-1">证书编号</label>
                            <input type="text" id="certificateNumber" name="certificateNumber" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入证书编号">
                        </div>
                    </div>
                    
                    <!-- 详细信息 -->
                    <div>
                        <label for="awardDescription" class="block text-sm font-medium text-gray-700 mb-1">获奖项目描述</label>
                        <textarea id="awardDescription" name="awardDescription" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入获奖项目描述"></textarea>
                    </div>
                    
                    <!-- 附件上传 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                            <div class="mt-2">
                                <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="file-upload" name="file-upload" type="file" class="sr-only" multiple>
                                </label>
                                <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">支持PDF、JPG、PNG格式，单个文件不超过10MB</p>
                        </div>
                        <div id="uploadedFiles" class="mt-3 space-y-2 hidden">
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                                    </svg>
                                    <span class="text-sm text-gray-700">获奖证书.pdf</span>
                                </div>
                                <button class="text-red-500 hover:text-red-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button type="button" onclick="closeModal('addAwardModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                            取消
                        </button>
                        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            保存
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 获奖详情弹窗 -->
    <div id="awardDetailModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl bg-white rounded-lg shadow-xl max-h-[90vh] overflow-y-auto">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-medium text-gray-900">获奖详情</h3>
                    <button onclick="closeModal('awardDetailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-6">
                    <!-- 获奖人员信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">获奖人员信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">姓名：</span>
                                <span class="font-medium text-gray-900" id="detail-winnerName">张明华</span>
                            </div>
                            <div>
                                <span class="text-gray-500">工作单位：</span>
                                <span class="font-medium text-gray-900" id="detail-winnerUnit">宁波市科技创新研究院</span>
                            </div>
                            <div>
                                <span class="text-gray-500">职务/职称：</span>
                                <span class="font-medium text-gray-900" id="detail-winnerPosition">研究员</span>
                            </div>
                            <div>
                                <span class="text-gray-500">联系电话：</span>
                                <span class="font-medium text-gray-900" id="detail-winnerPhone">0574-87654321</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 奖项基本信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">奖项基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">奖项名称：</span>
                                <span class="font-medium text-gray-900" id="detail-awardName">浙江省科学技术进步奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">获奖项目：</span>
                                <span class="font-medium text-gray-900" id="detail-awardProject">新型纳米材料制备技术研究</span>
                            </div>
                            <div>
                                <span class="text-gray-500">奖励类型：</span>
                                <span class="font-medium text-gray-900" id="detail-awardType">科学技术奖</span>
                            </div>
                            <div>
                                <span class="text-gray-500">奖励级别：</span>
                                <span class="font-medium text-gray-900" id="detail-awardLevel">省级</span>
                            </div>
                            <div>
                                <span class="text-gray-500">奖励年度：</span>
                                <span class="font-medium text-gray-900" id="detail-awardYear">2024</span>
                            </div>
                            <div>
                                <span class="text-gray-500">获奖时间：</span>
                                <span class="font-medium text-gray-900" id="detail-awardDate">2024-03-15</span>
                            </div>
                            <div>
                                <span class="text-gray-500">颁奖单位：</span>
                                <span class="font-medium text-gray-900" id="detail-awardingBody">浙江省科技厅</span>
                            </div>
                            <div>
                                <span class="text-gray-500">证书编号：</span>
                                <span class="font-medium text-gray-900" id="detail-certificateNumber">ZJKJ-2024-058</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 获奖项目描述 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">获奖项目描述</h4>
                        <div class="text-sm text-gray-700" id="detail-awardDescription">
                            本项目针对纳米材料制备过程中的关键技术瓶颈，开发了新型高效的纳米材料合成方法，解决了传统制备工艺中产物纯度低、粒径分布不均、能耗高等问题。研究成果已在多家企业实现产业化应用，产生了显著的经济效益和社会效益，对推动浙江省新材料产业发展具有重要意义。
                        </div>
                    </div>
                    
                    <!-- 附件信息 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">相关附件</h4>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                                    </svg>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">获奖证书.pdf</div>
                                        <div class="text-xs text-gray-500">2.4 MB · 上传于 2024-04-01</div>
                                    </div>
                                </div>
                                <button class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-md hover:bg-blue-200 transition-colors">
                                    下载
                                </button>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">项目验收报告.docx</div>
                                        <div class="text-xs text-gray-500">1.8 MB · 上传于 2024-04-01</div>
                                    </div>
                                </div>
                                <button class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-md hover:bg-blue-200 transition-colors">
                                    下载
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作记录 -->
                    <div class="bg-gray-50 rounded-lg p-5">
                        <h4 class="text-lg font-semibold text-gray-900 mb-4">操作记录</h4>
                        <div class="space-y-3 text-sm">
                            <div class="p-3 bg-white border border-gray-200 rounded-md">
                                <div class="flex justify-between">
                                    <div class="font-medium text-gray-900">新增记录</div>
                                    <div class="text-gray-500">2024-04-01 09:30:25</div>
                                </div>
                                <div class="mt-1 text-gray-700">操作员：admin</div>
                            </div>
                            <div class="p-3 bg-white border border-gray-200 rounded-md">
                                <div class="flex justify-between">
                                    <div class="font-medium text-gray-900">更新信息</div>
                                    <div class="text-gray-500">2024-04-10 14:20:18</div>
                                </div>
                                <div class="mt-1 text-gray-700">操作员：王管理员</div>
                                <div class="mt-1 text-gray-700">更新内容：修改了联系电话</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end mt-6">
                    <button onclick="closeModal('awardDetailModal')" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div id="deleteConfirmModal" class="modal-overlay fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="relative top-40 mx-auto p-5 border w-full max-w-md bg-white rounded-lg shadow-xl">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">确认移除</h3>
                    <button onclick="closeModal('deleteConfirmModal')" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="text-center mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-yellow-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-.833-2.694-.833-3.464 0L3.34 16c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p class="text-gray-700 mb-2" id="deleteConfirmText">您确定要移除这条获奖记录吗？</p>
                    <p class="text-sm text-gray-500">此操作不可撤销，移除后将无法恢复。</p>
                </div>
                
                <div class="mb-4">
                    <label for="deleteReason" class="block text-sm font-medium text-gray-700 mb-1">移除原因</label>
                    <textarea id="deleteReason" rows="2" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入移除原因"></textarea>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('deleteConfirmModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button type="button" id="confirmDeleteBtn" onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors">
                        确认移除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 全选/取消全选功能
            const selectAllCheckbox = document.getElementById('selectAll');
            const recordCheckboxes = document.querySelectorAll('.record-checkbox');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            
            selectAllCheckbox.addEventListener('change', function() {
                recordCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateBatchButtonState();
            });
            
            recordCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateBatchButtonState);
            });
            
            function updateBatchButtonState() {
                const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
                if (checkedCount > 0) {
                    batchDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                    batchDeleteBtn.addEventListener('click', confirmBatchDelete);
                } else {
                    batchDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
                    batchDeleteBtn.removeEventListener('click', confirmBatchDelete);
                }
            }
            
            // 批量导出按钮点击事件
            document.getElementById('batchExportBtn').addEventListener('click', function() {
                const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
                if (checkedCount > 0) {
                    alert(`已选择${checkedCount}条记录，准备导出数据...`);
                } else {
                    alert('请先选择需要导出的记录');
                }
            });
            
            // 批量删除确认
            function confirmBatchDelete() {
                const checkedCount = document.querySelectorAll('.record-checkbox:checked').length;
                document.getElementById('deleteConfirmText').textContent = `您确定要移除选中的${checkedCount}条获奖记录吗？`;
                document.getElementById('awardId').value = 'batch'; // 标记为批量删除
                openModal('deleteConfirmModal');
            }
            
            // 文件上传处理
            document.getElementById('file-upload').addEventListener('change', function() {
                if (this.files.length > 0) {
                    document.getElementById('uploadedFiles').classList.remove('hidden');
                }
            });
            
            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 绑定ESC键关闭
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理
            document.getElementById('awardForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const awardId = document.getElementById('awardId').value;
                const isEdit = !!awardId;
                
                alert(isEdit ? '获奖记录已更新成功！' : '新获奖记录已添加成功！');
                closeModal('addAwardModal');
            });
        });

        // --- 3. 业务功能函数 ---
        // 查看获奖详情
        function viewAwardDetail(id) {
            // 在实际应用中，这里会根据id从服务器获取数据
            // 这里使用示例数据
            openModal('awardDetailModal');
        }

        // 编辑获奖记录
        function editAward(id) {
            document.getElementById('awardModalTitle').textContent = '修改获奖记录';
            document.getElementById('awardId').value = id;
            
            // 在实际应用中，这里会根据id从服务器获取数据并填充表单
            // 这里使用示例数据
            document.getElementById('awardWinnerName').value = '张明华';
            document.getElementById('awardWinnerUnit').value = '宁波市科技创新研究院';
            document.getElementById('awardWinnerPosition').value = '研究员';
            document.getElementById('awardWinnerPhone').value = '0574-87654321';
            document.getElementById('awardName').value = '浙江省科学技术进步奖';
            document.getElementById('awardProject').value = '新型纳米材料制备技术研究';
            document.getElementById('awardTypeSelect').value = 'science';
            document.getElementById('awardLevelSelect').value = 'provincial';
            document.getElementById('awardYearSelect').value = '2024';
            document.getElementById('awardDate').value = '2024-03-15';
            document.getElementById('awardingBodyInput').value = '浙江省科技厅';
            document.getElementById('certificateNumber').value = 'ZJKJ-2024-058';
            document.getElementById('awardDescription').value = '本项目针对纳米材料制备过程中的关键技术瓶颈，开发了新型高效的纳米材料合成方法...';
            
            openModal('addAwardModal');
        }

        // 删除获奖记录
        function deleteAward(id) {
            document.getElementById('awardId').value = id;
            document.getElementById('deleteConfirmText').textContent = '您确定要移除这条获奖记录吗？';
            openModal('deleteConfirmModal');
        }

        // 确认删除
        function confirmDelete() {
            const awardId = document.getElementById('awardId').value;
            const deleteReason = document.getElementById('deleteReason').value;
            
            if (!deleteReason.trim()) {
                alert('请输入移除原因');
                return;
            }
            
            // 在实际应用中，这里会发送删除请求到服务器
            alert('获奖记录已成功移除！');
            closeModal('deleteConfirmModal');
            
            // 如果是批量删除，取消所有选中状态并更新按钮状态
            if (awardId === 'batch') {
                document.getElementById('selectAll').checked = false;
                document.querySelectorAll('.record-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
                document.getElementById('batchDeleteBtn').classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    </script>
</body>
</html>