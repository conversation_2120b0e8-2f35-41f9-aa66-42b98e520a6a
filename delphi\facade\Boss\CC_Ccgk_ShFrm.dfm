object CC_Ccgk_ShFrame: TCC_Ccgk_ShFrame
  Left = 0
  Top = 0
  Width = 1300
  Height = 760
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  ParentFont = False
  TabOrder = 0
  object MainPanel: TRzPanel
    Left = 0
    Top = 60
    Width = 1300
    Height = 700
    Align = alClient
    BorderOuter = fsNone
    BorderColor = clWhite
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 0
    object RzPanel1: TRzPanel
      Left = 0
      Top = 40
      Width = 1300
      Height = 475
      Align = alClient
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object GjAdvStringGrid: TAdvStringGrid
        Left = 0
        Top = 0
        Width = 1300
        Height = 475
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        ColCount = 20
        Ctl3D = True
        DefaultRowHeight = 24
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedCols = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goDrawFocusSelected, goEditing]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        ScrollBars = ssBoth
        TabOrder = 0
        Visible = False
        GridLineColor = 15855083
        GridFixedLineColor = 13745060
        HoverRowCells = [hcNormal, hcSelected]
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #24494#36719#38597#40657
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 10344697
        ActiveCellColorTo = 6210033
        ControlLook.FixedGradientFrom = 16513526
        ControlLook.FixedGradientTo = 15260626
        ControlLook.FixedGradientHoverFrom = 15000287
        ControlLook.FixedGradientHoverTo = 14406605
        ControlLook.FixedGradientHoverMirrorFrom = 14406605
        ControlLook.FixedGradientHoverMirrorTo = 13813180
        ControlLook.FixedGradientHoverBorder = 12033927
        ControlLook.FixedGradientDownFrom = 14991773
        ControlLook.FixedGradientDownTo = 14991773
        ControlLook.FixedGradientDownMirrorFrom = 14991773
        ControlLook.FixedGradientDownMirrorTo = 14991773
        ControlLook.FixedGradientDownBorder = 14991773
        ControlLook.DropDownCount = 20
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.ColumnWidth = True
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -12
        FilterDropDown.Font.Name = #24494#36719#38597#40657
        FilterDropDown.Font.Style = []
        FilterDropDown.GlyphActive.Data = {
          36050000424D3605000000000000360400002800000010000000100000000100
          08000000000000010000530B0000530B00000001000000010000104A10001063
          100010731000108410001094100039AD520031BD520039BD5A0042CE5A0039BD
          63004AD663004AD6730052DE730052E77B0052EF7B0063EF840063EF8C006BEF
          8C006BEF940084F7A500FF10FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFF
          FF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00FFFFFF00141414141414
          1414020214141414141414141414141414030902141414141414141414141414
          030D090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141414
          0313090214141414141414141414141403130902141414141414141414141403
          130D09000214141414141414141403130D0D0501000214141414141414031311
          0F0D07040100021414141414031312100D0B0909040100021414140313120C0A
          0806090909040100021403030303030303030303030303030303141414141414
          1414141414141414141414141414141414141414141414141414}
        FilterDropDown.Height = 200
        FilterDropDown.TextChecked = 'Checked'
        FilterDropDown.TextUnChecked = 'Unchecked'
        FilterDropDown.Width = 200
        FilterDropDownClear = #20840#37096
        FilterDropDownCheck = True
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedFooters = 1
        FixedColWidth = 35
        FixedRowHeight = 24
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #24494#36719#38597#40657
        FixedFont.Style = []
        FloatFormat = '%.2f'
        FloatingFooter.Visible = True
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HoverFixedCells = hfFixedRows
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glOffice2007
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        ScrollProportional = True
        ScrollSynch = True
        SearchFooter.Color = 16513526
        SearchFooter.ColorTo = clNone
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = 6210033
        SortSettings.DefaultFormat = ssAutomatic
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaCenter
        Version = '8.1.3.0'
        WordWrap = False
        ColWidths = (
          35
          51
          61
          59
          51
          54
          50
          51
          58
          54
          64
          64
          64
          64
          64
          64
          64
          64
          64
          64)
      end
    end
    object ButtomPanel: TRzPanel
      Left = 0
      Top = 515
      Width = 1300
      Height = 185
      Align = alBottom
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 1
      Visible = False
      object ButtomMainPanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1300
        Height = 185
        Align = alClient
        BorderOuter = fsNone
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        object ButtomLeftPanel: TRzPanel
          Left = 0
          Top = 0
          Width = 10
          Height = 185
          Align = alLeft
          BorderOuter = fsNone
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          TabOrder = 0
          Transparent = True
        end
        object ButtomClientPanel: TRzPanel
          Left = 10
          Top = 0
          Width = 1290
          Height = 185
          Align = alClient
          BorderOuter = fsNone
          Color = clWhite
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -12
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          ParentFont = False
          TabOrder = 1
          Transparent = True
          object AdvSmoothImageListBox1: TAdvSmoothImageListBox
            Left = 0
            Top = 0
            Width = 1290
            Height = 185
            ScrollType = stNormal
            AnimationFactor = 1
            ZoomAnimationFactor = 1.500000000000000000
            SelectedItemIndex = 0
            Items = <>
            TopLayerItems = <
              item
                Top = 250
                Left = 250
                HTMLText.Location = cpCenterCenter
                HTMLText.Font.Charset = DEFAULT_CHARSET
                HTMLText.Font.Color = clWindowText
                HTMLText.Font.Height = -21
                HTMLText.Font.Name = 'Tahoma'
                HTMLText.Font.Style = []
                Fill.Color = clWhite
                Fill.ColorTo = clWhite
                Fill.ColorMirror = clNone
                Fill.ColorMirrorTo = clNone
                Fill.GradientType = gtVertical
                Fill.GradientMirrorType = gtSolid
                Fill.PictureLeft = 10
                Fill.PictureTop = 10
                Fill.PictureSize = psCustom
                Fill.PictureWidth = 75
                Fill.PictureHeight = 75
                Fill.Opacity = 114
                Fill.OpacityTo = 194
                Fill.BorderColor = clBlack
                Fill.Rounding = 20
                Fill.RoundingType = rtNone
                Fill.ShadowOffset = 0
                Fill.Glow = gmNone
                Width = 0
                Height = 0
                Tag = 0
              end>
            ItemAppearance.AutoSize = True
            ItemAppearance.TextVisible = True
            ItemAppearance.TextTop = 5
            ItemAppearance.TextHeight = 15
            ItemAppearance.ItemWidth = 137
            ItemAppearance.ItemHeight = 152
            ItemAppearance.Fill.Color = 16773091
            ItemAppearance.Fill.ColorTo = 16768452
            ItemAppearance.Fill.ColorMirror = 16765357
            ItemAppearance.Fill.ColorMirrorTo = 16767936
            ItemAppearance.Fill.GradientType = gtVertical
            ItemAppearance.Fill.GradientMirrorType = gtVertical
            ItemAppearance.Fill.BorderColor = 16765357
            ItemAppearance.Fill.Rounding = 0
            ItemAppearance.Fill.ShadowOffset = 0
            ItemAppearance.Fill.Glow = gmNone
            ItemAppearance.SelectedFill.Color = 11196927
            ItemAppearance.SelectedFill.ColorTo = 7257087
            ItemAppearance.SelectedFill.ColorMirror = 4370174
            ItemAppearance.SelectedFill.ColorMirrorTo = 8053246
            ItemAppearance.SelectedFill.GradientType = gtVertical
            ItemAppearance.SelectedFill.GradientMirrorType = gtVertical
            ItemAppearance.SelectedFill.BorderColor = 16765357
            ItemAppearance.SelectedFill.BorderWidth = 10
            ItemAppearance.SelectedFill.Rounding = 0
            ItemAppearance.SelectedFill.ShadowOffset = 0
            ItemAppearance.SelectedFill.Glow = gmNone
            ItemAppearance.DisabledFill.Color = 15921906
            ItemAppearance.DisabledFill.ColorTo = 11974326
            ItemAppearance.DisabledFill.ColorMirror = 11974326
            ItemAppearance.DisabledFill.ColorMirrorTo = 15921906
            ItemAppearance.DisabledFill.GradientType = gtVertical
            ItemAppearance.DisabledFill.GradientMirrorType = gtVertical
            ItemAppearance.DisabledFill.BorderColor = 16765357
            ItemAppearance.DisabledFill.Rounding = 0
            ItemAppearance.DisabledFill.ShadowOffset = 0
            ItemAppearance.DisabledFill.Glow = gmNone
            ItemAppearance.HoverFill.Color = 15465983
            ItemAppearance.HoverFill.ColorTo = 11332863
            ItemAppearance.HoverFill.ColorMirror = 5888767
            ItemAppearance.HoverFill.ColorMirrorTo = 10807807
            ItemAppearance.HoverFill.GradientType = gtVertical
            ItemAppearance.HoverFill.GradientMirrorType = gtVertical
            ItemAppearance.HoverFill.BorderColor = 10079963
            ItemAppearance.HoverFill.Rounding = 0
            ItemAppearance.HoverFill.ShadowOffset = 0
            ItemAppearance.HoverFill.Glow = gmNone
            ItemAppearance.HoverSize = 20
            ItemAppearance.Splitter.Fill.Color = 11196927
            ItemAppearance.Splitter.Fill.ColorTo = 7257087
            ItemAppearance.Splitter.Fill.ColorMirror = clNone
            ItemAppearance.Splitter.Fill.ColorMirrorTo = clNone
            ItemAppearance.Splitter.Fill.GradientType = gtHorizontal
            ItemAppearance.Splitter.Fill.GradientMirrorType = gtSolid
            ItemAppearance.Splitter.Fill.BorderColor = 16765357
            ItemAppearance.Splitter.Fill.Rounding = 0
            ItemAppearance.Splitter.Fill.ShadowOffset = 0
            ItemAppearance.Splitter.Fill.Glow = gmNone
            ItemAppearance.Splitter.TextLocation = cpBottomCenter
            ItemAppearance.Splitter.TextFont.Charset = DEFAULT_CHARSET
            ItemAppearance.Splitter.TextFont.Color = clWindowText
            ItemAppearance.Splitter.TextFont.Height = -11
            ItemAppearance.Splitter.TextFont.Name = 'Tahoma'
            ItemAppearance.Splitter.TextFont.Style = []
            ItemAppearance.Splitter.ExpanderColor = 16773091
            ItemAppearance.Splitter.ExpanderDownColor = 7257087
            ItemAppearance.Splitter.ExpanderHoverColor = 11196927
            Header.Caption = 'Header'
            Header.Font.Charset = DEFAULT_CHARSET
            Header.Font.Color = 7485192
            Header.Font.Height = -13
            Header.Font.Name = 'Tahoma'
            Header.Font.Style = []
            Header.Fill.Color = 16773091
            Header.Fill.ColorTo = 16765615
            Header.Fill.ColorMirror = clNone
            Header.Fill.ColorMirrorTo = clNone
            Header.Fill.GradientType = gtVertical
            Header.Fill.GradientMirrorType = gtSolid
            Header.Fill.BorderColor = 16765615
            Header.Fill.Rounding = 0
            Header.Fill.ShadowOffset = 0
            Header.Fill.Glow = gmNone
            Header.Visible = False
            Header.Navigator.Visible = False
            Header.Navigator.Color = 16773091
            Header.Navigator.HintNext = 'Next Item'
            Header.Navigator.HintPrevious = 'Previous Item'
            Header.Navigator.HintNextPage = 'Next Page'
            Header.Navigator.HintPreviousPage = 'Previous Page'
            Header.Navigator.DisabledColor = clGray
            Header.Navigator.HoverColor = 11196927
            Header.Navigator.DownColor = 7257087
            Header.Navigator.BorderColor = clBlack
            Footer.Caption = 'Footer'
            Footer.Font.Charset = DEFAULT_CHARSET
            Footer.Font.Color = 7485192
            Footer.Font.Height = -13
            Footer.Font.Name = 'Tahoma'
            Footer.Font.Style = []
            Footer.Fill.Color = 16773091
            Footer.Fill.ColorTo = 16765615
            Footer.Fill.ColorMirror = clNone
            Footer.Fill.ColorMirrorTo = clNone
            Footer.Fill.GradientType = gtVertical
            Footer.Fill.GradientMirrorType = gtSolid
            Footer.Fill.BorderColor = 16765615
            Footer.Fill.Rounding = 0
            Footer.Fill.ShadowOffset = 0
            Footer.Fill.Glow = gmNone
            Footer.Visible = False
            Footer.Navigator.Visible = True
            Footer.Navigator.Color = 16773091
            Footer.Navigator.HintNext = 'Next Item'
            Footer.Navigator.HintPrevious = 'Previous Item'
            Footer.Navigator.HintNextPage = 'Next Page'
            Footer.Navigator.HintPreviousPage = 'Previous Page'
            Footer.Navigator.DisabledColor = clGray
            Footer.Navigator.HoverColor = 11196927
            Footer.Navigator.DownColor = 7257087
            Footer.Navigator.BorderColor = clBlack
            Fill.Color = 14464664
            Fill.ColorTo = 14464664
            Fill.ColorMirror = clNone
            Fill.ColorMirrorTo = clNone
            Fill.GradientType = gtVertical
            Fill.GradientMirrorType = gtSolid
            Fill.HatchStyle = HatchStyleBackwardDiagonal
            Fill.HatchStyleMirror = HatchStyleBackwardDiagonal
            Fill.BackGroundPicturePosition = ppStretched
            Fill.OpacityTo = 138
            Fill.BorderColor = clBlack
            Fill.BorderOpacity = 151
            Fill.BorderWidth = 0
            Fill.Rounding = 0
            Fill.ShadowOffset = 0
            Fill.Glow = gmNone
            DefaultHTMLText.Location = cpTopLeft
            DefaultHTMLText.Font.Charset = DEFAULT_CHARSET
            DefaultHTMLText.Font.Color = clWindowText
            DefaultHTMLText.Font.Height = -11
            DefaultHTMLText.Font.Name = 'Tahoma'
            DefaultHTMLText.Font.Style = []
            Rows = 1
            ZoomOnDblClick = False
            ZoomMode = zmAspectRatio
            ShowScrollBar = False
            Align = alClient
            TabOrder = 0
            ParentShowHint = False
            ShowHint = True
            TMSStyle = 4
          end
        end
      end
    end
    object RzPanel2: TRzPanel
      Left = 0
      Top = 0
      Width = 1300
      Height = 40
      Align = alTop
      BorderOuter = fsNone
      BorderSides = [sdLeft, sdTop, sdRight]
      BorderColor = 14671839
      BorderWidth = 1
      Color = clWhite
      DoubleBuffered = False
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -11
      Font.Name = 'Tahoma'
      Font.Style = []
      GradientColorStyle = gcsCustom
      GradientColorStart = 16643306
      GradientColorStop = 16049103
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 2
      Visible = False
      object RzLabel10: TRzLabel
        Left = 24
        Top = 8
        Width = 76
        Height = 26
        Caption = #28040#24687#27719#24635
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlue
        Font.Height = -19
        Font.Name = #24494#36719#38597#40657
        Font.Style = [fsBold]
        ParentFont = False
        Transparent = True
      end
    end
  end
  object TopPanel: TRzPanel
    Left = 0
    Top = 0
    Width = 1300
    Height = 60
    Align = alTop
    BorderOuter = fsNone
    Color = 16051944
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #24494#36719#38597#40657
    Font.Style = []
    GradientColorStyle = gcsCustom
    ParentFont = False
    TabOrder = 1
    object TopHeadPanel: TRzPanel
      Left = 0
      Top = 0
      Width = 1300
      Height = 60
      Align = alTop
      BorderOuter = fsNone
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #24494#36719#38597#40657
      Font.Style = []
      ParentFont = False
      TabOrder = 0
      Transparent = True
      object TopButtonPanel: TRzPanel
        Left = 0
        Top = 15
        Width = 1300
        Height = 45
        Align = alTop
        BorderOuter = fsNone
        BorderColor = 14671839
        BorderWidth = 1
        Color = clWhite
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = #24494#36719#38597#40657
        Font.Style = []
        ParentFont = False
        TabOrder = 0
        object Lab_1: TRzLabel
          Left = 675
          Top = 6
          Width = 38
          Height = 19
          Caption = 'Lab_1'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clRed
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
          Visible = False
        end
        object Lab_2: TRzLabel
          Left = 162
          Top = 6
          Width = 60
          Height = 19
          Caption = 'RzLabel2'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clRed
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Lab_3: TRzLabel
          Left = 306
          Top = 6
          Width = 60
          Height = 19
          Caption = 'RzLabel2'
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clRed
          Font.Height = -13
          Font.Name = #24494#36719#38597#40657
          Font.Style = [fsBold]
          ParentFont = False
        end
        object Btn_Type1: TAdvGlowButton
          Left = 571
          Top = 6
          Width = 100
          Height = 33
          Caption = '    '#36229' '#39044' '#31639
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 0
          Visible = False
          OnClick = Btn_Type1Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Type2: TAdvGlowButton
          Left = 56
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#36130#21153' '#23457#26680
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 1
          OnClick = Btn_Type2Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Type3: TAdvGlowButton
          Left = 200
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#20179#24211' '#23457#26680
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 2
          OnClick = Btn_Type3Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Type4: TAdvGlowButton
          Left = 665
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#20027#26009' '#23457#26680
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 3
          OnClick = Btn_Type4Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Type5: TAdvGlowButton
          Left = 809
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#24037#36164' '#32479#35745
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 4
          OnClick = Btn_Type5Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
        object Btn_Type6: TAdvGlowButton
          Left = 953
          Top = 6
          Width = 100
          Height = 33
          Caption = '   '#22806#24065' '#23457#26680
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clNavy
          Font.Height = -15
          Font.Name = #24494#36719#38597#40657
          Font.Style = []
          NotesFont.Charset = DEFAULT_CHARSET
          NotesFont.Color = clWindowText
          NotesFont.Height = -11
          NotesFont.Name = 'Tahoma'
          NotesFont.Style = []
          ParentFont = False
          TabOrder = 5
          OnClick = Btn_Type6Click
          Appearance.ColorChecked = clWhite
          Appearance.ColorCheckedTo = clWhite
          Appearance.ColorDisabled = clWhite
          Appearance.ColorDisabledTo = clWhite
          Appearance.ColorDown = clWhite
          Appearance.ColorDownTo = clWhite
          Appearance.ColorHot = clWhite
          Appearance.ColorHotTo = clWhite
          Appearance.ColorMirror = clWhite
          Appearance.ColorMirrorHot = clWhite
          Appearance.ColorMirrorHotTo = clWhite
          Appearance.ColorMirrorDown = clWhite
          Appearance.ColorMirrorDownTo = clWhite
          Appearance.ColorMirrorChecked = clWhite
          Appearance.ColorMirrorCheckedTo = clWhite
          Appearance.ColorMirrorDisabled = clWhite
          Appearance.ColorMirrorDisabledTo = clWhite
          Layout = blGlyphLeftAdjusted
        end
      end
      object TopBluePanel: TRzPanel
        Left = 0
        Top = 0
        Width = 1300
        Height = 15
        Align = alTop
        BorderOuter = fsNone
        BorderSides = [sdLeft, sdTop, sdRight]
        BorderColor = 14671839
        BorderWidth = 1
        Color = 16049103
        DoubleBuffered = False
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -11
        Font.Name = 'Tahoma'
        Font.Style = []
        GradientColorStyle = gcsCustom
        GradientColorStart = 16643306
        GradientColorStop = 16049103
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 1
        VisualStyle = vsGradient
      end
    end
  end
  object Timer1: TTimer
    Enabled = False
    Interval = 100
    OnTimer = Timer1Timer
    Left = 768
    Top = 79
  end
  object Timer2: TTimer
    Enabled = False
    Interval = 100
    OnTimer = Timer2Timer
    Left = 816
    Top = 84
  end
end
