unit BankModelConfig;

interface

uses
  Classes;

type
  TBankModelConfig = class
  private
    FName: string;
    FModelIndex: integer;
    FColBeginIndex: integer;
    FColSpanx: integer;
    FColSpany: integer;
    FParentTabIndex: integer;
    FIsShow: boolean;
    FTabDefaultShowName: string;
    FTabShowName: string;
    FTableDefaultShowName: string;
    FTableShowName: string;
  public
    property Name: string read FName write FName;
    property ModelIndex: integer read FModelIndex write FModelIndex;
    property ColBeginIndex: integer read FColBeginIndex write FColBeginIndex;
    property ColSpanx: integer read FColSpanx write FColSpanx;
    property ColSpany: integer read FColSpany write FColSpany;
    property ParentTabIndex: integer read FParentTabIndex write FParentTabIndex;
    property IsShow: boolean read FIsShow write FIsShow;
    property TabDefaultShowName: string read FTabDefaultShowName
      write FTabDefaultShowName;
    property TabShowName: string read FTabShowName write FTabShowName;
    property TableDefaultShowName: string read FTableDefaultShowName
      write FTableDefaultShowName;
    property TableShowName: string read FTableShowName write FTableShowName;
  end;

implementation

end.
