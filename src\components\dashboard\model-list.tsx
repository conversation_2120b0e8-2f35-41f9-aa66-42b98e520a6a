import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, Brain } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const models = [
  {
    id: 1,
    name: "AAAAAAAAAAAA",
    type: "隐私模型",
    tags: ["对话应用", "信息抽取"],
    updateTime: "2024-11-22",
    status: "已发布",
  },
  {
    id: 2,
    name: "BBBBBBBBBBBBB",
    type: "计算模型",
    tags: ["文本生成"],
    updateTime: "2024-11-20",
    status: "测试中",
  },
]

export function ModelList() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm rounded-lg">
      <CardHeader className="border-b border-[#E5E9EF] bg-[#F8FAFC] rounded-t-lg">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">模型列表</CardTitle>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              <Search className="h-4 w-4 mr-1" />
              搜索
            </Button>
            <Button variant="secondary" size="sm">
              <Filter className="h-4 w-4 mr-1" />
              筛选
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow className="bg-[#F8FAFC] hover:bg-[#F8FAFC]">
              <TableHead className="text-[#666]">模型名称</TableHead>
              <TableHead className="text-[#666]">类型</TableHead>
              <TableHead className="text-[#666]">标签</TableHead>
              <TableHead className="text-[#666]">更新时间</TableHead>
              <TableHead className="text-[#666]">状态</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {models.map((model) => (
              <TableRow key={model.id} className="hover:bg-[#F0F7FF]">
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 rounded-lg bg-[#E8F3FF] flex items-center justify-center">
                      <Brain className="h-5 w-5 text-[#1664FF]" />
                    </div>
                    <span className="font-medium text-[#1664FF]">{model.name}</span>
                  </div>
                </TableCell>
                <TableCell>{model.type}</TableCell>
                <TableCell>
                  <div className="flex gap-1">
                    {model.tags.map((tag) => (
                      <Badge key={tag} className="bg-[#F0F7FF] text-[#1664FF] hover:bg-[#E8F3FF]">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </TableCell>
                <TableCell className="text-[#666]">{model.updateTime}</TableCell>
                <TableCell>
                  <Badge 
                    className={
                      model.status === "已发布" 
                        ? "bg-[#E8F3FF] text-[#1664FF]" 
                        : "bg-[#FFF7E6] text-[#D46B08]"
                    }
                  >
                    {model.status}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}