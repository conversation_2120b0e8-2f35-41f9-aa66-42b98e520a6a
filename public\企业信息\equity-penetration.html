<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股权穿透分析 - 企业信息展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .equity-node {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .equity-node:hover {
            transform: scale(1.1);
        }
        .equity-line {
            stroke: #3B82F6;
            stroke-width: 2;
            fill: none;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="min-h-screen p-6">
        <!-- 导航栏 -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center text-blue-600 hover:text-blue-800 mr-4">
                        <i data-lucide="arrow-left" class="w-5 h-5 mr-1"></i>
                        返回企业概览
                    </a>
                    <h1 class="text-2xl font-semibold text-blue-800 flex items-center">
                        <i data-lucide="git-branch" class="mr-3 h-6 w-6 text-blue-600"></i>
                        股权穿透分析
                    </h1>
                </div>
                <div class="flex gap-2">
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="layers" class="w-4 h-4"></i>
                        切换视图
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="minimize" class="w-4 h-4"></i>
                        收起
                    </button>
                    <button class="flex items-center gap-2 px-4 py-2 bg-white border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        导出图片
                    </button>
                </div>
            </div>
            <p class="text-gray-600 mt-2">宁波创新科技股份有限公司 - 完整股权关系穿透图</p>
        </div>

        <!-- 控制面板 -->
        <div class="bg-white rounded-lg shadow-md border border-blue-100 p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <label class="text-sm font-medium text-gray-700">穿透层级：</label>
                        <select class="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">1层</option>
                            <option value="2" selected>2层</option>
                            <option value="3">3层</option>
                            <option value="4">4层</option>
                            <option value="all">全部</option>
                        </select>
                    </div>
                    <div class="flex items-center gap-2">
                        <label class="text-sm font-medium text-gray-700">最小持股比例：</label>
                        <select class="px-3 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="1">1%</option>
                            <option value="5" selected>5%</option>
                            <option value="10">10%</option>
                            <option value="20">20%</option>
                        </select>
                    </div>
                    <div class="flex items-center gap-2">
                        <input type="checkbox" id="showNaturalPerson" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <label for="showNaturalPerson" class="text-sm font-medium text-gray-700">显示自然人</label>
                    </div>
                </div>
                
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-3 h-3 rounded-full bg-blue-500"></div>
                        <span>目标企业</span>
                    </div>
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-3 h-3 rounded-full bg-green-500"></div>
                        <span>自然人</span>
                    </div>
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                        <span>法人股东</span>
                    </div>
                    <div class="flex items-center gap-2 text-sm">
                        <div class="w-3 h-3 rounded-full bg-purple-500"></div>
                        <span>投资基金</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 股权穿透图 -->
        <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6 mb-6" style="height: 70vh;">
            <div class="w-full h-full relative overflow-auto">
                <svg width="100%" height="100%" viewBox="0 0 1200 800" class="absolute inset-0">
                    <!-- 股权关系连线 -->
                    <defs>
                        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                            <polygon points="0 0, 10 3.5, 0 7" fill="#3B82F6" />
                        </marker>
                    </defs>
                    
                    <!-- 第一层连线 -->
                    <line x1="600" y1="400" x2="200" y2="200" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="600" y1="400" x2="400" y2="200" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="600" y1="400" x2="600" y2="200" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="600" y1="400" x2="800" y2="200" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="600" y1="400" x2="1000" y2="200" class="equity-line" marker-end="url(#arrowhead)"/>
                    
                    <!-- 第二层连线 -->
                    <line x1="400" y1="200" x2="300" y2="50" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="400" y1="200" x2="500" y2="50" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="1000" y1="200" x2="900" y2="50" class="equity-line" marker-end="url(#arrowhead)"/>
                    <line x1="1000" y1="200" x2="1100" y2="50" class="equity-line" marker-end="url(#arrowhead)"/>
                    
                    <!-- 持股比例标签 -->
                    <text x="400" y="300" text-anchor="middle" class="text-sm fill-blue-600 font-medium">35%</text>
                    <text x="300" y="300" text-anchor="middle" class="text-sm fill-blue-600 font-medium">25%</text>
                    <text x="600" y="300" text-anchor="middle" class="text-sm fill-blue-600 font-medium">12.5%</text>
                    <text x="700" y="300" text-anchor="middle" class="text-sm fill-blue-600 font-medium">10%</text>
                    <text x="800" y="300" text-anchor="middle" class="text-sm fill-blue-600 font-medium">17.5%</text>
                    
                    <text x="350" y="125" text-anchor="middle" class="text-xs fill-blue-600 font-medium">60%</text>
                    <text x="450" y="125" text-anchor="middle" class="text-xs fill-blue-600 font-medium">40%</text>
                    <text x="950" y="125" text-anchor="middle" class="text-xs fill-blue-600 font-medium">75%</text>
                    <text x="1050" y="125" text-anchor="middle" class="text-xs fill-blue-600 font-medium">25%</text>
                </svg>
                
                <!-- 企业节点 -->
                <div class="absolute" style="top: 350px; left: 500px;">
                    <div class="equity-node bg-blue-500 text-white rounded-lg p-4 shadow-lg border-4 border-blue-300 min-w-0" style="width: 200px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">宁波创新科技</div>
                            <div class="text-xs opacity-90">股份有限公司</div>
                            <div class="text-xs opacity-75 mt-1">目标企业</div>
                        </div>
                    </div>
                </div>
                
                <!-- 第一层股东节点 -->
                <div class="absolute" style="top: 150px; left: 100px;">
                    <div class="equity-node bg-green-500 text-white rounded-lg p-3 shadow-lg border-2 border-green-300" style="width: 150px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">张明</div>
                            <div class="text-xs opacity-90">自然人</div>
                            <div class="text-xs opacity-75">创始人</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 150px; left: 300px;">
                    <div class="equity-node bg-yellow-500 text-white rounded-lg p-3 shadow-lg border-2 border-yellow-300" style="width: 150px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">宁波投资</div>
                            <div class="text-xs opacity-90">有限公司</div>
                            <div class="text-xs opacity-75">法人股东</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 150px; left: 500px;">
                    <div class="equity-node bg-green-500 text-white rounded-lg p-3 shadow-lg border-2 border-green-300" style="width: 150px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">李华</div>
                            <div class="text-xs opacity-90">自然人</div>
                            <div class="text-xs opacity-75">联合创始人</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 150px; left: 700px;">
                    <div class="equity-node bg-purple-500 text-white rounded-lg p-3 shadow-lg border-2 border-purple-300" style="width: 150px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">上海科技基金</div>
                            <div class="text-xs opacity-90">投资基金</div>
                            <div class="text-xs opacity-75">财务投资者</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 150px; left: 900px;">
                    <div class="equity-node bg-yellow-500 text-white rounded-lg p-3 shadow-lg border-2 border-yellow-300" style="width: 150px;">
                        <div class="text-center">
                            <div class="font-bold text-sm">其他股东</div>
                            <div class="text-xs opacity-90">6位</div>
                            <div class="text-xs opacity-75">员工持股</div>
                        </div>
                    </div>
                </div>
                
                <!-- 第二层股东节点 -->
                <div class="absolute" style="top: 20px; left: 200px;">
                    <div class="equity-node bg-green-500 text-white rounded-lg p-2 shadow-lg border-2 border-green-300" style="width: 120px;">
                        <div class="text-center">
                            <div class="font-bold text-xs">王强</div>
                            <div class="text-xs opacity-90">自然人</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 20px; left: 400px;">
                    <div class="equity-node bg-yellow-500 text-white rounded-lg p-2 shadow-lg border-2 border-yellow-300" style="width: 120px;">
                        <div class="text-center">
                            <div class="font-bold text-xs">宁波实业</div>
                            <div class="text-xs opacity-90">有限公司</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 20px; left: 800px;">
                    <div class="equity-node bg-green-500 text-white rounded-lg p-2 shadow-lg border-2 border-green-300" style="width: 120px;">
                        <div class="text-center">
                            <div class="font-bold text-xs">刘敏</div>
                            <div class="text-xs opacity-90">自然人</div>
                        </div>
                    </div>
                </div>
                
                <div class="absolute" style="top: 20px; left: 1000px;">
                    <div class="equity-node bg-green-500 text-white rounded-lg p-2 shadow-lg border-2 border-green-300" style="width: 120px;">
                        <div class="text-center">
                            <div class="font-bold text-xs">陈晓</div>
                            <div class="text-xs opacity-90">自然人</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 穿透分析结果 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 最终受益人分析 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="crown" class="mr-2 h-5 w-5 text-blue-600"></i>
                    最终受益人
                </h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900">张明</div>
                            <div class="text-sm text-gray-600">直接+间接持股</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-blue-600">38.5%</div>
                            <div class="text-xs text-gray-500">实际控制人</div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900">李华</div>
                            <div class="text-sm text-gray-600">自然人股东</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-green-600">12.5%</div>
                            <div class="text-xs text-gray-500">主要股东</div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900">王强</div>
                            <div class="text-sm text-gray-600">间接持股</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-yellow-600">15.0%</div>
                            <div class="text-xs text-gray-500">通过宁波投资</div>
                        </div>
                    </div>
                    <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                            <div class="font-medium text-gray-900">其他自然人</div>
                            <div class="text-sm text-gray-600">分散持股</div>
                        </div>
                        <div class="text-right">
                            <div class="font-bold text-gray-600">24.0%</div>
                            <div class="text-xs text-gray-500">员工等</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 股权集中度分析 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="pie-chart" class="mr-2 h-5 w-5 text-blue-600"></i>
                    股权集中度
                </h3>
                <div class="space-y-4">
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm text-gray-600">第一大股东</span>
                            <span class="font-semibold">38.5%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full" style="width: 38.5%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm text-gray-600">前三大股东</span>
                            <span class="font-semibold">73.5%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 73.5%"></div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between mb-2">
                            <span class="text-sm text-gray-600">前五大股东</span>
                            <span class="font-semibold">90.0%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-600 h-2 rounded-full" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 p-3 bg-blue-50 rounded-lg">
                    <h4 class="font-medium text-blue-800 mb-2">集中度评估</h4>
                    <p class="text-sm text-gray-700">股权相对集中，第一大股东持股38.5%，为实际控制人。前三大股东持股73.5%，控制权稳定。</p>
                </div>
            </div>

            <!-- 关联关系网络 -->
            <div class="bg-white rounded-xl shadow-lg border border-blue-100 p-6">
                <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                    <i data-lucide="network" class="mr-2 h-5 w-5 text-blue-600"></i>
                    关联关系网络
                </h3>
                <div class="space-y-3">
                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="font-medium text-gray-900 mb-1">控制链条</div>
                        <div class="text-sm text-gray-600">
                            张明 → 宁波创新科技（35%）<br>
                            张明 → 宁波投资 → 宁波创新科技（15%）<br>
                            <span class="text-blue-600 font-medium">合计控制: 50%</span>
                        </div>
                    </div>
                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="font-medium text-gray-900 mb-1">投资关系</div>
                        <div class="text-sm text-gray-600">
                            关联企业: 宁波投资有限公司<br>
                            投资基金: 上海科技基金<br>
                            员工持股: 通过其他股东渠道
                        </div>
                    </div>
                    <div class="p-3 border border-gray-200 rounded-lg">
                        <div class="font-medium text-gray-900 mb-1">风险提示</div>
                        <div class="text-sm text-gray-600">
                            <span class="text-green-600">• 控制权稳定</span><br>
                            <span class="text-yellow-600">• 需关注关联交易</span><br>
                            <span class="text-blue-600">• 治理结构良好</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 添加节点点击事件
        document.querySelectorAll('.equity-node').forEach(node => {
            node.addEventListener('click', function() {
                // 模拟展开下级节点
                console.log('展开节点:', this.textContent.trim());
                
                // 添加选中状态
                document.querySelectorAll('.equity-node').forEach(n => n.classList.remove('ring-4', 'ring-blue-300'));
                this.classList.add('ring-4', 'ring-blue-300');
            });
        });

        // 图表缩放功能
        let scale = 1;
        const svg = document.querySelector('svg');
        
        document.addEventListener('wheel', function(e) {
            if (e.target.closest('svg')) {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                scale *= delta;
                scale = Math.max(0.5, Math.min(3, scale));
                svg.style.transform = `scale(${scale})`;
            }
        });
    </script>
</body>
</html> 