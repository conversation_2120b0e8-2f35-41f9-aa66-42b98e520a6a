<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三甲医院-医院基础信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .scroll-gallery {
            scrollbar-width: thin;
            scrollbar-color: #3b82f6 #f1f1f1;
        }
        .scroll-gallery::-webkit-scrollbar {
            height: 6px;
        }
        .scroll-gallery::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        .scroll-gallery::-webkit-scrollbar-thumb {
            background-color: #3b82f6;
            border-radius: 10px;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">宁波市第一医院基础信息</h1>
            <p class="mt-2 text-gray-600">全面展示医院概况、学科建设、人才队伍及荣誉资质等信息</p>
        </div>

        <!-- 基本概况区 -->
        <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
            <div class="md:flex">
                <div class="p-6 md:w-2/3">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">基本概况</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">医院名称</p>
                            <p class="font-medium">宁波市第一医院</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">隶属关系</p>
                            <p class="font-medium">宁波市卫生健康委员会</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">建院时间</p>
                            <p class="font-medium">1931年</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">医院等级</p>
                            <p class="font-medium">三级甲等</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">开放床位数</p>
                            <p class="font-medium">2,500张</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">职工总数</p>
                            <p class="font-medium">3,200人</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">年门急诊量</p>
                            <p class="font-medium">380万人次</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">官方网站</p>
                            <a href="https://www.nbdyyy.com" target="_blank" class="text-blue-600 hover:underline flex items-center">
                                www.nbdyyy.com
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="mt-6">
                        <p class="text-sm text-gray-500 mb-2">医院二维码</p>
                        <div class="w-32 h-32 bg-gray-100 flex items-center justify-center rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/3">
                    <div class="h-full bg-gray-100 flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-48 w-full text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 9l9-7 9 7v11a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 22V12h6v10" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细介绍区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">详细介绍</h2>
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">历史沿革</h3>
                    <p class="text-gray-700 indent-8">宁波市第一医院始建于1931年，前身为宁波公立医院，是宁波市历史最悠久的综合性医院之一。1954年更名为宁波市第一医院，1994年被评为三级甲等医院。经过90余年的发展，现已成为集医疗、教学、科研、预防、保健、康复为一体的现代化综合性医院。</p>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">办院宗旨</h3>
                    <p class="text-gray-700 indent-8">秉承"仁心仁术、济世惠民"的办院宗旨，坚持"以病人为中心"的服务理念，致力于为人民群众提供优质、高效、便捷的医疗服务。</p>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-800 mb-2">发展愿景</h3>
                    <p class="text-gray-700 indent-8">建设成为国内一流、国际知名的现代化研究型医院，打造长三角南翼区域医疗中心，为健康中国建设贡献力量。</p>
                </div>
            </div>
        </div>

        <!-- 学科与平台区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">学科与平台</h2>
            <div class="space-y-4">
                <!-- 国家级重点学科 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button onclick="toggleCollapse('national')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">国家级重点学科</span>
                        <svg id="national-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="national-content" class="px-4 py-3 border-t border-gray-200">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">心血管内科</a>
                                <div class="text-sm text-gray-500">王教授 | 2015年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">神经外科</a>
                                <div class="text-sm text-gray-500">李教授 | 2018年获批</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 省级重点学科 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button onclick="toggleCollapse('provincial')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">省级重点学科</span>
                        <svg id="provincial-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="provincial-content" class="px-4 py-3 border-t border-gray-200 hidden">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">消化内科</a>
                                <div class="text-sm text-gray-500">张教授 | 2016年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">呼吸内科</a>
                                <div class="text-sm text-gray-500">刘教授 | 2017年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">骨科</a>
                                <div class="text-sm text-gray-500">陈教授 | 2019年获批</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 市级重点学科 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button onclick="toggleCollapse('municipal')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">市级重点学科</span>
                        <svg id="municipal-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="municipal-content" class="px-4 py-3 border-t border-gray-200 hidden">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">内分泌科</a>
                                <div class="text-sm text-gray-500">赵教授 | 2018年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">肾内科</a>
                                <div class="text-sm text-gray-500">钱教授 | 2020年获批</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科研平台 -->
                <div class="border border-gray-200 rounded-lg overflow-hidden">
                    <button onclick="toggleCollapse('platform')" class="w-full px-4 py-3 bg-gray-50 text-left flex justify-between items-center">
                        <span class="font-medium">科研平台</span>
                        <svg id="platform-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 transform rotate-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>
                    <div id="platform-content" class="px-4 py-3 border-t border-gray-200 hidden">
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">浙江省心血管病重点实验室</a>
                                <div class="text-sm text-gray-500">王教授 | 2016年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">宁波市神经科学研究所</a>
                                <div class="text-sm text-gray-500">李教授 | 2018年获批</div>
                            </div>
                            <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                                <a href="#" class="text-blue-600 hover:underline">宁波市肿瘤精准治疗中心</a>
                                <div class="text-sm text-gray-500">张教授 | 2020年获批</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 人才队伍区 -->
        <div class="bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">人才队伍</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-1">两院院士</p>
                    <p class="text-2xl font-bold text-blue-600">2</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-1">国家杰青</p>
                    <p class="text-2xl font-bold text-green-600">5</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-1">万人计划</p>
                    <p class="text-2xl font-bold text-purple-600">3</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-1">高级职称</p>
                    <p class="text-2xl font-bold text-yellow-600">486</p>
                </div>
            </div>
            <div class="mt-6">
                <a href="#" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    查看人才库
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- 荣誉画廊区 -->
        <div class="bg-white rounded-xl shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800">荣誉画廊</h2>
                <a href="#" class="text-sm text-blue-600 hover:underline">查看更多荣誉</a>
            </div>
            <div class="scroll-gallery overflow-x-auto whitespace-nowrap pb-4">
                <div class="inline-block w-48 h-48 bg-gray-100 rounded-lg mr-4 overflow-hidden relative group">
                    <div class="h-full w-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center p-4">
                        <p class="text-white text-sm text-center">国家科技进步二等奖<br>2019年</p>
                    </div>
                </div>
                <div class="inline-block w-48 h-48 bg-gray-100 rounded-lg mr-4 overflow-hidden relative group">
                    <div class="h-full w-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center p-4">
                        <p class="text-white text-sm text-center">全国文明单位<br>2020年</p>
                    </div>
                </div>
                <div class="inline-block w-48 h-48 bg-gray-100 rounded-lg mr-4 overflow-hidden relative group">
                    <div class="h-full w-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center p-4">
                        <p class="text-white text-sm text-center">浙江省五一劳动奖状<br>2018年</p>
                    </div>
                </div>
                <div class="inline-block w-48 h-48 bg-gray-100 rounded-lg mr-4 overflow-hidden relative group">
                    <div class="h-full w-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center p-4">
                        <p class="text-white text-sm text-center">宁波市科技创新特别奖<br>2021年</p>
                    </div>
                </div>
                <div class="inline-block w-48 h-48 bg-gray-100 rounded-lg overflow-hidden relative group">
                    <div class="h-full w-full flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center p-4">
                        <p class="text-white text-sm text-center">全国卫生系统先进集体<br>2017年</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 图片查看弹窗 -->
    <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50 flex items-center justify-center">
        <div class="relative max-w-4xl w-full">
            <button onclick="closeModal('imageModal')" class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full p-2 hover:bg-opacity-75">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
            <img id="modalImage" src="" alt="荣誉证书" class="w-full h-auto max-h-screen rounded-lg">
            <div class="mt-2 text-center text-white">
                <p id="imageTitle" class="text-lg font-medium"></p>
                <p id="imageDate" class="text-sm"></p>
            </div>
        </div>
    </div>

    <script>
        // 折叠面板切换
        function toggleCollapse(id) {
            const content = document.getElementById(`${id}-content`);
            const icon = document.getElementById(`${id}-icon`);
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.remove('rotate-0');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
                icon.classList.add('rotate-0');
            }
        }

        // 打开弹窗
        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 关闭弹窗
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('imageModal');
            }
        });

        // 绑定荣誉图片点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const galleryItems = document.querySelectorAll('.scroll-gallery > div');
            galleryItems.forEach(item => {
                item.addEventListener('click', function() {
                    const title = this.querySelector('.group-hover\\:opacity-100 p').textContent.split('\n')[0];
                    const date = this.querySelector('.group-hover\\:opacity-100 p').textContent.split('\n')[1];
                    
                    document.getElementById('modalImage').src = 'https://source.unsplash.com/800x600/?certificate,award';
                    document.getElementById('imageTitle').textContent = title;
                    document.getElementById('imageDate').textContent = date;
                    
                    openModal('imageModal');
                });
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('imageModal').classList.contains('hidden')) {
                    closeModal('imageModal');
                }
            });
        });
    </script>
</body>
</html>