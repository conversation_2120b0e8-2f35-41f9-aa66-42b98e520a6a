'use client'

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Send, Brain, Headset,Sparkles, Plus, MessageSquare, FileText, ExternalLink, Play, CheckCircle, Clock, Zap, Download, File, Loader2, ChevronDown, ChevronUp, Lock, Lightbulb, User, Bot, Copy, ThumbsUp, ThumbsDown, RotateCcw } from "lucide-react"
import { cn } from '@/lib/utils'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import Image from 'next/image'

// 导入图标
import thinkIcon from '@/assets/images/think.png'
import webIcon from '@/assets/images/web.png'

// 访问密钥 - 可以根据需要修改
const ACCESS_KEY = 'zscq'

// 优化的markdown渲染 - 更好的换行和格式处理
const renderMarkdown = (content: string) => {
  if (!content) return content

  // 处理行内元素
  let processed = content
    // 代码块（需要在其他处理之前）
    .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-3 rounded-lg my-3 overflow-x-auto"><code class="text-sm font-mono whitespace-pre-wrap">$1</code></pre>')
    // 行内代码
    .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>')
    // 粗体
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
    // 斜体
    .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
    // Markdown链接
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline" target="_blank" rel="noopener noreferrer">$1</a>')
    // HTML链接（保持原有的HTML链接不变）
    .replace(/<a\s+([^>]*?)>/g, '<a $1 class="text-blue-600 hover:text-blue-800 underline">')

  // 按行处理，支持嵌套列表
  const lines = processed.split('\n')
  let result = ''
  let currentParagraph = ''
  let inOrderedList = false
  let inUnorderedList = false
  let lastWasOrderedItem = false

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    const trimmed = line.trim()

    // 空行处理
    if (trimmed === '') {
      if (currentParagraph) {
        result += `<p class="mb-3 leading-normal text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }
      // 保留空行作为段落间距
      result += '<div class="mb-2"></div>'
      continue
    }

    // 标题处理
    if (trimmed.startsWith('### ')) {
      if (currentParagraph) {
        result += `<p class="mb-3 leading-normal text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }
      if (inOrderedList) { result += '</ol>'; inOrderedList = false }
      if (inUnorderedList) { result += '</ul>'; inUnorderedList = false }
      result += `<h3 class="text-lg font-semibold mt-6 mb-3 text-gray-800 break-words">${trimmed.substring(4)}</h3>`
      lastWasOrderedItem = false
      continue
    }
    if (trimmed.startsWith('## ')) {
      if (currentParagraph) {
        result += `<p class="mb-3 leading-normal text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }
      if (inOrderedList) { result += '</ol>'; inOrderedList = false }
      if (inUnorderedList) { result += '</ul>'; inUnorderedList = false }
      result += `<h2 class="text-xl font-semibold mt-6 mb-3 text-gray-800 break-words">${trimmed.substring(3)}</h2>`
      lastWasOrderedItem = false
      continue
    }
    if (trimmed.startsWith('# ')) {
      if (currentParagraph) {
        result += `<p class="mb-3 leading-normal text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }
      if (inOrderedList) { result += '</ol>'; inOrderedList = false }
      if (inUnorderedList) { result += '</ul>'; inUnorderedList = false }
      result += `<h1 class="text-2xl font-bold mt-8 mb-4 text-gray-800 break-words">${trimmed.substring(2)}</h1>`
      lastWasOrderedItem = false
      continue
    }

    // 数字列表项处理
    if (/^\s*\d+\.\s/.test(line)) {
      if (currentParagraph) {
        result += `<p class="mb-3 leading-normal text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }

      // 如果当前在无序列表中，且上一个不是有序列表项，则关闭无序列表
      if (inUnorderedList && !lastWasOrderedItem) {
        result += '</ul>'
        inUnorderedList = false
      }

      if (!inOrderedList) {
        result += '<ol class="list-decimal pl-6 mb-4 space-y-2">'
        inOrderedList = true
      }

      const content = line.replace(/^\s*\d+\.\s/, '').trim()
      result += `<li class="text-gray-700 leading-relaxed break-words">${content}</li>`
      lastWasOrderedItem = true
      continue
    }

    // 无序列表项处理
    if (/^\s*[-*•]\s/.test(line)) {
      if (currentParagraph) {
        result += `<p class="mb-4 leading-relaxed text-gray-700 break-words">${currentParagraph.trim()}</p>`
        currentParagraph = ''
      }

      // 如果在有序列表中，开始嵌套的无序列表
      if (inOrderedList && !inUnorderedList) {
        result += '<ul class="list-disc pl-6 mb-3 mt-2 space-y-1">'
        inUnorderedList = true
      } else if (!inUnorderedList) {
        result += '<ul class="list-disc pl-6 mb-4 space-y-2">'
        inUnorderedList = true
      }

      const content = line.replace(/^\s*[-*•]\s/, '').trim()
      result += `<li class="text-gray-700 leading-relaxed break-words">${content}</li>`
      lastWasOrderedItem = false
      continue
    }

    // 普通文本行 - 结束所有列表，累积到段落
    if (inUnorderedList) { result += '</ul>'; inUnorderedList = false }
    if (inOrderedList) { result += '</ol>'; inOrderedList = false }
    lastWasOrderedItem = false

    if (currentParagraph) {
      currentParagraph += '<br>' + trimmed  // 使用<br>保留换行
    } else {
      currentParagraph = trimmed
    }
  }

  // 处理最后的内容
  if (currentParagraph) {
    result += `<p class="mb-4 leading-relaxed text-gray-700 break-words">${currentParagraph.trim()}</p>`
  }
  if (inUnorderedList) result += '</ul>'
  if (inOrderedList) result += '</ol>'

  return result
}

// 解析think标签内容
const parseThinkContent = (content: string) => {
  const thinkRegex = /<think>([\s\S]*?)<\/think>/g
  const thinks: Array<{content: string, index: number}> = []
  let match
  let lastIndex = 0
  let parts: Array<{type: 'text' | 'think', content: string, index?: number}> = []
  
  while ((match = thinkRegex.exec(content)) !== null) {
    // 添加think之前的文本
    if (match.index > lastIndex) {
      const beforeText = content.slice(lastIndex, match.index).trim()
      if (beforeText) {
        parts.push({type: 'text', content: beforeText})
      }
    }
    
    // 添加think内容
    const thinkContent = match[1].trim()
    if (thinkContent) {
      const thinkIndex = thinks.length
      thinks.push({content: thinkContent, index: thinkIndex})
      parts.push({type: 'think', content: thinkContent, index: thinkIndex})
    }
    
    lastIndex = match.index + match[0].length
  }
  
  // 添加最后剩余的文本
  if (lastIndex < content.length) {
    const remainingText = content.slice(lastIndex).trim()
    if (remainingText) {
      parts.push({type: 'text', content: remainingText})
    }
  }
  
  // 如果没有think内容，返回原始内容
  if (thinks.length === 0) {
    return {hasThink: false, parts: [{type: 'text' as const, content}], thinks: []}
  }
  
  return {hasThink: true, parts, thinks}
}

interface WorkflowNode {
  id: string
  node_id: string
  node_type: string
  title: string
  index: number
  status: 'waiting' | 'running' | 'completed' | 'failed'
  elapsed_time?: number
  created_at?: number
  finished_at?: number
  tokens?: number
}

interface MessageFile {
  id?: string
  filename: string
  url: string
  mime_type: string
  size: number
  type: string
  extension?: string
}

interface Message {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  conversation_id?: string
  message_id?: string
  metadata?: {
    retriever_resources?: Array<{
      position: number
      dataset_name: string
      document_name: string
      score: number
      content: string
      hit_count: number
      word_count: number
    }>
    usage?: {
      total_tokens: number
      prompt_tokens: number
      completion_tokens: number
    }
    thinking_time?: number
    elapsed_time?: number
  }
  suggested_questions?: string[]
  workflow_nodes?: WorkflowNode[]
  workflow_running?: boolean
  files?: MessageFile[]
}

// 访问权限验证组件
function AccessDenied() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-b from-red-50 to-red-100">
      <Card className="w-full max-w-md mx-4">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <Lock className="w-8 h-8 text-red-600" />
          </div>
          <CardTitle className="text-red-800">访问受限</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-red-600">
            此页面需要有效的访问密钥才能访问。
          </p>
          <p className="text-sm text-gray-600">
            请确保URL中包含正确的key参数。
          </p>
          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            示例: /agent/difyznwd?key=your_access_key
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default function DifyChatPage() {
  const searchParams = useSearchParams()
  const providedKey = searchParams.get('key')
  
  // 聊天相关状态
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [conversationMessages, setConversationMessages] = useState<{[key: string]: Message[]}>({})
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [suggestedQuestions, setSuggestedQuestions] = useState<string[]>([])
  const [expandedThinks, setExpandedThinks] = useState<Set<string>>(new Set())
  const [currentTime, setCurrentTime] = useState(Date.now())

  // 深度思考状态 - 默认启用
  const [deepThinkingEnabled, setDeepThinkingEnabled] = useState(false)

  // 联网搜索状态 - 默认不启用
  const [webSearchEnabled, setWebSearchEnabled] = useState(true)

  // 消息操作状态
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null)
  const [messageRatings, setMessageRatings] = useState<{[key: string]: 'up' | 'down' | undefined}>({})

  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const userId = 'user-123' // 固定用户ID

  // 计算思考耗时
  const getThinkingTime = (message: Message) => {
    // 优先使用已保存的纯思考时间
    if (message.metadata?.thinking_time) {
      return message.metadata.thinking_time.toFixed(1);
    }
    
    // 如果正在思考阶段，显示估算时间
    const parsed = parseThinkContent(message.content)
    if (parsed.hasThink && !message.metadata?.thinking_time && isLoading) {
      const estimatedTime = (currentTime - message.timestamp.getTime()) / 1000;
      return Math.min(estimatedTime, 30).toFixed(1);
    }
    
    // 备用方案
    if (message.metadata?.elapsed_time) {
      return (message.metadata.elapsed_time / 1000).toFixed(1);
    }
    
    return '0.0';
  }

  // 切换think展开状态
  const toggleThink = (messageId: string, thinkIndex: number) => {
    const key = `${messageId}-${thinkIndex}`
    setExpandedThinks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(key)) {
        newSet.delete(key)
      } else {
        newSet.add(key)
      }
      return newSet
    })
    
    // 触发滚动调整
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('chatContentHeightChanged'))
    }, 100)
  }

  // 访问权限验证
  const hasAccess = providedKey === ACCESS_KEY
  
  // 获取当前会话的消息 - 智能查找消息，处理状态切换过程中的消息显示
  const currentMessages = useMemo(() => {
    // 优先查找当前会话ID的消息
    if (currentConversationId && conversationMessages[currentConversationId]) {
      const messages = conversationMessages[currentConversationId]
      console.log('使用当前会话消息:', { currentConversationId, messageCount: messages.length })
      return messages
    }
    
    // 如果当前会话没有消息，查找temp消息
    if (conversationMessages['temp'] && conversationMessages['temp'].length > 0) {
      const messages = conversationMessages['temp']
      console.log('使用临时消息:', { messageCount: messages.length })
      return messages
    }
    
    // 如果没有找到任何消息，尝试查找最新的消息（用于状态切换期间）
    const allKeys = Object.keys(conversationMessages)
    if (allKeys.length > 0) {
      // 排除temp，找到最新的会话
      const nonTempKeys = allKeys.filter(key => key !== 'temp')
      if (nonTempKeys.length > 0) {
        const latestKey = nonTempKeys[nonTempKeys.length - 1]
        const messages = conversationMessages[latestKey] || []
        console.log('使用最新会话消息作为备选:', { latestKey, messageCount: messages.length })
        return messages
      }
    }
    
    console.log('没有找到任何消息')
    return []
  }, [currentConversationId, conversationMessages])

  // 实时更新时间，用于动态显示思考时间
  useEffect(() => {
    if (isLoading && currentMessages.length > 0) {
      const lastMessage = currentMessages[currentMessages.length - 1]
      const parsed = parseThinkContent(lastMessage.content || '')
      
      if (parsed.hasThink && !lastMessage.metadata?.thinking_time) {
        const timer = setInterval(() => {
          setCurrentTime(Date.now());
        }, 100);
        
        return () => clearInterval(timer);
      }
    }
  }, [isLoading, currentMessages])

  // 自动滚动到底部
  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollContainer = scrollAreaRef.current
      scrollContainer.scrollTop = scrollContainer.scrollHeight
    }
  }, [currentMessages])

  // 当思考完成后，自动收起think内容
  useEffect(() => {
    if (currentMessages.length > 0) {
      const lastMessage = currentMessages[currentMessages.length - 1]
      if (lastMessage.role === 'assistant' && lastMessage.content && !isLoading) {
        const parsed = parseThinkContent(lastMessage.content)
        if (parsed.hasThink && parsed.thinks.length > 0) {
          // 思考完成后，确保think标签是收起状态
          const firstThinkKey = `${lastMessage.id}-0`
          setExpandedThinks(prev => {
            const newSet = new Set(prev)
            newSet.delete(firstThinkKey) // 收起think内容
            return newSet
          })
        }
      }
    }
  }, [currentMessages.length, isLoading])

  // 聚焦输入框
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  // 去重文件列表
  const deduplicateFiles = (files: MessageFile[]): MessageFile[] => {
    const seen = new Set<string>()
    return files.filter(file => {
      // 使用文件名和大小作为唯一标识
      const key = `${file.filename}_${file.size}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  // 从文本中移除文件下载链接
  const removeFileLinksFromText = (content: string): string => {
    // 移除Markdown格式的文件链接：[文件名.ext](/files/...)
    return content.replace(/\[([^\]]+\.(docx?|xlsx?|pptx?|pdf|txt|zip|rar))\]\([^)]+\)/gi, '')
      .replace(/文件下载：\s*/g, '') // 移除"文件下载："前缀
      .replace(/\n\s*\n/g, '\n') // 清理多余的空行
      .trim()
  }

  // 加载指定会话的消息
  const loadConversationMessages = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/dify-chat/messages?conversation_id=${conversationId}&user=${userId}&limit=50`)
      if (response.ok) {
        const data = await response.json()
        console.log('获取到的历史消息:', data)
        
        const messages: Message[] = []
        
        if (data.data && Array.isArray(data.data)) {
          data.data.forEach((item: any) => {
            // 添加用户消息
            if (item.query) {
              messages.push({
                id: `${item.id}_user`,
                role: 'user',
                content: item.query,
                timestamp: new Date(item.created_at * 1000),
                conversation_id: conversationId,
                message_id: item.id
              })
            }
            
            // 添加助手消息
            if (item.answer) {
              // 清理历史消息中的文件链接
              const cleanedAnswer = removeFileLinksFromText(item.answer)
              
              // 处理历史消息中的文件
              let messageFiles: MessageFile[] = []
              if (item.message_files && item.message_files.length > 0) {
                messageFiles = item.message_files
              }
              
              messages.push({
                id: `${item.id}_assistant`,
                role: 'assistant',
                content: cleanedAnswer,
                timestamp: new Date(item.created_at * 1000),
                conversation_id: conversationId,
                message_id: item.id,
                metadata: {
                  retriever_resources: item.retriever_resources || [],
                  usage: item.usage
                },
                files: deduplicateFiles(messageFiles)
              })
            }
          })
        }
        
        // 按时间排序
        messages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime())
        
        setConversationMessages(prev => ({
          ...prev,
          [conversationId]: messages
        }))
      } else {
        console.error('获取历史消息失败:', response.status)
      }
    } catch (error) {
      console.error('加载会话消息失败:', error)
    }
  }

  // 获取建议问题
  const loadSuggestedQuestions = async (messageId: string) => {
    try {
      const response = await fetch(`/api/dify-chat/suggested?message_id=${messageId}&user=${userId}`)
      if (response.ok) {
        const data = await response.json()
        setSuggestedQuestions(data.data || [])
      }
    } catch (error) {
      console.error('获取建议问题失败:', error)
    }
  }

  // 创建新会话
  const createNewConversation = () => {
    setCurrentConversationId(null)
    setError(null)
    setSuggestedQuestions([])
    
    // 清理临时消息
    setConversationMessages(prev => {
      const newMessages = { ...prev }
      delete newMessages['temp']
      return newMessages
    })
    
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 获取文件图标
  const getFileIcon = (mimeType: string, extension?: string) => {
    if (mimeType.includes('image/')) return '🖼️'
    if (mimeType.includes('pdf')) return '📄'
    if (mimeType.includes('word') || extension === '.docx' || extension === '.doc') return '📝'
    if (mimeType.includes('excel') || extension === '.xlsx' || extension === '.xls') return '📊'
    if (mimeType.includes('powerpoint') || extension === '.pptx' || extension === '.ppt') return '📋'
    if (mimeType.includes('text/')) return '📄'
    return '📁'
  }

  // 处理文件下载
  const handleFileDownload = async (file: MessageFile) => {
    try {
      // 构建完整的下载URL
      const downloadUrl = file.url.startsWith('http') 
        ? file.url 
        : `http://111.229.163.150${file.url}`
      
      console.log('下载文件:', file.filename, downloadUrl)
      
      // 创建一个临时的a标签来触发下载
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = file.filename
      link.target = '_blank'
      
      // 添加到DOM并点击
      document.body.appendChild(link)
      link.click()
      
      // 清理
      document.body.removeChild(link)
    } catch (error) {
      console.error('下载文件失败:', error)
      setError('文件下载失败，请稍后重试')
    }
  }

  // 复制消息内容
  const handleCopy = async (content: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(content)
      setCopiedMessageId(messageId)
      setTimeout(() => setCopiedMessageId(null), 2000)
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  // 处理消息评分
  const handleRating = (messageId: string, rating: 'up' | 'down') => {
    setMessageRatings(prev => ({
      ...prev,
      [messageId]: prev[messageId] === rating ? undefined : rating
    }))
  }

  // 重新生成消息
  const handleRetry = (messageId: string) => {
    // 找到该消息在当前消息列表中的位置
    const messageIndex = currentMessages.findIndex(msg => msg.id === messageId)
    if (messageIndex > 0) {
      // 获取用户的问题（前一条消息）
      const userMessage = currentMessages[messageIndex - 1]
      if (userMessage && userMessage.role === 'user') {
        // 重新发送用户的问题
        handleSendMessage(userMessage.content)
      }
    }
  }

  // 格式化时间
  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 渲染文件列表
  const renderMessageFiles = (files: MessageFile[]) => {
    if (!files || files.length === 0) return null
    
    // 再次确保去重
    const uniqueFiles = deduplicateFiles(files)
    
    return (
      <div className="mt-3 p-2 sm:p-3 bg-purple-50 rounded-lg border border-purple-200">
        <div className="flex items-center gap-2 mb-2">
          <File size={14} className="text-purple-600 sm:size-4" />
          <span className="text-xs sm:text-sm font-medium text-purple-800">
            附件文件 {uniqueFiles.length > 1 && `(${uniqueFiles.length}个)`}
          </span>
        </div>
        <div className="space-y-1 sm:space-y-2">
          {uniqueFiles.map((file, index) => (
            <div key={`${file.filename}_${file.size}_${index}`} className="flex items-center justify-between p-2 sm:p-3 bg-white rounded-lg border border-purple-100 hover:bg-purple-50 transition-colors">
              <div className="flex items-center gap-2 sm:gap-3 flex-1 min-w-0">
                <span className="text-lg sm:text-2xl flex-shrink-0">{getFileIcon(file.mime_type, file.extension)}</span>
                <div className="flex-1 min-w-0">
                  <div className="text-xs sm:text-sm font-medium text-gray-800 truncate">
                    {file.filename}
                  </div>
                  <div className="text-xs text-gray-500">
                    <span className="block sm:inline">{formatFileSize(file.size)}</span>
                    <span className="hidden sm:inline"> • </span>
                    <span className="block sm:inline truncate">{file.mime_type}</span>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1 sm:gap-2 text-purple-600 border-purple-200 hover:bg-purple-100 hover:border-purple-300 transition-colors text-xs sm:text-sm h-8 sm:h-9 px-2 sm:px-3"
                onClick={() => handleFileDownload(file)}
              >
                <Download size={14} className="sm:size-4" />
                <span className="hidden sm:inline">下载</span>
              </Button>
            </div>
          ))}
        </div>
      </div>
    )
  }

  // 修改发送消息方法，处理文件信息
  const handleSendMessage = async (messageText?: string) => {
    const queryText = messageText || input.trim()
    if (!queryText) return

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: queryText,
      timestamp: new Date(),
      conversation_id: currentConversationId || undefined
    }

    // 添加用户消息到当前会话
    let conversationId = currentConversationId || 'temp'
    setConversationMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), userMessage]
    }))

    setInput('')
    setIsLoading(true)
    setError(null)
    setSuggestedQuestions([])

    // 创建助手消息占位符
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: Message = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      conversation_id: currentConversationId || undefined,
      workflow_nodes: [],
      workflow_running: false,
      files: []
    }
    
    console.log('创建助手消息占位符 - ID:', assistantMessageId, '会话ID:', conversationId)
    
    setConversationMessages(prev => ({
      ...prev,
      [conversationId]: [...(prev[conversationId] || []), assistantMessage]
    }))

    try {
      const requestBody = {
        query: queryText,
        user: userId,
        deep_thinking: deepThinkingEnabled, // 传递深度思考状态
        web_search: webSearchEnabled, // 传递联网搜索状态
        ...(currentConversationId && { conversation_id: currentConversationId })
      }

      const response = await fetch('/api/dify-chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody),
      })
      
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }

      // 处理流式响应
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()
      
      if (reader) {
        let buffer = ''
        let accumulatedAnswer = ''
        let newConversationId = currentConversationId
        let finalMessageId = ''
        let messageMetadata: any = null
        let messageFiles: MessageFile[] = []
        let workflowNodes: WorkflowNode[] = []
        let workflowRunning = false
        let isNewConversation = !currentConversationId
        
        while (true) {
          const { done, value } = await reader.read()
          if (done) break
          
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk
          
          // 处理完整的SSE事件
          const events = buffer.split('\n\n')
          buffer = events.pop() || ''
          
          for (const event of events) {
            if (!event.trim() || !event.startsWith('data:')) continue
            
            try {
              const jsonStr = event.substring(5).trim()
              const data = JSON.parse(jsonStr)
              
              console.log("收到Dify数据:", data)
              
                            // 获取会话ID（用于新会话）
              if (data.conversation_id && !newConversationId) {
                newConversationId = data.conversation_id
                
                // 将临时消息复制到新会话ID下，但不立即切换界面显示
                if (conversationId === 'temp' && newConversationId) {
                  console.log('复制临时消息到新会话:', newConversationId)
                  
                  // 立即更新conversationId变量以确保后续消息更新正确
                  conversationId = newConversationId
                  
                  setConversationMessages(prev => {
                    const tempMessages = prev['temp'] || []
                    console.log('复制临时消息详情:', tempMessages.map(msg => ({
                      id: msg.id,
                      role: msg.role,
                      content: msg.content.substring(0, 50),
                      workflow_nodes: msg.workflow_nodes?.length || 0,
                      workflow_running: msg.workflow_running
                    })))
                    
                    const newMessages = { ...prev }
                    // 复制到新会话ID下，但保留temp消息让界面继续显示
                    if (newConversationId) {
                      newMessages[newConversationId] = tempMessages.map(msg => ({
                        ...msg,
                        conversation_id: newConversationId || undefined
                      }))
                    }
                    return newMessages
                  })
                  
                  // 延迟切换界面显示的会话ID，确保消息已经完全复制
                  setTimeout(() => {
                    console.log('延迟切换界面显示到新会话ID:', newConversationId)
                    setCurrentConversationId(newConversationId)
                    
                    // 再延迟清理temp消息
                    setTimeout(() => {
                      setConversationMessages(prev => {
                        const newMessages = { ...prev }
                        delete newMessages['temp']
                        return newMessages
                      })
                    }, 50)
                  }, 50) // 短暂延迟，让消息复制完成
                }
                
                // 新会话创建完成
                if (isNewConversation) {
                  console.log('新会话创建完成')
                }
              }
              
              // 获取消息ID
              if (data.message_id) {
                finalMessageId = data.message_id
              }
              
              // 处理工作流开始事件
              if (data.event === 'workflow_started') {
                workflowRunning = true
                workflowNodes = []
                
                const targetConvId = newConversationId || conversationId
                console.log('工作流开始 - 会话ID:', targetConvId, '助手消息ID:', assistantMessageId)
                
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的工作流状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_running: true,
                            workflow_nodes: []
                          }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的工作流状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_running: true,
                            workflow_nodes: []
                          }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理节点开始事件
              if (data.event === 'node_started') {
                const nodeData = data.data
                const newNode: WorkflowNode = {
                  id: nodeData.id,
                  node_id: nodeData.node_id,
                  node_type: nodeData.node_type,
                  title: nodeData.title,
                  index: nodeData.index,
                  status: 'running',
                  created_at: nodeData.created_at
                }
                
                // 更新现有节点为等待状态，新节点为运行状态
                workflowNodes = workflowNodes.map(node => ({
                  ...node,
                  status: node.status === 'running' ? 'completed' : node.status
                }))
                workflowNodes.push(newNode)
                
                const targetConvId = newConversationId || conversationId
                console.log('节点开始 - 会话ID:', targetConvId, '节点:', newNode.title, '总节点数:', workflowNodes.length)
                
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的节点状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, workflow_nodes: [...workflowNodes] }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的节点状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, workflow_nodes: [...workflowNodes] }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理节点完成事件
              if (data.event === 'node_finished') {
                const nodeData = data.data
                
                // 只从outputs.files中获取文件信息，避免重复
                if (nodeData.outputs && nodeData.outputs.files && nodeData.outputs.files.length > 0) {
                  const newFiles = nodeData.outputs.files.filter((file: any) => 
                    !messageFiles.some(existing => 
                      existing.filename === file.filename && existing.size === file.size
                    )
                  )
                  messageFiles = [...messageFiles, ...newFiles]
                  console.log('节点完成，发现新文件:', newFiles)
                }
                
                workflowNodes = workflowNodes.map(node => 
                  node.id === nodeData.id 
                    ? {
                        ...node,
                        status: nodeData.status === 'succeeded' ? 'completed' : 'failed',
                        elapsed_time: nodeData.elapsed_time,
                        finished_at: nodeData.finished_at,
                        tokens: nodeData.execution_metadata?.total_tokens
                      }
                    : node
                )
                
                const targetConvId = newConversationId || conversationId
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话的节点和文件状态
                  if (prev[targetConvId]) {
                    newMessages[targetConvId] = prev[targetConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_nodes: [...workflowNodes],
                            files: [...messageFiles]
                          }
                        : msg
                    )
                  }
                  
                  // 同时更新temp消息的节点和文件状态（如果存在）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { 
                            ...msg, 
                            workflow_nodes: [...workflowNodes],
                            files: [...messageFiles]
                          }
                        : msg
                    )
                  }
                  
                  return newMessages
                })
              }
              
              // 处理增量消息内容
              if (data.event === 'message' && data.answer !== undefined) {
                accumulatedAnswer += data.answer
                
                // 确保使用正确的会话ID - 优先使用新获取的会话ID
                const currentConvId = newConversationId || conversationId
                console.log('更新消息内容:', {
                  targetConvId: currentConvId,
                  contentLength: accumulatedAnswer.length,
                  newConversationId,
                  originalConversationId: conversationId,
                  assistantMessageId
                })
                
                // 实时更新助手消息 - 同时更新temp和新会话（如果存在）确保界面显示正确
                setConversationMessages(prev => {
                  const newMessages = { ...prev }
                  
                  // 更新目标会话ID的消息
                  if (prev[currentConvId]) {
                    newMessages[currentConvId] = prev[currentConvId].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedAnswer }
                        : msg
                    )
                  }
                  
                  // 如果有新会话ID且不同于原始会话ID，也同时更新temp消息（为了界面显示）
                  if (newConversationId && conversationId === 'temp' && prev['temp']) {
                    newMessages['temp'] = prev['temp'].map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedAnswer }
                        : msg
                    )
                  }
                  
                  console.log('消息更新结果:', {
                    targetConvId: currentConvId,
                    updatedTemp: newConversationId && conversationId === 'temp',
                    tempMessageCount: newMessages['temp']?.length,
                    targetMessageCount: newMessages[currentConvId]?.length
                  })
                  
                  return newMessages
                })
              }
              
              // 处理消息结束事件
              if (data.event === 'message_end') {
                console.log("消息结束，最终答案:", accumulatedAnswer)
                messageMetadata = data.metadata
                workflowRunning = false
                
                // 清理文本内容，移除文件下载链接
                const cleanedContent = removeFileLinksFromText(accumulatedAnswer)
                
                // 去重文件列表
                const uniqueFiles = deduplicateFiles(messageFiles)
                
                console.log('最终文件列表（去重后）:', uniqueFiles)
                
                // 更新最终消息
                const targetConvId = newConversationId || conversationId
                setConversationMessages(prev => ({
                  ...prev,
                  [targetConvId]: prev[targetConvId]?.map(msg => 
                    msg.id === assistantMessageId 
                      ? { 
                          ...msg, 
                          content: cleanedContent, // 使用清理后的内容
                          metadata: messageMetadata,
                          message_id: finalMessageId,
                          workflow_running: false,
                          workflow_nodes: workflowNodes.map(node => ({
                            ...node,
                            status: node.status === 'running' ? 'completed' : node.status
                          })),
                          files: uniqueFiles // 使用去重后的文件列表
                        }
                      : msg
                  ) || []
                }))
                
                // 获取建议问题
                if (finalMessageId) {
                  setTimeout(() => {
                    loadSuggestedQuestions(finalMessageId)
                  }, 1000)
                }
                
                // 如果是新会话，在消息结束后重新加载历史消息以确保完整性
                if (isNewConversation && newConversationId) {
                  console.log('新会话消息结束，重新加载历史消息确保数据完整性')
                  setTimeout(() => {
                    if (newConversationId) {
                      loadConversationMessages(newConversationId)
                    }
                  }, 2000) // 给Dify更多时间保存消息到后端
                }
              }
              
            } catch (e) {
              console.error("处理SSE事件失败:", e, event)
            }
          }
        }
      }
      
    } catch (err) {
      console.error('发送消息失败:', err)
      setError('消息发送失败，请稍后重试')
      
      // 更新错误消息
      setConversationMessages(prev => ({
        ...prev,
        [conversationId]: prev[conversationId]?.map(msg => 
          msg.id === assistantMessageId 
            ? { ...msg, content: '抱歉，服务暂时不可用，请稍后再试。' }
            : msg
        ) || []
      }))
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }





  // 智能滚动函数
  const smartScroll = useCallback(() => {
    const scrollArea = scrollAreaRef.current
    if (scrollArea) {
      const viewport = scrollArea.querySelector('[data-radix-scroll-area-viewport]') as HTMLElement
      if (viewport) {
        const scrollTop = viewport.scrollTop
        const scrollHeight = viewport.scrollHeight
        const clientHeight = viewport.clientHeight
        const threshold = 80
        
        const wasAtBottom = scrollTop >= scrollHeight - clientHeight - threshold
        
        if (wasAtBottom || isLoading) {
          requestAnimationFrame(() => {
            viewport.scrollTop = viewport.scrollHeight
          })
        }
      }
    }
    
    if (messagesEndRef.current) {
      const element = messagesEndRef.current
      const isNearBottom = window.scrollY >= document.documentElement.scrollHeight - window.innerHeight - 100
      if (isNearBottom || isLoading) {
        element.scrollIntoView({ behavior: 'smooth' })
      }
    }
  }, [isLoading])

  // 当消息列表变化时自动滚动
  useEffect(() => {
    const timer = setTimeout(smartScroll, 100)
    return () => clearTimeout(timer)
  }, [currentMessages, smartScroll])

  // 监听消息内容变化，实现实时滚动
  useEffect(() => {
    if (isLoading) {
      const scrollInterval = setInterval(smartScroll, 300)
      return () => clearInterval(scrollInterval)
    }
  }, [isLoading, smartScroll])

  // 监听聊天内容高度变化事件（如思考区域折叠展开）
  useEffect(() => {
    const handleContentHeightChange = () => {
      setTimeout(smartScroll, 100)
    }

    window.addEventListener('chatContentHeightChanged', handleContentHeightChange)
    
    return () => {
      window.removeEventListener('chatContentHeightChanged', handleContentHeightChange)
    }
  }, [smartScroll])

  // 如果没有访问权限，显示错误页面
  if (!hasAccess) {
    return <AccessDenied />
  }

  return (
    <div className={cn('flex flex-col h-[100vh] bg-gradient-to-b from-blue-50/30 to-white/80 p-2 sm:p-4 overflow-hidden')}>
      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col min-w-0 overflow-hidden">
        <Card className={cn('flex flex-col h-full shadow-lg rounded-xl border-border/50')}>
          <CardHeader className={cn("flex-shrink-0 border-b bg-gradient-to-r from-blue-50 to-blue-100 rounded-t-xl p-3 sm:p-6")}>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3 text-lg sm:text-xl">
                <MessageSquare className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                <span className="truncate font-semibold">
                  甬知AI智能问答助手
                  {isLoading && (
                    <span className="ml-2 text-muted-foreground text-sm sm:text-base font-normal flex items-center gap-1">
                      <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                      思考中...
                    </span>
                  )}
                </span>
              </CardTitle>

              {/* 新建会话按钮 */}
              {/* <Button
                variant="outline"
                size="sm"
                onClick={createNewConversation}
                className={cn("h-8 sm:h-9 flex-shrink-0 text-xs sm:text-sm shadow-sm hover:shadow-md transition-shadow bg-gradient-to-r from-blue-500 to-blue-600 text-white border-none hover:from-blue-600 hover:to-blue-700")}
              >
                <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                新对话
              </Button> */}
            </div>
          </CardHeader>
          
          <CardContent className={cn("flex-1 p-0 flex flex-col min-h-0 overflow-hidden bg-gradient-to-b from-blue-50/30 to-white/80")}>
            {/* 消息列表 */}
            <ScrollArea className="flex-1 p-3 sm:p-6 overflow-auto" ref={scrollAreaRef}>
              {currentMessages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center space-y-6 sm:space-y-8 px-4">
                  <div className="relative">
                    <div className="w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-full flex items-center justify-center shadow-lg backdrop-blur-sm">
                      <MessageSquare className="w-8 h-8 sm:w-12 sm:h-12 text-blue-600" />
                    </div>
                    <div className="absolute inset-0 w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-br from-blue-400/20 to-blue-500/20 rounded-full animate-pulse"></div>
                  </div>
                  <div className="max-w-md">
                    <h3 className="text-xl sm:text-2xl font-semibold mb-3 sm:mb-4 text-gray-800">
                      开始新的对话
                    </h3>
                    <p className="text-sm sm:text-base text-gray-600 leading-relaxed">
                    我可以帮您查询和解答知识库中的专业问题。请在下方输入您的问题，我会基于知识库中的资料为您提供准确的回答。
                    </p>
                  </div>
                  {/* 快速入门提示 */}

                  {error && (
                    <div className="mt-4 text-red-500 p-3 bg-red-50 rounded-lg text-sm">
                      {error}
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4 sm:space-y-6 max-w-full word-wrap overflow-wrap-anywhere">
                  {currentMessages.map((message) => (
                    <div
                      key={message.id}
                      className={cn(
                        'group mb-6 flex gap-3 max-w-full',
                        message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                      )}
                    >
                      {/* 头像 */}
                      <Avatar className="h-8 w-8 mt-1 flex-shrink-0">
                        <AvatarFallback className={cn(
                          'text-sm font-medium border',
                          message.role === 'user'
                            ? 'bg-blue-500 text-white border-blue-600'
                            : 'bg-gray-100 text-gray-600 border-gray-200'
                        )}>
                          {message.role === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                        </AvatarFallback>
                      </Avatar>

                      {/* 消息内容容器 */}
                      <div className={cn(
                        "flex-1 min-w-0 max-w-[85%] relative",
                        message.role === 'user' && "flex flex-col items-end"
                      )}>
                        {/* 消息内容 */}
                        <div className={cn(
                          "rounded-lg p-4 transition-all duration-200 ease-in-out word-wrap message-content",
                          message.role === 'user'
                            ? "bg-white text-gray-900 border border-gray-200 shadow-sm"
                            : "bg-muted"
                        )}>
                          {/* 用户消息直接显示 */}
                          {message.role === 'user' && (
                            <div className="whitespace-pre-wrap break-words">{message.content}</div>
                          )}

                          {/* 助手消息处理 */}
                          {message.role === 'assistant' && (
                            <div className="bg-white rounded-lg p-3 shadow-sm text-gray-900">
                              {(() => {
                                const parsed = parseThinkContent(message.content)

                                if (!parsed.hasThink) {
                            // 没有think标签，显示助手标识和直接渲染markdown内容
                            return (
                              <div>
                                {message.role === 'assistant' && (
                                  <div className="flex items-center mb-2">
                                    <Headset size={14} className="mr-2 text-blue-500 sm:size-4" />
                                    <span className="font-medium text-blue-700 text-sm sm:text-base">甬知AI智能问答助手</span>
                                  </div>
                                )}
                                {message.content ? (
                                  // 检查内容是否包含markdown格式或HTML链接
                                  /[#*`\[\]()_-]|\d+\.|\n\s*[-*•]|<a\s+[^>]*>/.test(message.content) ? (
                                    // 包含markdown格式，使用renderMarkdown
                                    <div
                                      className="max-w-none leading-relaxed text-sm sm:text-base break-words"
                                      dangerouslySetInnerHTML={{
                                        __html: renderMarkdown(message.content)
                                      }}
                                    />
                                  ) : (
                                    // 纯文本，使用whitespace-pre-wrap保留换行
                                    <div className="max-w-none leading-relaxed text-sm sm:text-base break-words whitespace-pre-wrap">
                                      {message.content}
                                    </div>
                                  )
                                ) : (
                                  // 显示加载动画（当内容为空时）
                                  <div className="flex space-x-2">
                                    <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce"></div>
                                    <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce delay-100"></div>
                                    <div className="h-2 w-2 bg-blue-400 rounded-full animate-bounce delay-200"></div>
                                  </div>
                                )}
                              </div>
                            )
                          }
                          
                          return (
                            <div className="space-y-3">
                              {/* 为有think标签的助手消息添加统一的助手标识 */}
                              {message.role === 'assistant' && (
                                <div className="flex items-center mb-2">
                                  <Headset size={14} className="mr-2 text-blue-500 sm:size-4" />
                                  <span className="font-medium text-blue-700 text-sm sm:text-base">甬知AI智能问答助手</span>
                                </div>
                              )}
                              {parsed.parts.map((part, partIndex) => {
                                if (part.type === 'text') {
                                  // 正式回答部分，智能选择渲染方式
                                  return (
                                    <div
                                      key={partIndex}
                                      className="max-w-none leading-relaxed text-sm sm:text-base break-words bg-white rounded-lg p-4 shadow-sm border border-gray-100"
                                    >
                                      {/[#*`\[\]()_-]|\d+\.|\n\s*[-*•]|<a\s+[^>]*>/.test(part.content) ? (
                                        // 包含markdown格式
                                        <div dangerouslySetInnerHTML={{
                                          __html: renderMarkdown(part.content)
                                        }} />
                                      ) : (
                                        // 纯文本，保留换行
                                        <div className="whitespace-pre-wrap">
                                          {part.content}
                                        </div>
                                      )}
                                    </div>
                                  )
                                } else if (part.type === 'think' && part.index !== undefined) {
                                  const thinkKey = `${message.id}-${part.index}`
                                  const isExpanded = expandedThinks.has(thinkKey)
                                  
                                  return (
                                    <div key={partIndex} className="mb-3">
                                      <Collapsible 
                                        open={isExpanded} 
                                        onOpenChange={(open) => {
                                          if (open) {
                                            setExpandedThinks(prev => new Set([...prev, thinkKey]))
                                          } else {
                                            setExpandedThinks(prev => {
                                              const newSet = new Set(prev)
                                              newSet.delete(thinkKey)
                                              return newSet
                                            })
                                          }
                                          // 触发滚动调整
                                          setTimeout(() => {
                                            window.dispatchEvent(new CustomEvent('chatContentHeightChanged'))
                                          }, 200)
                                        }}
                                      >
                                        <CollapsibleTrigger asChild>
                                          <button className="inline-flex items-center gap-1.5 text-xs bg-gradient-to-r from-blue-500 to-blue-600 text-white px-3 py-1 rounded-full shadow-sm hover:shadow-md transition-all font-medium">
                                            <Brain className="h-3.5 w-3.5" />
                                            <span>已深度思考 ({getThinkingTime(message)}s)</span>
                                            <ChevronDown className={cn("h-3.5 w-3.5 transition-transform", isExpanded && "rotate-180")} />
                                          </button>
                                        </CollapsibleTrigger>
                                        <CollapsibleContent>
                                          <div className="bg-white border border-blue-200 rounded-lg p-3 mt-2 text-sm text-gray-900 max-h-72 overflow-y-auto shadow-sm">
                                            <div className="whitespace-pre-wrap leading-relaxed">{part.content}</div>
                                          </div>
                                        </CollapsibleContent>
                                      </Collapsible>
                                    </div>
                                  )
                                }
                                return null
                              })}
                            </div>
                          )
                              })()}
                            </div>
                          )}
                        </div>

                        {/* 显示文件下载 */}
                        {message.role === 'assistant' && message.files && message.files.length > 0 &&
                          renderMessageFiles(message.files)
                        }

                        {/* 底部操作按钮和时间 */}
                        <div className={cn(
                          'flex items-center gap-2 text-xs mt-2 px-1',
                          message.role === 'user'
                            ? 'justify-start text-gray-500'
                            : 'justify-between text-muted-foreground'
                        )}>
                          <div className="flex items-center gap-2">
                            <span>{formatTime(message.timestamp)}</span>

                            {/* 消息操作按钮 - 仅对AI消息显示 */}
                            {message.role === 'assistant' && message.content && (
                              <div className="flex items-center gap-1 opacity-100 transition-opacity">
                                {/* 复制按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 w-7 p-0"
                                  onClick={() => handleCopy(message.content, message.id)}
                                  title="复制消息"
                                >
                                  {copiedMessageId === message.id ? (
                                    <span className="text-green-600 font-bold text-xs">✓</span>
                                  ) : (
                                    <Copy className="h-3 w-3" />
                                  )}
                                </Button>

                                {/* 重新生成按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 w-7 p-0"
                                  onClick={() => handleRetry(message.id)}
                                  title="重新生成"
                                >
                                  <RotateCcw className="h-3 w-3" />
                                </Button>

                                {/* 点赞按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={cn(
                                    "h-7 w-7 p-0",
                                    messageRatings[message.id] === 'up' && "text-green-600 bg-green-50"
                                  )}
                                  onClick={() => handleRating(message.id, 'up')}
                                  title="点赞"
                                >
                                  <ThumbsUp className="h-3 w-3" />
                                </Button>

                                {/* 点踩按钮 */}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className={cn(
                                    "h-7 w-7 p-0",
                                    messageRatings[message.id] === 'down' && "text-red-600 bg-red-50"
                                  )}
                                  onClick={() => handleRating(message.id, 'down')}
                                  title="点踩"
                                >
                                  <ThumbsDown className="h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>

                          {/* Token使用信息 */}
                          {message.role === 'assistant' && message.metadata?.usage && (
                            <div className="flex items-center gap-2">
                              {message.metadata.elapsed_time && (
                                <Badge variant="outline" className="text-xs">
                                  {(message.metadata.elapsed_time / 1000).toFixed(1)}s
                                </Badge>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {/* 建议问题 */}
                  {suggestedQuestions.length > 0 && (
                    <div className="flex justify-start">
                      <div className="max-w-[85%] sm:max-w-[80%] bg-gray-50 border border-gray-200 rounded-lg p-3 sm:p-4">
                        <div className="flex items-center gap-2 mb-3">
                          <Sparkles size={14} className="text-blue-500 sm:size-4" />
                          <span className="text-sm font-medium text-gray-700">建议问题</span>
                        </div>
                        <div className="space-y-2">
                          {suggestedQuestions.map((question, index) => (
                            <Button
                              key={index}
                              variant="outline"
                              size="sm"
                              className="w-full text-left justify-start h-auto p-2 text-xs sm:text-sm"
                              onClick={() => handleSendMessage(question)}
                            >
                              {question}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                  

                  
                  {/* 用于自动滚动的底部元素 */}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </ScrollArea>
            
            {/* 输入区域 */}
            <div className="flex-shrink-0 p-3 sm:p-4 border-t border-blue-100 bg-white">
              <div className="relative flex flex-col w-full gap-3">
                {/* 输入框区域 */}
                <div className="flex items-center gap-3 w-full bg-white rounded-2xl shadow-sm p-3 border border-blue-100/70">
                  {/* 文本输入区 */}
                  <div className="flex-1 min-w-0 flex items-center gap-2">
                    <Input
                      ref={inputRef}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="发送消息..."
                      className="min-h-[40px] border-0 focus-visible:ring-0 focus-visible:ring-offset-0 p-0 bg-transparent flex-1 text-sm"
                      disabled={isLoading}
                    />
                    {/* 加载状态提示 */}
                    {isLoading && (
                      <div className="flex items-center gap-1 text-blue-600 text-sm">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="hidden sm:inline">正在发送...</span>
                      </div>
                    )}
                  </div>

                  {/* 发送按钮 */}
                  <button
                    type="button"
                    onClick={() => handleSendMessage()}
                    disabled={!input.trim() || isLoading}
                    className={cn(
                      "flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all",
                      input.trim() && !isLoading
                        ? "bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:shadow-md"
                        : "bg-blue-100 text-blue-400 cursor-not-allowed"
                    )}
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </div>

                {/* 功能按钮区域 */}
                <div className="flex items-center gap-2 justify-start">
                  {/* 深度思考按钮 */}
                  <button
                    type="button"
                    onClick={() => setDeepThinkingEnabled(!deepThinkingEnabled)}
                    disabled={isLoading}
                    className={cn(
                      "h-9 px-4 rounded-full flex items-center justify-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium",
                      deepThinkingEnabled
                        ? "bg-blue-100 text-blue-600 hover:bg-blue-150"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                    title={deepThinkingEnabled ? "关闭深度思考" : "开启深度思考"}
                  >
                    <Image src={thinkIcon} alt="深度思考" width={16} height={16} className="w-4 h-4" />
                    <span>深度思考 (R1)</span>
                  </button>

                  {/* 联网搜索按钮 */}
                  <button
                    type="button"
                    onClick={() => setWebSearchEnabled(!webSearchEnabled)}
                    disabled={isLoading}
                    className={cn(
                      "h-9 px-4 rounded-full flex items-center justify-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm font-medium",
                      webSearchEnabled
                        ? "bg-blue-100 text-blue-600 hover:bg-blue-150"
                        : "bg-gray-100 text-gray-600 hover:bg-gray-200"
                    )}
                    title={webSearchEnabled ? "关闭联网搜索" : "开启联网搜索"}
                  >
                    <Image src={webIcon} alt="联网搜索" width={16} height={16} className="w-4 h-4" />
                    <span>联网搜索</span>
                  </button>
                </div>

                {/* 提示信息 */}
                <div className="text-xs text-center text-muted-foreground">
                  按 Enter 发送 · 当前模式: {
                    webSearchEnabled && deepThinkingEnabled ? '联网搜索+深度思考' :
                    webSearchEnabled && !deepThinkingEnabled ? '联网搜索' :
                    !webSearchEnabled && deepThinkingEnabled ? '深度思考' :
                    '普通模式'
                  }
                </div>
              </div>
            </div>
            
            {error && (
              <div className="text-center p-3 bg-red-50 border-t">
                <p className="text-red-500 text-sm bg-red-100 p-2 rounded-lg inline-block">{error}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}