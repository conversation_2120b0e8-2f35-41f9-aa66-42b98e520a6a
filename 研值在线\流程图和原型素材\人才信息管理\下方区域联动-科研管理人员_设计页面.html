<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员区域联动分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科研管理人员区域联动分析</h1>
            <p class="text-gray-600">基于宁波市辖区空间数据，动态呈现科研管理人员分布态势与结构特征</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6 h-[calc(100vh-180px)]">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 区域分布统计提示区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">全市科研管理人员总数</div>
                                <div class="text-2xl font-bold text-blue-600">1,245</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">参政咨政人员</div>
                                <div class="text-2xl font-bold text-green-600">328</div>
                            </div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">企业家培训参与率</div>
                                <div class="text-2xl font-bold text-purple-600">76%</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 地图热力图区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                        </svg>
                        宁波市科研管理人员分布热力图
                    </h2>
                    <div class="border border-gray-200 rounded-md p-4">
                        <!-- 地图占位图 -->
                        <div class="relative">
                            <svg class="w-full h-96 bg-gray-100 text-gray-400" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle">宁波市地图热力图</text>
                                <!-- 模拟热力区域 -->
                                <rect x="20%" y="30%" width="15%" height="20%" fill="#3b82f6" opacity="0.7" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="海曙区" data-count="245"/>
                                <rect x="40%" y="40%" width="15%" height="20%" fill="#3b82f6" opacity="0.5" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="鄞州区" data-count="320"/>
                                <rect x="60%" y="20%" width="15%" height="20%" fill="#3b82f6" opacity="0.3" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="江北区" data-count="180"/>
                                <rect x="30%" y="60%" width="15%" height="20%" fill="#3b82f6" opacity="0.6" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="北仑区" data-count="280"/>
                                <rect x="50%" y="70%" width="15%" height="20%" fill="#3b82f6" opacity="0.4" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="镇海区" data-count="150"/>
                                <rect x="70%" y="60%" width="15%" height="20%" fill="#3b82f6" opacity="0.2" rx="2" ry="2" class="cursor-pointer hover:opacity-100" data-region="奉化区" data-count="70"/>
                            </svg>
                            <!-- 热力图提示框 -->
                            <div id="heatmapTooltip" class="absolute hidden bg-white shadow-lg rounded-md p-3 border border-gray-200 z-10">
                                <div class="text-sm font-medium text-gray-900" id="tooltipRegion"></div>
                                <div class="text-sm text-gray-600">科研管理人员: <span id="tooltipCount" class="font-medium"></span>人</div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mt-4">
                            <div class="text-sm text-gray-500">热力强度表示科研管理人员数量</div>
                            <button onclick="showAllRegions()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                查看全部区域
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计图表区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        多维度统计图表
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 参政咨政活动分布 -->
                        <div class="border border-gray-200 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">参政咨政活动分布</h3>
                            <div class="h-48 flex items-end space-x-2">
                                <div class="w-8 bg-blue-500 rounded-t" style="height: 70%" title="人大代表: 120人"></div>
                                <div class="w-8 bg-blue-400 rounded-t" style="height: 50%" title="政协委员: 85人"></div>
                                <div class="w-8 bg-blue-300 rounded-t" style="height: 30%" title="政府顾问: 52人"></div>
                                <div class="w-8 bg-blue-200 rounded-t" style="height: 20%" title="其他: 31人"></div>
                            </div>
                            <div class="flex justify-between mt-2 text-xs text-gray-500">
                                <span>人大代表</span>
                                <span>政协委员</span>
                                <span>政府顾问</span>
                                <span>其他</span>
                            </div>
                        </div>
                        <!-- 性别比例 -->
                        <div class="border border-gray-200 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">性别比例</h3>
                            <div class="flex items-center justify-center">
                                <div class="w-32 h-32 rounded-full bg-gradient-to-r from-blue-500 to-pink-500 flex items-center justify-center">
                                    <div class="w-24 h-24 bg-white rounded-full"></div>
                                </div>
                            </div>
                            <div class="flex justify-center space-x-6 mt-2 text-xs text-gray-500">
                                <span class="flex items-center"><span class="w-3 h-3 bg-blue-500 rounded-full mr-1"></span>男性 68%</span>
                                <span class="flex items-center"><span class="w-3 h-3 bg-pink-500 rounded-full mr-1"></span>女性 32%</span>
                            </div>
                        </div>
                        <!-- 年龄结构 -->
                        <div class="border border-gray-200 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">年龄结构</h3>
                            <div class="h-48 flex items-end space-x-1">
                                <div class="w-6 bg-green-500 rounded-t" style="height: 20%" title="30岁以下: 45人"></div>
                                <div class="w-6 bg-green-400 rounded-t" style="height: 50%" title="31-40岁: 112人"></div>
                                <div class="w-6 bg-green-300 rounded-t" style="height: 70%" title="41-50岁: 156人"></div>
                                <div class="w-6 bg-green-200 rounded-t" style="height: 40%" title="51-60岁: 89人"></div>
                                <div class="w-6 bg-green-100 rounded-t" style="height: 10%" title="60岁以上: 23人"></div>
                            </div>
                            <div class="flex justify-between mt-2 text-xs text-gray-500">
                                <span>30以下</span>
                                <span>31-40</span>
                                <span>41-50</span>
                                <span>51-60</span>
                                <span>60+</span>
                            </div>
                        </div>
                        <!-- 企业家培训参与情况 -->
                        <div class="border border-gray-200 rounded-md p-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-2">企业家培训参与情况</h3>
                            <div class="h-48 flex items-center justify-center">
                                <div class="w-32 h-32 rounded-full bg-gradient-to-r from-purple-500 to-purple-300 flex items-center justify-center">
                                    <div class="w-24 h-24 bg-white rounded-full flex items-center justify-center">
                                        <span class="text-2xl font-bold text-purple-600">76%</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex justify-center space-x-6 mt-2 text-xs text-gray-500">
                                <span class="flex items-center"><span class="w-3 h-3 bg-purple-500 rounded-full mr-1"></span>已参与 76%</span>
                                <span class="flex items-center"><span class="w-3 h-3 bg-gray-300 rounded-full mr-1"></span>未参与 24%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科研管理人员清册区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                                <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                科研管理人员清册
                            </h2>
                            <div class="flex space-x-2">
                                <div class="relative" id="exportDropdown">
                                    <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4 4m0 0l-4-4m4 4V4"></path>
                                        </svg>
                                        导出
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                    </button>
                                    <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-md border border-gray-300 p-4 hidden z-10">
                                        <div class="py-1">
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                            <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="resetFilters()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    重置筛选
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属区域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职务</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">参政咨政</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">培训经历</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">张伟</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">鄞州区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市智能制造研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">副院长</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">人大代表</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDetail('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">王芳</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">海曙区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市科技创新有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">总经理</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-purple-100 text-purple-800">政协委员</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDetail('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">李强</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">北仑区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波港集团</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">技术总监</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">政府顾问</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">未参与</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDetail('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">陈明</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">江北区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市新材料研究院</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">院长</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">人大代表</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDetail('4')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">赵静</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">镇海区</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">宁波市石化技术有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-500">研发主管</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">无</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已参与</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewDetail('5')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示 <span class="font-medium">1</span> 到 <span class="font-medium">5</span> 条，共 <span class="font-medium">1245</span> 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计与筛选区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 区域筛选 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        区域筛选
                    </h2>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部区域</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">海曙区</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">鄞州区</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">江北区</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">北仑区</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">镇海区</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">奉化区</span>
                        </label>
                    </div>
                </div>

                <!-- 属性筛选 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"></path>
                        </svg>
                        属性筛选
                    </h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">参政咨政</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">人大代表</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">政协委员</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">政府顾问</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">其他</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">企业家培训</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">已参与</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">未参与</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">性别</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">男性</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">女性</span>
                                </label>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">年龄段</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">30岁以下</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">31-40岁</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">41-50岁</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">51-60岁</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                    <span class="ml-2 text-sm text-gray-700">60岁以上</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            应用筛选
                        </button>
                    </div>
                </div>

                <!-- 热门单位 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        热门单位
                    </h2>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">宁波市智能制造研究院</span>
                            <span class="text-sm font-medium text-blue-600">45人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">宁波市科技创新有限公司</span>
                            <span class="text-sm font-medium text-blue-600">32人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">宁波港集团</span>
                            <span class="text-sm font-medium text-blue-600">28人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">宁波市新材料研究院</span>
                            <span class="text-sm font-medium text-blue-600">25人</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">宁波市石化技术有限公司</span>
                            <span class="text-sm font-medium text-blue-600">18人</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 人员详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">科研管理人员详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- 基本信息 -->
                        <div class="md:col-span-1">
                            <div class="flex flex-col items-center">
                                <div class="w-32 h-32 bg-gray-200 rounded-full mb-4 flex items-center justify-center">
                                    <svg class="w-20 h-20 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-gray-900" id="detailName">张伟</h4>
                                <p class="text-sm text-gray-500" id="detailPosition">副院长</p>
                                <p class="text-sm text-gray-500" id="detailOrg">宁波市智能制造研究院</p>
                            </div>
                            <div class="mt-6 space-y-3">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700" id="detailRegion">鄞州区</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700" id="detailPhone">138****5678</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <span class="text-sm text-gray-700" id="detailEmail"><EMAIL></span>
                                </div>
                            </div>
                        </div>
                        <!-- 详细信息 -->
                        <div class="md:col-span-2 space-y-6">
                            <!-- 参政咨政 -->
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-2">参政咨政</h4>
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-sm font-medium text-blue-800">人大代表</span>
                                    </div>
                                    <div class="mt-2 text-sm text-gray-700">
                                        <p>第十五届宁波市人大代表</p>
                                        <p class="mt-1">任期: 2020-2025</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 培训经历 -->
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-2">培训经历</h4>
                                <div class="space-y-3">
                                    <div class="bg-white border border-gray-200 rounded-lg p-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-medium text-gray-900">宁波市企业家高级研修班</span>
                                            <span class="text-xs text-gray-500">2023-05-15</span>
                                        </div>
                                        <p class="mt-1 text-sm text-gray-600">培训内容: 数字化转型与智能制造</p>
                                    </div>
                                    <div class="bg-white border border-gray-200 rounded-lg p-3">
                                        <div class="flex justify-between items-center">
                                            <span class="text-sm font-medium text-gray-900">浙江省科技创新管理培训</span>
                                            <span class="text-xs text-gray-500">2022-11-20</span>
                                        </div>
                                        <p class="mt-1 text-sm text-gray-600">培训内容: 科技创新政策解读与实务</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 社会活动 -->
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-2">社会活动</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">宁波市科技创新协会 理事</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">浙江省智能制造专家委员会 委员</span>
                                    </div>
                                    <div class="flex items-center">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-sm text-gray-700">宁波市高层次人才联谊会 会员</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 科研成果 -->
                            <div>
                                <h4 class="text-md font-semibold text-gray-900 mb-2">科研成果</h4>
                                <div class="space-y-2">
                                    <div class="text-sm text-gray-700">1. 智能制造系统集成技术研究，国家自然科学基金项目，2021-2023</div>
                                    <div class="text-sm text-gray-700">2. 基于工业互联网的智能工厂解决方案，浙江省重点研发计划，2020-2022</div>
                                    <div class="text-sm text-gray-700">3. 5G+工业互联网应用示范项目，宁波市科技计划项目，2019-2021</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeDetailModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        关闭
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        导出个人档案
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 热力图交互
        const heatmapRegions = document.querySelectorAll('rect[data-region]');
        const tooltip = document.getElementById('heatmapTooltip');
        const tooltipRegion = document.getElementById('tooltipRegion');
        const tooltipCount = document.getElementById('tooltipCount');

        heatmapRegions.forEach(region => {
            region.addEventListener('mouseenter', (e) => {
                const rect = region.getBoundingClientRect();
                tooltip.style.left = `${rect.left + window.scrollX}px`;
                tooltip.style.top = `${rect.top + window.scrollY - 50}px`;
                tooltipRegion.textContent = region.dataset.region;
                tooltipCount.textContent = region.dataset.count;
                tooltip.classList.remove('hidden');
            });

            region.addEventListener('mouseleave', () => {
                tooltip.classList.add('hidden');
            });

            region.addEventListener('click', () => {
                alert(`即将跳转到${region.dataset.region}的科研管理人员列表`);
            });
        });

        // 查看全部区域
        function showAllRegions() {
            alert('显示所有区域的科研管理人员列表');
        }

        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }

        // 重置筛选
        function resetFilters() {
            alert('筛选条件已重置');
        }

        // 人员详情弹窗
        function viewDetail(id) {
            // 模拟根据ID获取人员详情
            const sampleData = {
                '1': {
                    name: '张伟',
                    position: '副院长',
                    org: '宁波市智能制造研究院',
                    region: '鄞州区',
                    phone: '138****5678',
                    email: '<EMAIL>'
                },
                '2': {
                    name: '王芳',
                    position: '总经理',
                    org: '宁波市科技创新有限公司',
                    region: '海曙区',
                    phone: '139****1234',
                    email: '<EMAIL>'
                },
                '3': {
                    name: '李强',
                    position: '技术总监',
                    org: '宁波港集团',
                    region: '北仑区',
                    phone: '137****8765',
                    email: '<EMAIL>'
                },
                '4': {
                    name: '陈明',
                    position: '院长',
                    org: '宁波市新材料研究院',
                    region: '江北区',
                    phone: '135****4321',
                    email: '<EMAIL>'
                },
                '5': {
                    name: '赵静',
                    position: '研发主管',
                    org: '宁波市石化技术有限公司',
                    region: '镇海区',
                    phone: '136****9876',
                    email: '<EMAIL>'
                }
            };

            const data = sampleData[id] || sampleData['1'];
            
            document.getElementById('detailName').textContent = data.name;
            document.getElementById('detailPosition').textContent = data.position;
            document.getElementById('detailOrg').textContent = data.org;
            document.getElementById('detailRegion').textContent = data.region;
            document.getElementById('detailPhone').textContent = data.phone;
            document.getElementById('detailEmail').textContent = data.email;
            
            document.getElementById('detailModal').classList.remove('hidden');
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });

        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });
    </script>
</body>
</html>