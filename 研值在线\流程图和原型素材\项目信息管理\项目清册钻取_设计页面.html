<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目清册钻取</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">项目清册钻取</h1>

        <!-- 筛选条件区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                </svg>
                筛选条件
            </h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="tech">科技创新项目</option>
                        <option value="industry">产业升级项目</option>
                        <option value="talent">人才引进项目</option>
                        <option value="green">绿色低碳项目</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">区域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部区域</option>
                        <option value="haishu">海曙区</option>
                        <option value="jiangdong">江东区</option>
                        <option value="jiangbei">江北区</option>
                        <option value="yinzhou">鄞州区</option>
                        <option value="beilun">北仑区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="planning">立项中</option>
                        <option value="ongoing">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="paused">已暂停</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">承担单位</label>
                    <input type="text" placeholder="输入单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">合作单位</label>
                    <input type="text" placeholder="输入单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">起止年度</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="起始年" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" placeholder="结束年" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">经费区间(万元)</label>
                    <div class="flex space-x-2">
                        <input type="number" placeholder="最小值" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <input type="number" placeholder="最大值" class="w-1/2 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="md:col-span-2 lg:col-span-3">
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键词搜索</label>
                    <div class="flex">
                        <input type="text" placeholder="输入项目名称或编号" class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            搜索
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 项目清册表格区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                <h2 class="text-lg font-medium text-gray-800">项目清册</h2>
                <div class="flex space-x-3">
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        批量导出
                    </button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        批量跟踪
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">承担单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经费总额(万元)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">已拨付(万元)</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2023-001</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市智能制造关键技术研发</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">产业升级项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市智能制造研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学、宁波工程学院</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-01 ~ 2024-12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">500.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">250.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张教授</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">跟踪</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2023-002</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市新材料应用技术研究</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科技创新项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市材料科学研究所</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中科院宁波材料所</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">立项中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-03 ~ 2025-03</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">800.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">0.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李研究员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">跟踪</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2023-003</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市高端人才引进计划</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人才引进项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市人社局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市各高校</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">已暂停</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-02 ~ 2023-12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">300.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">150.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王主任</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">跟踪</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2023-004</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市绿色低碳技术示范</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">绿色低碳项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市环保局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波诺丁汉大学</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-04 ~ 2024-06</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">450.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">225.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵博士</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">跟踪</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">NB2023-005</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市数字经济产业园建设</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">产业升级项目</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市经信局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波软件园</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">已完成</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-10 ~ 2023-09</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">600.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">600.00</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">陈处长</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openDetailModal('5')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">跟踪</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-4/5 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">项目详情</h3>
                    <button onclick="closeDetailModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- 左侧基本信息 -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">基本信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">项目编号:</p>
                                    <p class="font-medium text-gray-900">NB2023-001</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">项目名称:</p>
                                    <p class="font-medium text-gray-900">宁波市智能制造关键技术研发</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">项目类型:</p>
                                    <p class="font-medium text-gray-900">产业升级项目</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">项目状态:</p>
                                    <p class="font-medium text-gray-900">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">进行中</span>
                                    </p>
                                </div>
                                <div>
                                    <p class="text-gray-500">承担单位:</p>
                                    <p class="font-medium text-gray-900">宁波市智能制造研究院</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">合作单位:</p>
                                    <p class="font-medium text-gray-900">宁波大学、宁波工程学院</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">项目负责人:</p>
                                    <p class="font-medium text-gray-900">张教授</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">起止时间:</p>
                                    <p class="font-medium text-gray-900">2023-01 ~ 2024-12</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">经费信息</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">经费总额(万元):</p>
                                    <p class="font-medium text-gray-900">500.00</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">已拨付(万元):</p>
                                    <p class="font-medium text-gray-900">250.00</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">经费使用率:</p>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 50%"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">50%</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">经费来源:</p>
                                    <p class="font-medium text-gray-900">市财政拨款</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">项目简介</h4>
                            <p class="text-sm text-gray-700">
                                本项目旨在研发适用于宁波市制造业的智能制造关键技术，包括工业互联网平台、智能生产线改造、数字孪生技术等。通过本项目的实施，将提升宁波市制造业的智能化水平，推动产业转型升级。
                            </p>
                        </div>
                    </div>
                    
                    <!-- 右侧详细信息 -->
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">项目进度</h4>
                            <div class="space-y-3">
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="font-medium">需求调研</span>
                                        <span class="text-gray-500">2023-01 ~ 2023-03</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 100%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="font-medium">方案设计</span>
                                        <span class="text-gray-500">2023-04 ~ 2023-06</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-green-500 h-2.5 rounded-full" style="width: 80%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="font-medium">系统开发</span>
                                        <span class="text-gray-500">2023-07 ~ 2023-12</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 40%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="font-medium">试点应用</span>
                                        <span class="text-gray-500">2024-01 ~ 2024-06</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-gray-300 h-2.5 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between text-sm mb-1">
                                        <span class="font-medium">推广应用</span>
                                        <span class="text-gray-500">2024-07 ~ 2024-12</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                                        <div class="bg-gray-300 h-2.5 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">项目团队</h4>
                            <div class="grid grid-cols-2 gap-4 text-sm">
                                <div>
                                    <p class="text-gray-500">项目负责人:</p>
                                    <p class="font-medium text-gray-900">张教授</p>
                                    <p class="text-xs text-gray-500">宁波市智能制造研究院</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">技术负责人:</p>
                                    <p class="font-medium text-gray-900">王工程师</p>
                                    <p class="text-xs text-gray-500">宁波大学</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">财务负责人:</p>
                                    <p class="font-medium text-gray-900">李会计</p>
                                    <p class="text-xs text-gray-500">宁波市智能制造研究院</p>
                                </div>
                                <div>
                                    <p class="text-gray-500">项目成员:</p>
                                    <p class="font-medium text-gray-900">5人</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <h4 class="text-md font-semibold text-gray-900 mb-3">附件资料</h4>
                            <div class="space-y-2">
                                <div class="flex items-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-blue-600 hover:text-blue-800 cursor-pointer">项目立项书.pdf</span>
                                    <span class="text-xs text-gray-500 ml-2">2.5MB</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-blue-600 hover:text-blue-800 cursor-pointer">技术方案.docx</span>
                                    <span class="text-xs text-gray-500 ml-2">1.8MB</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="text-blue-600 hover:text-blue-800 cursor-pointer">中期报告.pdf</span>
                                    <span class="text-xs text-gray-500 ml-2">3.2MB</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between mt-6 pt-4 border-t border-gray-200">
                    <div>
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                            </svg>
                            上一条
                        </button>
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 ml-2">
                            下一条
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                            </svg>
                        </button>
                    </div>
                    <div>
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                            </svg>
                            导出报告
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 ml-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            跟踪项目
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeDetailModal() {
            document.getElementById('detailModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // DOM加载完毕后执行的初始化代码
        document.addEventListener('DOMContentLoaded', function() {
            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailModal();
                }
            });

            // ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && !document.getElementById('detailModal').classList.contains('hidden')) {
                    closeDetailModal();
                }
            });
        });
    </script>
</body>
</html>