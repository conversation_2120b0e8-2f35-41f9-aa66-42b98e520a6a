<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校科研合作展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">高等院校科研合作展示</h1>
            <p class="text-gray-600">多维度展示高校与地方政府、企业及科研院所的科研合作概况与成效</p>
        </div>

        <!-- 合作概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">地方合作数量</p>
                        <p class="text-2xl font-bold text-blue-600 mt-1">86</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="localChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">企业合作数量</p>
                        <p class="text-2xl font-bold text-green-600 mt-1">156</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="enterpriseChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">高校院所合作</p>
                        <p class="text-2xl font-bold text-purple-600 mt-1">72</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="instituteChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-500">合作项目总数</p>
                        <p class="text-2xl font-bold text-orange-600 mt-1">314</p>
                    </div>
                    <div class="w-16 h-16">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选条件区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">合作类型</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">地方政府</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">企业</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">高校院所</span>
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                        <option>全部领域</option>
                        <option>新能源</option>
                        <option>新材料</option>
                        <option>生物医药</option>
                        <option>智能制造</option>
                        <option>信息技术</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-1/2 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <span class="flex items-center text-gray-500 text-xs">至</span>
                        <input type="date" class="w-1/2 px-2 py-2 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="合作单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">项目状态</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="status" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="status" value="active" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">在研</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="status" value="completed" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">已完成</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                    查询
                </button>
            </div>
        </div>

        <!-- 主要内容区 -->
        <div class="flex flex-col lg:flex-row gap-6">
            <!-- 合作单位列表区 -->
            <div class="flex-1">
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-semibold text-gray-900">合作单位列表</h2>
                            <div class="text-sm text-gray-500">
                                共 <span class="font-medium text-gray-900">128</span> 条记录
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作单位</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合作类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">行业领域</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目数</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在研/完成</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">投入金额</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市科技局</div>
                                        <div class="text-xs text-gray-500">地方政府</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">地方政府</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">综合</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">24</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="text-green-600">12</span> / <span class="text-blue-600">12</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥1,850万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openProjectDetail('1')" class="text-blue-600 hover:text-blue-900">查看项目</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波吉利汽车研究院</div>
                                        <div class="text-xs text-gray-500">企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">智能制造</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="text-green-600">8</span> / <span class="text-blue-600">10</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥1,250万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openProjectDetail('2')" class="text-blue-600 hover:text-blue-900">查看项目</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">浙江大学宁波研究院</div>
                                        <div class="text-xs text-gray-500">高校院所</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高校院所</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">综合</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="text-green-600">6</span> / <span class="text-blue-600">9</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥980万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openProjectDetail('3')" class="text-blue-600 hover:text-blue-900">查看项目</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波容百新能源科技</div>
                                        <div class="text-xs text-gray-500">企业</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">新能源</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="text-green-600">5</span> / <span class="text-blue-600">7</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥750万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openProjectDetail('4')" class="text-blue-600 hover:text-blue-900">查看项目</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波市第一医院</div>
                                        <div class="text-xs text-gray-500">医疗机构</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗机构</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">生物医药</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">9</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <span class="text-green-600">4</span> / <span class="text-blue-600">5</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥520万</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openProjectDetail('5')" class="text-blue-600 hover:text-blue-900">查看项目</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 128 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 合作分析图表区 -->
            <div class="w-full lg:w-1/3 space-y-6">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">合作投入趋势</h3>
                        <select class="px-2 py-1 text-xs border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option>近3年</option>
                            <option>近5年</option>
                            <option>全部</option>
                        </select>
                    </div>
                    <div class="h-64">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">投入产出比</h3>
                        <button class="px-2 py-1 text-xs border border-gray-300 rounded-md hover:bg-gray-50">重置视图</button>
                    </div>
                    <div class="h-64">
                        <canvas id="ratioChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目详情抽屉 -->
    <div id="projectDetail" class="fixed inset-y-0 right-0 w-full lg:w-1/2 bg-white shadow-xl transform transition-transform translate-x-full z-50 hidden">
        <div class="h-full overflow-y-auto">
            <div class="p-6 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">合作项目详情</h3>
                <button onclick="closeProjectDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">合作单位信息</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">单位名称：</span>
                            <span class="font-medium text-gray-900">宁波吉利汽车研究院</span>
                        </div>
                        <div>
                            <span class="text-gray-500">合作类型：</span>
                            <span class="font-medium text-gray-900">企业</span>
                        </div>
                        <div>
                            <span class="text-gray-500">行业领域：</span>
                            <span class="font-medium text-gray-900">智能制造</span>
                        </div>
                        <div>
                            <span class="text-gray-500">合作起始：</span>
                            <span class="font-medium text-gray-900">2018年3月</span>
                        </div>
                        <div>
                            <span class="text-gray-500">总项目数：</span>
                            <span class="font-medium text-gray-900">18</span>
                        </div>
                        <div>
                            <span class="text-gray-500">总投入金额：</span>
                            <span class="font-medium text-gray-900">¥1,250万</span>
                        </div>
                    </div>
                </div>

                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-900 mb-4">项目列表</h4>
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">新能源汽车电池管理系统研发</h5>
                                    <p class="text-xs text-gray-500 mt-1">项目编号：NBUT-2022-018</p>
                                </div>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在研</span>
                            </div>
                            <div class="mt-3 grid grid-cols-2 gap-2 text-xs">
                                <div>
                                    <span class="text-gray-500">开始时间：</span>
                                    <span>2022年3月</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">预计结束：</span>
                                    <span>2024年6月</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">投入金额：</span>
                                    <span>¥180万</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">负责人：</span>
                                    <span>张教授</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>项目进度</span>
                                    <span>45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-600 h-2 rounded-full" style="width: 45%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900">智能驾驶辅助系统开发</h5>
                                    <p class="text-xs text-gray-500 mt-1">项目编号：NBUT-2021-012</p>
                                </div>
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已完成</span>
                            </div>
                            <div class="mt-3 grid grid-cols-2 gap-2 text-xs">
                                <div>
                                    <span class="text-gray-500">开始时间：</span>
                                    <span>2021年5月</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">结束时间：</span>
                                    <span>2023年2月</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">投入金额：</span>
                                    <span>¥150万</span>
                                </div>
                                <div>
                                    <span class="text-gray-500">负责人：</span>
                                    <span>李教授</span>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="text-xs text-gray-500 mb-1">成果：发明专利2项，论文3篇</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 text-sm">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出报告
                    </button>
                    <button onclick="closeProjectDetail()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                        返回列表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 全局UI控制函数 ---
        function openProjectDetail(id) {
            document.getElementById('projectDetail').classList.remove('hidden');
            document.getElementById('projectDetail').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        function closeProjectDetail() {
            document.getElementById('projectDetail').classList.add('translate-x-full');
            setTimeout(() => {
                document.getElementById('projectDetail').classList.add('hidden');
            }, 300);
            document.body.style.overflow = 'auto';
        }

        // --- DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化概览小图表
            const initSmallChart = (id, color) => {
                const ctx = document.getElementById(id).getContext('2d');
                new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                        datasets: [{
                            data: [12, 19, 3, 5],
                            backgroundColor: color,
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: { legend: { display: false } },
                        scales: { x: { display: false }, y: { display: false } }
                    }
                });
            };

            initSmallChart('localChart', 'rgba(59, 130, 246, 0.5)');
            initSmallChart('enterpriseChart', 'rgba(16, 185, 129, 0.5)');
            initSmallChart('instituteChart', 'rgba(139, 92, 246, 0.5)');
            initSmallChart('projectChart', 'rgba(245, 158, 11, 0.5)');

            // 合作投入趋势图
            const trendCtx = document.getElementById('trendChart').getContext('2d');
            new Chart(trendCtx, {
                type: 'line',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '投入金额(万)',
                        data: [320, 450, 680, 920, 1250],
                        borderColor: '#3B82F6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } }
                }
            });

            // 投入产出比图表
            const ratioCtx = document.getElementById('ratioChart').getContext('2d');
            new Chart(ratioCtx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: '地方政府',
                        data: [{x: 120, y: 8}, {x: 180, y: 12}, {x: 250, y: 15}],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        pointRadius: 8
                    }, {
                        label: '企业',
                        data: [{x: 150, y: 12}, {x: 220, y: 18}, {x: 300, y: 24}],
                        backgroundColor: 'rgba(16, 185, 129, 0.8)',
                        pointRadius: 8
                    }, {
                        label: '高校院所',
                        data: [{x: 80, y: 6}, {x: 120, y: 9}, {x: 160, y: 12}],
                        backgroundColor: 'rgba(139, 92, 246, 0.8)',
                        pointRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.dataset.label}: 投入¥${context.parsed.x}万 产出${context.parsed.y}项`;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            title: { display: true, text: '投入金额(万)' }
                        },
                        y: {
                            title: { display: true, text: '产出成果(项)' }
                        }
                    }
                }
            });

            // 点击抽屉外部关闭
            document.addEventListener('click', function(e) {
                const drawer = document.getElementById('projectDetail');
                if (!drawer.contains(e.target) && e.target.closest('[onclick*="openProjectDetail"]') === null && !drawer.classList.contains('hidden')) {
                    closeProjectDetail();
                }
            });
        });
    </script>
</body>
</html>