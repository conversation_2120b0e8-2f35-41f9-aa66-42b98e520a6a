'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  Plus, 
  Search, 
  Filter, 
  Download, 
  QrCode,
  Edit,
  Eye,
  Trash2
} from "lucide-react"

export default function ResearchCodeManagementPage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      {/* 头部区域 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-medium text-gray-900">科研码管理</h1>
          <p className="text-sm text-gray-500 mt-1">管理创新主体的科研码信息</p>
        </div>
        <div className="flex items-center gap-4">
          <Button className="bg-blue-500 hover:bg-blue-600">
            <Plus className="mr-2 h-4 w-4" />
            新建科研码
          </Button>
        </div>
      </div>

      {/* 筛选工具栏 */}
      <Card className="border-[#E5E9EF]">
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input 
                  placeholder="搜索科研码、机构名称..." 
                  className="pl-10"
                />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="机构类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  <SelectItem value="university">高等院校</SelectItem>
                  <SelectItem value="institute">科研院所</SelectItem>
                  <SelectItem value="enterprise">企业</SelectItem>
                  <SelectItem value="hospital">医院</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="active">
                <SelectTrigger className="w-[160px]">
                  <SelectValue placeholder="状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">已激活</SelectItem>
                  <SelectItem value="pending">待审核</SelectItem>
                  <SelectItem value="inactive">已停用</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                高级筛选
              </Button>
            </div>
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              导出数据
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 统计卡片 */}
      <div className="grid grid-cols-4 gap-6">
        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">总科研码数</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">1,248</p>
              </div>
              <div className="h-12 w-12 bg-blue-50 rounded-lg flex items-center justify-center">
                <QrCode className="h-6 w-6 text-blue-500" />
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 12% 较上月
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">已激活</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">1,156</p>
              </div>
              <div className="h-12 w-12 bg-green-50 rounded-lg flex items-center justify-center">
                <Badge className="bg-green-100 text-green-700 h-6 w-6 rounded-full p-0 flex items-center justify-center">✓</Badge>
              </div>
            </div>
            <div className="mt-4 text-sm text-green-600">
              ↑ 8% 较上月
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">待审核</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">64</p>
              </div>
              <div className="h-12 w-12 bg-yellow-50 rounded-lg flex items-center justify-center">
                <Badge className="bg-yellow-100 text-yellow-700 h-6 w-6 rounded-full p-0 flex items-center justify-center">?</Badge>
              </div>
            </div>
            <div className="mt-4 text-sm text-yellow-600">
              ↑ 15% 较上月
            </div>
          </CardContent>
        </Card>

        <Card className="border-[#E5E9EF]">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">已停用</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">28</p>
              </div>
              <div className="h-12 w-12 bg-red-50 rounded-lg flex items-center justify-center">
                <Badge className="bg-red-100 text-red-700 h-6 w-6 rounded-full p-0 flex items-center justify-center">×</Badge>
              </div>
            </div>
            <div className="mt-4 text-sm text-red-600">
              ↓ 5% 较上月
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 科研码列表 */}
      <Card className="border-[#E5E9EF]">
        <CardHeader className="border-b border-[#E5E9EF]">
          <CardTitle>科研码列表</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    科研码
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    机构名称
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    机构类型
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    创建时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {[
                  { code: "RC2024001", name: "清华大学", type: "高等院校", date: "2024-01-15", status: "已激活" },
                  { code: "RC2024002", name: "中科院计算所", type: "科研院所", date: "2024-01-16", status: "已激活" },
                  { code: "RC2024003", name: "华为技术有限公司", type: "企业", date: "2024-01-17", status: "待审核" },
                  { code: "RC2024004", name: "协和医院", type: "医院", date: "2024-01-18", status: "已激活" },
                  { code: "RC2024005", name: "北京理工大学", type: "高等院校", date: "2024-01-19", status: "已停用" }
                ].map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <QrCode className="h-4 w-4 text-blue-500 mr-2" />
                        <span className="text-sm font-medium text-blue-600">{item.code}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {item.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {item.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge 
                        className={
                          item.status === "已激活" ? "bg-green-100 text-green-700" :
                          item.status === "待审核" ? "bg-yellow-100 text-yellow-700" :
                          "bg-red-100 text-red-700"
                        }
                      >
                        {item.status}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-500">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 