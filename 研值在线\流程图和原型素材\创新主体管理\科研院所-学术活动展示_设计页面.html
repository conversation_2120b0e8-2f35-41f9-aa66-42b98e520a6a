<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研院所-学术活动展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">学术活动展示</h1>
        <p class="text-gray-600 mb-6">集中呈现各类学术活动信息，促进学术资源共享与协同创新</p>

        <!-- 活动卡片区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">学术会议</span>
                        <span class="text-sm text-gray-500">剩余名额: 12</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">人工智能与医疗健康前沿论坛</h3>
                    <p class="text-gray-600 mb-4">探讨AI在医疗健康领域的最新应用与发展趋势</p>
                    <div class="flex items-center text-sm text-gray-500 mb-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        2024-03-15 至 2024-03-17
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        宁波市高新区科技广场会议中心
                    </div>
                    <button onclick="openModal('detailModal')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        查看详情
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">学术讲座</span>
                        <span class="text-sm text-gray-500">剩余名额: 45</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">新材料研发与应用研讨会</h3>
                    <p class="text-gray-600 mb-4">分享新材料领域最新研究成果与产业化应用</p>
                    <div class="flex items-center text-sm text-gray-500 mb-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        2024-03-22
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        宁波大学安中大楼报告厅
                    </div>
                    <button onclick="openModal('detailModal')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        查看详情
                    </button>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">培训课程</span>
                        <span class="text-sm text-gray-500">剩余名额: 8</span>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">科研项目管理高级研修班</h3>
                    <p class="text-gray-600 mb-4">提升科研项目管理能力与成果转化效率</p>
                    <div class="flex items-center text-sm text-gray-500 mb-2">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        2024-04-05 至 2024-04-07
                    </div>
                    <div class="flex items-center text-sm text-gray-500 mb-4">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        宁波市科技创新中心
                    </div>
                    <button onclick="openModal('detailModal')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        查看详情
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">活动类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            学术会议
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            学术讲座
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            培训课程
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <div class="flex space-x-2">
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">举办地点</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部地点</option>
                        <option>宁波市高新区</option>
                        <option>宁波市鄞州区</option>
                        <option>宁波市江北区</option>
                        <option>宁波市海曙区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">主办单位</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                        <option>全部单位</option>
                        <option>宁波市科技局</option>
                        <option>宁波大学</option>
                        <option>宁波市科学院</option>
                        <option>宁波市第一医院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">费用区间</label>
                    <div class="flex items-center space-x-2">
                        <input type="range" min="0" max="5000" step="100" class="w-full">
                        <span class="text-sm text-gray-500">0-5000元</span>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">关键字搜索</label>
                    <input type="text" placeholder="输入活动名称或关键词" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 活动列表区 -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">学术活动列表</h2>
                    <div class="flex space-x-2">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                            </svg>
                            新增活动
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">中心议题</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">活动形式</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举办时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举办地点</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">目标对象</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">费用</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主办单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">人工智能与医疗健康前沿论坛</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">探讨AI在医疗健康领域的最新应用与发展趋势</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">学术会议</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-15 至 2024-03-17</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市高新区科技广场会议中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">医疗AI研究人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1200元</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">立即报名</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">新材料研发与应用研讨会</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">分享新材料领域最新研究成果与产业化应用</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">学术讲座</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-03-22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学安中大楼报告厅</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">材料科学研究者</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">免费</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">立即报名</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">科研项目管理高级研修班</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">提升科研项目管理能力与成果转化效率</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <span class="inline-flex px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">培训课程</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-04-05 至 2024-04-07</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技创新中心</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">科研管理人员</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2800元</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科学院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="openModal('detailModal')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                <button class="text-green-600 hover:text-green-900">立即报名</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 28 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">统计分析</h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-2">年度活动数量趋势</h3>
                    <div class="h-64">
                        <canvas id="activityTrendChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-2">活动形式占比</h3>
                    <div class="h-64">
                        <canvas id="activityTypeChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-2">季度报名人数变化</h3>
                    <div class="h-64">
                        <canvas id="registrationTrendChart"></canvas>
                    </div>
                </div>
                <div>
                    <h3 class="text-md font-medium text-gray-900 mb-2">活动覆盖领域对比</h3>
                    <div class="h-64">
                        <canvas id="activityFieldChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 活动详情弹窗 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 w-11/12 max-w-6xl">
            <div class="bg-white rounded-lg shadow-xl overflow-hidden">
                <div class="flex justify-between items-center p-6 border-b">
                    <h3 class="text-xl font-semibold text-gray-900">活动详情</h3>
                    <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="lg:col-span-2">
                            <!-- 活动封面 -->
                            <div class="mb-6">
                                <img src="https://source.unsplash.com/800x400?conference" alt="活动封面" class="w-full rounded-lg">
                            </div>
                            
                            <!-- 标签页导航 -->
                            <div class="border-b border-gray-200 mb-6">
                                <nav class="-mb-px flex space-x-8">
                                    <button class="border-b-2 border-blue-500 text-blue-600 px-4 py-3 text-sm font-medium">详细议程</button>
                                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">演讲嘉宾</button>
                                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">报名须知</button>
                                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">交通住宿</button>
                                    <button class="border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 px-4 py-3 text-sm font-medium">联系方式</button>
                                </nav>
                            </div>
                            
                            <!-- 详细议程内容 -->
                            <div>
                                <h4 class="text-lg font-medium text-gray-900 mb-4">详细议程</h4>
                                <div class="space-y-4">
                                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                                        <div class="text-sm font-medium text-gray-900">第一天 (2024-03-15)</div>
                                        <div class="mt-2 space-y-2">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <span class="text-blue-800">09:00</span>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">开幕式</div>
                                                    <div class="text-sm text-gray-500">宁波市科技局领导致辞</div>
                                                </div>
                                            </div>
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <span class="text-blue-800">10:30</span>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">主题报告: AI在医疗影像分析中的应用</div>
                                                    <div class="text-sm text-gray-500">张教授, 宁波大学医学院</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="border-l-4 border-blue-500 pl-4 py-2">
                                        <div class="text-sm font-medium text-gray-900">第二天 (2024-03-16)</div>
                                        <div class="mt-2 space-y-2">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                    <span class="text-blue-800">09:00</span>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">分组讨论: AI与精准医疗</div>
                                                    <div class="text-sm text-gray-500">主持人: 李研究员, 宁波市第一医院</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div>
                            <!-- 基本信息卡片 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-medium text-gray-900 mb-4">基本信息</h4>
                                <div class="space-y-3 text-sm">
                                    <div>
                                        <span class="text-gray-500">活动名称:</span>
                                        <span class="font-medium text-gray-900">人工智能与医疗健康前沿论坛</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">活动形式:</span>
                                        <span class="font-medium text-gray-900">学术会议</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">举办时间:</span>
                                        <span class="font-medium text-gray-900">2024-03-15 至 2024-03-17</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">举办地点:</span>
                                        <span class="font-medium text-gray-900">宁波市高新区科技广场会议中心</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">目标对象:</span>
                                        <span class="font-medium text-gray-900">医疗AI研究人员</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">费用:</span>
                                        <span class="font-medium text-gray-900">1200元</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">主办单位:</span>
                                        <span class="font-medium text-gray-900">宁波市科技局</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">承办单位:</span>
                                        <span class="font-medium text-gray-900">宁波大学医学院</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">协办单位:</span>
                                        <span class="font-medium text-gray-900">宁波市第一医院</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 报名信息卡片 -->
                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                <h4 class="text-md font-medium text-gray-900 mb-4">报名信息</h4>
                                <div class="space-y-3 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">报名截止:</span>
                                        <span class="font-medium text-gray-900">2024-03-10</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">剩余名额:</span>
                                        <span class="font-medium text-gray-900">12</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-500">当前报名人数:</span>
                                        <span class="font-medium text-gray-900">48</span>
                                    </div>
                                    <div class="pt-4">
                                        <button class="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                            立即报名
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="space-y-3">
                                <button class="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    导出PDF
                                </button>
                                <button class="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    订阅日历
                                </button>
                                <button class="w-full px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                                    </svg>
                                    分享链接
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化图表
            const activityTrendCtx = document.getElementById('activityTrendChart');
            if (activityTrendCtx) {
                new Chart(activityTrendCtx.getContext('2d'), {
                    type: 'bar',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        datasets: [{
                            label: '活动数量',
                            data: [5, 8, 12, 10, 15, 18, 20, 16, 14, 10, 8, 6],
                            backgroundColor: 'rgba(59, 130, 246, 0.7)'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            const activityTypeCtx = document.getElementById('activityTypeChart');
            if (activityTypeCtx) {
                new Chart(activityTypeCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['学术会议', '学术讲座', '培训课程'],
                        datasets: [{
                            data: [45, 35, 20],
                            backgroundColor: [
                                'rgba(59, 130, 246, 0.7)',
                                'rgba(16, 185, 129, 0.7)',
                                'rgba(139, 92, 246, 0.7)'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            const registrationTrendCtx = document.getElementById('registrationTrendChart');
            if (registrationTrendCtx) {
                new Chart(registrationTrendCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                        datasets: [{
                            label: '报名人数',
                            data: [120, 180, 210, 160],
                            borderColor: 'rgba(59, 130, 246, 0.7)',
                            backgroundColor: 'rgba(59, 130, 246, 0.1)',
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            const activityFieldCtx = document.getElementById('activityFieldChart');
            if (activityFieldCtx) {
                new Chart(activityFieldCtx.getContext('2d'), {
                    type: 'radar',
                    data: {
                        labels: ['生物医学', '材料科学', '信息技术', '环境科学', '工程技术'],
                        datasets: [
                            {
                                label: '宁波市科技局',
                                data: [85, 70, 65, 80, 75],
                                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                                borderColor: 'rgba(59, 130, 246, 0.8)',
                                borderWidth: 2
                            },
                            {
                                label: '宁波大学',
                                data: [65, 85, 60, 70, 80],
                                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                                borderColor: 'rgba(16, 185, 129, 0.8)',
                                borderWidth: 2
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false
                    }
                });
            }

            // 绑定弹窗点击外部关闭事件
            const modals = document.querySelectorAll('[id$="Modal"]');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });
            });

            // 绑定ESC键关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    modals.forEach(modal => {
                        if (!modal.classList.contains('hidden')) {
                            closeModal(modal.id);
                        }
                    });
                }
            });
        });
    </script>
</body>
</html>