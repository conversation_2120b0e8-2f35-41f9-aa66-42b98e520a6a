<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多维度钻取交互分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 面包屑导航区 -->
        <div class="bg-white rounded-lg shadow-md p-4 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2 text-sm">
                    <span class="text-gray-500">当前路径：</span>
                    <button class="text-blue-600 hover:text-blue-800">全部指南</button>
                    <span class="text-gray-400">></span>
                    <span id="breadcrumb-path" class="text-gray-700"></span>
                </div>
                <div class="flex space-x-2">
                    <button id="back-button" class="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        返回上一级
                    </button>
                    <button id="reset-button" class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        重置
                    </button>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- 主图表区 -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 id="chart-title" class="text-lg font-semibold text-gray-900 mb-4">全部指南统计</h2>
                    <div class="h-96">
                        <canvas id="mainChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 维度联动面板 -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">维度筛选</h3>
                    <div class="space-y-4">
                        <!-- 技术领域 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">技术领域</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">高端装备</span>
                                    <span class="text-xs text-gray-500">1,245</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">新材料</span>
                                    <span class="text-xs text-gray-500">987</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">生物医药</span>
                                    <span class="text-xs text-gray-500">856</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">数字经济</span>
                                    <span class="text-xs text-gray-500">723</span>
                                </div>
                            </div>
                        </div>
                        <!-- 发布单位 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">发布单位</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">宁波市科技局</span>
                                    <span class="text-xs text-gray-500">2,345</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">宁波市经信局</span>
                                    <span class="text-xs text-gray-500">1,876</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">宁波市发改委</span>
                                    <span class="text-xs text-gray-500">1,234</span>
                                </div>
                            </div>
                        </div>
                        <!-- 计划类别 -->
                        <div>
                            <h4 class="text-sm font-medium text-gray-700 mb-2">计划类别</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">重大专项</span>
                                    <span class="text-xs text-gray-500">1,567</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">重点研发</span>
                                    <span class="text-xs text-gray-500">1,234</span>
                                </div>
                                <div class="flex justify-between items-center p-2 hover:bg-gray-50 rounded cursor-pointer">
                                    <span class="text-sm">基础研究</span>
                                    <span class="text-xs text-gray-500">876</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 指南详情弹窗 -->
        <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                    <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                        <h3 class="text-lg font-semibold text-gray-900">指南详情</h3>
                        <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-6 overflow-y-auto flex-1">
                        <div class="space-y-6">
                            <!-- 基础信息 -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">指南名称</label>
                                    <p class="text-sm text-gray-900">高端装备制造关键技术研发与应用</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">发布单位</label>
                                    <p class="text-sm text-gray-900">宁波市科技局</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                                    <p class="text-sm text-gray-900">高端装备</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">计划类别</label>
                                    <p class="text-sm text-gray-900">重大专项</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">申报截止日期</label>
                                    <p class="text-sm text-gray-900">2024-06-30</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">资助额度</label>
                                    <p class="text-sm text-gray-900">200万元</p>
                                </div>
                            </div>

                            <!-- 研究内容 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">研究内容</label>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <p class="text-sm text-gray-700">本项目针对宁波市高端装备制造业发展需求，重点突破智能制造、精密加工等关键技术，研发具有自主知识产权的高端装备产品，提升宁波市装备制造业核心竞争力。</p>
                                </div>
                            </div>

                            <!-- 考核指标 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">考核指标</label>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <ul class="list-disc list-inside text-sm text-gray-700 space-y-1">
                                        <li>开发高端装备产品2-3种</li>
                                        <li>申请发明专利5项以上</li>
                                        <li>形成产业化生产线1条</li>
                                        <li>实现年产值5000万元以上</li>
                                    </ul>
                                </div>
                            </div>

                            <!-- 申报要求 -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">申报要求</label>
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <ul class="list-disc list-inside text-sm text-gray-700 space-y-1">
                                        <li>申报单位应为宁波市注册企业</li>
                                        <li>具有相关领域研发基础</li>
                                        <li>项目团队核心成员不少于5人</li>
                                        <li>配套资金不低于财政资助金额</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                        <button onclick="closeModal('detailModal')" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                            关闭
                        </button>
                        <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                            复制信息
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化主图表
            const mainCtx = document.getElementById('mainChart').getContext('2d');
            const mainChart = new Chart(mainCtx, {
                type: 'bar',
                data: {
                    labels: ['高端装备', '新材料', '生物医药', '数字经济', '新能源', '其他'],
                    datasets: [{
                        label: '指南数量',
                        data: [1245, 987, 856, 723, 567, 432],
                        backgroundColor: 'rgba(59, 130, 246, 0.8)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    return '占比: ' + ((context.raw / 4829) * 100).toFixed(1) + '%';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    onClick: function(evt, elements) {
                        if (elements.length > 0) {
                            const index = elements[0].index;
                            const label = this.data.labels[index];
                            document.getElementById('breadcrumb-path').textContent = '技术领域 > ' + label;
                            document.getElementById('chart-title').textContent = '技术领域: ' + label + ' 下的发布单位分布';
                            
                            // 模拟下钻数据
                            mainChart.data.labels = ['宁波市科技局', '宁波市经信局', '宁波市发改委', '其他单位'];
                            mainChart.data.datasets[0].data = [567, 432, 321, 125];
                            mainChart.update();
                            
                            // 显示详情弹窗
                            openModal('detailModal');
                        }
                    }
                }
            });

            // 绑定返回按钮
            document.getElementById('back-button').addEventListener('click', function() {
                // 模拟返回上一级
                document.getElementById('breadcrumb-path').textContent = '';
                document.getElementById('chart-title').textContent = '全部指南统计';
                
                // 恢复初始数据
                mainChart.data.labels = ['高端装备', '新材料', '生物医药', '数字经济', '新能源', '其他'];
                mainChart.data.datasets[0].data = [1245, 987, 856, 723, 567, 432];
                mainChart.update();
            });

            // 绑定重置按钮
            document.getElementById('reset-button').addEventListener('click', function() {
                document.getElementById('breadcrumb-path').textContent = '';
                document.getElementById('chart-title').textContent = '全部指南统计';
                
                // 恢复初始数据
                mainChart.data.labels = ['高端装备', '新材料', '生物医药', '数字经济', '新能源', '其他'];
                mainChart.data.datasets[0].data = [1245, 987, 856, 723, 567, 432];
                mainChart.update();
            });

            // 维度联动面板点击事件
            document.querySelectorAll('#dimension-panel [data-dimension]').forEach(item => {
                item.addEventListener('click', function() {
                    const dimension = this.getAttribute('data-dimension');
                    const value = this.getAttribute('data-value');
                    
                    // 更新面包屑
                    const currentPath = document.getElementById('breadcrumb-path').textContent;
                    document.getElementById('breadcrumb-path').textContent = currentPath ? currentPath + ' > ' + value : dimension + ' > ' + value;
                    
                    // 更新图表标题
                    document.getElementById('chart-title').textContent = '筛选条件下的指南分布';
                    
                    // 模拟数据更新
                    mainChart.data.labels = ['高端装备', '新材料', '生物医药'];
                    mainChart.data.datasets[0].data = [345, 267, 189];
                    mainChart.update();
                });
            });

            // 点击弹窗外部关闭
            document.getElementById('detailModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal('detailModal');
                }
            });
        });
    </script>
</body>
</html>