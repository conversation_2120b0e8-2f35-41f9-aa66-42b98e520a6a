unit CcDdYs;

interface
uses
  Classes;

type
  TCcDdYs = class
  private
    FDdYsid: integer;
    FDdid: integer;
    FDdxh: integer;
    FDdh: string;
    FDdKh: string;
    FXdrq: string;
    FChrq: string;
    FYsid: integer;
    FBjbh: string;
    FYsKh: string;
    FBjrq: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    FKsbm: string;
  public
    property DdYsid: integer read FDdYsid write FDdYsid;
    property Ddid: integer read FDdid write FDdid;
    property Ddxh: integer read FDdxh write FDdxh;
    property Ddh: string read FDdh write FDdh;
    property DdKh: string read FDdKh write FDdKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ysid: integer read FYsid write FYsid;
    property Bjbh: string read FBjbh write FBjbh;
    property YsKh: string read FYsKh write FYsKh;
    property Bjrq: string read FBjrq write FBjrq;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Ksbm: string read FKsbm write FKsbm;
  end;
implementation

end.

