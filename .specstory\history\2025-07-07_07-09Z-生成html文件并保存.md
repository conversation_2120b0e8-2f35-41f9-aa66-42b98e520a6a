<!-- Generated by SpecStory -->

# 生成html文件并保存 (2025-07-07 07:09Z)

_**User**_

@企业-项目展示_设计页面.md 请处理并生成同名的html文件，保存在public目录下

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求我根据提供的markdown文件生成一个HTML文件。让我仔细分析这个文档：

1. 这是一个功能设计文档，功能名是 `企业-项目展示`
2. 需求分析：
   - 面向企业创新主体，展示研发项目与高新技术产业投资项目的全生命周期信息
   - 需要可视化概览与可下钻的多维列表
   - 支持按项目类型、行业领域等多条件筛选
   - 提供项目详情深度浏览能力

3. 界面结构包括：
   - 统计概览区：卡片形式展示核心指标，有环形进度条
   - 条件筛选区：项目类型、行业领域、状态、时间范围、关键字搜索
   - 项目列表区：数据网格，包含项目名称、类别、行业、负责人、阶段、金额等
   - 关联图表区：可折叠面板，柱状图、堆积面积图、散点图
   - 项目详情抽屉：滑出抽屉展示详细信息

从这个需求来看，这个页面既有管理后台的特征（列表、筛选、详情），也有数据大屏的特征（统计概览、图表）。但从整体功能来看，更偏向于管理后台，因为主要是项目管理和查看，而不是数据监控。

所以我应该使用管理后台的设计心法和代码范例。</details></think>

---



---



---

