<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校-院校基础信息</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 头部概览区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div class="flex items-center mb-4 md:mb-0">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center mr-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">宁波大学</h1>
                        <p class="text-gray-500">统一社会信用代码：123456789012345678</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                        </svg>
                        官网访问
                    </button>
                    <div class="w-10 h-10 bg-gray-100 rounded-md flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2m6 0H10m0 0H8m4 0h4m-4-6v6m-6 0h12a2 2 0 002-2V8a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 左侧内容区 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 基础信息卡片区 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white rounded-lg shadow-md p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">基本属性</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">建校年份</span>
                                <span class="font-medium">1986年</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">办学层次</span>
                                <span class="font-medium">本科/研究生</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">主管部门</span>
                                <span class="font-medium">浙江省教育厅</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">所在地区</span>
                                <span class="font-medium">浙江省宁波市</span>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-400">更新时间：2024-01-15</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">规模数据</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">校园面积</span>
                                <span class="font-medium">3000亩</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">在校生规模</span>
                                <span class="font-medium">25,000人</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">师资数量</span>
                                <span class="font-medium">1,500人</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">博士点</span>
                                <span class="font-medium">45个</span>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-400">更新时间：2024-01-10</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">学科建设</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">学科门类</span>
                                <span class="font-medium">11个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">本科专业</span>
                                <span class="font-medium">75个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">硕士点</span>
                                <span class="font-medium">125个</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">国家重点学科</span>
                                <span class="font-medium">6个</span>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-400">更新时间：2023-12-20</div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-4">
                        <h3 class="text-lg font-medium text-gray-900 mb-3">科研实力</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">科研经费</span>
                                <span class="font-medium">5.8亿元</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">国家奖项</span>
                                <span class="font-medium">12项</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">专利授权</span>
                                <span class="font-medium">1,200件</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">国际合作</span>
                                <span class="font-medium">35个国家</span>
                            </div>
                        </div>
                        <div class="mt-4 text-xs text-gray-400">更新时间：2024-01-05</div>
                    </div>
                </div>

                <!-- 特色标签区 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">特色标签</h3>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm cursor-pointer hover:bg-blue-200">双一流建设高校</span>
                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm cursor-pointer hover:bg-green-200">国家重点实验室</span>
                        <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm cursor-pointer hover:bg-purple-200">省部共建协同创新中心</span>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm cursor-pointer hover:bg-yellow-200">教育部重点实验室</span>
                        <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm cursor-pointer hover:bg-red-200">国家工程技术研究中心</span>
                        <span class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm cursor-pointer hover:bg-indigo-200">产学研示范基地</span>
                        <span class="px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm cursor-pointer hover:bg-pink-200">卓越工程师培养计划</span>
                        <span class="px-3 py-1 bg-teal-100 text-teal-800 rounded-full text-sm cursor-pointer hover:bg-teal-200">国家级特色专业</span>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="space-y-6">
                <!-- 地图定位 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-gray-900">地理位置</h3>
                        <button class="text-blue-600 text-sm">收起</button>
                    </div>
                    <div class="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <div class="mt-4 text-sm text-gray-600">
                        <p>主校区：浙江省宁波市江北区风华路818号</p>
                        <p class="mt-1">占地面积：3000亩</p>
                    </div>
                </div>

                <!-- 学科实力雷达图 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">学科实力评估</h3>
                    <div class="h-64">
                        <canvas id="radarChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 数据溯源与声明区 -->
        <div class="mt-6 bg-blue-50 rounded-lg p-4">
            <div class="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                    <p class="text-sm text-blue-700">数据来源：教育部高校数据平台、浙江省教育厅、宁波大学官网公开数据</p>
                    <p class="text-sm text-blue-700 mt-1">更新周期：每月15日自动更新 | 最后更新：2024-01-15</p>
                    <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">纠错反馈</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 雷达图初始化
        document.addEventListener('DOMContentLoaded', function() {
            const radarCtx = document.getElementById('radarChart').getContext('2d');
            new Chart(radarCtx, {
                type: 'radar',
                data: {
                    labels: ['学科广度', '科研产出', '师资力量', '国际交流', '社会服务', '创新水平'],
                    datasets: [{
                        label: '宁波大学',
                        data: [85, 78, 82, 75, 80, 77],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                        pointRadius: 4
                    }, {
                        label: '全省平均',
                        data: [65, 60, 68, 55, 62, 58],
                        backgroundColor: 'rgba(156, 163, 175, 0.2)',
                        borderColor: 'rgba(156, 163, 175, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(156, 163, 175, 1)',
                        pointRadius: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true,
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            suggestedMin: 0,
                            suggestedMax: 100,
                            ticks: {
                                stepSize: 20,
                                backdropColor: 'transparent'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        });

        // 官网访问按钮点击事件
        document.querySelector('button').addEventListener('click', function() {
            window.open('https://www.nbu.edu.cn', '_blank');
        });
    </script>
</body>
</html>