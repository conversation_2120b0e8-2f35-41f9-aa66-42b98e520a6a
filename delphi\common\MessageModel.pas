unit MessageModel;

interface

uses
  Classes;

type
  TMessageModel = class
  private
    FMessagemainid: integer;
    FMessagetypenum: integer;
    FMessagetypesrcnum: integer;
    FMessagetypesrcuser: string;
    FMessagetypetargetnum: integer;
    FMessagetypetargetuser: string;
    FTitle: string;
    FContent: string;
    FNote: string;
    FReplycontent: string;
    FReplynote: string;
    FMessagestate: integer;
    FMessageAuditstate: integer;
    FDdid: integer;
    FYsid: integer;
    FCwid: integer;
    FMessagedate: string;
    FMessagereplydate: string;
    FKh: string;
    FDdh: string;
    FKz: string;
    FLb: string;
    FMl: string;
    FKs: string;
    FYs: string;
    FDds: integer;
    FSys: integer;
    FYss: integer;
    FYjTypeName: string;
    FBjIndexStr: string;
    FMlIndexStr: string;
    FFlIndexStr: string;
    FYxhIndexStr: string;
    FGjIndexStr: string;
    FChIndexStr: string;
    Fzrr1: string;
    Fzrr2: string;
    FYjtypedl: string;
    FYjtypexl: string;
    Fpeik: string;
    Fxdrq: string;
    Fsccj: string;
    Fscnote: string;
    Fts1: integer;
    Fts2: integer;
    Fchrq: string;
  public
    property Messagemainid: integer read FMessagemainid write FMessagemainid;
    property Messagetypenum: integer read FMessagetypenum write FMessagetypenum;
    property Messagetypesrcnum: integer read FMessagetypesrcnum
      write FMessagetypesrcnum;
    property Messagetypesrcuser: string read FMessagetypesrcuser
      write FMessagetypesrcuser;
    property Messagetypetargetnum: integer read FMessagetypetargetnum
      write FMessagetypetargetnum;
    property Messagetypetargetuser: string read FMessagetypetargetuser
      write FMessagetypetargetuser;
    property Title: string read FTitle write FTitle;
    property Content: string read FContent write FContent;
    property Note: string read FNote write FNote;
    property Replycontent: string read FReplycontent write FReplycontent;
    property Replynote: string read FReplynote write FReplynote;
    property Messagestate: integer read FMessagestate write FMessagestate;
    property MessageAuditstate: integer read FMessageAuditstate
      write FMessageAuditstate;
    property Ddid: integer read FDdid write FDdid;
    property Ysid: integer read FYsid write FYsid;
    property Cwid: integer read FCwid write FCwid;
    property Messagedate: string read FMessagedate write FMessagedate;
    property Messagereplydate: string read FMessagereplydate
      write FMessagereplydate;
    property Kh: string read FKh write FKh;
    property Ddh: string read FDdh write FDdh;
    property Kz: string read FKz write FKz;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Ys: string read FYs write FYs;
    property Dds: integer read FDds write FDds;
    property Sys: integer read FSys write FSys;
    property Yss: integer read FYss write FYss;
    property YjTypeName: string read FYjTypeName write FYjTypeName;
    property BjIndexStr: string read FBjIndexStr write FBjIndexStr;
    property MlIndexStr: string read FMlIndexStr write FMlIndexStr;
    property FlIndexStr: string read FFlIndexStr write FFlIndexStr;
    property YxhIndexStr: string read FYxhIndexStr write FYxhIndexStr;
    property GjIndexStr: string read FGjIndexStr write FGjIndexStr;
    property ChIndexStr: string read FChIndexStr write FChIndexStr;
    property Peik: string read Fpeik write Fpeik;
    property zrr1: string read Fzrr1 write Fzrr1;
    property zrr2: string read Fzrr2 write Fzrr2;
    property Yjtypedl: string read FYjtypedl write FYjtypedl;
    property Yjtypexl: string read FYjtypexl write FYjtypexl;
    property xdrq: string read Fxdrq write Fxdrq;
    property sccj: string read Fsccj write Fsccj;
    property scnote: string read Fscnote write Fscnote;
    property ts1: integer read Fts1 write Fts1;
    property ts2: integer read Fts2 write Fts2;
    property chrq: string read Fchrq write Fchrq;
  end;

implementation

end.
