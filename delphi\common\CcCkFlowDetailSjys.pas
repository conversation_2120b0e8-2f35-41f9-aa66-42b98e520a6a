unit CcCkFlowDetailSjys;

interface

uses
  Classes;

type
  TCcCkFlowDetailSjys = class
  private
    FCkflowdetailsjysid: integer;
    FCkflowid: integer;

    FPbPs1: double;
    FPbSb1: double;
    FPbSh1: double;
    FPbPm1: string;
    FPbGg1: string;
    FPbGys1: string;
    FPbAmount1: double;

    FPbPs2: double;
    FPbSb2: double;
    FPbSh2: double;
    FPbPm2: string;
    FPbGg2: string;
    FPbGys2: string;
    FPbAmount2: double;

    FPbPs3: double;
    FPbSb3: double;
    FPbSh3: double;
    FPbPm3: string;
    FPbGg3: string;
    FPbGys3: string;
    FPbAmount3: double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Ckflowid: integer read FCkflowid write FCkflowid;
    property Ckflowdetailsjysid: integer read FCkflowdetailsjysid
      write FCkflowdetailsjysid;

    property PbPs1: double read FPbPs1 write FPbPs1;
    property PbSb1: double read FPbSb1 write FPbSb1;
    property PbSh1: double read FPbSh1 write FPbSh1;
    property PbPm1: string read FPbPm1 write FPbPm1;
    property PbGg1: string read FPbGg1 write FPbGg1;
    property PbGys1: string read FPbGys1 write FPbGys1;
    property PbAmount1: double read FPbAmount1 write FPbAmount1;

    property PbPs2: double read FPbPs2 write FPbPs2;
    property PbSb2: double read FPbSb2 write FPbSb2;
    property PbSh2: double read FPbSh2 write FPbSh2;
    property PbPm2: string read FPbPm2 write FPbPm2;
    property PbGg2: string read FPbGg2 write FPbGg2;
    property PbGys2: string read FPbGys2 write FPbGys2;
    property PbAmount2: double read FPbAmount2 write FPbAmount2;

    property PbPs3: double read FPbPs3 write FPbPs3;
    property PbSb3: double read FPbSb3 write FPbSb3;
    property PbSh3: double read FPbSh3 write FPbSh3;
    property PbPm3: string read FPbPm3 write FPbPm3;
    property PbGg3: string read FPbGg3 write FPbGg3;
    property PbGys3: string read FPbGys3 write FPbGys3;
    property PbAmount3: double read FPbAmount3 write FPbAmount3;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
