<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1100" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">标引管理业务流程</text>

  <!-- 阶段一：标引新增与修改 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：标引新增与修改</text>
  
  <!-- 节点1: 运维人员操作 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">运维人员操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">新增或修改标引</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">名称编码唯一性与层级合法性</text>
  </g>

  <!-- 节点3: 生成待审核记录 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成待审核记录</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">标记状态为待审核</text>
  </g>

  <!-- 节点4: 批量导入 -->
  <g transform="translate(1000, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">逐行校验上传文件</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 320 165 Q 360 165 400 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 620 165 Q 660 165 700 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="660" y="155" text-anchor="middle" font-size="12" fill="#555">校验通过</text>

  <!-- 连接线 4 -> 3 -->
  <path d="M 1000 165 C 950 165, 900 165, 920 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与启用 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与启用</text>

  <!-- 节点5: 推送管理员 -->
  <g transform="translate(200, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送管理员</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">待审核记录推送</text>
  </g>

  <!-- 节点6: 管理员审批 -->
  <g transform="translate(500, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员审批</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">核对无误后审批通过</text>
  </g>

  <!-- 节点7: 状态更新 -->
  <g transform="translate(800, 320)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">已启用并写入缓存</text>
  </g>

  <!-- 连接线 生成记录 -> 推送管理员 -->
  <path d="M 810 200 C 810 240, 400 280, 310 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 420 355 Q 460 355 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 720 355 Q 760 355 800 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：停用与删除管理 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：停用与删除管理</text>

  <!-- 节点8: 用户操作 -->
  <g transform="translate(150, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">停用或删除标引</text>
  </g>

  <!-- 节点9: 引用检查 -->
  <g transform="translate(450, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">引用检查</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">检查是否被指南引用</text>
  </g>

  <!-- 节点10: 执行操作 -->
  <g transform="translate(750, 520)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">执行操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">同步更新缓存</text>
  </g>

  <!-- 节点11: 阻止操作 -->
  <g transform="translate(450, 650)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">阻止操作</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">提示处理依赖</text>
  </g>

  <!-- 连接线 8 -> 9 -->
  <path d="M 370 555 Q 410 555 450 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 670 555 Q 710 555 750 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="710" y="545" text-anchor="middle" font-size="12" fill="#555">无引用</text>

  <!-- 连接线 9 -> 11 -->
  <path d="M 560 590 Q 560 620 560 650" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="580" y="620" text-anchor="middle" font-size="12" fill="#555">存在引用</text>

  <!-- 阶段四：定时扫描与审计 -->
  <text x="700" y="800" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：定时扫描与审计</text>

  <!-- 节点12: 定时扫描 -->
  <g transform="translate(200, 840)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时扫描</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">每日扫描标引库</text>
  </g>

  <!-- 节点13: 修复任务 -->
  <g transform="translate(500, 840)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">修复任务</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">生成修复任务推送运维</text>
  </g>

  <!-- 节点14: 审计记录 -->
  <g transform="translate(800, 840)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计记录</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">变更操作写入审计库</text>
  </g>

  <!-- 连接线 12 -> 13 -->
  <path d="M 420 875 Q 460 875 500 875" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 13 -> 14 -->
  <path d="M 720 875 Q 760 875 800 875" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：修复任务回到运维操作 -->
  <path d="M 500 840 C 50 840, 50 165, 100 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="75" y="500" text-anchor="middle" font-size="12" fill="#666" transform="rotate(-90, 75, 500)">修复反馈循环</text>

  <!-- 历史版本循环：状态更新到历史版本 -->
  <path d="M 910 390 C 1150 390, 1150 165, 1000 165" stroke="#666" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1075" y="280" text-anchor="middle" font-size="12" fill="#666" transform="rotate(90, 1075, 280)">历史版本</text>

</svg>