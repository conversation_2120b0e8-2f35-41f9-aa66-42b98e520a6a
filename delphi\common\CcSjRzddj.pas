unit CcSjRzddj;

interface
uses
  Classes;

type
  TCcSjRzddj = class
  private

    FSjrzddjid: Integer;
    FDdid: Integer;
    FRzdgysid: string;
    FRzd_a: Double;
    FDj_a: Double;
    FRzd_b: Double;
    FDj_b: Double;
    FRzdcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;


  public
    property Sjrzddjid: integer read FSjrzddjid write FSjrzddjid;
    property Ddid: integer read FDdid write FDdid;
    property Rzdgysid: string read FRzdgysid write FRzdgysid;
    property Rzd_a: double read FRzd_a write FRzd_a;
    property Dj_a: double read FDj_a write FDj_a;
    property Rzd_b: double read FRzd_b write FRzd_b;
    property Dj_b: double read FDj_b write FDj_b;
    property Rzdcb: double read FRzdcb write FRzdcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarket<PERSON>ser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

