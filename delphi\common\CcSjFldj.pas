unit CcSjFldj;

interface

uses
  Classes;

type
  TCcSjFldj = class
  private

    FSjfldjid: Integer;
    FDdid: Integer;

    FDdh: string;
    FKh: string;
    FXdrq: string;
    FChrq: string;
    FYwpm: string;
    FZwpm: string;
    FKsbm: string;
    FSjh: string;
    FDds: Integer;
    FLb: string;
    FMl: string;
    FKs: string;
    FKz: string;
    FYs: string;
    Fwczt: string;

    FSbsys: Double;
    FSbdj: Double;
    FSbje: Double;

    FSbsys_a: Double;
    FSbdj_a: Double;
    FSbje_a: Double;

    FSbsys_b: Double;
    FSbdj_b: Double;
    FSbje_b: Double;

    FSbsys_c: Double;
    FSbdj_c: Double;
    FSbje_c: Double;

    FSbsys_d: Double;
    FSbdj_d: Double;
    FSbje_d: Double;

    FSbzje: Double;

    FDpsys: Double;
    FDpdj: Double;
    FDpje: Double;

    FDpsys_a: Double;
    FDpdj_a: Double;
    FDpje_a: Double;

    FDpsys_b: Double;
    FDpdj_b: Double;
    FDpje_b: Double;

    FDpsys_c: Double;
    FDpdj_c: Double;
    FDpje_c: Double;

    FDpsys_d: Double;
    FDpdj_d: Double;
    FDpje_d: Double;

    FDpzje: Double;

    FXsys: Double;
    FXdj: Double;
    FXje: Double;

    FXsys_a: Double;
    FXdj_a: Double;
    FXje_a: Double;

    FXsys_b: Double;
    FXdj_b: Double;
    FXje_b: Double;

    FXsys_c: Double;
    FXdj_c: Double;
    FXje_c: Double;

    FXzje: Double;

    FNksys: Double;
    FNkdj: Double;
    FNkje: Double;

    FNksys_a: Double;
    FNkdj_a: Double;
    FNkje_a: Double;

    FNksys_b: Double;
    FNkdj_b: Double;
    FNkje_b: Double;

    FNksys_c: Double;
    FNkdj_c: Double;
    FNkje_c: Double;

    FNkzje: Double;

    FLlsys: Double;
    FLldj: Double;
    FLlje: Double;

    FLlsys_a: Double;
    FLldj_a: Double;
    FLlje_a: Double;

    FLlsys_b: Double;
    FLldj_b: Double;
    FLlje_b: Double;

    FLlsys_c: Double;
    FLldj_c: Double;
    FLlje_c: Double;

    FLlzje: Double;

    FNhcsys: Double;
    FNhcdj: Double;
    FNhcje: Double;

    FNhcsys_a: Double;
    FNhcdj_a: Double;
    FNhcje_a: Double;

    FNhcsys_b: Double;
    FNhcdj_b: Double;
    FNhcje_b: Double;

    FNhczje: Double;

    FXjdsys: Double;
    FXjddj: Double;
    FXjdje: Double;

    FXjdsys_a: Double;
    FXjddj_a: Double;
    FXjdje_a: Double;

    FXjdsys_b: Double;
    FXjddj_b: Double;
    FXjdje_b: Double;

    FXjdzje: Double;

    FJdggid: string;
    FJdsys: Double;
    FJddj: Double;
    FJdje: Double;
    FTxmsys: Double;
    FTxmdj: Double;
    FTxmje: Double;
    FCzsys: Double;
    FCzdj: Double;
    FCzje: Double;
    FCbsys: Double;
    FCbdj: Double;
    FCbje: Double;
    FBzqtdj: Double;
    FBzqtsh: Double;
    FBzqtje: Double;
    FZxsys: Double;
    FZxdj: Double;
    FZxje: Double;
    FFgdsys: Double;
    FFgddj: Double;
    FFgdje: Double;
    FPmbmid_a: string;
    FQtsl_a: Double;
    FQtdj_a: Double;
    FQtje_a: Double;
    FPmbmid_b: string;
    FQtsl_b: Double;
    FQtdj_b: Double;
    FQtje_b: Double;
    FBz: string;

    FZdsys: Double;
    FZddj: Double;
    FZdje: Double;
    FLldsys: Double;
    FLlddj: Double;
    FLldje: Double;
    FPmbmid_c: string;
    FQtsl_c: Double;
    FQtdj_c: Double;
    FQtje_c: Double;

    FSbsh_a: Double;
    FSbsh_b: Double;
    FSbsh_c: Double;
    FSbsh_d: Double;

    FDpsh_a: Double;
    FDpsh_b: Double;
    FDpsh_c: Double;
    FDpsh_d: Double;

    FXsh_a: Double;
    FXsh_b: Double;
    FXsh_c: Double;

    FNksh_a: Double;
    FNksh_b: Double;
    FNksh_c: Double;

    FLlsh_a: Double;
    FLlsh_b: Double;
    FLlsh_c: Double;

    FXjdsh_a: Double;
    FXjdsh_b: Double;

    FNhcsh_a: Double;
    FNhcsh_b: Double;

    FQtsh_a: Double;
    FQtsh_b: Double;
    FQtsh_c: Double;

    FTxmsh: Double;
    FCzsh: Double;
    FCbsh: Double;
    FJdsh: Double;
    FZxsh: Double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Sjfldjid: Integer read FSjfldjid write FSjfldjid;
    property Ddid: Integer read FDdid write FDdid;

    property Ddh: string read FDdh write FDdh;
    property Kh: string read FKh write FKh;
    property Xdrq: string read FXdrq write FXdrq;
    property Chrq: string read FChrq write FChrq;
    property Ywpm: string read FYwpm write FYwpm;
    property Zwpm: string read FZwpm write FZwpm;
    property Ksbm: string read FKsbm write FKsbm;
    property Sjh: string read FSjh write FSjh;
    property Dds: Integer read FDds write FDds;
    property Lb: string read FLb write FLb;
    property Ml: string read FMl write FMl;
    property Ks: string read FKs write FKs;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Wczt: string read FWczt write FWczt;

    property Sbsys: Double read FSbsys write FSbsys;
    property Sbdj: Double read FSbdj write FSbdj;
    property Sbje: Double read FSbje write FSbje;

    property Sbsys_a: Double read FSbsys_a write FSbsys_a;
    property Sbdj_a: Double read FSbdj_a write FSbdj_a;
    property Sbje_a: Double read FSbje_a write FSbje_a;

    property Sbsys_b: Double read FSbsys_b write FSbsys_b;
    property Sbdj_b: Double read FSbdj_b write FSbdj_b;
    property Sbje_b: Double read FSbje_b write FSbje_b;

    property Sbsys_c: Double read FSbsys_c write FSbsys_c;
    property Sbdj_c: Double read FSbdj_c write FSbdj_c;
    property Sbje_c: Double read FSbje_c write FSbje_c;

    property Sbsys_d: Double read FSbsys_d write FSbsys_d;
    property Sbdj_d: Double read FSbdj_d write FSbdj_d;
    property Sbje_d: Double read FSbje_d write FSbje_d;

    property Sbzje: Double read FSbzje write FSbzje;

    property Dpsys: Double read FDpsys write FDpsys;
    property Dpdj: Double read FDpdj write FDpdj;
    property Dpje: Double read FDpje write FDpje;

    property Dpsys_a: Double read FDpsys_a write FDpsys_a;
    property Dpdj_a: Double read FDpdj_a write FDpdj_a;
    property Dpje_a: Double read FDpje_a write FDpje_a;

    property Dpsys_b: Double read FDpsys_b write FDpsys_b;
    property Dpdj_b: Double read FDpdj_b write FDpdj_b;
    property Dpje_b: Double read FDpje_b write FDpje_b;

    property Dpsys_c: Double read FDpsys_c write FDpsys_c;
    property Dpdj_c: Double read FDpdj_c write FDpdj_c;
    property Dpje_c: Double read FDpje_c write FDpje_c;

    property Dpsys_d: Double read FDpsys_d write FDpsys_d;
    property Dpdj_d: Double read FDpdj_d write FDpdj_d;
    property Dpje_d: Double read FDpje_d write FDpje_d;

    property Dpzje: Double read FDpzje write FDpzje;

    property Xsys: Double read FXsys write FXsys;
    property Xdj: Double read FXdj write FXdj;
    property Xje: Double read FXje write FXje;

    property Xsys_a: Double read FXsys_a write FXsys_a;
    property Xdj_a: Double read FXdj_a write FXdj_a;
    property Xje_a: Double read FXje_a write FXje_a;

    property Xsys_b: Double read FXsys_b write FXsys_b;
    property Xdj_b: Double read FXdj_b write FXdj_b;
    property Xje_b: Double read FXje_b write FXje_b;

    property Xsys_c: Double read FXsys_c write FXsys_c;
    property Xdj_c: Double read FXdj_c write FXdj_c;
    property Xje_c: Double read FXje_c write FXje_c;

    property Xzje: Double read FXzje write FXzje;

    property Nksys: Double read FNksys write FNksys;
    property Nkdj: Double read FNkdj write FNkdj;
    property Nkje: Double read FNkje write FNkje;

    property Nksys_a: Double read FNksys_a write FNksys_a;
    property Nkdj_a: Double read FNkdj_a write FNkdj_a;
    property Nkje_a: Double read FNkje_a write FNkje_a;

    property Nksys_b: Double read FNksys_b write FNksys_b;
    property Nkdj_b: Double read FNkdj_b write FNkdj_b;
    property Nkje_b: Double read FNkje_b write FNkje_b;

    property Nksys_c: Double read FNksys_c write FNksys_c;
    property Nkdj_c: Double read FNkdj_c write FNkdj_c;
    property Nkje_c: Double read FNkje_c write FNkje_c;

    property Nkzje: Double read FNkzje write FNkzje;

    property Llsys: Double read FLlsys write FLlsys;
    property Lldj: Double read FLldj write FLldj;
    property Llje: Double read FLlje write FLlje;

    property Llsys_a: Double read FLlsys_a write FLlsys_a;
    property Lldj_a: Double read FLldj_a write FLldj_a;
    property Llje_a: Double read FLlje_a write FLlje_a;

    property Llsys_b: Double read FLlsys_b write FLlsys_b;
    property Lldj_b: Double read FLldj_b write FLldj_b;
    property Llje_b: Double read FLlje_b write FLlje_b;

    property Llsys_c: Double read FLlsys_c write FLlsys_c;
    property Lldj_c: Double read FLldj_c write FLldj_c;
    property Llje_c: Double read FLlje_c write FLlje_c;

    property Llzje: Double read FLlzje write FLlzje;

    property Nhcsys: Double read FNhcsys write FNhcsys;
    property Nhcdj: Double read FNhcdj write FNhcdj;
    property Nhcje: Double read FNhcje write FNhcje;

    property Nhcsys_a: Double read FNhcsys_a write FNhcsys_a;
    property Nhcdj_a: Double read FNhcdj_a write FNhcdj_a;
    property Nhcje_a: Double read FNhcje_a write FNhcje_a;

    property Nhcsys_b: Double read FNhcsys_b write FNhcsys_b;
    property Nhcdj_b: Double read FNhcdj_b write FNhcdj_b;
    property Nhcje_b: Double read FNhcje_b write FNhcje_b;

    property Nhczje: Double read FNhczje write FNhczje;

    property Xjdsys: Double read FXjdsys write FXjdsys;
    property Xjddj: Double read FXjddj write FXjddj;
    property Xjdje: Double read FXjdje write FXjdje;

    property Xjdsys_a: Double read FXjdsys_a write FXjdsys_a;
    property Xjddj_a: Double read FXjddj_a write FXjddj_a;
    property Xjdje_a: Double read FXjdje_a write FXjdje_a;

    property Xjdsys_b: Double read FXjdsys_b write FXjdsys_b;
    property Xjddj_b: Double read FXjddj_b write FXjddj_b;
    property Xjdje_b: Double read FXjdje_b write FXjdje_b;

    property Xjdzje: Double read FXjdzje write FXjdzje;

    property Jdggid: string read FJdggid write FJdggid;
    property Jdsys: Double read FJdsys write FJdsys;
    property Jddj: Double read FJddj write FJddj;
    property Jdje: Double read FJdje write FJdje;
    property Txmsys: Double read FTxmsys write FTxmsys;
    property Txmdj: Double read FTxmdj write FTxmdj;
    property Txmje: Double read FTxmje write FTxmje;
    property Czsys: Double read FCzsys write FCzsys;
    property Czdj: Double read FCzdj write FCzdj;
    property Czje: Double read FCzje write FCzje;
    property Cbsys: Double read FCbsys write FCbsys;
    property Cbdj: Double read FCbdj write FCbdj;
    property Cbje: Double read FCbje write FCbje;
    property Zxsys: Double read FZxsys write FZxsys;
    property Zxdj: Double read FZxdj write FZxdj;
    property Zxje: Double read FZxje write FZxje;

    property Fgdsys: Double read FFgdsys write FFgdsys;
    property Fgddj: Double read FFgddj write FFgddj;
    property Fgdje: Double read FFgdje write FFgdje;
    property Zdsys: Double read FZdsys write FZdsys;
    property Zddj: Double read FZddj write FZddj;
    property Zdje: Double read FZdje write FZdje;
    property Lldsys: Double read FLldsys write FLldsys;
    property Llddj: Double read FLlddj write FLlddj;
    property Lldje: Double read FLldje write FLldje;

    property Pmbmid_a: string read FPmbmid_a write FPmbmid_a;
    property Qtsl_a: Double read FQtsl_a write FQtsl_a;
    property Qtdj_a: Double read FQtdj_a write FQtdj_a;
    property Qtje_a: Double read FQtje_a write FQtje_a;
    property Pmbmid_b: string read FPmbmid_b write FPmbmid_b;
    property Qtsl_b: Double read FQtsl_b write FQtsl_b;
    property Qtdj_b: Double read FQtdj_b write FQtdj_b;
    property Qtje_b: Double read FQtje_b write FQtje_b;
    property Pmbmid_c: string read FPmbmid_c write FPmbmid_c;
    property Qtsl_c: Double read FQtsl_c write FQtsl_c;
    property Qtdj_c: Double read FQtdj_c write FQtdj_c;
    property Qtje_c: Double read FQtje_c write FQtje_c;

    property Sbsh_a: Double read FSbsh_a write FSbsh_a;
    property Sbsh_b: Double read FSbsh_b write FSbsh_b;
    property Sbsh_c: Double read FSbsh_c write FSbsh_c;
    property Sbsh_d: Double read FSbsh_d write FSbsh_d;

    property Dpsh_a: Double read FDpsh_a write FDpsh_a;
    property Dpsh_b: Double read FDpsh_b write FDpsh_b;
    property Dpsh_c: Double read FDpsh_c write FDpsh_c;
    property Dpsh_d: Double read FDpsh_d write FDpsh_d;

    property Xsh_a: Double read FXsh_a write FXsh_a;
    property Xsh_b: Double read FXsh_b write FXsh_b;
    property Xsh_c: Double read FXsh_c write FXsh_c;

    property Nksh_a: Double read FNksh_a write FNksh_a;
    property Nksh_b: Double read FNksh_b write FNksh_b;
    property Nksh_c: Double read FNksh_c write FNksh_c;

    property Llsh_a: Double read FLlsh_a write FLlsh_a;
    property Llsh_b: Double read FLlsh_b write FLlsh_b;
    property Llsh_c: Double read FLlsh_c write FLlsh_c;

    property Xjdsh_a: Double read FXjdsh_a write FXjdsh_a;
    property Xjdsh_b: Double read FXjdsh_b write FXjdsh_b;

    property Nhcsh_a: Double read FNhcsh_a write FNhcsh_a;
    property Nhcsh_b: Double read FNhcsh_b write FNhcsh_b;

    property Qtsh_a: Double read FQtsh_a write FQtsh_a;
    property Qtsh_b: Double read FQtsh_b write FQtsh_b;
    property Qtsh_c: Double read FQtsh_c write FQtsh_c;

    property Txmsh: Double read FTxmsh write FTxmsh;
    property Czsh: Double read FCzsh write FCzsh;
    property Cbsh: Double read FCbsh write FCbsh;
    property Jdsh: Double read FJdsh write FJdsh;
    property Zxsh: Double read FZxsh write FZxsh;

    property Bzqtdj: Double read FBzqtdj write FBzqtdj;
    property Bzqtsh: Double read FBzqtsh write FBzqtsh;
    property Bzqtje: Double read FBzqtje write FBzqtje;

    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;

implementation

end.
