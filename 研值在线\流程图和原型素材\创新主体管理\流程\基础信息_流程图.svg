<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">孵化载体基础信息管理系统流程图</text>

  <!-- 阶段一：数据同步与处理 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与处理</text>
  
  <!-- 节点1: 数据源 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据系统</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">科技部门备案系统</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">孵化载体运营系统</text>
  </g>

  <!-- 节点2: 数据处理 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据处理中心</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">数据清洗、主键映射</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">一致性校验</text>
  </g>

  <!-- 节点3: 主题数据库 -->
  <g transform="translate(700, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">孵化载体主题数据库</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">载体基础数据</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">资质信息、地理坐标</text>
  </g>

  <!-- 连接线 数据源 -> 数据处理 -->
  <path d="M 300 170 Q 350 170 400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="350" y="160" text-anchor="middle" font-size="12" fill="#555">每日凌晨同步</text>

  <!-- 连接线 数据处理 -> 主题数据库 -->
  <path d="M 600 170 Q 650 170 700 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：统计服务与缓存 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：统计服务与缓存</text>

  <!-- 节点4: 统计服务 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统计服务</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">聚合计算、指标生成</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">载体数量、资质分布</text>
  </g>

  <!-- 节点5: 缓存库 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存库</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">高频读取优化</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">消息通知机制</text>
  </g>

  <!-- 连接线 主题数据库 -> 统计服务 -->
  <path d="M 750 210 C 750 250, 650 280, 600 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 统计服务 -> 缓存库 -->
  <path d="M 700 350 Q 750 350 800 350" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点6: 前端组件 -->
  <g transform="translate(200, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">前端组件</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">概览卡片渲染</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">分页载体清单</text>
  </g>

  <!-- 节点7: 权限控制 -->
  <g transform="translate(500, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">权限控制接口</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">用户权限验证</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">编辑标识返回</text>
  </g>

  <!-- 节点8: 用户交互 -->
  <g transform="translate(800, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互功能</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">官网访问、地图导航</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">行为日志记录</text>
  </g>

  <!-- 连接线 缓存库 -> 前端组件 -->
  <path d="M 850 390 C 850 430, 350 460, 300 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 前端组件 -> 权限控制 -->
  <path d="M 400 530 Q 450 530 500 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 权限控制 -> 用户交互 -->
  <path d="M 700 530 Q 750 530 800 530" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据导出与质量监控 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据导出与质量监控</text>

  <!-- 节点9: 导出服务 -->
  <g transform="translate(300, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">导出服务</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">Excel文件生成</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">一次性下载链接</text>
  </g>

  <!-- 节点10: 质量监控 -->
  <g transform="translate(600, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据质量监控</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">字段差异比对</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">治理工单生成</text>
  </g>

  <!-- 节点11: 数据管理平台 -->
  <g transform="translate(900, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据管理平台</text>
    <text x="100" y="50" text-anchor="middle" font-size="12" fill="#555">工单处理</text>
    <text x="100" y="65" text-anchor="middle" font-size="12" fill="#555">修正结果回写</text>
  </g>

  <!-- 连接线 用户交互 -> 导出服务 -->
  <path d="M 850 570 C 850 610, 450 640, 400 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="625" y="620" text-anchor="middle" font-size="12" fill="#555">筛选导出</text>

  <!-- 连接线 主题数据库 -> 质量监控 -->
  <path d="M 800 210 C 1050 210, 1050 640, 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="1070" y="440" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1070, 440)">每日监控</text>

  <!-- 连接线 质量监控 -> 数据管理平台 -->
  <path d="M 800 710 Q 850 710 900 710" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 数据管理平台 -> 主题数据库 (回写) -->
  <path d="M 1000 670 C 1000 600, 1150 300, 1150 170 C 1150 170, 950 170, 900 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1170" y="420" text-anchor="middle" font-size="12" fill="#555" transform="rotate(90, 1170, 420)">修正回写</text>

</svg>