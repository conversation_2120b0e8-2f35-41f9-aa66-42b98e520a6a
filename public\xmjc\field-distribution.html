<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>领域分布 - 项目检测大屏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .field-card {
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        .field-card:hover {
            border-color: #3B82F6;
            background: linear-gradient(135deg, #EBF8FF, #F0F9FF);
        }
        .field-active {
            border-color: #3B82F6;
            background: linear-gradient(135deg, #EBF8FF, #F0F9FF);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.location.href='index.html'" class="text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-industry text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">领域分布</h1>
                        <p class="text-sm text-gray-600">Field Distribution</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-indigo-500 text-white px-4 py-2 rounded-lg hover:bg-indigo-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出分析
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 分类选择器 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">领域分类</h3>
                <div class="flex space-x-4">
                    <button id="btn-industry" class="bg-indigo-500 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        361产业领域
                    </button>
                    <button id="btn-technology" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                        510技术领域
                    </button>
                </div>
            </div>
            
            <!-- 图表切换 -->
            <div class="flex justify-center space-x-4">
                <button id="chart-bar" class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                    <i class="fas fa-chart-column mr-2"></i>柱状图
                </button>
                <button id="chart-pie" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors">
                    <i class="fas fa-chart-pie mr-2"></i>饼图
                </button>
                <button id="chart-stack" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors">
                    <i class="fas fa-chart-bar mr-2"></i>堆叠图
                </button>
            </div>
        </div>

        <!-- 主要图表展示区 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- 左侧图表 -->
            <div class="lg:col-span-2 bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900" id="chart-title">361产业领域分布</h3>
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i class="fas fa-info-circle"></i>
                        <span>点击查看详细数据</span>
                    </div>
                </div>
                
                <!-- 图表区域 -->
                <div class="h-96 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg chart-placeholder flex items-center justify-center">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-chart-column text-4xl mb-4"></i>
                        <p class="text-lg">项目数量与资金分布图表</p>
                        <p class="text-sm mt-2">按产业领域统计项目数量与投资金额</p>
                        <div class="grid grid-cols-2 gap-4 mt-4 text-left">
                            <div class="bg-white p-3 rounded-lg">
                                <p class="text-xs text-gray-600">项目总数</p>
                                <p class="text-lg font-bold text-indigo-600">2,847项</p>
                            </div>
                            <div class="bg-white p-3 rounded-lg">
                                <p class="text-xs text-gray-600">投资总额</p>
                                <p class="text-lg font-bold text-green-600">156.8亿</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计信息 -->
            <div class="space-y-6">
                <!-- TOP5领域 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                    <h4 class="font-semibold text-gray-900 mb-4">TOP5领域</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center p-2 bg-blue-50 rounded">
                            <span class="text-sm font-medium">新一代信息技术</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-blue-600">342项</span>
                                <span class="text-xs text-gray-500 block">34.5亿元</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-green-50 rounded">
                            <span class="text-sm font-medium">高端装备制造</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-green-600">268项</span>
                                <span class="text-xs text-gray-500 block">28.9亿元</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-purple-50 rounded">
                            <span class="text-sm font-medium">新材料</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-purple-600">234项</span>
                                <span class="text-xs text-gray-500 block">22.3亿元</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-yellow-50 rounded">
                            <span class="text-sm font-medium">生物医药</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-yellow-600">198项</span>
                                <span class="text-xs text-gray-500 block">19.8亿元</span>
                            </div>
                        </div>
                        <div class="flex justify-between items-center p-2 bg-cyan-50 rounded">
                            <span class="text-sm font-medium">新能源</span>
                            <div class="text-right">
                                <span class="text-sm font-bold text-cyan-600">156项</span>
                                <span class="text-xs text-gray-500 block">15.6亿元</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 增长趋势 -->
                <div class="bg-white rounded-xl border border-gray-200 p-6 hover-lift">
                    <h4 class="font-semibold text-gray-900 mb-4">年度增长趋势</h4>
                    <div class="h-32 bg-gradient-to-r from-green-50 to-green-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-line text-2xl mb-2"></i>
                            <p class="text-sm">领域增长趋势图</p>
                        </div>
                    </div>
                    <div class="mt-4 grid grid-cols-2 gap-2 text-xs">
                        <div class="text-center p-2 bg-green-50 rounded">
                            <p class="text-green-600 font-medium">+18.5%</p>
                            <p class="text-gray-600">项目数量</p>
                        </div>
                        <div class="text-center p-2 bg-blue-50 rounded">
                            <p class="text-blue-600 font-medium">+22.8%</p>
                            <p class="text-gray-600">投资金额</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 详细领域列表 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">详细领域分布</h3>
                <div class="flex space-x-2">
                    <button class="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-filter mr-1"></i>筛选
                    </button>
                    <button class="text-sm bg-blue-500 text-white px-3 py-1 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-sort mr-1"></i>排序
                    </button>
                </div>
            </div>

            <div id="industry-fields" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- 361产业领域卡片 -->
                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="新一代信息技术">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">新一代信息技术</h4>
                            <p class="text-xs text-gray-600">Information Technology</p>
                        </div>
                        <i class="fas fa-microchip text-blue-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">342项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">34.5亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">12.0%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+15.8%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="高端装备制造">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">高端装备制造</h4>
                            <p class="text-xs text-gray-600">Advanced Manufacturing</p>
                        </div>
                        <i class="fas fa-cogs text-green-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">268项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">28.9亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">9.4%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+12.3%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="新材料">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">新材料</h4>
                            <p class="text-xs text-gray-600">New Materials</p>
                        </div>
                        <i class="fas fa-atom text-purple-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">234项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">22.3亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">8.2%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+18.9%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="生物医药">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">生物医药</h4>
                            <p class="text-xs text-gray-600">Biomedical</p>
                        </div>
                        <i class="fas fa-dna text-red-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">198项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">19.8亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">7.0%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+14.2%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="新能源">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">新能源</h4>
                            <p class="text-xs text-gray-600">New Energy</p>
                        </div>
                        <i class="fas fa-solar-panel text-yellow-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">156项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">15.6亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">5.5%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+21.4%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="海洋经济">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">海洋经济</h4>
                            <p class="text-xs text-gray-600">Marine Economy</p>
                        </div>
                        <i class="fas fa-ship text-cyan-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">142项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">13.8亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">5.0%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+16.7%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div id="technology-fields" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 hidden">
                <!-- 510技术领域卡片 -->
                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="人工智能">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">人工智能</h4>
                            <p class="text-xs text-gray-600">Artificial Intelligence</p>
                        </div>
                        <i class="fas fa-brain text-blue-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">189项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">25.8亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">6.6%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+28.5%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="量子技术">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">量子技术</h4>
                            <p class="text-xs text-gray-600">Quantum Technology</p>
                        </div>
                        <i class="fas fa-atom text-purple-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">67项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">12.4亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">2.4%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+45.2%</span>
                        </div>
                    </div>
                </div>

                <div class="field-card bg-white p-4 rounded-lg border border-gray-200" data-field="生物技术">
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-900">生物技术</h4>
                            <p class="text-xs text-gray-600">Biotechnology</p>
                        </div>
                        <i class="fas fa-dna text-green-500 text-xl"></i>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div>
                            <span class="text-gray-500">项目数：</span>
                            <span class="font-medium">156项</span>
                        </div>
                        <div>
                            <span class="text-gray-500">投资额：</span>
                            <span class="font-medium">18.9亿</span>
                        </div>
                        <div>
                            <span class="text-gray-500">占比：</span>
                            <span class="font-medium">5.5%</span>
                        </div>
                        <div>
                            <span class="text-gray-500">增速：</span>
                            <span class="font-medium text-green-600">+19.8%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 返回按钮 -->
        <div class="text-center">
            <button onclick="window.location.href='index.html'" 
                    class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>
                返回首页
            </button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 领域切换
            const btnIndustry = document.getElementById('btn-industry');
            const btnTechnology = document.getElementById('btn-technology');
            const industryFields = document.getElementById('industry-fields');
            const technologyFields = document.getElementById('technology-fields');
            const chartTitle = document.getElementById('chart-title');

            function switchField(isIndustry) {
                if (isIndustry) {
                    btnIndustry.className = 'bg-indigo-500 text-white px-4 py-2 rounded-lg font-medium transition-colors';
                    btnTechnology.className = 'bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors';
                    industryFields.classList.remove('hidden');
                    technologyFields.classList.add('hidden');
                    chartTitle.textContent = '361产业领域分布';
                } else {
                    btnTechnology.className = 'bg-indigo-500 text-white px-4 py-2 rounded-lg font-medium transition-colors';
                    btnIndustry.className = 'bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors';
                    technologyFields.classList.remove('hidden');
                    industryFields.classList.add('hidden');
                    chartTitle.textContent = '510技术领域分布';
                }
            }

            btnIndustry.addEventListener('click', () => switchField(true));
            btnTechnology.addEventListener('click', () => switchField(false));

            // 图表切换
            const chartButtons = document.querySelectorAll('#chart-bar, #chart-pie, #chart-stack');
            chartButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    chartButtons.forEach(b => {
                        b.className = 'bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors';
                    });
                    this.className = 'bg-blue-500 text-white px-4 py-2 rounded-lg text-sm transition-colors';
                });
            });

            // 领域卡片点击
            document.addEventListener('click', function(e) {
                const card = e.target.closest('.field-card');
                if (card) {
                    // 移除其他卡片的激活状态
                    document.querySelectorAll('.field-card').forEach(c => {
                        c.classList.remove('field-active');
                    });
                    // 激活当前卡片
                    card.classList.add('field-active');
                    
                    const fieldName = card.dataset.field;
                    // 这里可以添加钻取逻辑
                    console.log('查看领域详情:', fieldName);
                }
            });

            // 悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 