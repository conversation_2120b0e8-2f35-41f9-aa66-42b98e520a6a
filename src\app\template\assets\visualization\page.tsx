'use client'

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Save, 
  History,
  Share2,
  Eye,
  Settings,
  LayoutGrid,
  Palette,
  Database
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { HistoryDialog } from "@/components/assets/history-dialog"
import { ShareDialog } from "@/components/assets/share-dialog"
import { VisualizationLayout } from "@/components/assets/visualization-layout"
import { VisualizationConfig } from "@/components/assets/visualization-config"
import { LayoutConfig } from "@/components/assets/layout-config"
import { StyleConfig } from "@/components/assets/style-config"
import { VisualizationDataConfig } from "@/components/assets/visualization-data-config"

export default function VisualizationPage() {
  const router = useRouter()
  const [historyOpen, setHistoryOpen] = useState(false)
  const [shareOpen, setShareOpen] = useState(false)

  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      {/* 头部区域 */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-medium text-gray-900">可视化配置</h1>
            <p className="text-sm text-gray-500 mt-1">自定义大屏展示配置和布局管理</p>
          </div>
          <div className="flex items-center gap-4">
            <Select defaultValue="default">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="选择模板" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">默认财政大屏</SelectItem>
                <SelectItem value="budget">预算分析大屏</SelectItem>
                <SelectItem value="payment">支付监控大屏</SelectItem>
                <SelectItem value="custom1">自定义模板1</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => setHistoryOpen(true)}>
              <History className="mr-2 h-4 w-4" />
              历史版本
            </Button>
            <Button variant="outline" onClick={() => setShareOpen(true)}>
              <Share2 className="mr-2 h-4 w-4" />
              分享
            </Button>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              <Save className="mr-2 h-4 w-4" />
              保存配置
            </Button>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-12 gap-6">
          {/* 左侧配置面板 */}
          <div className="col-span-3 space-y-6">
            <Card className="border-[#E5E9EF]">
              <CardHeader className="border-b border-[#E5E9EF]">
                <CardTitle>组件配置</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs defaultValue="components" className="w-full">
                  <TabsList className="w-full grid grid-cols-4">
                    <TabsTrigger value="components">
                      <LayoutGrid className="h-4 w-4 mr-2" />
                      组件
                    </TabsTrigger>
                    <TabsTrigger value="layout">
                      <Settings className="h-4 w-4 mr-2" />
                      布局
                    </TabsTrigger>
                    <TabsTrigger value="style">
                      <Palette className="h-4 w-4 mr-2" />
                      样式
                    </TabsTrigger>
                    <TabsTrigger value="data">
                      <Database className="h-4 w-4 mr-2" />
                      数据
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="components">
                    <VisualizationConfig />
                  </TabsContent>
                  <TabsContent value="layout">
                    <LayoutConfig />
                  </TabsContent>
                  <TabsContent value="style">
                    <StyleConfig />
                  </TabsContent>
                  <TabsContent value="data">
                    <VisualizationDataConfig />
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* 预览区域 */}
          <div className="col-span-9">
            <Card className="border-[#E5E9EF]">
              <CardHeader className="flex flex-row items-center justify-between border-b border-[#E5E9EF] py-3">
                <CardTitle>布局配置</CardTitle>
                <div className="flex items-center gap-2">
                  <Select defaultValue="1920">
                    <SelectTrigger className="w-[120px]">
                      <SelectValue placeholder="屏幕尺寸" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1920">1920×1080</SelectItem>
                      <SelectItem value="2560">2560×1440</SelectItem>
                      <SelectItem value="3840">3840×2160</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => router.push('/template/assets/visualization/preview')}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-4">
                <VisualizationLayout 
                  isPreview={false}
                  onEdit={(id) => {
                    console.log('编辑区域:', id)
                    // TODO: 处理编辑逻辑
                  }}
                  onDelete={(id) => {
                    console.log('删除区域:', id)
                    // TODO: 处理删除逻辑
                  }}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <HistoryDialog open={historyOpen} onOpenChange={setHistoryOpen} />
      <ShareDialog open={shareOpen} onOpenChange={setShareOpen} />
    </div>
  )
} 