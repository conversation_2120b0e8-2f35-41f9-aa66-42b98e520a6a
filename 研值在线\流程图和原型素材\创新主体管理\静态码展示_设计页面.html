<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>静态码展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.1/build/qrcode.min.js"></script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-2xl font-bold text-gray-900">静态码展示</h1>
            <p class="text-gray-600 mt-2">为创新主体生成科研码及二维码，便于信息展示与分享</p>
        </div>

        <!-- 生成配置区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
                科研码生成配置
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">主体类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择主体类型</option>
                        <option value="enterprise">企业</option>
                        <option value="university">高校</option>
                        <option value="institute">科研院所</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码</label>
                    <input type="text" placeholder="请输入信用代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">行业/领域代码</label>
                    <input type="text" placeholder="请输入行业代码" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">科研活动类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">请选择活动类型</option>
                        <option value="basic">基础研究</option>
                        <option value="applied">应用研究</option>
                        <option value="development">试验发展</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">随机标识符</label>
                    <div class="flex items-center">
                        <input type="text" value="A7B9C2" readonly class="w-full px-3 py-2 border border-gray-300 rounded-l-md bg-gray-100">
                        <button class="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end">
                <button id="generateBtn" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    生成科研码
                </button>
            </div>
            
            <div id="generatingStatus" class="hidden mt-4 p-3 bg-blue-50 text-blue-800 rounded-md flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                正在生成科研码，请稍候...
            </div>
        </div>

        <!-- 静态码预览区 -->
        <div id="codePreview" class="hidden bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                科研码预览
            </h2>
            
            <div class="flex flex-col md:flex-row items-center md:items-start">
                <div class="flex flex-col items-center mr-0 md:mr-8 mb-6 md:mb-0">
                    <div id="qrcode" class="w-48 h-48 mb-4"></div>
                    <div class="bg-gray-100 px-4 py-2 rounded-md font-mono text-sm">RES-ENT-91310101MA1FPX1234-TECH-A7B9C2</div>
                </div>
                
                <div class="flex-1">
                    <div class="mb-6">
                        <h3 class="text-md font-medium text-gray-900 mb-2">主体信息摘要</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-gray-500">主体名称</p>
                                <p class="text-sm font-medium">宁波市科技创新有限公司</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">主体类型</p>
                                <p class="text-sm font-medium">高新技术企业</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">行业领域</p>
                                <p class="text-sm font-medium">新一代信息技术</p>
                            </div>
                            <div>
                                <p class="text-sm text-gray-500">科研活动</p>
                                <p class="text-sm font-medium">应用研究</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex flex-wrap gap-3">
                        <div class="flex items-center">
                            <label class="mr-2 text-sm text-gray-700">尺寸:</label>
                            <select class="px-3 py-1 border border-gray-300 rounded-md text-sm">
                                <option>200×200</option>
                                <option>300×300</option>
                                <option>400×400</option>
                            </select>
                        </div>
                        <button id="downloadBtn" class="px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            下载PNG
                        </button>
                        <button id="copyLinkBtn" class="px-4 py-1 bg-gray-100 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-200 text-sm flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
                            </svg>
                            复制链接
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 信息摘要区 -->
        <div id="infoSummary" class="hidden bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-start mb-4">
                <h2 class="text-lg font-semibold text-gray-900">主体公开信息摘要</h2>
                <span class="text-sm text-gray-500">更新时间: 2024-01-15 14:30</span>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="border-l-4 border-blue-500 pl-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">工商注册信息</h3>
                    <p class="text-sm">统一社会信用代码: 91310101MA1FPX1234</p>
                    <p class="text-sm">注册资本: 1000万元</p>
                    <p class="text-sm">成立日期: 2018-05-10</p>
                </div>
                
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">科技成果</h3>
                    <p class="text-sm">专利数量: 23项</p>
                    <p class="text-sm">软件著作权: 5项</p>
                    <p class="text-sm">论文发表: 12篇</p>
                </div>
                
                <div class="border-l-4 border-yellow-500 pl-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">仪器设备</h3>
                    <p class="text-sm">设备总数: 15台</p>
                    <p class="text-sm">原值总额: 580万元</p>
                    <p class="text-sm">共享设备: 8台</p>
                </div>
                
                <div class="border-l-4 border-purple-500 pl-4">
                    <h3 class="text-sm font-medium text-gray-500 mb-2">获奖荣誉</h3>
                    <p class="text-sm">高新技术企业认证</p>
                    <p class="text-sm">宁波市科技进步奖</p>
                    <p class="text-sm">专精特新中小企业</p>
                </div>
            </div>
            
            <div class="mt-6 text-right">
                <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center justify-end">
                    查看完整档案
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- 合规与防伪提示区 -->
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-md">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">合规与防伪提示</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <p>• 本二维码采用AES-256加密和数字签名技术，确保数据真实可靠</p>
                        <p>• 请勿将二维码用于非法用途或虚假宣传</p>
                        <p>• 二维码内容将随主体信息变更自动更新</p>
                    </div>
                    <div class="mt-4">
                        <button onclick="openVerificationModal()" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200">
                            验证真伪
                        </button>
                        <a href="#" class="ml-3 text-sm font-medium text-yellow-700 hover:text-yellow-600">
                            联系客服
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 验证真伪弹窗 -->
    <div id="verificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">验证二维码真伪</h3>
                    <button onclick="closeVerificationModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6">
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="mt-2 text-lg font-medium text-gray-900">验证科研码真伪</h3>
                        <div class="mt-4">
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>上传二维码图片</span>
                                            <input type="file" class="sr-only">
                                        </label>
                                        <p class="pl-1">或拖拽到此处</p>
                                    </div>
                                    <p class="text-xs text-gray-500">支持PNG、JPG格式</p>
                                </div>
                            </div>
                        </div>
                        <div class="mt-4 relative">
                            <div class="absolute inset-0 flex items-center">
                                <div class="w-full border-t border-gray-300"></div>
                            </div>
                            <div class="relative flex justify-center text-sm">
                                <span class="px-2 bg-white text-gray-500">或</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label for="codeInput" class="block text-sm font-medium text-gray-700">输入科研码</label>
                            <input type="text" id="codeInput" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="RES-ENT-91310101MA1FPX1234-TECH-A7B9C2">
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t bg-gray-50 flex justify-end">
                    <button onclick="closeVerificationModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-2">
                        取消
                    </button>
                    <button onclick="verifyCode()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        验证
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 验证结果弹窗 -->
    <div id="verificationResult" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">验证结果</h3>
                    <button onclick="closeVerificationResult()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h3 class="mt-2 text-lg font-medium text-gray-900">验证通过</h3>
                    <div class="mt-4 text-left">
                        <p class="text-sm text-gray-500">科研码: <span class="font-medium text-gray-900">RES-ENT-91310101MA1FPX1234-TECH-A7B9C2</span></p>
                        <p class="text-sm text-gray-500 mt-2">所属主体: <span class="font-medium text-gray-900">宁波市科技创新有限公司</span></p>
                        <p class="text-sm text-gray-500 mt-2">生成时间: <span class="font-medium text-gray-900">2024-01-15 14:30:21</span></p>
                        <p class="text-sm text-gray-500 mt-2">有效期至: <span class="font-medium text-gray-900">2025-01-15 14:30:21</span></p>
                    </div>
                </div>
                <div class="px-6 py-4 border-t bg-gray-50 flex justify-end">
                    <button onclick="closeVerificationResult()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        确定
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openVerificationModal() {
            document.getElementById('verificationModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeVerificationModal() {
            document.getElementById('verificationModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function closeVerificationResult() {
            document.getElementById('verificationResult').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        function verifyCode() {
            closeVerificationModal();
            document.getElementById('verificationResult').classList.remove('hidden');
        }

        // 生成科研码
        document.getElementById('generateBtn').addEventListener('click', function() {
            const generatingStatus = document.getElementById('generatingStatus');
            const codePreview = document.getElementById('codePreview');
            const infoSummary = document.getElementById('infoSummary');
            
            generatingStatus.classList.remove('hidden');
            
            // 模拟生成过程
            setTimeout(function() {
                generatingStatus.classList.add('hidden');
                codePreview.classList.remove('hidden');
                infoSummary.classList.remove('hidden');
                
                // 生成二维码
                QRCode.toCanvas(document.getElementById('qrcode'), 'RES-ENT-91310101MA1FPX1234-TECH-A7B9C2', {
                    width: 200,
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#ffffff'
                    }
                }, function(error) {
                    if (error) console.error(error);
                });
            }, 2000);
        });

        // 下载PNG
        document.getElementById('downloadBtn').addEventListener('click', function() {
            const canvas = document.querySelector('#qrcode canvas');
            const link = document.createElement('a');
            link.download = '科研码-宁波市科技创新有限公司.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });

        // 复制链接
        document.getElementById('copyLinkBtn').addEventListener('click', function() {
            const input = document.createElement('input');
            input.value = 'https://example.com/code/RES-ENT-91310101MA1FPX1234-TECH-A7B9C2';
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
            
            alert('链接已复制到剪贴板');
        });

        // 点击弹窗外部关闭
        document.getElementById('verificationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVerificationModal();
            }
        });

        document.getElementById('verificationResult').addEventListener('click', function(e) {
            if (e.target === this) {
                closeVerificationResult();
            }
        });

        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                if (!document.getElementById('verificationModal').classList.contains('hidden')) {
                    closeVerificationModal();
                }
                if (!document.getElementById('verificationResult').classList.contains('hidden')) {
                    closeVerificationResult();
                }
            }
        });
    </script>
</body>
</html>