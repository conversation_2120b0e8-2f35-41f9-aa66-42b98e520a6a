<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">企业信息展示数据处理与可视化流程</text>

  <!-- 阶段一：数据接收与聚合 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据接收与聚合</text>
  
  <!-- 节点1: 企业科研码接收 -->
  <g transform="translate(100, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">企业科研码接收</text>
    <text x="110" y="50" text-anchor="middle" font-size="12" fill="#555">用户选择企业科研码</text>
    <text x="110" y="65" text-anchor="middle" font-size="12" fill="#555">系统接收请求</text>
  </g>

  <!-- 节点2: 多源数据聚合 -->
  <g transform="translate(400, 130)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据聚合</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">工商、股东、投资、社保、股权链</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">字段标准化与画像计算</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 320 170 Q 360 170 400 170" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：股权结构处理与可视化 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：股权结构处理与可视化</text>

  <!-- 节点3: 股权结构生成 -->
  <g transform="translate(250, 310)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">股权结构生成</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">根据统一社会信用代码匹配</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">股东及对外投资信息</text>
  </g>

  <!-- 节点4: 图形渲染服务 -->
  <g transform="translate(600, 310)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">图形渲染服务</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">同步图形渲染服务</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">输出可视化组件</text>
  </g>

  <!-- 连接线 2 -> 3,4 -->
  <path d="M 540 210 C 480 250, 420 280, 390 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 540 210 C 600 250, 660 280, 740 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 3 -> 4 -->
  <path d="M 530 350 Q 565 350 600 350" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与用户交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与用户交互</text>

  <!-- 节点5: 模块化展示 -->
  <g transform="translate(150, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">模块化展示</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">企业概况、股东信息、投资</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">参保、股权穿透、行业产业</text>
  </g>

  <!-- 节点6: 用户下钻交互 -->
  <g transform="translate(500, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户下钻交互</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">点击股东条目或投资卡片</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">加载详情页数据</text>
  </g>

  <!-- 节点7: 动态可视化更新 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">动态可视化更新</text>
    <text x="140" y="50" text-anchor="middle" font-size="12" fill="#555">股权穿透图节点展开</text>
    <text x="140" y="65" text-anchor="middle" font-size="12" fill="#555">异步获取股权链数据</text>
  </g>

  <!-- 连接线 4 -> 5,6,7 -->
  <path d="M 650 390 C 550 430, 450 460, 290 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 740 390 Q 740 440 640 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 830 390 C 900 430, 970 460, 990 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：日志记录与优化分析 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：日志记录与优化分析</text>
  
  <!-- 节点8: 操作日志与统计分析 -->
  <g transform="translate(450, 670)" filter="url(#soft-shadow)">
      <rect width="500" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
      <text x="250" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">操作日志与统计分析</text>
      <text x="250" y="55" text-anchor="middle" font-size="13" fill="#555">
        <tspan dx="-150">用户浏览记录</tspan>
        <tspan dx="40">下钻操作日志</tspan>
        <tspan dx="40">导出行为统计</tspan>
        <tspan dx="40">画像优化分析</tspan>
      </text>
  </g>

  <!-- 连接线 5,6,7 -> 8 -->
  <path d="M 290 570 C 350 600, 450 640, 500 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 640 570 Q 640 620 700 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 990 570 C 950 600, 850 640, 900 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 -->
  <path d="M 450 710 C 200 750, 100 600, 150 390" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="250" y="780" text-anchor="middle" font-size="12" fill="#666">数据优化反馈</text>

  <!-- 缓存同步线 -->
  <path d="M 990 530 C 1100 550, 1100 400, 880 350" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="3,3" marker-end="url(#arrowhead)" />
  <text x="1050" y="450" text-anchor="middle" font-size="12" fill="#666">缓存同步</text>

</svg>