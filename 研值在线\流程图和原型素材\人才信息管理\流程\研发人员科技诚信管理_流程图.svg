<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">研发人员科技诚信管理流程图</text>

  <!-- 阶段一：界面初始化与数据加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：界面初始化与数据加载</text>
  
  <!-- 节点1: 用户进入界面 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入界面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(科技诚信管理)</text>
  </g>

  <!-- 节点2: 默认加载诚信清单 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">默认加载清单</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(全部诚信事项)</text>
  </g>

  <!-- 连接线 1 -> 2 -->
  <path d="M 500 165 Q 550 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：查询筛选与列表展示 -->
  <text x="700" y="260" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：查询筛选与列表展示</text>

  <!-- 节点3: 设置筛选条件 -->
  <g transform="translate(200, 290)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">设置筛选条件</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(查询区参数)</text>
  </g>

  <!-- 节点4: 实时刷新列表 -->
  <g transform="translate(500, 290)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时刷新列表</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(诚信行为列表)</text>
  </g>

  <!-- 节点5: 数据导出 -->
  <g transform="translate(800, 290)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(报表归档/评审)</text>
  </g>

  <!-- 连接线 加载 -> 筛选 -->
  <path d="M 650 200 C 600 230, 400 260, 300 290" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 3 -> 4 -> 5 -->
  <path d="M 400 325 Q 450 325 500 325" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 325 Q 750 325 800 325" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：诚信事项操作管理 -->
  <text x="700" y="420" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：诚信事项操作管理</text>

  <!-- 节点6: 新增诚信行为 -->
  <g transform="translate(100, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新增诚信行为</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(填写详情+校验)</text>
  </g>

  <!-- 节点7: 编辑删除操作 -->
  <g transform="translate(350, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑删除操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(权限校验+状态)</text>
  </g>

  <!-- 节点8: 诚信修复流程 -->
  <g transform="translate(600, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">诚信修复流程</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(修复说明+审核)</text>
  </g>

  <!-- 节点9: 自动关联项目 -->
  <g transform="translate(850, 450)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自动关联项目</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">(写入诚信管理库)</text>
  </g>

  <!-- 连接线 列表 -> 各操作 -->
  <path d="M 550 360 C 500 390, 250 420, 190 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 580 360 C 550 390, 480 420, 440 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 360 C 620 390, 660 420, 690 450" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 新增 -> 关联项目 -->
  <path d="M 280 485 C 500 520, 700 520, 850 485" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：审核流程与状态管理 -->
  <text x="700" y="580" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审核流程与状态管理</text>

  <!-- 节点10: 修复审核 -->
  <g transform="translate(300, 610)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">修复审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(审核通过+状态更新)</text>
  </g>

  <!-- 节点11: 历史记录归档 -->
  <g transform="translate(600, 610)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">历史记录归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">(全程留痕+透明)</text>
  </g>

  <!-- 连接线 修复流程 -> 修复审核 -->
  <path d="M 650 520 C 600 550, 450 580, 400 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 各操作 -> 历史记录 -->
  <path d="M 190 520 C 190 560, 600 580, 650 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 440 520 C 440 560, 650 580, 680 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 940 520 C 940 560, 750 580, 720 610" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段五：系统监控与质量保障 -->
  <text x="700" y="750" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段五：系统监控与质量保障</text>
  
  <!-- 节点12: 责任追溯与过程透明 -->
  <g transform="translate(500, 780)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">责任追溯与过程透明保障</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">完整历史记录</tspan>
      <tspan dx="40">责任追溯</tspan>
      <tspan dx="40">过程透明</tspan>
    </text>
  </g>

  <!-- 连接线 历史记录 -> 责任追溯 -->
  <path d="M 700 680 Q 700 730 700 780" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：责任追溯 -> 筛选条件 -->
  <path d="M 500 800 C 200 850, 100 800, 50 650 C 50 400, 100 350, 200 325" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="80" y="580" text-anchor="middle" font-size="11" fill="#666" transform="rotate(-90, 80, 580)">质量反馈优化</text>

  <!-- 反馈循环：数据导出 -> 默认加载 -->
  <path d="M 900 290 C 1100 200, 1150 150, 1100 100 C 1050 50, 800 80, 700 130" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1050" y="150" text-anchor="middle" font-size="11" fill="#666">导出需求反馈</text>

</svg>