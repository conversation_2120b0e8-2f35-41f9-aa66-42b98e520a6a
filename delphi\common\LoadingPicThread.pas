unit LoadingPicThread;

interface

uses
  Classes, SysUtils, StdCtrls, Vcl.Forms, Cc_LoadingPicFrm;

type
  TThreadPicModel = class(TThread)
  private
    FLoadingPicForm: TLoadingPicForm;
    { Private declarations }
  protected
    procedure Execute; override;
    procedure DoTerminate; override;
    procedure CallVclMethod;
  public
    property LoadingPicForm: TLoadingPicForm read FLoadingPicForm
      write FLoadingPicForm;

  end;

var
  LoadingPicForm: TLoadingPicForm;

implementation

{ Important: Methods and properties of objects in visual components can only be
  used in a method called using Synchronize, for example,

  Synchronize(UpdateCaption);

  and UpdateCaption could look like,

  procedure threadModel.UpdateCaption;
  begin
  Form1.Caption := 'Updated in a thread';
  end; }

{ threadModel }

procedure TThreadPicModel.Execute;
begin
  // application.ProcessMessages;
  // while not Terminated do
  // begin
  // // Sleep(500);
  // Synchronize(CallVclMethod);
  // application.ProcessMessages;
  // end;
  { Place thread code here }
end;

procedure TThreadPicModel.CallVclMethod;
begin
  // LoadingPicForm.Show;
  // LoadingPicForm.Update;
  // LoadingPicForm.RxGIFAnimator1.Animate := true;
  // LoadingPicForm.Update;
end;

procedure TThreadPicModel.DoTerminate;
begin
  LoadingPicForm.Close;
end;

end.
