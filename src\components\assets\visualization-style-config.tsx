'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { Button } from "@/components/ui/button"
import { ColorPicker } from "@/components/ui/color-picker"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export function VisualizationStyleConfig() {
  const [theme, setTheme] = useState('dark')
  const [fontSize, setFontSize] = useState(14)
  const [primaryColor, setPrimaryColor] = useState('#1677ff')
  const [animation, setAnimation] = useState(true)

  return (
    <div className="p-4">
      <Tabs defaultValue="theme">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="theme">主题</TabsTrigger>
          <TabsTrigger value="typography">字体</TabsTrigger>
          <TabsTrigger value="colors">配色</TabsTrigger>
          <TabsTrigger value="effects">效果</TabsTrigger>
        </TabsList>

        <TabsContent value="theme" className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label>主题模式</Label>
              <Select value={theme} onValueChange={setTheme}>
                <SelectTrigger>
                  <SelectValue placeholder="选择主题" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">浅色主题</SelectItem>
                  <SelectItem value="dark">深色主题</SelectItem>
                  <SelectItem value="system">跟随系统</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>布局方式</Label>
              <Select defaultValue="fluid">
                <SelectTrigger>
                  <SelectValue placeholder="选择布局" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fluid">流式布局</SelectItem>
                  <SelectItem value="fixed">固定布局</SelectItem>
                  <SelectItem value="grid">网格布局</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="typography" className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label>字体大小</Label>
              <Slider
                value={[fontSize]}
                onValueChange={([value]) => setFontSize(value)}
                min={12}
                max={20}
                step={1}
              />
              <div className="mt-1 text-sm text-gray-500">{fontSize}px</div>
            </div>

            <div>
              <Label>字体系列</Label>
              <Select defaultValue="system">
                <SelectTrigger>
                  <SelectValue placeholder="选择字体" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="system">系统默认</SelectItem>
                  <SelectItem value="serif">衬线字体</SelectItem>
                  <SelectItem value="mono">等宽字体</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="colors" className="space-y-4">
          <div className="space-y-4">
            <div>
              <Label>主题色</Label>
              <ColorPicker
                color={primaryColor}
                onChange={setPrimaryColor}
              />
            </div>

            <div>
              <Label>背景色</Label>
              <ColorPicker
                color="#ffffff"
                onChange={() => {}}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="effects" className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>启用动画</Label>
              <Switch
                checked={animation}
                onCheckedChange={setAnimation}
              />
            </div>

            <div>
              <Label>动画速度</Label>
              <Select defaultValue="normal">
                <SelectTrigger>
                  <SelectValue placeholder="选择速度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="slow">慢速</SelectItem>
                  <SelectItem value="normal">正常</SelectItem>
                  <SelectItem value="fast">快速</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
} 