<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科研管理人员详情展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科研管理人员详情展示</h1>
            <p class="text-gray-600">全方位展示科研管理人员的基础信息、科技成果、奖励荣誉及社会活动等核心信息</p>
        </div>

        <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">
            <!-- 左侧主要内容区 -->
            <div class="xl:col-span-3 space-y-6">
                <!-- 基础信息卡片 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <div class="flex items-start">
                        <!-- 头像占位 -->
                        <div class="mr-6">
                            <svg class="w-24 h-24 bg-gray-200 text-gray-500 rounded-full" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" font-size="24">头像</text>
                            </svg>
                        </div>
                        <!-- 基本信息 -->
                        <div class="flex-1">
                            <div class="flex items-center mb-4">
                                <h2 class="text-2xl font-bold text-gray-900 mr-4">张伟</h2>
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">高级工程师</span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <p class="text-sm text-gray-500">性别/年龄</p>
                                    <p class="text-sm font-medium text-gray-900">男 / 42岁</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">出生日期</p>
                                    <p class="text-sm font-medium text-gray-900">1981-05-15</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">学历/学位</p>
                                    <p class="text-sm font-medium text-gray-900">博士 / 工学博士</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">毕业院校</p>
                                    <p class="text-sm font-medium text-gray-900">浙江大学</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">工作单位</p>
                                    <p class="text-sm font-medium text-gray-900">宁波市智能制造研究院</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-500">技术领域</p>
                                    <p class="text-sm font-medium text-gray-900">智能制造、工业互联网</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 科技成果区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            科技成果
                        </h2>
                    </div>
                    <div class="p-6">
                        <!-- 专利列表 -->
                        <div class="mb-8">
                            <h3 class="text-md font-medium text-gray-800 mb-4 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                专利清单
                            </h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利名称</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">专利号</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">一种智能制造生产线控制系统</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202010123456.7</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-05-20</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                            </td>
                                        </tr>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">工业互联网数据采集装置</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL201920123456.7</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型</td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-08-15</td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- 论文列表 -->
                        <div>
                            <h3 class="text-md font-medium text-gray-800 mb-4 flex items-center">
                                <svg class="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                论文发表
                            </h3>
                            <div class="space-y-4">
                                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                    <div class="text-sm font-medium text-gray-900">基于工业互联网的智能制造系统研究</div>
                                    <div class="text-sm text-gray-500 mt-1">《机械工程学报》, 2022, 58(3): 1-10</div>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span class="mr-4">SCI收录</span>
                                        <span>引用次数: 28</span>
                                    </div>
                                </div>
                                <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                    <div class="text-sm font-medium text-gray-900">数字孪生技术在智能制造中的应用</div>
                                    <div class="text-sm text-gray-500 mt-1">《计算机集成制造系统》, 2021, 27(5): 1234-1245</div>
                                    <div class="flex items-center mt-2 text-xs text-gray-500">
                                        <span class="mr-4">EI收录</span>
                                        <span>引用次数: 15</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 奖励荣誉区 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v13m8-8v8m-16-8v8M7 17l5-5 5 5"></path>
                            </svg>
                            奖励荣誉
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="h-10 w-10 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-lg font-medium text-gray-900">浙江省科学技术进步奖</div>
                                        <div class="text-sm text-gray-500 mt-1">一等奖 | 浙江省人民政府 | 2022年</div>
                                        <p class="mt-2 text-sm text-gray-600">获奖项目：智能制造关键技术研发与应用</p>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <svg class="h-10 w-10 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-lg font-medium text-gray-900">宁波市科技创新领军人才</div>
                                        <div class="text-sm text-gray-500 mt-1">宁波市人民政府 | 2021年</div>
                                        <p class="mt-2 text-sm text-gray-600">表彰在智能制造领域的创新贡献</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 参政咨政及企业家培训 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            参政咨政及企业家培训
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造产业发展咨询委员会委员</div>
                                <div class="text-sm text-gray-500 mt-1">2020年至今</div>
                                <p class="mt-2 text-sm text-gray-600">参与宁波市智能制造产业政策制定与咨询</p>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="text-sm font-medium text-gray-900">浙江省企业家高级研修班</div>
                                <div class="text-sm text-gray-500 mt-1">清华大学经济管理学院 | 2019年</div>
                                <p class="mt-2 text-sm text-gray-600">完成全部课程并获得结业证书</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 社会活动 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            社会活动
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="text-sm font-medium text-gray-900">宁波市科技志愿者</div>
                                <div class="text-sm text-gray-500 mt-1">2018年至今</div>
                                <p class="mt-2 text-sm text-gray-600">参与科技下乡、科普讲座等公益活动</p>
                            </div>
                            <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="text-sm font-medium text-gray-900">宁波市智能制造产业联盟理事</div>
                                <div class="text-sm text-gray-500 mt-1">2019年至今</div>
                                <p class="mt-2 text-sm text-gray-600">推动产学研合作与技术创新</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 项目参与 -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            项目参与
                        </h2>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目编号</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">起止时间</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">责任分工</th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">宁波市智能制造示范工厂建设</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">NB2022ZD001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022.01-2024.12</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">技术负责人</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">进行中</span>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">工业互联网平台关键技术研发</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZJ2021KY001</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021.03-2023.06</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">项目负责人</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已结题</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧统计区 -->
            <div class="xl:col-span-1 space-y-6">
                <!-- 基本信息统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        基本信息统计
                    </h2>
                    <div class="space-y-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">工作年限</div>
                                <div class="text-xl font-bold text-blue-600">18年</div>
                            </div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">职称年限</div>
                                <div class="text-xl font-bold text-green-600">6年</div>
                            </div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="flex items-center justify-between">
                                <div class="text-sm text-gray-500">加入本单位</div>
                                <div class="text-xl font-bold text-yellow-600">5年</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 成果统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        成果统计
                    </h2>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">发明专利</span>
                            <span class="text-sm font-medium text-gray-900">8项</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">实用新型</span>
                            <span class="text-sm font-medium text-gray-900">12项</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">SCI论文</span>
                            <span class="text-sm font-medium text-gray-900">5篇</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">EI论文</span>
                            <span class="text-sm font-medium text-gray-900">10篇</span>
                        </div>
                    </div>
                </div>

                <!-- 项目统计 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        项目统计
                    </h2>
                    <div class="space-y-3">
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>国家级项目</span>
                                <span>2个</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 20%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>省级项目</span>
                                <span>5个</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 50%"></div>
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span>市级项目</span>
                                <span>8个</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 导出操作 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                        </svg>
                        导出操作
                    </h2>
                    <div class="space-y-3">
                        <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出PDF报告
                        </button>
                        <button class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出Excel数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>