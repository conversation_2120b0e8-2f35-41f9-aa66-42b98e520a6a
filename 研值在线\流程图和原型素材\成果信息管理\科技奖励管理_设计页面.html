<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技奖励管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">科技奖励管理</h1>
            <p class="text-gray-600">管理国家、省、市、区四级科技奖励信息，提供数据采集、维护和多维度关联分析功能</p>
        </div>

        <!-- 筛选控制区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                    </svg>
                    筛选条件
                </h2>
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"></path>
                    </svg>
                    保存筛选方案
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖励级别</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            国家级
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            省级
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            市级
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            区级
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">奖励类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="tech">科技进步奖</option>
                        <option value="invention">技术发明奖</option>
                        <option value="natural">自然科学奖</option>
                        <option value="patent">专利奖</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">获奖年份</label>
                    <div class="flex space-x-2">
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">起始年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                        <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">结束年份</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属单位</label>
                    <input type="text" placeholder="输入单位名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置
                </button>
                <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
            </div>
        </div>

        <!-- 奖励汇总概览区 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">国家级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">156</p>
                    </div>
                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        12.5%
                    </span>
                </div>
                <div class="mt-4 h-12">
                    <svg class="w-full h-full" viewBox="0 0 100 20">
                        <polyline points="0,15 20,10 40,12 60,8 80,5 100,10" stroke="#3b82f6" stroke-width="2" fill="none"/>
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">省级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">289</p>
                    </div>
                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        8.2%
                    </span>
                </div>
                <div class="mt-4 h-12">
                    <svg class="w-full h-full" viewBox="0 0 100 20">
                        <polyline points="0,10 20,15 40,12 60,8 80,10 100,5" stroke="#3b82f6" stroke-width="2" fill="none"/>
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">市级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">432</p>
                    </div>
                    <span class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded-full flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                        3.5%
                    </span>
                </div>
                <div class="mt-4 h-12">
                    <svg class="w-full h-full" viewBox="0 0 100 20">
                        <polyline points="0,5 20,10 40,8 60,12 80,15 100,10" stroke="#3b82f6" stroke-width="2" fill="none"/>
                    </svg>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 cursor-pointer hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                    <div>
                        <p class="text-sm text-gray-500">区级奖励</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">87</p>
                    </div>
                    <span class="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                        </svg>
                        15.8%
                    </span>
                </div>
                <div class="mt-4 h-12">
                    <svg class="w-full h-full" viewBox="0 0 100 20">
                        <polyline points="0,10 20,5 40,8 60,12 80,10 100,15" stroke="#3b82f6" stroke-width="2" fill="none"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- 数据表格区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                        </svg>
                        奖励列表
                    </h2>
                    <div class="flex space-x-2">
                        <div class="relative" id="exportDropdown">
                            <button onclick="toggleExportDropdown()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                导出
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div id="exportOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg hidden z-10">
                                <div class="py-1">
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为Excel</a>
                                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">导出为CSV</a>
                                </div>
                            </div>
                        </div>
                        <button onclick="openImportModal()" class="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            批量导入
                        </button>
                        <button onclick="openAddModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            新增奖励
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">获奖年份</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目/个人</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">所属单位</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">奖励类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">等级</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2023</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市智慧港口关键技术研究</div>
                                <div class="text-sm text-gray-500">项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波港集团有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技进步奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">国家级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editItem('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteItem('1')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2022</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">王伟</div>
                                <div class="text-sm text-gray-500">个人</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波大学</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">自然科学奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">省级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editItem('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteItem('2')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2021</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市新材料产业化应用</div>
                                <div class="text-sm text-gray-500">项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波新材料科技有限公司</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">技术发明奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">市级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">一等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">即将到期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editItem('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteItem('3')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2020</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波智能制造关键技术突破</div>
                                <div class="text-sm text-gray-500">项目</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波智能制造研究院</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">科技进步奖</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">区级</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">二等奖</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">已过期</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button onclick="viewDetail('4')" class="text-blue-600 hover:text-blue-900">详情</button>
                                    <button onclick="editItem('4')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                    <button onclick="deleteItem('4')" class="text-red-600 hover:text-red-900">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">4</span> 条，共 <span class="font-medium">156</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表分析区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    奖励分析
                </h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">年度趋势</button>
                    <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50">类型分布</button>
                    <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">单位排名</button>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">年度趋势</h3>
                    <div class="h-48">
                        <svg class="w-full h-full" viewBox="0 0 100 50">
                            <polyline points="10,40 30,30 50,35 70,20 90,10" stroke="#3b82f6" stroke-width="2" fill="none"/>
                            <line x1="10" y1="0" x2="10" y2="45" stroke="#9ca3af" stroke-width="1" stroke-dasharray="2,2"/>
                            <line x1="30" y1="0" x2="30" y2="45" stroke="#9ca3af" stroke-width="1" stroke-dasharray="2,2"/>
                            <line x1="50" y1="0" x2="50" y2="45" stroke="#9ca3af" stroke-width="1" stroke-dasharray="2,2"/>
                            <line x1="70" y1="0" x2="70" y2="45" stroke="#9ca3af" stroke-width="1" stroke-dasharray="2,2"/>
                            <line x1="90" y1="0" x2="90" y2="45" stroke="#9ca3af" stroke-width="1" stroke-dasharray="2,2"/>
                            <text x="10" y="48" font-size="3" text-anchor="middle" fill="#6b7280">2020</text>
                            <text x="30" y="48" font-size="3" text-anchor="middle" fill="#6b7280">2021</text>
                            <text x="50" y="48" font-size="3" text-anchor="middle" fill="#6b7280">2022</text>
                            <text x="70" y="48" font-size="3" text-anchor="middle" fill="#6b7280">2023</text>
                            <text x="90" y="48" font-size="3" text-anchor="middle" fill="#6b7280">2024</text>
                        </svg>
                    </div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">类型分布</h3>
                    <div class="h-48 flex items-center justify-center">
                        <svg class="w-32 h-32" viewBox="0 0 32 32">
                            <circle cx="16" cy="16" r="15" stroke="#e5e7eb" stroke-width="2" fill="none"/>
                            <path d="M16 16 L16 1 A15 15 0 0 1 28 28 Z" fill="#3b82f6"/>
                            <path d="M16 16 L28 28 A15 15 0 0 1 16 31 Z" fill="#10b981"/>
                            <path d="M16 16 L16 31 A15 15 0 0 1 4 4 Z" fill="#f59e0b"/>
                            <circle cx="16" cy="16" r="8" fill="#f3f4f6"/>
                        </svg>
                    </div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h3 class="text-sm font-medium text-gray-700 mb-3">单位排名</h3>
                    <div class="h-48">
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 bg-blue-600 mr-2"></div>
                            <span class="text-xs text-gray-700">宁波大学</span>
                            <span class="ml-auto text-xs font-medium">45</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 bg-blue-500 mr-2"></div>
                            <span class="text-xs text-gray-700">宁波港集团</span>
                            <span class="ml-auto text-xs font-medium">32</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 bg-blue-400 mr-2"></div>
                            <span class="text-xs text-gray-700">宁波智能制造研究院</span>
                            <span class="ml-auto text-xs font-medium">28</span>
                        </div>
                        <div class="flex items-center mb-2">
                            <div class="w-4 h-4 bg-blue-300 mr-2"></div>
                            <span class="text-xs text-gray-700">宁波新材料科技</span>
                            <span class="ml-auto text-xs font-medium">18</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-blue-200 mr-2"></div>
                            <span class="text-xs text-gray-700">宁波工程学院</span>
                            <span class="ml-auto text-xs font-medium">12</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 浮动工具条 -->
        <div class="fixed bottom-6 right-6 bg-white shadow-lg rounded-lg p-4 z-10">
            <div class="flex space-x-2">
                <button class="bg-white border border-gray-300 text-gray-700 px-3 py-2 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    模板
                </button>
                <button class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    导入
                </button>
                <button class="bg-green-600 text-white px-3 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    生成标签
                </button>
            </div>
        </div>
    </div>

    <!-- 详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full max-w-lg bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-6 border-b">
                <h3 class="text-lg font-semibold text-gray-900">奖励详情</h3>
                <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <div class="p-6 overflow-y-auto flex-1">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">基本信息</h4>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p class="text-xs text-gray-500">获奖年份</p>
                                <p class="text-sm text-gray-900">2023</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">奖励类型</p>
                                <p class="text-sm text-gray-900">科技进步奖</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">级别</p>
                                <p class="text-sm text-gray-900">国家级</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">等级</p>
                                <p class="text-sm text-gray-900">一等奖</p>
                            </div>
                            <div>
                                <p class="text-xs text-gray-500">状态</p>
                                <p class="text-sm text-gray-900">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">获奖项目/个人</h4>
                        <p class="text-sm text-gray-900">宁波市智慧港口关键技术研究</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">所属单位</h4>
                        <p class="text-sm text-gray-900">宁波港集团有限公司</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">主要完成人</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">张伟</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">王芳</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">李强</span>
                            <span class="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs">刘明</span>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">奖励背景说明</h4>
                        <p class="text-sm text-gray-900">该项目针对宁波港口智能化升级中的关键技术问题，研发了一套完整的智慧港口解决方案，显著提升了港口运营效率和安全性。</p>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">获奖证书</h4>
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <p class="mt-2 text-sm text-gray-600">证书预览</p>
                            <button class="mt-2 text-blue-600 hover:text-blue-800 text-sm">下载扫描件</button>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-500 mb-2">关联信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h5 class="text-xs font-medium text-gray-500 mb-1">关联项目</h5>
                                <ul class="text-sm text-gray-900 space-y-1">
                                    <li>宁波智慧港口建设项目</li>
                                    <li>港口自动化技术研发</li>
                                </ul>
                            </div>
                            <div>
                                <h5 class="text-xs font-medium text-gray-500 mb-1">关联主体</h5>
                                <ul class="text-sm text-gray-900 space-y-1">
                                    <li>宁波港集团技术中心</li>
                                    <li>宁波大学海洋工程学院</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-4 border-t bg-gray-50 flex-shrink-0">
                <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    关联更多信息
                </button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑奖励弹窗 -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">新增科技奖励</h3>
                    <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">获奖年份 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择年份</option>
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                    <option value="2022">2022</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">奖励类型 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择类型</option>
                                    <option value="tech">科技进步奖</option>
                                    <option value="invention">技术发明奖</option>
                                    <option value="natural">自然科学奖</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">奖励级别 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择级别</option>
                                    <option value="national">国家级</option>
                                    <option value="provincial">省级</option>
                                    <option value="municipal">市级</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">奖励等级 <span class="text-red-500">*</span></label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">请选择等级</option>
                                    <option value="first">一等奖</option>
                                    <option value="second">二等奖</option>
                                    <option value="third">三等奖</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">项目/个人名称 <span class="text-red-500">*</span></label>
                            <input type="text" placeholder="请输入项目或个人名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">所属单位 <span class="text-red-500">*</span></label>
                            <input type="text" placeholder="请输入所属单位" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">主要完成人</label>
                            <div class="flex flex-wrap gap-2">
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs flex items-center">
                                    张伟
                                    <button class="ml-1 text-blue-600 hover:text-blue-900">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </span>
                                <button class="px-2 py-1 border border-dashed border-gray-300 rounded-full text-xs text-gray-500 hover:bg-gray-50 flex items-center">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    添加
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">奖励说明</label>
                            <textarea rows="3" placeholder="请输入奖励背景说明" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">证书扫描件</label>
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <div class="mt-2">
                                    <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                        <span>点击上传文件</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="text-xs text-gray-500">支持JPG,PNG,PDF格式，最大10MB</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeEditModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        保存
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入弹窗 -->
    <div id="importModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="flex items-center justify-between p-6 border-b flex-shrink-0">
                    <h3 class="text-lg font-semibold text-gray-900">批量导入奖励数据</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="space-y-6">
                        <div>
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                                </svg>
                                下载导入模板
                            </a>
                            <p class="mt-1 text-xs text-gray-500">请使用标准模板填写数据，确保格式正确</p>
                        </div>

                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <div class="mt-2">
                                <label for="import-file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                    <span>点击上传文件</span>
                                    <input id="import-file-upload" name="import-file-upload" type="file" class="sr-only">
                                </label>
                                <p class="text-xs text-gray-500">或拖拽文件到此处</p>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">支持.xlsx格式，最大10MB</p>
                        </div>

                        <div id="importProgress" class="hidden">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span class="text-sm font-medium text-gray-700">75%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>

                        <div id="importResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-yellow-800">发现3条数据存在问题</p>
                                    <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                        <li>第5行：奖励类型无效</li>
                                        <li>第8行：获奖年份缺失</li>
                                        <li>第12行：所属单位重复</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-6 border-t bg-gray-50 flex-shrink-0">
                    <button onclick="closeImportModal()" class="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors">
                        取消
                    </button>
                    <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors">
                        开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 详情抽屉
        function viewDetail(id) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
        }

        function closeDetailDrawer() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
        }

        // 编辑弹窗
        function editItem(id) {
            document.getElementById('editModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        // 新增弹窗
        function openAddModal() {
            document.getElementById('editModal').classList.remove('hidden');
        }

        // 导入弹窗
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
        }

        function closeImportModal() {
            document.getElementById('importModal').classList.add('hidden');
        }

        // 导出下拉菜单
        function toggleExportDropdown() {
            document.getElementById('exportOptions').classList.toggle('hidden');
        }

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('uploadProgress').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#uploadProgress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#uploadProgress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                        setTimeout(() => {
                            document.getElementById('uploadProgress').classList.add('hidden');
                            alert('文件上传成功！');
                        }, 500);
                    }
                }, 200);
            }
        });

        // 导入文件上传处理
        document.getElementById('import-file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('importProgress').classList.remove('hidden');
                document.getElementById('importResult').classList.remove('hidden');
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 10;
                    document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#importProgress span:last-child').textContent = progress + '%';
                    if (progress >= 100) {
                        clearInterval(interval);
                    }
                }, 200);
            }
        });

        // 点击模态框外部关闭
        document.getElementById('editModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });

        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportModal();
            }
        });

        // 点击其他区域关闭下拉菜单
        document.addEventListener('click', function(e) {
            // 导出下拉菜单
            const exportDropdown = document.getElementById('exportDropdown');
            const exportOptions = document.getElementById('exportOptions');
            if (!exportDropdown.contains(e.target) && !exportOptions.classList.contains('hidden')) {
                exportOptions.classList.add('hidden');
            }
        });

        // 删除确认
        function deleteItem(id) {
            if (confirm('确定要删除这条奖励记录吗？此操作不可恢复！')) {
                console.log('删除记录:', id);
            }
        }
    </script>
</body>
</html>