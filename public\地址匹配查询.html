<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地址匹配查询</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        blue: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            500: '#3b82f6',
                            700: '#1d4ed8',
                            800: '#1e40af'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="flex-1 p-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-blue-800 flex items-center">
                <svg class="w-6 h-6 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
                地址匹配查询
            </h1>
            <p class="text-gray-600 mt-1">科研人才与项目管理场景下的地址标准化服务</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-160px)]">
            <!-- 左侧主要内容 -->
            <div class="lg:col-span-2 space-y-6">
                <!-- 查询条件区 -->
                <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                    <h2 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                        查询条件
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-2">待匹配地址</label>
                            <input type="text" 
                                   placeholder="请输入地址信息，支持拼写纠错与实时联想"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">匹配维度</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option>行政区匹配</option>
                                <option>功能区匹配</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">开始时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">结束时间</label>
                            <input type="date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="flex items-end gap-2">
                            <button class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                                查询
                            </button>
                            <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">重置</button>
                        </div>
                    </div>
                </div>

                <!-- 选项卡切换 -->
                <div class="bg-white rounded-lg shadow-md border border-blue-100">
                    <div class="border-b border-gray-200">
                        <nav class="flex space-x-8 px-6" aria-label="Tabs">
                            <button class="tab-button active border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600" data-tab="results">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                查询结果
                            </button>
                            <button class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300" data-tab="batch">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                                </svg>
                                批量导入
                            </button>
                        </nav>
                    </div>

                    <!-- 查询结果区 -->
                    <div id="results-tab" class="tab-content p-6">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100">
                                            原始地址
                                            <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"/>
                                            </svg>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标准化地址</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配行政区</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">匹配功能区</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">置信度</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-900">北京市海淀区中关村软件园</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">北京市海淀区中关村软件园二期</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">海淀区</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">中关村科技园区</td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                95%
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                已完成
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm space-x-2">
                                            <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button onclick="copyResult('1')" class="text-green-600 hover:text-green-900">复制结果</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-900">上海浦东新区张江高科技园</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">上海市浦东新区张江高科技园区</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">浦东新区</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">张江科学城</td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                82%
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                已完成
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm space-x-2">
                                            <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button onclick="copyResult('2')" class="text-green-600 hover:text-green-900">复制结果</button>
                                        </td>
                                    </tr>
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 text-sm text-gray-900">深圳南山科技园</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">深圳市南山区高新技术产业园</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">南山区</td>
                                        <td class="px-6 py-4 text-sm text-gray-900">深圳高新区</td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                65%
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                待校正
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 text-sm space-x-2">
                                            <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">查看详情</button>
                                            <button onclick="copyResult('3')" class="text-green-600 hover:text-green-900">复制结果</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-4 flex items-center justify-between">
                            <div class="text-sm text-gray-700">
                                显示第 1-3 条，共 3 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50" disabled>上一页</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded disabled:opacity-50" disabled>下一页</button>
                            </div>
                        </div>
                    </div>

                    <!-- 批量导入区 -->
                    <div id="batch-tab" class="tab-content p-6 hidden">
                        <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                            </svg>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">上传文件进行批量匹配</h3>
                            <p class="text-gray-500 mb-4">支持 Excel (.xlsx) 和 CSV (.csv) 格式文件</p>
                            <div class="space-y-4">
                                <input type="file" id="file-upload" class="hidden" accept=".xlsx,.csv">
                                <button onclick="document.getElementById('file-upload').click()" class="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors">
                                    选择文件
                                </button>
                                <div class="text-sm text-gray-500">
                                    或拖拽文件到此处
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex items-center justify-between">
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-sm flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                                下载标准模板
                            </a>
                            
                            <div class="text-sm text-gray-500">
                                最大支持 10,000 条记录
                            </div>
                        </div>

                        <!-- 处理进度条 (默认隐藏) -->
                        <div id="progress-section" class="mt-6 hidden">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">处理进度</h4>
                            <div class="bg-gray-200 rounded-full h-3 mb-2">
                                <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" style="width: 0%" id="progress-bar"></div>
                            </div>
                            <div class="text-sm text-gray-600" id="progress-text">等待处理...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧导出和共享区 -->
            <div class="space-y-6">
                <!-- 导出与共享 -->
                <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                        导出与共享
                    </h3>
                    <div class="space-y-3">
                        <button class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3"/>
                            </svg>
                            导出 Excel
                        </button>
                        <button class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                            </svg>
                            生成共享链接
                        </button>
                    </div>
                    
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h4 class="text-sm font-medium text-gray-700 mb-2">导出历史</h4>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex justify-between">
                                <span>2024-01-15 14:30</span>
                                <span class="text-green-600">成功</span>
                            </div>
                            <div class="flex justify-between">
                                <span>2024-01-14 09:15</span>
                                <span class="text-green-600">成功</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="bg-white rounded-lg shadow-md border border-blue-100 p-6">
                    <h3 class="text-lg font-semibold text-blue-800 mb-4 flex items-center">
                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                        统计信息
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">总处理数量</span>
                            <span class="font-semibold text-blue-600">3</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">成功匹配</span>
                            <span class="font-semibold text-green-600">2</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">待校正</span>
                            <span class="font-semibold text-yellow-600">1</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">匹配失败</span>
                            <span class="font-semibold text-red-600">0</span>
                        </div>
                        <div class="pt-2 border-t border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="text-gray-600">平均置信度</span>
                                <span class="font-semibold text-blue-600">80.7%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div id="detail-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-blue-800">地址匹配详情</h3>
                    <button onclick="closeDetail()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- 左侧：地址信息 -->
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">原始地址</h4>
                                <p class="text-gray-600 bg-gray-50 p-3 rounded">北京市海淀区中关村软件园</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">标准化地址</h4>
                                <p class="text-gray-600 bg-gray-50 p-3 rounded">北京市海淀区中关村软件园二期</p>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">地址分词路径</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center space-x-2">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">省/直辖市</span>
                                        <span class="text-gray-600">北京市</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">区/县</span>
                                        <span class="text-gray-600">海淀区</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">功能区</span>
                                        <span class="text-gray-600">中关村软件园</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">层级归属链</h4>
                                <p class="text-sm text-gray-600">中华人民共和国 → 北京市 → 海淀区 → 中关村科技园区 → 中关村软件园</p>
                            </div>
                        </div>
                        
                        <!-- 右侧：地图预览 -->
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">地图定位预览</h4>
                                <div class="bg-gray-100 h-64 rounded-lg flex items-center justify-center">
                                    <img src="https://source.unsplash.com/400x300?map,beijing" 
                                         alt="地图预览" 
                                         class="w-full h-full object-cover rounded-lg">
                                </div>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">匹配信息</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">匹配置信度</span>
                                        <span class="font-medium text-green-600">95%</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">经纬度</span>
                                        <span class="font-medium">116.3074, 40.0298</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">邮政编码</span>
                                        <span class="font-medium">100190</span>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-medium text-gray-900 mb-2">人工校正</h4>
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md" 
                                         rows="3" 
                                         placeholder="如需要校正，请在此输入正确的地址信息"></textarea>
                                <button class="mt-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                    保存校正
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换功能
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 更新按钮状态
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                this.classList.add('active', 'border-blue-500', 'text-blue-600');
                this.classList.remove('border-transparent', 'text-gray-500');
                
                // 切换内容区域
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(targetTab + '-tab').classList.remove('hidden');
            });
        });

        // 显示详情弹窗
        function showDetail(id) {
            document.getElementById('detail-modal').classList.remove('hidden');
        }

        // 关闭详情弹窗
        function closeDetail() {
            document.getElementById('detail-modal').classList.add('hidden');
        }

        // 复制结果
        function copyResult(id) {
            // 这里可以实现复制到剪贴板的功能
            alert('结果已复制到剪贴板');
        }

        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                const progressSection = document.getElementById('progress-section');
                const progressBar = document.getElementById('progress-bar');
                const progressText = document.getElementById('progress-text');
                
                progressSection.classList.remove('hidden');
                progressText.textContent = `正在处理文件: ${file.name}`;
                
                // 模拟进度条
                let progress = 0;
                const interval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress >= 100) {
                        progress = 100;
                        clearInterval(interval);
                        progressText.textContent = '处理完成';
                    }
                    progressBar.style.width = progress + '%';
                }, 500);
            }
        });

        // 点击模态框背景关闭弹窗
        document.getElementById('detail-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetail();
            }
        });
    </script>
</body>
</html> 