<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高等院校信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">高等院校信息管理</h1>

        <!-- 查询条件区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">查询条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="universityName" class="block text-sm font-medium text-gray-700 mb-1">高校名称</label>
                    <input type="text" id="universityName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入高校名称">
                </div>
                <div>
                    <label for="creditCode" class="block text-sm font-medium text-gray-700 mb-1">统一社会信用代码</label>
                    <input type="text" id="creditCode" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入信用代码">
                </div>
                <div>
                    <label for="sponsor" class="block text-sm font-medium text-gray-700 mb-1">举办单位</label>
                    <select id="sponsor" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="ministry">教育部</option>
                        <option value="province">省教育厅</option>
                        <option value="municipal">市教育局</option>
                        <option value="private">民办</option>
                    </select>
                </div>
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700 mb-1">院校类型</label>
                    <select id="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="comprehensive">综合类</option>
                        <option value="polytechnic">理工类</option>
                        <option value="normal">师范类</option>
                        <option value="medical">医药类</option>
                    </select>
                </div>
                <div>
                    <label for="region" class="block text-sm font-medium text-gray-700 mb-1">所在行政区划</label>
                    <select id="region" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="haishu">海曙区</option>
                        <option value="jiangdong">江东区</option>
                        <option value="jiangbei">江北区</option>
                        <option value="yinzhou">鄞州区</option>
                    </select>
                </div>
                <div>
                    <label for="discipline" class="block text-sm font-medium text-gray-700 mb-1">学科门类</label>
                    <select id="discipline" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="philosophy">哲学</option>
                        <option value="economics">经济学</option>
                        <option value="law">法学</option>
                        <option value="education">教育学</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 批量管理工具栏 -->
        <div class="bg-white shadow-md rounded-lg p-4 mb-6">
            <div class="flex flex-wrap items-center justify-between">
                <div class="flex space-x-2 mb-2 sm:mb-0">
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新增院校
                    </button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        批量导入
                    </button>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        批量导出
                    </button>
                </div>
                <div>
                    <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        下载模板
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据列表展示区 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">院校名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">统一社会信用代码</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">举办单位</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">院校层次</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在校生规模</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">师资人数</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">重点学科</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">科创平台</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据完整度</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波大学</div>
                                <div class="text-sm text-gray-500">综合类</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345678</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">浙江省教育厅</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">双一流</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">25,000</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">1,856</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 95%"></div>
                                </div>
                                <span class="text-xs text-gray-500 mt-1">95%</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波工程学院</div>
                                <div class="text-sm text-gray-500">理工类</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345679</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">宁波市教育局</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">本科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">12,500</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">856</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">5</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">4</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 75%"></div>
                                </div>
                                <span class="text-xs text-gray-500 mt-1">75%</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波诺丁汉大学</div>
                                <div class="text-sm text-gray-500">中外合作</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">123456789012345680</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">民办</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">本科</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">8,200</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">420</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">3</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">2</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-red-500 h-2.5 rounded-full" style="width: 60%"></div>
                                </div>
                                <span class="text-xs text-gray-500 mt-1">60%</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                <button onclick="showDetail('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                                <button class="text-red-600 hover:text-red-900">删除</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        显示 <span class="font-medium">1</span> 到 <span class="font-medium">3</span> 条，共 <span class="font-medium">42</span> 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 院校详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-full max-w-3xl bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50">
        <div class="flex flex-col h-full">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">院校详情</h3>
                <button onclick="hideDetail()" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <!-- 基本信息 -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-900">基本信息</h4>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">编辑信息</button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p class="text-sm text-gray-500">院校名称</p>
                            <p class="text-sm font-medium text-gray-900">宁波大学</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">统一社会信用代码</p>
                            <p class="text-sm font-medium text-gray-900">123456789012345678</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">举办单位</p>
                            <p class="text-sm font-medium text-gray-900">浙江省教育厅</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">院校层次</p>
                            <p class="text-sm font-medium text-gray-900">双一流</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">院校类型</p>
                            <p class="text-sm font-medium text-gray-900">综合类</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">所在行政区划</p>
                            <p class="text-sm font-medium text-gray-900">宁波市鄞州区</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">建校时间</p>
                            <p class="text-sm font-medium text-gray-900">1986年</p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">占地面积</p>
                            <p class="text-sm font-medium text-gray-900">3,500亩</p>
                        </div>
                    </div>
                </div>

                <!-- 师资队伍 -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-900">师资队伍</h4>
                        <button onclick="showRelationModal('faculty')" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">管理关联</button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">专任教师总数</p>
                            <p class="text-xl font-bold text-blue-600">1,856</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">教授人数</p>
                            <p class="text-xl font-bold text-green-600">356</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">博士比例</p>
                            <p class="text-xl font-bold text-purple-600">78%</p>
                        </div>
                    </div>
                </div>

                <!-- 重点学科 -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-900">重点学科</h4>
                        <button onclick="showRelationModal('discipline')" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">管理关联</button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">国家级重点学科</p>
                            <p class="text-xl font-bold text-blue-600">5</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">省级重点学科</p>
                            <p class="text-xl font-bold text-green-600">12</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">市级重点学科</p>
                            <p class="text-xl font-bold text-purple-600">8</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-2">学科列表</h5>
                        <div class="flex flex-wrap gap-2">
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">信息与通信工程</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">应用海洋生物技术</span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">力学</span>
                        </div>
                    </div>
                </div>

                <!-- 科创平台 -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-semibold text-gray-900">科创平台</h4>
                        <button onclick="showRelationModal('platform')" class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700">管理关联</button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">国家级平台</p>
                            <p class="text-xl font-bold text-blue-600">3</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">省级平台</p>
                            <p class="text-xl font-bold text-green-600">5</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <p class="text-sm text-gray-500">市级平台</p>
                            <p class="text-xl font-bold text-purple-600">8</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <h5 class="text-sm font-medium text-gray-700 mb-2">平台列表</h5>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between p-2 hover:bg-gray-50">
                                <span class="text-sm">宁波市新型功能材料及其制备科学国家重点实验室</span>
                                <span class="text-xs text-gray-500">国家级</span>
                            </div>
                            <div class="flex items-center justify-between p-2 hover:bg-gray-50">
                                <span class="text-sm">浙江省海洋生物工程重点实验室</span>
                                <span class="text-xs text-gray-500">省级</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 关联管理弹窗 -->
    <div id="relationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
                <div class="px-6 py-4 border-b flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900" id="relationModalTitle">关联管理</h3>
                    <button onclick="hideRelationModal()" class="text-gray-400 hover:text-gray-600">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div class="p-6 overflow-y-auto flex-1">
                    <div class="mb-4">
                        <input type="text" placeholder="搜索..." class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-3 text-sm">王教授 - 信息与通信工程</span>
                            </div>
                            <span class="text-xs text-gray-500">教授</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-3 text-sm">李教授 - 应用海洋生物技术</span>
                            </div>
                            <span class="text-xs text-gray-500">教授</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                                <span class="ml-3 text-sm">张教授 - 力学</span>
                            </div>
                            <span class="text-xs text-gray-500">教授</span>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 p-4 border-t bg-gray-50">
                    <button onclick="hideRelationModal()" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        确认关联
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示详情抽屉
        function showDetail(id) {
            document.getElementById('detailDrawer').classList.remove('translate-x-full');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏详情抽屉
        function hideDetail() {
            document.getElementById('detailDrawer').classList.add('translate-x-full');
            document.body.style.overflow = 'auto';
        }

        // 显示关联管理弹窗
        function showRelationModal(type) {
            const modal = document.getElementById('relationModal');
            const title = document.getElementById('relationModalTitle');
            
            if (type === 'faculty') {
                title.textContent = '关联师资队伍';
            } else if (type === 'discipline') {
                title.textContent = '关联重点学科';
            } else if (type === 'platform') {
                title.textContent = '关联科创平台';
            }
            
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        // 隐藏关联管理弹窗
        function hideRelationModal() {
            document.getElementById('relationModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        // 点击弹窗外部关闭
        document.getElementById('relationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideRelationModal();
            }
        });
    </script>
</body>
</html>