<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科技表单信息系统 - 数字化科技项目管理平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .hover-lift {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .hover-lift:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        }
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .module-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid #E5E9EF;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clipboard-list text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">科技表单信息系统</h1>
                        <p class="text-sm text-gray-600">数字化科技项目全流程管理平台</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <div class="status-indicator bg-green-500"></div>
                        <span>系统运行正常</span>
                    </div>
                    <button class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-user-circle mr-2"></i>
                        管理员
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-7xl mx-auto px-6 py-8">
        <!-- 系统概览统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">总表单数</p>
                        <p class="text-2xl font-bold text-gray-900">2,468</p>
                        <p class="text-xs text-green-600 mt-1">
                            <i class="fas fa-arrow-up mr-1"></i>+12.5%
                        </p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">已完成</p>
                        <p class="text-2xl font-bold text-gray-900">1,856</p>
                        <p class="text-xs text-blue-600 mt-1">完成率 75.2%</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-clock text-orange-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">进行中</p>
                        <p class="text-2xl font-bold text-gray-900">486</p>
                        <p class="text-xs text-orange-600 mt-1">需跟进</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-handshake text-purple-600 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">成功对接</p>
                        <p class="text-2xl font-bold text-gray-900">326</p>
                        <p class="text-xs text-purple-600 mt-1">对接率 13.2%</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能模块导航 -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-gray-900">功能模块</h2>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">共8个模块</span>
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 共性基础信息模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-basic.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-database text-blue-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">基础</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">共性基础信息</h3>
                    <p class="text-sm text-gray-600 mb-4">采集所有科技项目通用属性，作为表单的核心主表</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>已录入</span>
                            <span class="font-medium">2,468条</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-500 h-2 rounded-full" style="width: 85%"></div>
                        </div>
                    </div>
                </div>

                <!-- 专项征集模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-special.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-bullhorn text-purple-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium">专项</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">专项征集</h3>
                    <p class="text-sm text-gray-600 mb-4">科创甬江2035、自然基金等专项需求信息采集</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>活跃专项</span>
                            <span class="font-medium">15个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-purple-500 h-2 rounded-full" style="width: 68%"></div>
                        </div>
                    </div>
                </div>

                <!-- 企业接触收集模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-enterprise.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-green-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">企业</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">企业接触收集</h3>
                    <p class="text-sm text-gray-600 mb-4">记录走访企业过程中采集到的技术需求信息</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>走访企业</span>
                            <span class="font-medium">328家</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-green-500 h-2 rounded-full" style="width: 72%"></div>
                        </div>
                    </div>
                </div>

                <!-- 科技大市场挖掘模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-market.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-store text-orange-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-xs font-medium">市场</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">科技大市场挖掘</h3>
                    <p class="text-sm text-gray-600 mb-4">从科技市场场景中挖掘企业技术需求</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>市场需求</span>
                            <span class="font-medium">156条</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-orange-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                    </div>
                </div>

                <!-- 供需匹配对接模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-matching.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-handshake text-teal-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-teal-100 text-teal-700 rounded-full text-xs font-medium">对接</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">供需匹配对接</h3>
                    <p class="text-sm text-gray-600 mb-4">记录供需对接推进情况与处理状态</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>对接成功</span>
                            <span class="font-medium">326次</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-teal-500 h-2 rounded-full" style="width: 58%"></div>
                        </div>
                    </div>
                </div>

                <!-- 活动推送模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-activity.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-check text-pink-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-pink-100 text-pink-700 rounded-full text-xs font-medium">活动</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">活动推送</h3>
                    <p class="text-sm text-gray-600 mb-4">记录需求参与其他活动的流程及采用情况</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>推送活动</span>
                            <span class="font-medium">68个</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-pink-500 h-2 rounded-full" style="width: 38%"></div>
                        </div>
                    </div>
                </div>

                <!-- 企业常态化填报模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-regular.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-edit text-indigo-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-xs font-medium">填报</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">企业常态化填报</h3>
                    <p class="text-sm text-gray-600 mb-4">企业日常提交的需求信息采集</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>日常填报</span>
                            <span class="font-medium">892条</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-indigo-500 h-2 rounded-full" style="width: 76%"></div>
                        </div>
                    </div>
                </div>

                <!-- 省技术需求回流模块 -->
                <div class="module-card rounded-xl p-6 hover-lift cursor-pointer" onclick="window.open('form-provincial.html', '_blank')">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-exchange-alt text-yellow-600 text-xl"></i>
                        </div>
                        <span class="px-3 py-1 bg-yellow-100 text-yellow-700 rounded-full text-xs font-medium">回流</span>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">省技术需求回流</h3>
                    <p class="text-sm text-gray-600 mb-4">采集省级技术对接平台反馈的需求信息</p>
                    <div class="space-y-2 text-xs text-gray-500">
                        <div class="flex justify-between">
                            <span>回流需求</span>
                            <span class="font-medium">134条</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 28%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状态流转图 -->
        <div class="bg-white rounded-xl card-shadow p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-gray-900">需求状态流转图</h3>
                <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    <i class="fas fa-expand-arrows-alt mr-1"></i>
                    查看详细流程
                </button>
            </div>
            <div class="flex items-center justify-between">
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-plus text-blue-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900">需求提出</span>
                    <span class="text-xs text-gray-500">2,468</span>
                </div>
                <div class="flex-1 mx-4 border-t-2 border-dashed border-gray-300 relative">
                    <div class="absolute top-0 left-1/4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-400 transform -translate-y-1"></div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-search text-orange-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900">需求评估</span>
                    <span class="text-xs text-gray-500">1,956</span>
                </div>
                <div class="flex-1 mx-4 border-t-2 border-dashed border-gray-300 relative">
                    <div class="absolute top-0 left-1/4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-400 transform -translate-y-1"></div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-users text-purple-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900">供需匹配</span>
                    <span class="text-xs text-gray-500">1,124</span>
                </div>
                <div class="flex-1 mx-4 border-t-2 border-dashed border-gray-300 relative">
                    <div class="absolute top-0 left-1/4 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-400 transform -translate-y-1"></div>
                </div>
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                        <i class="fas fa-check text-green-600"></i>
                    </div>
                    <span class="text-sm font-medium text-gray-900">成功对接</span>
                    <span class="text-xs text-gray-500">326</span>
                </div>
            </div>
        </div>

        <!-- 快速操作区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 最新动态 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">最新动态</h3>
                    <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">查看全部</button>
                </div>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900 font-medium">新增专项征集表单</p>
                            <p class="text-xs text-gray-500 mt-1">科创甬江2035专项新增15个需求表单</p>
                            <span class="text-xs text-gray-400">2分钟前</span>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900 font-medium">供需对接成功</p>
                            <p class="text-xs text-gray-500 mt-1">宁波智能制造公司与大学实验室达成合作</p>
                            <span class="text-xs text-gray-400">15分钟前</span>
                        </div>
                    </div>
                    <div class="flex items-start space-x-3">
                        <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900 font-medium">企业走访活动</p>
                            <p class="text-xs text-gray-500 mt-1">完成高新区12家企业技术需求调研</p>
                            <span class="text-xs text-gray-400">1小时前</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统计图表 -->
            <div class="bg-white rounded-xl card-shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">模块使用统计</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">本月</button>
                        <button class="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200">本年</button>
                    </div>
                </div>
                <div class="h-48 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg flex items-center justify-center">
                    <img src="https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=300&fit=crop&crop=entropy" 
                         alt="使用统计图表" 
                         class="w-full h-full object-cover rounded-lg opacity-80">
                    <div class="absolute inset-0 bg-blue-500 bg-opacity-10 rounded-lg flex items-center justify-center">
                        <div class="text-center text-blue-800">
                            <i class="fas fa-chart-bar text-3xl mb-2"></i>
                            <p class="font-semibold">模块使用统计图</p>
                            <p class="text-sm mt-1">各模块月度使用频次对比</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 py-6 mt-12">
        <div class="max-w-7xl mx-auto px-6">
            <div class="flex items-center justify-between">
                <p class="text-sm text-gray-500">
                    © 2024 科技表单信息系统. 宁波市科技局.
                </p>
                <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>系统版本：v2.1.0</span>
                    <span>|</span>
                    <span>技术支持：数字化平台</span>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 页面加载完成后的交互功能
        document.addEventListener('DOMContentLoaded', function() {
            // 模拟实时数据更新
            setInterval(function() {
                const indicators = document.querySelectorAll('.status-indicator');
                indicators.forEach(indicator => {
                    indicator.style.opacity = indicator.style.opacity === '0.5' ? '1' : '0.5';
                });
            }, 2000);

            // 添加卡片点击效果
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                card.addEventListener('click', function() {
                    // 添加点击反馈效果
                    this.style.transform = 'translateY(-2px)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html> 