import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

// 年度总览（对应 FillGridByDate 逻辑的汇总）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const year = searchParams.get('year') || '2025'
    const kh = searchParams.get('kh') || 'J B'
    const debug = searchParams.get('debug') === '1'

    const beginTime = `${year}-01-01`
    const endTime = `${year}-12-31`

    // 基于 Delphi 的 SQL 语义进行等价迁移，直接返回分列字段，避免字符串拆分误差
    const sql = `
      select
        sum(xx_scdd.dds) as zj_xl2,
        xx_scdd.ddlb,
        xx_scdd.ddml,
        xx_scdd.ddks
      from xx_scdd
      join cc_sj_scdj on cc_sj_scdj.scid = xx_scdd.scid
      where xx_scdd.kh = ?
        and xx_scdd.xdrq >= ? and xx_scdd.xdrq <= ?
      group by xx_scdd.ddlb, xx_scdd.ddml, xx_scdd.ddks
      order by zj_xl2 desc
      limit 99
    `

    const rows = await query(sql, [kh, beginTime, endTime])

    const result = rows.map((r: any) => ({
      ddlb: r.ddlb as string,
      ddml: r.ddml as string,
      ddks: r.ddks as string,
      value: Number(r.zj_xl2) || 0,
    }))

    if (debug) {
      console.log('[API all] year=', year, 'kh=', kh, 'rows=', rows.length)
      console.log('[API all] 原始数据前3条:', rows.slice(0, 3))
      console.log('[API all] 处理后数据前3条:', result.slice(0, 3))
      console.log('[API all] 完整返回数据结构:', {
        success: true,
        dataLength: result.length,
        data: result.slice(0, 3), // 显示前3条数据
        meta: { rows: rows.length, year, kh }
      })
    }
    return NextResponse.json({ success: true, data: result, meta: debug ? { rows: rows.length, year, kh } : undefined })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e?.message || '查询失败' }, { status: 500 })
  }
}


