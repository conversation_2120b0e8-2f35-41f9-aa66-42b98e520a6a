<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目检测大屏 - 科技项目智能监测分析平台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .hover-lift {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .gradient-text {
            background: linear-gradient(135deg, #3B82F6, #1E40AF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .pulse-dot {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .chart-placeholder {
            background: linear-gradient(45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(-45deg, #f8fafc 25%, transparent 25%),
                       linear-gradient(45deg, transparent 75%, #f8fafc 75%),
                       linear-gradient(-45deg, transparent 75%, #f8fafc 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-project-diagram text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold gradient-text">项目检测大屏</h1>
                        <p class="text-sm text-gray-600">科技项目智能监测分析平台</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <div class="w-2 h-2 bg-green-500 rounded-full pulse-dot"></div>
                        <span>系统正常运行</span>
                    </div>
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 项目概览仪表板 -->
    <div class="max-w-7xl mx-auto px-6 py-6">
        <!-- 核心统计指标 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">项目总数</p>
                        <p class="text-2xl font-bold text-gray-900">2,847</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +12.5%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">项目总投资</p>
                        <p class="text-2xl font-bold text-gray-900">156.8亿</p>
                        <p class="text-green-600 text-xs">
                            <i class="fas fa-arrow-up mr-1"></i>
                            较上年 +18.9%
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">在研项目</p>
                        <p class="text-2xl font-bold text-gray-900">1,263</p>
                        <p class="text-blue-600 text-xs">
                            <i class="fas fa-cog mr-1"></i>
                            执行中项目
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-cogs text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-xl p-6 border border-gray-200 hover-lift">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm mb-1">预警项目</p>
                        <p class="text-2xl font-bold text-gray-900">47</p>
                        <p class="text-red-600 text-xs">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            需要关注
                        </p>
                    </div>
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要功能模块 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- 项目总览模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-bar text-blue-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-900">项目总览</h3>
                                <p class="text-sm text-gray-600">Project Overview</p>
                            </div>
                        </div>
                        <button onclick="window.location.href='project-overview.html'" 
                                class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                            <i class="fas fa-arrow-right mr-2"></i>
                            进入模块
                        </button>
                    </div>
                    
                    <!-- 项目类型分布预览 -->
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">科技计划项目</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium">1,456项</span>
                                <div class="w-20 h-2 bg-gray-200 rounded-full">
                                    <div class="w-16 h-2 bg-blue-500 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">自研项目</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium">892项</span>
                                <div class="w-20 h-2 bg-gray-200 rounded-full">
                                    <div class="w-12 h-2 bg-green-500 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">高新投资项目</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-sm font-medium">499项</span>
                                <div class="w-20 h-2 bg-gray-200 rounded-full">
                                    <div class="w-8 h-2 bg-purple-500 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 快速功能入口 -->
                    <div class="grid grid-cols-2 gap-3">
                        <button onclick="window.location.href='field-distribution.html'" 
                                class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-industry text-blue-500 text-lg mb-1"></i>
                            <p class="text-xs text-gray-700">领域分布</p>
                        </button>
                        <button onclick="window.location.href='project-funding.html'" 
                                class="bg-gray-50 hover:bg-gray-100 p-3 rounded-lg text-center transition-colors">
                            <i class="fas fa-dollar-sign text-green-500 text-lg mb-1"></i>
                            <p class="text-xs text-gray-700">项目经费</p>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 项目预警模块 -->
            <div class="bg-white rounded-xl border border-gray-200 overflow-hidden hover-lift">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-4">
                            <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="font-bold text-gray-900">项目预警</h3>
                                <p class="text-sm text-gray-600">Project Alert</p>
                            </div>
                        </div>
                        <button onclick="window.location.href='project-alert.html'" 
                                class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                            <i class="fas fa-arrow-right mr-2"></i>
                            进入模块
                        </button>
                    </div>
                    
                    <!-- 预警统计 -->
                    <div class="space-y-4 mb-6">
                        <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-exclamation-circle text-red-500"></i>
                                <span class="text-sm font-medium text-gray-900">信用预警</span>
                            </div>
                            <span class="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full font-medium">23项</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-user-shield text-yellow-500"></i>
                                <span class="text-sm font-medium text-gray-900">科研诚信预警</span>
                            </div>
                            <span class="bg-yellow-100 text-yellow-600 text-xs px-2 py-1 rounded-full font-medium">15项</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-clock text-orange-500"></i>
                                <span class="text-sm font-medium text-gray-900">验收预警</span>
                            </div>
                            <span class="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full font-medium">9项</span>
                        </div>
                    </div>
                    
                    <!-- 最新预警 -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <p class="text-xs text-gray-600 mb-2">最新预警</p>
                        <div class="space-y-2">
                            <div class="text-xs text-red-600">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                宁波新材料科技：信用异常
                            </div>
                            <div class="text-xs text-yellow-600">
                                <i class="fas fa-clock mr-1"></i>
                                智能制造项目：验收延期
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目趋势分析 -->
        <div class="bg-white rounded-xl border border-gray-200 p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h3 class="font-bold text-gray-900 flex items-center">
                    <i class="fas fa-chart-line text-indigo-500 mr-2"></i>
                    项目趋势分析
                </h3>
                <div class="flex space-x-2">
                    <button class="text-xs bg-indigo-500 text-white px-3 py-1 rounded-lg">年度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">季度</button>
                    <button class="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-lg hover:bg-gray-200 transition-colors">月度</button>
                </div>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 项目数量趋势 -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-3">年度立项项目数变化</h4>
                    <div class="h-48 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-line text-3xl mb-2"></i>
                            <p class="text-sm">项目数量趋势图</p>
                            <p class="text-xs">2019-2024年度数据</p>
                        </div>
                    </div>
                </div>
                <!-- 项目金额趋势 -->
                <div>
                    <h4 class="text-sm font-medium text-gray-700 mb-3">年度项目金额变化</h4>
                    <div class="h-48 bg-gradient-to-r from-green-50 to-green-100 rounded-lg chart-placeholder flex items-center justify-center">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-chart-bar text-3xl mb-2"></i>
                            <p class="text-sm">项目金额趋势图</p>
                            <p class="text-xs">投资规模变化分析</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-4 text-center">
                <button onclick="window.location.href='project-trends.html'" 
                        class="text-indigo-500 hover:text-indigo-700 text-sm font-medium">
                    查看详细趋势分析 <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </div>

        <!-- 快速导航面板 -->
        <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <button onclick="window.location.href='project-overview.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-chart-bar text-blue-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">项目总览</p>
                <p class="text-xs text-gray-600">全面统计分析</p>
            </button>
            <button onclick="window.location.href='project-alert.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-exclamation-triangle text-red-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">项目预警</p>
                <p class="text-xs text-gray-600">风险监控提醒</p>
            </button>
            <button onclick="window.location.href='field-distribution.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-industry text-indigo-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">领域分布</p>
                <p class="text-xs text-gray-600">产业技术分析</p>
            </button>
            <button onclick="window.location.href='project-funding.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-dollar-sign text-green-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">项目经费</p>
                <p class="text-xs text-gray-600">资金流向统计</p>
            </button>
            <button onclick="window.location.href='project-trends.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-chart-line text-purple-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">趋势分析</p>
                <p class="text-xs text-gray-600">时间序列对比</p>
            </button>
            <button onclick="window.location.href='region-distribution.html'" 
                    class="bg-white rounded-lg p-4 border border-gray-200 hover-lift text-center">
                <i class="fas fa-map text-cyan-500 text-2xl mb-2"></i>
                <p class="text-sm font-medium text-gray-900">区域分布</p>
                <p class="text-xs text-gray-600">地理位置分析</p>
            </button>
        </div>

        <!-- 底部信息 -->
        <div class="mt-12 text-center text-gray-500 text-sm">
            <p>© 2024 项目检测大屏系统 - 智能化项目监测与管理</p>
            <p class="mt-1">最后更新：2024年1月15日 16:30 | 数据来源：科技项目管理平台</p>
        </div>
    </div>

    <script>
        // 页面加载动画和交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 添加悬停效果
            const cards = document.querySelectorAll('.hover-lift');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 统计数字动画效果
            const animateNumbers = () => {
                const numbers = [
                    { element: document.querySelector('[data-number="2847"]'), target: 2847 },
                    { element: document.querySelector('[data-number="156.8"]'), target: 156.8 },
                    { element: document.querySelector('[data-number="1263"]'), target: 1263 },
                    { element: document.querySelector('[data-number="47"]'), target: 47 }
                ];

                numbers.forEach(({ element, target }) => {
                    if (element) {
                        let current = 0;
                        const increment = target / 50;
                        const timer = setInterval(() => {
                            current += increment;
                            if (current >= target) {
                                if (target === 156.8) {
                                    element.textContent = target.toFixed(1) + '亿';
                                } else {
                                    element.textContent = Math.round(target).toLocaleString();
                                }
                                clearInterval(timer);
                            } else {
                                if (target === 156.8) {
                                    element.textContent = current.toFixed(1) + '亿';
                                } else {
                                    element.textContent = Math.floor(current).toLocaleString();
                                }
                            }
                        }, 30);
                    }
                });
            };

            // 延迟执行数字动画
            setTimeout(animateNumbers, 500);
        });
    </script>
</body>
</html> 