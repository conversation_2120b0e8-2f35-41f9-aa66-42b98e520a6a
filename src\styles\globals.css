@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 214 100% 48%;
    --primary-foreground: 210 40% 98%;

    --secondary: 214 100% 96%;
    --secondary-foreground: 214 100% 48%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 214 100% 48%;

    --radius: 0.5rem;
  }
}

/* 自定义样式 */
.btn-primary {
  @apply bg-[#1664FF] text-white hover:bg-[#0052FF];
}

.table-header {
  @apply bg-[#F8FAFC] text-sm font-medium text-gray-600;
}

.card-header {
  @apply border-b border-[#E5E9EF] pb-4;
}

.status-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.status-active {
  @apply bg-[#E8F3FF] text-[#1664FF];
}

.status-pending {
  @apply bg-[#FFF7E6] text-[#D46B08];
}

.recharts-default-tooltip {
  background-color: white !important;
  border: 1px solid #E5E9EF !important;
  border-radius: 6px !important;
  padding: 8px 12px !important;
}

.card-stats {
  background: linear-gradient(to right, #1664FF, #0052FF);
}

.card-stats-secondary {
  background: linear-gradient(to right, #00C49F, #00A3FF);
}

.table-row-hover:hover {
  background-color: #F8FAFC;
}

.status-dot {
  @apply w-2 h-2 rounded-full inline-block mr-2;
}

.status-dot-active {
  @apply bg-[#00C49F];
}

.status-dot-pending {
  @apply bg-[#FFBB28];
} 