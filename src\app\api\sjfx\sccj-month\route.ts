import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'

function getMonthRange(year: string, month: string): { begin: string; end: string } {
  const y = parseInt(year, 10)
  const m = parseInt(month, 10)
  const begin = `${year}-${month}-01`
  const endDate = new Date(y, m, 0) // month is 1-based here because JS Date uses month index next month day 0
  const end = `${year}-${month}-${String(endDate.getDate()).padStart(2, '0')}`
  return { begin, end }
}

// 月度范围内的生产车间分解统计
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const year = searchParams.get('year') || '2025'
    const month = searchParams.get('month') || '05'
    const kh = searchParams.get('kh') || 'J B'
    const debug = searchParams.get('debug') === '1'
    const { begin, end } = getMonthRange(year, month)

    const sql = `
      SELECT b.ddksddlb AS Sccj_Xl2,
             b.dds       AS zj_xl2,
             SUM(CASE WHEN a.norm = '1车间' THEN a.dds ELSE 0 END) AS sccj1,
             SUM(CASE WHEN a.norm = '2 水琴' THEN a.dds ELSE 0 END) AS sccj2,
             SUM(CASE WHEN a.norm = '3 亚平' THEN a.dds ELSE 0 END) AS sccj3,
             SUM(CASE WHEN a.norm = '6车间' THEN a.dds ELSE 0 END) AS sccj4,
             SUM(CASE WHEN a.norm = '7车间' THEN a.dds ELSE 0 END) AS sccj5,
             SUM(CASE WHEN a.norm = '其他' THEN a.dds ELSE 0 END)   AS sccj6
      FROM (
        SELECT SUM(xx_scdd.dds) AS dds,
               CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks) AS ddksddlb
        FROM xx_scdd
        JOIN cc_sj_scdj ON cc_sj_scdj.scid = xx_scdd.scid
        WHERE xx_scdd.kh = ? AND xx_scdd.xdrq >= ? AND xx_scdd.xdrq <= ?
        GROUP BY CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks)
        ORDER BY dds DESC
        LIMIT 99
      ) b
      LEFT JOIN (
        SELECT CONCAT(xx_scdd.ddlb,' ',xx_scdd.ddml,' ',xx_scdd.ddks) AS ddksddlb,
               SUM(xx_scdd.dds) AS dds,
               CASE
                 WHEN REPLACE(xx_scdd.sccj,' ','') = '1车间' THEN '1车间'
                 WHEN xx_scdd.sccj = '2 水琴' THEN '2 水琴'
                 WHEN xx_scdd.sccj = '3 亚平' THEN '3 亚平'
                 WHEN REPLACE(xx_scdd.sccj,' ','') = '6车间' THEN '6车间'
                 WHEN REPLACE(xx_scdd.sccj,' ','') = '7车间' THEN '7车间'
                 ELSE '其他'
               END AS norm
        FROM xx_scdd
        JOIN cc_sj_scdj ON cc_sj_scdj.scid = xx_scdd.scid
        WHERE xx_scdd.kh = ? AND xx_scdd.xdrq >= ? AND xx_scdd.xdrq <= ?
        GROUP BY ddksddlb, norm
      ) a ON a.ddksddlb = b.ddksddlb
      GROUP BY b.ddksddlb, b.dds
      ORDER BY b.dds DESC
    `

    const rows = await query(sql, [kh, begin, end, kh, begin, end])
    if (debug) {
      console.log('[API sccj-month] year=', year, 'month=', month, 'kh=', kh, 'rows=', rows.length)
      if (rows.length > 0) {
        console.log('[API sccj-month] sample=', rows[0])
      }
    }
    return NextResponse.json({ success: true, data: rows, meta: debug ? { rows: rows.length, year, month, kh } : undefined })
  } catch (e: any) {
    return NextResponse.json({ success: false, error: e?.message || '查询失败' }, { status: 500 })
  }
}


