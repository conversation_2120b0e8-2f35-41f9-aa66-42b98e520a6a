<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技特派员工作自评总结填报流程</text>

  <!-- 阶段一：任务发起与通知 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：任务发起与通知</text>
  
  <!-- 节点1: 管理员发起 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">管理员发起任务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">按年度和批次发起自评</text>
  </g>

  <!-- 节点2: 系统生成名单 -->
  <g transform="translate(600, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统生成名单</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">自动生成自评名单</text>
  </g>

  <!-- 节点3: 推送通知 -->
  <g transform="translate(900, 130)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送填报通知</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">通知用户填报任务</text>
  </g>

  <!-- 连接线 阶段一 -->
  <path d="M 500 165 Q 550 165 600 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 165 Q 850 165 900 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：用户填报与校验 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：用户填报与校验</text>

  <!-- 节点4: 任务筛选 -->
  <g transform="translate(100, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">任务列表筛选</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">筛选本年度填报任务</text>
  </g>

  <!-- 节点5: 表单填报 -->
  <g transform="translate(400, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">自评表单填报</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">填写内容与上传材料</text>
  </g>

  <!-- 节点6: 草稿保存 -->
  <g transform="translate(700, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">草稿保存</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">多次保存与实时校验</text>
  </g>

  <!-- 节点7: 正式提交 -->
  <g transform="translate(1000, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">正式提交</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">提交并锁定内容</text>
  </g>

  <!-- 连接线 阶段二 -->
  <path d="M 1000 200 C 1000 250, 200 280, 200 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 300 355 Q 350 355 400 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 600 355 Q 650 355 700 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 900 355 Q 950 355 1000 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 草稿循环线 -->
  <path d="M 800 320 C 850 300, 850 280, 800 280 C 750 280, 650 280, 600 280 C 550 280, 500 300, 500 320" stroke="#A5D6A7" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="650" y="270" text-anchor="middle" font-size="12" fill="#A5D6A7">多次完善</text>

  <!-- 阶段三：管理员审核与反馈 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：管理员审核与反馈</text>

  <!-- 节点8: 后台审核 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">后台分批审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">查看导出与评价</text>
  </g>

  <!-- 节点9: 退回补全 -->
  <g transform="translate(500, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">退回补全</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">问题反馈与通知</text>
  </g>

  <!-- 节点10: 用户补充 -->
  <g transform="translate(800, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户补充完善</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">完善资料再次提交</text>
  </g>

  <!-- 连接线 阶段三 -->
  <path d="M 1100 390 C 1100 450, 300 480, 300 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 400 555 Q 450 555 500 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 700 555 Q 750 555 800 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 迭代循环线 -->
  <path d="M 900 520 C 950 500, 950 450, 900 450 C 850 450, 800 450, 750 450 C 700 450, 650 450, 600 450 C 550 450, 500 450, 450 450 C 400 450, 350 450, 300 450 C 250 450, 200 450, 150 450 C 100 450, 50 450, 50 500 C 50 550, 100 590, 200 590" stroke="#CE93D8" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="500" y="440" text-anchor="middle" font-size="12" fill="#CE93D8">多次迭代直至通过</text>

  <!-- 阶段四：归档与应用 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：归档与应用</text>

  <!-- 节点11: 系统归档 -->
  <g transform="translate(300, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统自动归档</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态更新与材料归档</text>
  </g>

  <!-- 节点12: 数据应用 -->
  <g transform="translate(600, 720)" filter="url(#soft-shadow)">
    <rect width="400" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="18" font-weight="600" fill="#333">数据查询导出与应用</text>
    <text x="200" y="60" text-anchor="middle" font-size="13" fill="#555">
      <tspan dx="-120">随时查询</tspan>
      <tspan dx="40">数据导出</tspan>
      <tspan dx="40">业务分析</tspan>
      <tspan dx="40">经验总结</tspan>
    </text>
  </g>

  <!-- 连接线 到归档 -->
  <path d="M 300 590 C 300 650, 400 680, 400 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 500 755 Q 550 755 600 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线：从数据应用回到任务发起 -->
  <path d="M 1000 760 C 1050 760, 1300 760, 1300 400 C 1300 200, 1200 150, 1100 165" stroke="#FFE082" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1300" y="480" text-anchor="middle" font-size="12" fill="#FFE082" transform="rotate(-90, 1300, 480)">经验反馈</text>

</svg>