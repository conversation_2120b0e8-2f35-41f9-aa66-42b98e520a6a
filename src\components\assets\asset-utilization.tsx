'use client'

import * as React from "react"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart2,
  Cpu,
  HardDrive,
  Network,
  CircleAlert,
  ChevronUp as Arrow<PERSON><PERSON>,
  ChevronDown as ArrowDown
} from "lucide-react"

export function AssetUtilization() {
  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>资产利用率分析</CardTitle>
          <Badge variant="outline">实时监控</Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* 总体利用率 */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center gap-2">
                <BarChart2 className="h-5 w-5 text-blue-500" />
                <span className="font-medium">总体利用率</span>
              </div>
              <div className="flex items-center gap-1 text-green-600">
                <ArrowUp className="h-4 w-4" />
                <span>5.2%</span>
              </div>
            </div>
            <div className="text-2xl font-semibold text-blue-600">76.8%</div>
            <div className="mt-2 w-full h-2 bg-blue-100 rounded-full">
              <div className="h-full w-[76.8%] bg-blue-500 rounded-full"></div>
            </div>
          </div>

          {/* 关键指标 */}
          <div className="space-y-4">
            {/* CPU利用率 */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-purple-50 rounded-lg">
                  <Cpu className="h-4 w-4 text-purple-500" />
                </div>
                <div>
                  <div className="font-medium">CPU利用率</div>
                  <div className="text-sm text-gray-500">平均负载: 85%</div>
                </div>
              </div>
              <Badge variant="outline" className="bg-red-50 text-red-600">偏高</Badge>
            </div>

            {/* 内存使用率 - 使用 BarChart2 替代 */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 rounded-lg">
                  <BarChart2 className="h-4 w-4 text-blue-500" />
                </div>
                <div>
                  <div className="font-medium">内存使用率</div>
                  <div className="text-sm text-gray-500">已用: 24.5GB/32GB</div>
                </div>
              </div>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-600">注意</Badge>
            </div>

            {/* 存储空间 */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-50 rounded-lg">
                  <HardDrive className="h-4 w-4 text-green-500" />
                </div>
                <div>
                  <div className="font-medium">存储空间</div>
                  <div className="text-sm text-gray-500">剩余: 2.1TB/10TB</div>
                </div>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-600">正常</Badge>
            </div>

            {/* 网络带宽 */}
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-50 rounded-lg">
                  <Network className="h-4 w-4 text-orange-500" />
                </div>
                <div>
                  <div className="font-medium">网络带宽</div>
                  <div className="text-sm text-gray-500">使用率: 65%</div>
                </div>
              </div>
              <Badge variant="outline" className="bg-green-50 text-green-600">正常</Badge>
            </div>
          </div>

          {/* 性能优化建议 */}
          <div className="p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <CircleAlert className="h-4 w-4 text-yellow-500" />
              <span className="font-medium">性能优化建议</span>
            </div>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <span className="w-1 h-1 bg-yellow-500 rounded-full"></span>
                <span>建议对高负载服务器进行负载均衡</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-1 h-1 bg-yellow-500 rounded-full"></span>
                <span>考虑扩充内存容量至64GB</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-1 h-1 bg-yellow-500 rounded-full"></span>
                <span>优化数据库查询性能</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 