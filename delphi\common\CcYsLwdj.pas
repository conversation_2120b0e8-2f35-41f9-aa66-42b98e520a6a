unit CcYsLwdj;

interface
uses
  Classes;

type
  TCcYsLwdj = class
  private
    FYslwdjid: Integer;
    FYsid: Integer;
    FTgs: Double;
    FPbj: Double;
    FLwcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;
  public
    property Yslwdjid: integer read FYslwdjid write FYslwdjid;
    property Ysid: integer read FYsid write FYsid;
    property Tgs: double read FTgs write FTgs;
    property Pbj: double read FPbj write FPbj;
    property Lwcb: double read FLwcb write FLwcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read <PERSON>arket<PERSON>ser write FMarketU<PERSON>;
    property State: string read FState write FState;
  end;
implementation

end.

