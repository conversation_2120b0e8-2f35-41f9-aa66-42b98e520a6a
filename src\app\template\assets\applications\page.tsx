'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"

const applications = [
  {
    id: "APP001",
    name: "资源管理系统",
    type: "业务系统",
    department: "运维部",
    status: "运行中",
    version: "v2.1.0",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP002",
    name: "人力资源系统",
    type: "业务系统",
    department: "人事部",
    status: "运行中",
    version: "v3.0.2",
    lastUpdate: "2024-03-09",
  },
  {
    id: "APP003",
    name: "财务管理平台",
    type: "业务系统",
    department: "财务部",
    status: "维护中",
    version: "v2.5.1",
    lastUpdate: "2024-03-08",
  },
  {
    id: "APP004",
    name: "客户关系管理系统",
    type: "业务系统",
    department: "销售部",
    status: "运行中",
    version: "v4.1.0",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP005",
    name: "监控告警平台",
    type: "运维工具",
    department: "运维部",
    status: "运行中",
    version: "v1.9.5",
    lastUpdate: "2024-03-09",
  },
  {
    id: "APP006",
    name: "数据分析平台",
    type: "分析工具",
    department: "数据部",
    status: "运行中",
    version: "v3.2.0",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP007",
    name: "项目管理系统",
    type: "业务系统",
    department: "项目部",
    status: "维护中",
    version: "v2.8.3",
    lastUpdate: "2024-03-07",
  },
  {
    id: "APP008",
    name: "知识库系统",
    type: "支撑系统",
    department: "技术部",
    status: "运行中",
    version: "v1.5.2",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP009",
    name: "办公自动化系统",
    type: "业务系统",
    department: "行政部",
    status: "运行中",
    version: "v4.0.1",
    lastUpdate: "2024-03-09",
  },
  {
    id: "APP010",
    name: "代码仓库管理",
    type: "开发工具",
    department: "研发部",
    status: "运行中",
    version: "v2.2.4",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP011",
    name: "API网关服务",
    type: "基础服务",
    department: "研发部",
    status: "运行中",
    version: "v3.1.0",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP012",
    name: "消息队列服务",
    type: "基础服务",
    department: "研发部",
    status: "运行中",
    version: "v2.4.6",
    lastUpdate: "2024-03-09",
  },
  {
    id: "APP013",
    name: "日志分析平台",
    type: "运维工具",
    department: "运维部",
    status: "维护中",
    version: "v1.8.3",
    lastUpdate: "2024-03-08",
  },
  {
    id: "APP014",
    name: "容器编排平台",
    type: "基础服务",
    department: "运维部",
    status: "运行中",
    version: "v3.5.2",
    lastUpdate: "2024-03-10",
  },
  {
    id: "APP015",
    name: "配置中心服务",
    type: "基础服务",
    department: "研发部",
    status: "运行中",
    version: "v2.0.8",
    lastUpdate: "2024-03-09",
  }
]

export default function ApplicationsPage() {
  return (
    <div className="flex-1 space-y-6 p-8 bg-[#F8FAFC]">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-medium text-gray-900">应用资产</h1>
      </div>

      <Card className="border-[#E5E9EF] shadow-sm">
        <CardHeader className="border-b border-[#E5E9EF]">
          <CardTitle className="text-lg font-medium">应用清单</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-[#F8FAFC] hover:bg-[#F8FAFC]">
                <TableHead className="text-[#666]">应用编号</TableHead>
                <TableHead className="text-[#666]">应用名称</TableHead>
                <TableHead className="text-[#666]">应用类型</TableHead>
                <TableHead className="text-[#666]">所属部门</TableHead>
                <TableHead className="text-[#666]">运行状态</TableHead>
                <TableHead className="text-[#666]">版本</TableHead>
                <TableHead className="text-[#666]">最后更新</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {applications.map((app) => (
                <TableRow key={app.id} className="hover:bg-[#F0F7FF]">
                  <TableCell>{app.id}</TableCell>
                  <TableCell>{app.name}</TableCell>
                  <TableCell>{app.type}</TableCell>
                  <TableCell>{app.department}</TableCell>
                  <TableCell>
                    <Badge 
                      className={
                        app.status === "运行中" 
                          ? "bg-[#E8F3FF] text-[#1664FF]" 
                          : "bg-[#FFF7E6] text-[#D46B08]"
                      }
                    >
                      {app.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{app.version}</TableCell>
                  <TableCell>{app.lastUpdate}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
} 