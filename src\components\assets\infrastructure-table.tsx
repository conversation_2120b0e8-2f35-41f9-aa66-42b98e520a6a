'use client'

import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Server, 
  HardDrive, 
  Network, 
  Shield,
  MoreVertical,
  Eye,
  Edit,
  History,
  Terminal,
  AlertTriangle,
  Power,
  Settings,
  Trash2
} from "lucide-react"

// 状态标签配置
const statusConfig = {
  online: { label: '在线', class: 'bg-green-50 text-green-600' },
  offline: { label: '离线', class: 'bg-gray-50 text-gray-600' },
  warning: { label: '告警', class: 'bg-yellow-50 text-yellow-600' },
  error: { label: '故障', class: 'bg-red-50 text-red-600' },
  maintenance: { label: '维护中', class: 'bg-blue-50 text-blue-600' }
}

// 设备类型图标配置
const deviceIcons = {
  server: Server,
  storage: HardDrive,
  network: Network,
  security: Shield
}

// 模拟数据
const infrastructureData = [
  {
    id: 'SRV-001',
    name: '核心应用服务器-01',
    type: 'server',
    model: 'Dell PowerEdge R750',
    ip: '***********01',
    location: '主机房A区-R12',
    status: 'online',
    cpu: '75%',
    memory: '82%',
    uptime: '124天',
    lastMaintenance: '2024-01-15',
    healthScore: 92
  },
  {
    id: 'STR-002',
    name: '企业存储阵列-01',
    type: 'storage',
    model: 'NetApp AFF A400',
    ip: '***********02',
    location: '主机房B区-R05',
    status: 'warning',
    capacity: '85%',
    iops: '15.2K',
    uptime: '89天',
    lastMaintenance: '2024-02-01',
    healthScore: 78
  },
  {
    id: 'NET-003',
    name: '核心交换机-01',
    type: 'network',
    model: 'Cisco Nexus 9300',
    ip: '***********',
    location: '主机房A区-N01',
    status: 'online',
    bandwidth: '45%',
    packets: '1.2M/s',
    uptime: '156天',
    lastMaintenance: '2024-01-10',
    healthScore: 95
  },
  {
    id: 'SEC-004',
    name: '防火墙-01',
    type: 'security',
    model: 'Palo Alto PA-5250',
    ip: '*************',
    location: '主机房A区-S01',
    status: 'error',
    throughput: '8.5Gbps',
    connections: '1.5M',
    uptime: '45天',
    lastMaintenance: '2024-02-15',
    healthScore: 65
  },
  // ... 可以添加更多数据
]

export function InfrastructureTable() {
  return (
    <div className="space-y-4">
      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[30px]">
                <Checkbox />
              </TableHead>
              <TableHead className="w-[180px]">设备信息</TableHead>
              <TableHead className="w-[120px]">位置</TableHead>
              <TableHead className="w-[100px]">状态</TableHead>
              <TableHead className="w-[200px]">性能指标</TableHead>
              <TableHead className="w-[120px]">运行时间</TableHead>
              <TableHead className="w-[100px]">健康分</TableHead>
              <TableHead className="w-[100px]">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {infrastructureData.map((device) => {
              const DeviceIcon = deviceIcons[device.type]
              const status = statusConfig[device.status]
              
              return (
                <TableRow key={device.id}>
                  <TableCell>
                    <Checkbox />
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${
                        device.type === 'server' ? 'bg-blue-50' :
                        device.type === 'storage' ? 'bg-purple-50' :
                        device.type === 'network' ? 'bg-green-50' :
                        'bg-orange-50'
                      }`}>
                        <DeviceIcon className={`h-4 w-4 ${
                          device.type === 'server' ? 'text-blue-500' :
                          device.type === 'storage' ? 'text-purple-500' :
                          device.type === 'network' ? 'text-green-500' :
                          'text-orange-500'
                        }`} />
                      </div>
                      <div>
                        <div className="font-medium">{device.name}</div>
                        <div className="text-sm text-gray-500">
                          {device.id} · {device.ip}
                        </div>
                        <div className="text-xs text-gray-400">{device.model}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{device.location}</div>
                  </TableCell>
                  <TableCell>
                    <Badge className={status.class}>
                      {status.label}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {device.type === 'server' && (
                        <>
                          <div className="flex items-center justify-between text-sm">
                            <span>CPU</span>
                            <span className={device.cpu.replace('%', '') > 80 ? 'text-yellow-600' : 'text-gray-600'}>
                              {device.cpu}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>内存</span>
                            <span className={device.memory.replace('%', '') > 80 ? 'text-yellow-600' : 'text-gray-600'}>
                              {device.memory}
                            </span>
                          </div>
                        </>
                      )}
                      {device.type === 'storage' && (
                        <>
                          <div className="flex items-center justify-between text-sm">
                            <span>容量</span>
                            <span className={device.capacity.replace('%', '') > 80 ? 'text-yellow-600' : 'text-gray-600'}>
                              {device.capacity}
                            </span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>IOPS</span>
                            <span className="text-gray-600">{device.iops}</span>
                          </div>
                        </>
                      )}
                      {device.type === 'network' && (
                        <>
                          <div className="flex items-center justify-between text-sm">
                            <span>带宽</span>
                            <span className="text-gray-600">{device.bandwidth}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>包量</span>
                            <span className="text-gray-600">{device.packets}</span>
                          </div>
                        </>
                      )}
                      {device.type === 'security' && (
                        <>
                          <div className="flex items-center justify-between text-sm">
                            <span>吞吐量</span>
                            <span className="text-gray-600">{device.throughput}</span>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>连接数</span>
                            <span className="text-gray-600">{device.connections}</span>
                          </div>
                        </>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">{device.uptime}</div>
                    <div className="text-xs text-gray-500">
                      上次维护: {device.lastMaintenance}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className={`text-sm font-medium ${
                      device.healthScore >= 90 ? 'text-green-600' :
                      device.healthScore >= 70 ? 'text-yellow-600' :
                      'text-red-600'
                    }`}>
                      {device.healthScore}分
                    </div>
                    <div className="w-full h-1.5 bg-gray-100 rounded-full mt-1">
                      <div 
                        className={`h-full rounded-full ${
                          device.healthScore >= 90 ? 'bg-green-500' :
                          device.healthScore >= 70 ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}
                        style={{ width: `${device.healthScore}%` }}
                      />
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[160px]">
                        <DropdownMenuItem>
                          <Eye className="h-4 w-4 mr-2" />
                          查看详情
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Terminal className="h-4 w-4 mr-2" />
                          远程连接
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <History className="h-4 w-4 mr-2" />
                          查看日志
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑信息
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Settings className="h-4 w-4 mr-2" />
                          配置管理
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Power className="h-4 w-4 mr-2" />
                          重启设备
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除设备
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </div>

      {/* 分页控件 */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-500">
          共 1,286 条记录
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" disabled>
            上一页
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            1
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            2
          </Button>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            3
          </Button>
          <span className="mx-2">...</span>
          <Button variant="outline" size="sm" className="min-w-[40px]">
            42
          </Button>
          <Button variant="outline" size="sm">
            下一页
          </Button>
        </div>
      </div>
    </div>
  )
} 