<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>成果展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">成果展示</h1>
                    <p class="mt-2 text-sm text-gray-600">集中呈现专利、商标等自主知识产权及相关成果的全生命周期状态</p>
                </div>
                <div class="flex space-x-3">
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出数据
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 成果概览区 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-blue-100 rounded-md flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">专利总数</p>
                        <p class="text-2xl font-semibold text-gray-900">1,245</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-green-100 rounded-md flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">有效专利数</p>
                        <p class="text-2xl font-semibold text-gray-900">987</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-yellow-100 rounded-md flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">商标总数</p>
                        <p class="text-2xl font-semibold text-gray-900">562</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-purple-100 rounded-md flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">已转化成果数</p>
                        <p class="text-2xl font-semibold text-gray-900">156</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 专利申请与授权趋势图 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">专利申请与授权年度趋势</h3>
            <div class="h-64">
                <canvas id="patentTrendChart"></canvas>
            </div>
        </div>

        <!-- 条件筛选区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">条件筛选</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">成果类型</label>
                    <div class="flex flex-wrap gap-2">
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            专利
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2" checked>
                            商标
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            软件著作权
                        </label>
                        <label class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm">
                            <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 mr-2">
                            集成电路布图
                        </label>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">法律状态</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部状态</option>
                        <option value="pending">申请中</option>
                        <option value="authorized" selected>已授权</option>
                        <option value="valid">有效</option>
                        <option value="invalid">失效</option>
                        <option value="abandoned">放弃</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">技术领域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部领域</option>
                        <option value="ai" selected>人工智能</option>
                        <option value="biotech">生物医药</option>
                        <option value="newmaterial">新材料</option>
                        <option value="energy">新能源</option>
                        <option value="other">其他</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">申请年度范围</label>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="sr-only">开始年份</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="2019" selected>2019</option>
                                <option value="2018">2018</option>
                                <option value="2017">2017</option>
                                <option value="2016">2016</option>
                                <option value="2015">2015</option>
                            </select>
                        </div>
                        <div>
                            <label class="sr-only">结束年份</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="2023" selected>2023</option>
                                <option value="2022">2022</option>
                                <option value="2021">2021</option>
                                <option value="2020">2020</option>
                                <option value="2019">2019</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">权属人</label>
                    <input type="text" placeholder="请输入权属人名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">是否质押</label>
                    <div class="flex items-center space-x-4">
                        <label class="inline-flex items-center">
                            <input type="radio" name="pledged" value="all" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <span class="ml-2 text-sm text-gray-700">全部</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="pledged" value="yes" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">已质押</span>
                        </label>
                        <label class="inline-flex items-center">
                            <input type="radio" name="pledged" value="no" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">未质押</span>
                        </label>
                    </div>
                </div>
            </div>
            <div class="flex space-x-3 mt-6">
                <button class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    查询
                </button>
                <button class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    重置筛选
                </button>
            </div>
        </div>

        <!-- 成果列表与分析图表区 -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- 成果列表区 -->
            <div class="lg:col-span-3">
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">成果列表</h3>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-4">当前共 <span class="font-medium text-gray-900">156</span> 条成果</span>
                            <button class="text-sm text-blue-600 hover:text-blue-900 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">法律状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请号/注册号</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">申请日期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">授权日期</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">技术领域</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">转化状态</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">质押情况</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">一种基于深度学习的图像识别方法</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202010123456.7</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-02-25</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-06-18</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已转化</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">已质押</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('detailDrawer')" class="text-indigo-600 hover:text-indigo-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">宁波智慧医疗云平台</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">实用新型专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202120345678.9</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-02-18</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-08-05</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已转化</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未质押</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('detailDrawer')" class="text-indigo-600 hover:text-indigo-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">量子计算芯片散热装置</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">发明专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL201910567890.1</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2019-06-10</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-03-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未转化</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未质押</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('detailDrawer')" class="text-indigo-600 hover:text-indigo-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">智能语音交互系统</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">外观设计专利</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">ZL202230123456.7</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-03-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-09-08</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">人工智能</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已转化</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">已质押</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('detailDrawer')" class="text-indigo-600 hover:text-indigo-900">查看清册</button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">NINGBO TECH</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">商标</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已注册</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">第12345678号</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-05-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-12-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">品牌标识</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">已转化</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">未质押</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="openModal('detailDrawer')" class="text-indigo-600 hover:text-indigo-900">查看清册</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="px-6 py-4 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="text-sm text-gray-500">
                                显示第 1-5 条，共 156 条记录
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                                <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                                <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 成果分析图表区 -->
            <div class="lg:col-span-1 space-y-6">
                <!-- 授权率与注册率对比 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">授权率与注册率对比</h3>
                    <div class="h-64">
                        <canvas id="successRateChart"></canvas>
                    </div>
                </div>

                <!-- 技术领域成果分布 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">技术领域成果分布</h3>
                    <div class="h-64">
                        <canvas id="techFieldChart"></canvas>
                    </div>
                </div>

                <!-- 成果转化分析 -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-6">成果转化分析</h3>
                    <div class="h-64">
                        <canvas id="conversionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 清册下钻抽屉 -->
    <div id="detailDrawer" class="modal-overlay fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="absolute top-0 right-0 h-full w-full max-w-2xl bg-white shadow-xl transform transition-transform duration-300 translate-x-full" id="drawerContent">
            <div class="flex flex-col h-full">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">成果清册详情</h3>
                    <button onclick="closeModal('detailDrawer')" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="flex-1 overflow-y-auto p-6">
                    <div class="mb-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">成果基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-500">成果名称：</span>
                                <span class="font-medium text-gray-900">一种基于深度学习的图像识别方法</span>
                            </div>
                            <div>
                                <span class="text-gray-500">成果类型：</span>
                                <span class="font-medium text-gray-900">发明专利</span>
                            </div>
                            <div>
                                <span class="text-gray-500">申请号/注册号：</span>
                                <span class="font-medium text-gray-900">ZL202010123456.7</span>
                            </div>
                            <div>
                                <span class="text-gray-500">法律状态：</span>
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">已授权</span>
                            </div>
                            <div>
                                <span class="text-gray-500">申请日期：</span>
                                <span class="font-medium text-gray-900">2020-02-25</span>
                            </div>
                            <div>
                                <span class="text-gray-500">授权日期：</span>
                                <span class="font-medium text-gray-900">2022-06-18</span>
                            </div>
                            <div>
                                <span class="text-gray-500">技术领域：</span>
                                <span class="font-medium text-gray-900">人工智能</span>
                            </div>
                            <div>
                                <span class="text-gray-500">有效期至：</span>
                                <span class="font-medium text-gray-900">2040-02-24</span>
                            </div>
                            <div>
                                <span class="text-gray-500">申请人/注册人：</span>
                                <span class="font-medium text-gray-900">宁波人工智能研究院有限公司</span>
                            </div>
                            <div>
                                <span class="text-gray-500">发明人：</span>
                                <span class="font-medium text-gray-900">张明, 李华, 王芳</span>
                            </div>
                            <div>
                                <span class="text-gray-500">专利代理机构：</span>
                                <span class="font-medium text-gray-900">宁波智汇知识产权代理有限公司</span>
                            </div>
                            <div>
                                <span class="text-gray-500">代理人：</span>
                                <span class="font-medium text-gray-900">赵强</span>
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">年度分布</h4>
                        <div class="h-40">
                            <canvas id="annualDistributionChart"></canvas>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">法律状态变更记录</h4>
                        <div class="space-y-3">
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <span class="font-medium text-gray-900">授权</span>
                                        <p class="text-xs text-gray-500">专利正式获得授权</p>
                                    </div>
                                    <span class="text-xs text-gray-500">2022-06-18</span>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <span class="font-medium text-gray-900">实质审查生效</span>
                                        <p class="text-xs text-gray-500">进入实质审查阶段</p>
                                    </div>
                                    <span class="text-xs text-gray-500">2020-08-15</span>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <span class="font-medium text-gray-900">专利申请公布</span>
                                        <p class="text-xs text-gray-500">专利申请文件公开</p>
                                    </div>
                                    <span class="text-xs text-gray-500">2020-06-10</span>
                                </div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded-lg">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <span class="font-medium text-gray-900">专利申请受理</span>
                                        <p class="text-xs text-gray-500">专利申请被受理</p>
                                    </div>
                                    <span class="text-xs text-gray-500">2020-02-25</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-8">
                        <h4 class="text-md font-semibold text-gray-900 mb-4">质押与许可详情</h4>
                        <div class="space-y-4">
                            <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-100">
                                <h5 class="font-medium text-gray-900 mb-2">质押信息</h5>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                    <div>
                                        <span class="text-gray-500">质押登记号：</span>
                                        <span class="font-medium text-gray-900">2023990000123</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">出质人：</span>
                                        <span class="font-medium text-gray-900">宁波人工智能研究院有限公司</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">质权人：</span>
                                        <span class="font-medium text-gray-900">宁波银行科技支行</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">质押金额：</span>
                                        <span class="font-medium text-gray-900">500万元</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">登记日期：</span>
                                        <span class="font-medium text-gray-900">2023-03-15</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">状态：</span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                                <h5 class="font-medium text-gray-900 mb-2">许可信息</h5>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                                    <div>
                                        <span class="text-gray-500">被许可方：</span>
                                        <span class="font-medium text-gray-900">宁波智能科技有限公司</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">许可类型：</span>
                                        <span class="font-medium text-gray-900">独占许可</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">许可期限：</span>
                                        <span class="font-medium text-gray-900">2022-07-01 至 2032-06-30</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500">许可费用：</span>
                                        <span class="font-medium text-gray-900">300万元</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between">
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        返回列表定位
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        下载清册 PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 1. 全局UI控制函数 ---
        function openModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                document.body.style.overflow = 'hidden'; // 防止背景滚动
                
                // 如果是抽屉式弹窗，添加滑入动画
                if (modalId === 'detailDrawer') {
                    setTimeout(() => {
                        document.getElementById('drawerContent').classList.remove('translate-x-full');
                    }, 10);
                }
            }
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                // 如果是抽屉式弹窗，添加滑出动画
                if (modalId === 'detailDrawer') {
                    document.getElementById('drawerContent').classList.add('translate-x-full');
                    setTimeout(() => {
                        modal.classList.add('hidden');
                        document.body.style.overflow = 'auto'; // 恢复背景滚动
                    }, 300);
                } else {
                    modal.classList.add('hidden');
                    document.body.style.overflow = 'auto'; // 恢复背景滚动
                }
            }
        }

        // --- 2. DOM加载完毕后执行的初始化代码 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 专利申请与授权趋势图
            const patentTrendCtx = document.getElementById('patentTrendChart').getContext('2d');
            new Chart(patentTrendCtx, {
                type: 'bar',
                data: {
                    labels: ['2019', '2020', '2021', '2022', '2023'],
                    datasets: [
                        {
                            label: '申请量',
                            data: [185, 210, 245, 280, 325],
                            backgroundColor: 'rgba(59, 130, 246, 0.7)',
                            borderColor: 'rgba(59, 130, 246, 1)',
                            borderWidth: 1
                        },
                        {
                            label: '授权量',
                            data: [120, 150, 180, 210, 245],
                            backgroundColor: 'rgba(16, 185, 129, 0.7)',
                            borderColor: 'rgba(16, 185, 129, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // 授权率与注册率对比图
            const successRateCtx = document.getElementById('successRateChart').getContext('2d');
            new Chart(successRateCtx, {
                type: 'bar',
                data: {
                    labels: ['发明专利', '实用新型', '外观设计', '商标'],
                    datasets: [{
                        label: '成功率(%)',
                        data: [65, 92, 88, 95],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(139, 92, 246, 0.8)'
                        ],
                        borderColor: [
                            'rgba(59, 130, 246, 1)',
                            'rgba(16, 185, 129, 1)',
                            'rgba(245, 158, 11, 1)',
                            'rgba(139, 92, 246, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 技术领域成果分布图
            const techFieldCtx = document.getElementById('techFieldChart').getContext('2d');
            new Chart(techFieldCtx, {
                type: 'doughnut',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 15, 5],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(239, 68, 68, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // 成果转化分析图
            const conversionCtx = document.getElementById('conversionChart').getContext('2d');
            new Chart(conversionCtx, {
                type: 'radar',
                data: {
                    labels: ['人工智能', '生物医药', '新材料', '新能源', '其他'],
                    datasets: [
                        {
                            label: '成果数量',
                            data: [45, 35, 30, 25, 15],
                            backgroundColor: 'rgba(59, 130, 246, 0.2)',
                            borderColor: 'rgba(59, 130, 246, 0.8)',
                            pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgba(59, 130, 246, 1)'
                        },
                        {
                            label: '转化指数',
                            data: [80, 65, 70, 50, 30],
                            backgroundColor: 'rgba(16, 185, 129, 0.2)',
                            borderColor: 'rgba(16, 185, 129, 0.8)',
                            pointBackgroundColor: 'rgba(16, 185, 129, 1)',
                            pointBorderColor: '#fff',
                            pointHoverBackgroundColor: '#fff',
                            pointHoverBorderColor: 'rgba(16, 185, 129, 1)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            angleLines: {
                                display: true
                            },
                            suggestedMin: 0,
                            suggestedMax: 100
                        }
                    }
                }
            });

            // 清册抽屉中的年度分布图
            const annualDistributionCtx = document.getElementById('annualDistributionChart').getContext('2d');
            new Chart(annualDistributionCtx, {
                type: 'line',
                data: {
                    labels: ['2020', '2021', '2022', '2023'],
                    datasets: [{
                        label: '相关成果数量',
                        data: [5, 12, 28, 35],
                        fill: false,
                        borderColor: 'rgba(59, 130, 246, 1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 为所有弹窗绑定“点击外部关闭”事件
            const modals = document.querySelectorAll('.modal-overlay');
            modals.forEach(modal => {
                modal.addEventListener('click', function(event) {
                    // 确保点击的是蒙层本身，而不是弹窗内容
                    if (event.target === modal) {
                        closeModal(modal.id);
                    }
                });

                // 同时绑定“ESC键关闭”
                document.addEventListener('keydown', function (e) {
                    if (e.key === 'Escape' && !modal.classList.contains('hidden')) {
                        closeModal(modal.id);
                    }
                });
            });
            
            // 表单提交处理（用于原型）
            document.querySelectorAll('.modal-overlay form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    e.preventDefault(); // 阻止页面刷新
                    const parentModalId = form.closest('.modal-overlay').id;
                    alert('表单已提交 (原型演示)');
                    closeModal(parentModalId);
                });
            });
        });
    </script>
</body>
</html>