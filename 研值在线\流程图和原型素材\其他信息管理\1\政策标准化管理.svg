<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策标准化管理流程</text>

  <!-- 阶段一：数据标记与工单生成 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据标记与工单生成</text>
  
  <!-- 节点1: 新入库政策 -->
  <g transform="translate(250, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">新入库原始政策</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">系统每日自动检测</text>
  </g>

  <!-- 节点2: 标记未标准化 -->
  <g transform="translate(550, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标记未标准化</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">状态设置为待处理</text>
  </g>

  <!-- 节点3: 生成待办工单 -->
  <g transform="translate(850, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成待办工单</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">推送至管理人员</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 470 165 Q 500 165 550 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 770 165 Q 810 165 850 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：字段映射与规则校验 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：字段映射与规则校验</text>

  <!-- 节点4: 打开工作台 -->
  <g transform="translate(150, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">打开工作台</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">管理人员操作</text>
  </g>

  <!-- 节点5: 字段映射 -->
  <g transform="translate(400, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">字段映射绑定</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拖拽映射或文本绑定</text>
  </g>

  <!-- 节点6: 规则校验 -->
  <g transform="translate(650, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">执行规则校验</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">系统自动校验</text>
  </g>

  <!-- 节点7: 生成草稿 -->
  <g transform="translate(900, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成标准化草稿</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">进入待审核状态</text>
  </g>

  <!-- 连接线 工单 -> 工作台 -->
  <path d="M 960 200 C 960 250, 250 280, 250 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 4 -> 5 -->
  <path d="M 350 355 Q 375 355 400 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 5 -> 6 -->
  <path d="M 600 355 Q 625 355 650 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 6 -> 7 -->
  <path d="M 850 355 Q 875 355 900 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="875" y="345" text-anchor="middle" font-size="12" fill="#555">校验通过</text>

  <!-- 阶段三：审核与版本管理 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：审核与版本管理</text>

  <!-- 节点8: 待办审核 -->
  <g transform="translate(200, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">待办列表审核</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">核对映射与校验报告</text>
  </g>

  <!-- 节点9: 审核通过 -->
  <g transform="translate(450, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核通过</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">状态更新为已完成</text>
  </g>

  <!-- 节点10: 写入标准化库 -->
  <g transform="translate(700, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">写入标准化库</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">正式版本入库</text>
  </g>

  <!-- 节点11: 版本迭代 -->
  <g transform="translate(950, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">版本迭代管理</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">复制修改递增版本号</text>
  </g>

  <!-- 连接线 草稿 -> 审核 -->
  <path d="M 1000 390 C 1000 450, 300 490, 300 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 8 -> 9 -->
  <path d="M 400 555 Q 425 555 450 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 9 -> 10 -->
  <path d="M 650 555 Q 675 555 700 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 10 -> 11 -->
  <path d="M 900 555 Q 925 555 950 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 审核失败分支 -->
  <path d="M 300 590 C 200 650, 500 680, 500 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="350" y="650" text-anchor="middle" font-size="12" fill="#555">审核失败</text>

  <!-- 节点12: 退回修改 -->
  <g transform="translate(400, 720)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">退回修改</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">附加审核意见</text>
  </g>

  <!-- 退回到字段映射的循环 -->
  <path d="M 400 755 C 200 800, 100 400, 400 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />

  <!-- 阶段四：数据归档与同步 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据归档与同步</text>

  <!-- 节点13: 历史归档 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时归档</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">历史版本与校验日志</text>
  </g>

  <!-- 节点14: 数据同步 -->
  <g transform="translate(920, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">接口数据同步</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">向匹配度算法与推送模块同步</text>
  </g>

  <!-- 连接线 标准化库 -> 归档 -->
  <path d="M 800 590 C 800 650, 760 690, 760 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 归档 -> 同步 -->
  <path d="M 870 755 Q 895 755 920 755" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 版本迭代循环 -->
  <path d="M 1050 590 C 1200 650, 1200 400, 500 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="3,3" />
  <text x="1100" y="500" text-anchor="middle" font-size="12" fill="#555">版本迭代</text>

</svg>