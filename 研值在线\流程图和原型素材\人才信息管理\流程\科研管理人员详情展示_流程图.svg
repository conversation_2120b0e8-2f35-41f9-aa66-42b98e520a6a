<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科研管理人员详情展示流程图</text>

  <!-- 阶段一：用户请求与身份识别 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：用户请求与身份识别</text>
  
  <!-- 节点1: 用户选择检索 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户选择检索</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">人才管理模块目标选择</text>
  </g>

  <!-- 节点2: 系统加载ID -->
  <g transform="translate(880, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统加载ID</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">唯一标识与详情请求</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 520 165 Q 650 165, 880 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：基础信息聚合与校验 -->
  <text x="700" y="270" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：基础信息聚合与校验</text>

  <!-- 节点3: 数据源聚合 -->
  <g transform="translate(200, 300)" filter="url(#soft-shadow)">
    <rect width="240" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="120" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据聚合</text>
    <text x="120" y="55" text-anchor="middle" font-size="12" fill="#555">基础身份、学历、职称等</text>
  </g>

  <!-- 节点4: 实时校验 -->
  <g transform="translate(960, 300)" filter="url(#soft-shadow)">
    <rect width="240" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="120" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">实时状态校验</text>
    <text x="120" y="55" text-anchor="middle" font-size="12" fill="#555">最新信息验证更新</text>
  </g>

  <!-- 连接线 ID -> 数据聚合 -->
  <path d="M 880 200 C 750 230, 500 260, 320 300" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 数据聚合 -> 实时校验 -->
  <path d="M 440 335 Q 700 335, 960 335" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：成果关联与全景展示 -->
  <text x="700" y="440" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：成果关联与全景展示</text>

  <!-- 节点5: 成果库联动 -->
  <g transform="translate(150, 470)" filter="url(#soft-shadow)">
    <rect width="260" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="130" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">成果库自动检索</text>
    <text x="130" y="55" text-anchor="middle" font-size="12" fill="#555">专利、论文、项目、奖励</text>
  </g>

  <!-- 节点6: 活动记录展示 -->
  <g transform="translate(990, 470)" filter="url(#soft-shadow)">
    <rect width="260" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="130" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">活动记录同步</text>
    <text x="130" y="55" text-anchor="middle" font-size="12" fill="#555">参政咨政、培训、社会活动</text>
  </g>

  <!-- 节点7: 全景画像 -->
  <g transform="translate(500, 580)" filter="url(#soft-shadow)">
    <rect width="400" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="200" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">纵向全景画像生成</text>
    <text x="200" y="55" text-anchor="middle" font-size="12" fill="#555">多维度信息整合展示</text>
  </g>

  <!-- 连接线 校验 -> 成果库 -->
  <path d="M 960 370 C 800 400, 500 430, 280 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 校验 -> 活动记录 -->
  <path d="M 1080 370 Q 1120 420, 1120 470" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 成果库 -> 全景画像 -->
  <path d="M 410 505 Q 500 540, 500 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 活动记录 -> 全景画像 -->
  <path d="M 990 505 Q 900 540, 900 580" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：用户操作与数据同步 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：用户操作与数据同步</text>

  <!-- 节点8: 用户操作 -->
  <g transform="translate(200, 750)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户交互操作</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">浏览、筛选、钻取、导出</text>
  </g>

  <!-- 节点9: 跨系统同步 -->
  <g transform="translate(920, 750)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">跨系统数据同步</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">权限校验与一致性保障</text>
  </g>

  <!-- 连接线 全景画像 -> 用户操作 -->
  <path d="M 500 650 C 400 680, 380 720, 340 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 全景画像 -> 跨系统同步 -->
  <path d="M 900 650 C 1000 680, 1020 720, 1060 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：从跨系统同步回到数据聚合 -->
  <path d="M 920 785 C 100 850, 100 250, 200 335" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="100" y="600" text-anchor="middle" font-size="11" fill="#666">数据同步反馈</text>

  <!-- 反馈循环：从用户操作回到成果检索 -->
  <path d="M 200 785 C 50 600, 50 450, 150 505" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="50" y="650" text-anchor="middle" font-size="11" fill="#666">深度钻取</text>

</svg>