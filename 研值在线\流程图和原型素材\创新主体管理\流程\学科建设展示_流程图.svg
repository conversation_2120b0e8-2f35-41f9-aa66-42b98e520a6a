<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 900" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">学科建设展示流程</text>

  <!-- 阶段一：数据同步与处理 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：数据同步与处理</text>
  
  <!-- 节点1: 多源数据同步 -->
  <g transform="translate(150, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多源数据同步</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">医院系统与卫健委平台</text>
  </g>

  <!-- 节点2: 数据标准化 -->
  <g transform="translate(450, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据标准化</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">级别映射、阶段判定、去重</text>
  </g>

  <!-- 节点3: 统一学科建设库 -->
  <g transform="translate(750, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">统一学科建设库</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">标准化学科数据存储</text>
  </g>

  <!-- 连接线 1 -> 2 -> 3 -->
  <path d="M 370 165 Q 400 165 450 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 670 165 Q 700 165 750 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据聚合与缓存 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据聚合与缓存</text>

  <!-- 节点4: 级别聚合 -->
  <g transform="translate(200, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">级别聚合</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">国家、省、市级别统计</text>
  </g>

  <!-- 节点5: 可视化数据生成 -->
  <g transform="translate(500, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">可视化数据生成</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">概况卡片、图表、时间轴</text>
  </g>

  <!-- 节点6: 缓存写入 -->
  <g transform="translate(800, 310)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存写入</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">高性能数据访问</text>
  </g>

  <!-- 连接线 4 -> 5 -> 6 -->
  <path d="M 420 345 Q 450 345 500 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 720 345 Q 750 345 800 345" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：前端展示与交互 -->
  <text x="700" y="460" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：前端展示与交互</text>

  <!-- 节点7: 页面访问 -->
  <g transform="translate(100, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户访问页面</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">学科建设展示页面</text>
  </g>

  <!-- 节点8: 缓存接口调用 -->
  <g transform="translate(350, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">缓存接口调用</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">获取预处理数据</text>
  </g>

  <!-- 节点9: 多组件渲染 -->
  <g transform="translate(600, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">多组件渲染</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">概况、列表、图表、时间轴</text>
  </g>

  <!-- 节点10: 筛选交互 -->
  <g transform="translate(850, 490)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">筛选交互</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">级别点击、关键词搜索</text>
  </g>

  <!-- 连接线 7 -> 8 -> 9 -> 10 -->
  <path d="M 300 525 Q 325 525 350 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 525 Q 575 525 600 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 525 Q 825 525 850 525" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：详情查看与操作 -->
  <text x="700" y="640" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：详情查看与操作</text>

  <!-- 节点11: 学科详情 -->
  <g transform="translate(100, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">学科详情查看</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">侧滑页展示详细信息</text>
  </g>

  <!-- 节点12: 成果跳转 -->
  <g transform="translate(350, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">科研成果跳转</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">携带学科ID跳转</text>
  </g>

  <!-- 节点13: 数据导出 -->
  <g transform="translate(600, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">学科明细导出</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">Excel文件生成下载</text>
  </g>

  <!-- 节点14: 操作日志 -->
  <g transform="translate(850, 670)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">操作日志记录</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">行为分析与审计</text>
  </g>

  <!-- 连接线 11 -> 12 -> 13 -> 14 -->
  <path d="M 300 705 Q 325 705 350 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 550 705 Q 575 705 600 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <path d="M 800 705 Q 825 705 850 705" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 跨阶段连接线 -->
  <!-- 从统一学科建设库到级别聚合 -->
  <path d="M 860 200 C 860 240, 310 240, 310 310" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从缓存写入到页面访问 -->
  <path d="M 910 380 C 910 420, 200 420, 200 490" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 从筛选交互到学科详情 -->
  <path d="M 950 560 C 950 600, 200 600, 200 670" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环线 (虚线) -->
  <!-- 从筛选交互回到多组件渲染 -->
  <path d="M 950 560 C 950 580, 700 580, 700 560" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="825" y="575" font-size="11" fill="#666">数据刷新</text>

  <!-- 从操作日志回到缓存写入 -->
  <path d="M 950 740 C 950 780, 910 780, 910 380" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="930" y="795" font-size="11" fill="#666">定期同步</text>

  <!-- 从学科详情到操作日志 -->
  <path d="M 200 740 C 200 760, 950 760, 950 740" stroke="#666" stroke-width="1.5" fill="none" stroke-dasharray="5,5" marker-end="url(#arrowhead)" />
  <text x="575" y="775" font-size="11" fill="#666">查看与导出日志</text>

</svg>