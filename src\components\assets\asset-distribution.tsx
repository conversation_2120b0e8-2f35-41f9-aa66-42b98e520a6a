'use client'

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

const ReactECharts = dynamic(() => import('echarts-for-react'), { ssr: false })

export function AssetDistribution() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: ['服务器', '存储设备', '网络设备', '安全设备', '其他设备']
    },
    series: [
      {
        name: '资产分布',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        data: [
          { value: 456, name: '服务器', itemStyle: { color: '#2563EB' } },
          { value: 234, name: '存储设备', itemStyle: { color: '#10B981' } },
          { value: 189, name: '网络设备', itemStyle: { color: '#F59E0B' } },
          { value: 156, name: '安全设备', itemStyle: { color: '#8B5CF6' } },
          { value: 87, name: '其他设备', itemStyle: { color: '#EC4899' } }
        ]
      }
    ]
  }

  return (
    <Card className="border-[#E5E9EF] shadow-sm">
      <CardHeader className="border-b border-[#E5E9EF]">
        <CardTitle className="text-lg font-medium">资产类型分布</CardTitle>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[300px]">
          {mounted && (
            <ReactECharts option={option} style={{ height: '100%' }} />
          )}
        </div>
      </CardContent>
    </Card>
  )
} 