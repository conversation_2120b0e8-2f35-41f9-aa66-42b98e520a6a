unit FlCkReport;

interface

uses
  Classes;

type
  TFlCkReport = class
  private
    FCkcname: string;
    FAmount: double;
    FAmount1: double;
    FAmount2: double;
    FAmount3: double;
    FCkType: string;

    FGyscd: string;
    FCkdate: string;
    FYt: string;

    FItem_1: string;
    FItem_2: string;
    FItem_3: string;
    FItem_4: string;
    FItem_5: string;
    FCm: string;
    FKh: string;
    FDdh: string;
    FFlconfigtypeid: integer;

    Fsearchpmgg: string;
  public
    property Ckcname: string read FCkcname write FCkcname;
    property Amount: double read FAmount write FAmount;
    property Amount1: double read FAmount1 write FAmount1;
    property Amount2: double read FAmount2 write FAmount2;
    property Amount3: double read FAmount3 write FAmount3;
    property CkType: string read FCkType write FCkType;
    property Gyscd: string read FGyscd write FGyscd;
    property Ckdate: string read FCkdate write FCkdate;
    property Yt: string read FYt write FYt;
    property searchpmgg: string read Fsearchpmgg write Fsearchpmgg;

    property Flconfigtypeid: integer read FFlconfigtypeid write FFlconfigtypeid;
    property Item_1: string read FItem_1 write FItem_1;
    property Item_2: string read FItem_2 write FItem_2;
    property Item_3: string read FItem_3 write FItem_3;
    property Item_4: string read FItem_4 write FItem_4;
    property Item_5: string read FItem_5 write FItem_5;
    property Cm: string read FCm write FCm;
    property Kh: string read FKh write FKh;
    property Ddh: string read FDdh write FDdh;
  end;

implementation

end.
