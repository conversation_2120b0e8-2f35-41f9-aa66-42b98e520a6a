'use client'

import { useState } from 'react'
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Maximize2,
  Minimize2,
  Play,
  Pause,
  RefreshCw,
  Download,
  Share2,
  Settings,
  Timer
} from "lucide-react"

interface PreviewProps {
  isOpen: boolean
  onClose: () => void
  config: any // 大屏配置
}

export function VisualizationPreview({ isOpen, onClose, config }: PreviewProps) {
  const [isPlaying, setIsPlaying] = useState(true)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [refreshInterval, setRefreshInterval] = useState(30)

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[90vw] max-h-[90vh] p-0">
        {/* 预览工具栏 */}
        <div className="flex items-center justify-between p-4 border-b border-[#E5E9EF]">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsPlaying(!isPlaying)}
            >
              {isPlaying ? (
                <Pause className="h-4 w-4 mr-2" />
              ) : (
                <Play className="h-4 w-4 mr-2" />
              )}
              {isPlaying ? '暂停' : '播放'}
            </Button>
            
            <div className="flex items-center gap-2">
              <Timer className="h-4 w-4 text-gray-400" />
              <Select
                value={refreshInterval.toString()}
                onValueChange={(value) => setRefreshInterval(Number(value))}
              >
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">不自动刷新</SelectItem>
                  <SelectItem value="30">每30秒</SelectItem>
                  <SelectItem value="60">每1分钟</SelectItem>
                  <SelectItem value="300">每5分钟</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新数据
            </Button>
          </div>

          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-blue-50 text-blue-600">
              分辨率: 1920×1080
            </Badge>
            <Badge variant="outline" className="bg-green-50 text-green-600">
              刷新时间: 2024-03-28 17:30:00
            </Badge>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                导出图片
              </Button>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                分享链接
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                显示设置
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={toggleFullscreen}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* 预览内容区 */}
        <div className="relative bg-[#1A1A1A] w-full" style={{ height: 'calc(90vh - 65px)' }}>
          {/* 这里渲染实际的大屏内容 */}
          <div className="absolute inset-0 flex items-center justify-center text-white">
            预览内容区域
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 