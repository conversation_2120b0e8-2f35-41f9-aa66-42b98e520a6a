<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目详情钻取及分析展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <!-- 页面标题 -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">项目详情钻取及分析展示</h1>
                    <p class="mt-2 text-sm text-gray-600">提供科技项目多维度、全周期的深度画像与数据分析</p>
                </div>
                <div class="flex space-x-3">
                    <div class="relative">
                        <select class="pl-3 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white">
                            <option>切换项目</option>
                            <option>宁波市人工智能创新平台建设</option>
                            <option>宁波市生物医药产业集群培育</option>
                            <option>宁波市新材料研发中心</option>
                            <option>宁波市数字经济创新发展</option>
                        </select>
                    </div>
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        导出数据
                    </button>
                    <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                        刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 项目基础信息展示区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-wrap justify-between items-start">
                <div class="w-full lg:w-3/4">
                    <div class="flex flex-wrap items-start justify-between mb-6">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">宁波市人工智能创新平台建设</h2>
                            <div class="flex flex-wrap items-center mt-2 space-x-3">
                                <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                    进行中
                                </span>
                                <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                    重点项目
                                </span>
                                <span class="inline-flex px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                                    产学研合作
                                </span>
                            </div>
                        </div>
                        <div class="mt-4 lg:mt-0">
                            <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"></path>
                                </svg>
                                编辑项目
                            </button>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">项目编号</h3>
                            <p class="text-lg font-semibold text-gray-900">NBKJ-2024-AI-001</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">承担单位</h3>
                            <p class="text-lg font-semibold text-gray-900">宁波市人工智能研究院</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">项目负责人</h3>
                            <p class="text-lg font-semibold text-gray-900">张明教授</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">项目类型</h3>
                            <p class="text-lg font-semibold text-gray-900">重大科技专项</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">起止日期</h3>
                            <p class="text-lg font-semibold text-gray-900">2023-01-15 至 2025-12-31</p>
                        </div>
                        <div>
                            <h3 class="text-sm font-medium text-gray-500 mb-1">预算经费</h3>
                            <p class="text-lg font-semibold text-gray-900">3,500万元</p>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h3 class="text-base font-semibold text-gray-900 mb-3">项目内容</h3>
                        <p class="text-gray-700">
                            本项目旨在构建宁波市人工智能创新平台，整合产学研资源，推动人工智能技术研发与产业应用。平台将聚焦智能感知、机器学习、自然语言处理等关键技术领域，建设开放共享的AI研发基础设施，培育人工智能创新企业，促进技术成果转化，打造具有区域特色和国际影响力的人工智能创新高地。项目实施将助力宁波市数字经济转型升级，提升城市智能化水平和产业竞争力。
                        </p>
                    </div>

                    <div>
                        <h3 class="text-base font-semibold text-gray-900 mb-3">项目附件</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <a href="#" class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">项目立项书.pdf</p>
                                    <p class="text-xs text-gray-500">2.4 MB · 2023-01-10</p>
                                </div>
                            </a>
                            <a href="#" class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">项目合同.docx</p>
                                    <p class="text-xs text-gray-500">1.8 MB · 2023-01-15</p>
                                </div>
                            </a>
                            <a href="#" class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 text-purple-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">可行性研究报告.pdf</p>
                                    <p class="text-xs text-gray-500">3.2 MB · 2022-12-20</p>
                                </div>
                            </a>
                            <a href="#" class="flex items-center p-3 border border-gray-200 rounded-md hover:bg-gray-50">
                                <svg class="w-5 h-5 text-orange-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">年度进展报告.xlsx</p>
                                    <p class="text-xs text-gray-500">856 KB · 2023-12-30</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 项目进度 -->
                <div class="w-full lg:w-1/4 mt-8 lg:mt-0 lg:pl-8">
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h3 class="text-base font-semibold text-gray-900 mb-4">项目进度</h3>
                        <div class="space-y-6">
                            <div>
                                <div class="flex justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">总体进度</span>
                                    <span class="text-sm font-medium text-gray-700">45%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: 45%"></div>
                                </div>
                                <p class="mt-2 text-xs text-gray-500">已进行 13 个月，剩余 17 个月</p>
                            </div>

                            <div class="space-y-4">
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-xs font-medium text-gray-700">第一阶段：平台搭建</span>
                                        <span class="text-xs font-medium text-gray-700">100%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-green-500 h-1.5 rounded-full" style="width: 100%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-xs font-medium text-gray-700">第二阶段：技术研发</span>
                                        <span class="text-xs font-medium text-gray-700">60%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-blue-500 h-1.5 rounded-full" style="width: 60%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-xs font-medium text-gray-700">第三阶段：应用推广</span>
                                        <span class="text-xs font-medium text-gray-700">0%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-gray-400 h-1.5 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                                <div>
                                    <div class="flex justify-between mb-1">
                                        <span class="text-xs font-medium text-gray-700">第四阶段：验收评估</span>
                                        <span class="text-xs font-medium text-gray-700">0%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5">
                                        <div class="bg-gray-400 h-1.5 rounded-full" style="width: 0%"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="pt-4 border-t border-gray-200">
                                <h4 class="text-xs font-medium text-gray-700 mb-2">近期里程碑</h4>
                                <div class="space-y-3">
                                    <div class="flex">
                                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">研发中心揭牌</p>
                                            <p class="text-xs text-gray-500">2023-06-15</p>
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">首批科研设备到位</p>
                                            <p class="text-xs text-gray-500">2023-09-28</p>
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="text-sm font-medium text-gray-900">算法模型测试完成</p>
                                            <p class="text-xs text-gray-500">2024-03-15 (计划)</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目团队与合作伙伴区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-900">项目团队与合作伙伴</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                        查看历史成员
                    </button>
                    <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        管理团队
                    </button>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- 核心团队 -->
                <div class="lg:col-span-2">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">核心团队</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 项目负责人 -->
                        <div class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0 w-16 h-16 rounded-full bg-blue-100 flex items-center justify-center">
                                <span class="text-xl font-bold text-blue-600">张</span>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <h4 class="text-lg font-semibold text-gray-900">张明</h4>
                                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">负责人</span>
                                </div>
                                <p class="text-gray-600">教授 | 人工智能研究院</p>
                                <p class="mt-1 text-sm text-gray-700">研究方向：机器学习、计算机视觉</p>
                                <div class="mt-2 flex space-x-2">
                                    <a href="#" class="text-blue-600 hover:text-blue-900 text-sm">查看详情</a>
                                    <a href="#" class="text-gray-600 hover:text-gray-900 text-sm">发送邮件</a>
                                </div>
                            </div>
                        </div>

                        <!-- 核心成员 -->
                        <div class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0 w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                                <span class="text-xl font-bold text-green-600">李</span>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <h4 class="text-lg font-semibold text-gray-900">李华</h4>
                                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">核心成员</span>
                                </div>
                                <p class="text-gray-600">副教授 | 人工智能研究院</p>
                                <p class="mt-1 text-sm text-gray-700">研究方向：自然语言处理、知识图谱</p>
                                <div class="mt-2 flex space-x-2">
                                    <a href="#" class="text-blue-600 hover:text-blue-900 text-sm">查看详情</a>
                                    <a href="#" class="text-gray-600 hover:text-gray-900 text-sm">发送邮件</a>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0 w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center">
                                <span class="text-xl font-bold text-purple-600">王</span>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <h4 class="text-lg font-semibold text-gray-900">王芳</h4>
                                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">核心成员</span>
                                </div>
                                <p class="text-gray-600">高级工程师 | 宁波智能科技有限公司</p>
                                <p class="mt-1 text-sm text-gray-700">研究方向：智能系统架构、嵌入式开发</p>
                                <div class="mt-2 flex space-x-2">
                                    <a href="#" class="text-blue-600 hover:text-blue-900 text-sm">查看详情</a>
                                    <a href="#" class="text-gray-600 hover:text-gray-900 text-sm">发送邮件</a>
                                </div>
                            </div>
                        </div>

                        <div class="flex items-start p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <div class="flex-shrink-0 w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center">
                                <span class="text-xl font-bold text-orange-600">赵</span>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <h4 class="text-lg font-semibold text-gray-900">赵明</h4>
                                    <span class="ml-2 px-2 py-0.5 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">核心成员</span>
                                </div>
                                <p class="text-gray-600">研究员 | 浙江大学宁波理工学院</p>
                                <p class="mt-1 text-sm text-gray-700">研究方向：数据挖掘、大数据分析</p>
                                <div class="mt-2 flex space-x-2">
                                    <a href="#" class="text-blue-600 hover:text-blue-900 text-sm">查看详情</a>
                                    <a href="#" class="text-gray-600 hover:text-gray-900 text-sm">发送邮件</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <button class="text-blue-600 hover:text-blue-900 text-sm">查看全部12位团队成员</button>
                    </div>
                </div>

                <!-- 合作伙伴 -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">合作伙伴</h3>
                    
                    <div class="space-y-4">
                        <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <h4 class="font-semibold text-gray-900">宁波大学</h4>
                            <p class="text-sm text-gray-500">学术合作单位</p>
                            <div class="mt-2 flex items-center text-sm text-gray-700">
                                <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                参与研发、人才培养
                            </div>
                        </div>
                        
                        <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <h4 class="font-semibold text-gray-900">宁波智能科技有限公司</h4>
                            <p class="text-sm text-gray-500">产业合作单位</p>
                            <div class="mt-2 flex items-center text-sm text-gray-700">
                                <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                技术转化、市场推广
                            </div>
                        </div>
                        
                        <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <h4 class="font-semibold text-gray-900">宁波市科技局</h4>
                            <p class="text-sm text-gray-500">指导单位</p>
                            <div class="mt-2 flex items-center text-sm text-gray-700">
                                <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                政策支持、资源协调
                            </div>
                        </div>
                        
                        <div class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                            <h4 class="font-semibold text-gray-900">浙江大学</h4>
                            <p class="text-sm text-gray-500">技术支持单位</p>
                            <div class="mt-2 flex items-center text-sm text-gray-700">
                                <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                前沿技术研发、人才交流
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4 text-center">
                        <button class="text-blue-600 hover:text-blue-900 text-sm">查看全部8家合作单位</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目成果与成果关联区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-900">项目成果与成果关联</h2>
                <div class="flex space-x-2">
                    <div class="relative">
                        <select id="achievementType" class="pl-3 pr-10 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option value="all">全部成果类型</option>
                            <option value="paper">论文</option>
                            <option value="patent">专利</option>
                            <option value="product">产品</option>
                            <option value="standard">标准</option>
                            <option value="service">服务</option>
                        </select>
                    </div>
                    <button class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        添加成果
                    </button>
                </div>
            </div>

            <!-- 成果统计 -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                <div class="p-4 bg-blue-50 rounded-lg text-center">
                    <div class="text-2xl font-bold text-blue-600">12</div>
                    <div class="text-sm text-blue-700 mt-1">学术论文</div>
                </div>
                <div class="p-4 bg-green-50 rounded-lg text-center">
                    <div class="text-2xl font-bold text-green-600">8</div>
                    <div class="text-sm text-green-700 mt-1">授权专利</div>
                </div>
                <div class="p-4 bg-yellow-50 rounded-lg text-center">
                    <div class="text-2xl font-bold text-yellow-600">3</div>
                    <div class="text-sm text-yellow-700 mt-1">新产品</div>
                </div>
                <div class="p-4 bg-purple-50 rounded-lg text-center">
                    <div class="text-2xl font-bold text-purple-600">2</div>
                    <div class="text-sm text-purple-700 mt-1">技术标准</div>
                </div>
                <div class="p-4 bg-orange-50 rounded-lg text-center">
                    <div class="text-2xl font-bold text-orange-600">5</div>
                    <div class="text-sm text-orange-700 mt-1">技术服务</div>
                </div>
            </div>

            <!-- 成果列表 -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果名称</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成果类型</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">负责人</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完成时间</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联程度</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">基于深度学习的图像识别算法研究</div>
                                <div class="text-xs text-gray-500">IEEE Transactions on Pattern Analysis and Machine Intelligence</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">论文</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-08-15</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">核心成果</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500">已发表</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="openModal('achievementDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">一种基于自然语言处理的智能问答系统</div>
                                <div class="text-xs text-gray-500">专利号：ZL202310245678.9</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">专利</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">李华</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-11-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">核心成果</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500">已授权</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="openModal('achievementDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">智能工厂视觉检测平台V1.0</div>
                                <div class="text-xs text-gray-500">软件著作权：2023SR1234567</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">产品</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">王芳</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-05</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">重要成果</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500">已上线</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="openModal('achievementDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">人工智能算法评估规范</div>
                                <div class="text-xs text-gray-500">DB3302/T 10XX-2024</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">标准</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">赵明</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-02-10</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">重要成果</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500">已发布</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="openModal('achievementDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">企业AI转型咨询服务</div>
                                <div class="text-xs text-gray-500">服务对象：宁波某某股份有限公司</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-900">服务</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张明</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2023-12-20</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">一般成果</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="text-sm text-gray-500">已完成</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                                <button onclick="openModal('achievementDetailModal')" class="text-blue-600 hover:text-blue-900">详情</button>
                                <button class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="px-6 py-4 border-t border-gray-200 mt-4">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-5 条，共 30 条成果
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 text-sm bg-blue-600 text-white rounded-md">1</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目分析与可视化区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-900">项目分析与可视化</h2>
                <div class="flex space-x-2">
                    <div class="relative">
                        <select id="analysisPeriod" class="pl-3 pr-10 py-1.5 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm">
                            <option value="all">项目全周期</option>
                            <option value="quarter">近一季度</option>
                            <option value="halfyear">近半年</option>
                            <option value="year">近一年</option>
                        </select>
                    </div>
                    <button class="px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        导出图表
                    </button>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- 经费使用情况 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">经费使用情况</h3>
                    <div class="h-72">
                        <canvas id="budgetChart"></canvas>
                    </div>
                </div>

                <!-- 成果类型分布 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">成果类型分布</h3>
                    <div class="h-72">
                        <canvas id="achievementTypeChart"></canvas>
                    </div>
                </div>

                <!-- 项目进度跟踪 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">项目进度跟踪</h3>
                    <div class="h-72">
                        <canvas id="progressChart"></canvas>
                    </div>
                </div>

                <!-- 合作单位贡献度 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">合作单位贡献度</h3>
                    <div class="h-72">
                        <canvas id="contributionChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 关键指标分析 -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6">关键指标分析</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-4">投入产出比分析</h4>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">总体投入产出比</span>
                                <span class="text-sm font-medium text-green-600">1:1.8</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 65%"></div>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">研发投入</span>
                                    <span class="font-medium">1,200万元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">成果转化收入</span>
                                    <span class="font-medium">2,160万元</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">预计年收益增长</span>
                                    <span class="font-medium text-green-600">35%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-4">团队协作效率</h4>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">协作效率指数</span>
                                <span class="text-sm font-medium text-blue-600">8.7/10</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                                <div class="bg-blue-500 h-2.5 rounded-full" style="width: 87%"></div>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">跨单位协作次数</span>
                                    <span class="font-medium">24次</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">平均任务完成率</span>
                                    <span class="font-medium text-green-600">92%</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">团队沟通频率</span>
                                    <span class="font-medium">每周3.2次</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-700 mb-4">技术创新指数</h4>
                        <div class="bg-white p-4 rounded-lg shadow-sm">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">创新指数评分</span>
                                <span class="text-sm font-medium text-purple-600">8.2/10</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                                <div class="bg-purple-500 h-2.5 rounded-full" style="width: 82%"></div>
                            </div>
                            <div class="space-y-3 text-sm">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">核心技术突破</span>
                                    <span class="font-medium">3项</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">技术领先水平</span>
                                    <span class="font-medium">国内领先</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">技术扩散效应</span>
                                    <span class="font-medium text-green-600">良好</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目分析报告 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-6">项目分析报告</h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div class="lg:col-span-2">
                    <div class="space-y-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">项目总体评价</h3>
                            <p class="text-gray-700">
                                宁波市人工智能创新平台建设项目自启动以来，整体进展顺利，已完成第一阶段平台搭建任务，第二阶段技术研发按计划推进中，目前总体进度为45%。项目团队协作良好，已取得多项重要成果，包括12篇学术论文、8项授权专利、3项新产品、2项技术标准和5项技术服务。经费使用合理，投入产出比达1:1.8，预计年收益增长35%。
                            </p>
                            <p class="text-gray-700 mt-3">
                                项目在技术创新方面表现突出，实现了3项核心技术突破，技术水平达到国内领先。团队协作效率指数8.7/10，跨单位协作24次，平均任务完成率92%，显示出良好的团队协作氛围和高效的执行能力。
                            </p>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">优势与亮点</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>产学研合作模式成效显著，整合了高校、科研院所和企业的优势资源</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>技术创新能力强，在图像识别和自然语言处理领域取得突破</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>成果转化效率高，已有多项技术成功应用于实际生产</span>
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    <span>团队结构合理，核心成员专业背景互补，协作效率高</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">挑战与建议</h3>
                            <div class="space-y-4">
                                <div class="bg-yellow-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-yellow-800 mb-2">当前面临的挑战</h4>
                                    <ul class="space-y-1 text-sm text-yellow-700">
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>高端人才储备不足，特别是算法优化和系统架构方向</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>部分技术转化周期较长，市场推广进度低于预期</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-yellow-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>跨单位数据共享存在一定障碍，影响协同研发效率</span>
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="bg-blue-50 p-4 rounded-lg">
                                    <h4 class="font-medium text-blue-800 mb-2">未来发展建议</h4>
                                    <ul class="space-y-1 text-sm text-blue-700">
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <span>加大高端人才引进力度，特别是算法和架构方向的领军人才</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <span>建立专门的市场推广团队，加速技术成果转化和产业化</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <span>推动建立统一的数据共享平台，消除跨单位协作障碍</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg class="w-4 h-4 text-blue-500 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                            <span>加强与本地企业合作，开展更多应用示范项目</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div>
                    <div class="bg-gray-50 rounded-lg p-6 sticky top-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">报告信息</h3>
                        <div class="space-y-4 text-sm">
                            <div>
                                <h4 class="text-gray-500 mb-1">报告生成日期</h4>
                                <p class="font-medium text-gray-900">2024-03-15</p>
                            </div>
                            <div>
                                <h4 class="text-gray-500 mb-1">数据截止日期</h4>
                                <p class="font-medium text-gray-900">2024-03-10</p>
                            </div>
                            <div>
                                <h4 class="text-gray-500 mb-1">报告版本</h4>
                                <p class="font-medium text-gray-900">V1.2</p>
                            </div>
                            <div>
                                <h4 class="text-gray-500 mb-1">生成机构</h4>
                                <p class="font-medium text-gray-900">宁波市科技评估中心</p>
                            </div>
                            <div>
                                <h4 class="text-gray-500 mb-1">分析师</h4>
                                <p class="font-medium text-gray-900">陈科技</p>
                            </div>
                            <div class="pt-4 border-t border-gray-200">
                                <h4 class="text-gray-500 mb-3">报告附件</h4>
                                <div class="space-y-3">
                                    <a href="#" class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                        <svg class="w-5 h-5 text-blue-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a