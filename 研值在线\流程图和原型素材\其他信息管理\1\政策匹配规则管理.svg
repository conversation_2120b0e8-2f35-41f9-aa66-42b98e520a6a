<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">政策匹配规则管理流程</text>

  <!-- 阶段一：规则创建与校验 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：规则创建与校验</text>
  
  <!-- 节点1: 规则编辑创建 -->
  <g transform="translate(200, 130)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">规则编辑创建</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">运维人员创建或修改匹配规则</text>
  </g>

  <!-- 节点2: 系统校验 -->
  <g transform="translate(920, 130)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">系统校验</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">校验名称唯一性、条件语法与版本号</text>
  </g>

  <!-- 节点3: 生成待审核记录 -->
  <g transform="translate(560, 240)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">生成待审核记录</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">校验通过后生成规则记录</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 480 165 C 650 165, 750 165, 920 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  
  <!-- 连接线 2 -> 3 -->
  <path d="M 1060 200 C 1060 220, 800 220, 700 240" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：审核与生效 -->
  <text x="700" y="370" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：审核与生效</text>

  <!-- 节点4: 推送给管理员 -->
  <g transform="translate(200, 400)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">推送给管理员</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">系统推送待审核记录</text>
  </g>

  <!-- 节点5: 审核通过 -->
  <g transform="translate(560, 400)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审核通过</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">管理员核对条件逻辑与测试结果</text>
  </g>

  <!-- 节点6: 状态更新与缓存 -->
  <g transform="translate(920, 400)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">状态更新与缓存</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">更新为"已生效"并写入缓存</text>
  </g>

  <!-- 连接线 3 -> 4 -->
  <path d="M 600 310 C 500 340, 400 370, 340 400" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 4 -> 5 -->
  <path d="M 480 435 C 520 435, 520 435, 560 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 5 -> 6 -->
  <path d="M 840 435 C 880 435, 880 435, 920 435" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：依赖检查与操作 -->
  <text x="700" y="540" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：依赖检查与操作</text>
  
  <!-- 节点7: 停用删除检查 -->
  <g transform="translate(350, 570)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">停用删除检查</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">检查规则是否被算法模型引用</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">存在引用则阻止操作并提示</text>
  </g>

  <!-- 节点8: 版本复制调整 -->
  <g transform="translate(750, 570)" filter="url(#soft-shadow)">
    <rect width="300" height="80" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="150" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">版本复制调整</text>
    <text x="150" y="55" text-anchor="middle" font-size="12" fill="#555">复制历史版本进行调整</text>
    <text x="150" y="70" text-anchor="middle" font-size="12" fill="#555">自动递增版本号并进入待审核</text>
  </g>

  <!-- 连接线 6 -> 7 -->
  <path d="M 1000 470 C 1000 520, 600 540, 500 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 6 -> 8 -->
  <path d="M 1060 470 C 1060 520, 1000 540, 900 570" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：审计与报告 -->
  <text x="700" y="720" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：审计与报告</text>

  <!-- 节点9: 审计日志记录 -->
  <g transform="translate(200, 750)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">审计日志记录</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">记录所有规则变更、审核</text>
    <text x="140" y="70" text-anchor="middle" font-size="12" fill="#555">与回滚操作</text>
  </g>

  <!-- 节点10: 定时归档报告 -->
  <g transform="translate(920, 750)" filter="url(#soft-shadow)">
    <rect width="280" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时归档报告</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">每日定时归档并生成</text>
    <text x="140" y="70" text-anchor="middle" font-size="12" fill="#555">规则变更报告</text>
  </g>

  <!-- 节点11: 合规查询 -->
  <g transform="translate(560, 880)" filter="url(#soft-shadow)">
    <rect width="280" height="70" rx="8" ry="8" fill="#E8F5E8" stroke="#4CAF50" stroke-width="1.5" />
    <text x="140" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">合规查询</text>
    <text x="140" y="55" text-anchor="middle" font-size="12" fill="#555">供合规与算法团队查询</text>
  </g>

  <!-- 连接线 7 -> 9 -->
  <path d="M 450 650 C 400 680, 380 720, 340 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 8 -> 9 -->
  <path d="M 800 650 C 700 680, 500 720, 400 750" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 10 -->
  <path d="M 480 790 C 650 790, 750 790, 920 790" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <text x="700" y="780" text-anchor="middle" font-size="12" fill="#555">数据流转</text>

  <!-- 连接线 10 -> 11 -->
  <path d="M 1000 830 C 1000 850, 800 860, 700 880" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 连接线 9 -> 11 -->
  <path d="M 340 830 C 340 850, 500 860, 560 880" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

</svg>