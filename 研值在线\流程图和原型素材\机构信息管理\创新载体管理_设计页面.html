<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创新载体管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        secondary: '#64748b'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <h1 class="text-2xl font-bold text-gray-900 mb-6">创新载体管理</h1>

        <!-- 筛选区 -->
        <div class="bg-white shadow-md rounded-lg p-6 mb-6">
            <h2 class="text-lg font-medium text-gray-800 mb-4">筛选条件</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label for="platformName" class="block text-sm font-medium text-gray-700 mb-1">平台名称</label>
                    <input type="text" id="platformName" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入平台名称">
                </div>
                <div>
                    <label for="platformType" class="block text-sm font-medium text-gray-700 mb-1">平台类型</label>
                    <select id="platformType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部类型</option>
                        <option value="incubator">孵化器</option>
                        <option value="makerspace">众创空间</option>
                        <option value="starspace">星创天地</option>
                    </select>
                </div>
                <div>
                    <label for="platformLevel" class="block text-sm font-medium text-gray-700 mb-1">级别</label>
                    <select id="platformLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部级别</option>
                        <option value="national">国家级</option>
                        <option value="provincial">省级</option>
                        <option value="municipal">市级</option>
                    </select>
                </div>
                <div>
                    <label for="yearRange" class="block text-sm font-medium text-gray-700 mb-1">认定年份</label>
                    <div class="flex space-x-2">
                        <input type="number" id="yearFrom" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="起始年份" min="2000" max="2024">
                        <span class="flex items-center text-gray-500">至</span>
                        <input type="number" id="yearTo" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="结束年份" min="2000" max="2024">
                    </div>
                </div>
                <div>
                    <label for="supportUnit" class="block text-sm font-medium text-gray-700 mb-1">依托单位</label>
                    <input type="text" id="supportUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="请输入依托单位">
                </div>
                <div>
                    <label for="district" class="block text-sm font-medium text-gray-700 mb-1">行政区划</label>
                    <select id="district" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部区域</option>
                        <option value="haishu">海曙区</option>
                        <option value="jiangdong">江东区</option>
                        <option value="jiangbei">江北区</option>
                        <option value="yinzhou">鄞州区</option>
                    </select>
                </div>
            </div>
            <div class="flex justify-end space-x-3 mt-4">
                <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    重置
                </button>
                <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    查询
                </button>
            </div>
        </div>

        <!-- 操作工具栏 -->
        <div class="flex justify-between items-center mb-4">
            <div class="flex space-x-3">
                <button onclick="openImportModal()" class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    批量导入
                </button>
                <button class="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    导出数据
                </button>
                <button onclick="openAddModal()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    新增载体
                </button>
            </div>
            <div class="text-sm text-gray-500">
                共 <span class="font-medium text-gray-700">128</span> 条记录
            </div>
        </div>

        <!-- 数据列表 -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台名称</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定年份</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">依托单位</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">在孵企业</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务团队</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">孵化成果</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">完整度</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">宁波市科技企业孵化器</div>
                            <div class="text-sm text-gray-500">海曙区</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孵化器</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">宁波市科技局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">56</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">12</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">23</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-green-500 h-2.5 rounded-full" style="width: 95%"></div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal('1')" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal('1')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openManageModal('1')" class="text-purple-600 hover:text-purple-900">关联</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">鄞州众创空间</div>
                            <div class="text-sm text-gray-500">鄞州区</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">众创空间</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">鄞州区科技局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">32</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">15</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-yellow-500 h-2.5 rounded-full" style="width: 75%"></div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal('2')" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal('2')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openManageModal('2')" class="text-purple-600 hover:text-purple-900">关联</button>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">江北星创天地</div>
                            <div class="text-sm text-gray-500">江北区</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">星创天地</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">市级</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">江北区农业局</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">18</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">8</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="bg-red-500 h-2.5 rounded-full" style="width: 50%"></div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button onclick="openDetailModal('3')" class="text-blue-600 hover:text-blue-900">详情</button>
                            <button onclick="openEditModal('3')" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                            <button onclick="openManageModal('3')" class="text-purple-600 hover:text-purple-900">关联</button>
                        </td>
                    </tr>
                </tbody>
            </table>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 128 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div id="detailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">创新载体详情</h3>
                <button onclick="closeModal('detailModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 基本信息 -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-medium text-gray-800 mb-4">基本信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">平台名称</label>
                                <p class="text-sm text-gray-900">宁波市科技企业孵化器</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">平台类型</label>
                                <p class="text-sm text-gray-900">孵化器</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">级别</label>
                                <p class="text-sm text-gray-900">国家级</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">认定年份</label>
                                <p class="text-sm text-gray-900">2018</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">依托单位</label>
                                <p class="text-sm text-gray-900">宁波市科技局</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">行政区划</label>
                                <p class="text-sm text-gray-900">海曙区</p>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-500 mb-1">地址</label>
                                <p class="text-sm text-gray-900">宁波市海曙区柳汀街345号</p>
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-500 mb-1">简介</label>
                                <p class="text-sm text-gray-900">宁波市科技企业孵化器是宁波市重点打造的科技创新平台，主要服务于科技型中小企业，提供办公场地、技术支持、投融资对接等全方位服务。</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div class="bg-gray-50 rounded-lg p-6">
                        <h4 class="text-lg font-medium text-gray-800 mb-4">统计信息</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-sm font-medium text-gray-500 mb-1">在孵企业</div>
                                <div class="text-2xl font-bold text-blue-600">56</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-sm font-medium text-gray-500 mb-1">服务团队</div>
                                <div class="text-2xl font-bold text-green-600">12</div>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <div class="text-sm font-medium text-gray-500 mb-1">孵化成果</div>
                                <div class="text-2xl font-bold text-purple-600">23</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧信息 -->
                <div>
                    <!-- 联系人信息 -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-medium text-gray-800 mb-4">联系人信息</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">联系人</label>
                                <p class="text-sm text-gray-900">张主任</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">联系电话</label>
                                <p class="text-sm text-gray-900">0574-87654321</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500 mb-1">电子邮箱</label>
                                <p class="text-sm text-gray-900"><EMAIL></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="space-y-3">
                        <button onclick="openEditModal('1')" class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                            编辑信息
                        </button>
                        <button onclick="openManageModal('1')" class="w-full px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700">
                            管理关联
                        </button>
                        <button class="w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                            查看历史记录
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">编辑创新载体信息</h3>
                <button onclick="closeModal('editModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <form>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="editName" class="block text-sm font-medium text-gray-700 mb-1">平台名称</label>
                        <input type="text" id="editName" class="w-full px-3 py-2 border border-gray-300 rounded-md" value="宁波市科技企业孵化器">
                    </div>
                    <div>
                        <label for="editType" class="block text-sm font-medium text-gray-700 mb-1">平台类型</label>
                        <select id="editType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="incubator" selected>孵化器</option>
                            <option value="makerspace">众创空间</option>
                            <option value="starspace">星创天地</option>
                        </select>
                    </div>
                    <div>
                        <label for="editLevel" class="block text-sm font-medium text-gray-700 mb-1">级别</label>
                        <select id="editLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="national" selected>国家级</option>
                            <option value="provincial">省级</option>
                            <option value="municipal">市级</option>
                        </select>
                    </div>
                    <div>
                        <label for="editYear" class="block text-sm font-medium text-gray-700 mb-1">认定年份</label>
                        <input type="number" id="editYear" class="w-full px-3 py-2 border border-gray-300 rounded-md" value="2018" min="2000" max="2024">
                    </div>
                    <div>
                        <label for="editUnit" class="block text-sm font-medium text-gray-700 mb-1">依托单位</label>
                        <input type="text" id="editUnit" class="w-full px-3 py-2 border border-gray-300 rounded-md" value="宁波市科技局">
                    </div>
                    <div>
                        <label for="editDistrict" class="block text-sm font-medium text-gray-700 mb-1">行政区划</label>
                        <select id="editDistrict" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="haishu" selected>海曙区</option>
                            <option value="jiangdong">江东区</option>
                            <option value="jiangbei">江北区</option>
                            <option value="yinzhou">鄞州区</option>
                        </select>
                    </div>
                    <div class="md:col-span-2">
                        <label for="editAddress" class="block text-sm font-medium text-gray-700 mb-1">地址</label>
                        <input type="text" id="editAddress" class="w-full px-3 py-2 border border-gray-300 rounded-md" value="宁波市海曙区柳汀街345号">
                    </div>
                    <div class="md:col-span-2">
                        <label for="editDesc" class="block text-sm font-medium text-gray-700 mb-1">简介</label>
                        <textarea id="editDesc" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md">宁波市科技企业孵化器是宁波市重点打造的科技创新平台，主要服务于科技型中小企业，提供办公场地、技术支持、投融资对接等全方位服务。</textarea>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('editModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        取消
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        保存
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 关联管理模态框 -->
    <div id="manageModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">管理关联 - 宁波市科技企业孵化器</h3>
                <button onclick="closeModal('manageModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- 关联团队 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">服务团队</h4>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="team1" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="team1" class="ml-2 text-sm text-gray-700">宁波大学创新团队</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="team2" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="team2" class="ml-2 text-sm text-gray-700">宁波工程学院研发团队</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="team3" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="team3" class="ml-2 text-sm text-gray-700">宁波市科技局专家团队</label>
                        </div>
                    </div>
                </div>
                
                <!-- 关联设备 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">关键设备</h4>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="device1" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="device1" class="ml-2 text-sm text-gray-700">高分辨率质谱仪</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="device2" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="device2" class="ml-2 text-sm text-gray-700">液相色谱仪</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="device3" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="device3" class="ml-2 text-sm text-gray-700">原子吸收光谱仪</label>
                        </div>
                    </div>
                </div>
                
                <!-- 关联成果 -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h4 class="text-lg font-medium text-gray-800 mb-4">孵化成果</h4>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <input type="checkbox" id="result1" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="result1" class="ml-2 text-sm text-gray-700">智能家居控制系统</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="result2" class="h-4 w-4 text-blue-600 focus:ring-blue-500">
                            <label for="result2" class="ml-2 text-sm text-gray-700">新型环保材料</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="result3" class="h-4 w-4 text-blue-600 focus:ring-blue-500" checked>
                            <label for="result3" class="ml-2 text-sm text-gray-700">工业机器人控制系统</label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closeModal('manageModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    取消
                </button>
                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    保存关联
                </button>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-1/2 shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-xl font-semibold text-gray-900">批量导入创新载体</h3>
                <button onclick="closeModal('importModal')" class="text-gray-400 hover:text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <div class="space-y-6">
                <div>
                    <h4 class="text-md font-medium text-gray-700 mb-3">步骤1：下载模板文件</h4>
                    <button class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        下载Excel模板
                    </button>
                </div>
                
                <div>
                    <h4 class="text-md font-medium text-gray-700 mb-3">步骤2：填写并上传文件</h4>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                        </svg>
                        <div class="mt-4">
                            <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                <span>点击上传文件</span>
                                <input id="file-upload" name="file-upload" type="file" class="sr-only">
                            </label>
                            <p class="text-xs text-gray-500 mt-1">支持.xlsx格式，最大10MB</p>
                        </div>
                    </div>
                </div>
                
                <div id="importProgress" class="hidden">
                    <h4 class="text-md font-medium text-gray-700 mb-3">导入进度</h4>
                    <div class="flex items-center justify-between mb-1">
                        <span class="text-sm text-gray-500">正在处理...</span>
                        <span class="text-sm font-medium text-gray-700">75%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: 75%"></div>
                    </div>
                </div>
                
                <div id="importResult" class="hidden bg-yellow-50 p-4 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-yellow-800">导入完成，发现3条数据存在问题</p>
                            <ul class="mt-2 text-xs text-yellow-700 list-disc list-inside">
                                <li>第5行：平台名称不能为空</li>
                                <li>第8行：认定年份格式错误</li>
                                <li>第12行：依托单位不存在</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModal('importModal')" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        取消
                    </button>
                    <button type="button" id="startImport" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        开始导入
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局UI控制函数
        function openDetailModal(id) {
            document.getElementById('detailModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function openEditModal(id) {
            document.getElementById('editModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function openManageModal(id) {
            document.getElementById('manageModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
        
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            document.body.style.overflow = 'auto';
        }
        
        // 点击模态框外部关闭
        document.querySelectorAll('.fixed').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal(this.id);
                }
            });
        });
        
        // 文件上传处理
        document.getElementById('file-upload').addEventListener('change', function(e) {
            if (this.files.length > 0) {
                document.getElementById('importProgress').classList.remove('hidden');
                document.getElementById('startImport').textContent = '重新导入';
                
                // 模拟上传进度
                let progress = 0;
                const interval = setInterval(() => {
                    progress += 5;
                    document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';
                    document.querySelector('#importProgress span:last-child').textContent = progress + '%';
                    
                    if (progress >= 100) {
                        clearInterval(interval);
                        document.getElementById('importResult').classList.remove('hidden');
                    }
                }, 200);
            }
        });
        
        // 开始导入按钮
        document.getElementById('startImport').addEventListener('click', function() {
            if (document.getElementById('file-upload').files.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            // 重置进度
            document.querySelector('#importProgress .bg-blue-600').style.width = '0%';
            document.querySelector('#importProgress span:last-child').textContent = '0%';
            document.getElementById('importResult').classList.add('hidden');
            
            // 模拟上传
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                document.querySelector('#importProgress .bg-blue-600').style.width = progress + '%';
                document.querySelector('#importProgress span:last-child').textContent = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('importResult').classList.remove('hidden');
                }
            }, 200);
        });
    </script>
</body>
</html>