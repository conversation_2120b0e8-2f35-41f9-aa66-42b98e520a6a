unit Bmcj;

interface

uses
  Classes;

type
  TBmcj = class
  private
    FBmcjid: integer;
    FCjtypeid: integer;
    FBmcjName: string;
    FBmcjType: string;
  public
    property Bmcjid: integer read FBmcjid write FBmcjid;
    property Cjtypeid: integer read FCjtypeid write FCjtypeid;
    property BmcjName: string read FBmcjName write FBmcjName;
    property BmcjType: string read FBmcjType write FBmcjType;
  end;

implementation

end.
