unit Marks;

interface
uses
    Classes;

type
    TMarks = class
    private
        FMarksID: integer;
        FMarksName: string;
        FMarksDes: string;
        FMarketTime: string;
        FMarketUser: string;
        FState: string;
    public
        property MarksID: integer read FMarksID write FMarksID;
        property MarksName: string read FMarksName write FMarksName;
        property MarksDes: string read FMarksDes write FMarksDes;
        property MarketTime: string read FMarketTime write FMarketTime;
        property MarketUser:string read FMarket<PERSON>ser write FMarketUser;
        property State: string read FState write FState;
    end;

implementation

end.

