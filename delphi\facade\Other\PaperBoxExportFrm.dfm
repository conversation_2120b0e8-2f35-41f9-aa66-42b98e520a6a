object PaperBoxExportForm: TPaperBoxExportForm
  Left = 0
  Top = 0
  ClientHeight = 707
  ClientWidth = 955
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = #23435#20307
  Font.Style = []
  OldCreateOrder = True
  Position = poScreenCenter
  PixelsPerInch = 96
  TextHeight = 12
  object RzPanel1: TRzPanel
    Left = 0
    Top = 0
    Width = 955
    Height = 233
    Align = alTop
    BorderOuter = fsFlat
    TabOrder = 0
    object RzPanel4: TRzPanel
      Left = 1
      Top = 1
      Width = 953
      Height = 40
      Align = alTop
      BorderOuter = fsFlat
      TabOrder = 0
      object RzLabel9: TRzLabel
        Left = 125
        Top = 12
        Width = 72
        Height = 12
        Caption = #35746#21333#26085#26399#36215#65306
      end
      object RzLabel10: TRzLabel
        Left = 416
        Top = 12
        Width = 72
        Height = 12
        Caption = #35746#21333#26085#26399#27490#65306
      end
      object RzLabel2: TRzLabel
        Left = 15
        Top = 12
        Width = 78
        Height = 12
        Caption = #35746#21333#20449#24687#26597#35810
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #23435#20307
        Font.Style = [fsBold]
        ParentFont = False
      end
      object DateTimePicker_BeginTime: TAdvDateTimePicker
        Left = 203
        Top = 7
        Width = 145
        Height = 20
        Date = 40894.599976851850000000
        Format = ''
        Time = 40894.599976851850000000
        DoubleBuffered = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #23435#20307
        Font.Style = []
        Kind = dkDate
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 0
        TabStop = True
        BorderStyle = bsSingle
        Ctl3D = True
        DateTime = 40894.599976851850000000
        Version = '1.2.5.0'
        LabelFont.Charset = DEFAULT_CHARSET
        LabelFont.Color = clWindowText
        LabelFont.Height = -11
        LabelFont.Name = 'Tahoma'
        LabelFont.Style = []
      end
      object DateTimePicker_EndTime: TAdvDateTimePicker
        Left = 494
        Top = 7
        Width = 145
        Height = 20
        Date = 40894.599976851850000000
        Format = ''
        Time = 40894.599976851850000000
        DoubleBuffered = True
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -12
        Font.Name = #23435#20307
        Font.Style = []
        Kind = dkDate
        ParentDoubleBuffered = False
        ParentFont = False
        TabOrder = 1
        TabStop = True
        BorderStyle = bsSingle
        Ctl3D = True
        DateTime = 40894.599976851850000000
        Version = '1.2.5.0'
        LabelFont.Charset = DEFAULT_CHARSET
        LabelFont.Color = clWindowText
        LabelFont.Height = -11
        LabelFont.Name = 'Tahoma'
        LabelFont.Style = []
      end
      object RzButton1: TRzButton
        Left = 688
        Top = 6
        Caption = #26597#35810
        TabOrder = 2
        OnClick = RzButton1Click
      end
    end
    object RzPanel3: TRzPanel
      Left = 1
      Top = 41
      Width = 953
      Height = 191
      Align = alClient
      BorderOuter = fsNone
      TabOrder = 1
      object AdvStringGrid1: TAdvStringGrid
        Left = 0
        Top = 0
        Width = 953
        Height = 191
        Cursor = crDefault
        Align = alClient
        BevelInner = bvNone
        BevelOuter = bvNone
        ColCount = 8
        Ctl3D = False
        DoubleBuffered = False
        DrawingStyle = gdsClassic
        FixedColor = clWhite
        FixedCols = 0
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clBlack
        Font.Height = -12
        Font.Name = #23435#20307
        Font.Style = []
        Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRowSelect]
        ParentCtl3D = False
        ParentDoubleBuffered = False
        ParentFont = False
        ScrollBars = ssBoth
        TabOrder = 0
        OnMouseMove = AdvStringGrid1MouseMove
        GridLineColor = 15527152
        GridFixedLineColor = 13947601
        HoverRowCells = [hcNormal, hcSelected]
        OnCheckBoxChange = AdvStringGrid1CheckBoxChange
        HighlightColor = clNone
        ActiveCellFont.Charset = DEFAULT_CHARSET
        ActiveCellFont.Color = clWindowText
        ActiveCellFont.Height = -12
        ActiveCellFont.Name = #23435#20307
        ActiveCellFont.Style = [fsBold]
        ActiveCellColor = 16575452
        ActiveCellColorTo = 16571329
        ColumnHeaders.Strings = (
          #35746#21333#24207#21495
          #30446#30340#28207
          #23458#25143#21517#31216
          #24207#21495
          #21333#25454#32534#21495
          #35746#21333#25551#36848
          #24635#25968#37327
          #24635#31665#25968)
        ControlLook.FixedGradientMirrorFrom = 16049884
        ControlLook.FixedGradientMirrorTo = 16247261
        ControlLook.FixedGradientHoverFrom = 16710648
        ControlLook.FixedGradientHoverTo = 16446189
        ControlLook.FixedGradientHoverMirrorFrom = 16049367
        ControlLook.FixedGradientHoverMirrorTo = 15258305
        ControlLook.FixedGradientDownFrom = 15853789
        ControlLook.FixedGradientDownTo = 15852760
        ControlLook.FixedGradientDownMirrorFrom = 15522767
        ControlLook.FixedGradientDownMirrorTo = 15588559
        ControlLook.FixedGradientDownBorder = 14007466
        ControlLook.DropDownHeader.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownHeader.Font.Color = clWindowText
        ControlLook.DropDownHeader.Font.Height = -11
        ControlLook.DropDownHeader.Font.Name = 'Tahoma'
        ControlLook.DropDownHeader.Font.Style = []
        ControlLook.DropDownHeader.Visible = True
        ControlLook.DropDownHeader.Buttons = <>
        ControlLook.DropDownFooter.Font.Charset = DEFAULT_CHARSET
        ControlLook.DropDownFooter.Font.Color = clWindowText
        ControlLook.DropDownFooter.Font.Height = -11
        ControlLook.DropDownFooter.Font.Name = 'Tahoma'
        ControlLook.DropDownFooter.Font.Style = []
        ControlLook.DropDownFooter.Visible = True
        ControlLook.DropDownFooter.Buttons = <>
        Filter = <>
        FilterDropDown.Font.Charset = DEFAULT_CHARSET
        FilterDropDown.Font.Color = clWindowText
        FilterDropDown.Font.Height = -11
        FilterDropDown.Font.Name = 'Tahoma'
        FilterDropDown.Font.Style = []
        FilterDropDownClear = '(All)'
        FilterEdit.TypeNames.Strings = (
          'Starts with'
          'Ends with'
          'Contains'
          'Not contains'
          'Equal'
          'Not equal'
          'Clear')
        FixedColWidth = 69
        FixedRowHeight = 22
        FixedRowAlways = True
        FixedFont.Charset = DEFAULT_CHARSET
        FixedFont.Color = clBlack
        FixedFont.Height = -12
        FixedFont.Name = #23435#20307
        FixedFont.Style = [fsBold]
        Flat = True
        FloatFormat = '%.2f'
        HoverButtons.Buttons = <>
        HoverButtons.Position = hbLeftFromColumnLeft
        HTMLSettings.ImageFolder = 'images'
        HTMLSettings.ImageBaseName = 'img'
        Look = glWin7
        PrintSettings.DateFormat = 'dd/mm/yyyy'
        PrintSettings.Font.Charset = DEFAULT_CHARSET
        PrintSettings.Font.Color = clWindowText
        PrintSettings.Font.Height = -11
        PrintSettings.Font.Name = 'Tahoma'
        PrintSettings.Font.Style = []
        PrintSettings.FixedFont.Charset = DEFAULT_CHARSET
        PrintSettings.FixedFont.Color = clWindowText
        PrintSettings.FixedFont.Height = -11
        PrintSettings.FixedFont.Name = 'Tahoma'
        PrintSettings.FixedFont.Style = []
        PrintSettings.HeaderFont.Charset = DEFAULT_CHARSET
        PrintSettings.HeaderFont.Color = clWindowText
        PrintSettings.HeaderFont.Height = -11
        PrintSettings.HeaderFont.Name = 'Tahoma'
        PrintSettings.HeaderFont.Style = []
        PrintSettings.FooterFont.Charset = DEFAULT_CHARSET
        PrintSettings.FooterFont.Color = clWindowText
        PrintSettings.FooterFont.Height = -11
        PrintSettings.FooterFont.Name = 'Tahoma'
        PrintSettings.FooterFont.Style = []
        PrintSettings.PageNumSep = '/'
        SearchFooter.Color = 16645370
        SearchFooter.ColorTo = 16247261
        SearchFooter.FindNextCaption = 'Find &next'
        SearchFooter.FindPrevCaption = 'Find &previous'
        SearchFooter.Font.Charset = DEFAULT_CHARSET
        SearchFooter.Font.Color = clWindowText
        SearchFooter.Font.Height = -11
        SearchFooter.Font.Name = 'Tahoma'
        SearchFooter.Font.Style = []
        SearchFooter.HighLightCaption = 'Highlight'
        SearchFooter.HintClose = 'Close'
        SearchFooter.HintFindNext = 'Find next occurrence'
        SearchFooter.HintFindPrev = 'Find previous occurrence'
        SearchFooter.HintHighlight = 'Highlight occurrences'
        SearchFooter.MatchCaseCaption = 'Match case'
        SelectionColor = clNone
        ShowSelection = False
        SortSettings.DefaultFormat = ssAutomatic
        SortSettings.HeaderColor = 16579058
        SortSettings.HeaderColorTo = 16579058
        SortSettings.HeaderMirrorColor = 16380385
        SortSettings.HeaderMirrorColorTo = 16182488
        URLUnderlineOnHover = True
        UseInternalHintClass = False
        VAlignment = vtaBottom
        Version = '8.1.3.0'
        ColWidths = (
          69
          114
          143
          73
          64
          156
          64
          64)
      end
    end
  end
  object RzPanel2: TRzPanel
    Left = 0
    Top = 233
    Width = 955
    Height = 474
    Align = alClient
    BorderOuter = fsFlat
    Font.Charset = DEFAULT_CHARSET
    Font.Color = clWindowText
    Font.Height = -12
    Font.Name = #23435#20307
    Font.Style = []
    ParentFont = False
    TabOrder = 1
    object RzLabel11: TRzLabel
      Left = 24
      Top = 22
      Width = 91
      Height = 12
      Caption = #35013#31665#21333#25968#25454#24405#20837
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = [fsBold]
      ParentFont = False
    end
    object RzLabel12: TRzLabel
      Left = 24
      Top = 55
      Width = 48
      Height = 12
      Caption = #31532#19968#27493#65306
    end
    object RzLabel1: TRzLabel
      Left = 96
      Top = 55
      Width = 60
      Height = 12
      Caption = #23458#25143#25260#22836#65306
    end
    object RzLabel4: TRzLabel
      Left = 96
      Top = 89
      Width = 48
      Height = 12
      Caption = #35746#21333#21495#65306
    end
    object RzLabel5: TRzLabel
      Left = 96
      Top = 122
      Width = 60
      Height = 12
      Caption = #24037#21378#32534#21495#65306
    end
    object RzLabel3: TRzLabel
      Left = 96
      Top = 193
      Width = 60
      Height = 12
      Caption = #20986#36135#26085#26399#65306
    end
    object RzLabel6: TRzLabel
      Left = 96
      Top = 161
      Width = 60
      Height = 12
      Caption = #19979#21333#26085#26399#65306
    end
    object RzLabel7: TRzLabel
      Left = 384
      Top = 55
      Width = 60
      Height = 12
      Caption = #21457#31080#21495#30721#65306
    end
    object RzLabel8: TRzLabel
      Left = 384
      Top = 89
      Width = 48
      Height = 12
      Caption = #21512#21516#21495#65306
    end
    object RzLabel15: TRzLabel
      Left = 384
      Top = 122
      Width = 60
      Height = 12
      Caption = #21457#31080#26085#26399#65306
    end
    object RzLabel16: TRzLabel
      Left = 24
      Top = 329
      Width = 48
      Height = 12
      Caption = #31532#19977#27493#65306
    end
    object RzURLLabel1: TRzURLLabel
      Left = 96
      Top = 329
      Width = 72
      Height = 12
      Caption = #25171#24320#21457#31080#25991#20214
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clHighlight
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = [fsUnderline]
      ParentFont = False
      OnClick = RzURLLabel2Click
    end
    object RzLabel17: TRzLabel
      Left = 24
      Top = 236
      Width = 48
      Height = 12
      Caption = #31532#20108#27493#65306
    end
    object RzLabel19: TRzLabel
      Left = 384
      Top = 161
      Width = 108
      Height = 12
      Caption = #20986#36135#26126#32454#20013#21512#21516#21495#65306
    end
    object Edit_Order_Number: TRzEdit
      Left = 176
      Top = 86
      Width = 145
      Height = 20
      Text = ''
      TabOrder = 0
    end
    object DateTimePicker_Chrq: TAdvDateTimePicker
      Left = 176
      Top = 191
      Width = 145
      Height = 20
      Date = 40894.599976851850000000
      Format = ''
      Time = 40894.599976851850000000
      DoubleBuffered = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      Kind = dkDate
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 1
      TabStop = True
      BorderStyle = bsSingle
      Ctl3D = True
      DateTime = 40894.599976851850000000
      Version = '1.2.5.0'
      LabelFont.Charset = DEFAULT_CHARSET
      LabelFont.Color = clWindowText
      LabelFont.Height = -11
      LabelFont.Name = 'Tahoma'
      LabelFont.Style = []
    end
    object Edit_Factory: TRzEdit
      Left = 176
      Top = 119
      Width = 145
      Height = 20
      Text = ''
      TabOrder = 3
    end
    object DateTimePicker_Xdrq: TAdvDateTimePicker
      Left = 176
      Top = 156
      Width = 145
      Height = 20
      Date = 40894.599976851850000000
      Format = ''
      Time = 40894.599976851850000000
      DoubleBuffered = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      Kind = dkDate
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 2
      TabStop = True
      BorderStyle = bsSingle
      Ctl3D = True
      DateTime = 40894.599976851850000000
      Version = '1.2.5.0'
      LabelFont.Charset = DEFAULT_CHARSET
      LabelFont.Color = clWindowText
      LabelFont.Height = -11
      LabelFont.Name = 'Tahoma'
      LabelFont.Style = []
    end
    object ComboBox_Customer: TRzComboBox
      Left = 176
      Top = 52
      Width = 145
      Height = 20
      TabOrder = 4
      Items.Strings = (
        'TSCO PTY LTD'
        'JB'#39'S N.Z PTY,LTD')
    end
    object Edit_Invoice_Number: TRzEdit
      Left = 504
      Top = 52
      Width = 145
      Height = 20
      Text = ''
      TabOrder = 5
    end
    object Edit_Contract_Number: TRzEdit
      Left = 504
      Top = 86
      Width = 145
      Height = 20
      Text = ''
      TabOrder = 6
    end
    object DateTimePicker_Fprq: TAdvDateTimePicker
      Left = 504
      Top = 122
      Width = 145
      Height = 20
      Date = 40894.599976851850000000
      Format = ''
      Time = 40894.599976851850000000
      DoubleBuffered = True
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clWindowText
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      Kind = dkDate
      ParentDoubleBuffered = False
      ParentFont = False
      TabOrder = 7
      TabStop = True
      BorderStyle = bsSingle
      Ctl3D = True
      DateTime = 40894.599976851850000000
      Version = '1.2.5.0'
      LabelFont.Charset = DEFAULT_CHARSET
      LabelFont.Color = clWindowText
      LabelFont.Height = -11
      LabelFont.Name = 'Tahoma'
      LabelFont.Style = []
    end
    object AdvGlowButton_Fp: TAdvGlowButton
      Left = 96
      Top = 225
      Width = 121
      Height = 85
      BorderStyle = bsNone
      Caption = #23548#20986#21457#31080#25991#20214
      Font.Charset = DEFAULT_CHARSET
      Font.Color = clBlack
      Font.Height = -12
      Font.Name = #23435#20307
      Font.Style = []
      NotesFont.Charset = DEFAULT_CHARSET
      NotesFont.Color = clWindowText
      NotesFont.Height = -11
      NotesFont.Name = 'Tahoma'
      NotesFont.Style = []
      ParentFont = False
      Picture.Data = {
        89504E470D0A1A0A0000000D4948445200000040000000400806000000AA6971
        DE000000097048597300000B1300000B1301009A9C180000001974455874536F
        6674776172650041646F626520496D616765526561647971C9653C0000028749
        44415478DAEC5AC16EDA40105DA2724470424A2FA0D4224439840F08FD1D12F5
        23EA7E443E08D20FE012A5CD899C7A8922814F11DE9D8E9BB4057260D6637BBC
        EC8E34709961D7CF6FDE8CD728152C58B0601E5BA3CAC5CEBF276DFC1AED095B
        DC5DB61E0F0E80B3DBE42B2E16536201E3EEC7AD6F0703C0E92CE9E1420B9B1C
        04A1F3F3736B59F6DE8EAA000000FA0640D938E68CAAD8DB872A16D1505F11AC
        04001300F01E80621028A38D56038029A68D228E313196DC468FAA2A813CFED7
        06B3550F59146B4480E2592CE6B40F46039041FD1C6959A94C6BD206411440E7
        19A07D07C0944881C00017E6805002752981BC93983E841218E22466889318C6
        C63F362631EE2458661B244D82D174D5D306E2147742F12C1673DA9B1A90C7FF
        31E0F537ADBD3006702731E7BB40CABC02707D124CB94F73DC36681C67806233
        40B80DB26BD080EB1A205B02859CA8B85C024ABA04E41920AD0146F8020203A4
        1F86B822C4D600E33900E20CD09E6B406080B80678CF0071110C1AE07B17F05D
        0340CDAD37D1D8FA53947DBE6A2C3600C8B17E634109A3BD1E9F9C2CB10CE23F
        A540F3584D3EFD3F1ABF8A962A3B51A6BE17CF62AFA3F7F9190824C7D8ABE891
        769F6CECE6A1879FFD3D5173F565B0AC657EEE1278B3F38BE35FF8F5BC27ECE5
        AEA6F92C060C6F93630CFE480C7FBA1F6FBF1D3AB3C88712F2791AF07A26D04D
        0DA444EF44D3557347C8BBC4F72A29C6764E674973BB13425703A414C7D8CE60
        B6BD3EBB04B013A696329E6D60BD7101AC7CEEFA6C00B23B6BDBBD771E2859F9
        86995F0000B217A0E5010051000C737D090DD801001C6700FB0EBA5F026B6609
        AC9925B0160640F60E8A3340B34510421B643140BA0B6011CAB6C19218102C98
        E7F65B800100C7A05A98F2FBE89F0000000049454E44AE426082}
      TabOrder = 8
      OnClick = AdvGlowButton_FpClick
      Appearance.ColorChecked = 16111818
      Appearance.ColorCheckedTo = 16367008
      Appearance.ColorDisabled = 15921906
      Appearance.ColorDisabledTo = 15921906
      Appearance.ColorDown = 16111818
      Appearance.ColorDownTo = 16367008
      Appearance.ColorHot = 16117985
      Appearance.ColorHotTo = 16372402
      Appearance.ColorMirrorHot = 16107693
      Appearance.ColorMirrorHotTo = 16775412
      Appearance.ColorMirrorDown = 16102556
      Appearance.ColorMirrorDownTo = 16768988
      Appearance.ColorMirrorChecked = 16102556
      Appearance.ColorMirrorCheckedTo = 16768988
      Appearance.ColorMirrorDisabled = 11974326
      Appearance.ColorMirrorDisabledTo = 15921906
      Appearance.GradientMirrorHot = ggVertical
      Layout = blGlyphTop
    end
    object ComboBox_Hth: TRzComboBox
      Left = 504
      Top = 158
      Width = 145
      Height = 20
      TabOrder = 9
    end
  end
end
