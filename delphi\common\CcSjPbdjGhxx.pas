unit CcSjPbdjGhxx;

interface

uses
  Classes;

type
  TCcSjPbdjGhxx = class
  private
    FDdid: Integer;
    FPblx: string;
    FTgs: double;
    FXh: Integer;
    FGh: string;
  public
    property Ddid: Integer read FDdid write FDdid;
    property Pblx: string read FPblx write FPblx;
    property Tgs: double read FTgs write FTgs;
    property Xh: Integer read FXh write FXh;
    property Gh: String read FGh write FGh;
  end;

implementation

end.
