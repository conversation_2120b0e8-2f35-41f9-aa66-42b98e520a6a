'use client'

import { ColorPicker } from "@/components/ui/color-picker"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { ScrollArea } from "@/components/ui/scroll-area"

export function StyleConfig() {
  return (
    <ScrollArea className="h-[calc(100vh-250px)]">
      <div className="p-4 space-y-6">
        {/* 主题设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">主题设置</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between gap-4">
              <Label className="min-w-[80px]">主题色</Label>
              <ColorPicker color="#1677ff" onChange={() => {}} />
            </div>
            <div className="flex items-center justify-between gap-4">
              <Label className="min-w-[80px]">背景色</Label>
              <ColorPicker color="#ffffff" onChange={() => {}} />
            </div>
            <div className="flex items-center justify-between gap-4">
              <Label className="min-w-[80px]">边框颜色</Label>
              <ColorPicker color="#e5e7eb" onChange={() => {}} />
            </div>
          </div>
        </div>

        {/* 字体设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">字体设置</h3>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>字体</Label>
              <Select defaultValue="inter">
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="选择字体" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="inter">Inter</SelectItem>
                  <SelectItem value="roboto">Roboto</SelectItem>
                  <SelectItem value="arial">Arial</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>字号</Label>
              <Select defaultValue="14">
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="选择字号" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="12">12px</SelectItem>
                  <SelectItem value="14">14px</SelectItem>
                  <SelectItem value="16">16px</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* 圆角设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">圆角设置</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>圆角大小</Label>
              <span className="text-sm text-gray-500">4px</span>
            </div>
            <Slider defaultValue={[4]} max={16} min={0} step={2} className="bg-blue-50" />
          </div>
        </div>

        {/* 阴影设置 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">阴影设置</h3>
          <Select defaultValue="sm">
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="选择阴影" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">无阴影</SelectItem>
              <SelectItem value="sm">小阴影</SelectItem>
              <SelectItem value="md">中等阴影</SelectItem>
              <SelectItem value="lg">大阴影</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 动画效果 */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-700">动画效果</h3>
          <Select defaultValue="fade">
            <SelectTrigger className="bg-white">
              <SelectValue placeholder="选择动画" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">无动画</SelectItem>
              <SelectItem value="fade">淡入淡出</SelectItem>
              <SelectItem value="slide">滑动</SelectItem>
              <SelectItem value="zoom">缩放</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </ScrollArea>
  )
} 