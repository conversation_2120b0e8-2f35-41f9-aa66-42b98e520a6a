unit CcYsHjldj;

interface
uses
  Classes;

type
  TCcYsHjldj = class
  private

    FYshjldjid: Integer;
    FYsid: Integer;
    FTgs_a: Double;
    FTgs_b: Double;
    FPbj: Double;
    FHjjgf: Double;
    FHjlcb: Double;
    FBz: string;
    FMarketTime: string;
    FMarketUser: string;
    FState: string;

  public
    property Yshjldjid: integer read FYshjldjid write FYshjldjid;
    property Ysid: integer read FYsid write FYsid;
    property Tgs_a: double read FTgs_a write FTgs_a;
    property Tgs_b: double read FTgs_b write FTgs_b;
    property Pbj: double read FPbj write FPbj;
    property Hjjgf: double read FHjjgf write FHjjgf;
    property Hjlcb: double read FHjlcb write FHjlcb;
    property Bz: string read FBz write FBz;
    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;
  end;
implementation

end.

