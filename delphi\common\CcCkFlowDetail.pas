unit CcCkFlowDetail;

interface

uses
  Classes;

type
  TCcCkFlowDetail = class
  private
    FCkflowdetailid: integer;
    FCkflowid: integer;
    FCkflowtypenum: integer;
    FYwlxnum: integer;
    FGyscd: string;
    FLyckname: string;
    FPmtype: string;
    FPm: string;
    FGg: string;
    FLb: string;
    FKz: string;
    FYs: string;
    FSh: string;
    FGh: string;
    FCknamein: string;
    FCknameinregion: string;
    FCknameinamount: double;
    FCknamein1: string;
    FCknameinamount1: double;
    FCknamein2: string;
    FCknameinamount2: double;
    FCknameout: string;
    FCknameoutamount: double;
    FNote: string;
    FRecordtype: integer;
    FCkdate: string;
    FCkuser: string;
    FDetailindex: integer;
    FDetailtypenum: integer;
    FPbck: string;
    FPbps: double;
    FPbamount: double;
    FRspm: string;
    FRslb: string;
    FRskz: string;
    FRsgg: string;
    FRsck: string;
    FRsmf: string;
    FRsps: double;
    FRsOutps: double;
    FRsm: double;
    FRsoutm: double;
    FRsyjamount: double;
    FRsamount: double;
    FRsoutamount: double;
    FDzdj: double;
    FDzje: double;
    FDzrq: string;
    FDzpk: double;
    FDzfph: string;
    FDzFpje: double;
    FAuditstate: integer;
    FAuditResult: string;
    FHisPrice: double;
    Fce: double;

    FMarketTime: string;
    FMarketUser: string;
    FState: string;

    FCkflowcwid: integer;
    FCkYwlxnum: integer;
    FCkDzFph: string;
    FCkContent: string;
    FCkOutName: string;
    FCkDzrq: string;
    FCkDzje: double;
    FCkDzje_Auto: double;
    FCkAuditState: integer;
    FCkAuditStateContent: string;
    FCkDzNote: string;

    FFlconfigtypeid: integer;
    FHisPriceFlag: integer;

    FNian: string;
    FMonth: string;
    Famount: double;
    Fjhamount: double;
    FInamount: double;
    FOutamount: double;
    FCeamount1: double;
    FCeamount2: double;
    Fkcamount: double;
    Fceamountdd: double;
    FErrorCount: integer;

    FShzt: string;
  public
    property Nian: string read FNian write FNian;
    property Month: string read FMonth write FMonth;
    property jhamount: double read Fjhamount write Fjhamount;
    property amount: double read Famount write Famount;
    property Inamount: double read FInamount write FInamount;
    property Outamount: double read FOutamount write FOutamount;
    property Ceamount1: double read FCeamount1 write FCeamount1;
    property Ceamount2: double read FCeamount2 write FCeamount2;

    property Ckflowdetailid: integer read FCkflowdetailid write FCkflowdetailid;
    property Flconfigtypeid: integer read FFlconfigtypeid write FFlconfigtypeid;
    property Ckflowid: integer read FCkflowid write FCkflowid;
    property Ckflowtypenum: integer read FCkflowtypenum write FCkflowtypenum;
    property Ywlxnum: integer read FYwlxnum write FYwlxnum;
    property Gyscd: string read FGyscd write FGyscd;
    property Lyckname: string read FLyckname write FLyckname;
    property Pmtype: string read FPmtype write FPmtype;
    property Pm: string read FPm write FPm;
    property Gg: string read FGg write FGg;
    property Lb: string read FLb write FLb;
    property Kz: string read FKz write FKz;
    property Ys: string read FYs write FYs;
    property Sh: string read FSh write FSh;
    property Gh: string read FGh write FGh;
    property Cknamein: string read FCknamein write FCknamein;
    property Cknameinregion: string read FCknameinregion write FCknameinregion;
    property Cknameinamount: double read FCknameinamount write FCknameinamount;
    property Cknamein1: string read FCknamein1 write FCknamein1;
    property Cknameinamount1: double read FCknameinamount1
      write FCknameinamount1;
    property Cknamein2: string read FCknamein2 write FCknamein2;
    property Cknameinamount2: double read FCknameinamount2
      write FCknameinamount2;
    property Cknameout: string read FCknameout write FCknameout;
    property Cknameoutamount: double read FCknameoutamount
      write FCknameoutamount;

    property kcamount: double read Fkcamount write Fkcamount;
    property ceamountdd: double read Fceamountdd write Fceamountdd;

    property Note: string read FNote write FNote;
    property Recordtype: integer read FRecordtype write FRecordtype;
    property Ckdate: string read FCkdate write FCkdate;
    property Ckuser: string read FCkuser write FCkuser;

    property Detailindex: integer read FDetailindex write FDetailindex;
    property Detailtypenum: integer read FDetailtypenum write FDetailtypenum;

    property Pbck: string read FPbck write FPbck;
    property Pbps: double read FPbps write FPbps;
    property Pbamount: double read FPbamount write FPbamount;

    property Rspm: string read FRspm write FRspm;
    property Rsgg: string read FRsgg write FRsgg;
    property Rslb: string read FRslb write FRslb;
    property Rskz: string read FRskz write FRskz;
    property Rsck: string read FRsck write FRsck;
    property Rsmf: string read FRsmf write FRsmf;
    property Rsps: double read FRsps write FRsps;
    property Rsm: double read FRsm write FRsm;
    property Rsyjamount: double read FRsyjamount write FRsyjamount;
    property Rsamount: double read FRsamount write FRsamount;
    property Rsoutamount: double read FRsoutamount write FRsoutamount;
    property RsOutps: double read FRsOutps write FRsOutps;
    property Rsoutm: double read FRsoutm write FRsoutm;

    property Dzdj: double read FDzdj write FDzdj;
    property Dzje: double read FDzje write FDzje;
    property Dzpk: double read FDzpk write FDzpk;
    property Dzrq: string read FDzrq write FDzrq;
    property Dzfph: string read FDzfph write FDzfph;
    property DzFpje: double read FDzFpje write FDzFpje;
    property Auditstate: integer read FAuditstate write FAuditstate;
    property AuditResult: string read FAuditResult write FAuditResult;
    property HisPrice: double read FHisPrice write FHisPrice;
    property Ce: double read Fce write Fce;

    property MarketTime: string read FMarketTime write FMarketTime;
    property MarketUser: string read FMarketUser write FMarketUser;
    property State: string read FState write FState;

    property Ckflowcwid: integer read FCkflowcwid write FCkflowcwid;
    property CkYwlxnum: integer read FCkYwlxnum write FCkYwlxnum;
    property CkDzFph: string read FCkDzFph write FCkDzFph;
    property CkContent: string read FCkContent write FCkContent;
    property CkOutName: string read FCkOutName write FCkOutName;
    property CkDzrq: string read FCkDzrq write FCkDzrq;
    property CkDzje: double read FCkDzje write FCkDzje;
    property CkDzje_Auto: double read FCkDzje_Auto write FCkDzje_Auto;
    property CkAuditState: integer read FCkAuditState write FCkAuditState;
    property CkAuditStateContent: string read FCkAuditStateContent
      write FCkAuditStateContent;
    property CkDzNote: string read FCkDzNote write FCkDzNote;
    property HisPriceFlag: integer read FHisPriceFlag write FHisPriceFlag;
    property ErrorCount: integer read FErrorCount write FErrorCount;

    property Shzt: string read FShzt write FShzt;
  end;

implementation

end.
