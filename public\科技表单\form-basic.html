<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共性基础信息表单 - 科技表单信息系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1664FF',
                        'blue-50': '#F0F7FF',
                        'blue-100': '#E8F3FF',
                        'blue-500': '#1664FF',
                        'blue-600': '#0F52CC'
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #F8FAFC 0%, #F0F7FF 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .form-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
            border: 1px solid #E5E9EF;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button onclick="window.history.back()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <i class="fas fa-database text-blue-600"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">共性基础信息表单</h1>
                        <p class="text-sm text-gray-600">采集所有科技项目通用属性信息</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="flex items-center px-4 py-2 bg-gray-100 text-gray-600 rounded-lg hover:bg-gray-200 transition-colors">
                        <i class="fas fa-save mr-2"></i>
                        保存草稿
                    </button>
                    <button onclick="window.open('index1.html', '_self')" class="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        返回首页
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主内容区 -->
    <main class="max-w-5xl mx-auto px-6 py-8">
        <!-- 表单进度指示器 -->
        <div class="bg-white rounded-xl card-shadow p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">表单进度</h3>
                <span class="text-sm text-blue-600 font-medium">步骤 1/4</span>
            </div>
            <div class="flex items-center space-x-4">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                    <span class="ml-2 text-sm font-medium text-blue-600">基础信息</span>
                </div>
                <div class="flex-1 border-t-2 border-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                    <span class="ml-2 text-sm text-gray-500">技术详情</span>
                </div>
                <div class="flex-1 border-t-2 border-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                    <span class="ml-2 text-sm text-gray-500">合作信息</span>
                </div>
                <div class="flex-1 border-t-2 border-gray-300"></div>
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                    <span class="ml-2 text-sm text-gray-500">确认提交</span>
                </div>
            </div>
        </div>

        <!-- 表单内容 -->
        <form class="space-y-6">
            <!-- 基本信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    基本信息
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            需求编码 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" value="AUTO-2024-001256" readonly 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">系统自动生成，唯一标识</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            需求名称 <span class="text-red-500">*</span>
                        </label>
                        <input type="text" placeholder="请输入需求名称" 
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <p class="mt-1 text-xs text-gray-500">可从"科技大脑"自动填充</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            技术领域 <span class="text-red-500">*</span>
                        </label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择技术领域</option>
                            <option value="智能制造">智能制造</option>
                            <option value="生物医药">生物医药</option>
                            <option value="新材料">新材料</option>
                            <option value="新能源">新能源</option>
                            <option value="数字经济">数字经济</option>
                            <option value="海洋科技">海洋科技</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">支持"科技大脑"推荐分类</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            提出单位 <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="text" placeholder="搜索单位名称或统一社会信用代码" 
                                   class="w-full px-4 py-3 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-search text-gray-400"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">自动对接统一社会信用代码库</p>
                    </div>
                </div>
            </div>

            <!-- 需求分类 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-tags text-green-500 mr-2"></i>
                    需求分类
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            需求来源 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">专项征集</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">企业接触</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">常态化填报</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">省需求回流</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            研发类别 <span class="text-red-500">*</span>
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="radio" name="rd_category" value="基础研究" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">基础研究</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="rd_category" value="共性技术" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">共性技术</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="rd_category" value="应用示范" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">应用示范</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="rd_category" value="产业化" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                <span class="ml-2 text-sm text-gray-700">产业化</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            需求优先等级 <span class="text-red-500">*</span>
                        </label>
                        <div class="flex items-center space-x-2">
                            <div class="flex space-x-1" id="priority-stars">
                                <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 text-xl" data-rating="1">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 text-xl" data-rating="2">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 text-xl" data-rating="3">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 text-xl" data-rating="4">
                                    <i class="fas fa-star"></i>
                                </button>
                                <button type="button" class="star-btn text-gray-300 hover:text-yellow-400 text-xl" data-rating="5">
                                    <i class="fas fa-star"></i>
                                </button>
                            </div>
                            <span class="text-sm text-gray-600" id="priority-text">请选择优先等级</span>
                        </div>
                        <p class="mt-1 text-xs text-gray-500">来自专家评分与审核</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            所属产业链环节
                        </label>
                        <select class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">请选择产业链环节</option>
                            <option value="上游原材料">上游原材料</option>
                            <option value="中游制造">中游制造</option>
                            <option value="下游应用">下游应用</option>
                            <option value="配套服务">配套服务</option>
                        </select>
                        <p class="mt-1 text-xs text-gray-500">系统判断存量/新增需求</p>
                    </div>
                </div>
            </div>

            <!-- 合作信息 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-handshake text-purple-500 mr-2"></i>
                    合作信息
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            预期合作单位
                        </label>
                        <div id="cooperation-units" class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <input type="text" placeholder="合作单位名称" 
                                       class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <input type="text" placeholder="统一社会信用代码" 
                                       class="w-48 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" class="p-3 text-red-500 hover:text-red-700">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        <button type="button" id="add-cooperation-unit" 
                                class="mt-3 flex items-center px-4 py-2 text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50 transition-colors">
                            <i class="fas fa-plus mr-2"></i>
                            添加合作单位
                        </button>
                    </div>
                </div>
            </div>

            <!-- 流转状态 -->
            <div class="form-section rounded-xl p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                    <i class="fas fa-exchange-alt text-orange-500 mr-2"></i>
                    流转状态
                </h3>
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            技术需求流转情况
                        </label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">已形成指南</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">挂网交易</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">供需对接</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <span class="ml-2 text-sm text-gray-700">活动推送</span>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            专家建议
                        </label>
                        <textarea rows="4" placeholder="请输入专家建议或上传相关文件" 
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"></textarea>
                        <div class="mt-2 flex items-center space-x-2">
                            <button type="button" class="flex items-center px-3 py-2 text-sm text-blue-600 border border-blue-300 rounded-lg hover:bg-blue-50">
                                <i class="fas fa-upload mr-2"></i>
                                上传文件
                            </button>
                            <span class="text-xs text-gray-500">支持PDF、DOC、DOCX格式</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单操作按钮 -->
            <div class="flex items-center justify-between pt-6">
                <button type="button" class="flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                    <i class="fas fa-save mr-2"></i>
                    保存草稿
                </button>
                <div class="flex items-center space-x-4">
                    <button type="button" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        上一步
                    </button>
                    <button type="submit" class="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                        下一步
                    </button>
                </div>
            </div>
        </form>
    </main>

    <script>
        // 星级评分功能
        document.addEventListener('DOMContentLoaded', function() {
            const stars = document.querySelectorAll('.star-btn');
            const priorityText = document.getElementById('priority-text');
            let currentRating = 0;

            stars.forEach((star, index) => {
                star.addEventListener('click', function() {
                    currentRating = index + 1;
                    updateStars();
                    updatePriorityText();
                });

                star.addEventListener('mouseover', function() {
                    highlightStars(index + 1);
                });
            });

            document.getElementById('priority-stars').addEventListener('mouseleave', function() {
                updateStars();
            });

            function highlightStars(rating) {
                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.classList.remove('text-gray-300');
                        star.classList.add('text-yellow-400');
                    } else {
                        star.classList.remove('text-yellow-400');
                        star.classList.add('text-gray-300');
                    }
                });
            }

            function updateStars() {
                highlightStars(currentRating);
            }

            function updatePriorityText() {
                const texts = ['', '一般', '较低', '中等', '较高', '最高'];
                priorityText.textContent = currentRating > 0 ? `${texts[currentRating]}优先级` : '请选择优先等级';
            }

            // 动态添加合作单位
            document.getElementById('add-cooperation-unit').addEventListener('click', function() {
                const container = document.getElementById('cooperation-units');
                const newUnit = document.createElement('div');
                newUnit.className = 'flex items-center space-x-3';
                newUnit.innerHTML = `
                    <input type="text" placeholder="合作单位名称" 
                           class="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <input type="text" placeholder="统一社会信用代码" 
                           class="w-48 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <button type="button" class="p-3 text-red-500 hover:text-red-700 remove-unit">
                        <i class="fas fa-trash"></i>
                    </button>
                `;
                container.appendChild(newUnit);

                // 添加删除功能
                newUnit.querySelector('.remove-unit').addEventListener('click', function() {
                    newUnit.remove();
                });
            });

            // 为现有的删除按钮添加事件监听
            document.querySelectorAll('.remove-unit').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.closest('.flex').remove();
                });
            });
        });
    </script>
</body>
</html> 