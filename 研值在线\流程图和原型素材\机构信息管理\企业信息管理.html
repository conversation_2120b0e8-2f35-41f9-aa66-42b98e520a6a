<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业信息管理</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">企业信息管理</h1>
            <p class="text-gray-600">全生命周期企业档案管理，支持多维关联与数据追溯</p>
        </div>

        <!-- 主要内容区域 -->
        <div class="bg-white shadow-md rounded-lg h-[calc(100vh-120px)] overflow-hidden">
            <!-- 顶部导航与操作区 -->
            <div class="border-b border-gray-200 p-4">
                <div class="flex justify-between items-center">
                    <!-- 左侧标签页 -->
                    <div class="flex space-x-1">
                        <button class="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border-b-2 border-blue-600 rounded-t-md" onclick="switchTab('enterprise-list')">
                            <i class="fas fa-building mr-2"></i>企业列表
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-t-md" onclick="switchTab('high-tech')">
                            <i class="fas fa-award mr-2"></i>高新认定
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-t-md" onclick="switchTab('innovation-platform')">
                            <i class="fas fa-link mr-2"></i>科创平台关联
                        </button>
                    </div>
                    
                    <!-- 右侧操作按钮 -->
                    <div class="flex items-center space-x-3">
                        <div class="text-sm text-gray-500">
                            <span>最后更新：2024-01-15 14:30</span>
                            <span class="ml-4 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs">管理员权限</span>
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" onclick="openAddModal()">
                            <i class="fas fa-plus mr-2"></i>新增企业
                        </button>
                        <button class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors" onclick="openImportModal()">
                            <i class="fas fa-upload mr-2"></i>批量导入
                        </button>
                        <button class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>批量导出
                        </button>
                        <button class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
                            <i class="fas fa-file-download mr-2"></i>字段模板
                        </button>
                    </div>
                </div>
            </div>

            <!-- 企业列表内容区 -->
            <div id="enterprise-list" class="tab-content p-4 h-full overflow-auto">
                <!-- 筛选条件 -->
                <div class="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">企业名称</label>
                            <input type="text" placeholder="请输入企业名称" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">企业类型</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部类型</option>
                                <option value="有限责任公司">有限责任公司</option>
                                <option value="股份有限公司">股份有限公司</option>
                                <option value="个人独资企业">个人独资企业</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">经营状态</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">全部状态</option>
                                <option value="存续">存续</option>
                                <option value="在业">在业</option>
                                <option value="注销">注销</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                                <i class="fas fa-search mr-2"></i>搜索
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        <input type="checkbox" class="rounded border-gray-300">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">企业名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">统一社会信用代码</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">企业类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">经营状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">成立时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">注册资本</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定标签</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">北京科技创新有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91110000123456789X</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">有限责任公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">存续</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2018-03-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">1000万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex space-x-1">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">高新技术企业</span>
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">专精特新</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openDetailDrawer('1')">
                                            <i class="fas fa-eye"></i> 详情
                                        </button>
                                        <button class="text-green-600 hover:text-green-900 mr-3">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">上海智能制造股份有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91310000987654321Y</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">股份有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在业</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-07-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">5000万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">科技型中小企业</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openDetailDrawer('2')">
                                            <i class="fas fa-eye"></i> 详情
                                        </button>
                                        <button class="text-green-600 hover:text-green-900 mr-3">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                                <tr class="hover:bg-gray-50 bg-red-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <input type="checkbox" class="rounded border-gray-300">
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">深圳新材料技术有限公司</div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">91440300456789123Z</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">有限责任公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">注销</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2015-11-08</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">800万元</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">无</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="openDetailDrawer('3')">
                                            <i class="fas fa-eye"></i> 详情
                                        </button>
                                        <button class="text-gray-400 cursor-not-allowed mr-3">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                        <button class="text-gray-400 cursor-not-allowed">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200">
                        <div class="flex-1 flex justify-between sm:hidden">
                            <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">上一页</button>
                            <button class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">下一页</button>
                        </div>
                        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p class="text-sm text-gray-700">显示第 <span class="font-medium">1</span> 到 <span class="font-medium">10</span> 条，共 <span class="font-medium">97</span> 条记录</p>
                            </div>
                            <div>
                                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">上一页</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">1</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">2</button>
                                    <button class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">3</button>
                                    <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">下一页</button>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高新认定内容区 -->
            <div id="high-tech" class="tab-content p-4 h-full overflow-auto hidden">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-blue-100">高新技术企业</p>
                                <p class="text-3xl font-bold">156</p>
                                <p class="text-blue-100 text-sm">占比 65.3%</p>
                            </div>
                            <i class="fas fa-award text-4xl text-blue-200"></i>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-green-100">专精特新企业</p>
                                <p class="text-3xl font-bold">89</p>
                                <p class="text-green-100 text-sm">占比 37.2%</p>
                            </div>
                            <i class="fas fa-star text-4xl text-green-200"></i>
                        </div>
                    </div>
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-purple-100">科技型中小企业</p>
                                <p class="text-3xl font-bold">234</p>
                                <p class="text-purple-100 text-sm">占比 98.1%</p>
                            </div>
                            <i class="fas fa-rocket text-4xl text-purple-200"></i>
                        </div>
                    </div>
                </div>

                <!-- 即将到期提醒 -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-yellow-500 mr-3"></i>
                            <div>
                                <h3 class="text-sm font-medium text-yellow-800">证书到期提醒</h3>
                                <p class="text-sm text-yellow-700">有 12 家企业的认定证书将在 3 个月内到期</p>
                            </div>
                        </div>
                        <button class="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors">
                            <i class="fas fa-bell mr-2"></i>批量提醒
                        </button>
                    </div>
                </div>

                <!-- 认定明细表 -->
                <div class="bg-white border border-gray-200 rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg font-medium text-gray-900">认定明细</h3>
                            <div class="flex space-x-2">
                                <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <option>全部年份</option>
                                    <option>2024年</option>
                                    <option>2023年</option>
                                    <option>2022年</option>
                                </select>
                                <select class="px-3 py-2 border border-gray-300 rounded-md text-sm">
                                    <option>全部级别</option>
                                    <option>国家级</option>
                                    <option>省级</option>
                                    <option>市级</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">企业名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定级别</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">证书编号</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认定时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期至</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">北京科技创新有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">高新技术企业</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">国家级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">GR202011000123</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-12-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-12-14</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">即将到期</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">上海智能制造股份有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">专精特新</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">省级</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer hover:underline">ZX202200456</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2022-08-20</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2025-08-19</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">有效</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 科创平台关联内容区 -->
            <div id="innovation-platform" class="tab-content p-4 h-full overflow-auto hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">孵化器</p>
                                <p class="text-2xl font-bold text-gray-900">45</p>
                            </div>
                            <i class="fas fa-seedling text-2xl text-green-500"></i>
                        </div>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">众创空间</p>
                                <p class="text-2xl font-bold text-gray-900">32</p>
                            </div>
                            <i class="fas fa-users text-2xl text-blue-500"></i>
                        </div>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">加速器</p>
                                <p class="text-2xl font-bold text-gray-900">18</p>
                            </div>
                            <i class="fas fa-rocket text-2xl text-purple-500"></i>
                        </div>
                    </div>
                    <div class="bg-white border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">产业园区</p>
                                <p class="text-2xl font-bold text-gray-900">67</p>
                            </div>
                            <i class="fas fa-industry text-2xl text-orange-500"></i>
                        </div>
                    </div>
                </div>

                <div class="bg-white border border-gray-200 rounded-lg">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">平台关联详情</h3>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">企业名称</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联平台</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">平台类型</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入驻时间</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">服务状态</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">北京科技创新有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">中关村科技园</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">产业园区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2020-03-15</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">在孵</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-link"></i> 管理关联
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">上海智能制造股份有限公司</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">张江高科技园区</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">孵化器</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2021-07-22</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">毕业</span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-link"></i> 管理关联
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部数据质量与权限提示 -->
        <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex space-x-6 text-sm text-gray-600">
                    <span><i class="fas fa-exclamation-circle text-yellow-500 mr-1"></i>缺失关键信息企业：23家</span>
                    <span><i class="fas fa-clock text-blue-500 mr-1"></i>待审核变更：8条</span>
                    <span><i class="fas fa-shield-alt text-green-500 mr-1"></i>当前权限：管理员（可执行所有操作）</span>
                </div>
                <button class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-user-plus mr-1"></i>申请更高权限
                </button>
            </div>
        </div>
    </div>

    <!-- 企业详情抽屉 -->
    <div id="detailDrawer" class="fixed inset-y-0 right-0 w-1/2 bg-white shadow-xl transform translate-x-full transition-transform duration-300 ease-in-out z-50 hidden">
        <div class="h-full flex flex-col">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-medium text-gray-900">企业详情</h2>
                    <button onclick="closeDetailDrawer()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="flex-1 overflow-y-auto p-6">
                <!-- 基础信息卡片 -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">基础信息</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">企业地址：</span>
                            <span class="text-gray-900">北京市海淀区中关村大街1号</span>
                        </div>
                        <div>
                            <span class="text-gray-500">联系电话：</span>
                            <span class="text-gray-900">010-12345678</span>
                        </div>
                        <div>
                            <span class="text-gray-500">注册资本：</span>
                            <span class="text-gray-900">1000万元人民币</span>
                        </div>
                        <div>
                            <span class="text-gray-500">英文名称：</span>
                            <span class="text-gray-900">Beijing Tech Innovation Co., Ltd.</span>
                        </div>
                    </div>
                </div>

                <!-- 股东信息 -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">股东信息</h3>
                    <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">股东名称</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">持股比例</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">认缴金额</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr>
                                    <td class="px-4 py-2 text-sm text-gray-900">张三</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">60%</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">600万元</td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-2 text-sm text-gray-900">李四</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">40%</td>
                                    <td class="px-4 py-2 text-sm text-gray-500">400万元</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 主要人员 -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">主要人员</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-medium text-gray-900">张三</span>
                                <span class="text-sm text-gray-500 ml-2">法定代表人、总经理</span>
                            </div>
                            <span class="text-sm text-gray-500">持股60%</span>
                        </div>
                        <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                            <div>
                                <span class="font-medium text-gray-900">李四</span>
                                <span class="text-sm text-gray-500 ml-2">技术总监</span>
                            </div>
                            <span class="text-sm text-gray-500">持股40%</span>
                        </div>
                    </div>
                </div>

                <!-- 研发团队 -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">研发团队</h3>
                    <div class="space-y-2">
                        <div class="p-3 bg-blue-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="font-medium text-gray-900">王五</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">高级工程师</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">人工智能算法研究</p>
                        </div>
                        <div class="p-3 bg-green-50 rounded-lg">
                            <div class="flex justify-between items-center">
                                <span class="font-medium text-gray-900">赵六</span>
                                <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">研发经理</span>
                            </div>
                            <p class="text-sm text-gray-600 mt-1">机器学习平台开发</p>
                        </div>
                    </div>
                </div>

                <!-- 关联信息标签 -->
                <div class="mb-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">关联信息</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="p-3 bg-purple-50 rounded-lg cursor-pointer hover:bg-purple-100 transition-colors">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-purple-900">创新企业认定</span>
                                <span class="text-lg font-bold text-purple-600">3</span>
                            </div>
                            <p class="text-xs text-purple-600 mt-1">点击查看详情</p>
                        </div>
                        <div class="p-3 bg-blue-50 rounded-lg cursor-pointer hover:bg-blue-100 transition-colors">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-blue-900">科创平台</span>
                                <span class="text-lg font-bold text-blue-600">2</span>
                            </div>
                            <p class="text-xs text-blue-600 mt-1">点击查看详情</p>
                        </div>
                        <div class="p-3 bg-green-50 rounded-lg cursor-pointer hover:bg-green-100 transition-colors">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-green-900">设备资源</span>
                                <span class="text-lg font-bold text-green-600">15</span>
                            </div>
                            <p class="text-xs text-green-600 mt-1">点击查看详情</p>
                        </div>
                        <div class="p-3 bg-orange-50 rounded-lg cursor-pointer hover:bg-orange-100 transition-colors">
                            <div class="flex justify-between items-center">
                                <span class="text-sm font-medium text-orange-900">项目成果</span>
                                <span class="text-lg font-bold text-orange-600">8</span>
                            </div>
                            <p class="text-xs text-orange-600 mt-1">点击查看详情</p>
                        </div>
                    </div>
                </div>

                <!-- 变更记录时间轴 -->
                <div>
                    <h3 class="text-md font-semibold text-gray-700 mb-3">变更记录</h3>
                    <div class="space-y-4">
                        <div class="flex">
                            <div class="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2 mr-4"></div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">注册资本变更</p>
                                        <p class="text-xs text-gray-500">从 500万元 变更为 1000万元</p>
                                    </div>
                                    <span class="text-xs text-gray-400">2023-08-15</span>
                                </div>
                            </div>
                        </div>
                        <div class="flex">
                            <div class="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2 mr-4"></div>
                            <div class="flex-1">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">经营范围变更</p>
                                        <p class="text-xs text-gray-500">新增：人工智能技术开发</p>
                                    </div>
                                    <span class="text-xs text-gray-400">2023-03-20</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 批量导入模态框 -->
    <div id="importModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">批量导入企业信息</h3>
                    <button onclick="closeImportModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <!-- 导入步骤 -->
                <div class="mb-6">
                    <div class="flex items-center">
                        <div class="flex items-center text-blue-600">
                            <div class="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-blue-600">上传文件</p>
                            </div>
                        </div>
                        <div class="flex-1 h-px bg-gray-300 mx-4"></div>
                        <div class="flex items-center text-gray-400">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-300 text-white rounded-full flex items-center justify-center text-sm font-medium">2</div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">字段校验</p>
                            </div>
                        </div>
                        <div class="flex-1 h-px bg-gray-300 mx-4"></div>
                        <div class="flex items-center text-gray-400">
                            <div class="flex-shrink-0 w-8 h-8 bg-gray-300 text-white rounded-full flex items-center justify-center text-sm font-medium">3</div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-400">数据预览</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 文件上传区域 -->
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-900 mb-2">拖拽文件到此处或点击上传</p>
                    <p class="text-sm text-gray-500 mb-4">支持 .xlsx, .xls, .csv 格式，文件大小不超过 10MB</p>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        <i class="fas fa-file-upload mr-2"></i>选择文件
                    </button>
                </div>

                <!-- 模板下载提示 -->
                <div class="mt-4 p-4 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                        <div class="flex-1">
                            <p class="text-sm text-blue-800">请先下载导入模板，按照模板格式填写企业信息</p>
                        </div>
                        <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <i class="fas fa-download mr-1"></i>下载模板
                        </button>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button onclick="closeImportModal()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors">
                        取消
                    </button>
                    <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                        下一步
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName).classList.remove('hidden');
            
            // 更新标签页按钮样式
            document.querySelectorAll('button[onclick^="switchTab"]').forEach(btn => {
                btn.className = 'px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-gray-50 rounded-t-md';
            });
            
            event.target.className = 'px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border-b-2 border-blue-600 rounded-t-md';
        }

        // 打开企业详情抽屉
        function openDetailDrawer(enterpriseId) {
            const drawer = document.getElementById('detailDrawer');
            drawer.classList.remove('hidden');
            setTimeout(() => {
                drawer.classList.remove('translate-x-full');
            }, 10);
        }

        // 关闭企业详情抽屉
        function closeDetailDrawer() {
            const drawer = document.getElementById('detailDrawer');
            drawer.classList.add('translate-x-full');
            setTimeout(() => {
                drawer.classList.add('hidden');
            }, 300);
        }

        // 打开新增企业模态框
        function openAddModal() {
            alert('打开新增企业表单');
        }

        // 打开批量导入模态框
        function openImportModal() {
            document.getElementById('importModal').classList.remove('hidden');
        }

        // 关闭批量导入模态框
        function closeImportModal() {
            document.getElementById('importModal').classList.add('hidden');
        }

        // 点击模态框外部关闭
        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportModal();
            }
        });
    </script>
</body>
</html>