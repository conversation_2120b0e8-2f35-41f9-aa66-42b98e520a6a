<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险评估与预警分析</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3b82f6',
                        danger: '#ef4444',
                        warning: '#f59e0b',
                        success: '#10b981'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">风险评估与预警分析</h1>
            <p class="mt-2 text-gray-600">结合机构运营数据、诚信记录与人员结构，量化评估机构风险并提供实时预警</p>
        </div>

        <!-- 筛选与概览区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">机构类型</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option>企业</option>
                        <option>科研机构</option>
                        <option>高等院校</option>
                        <option>医院</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">行业</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option>电子信息</option>
                        <option>生物医药</option>
                        <option>新材料</option>
                        <option>智能制造</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">区域</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option>海曙区</option>
                        <option>江北区</option>
                        <option>鄞州区</option>
                        <option>高新区</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option>近3个月</option>
                        <option>近6个月</option>
                        <option>近1年</option>
                        <option>自定义</option>
                    </select>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500 mb-1">机构总数</div>
                    <div class="text-2xl font-bold text-blue-600">1,245</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500 mb-1">高风险机构</div>
                    <div class="text-2xl font-bold text-red-600">86 <span class="text-sm">(6.9%)</span></div>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg">
                    <div class="text-sm text-gray-500 mb-1">平均风险分</div>
                    <div class="text-2xl font-bold text-yellow-600">62.5</div>
                </div>
            </div>
        </div>

        <!-- 风险热力图区 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold text-gray-900">区域风险热力图</h2>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm">地图</button>
                    <button class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md text-sm">矩阵</button>
                </div>
            </div>
            <div class="h-80 bg-gray-100 rounded-lg flex items-center justify-center">
                <div class="text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="mt-2 text-sm text-gray-500">区域风险热力图将在此显示</p>
                </div>
            </div>
        </div>

        <!-- 风险机构列表区 -->
        <div class="bg-white rounded-lg shadow-md mb-6">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-semibold text-gray-900">风险机构列表</h2>
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            导出报告
                        </button>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            阈值设置
                        </button>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">机构名称</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">综合风险分</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险类型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最新预警时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波市XX科技有限公司</div>
                                <div class="text-sm text-gray-500">电子信息 | 高新区</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-red-600">89</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">财务异常</span>
                                    <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">信用不良</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-15 14:30</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波YY生物医药研究院</div>
                                <div class="text-sm text-gray-500">生物医药 | 鄞州区</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-red-600">78</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    <span class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">资质过期</span>
                                    <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">人员不足</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-14 09:15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">宁波ZZ大学材料研究所</div>
                                <div class="text-sm text-gray-500">新材料 | 江北区</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-bold text-yellow-600">65</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">职称结构不合理</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">2024-01-12 16:45</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="#" class="text-blue-600 hover:text-blue-900">详情</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-500">
                        显示第 1-3 条，共 86 条记录
                    </div>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">上一页</button>
                        <button class="px-3 py-1 bg-blue-600 text-white rounded-md text-sm">1</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">2</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">3</button>
                        <button class="px-3 py-1 border border-gray-300 rounded-md text-sm text-gray-500 hover:bg-gray-50">下一页</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 学历结构分析区 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">研发人员学历结构</h2>
                    <div class="flex items-center space-x-2">
                        <div class="text-sm">
                            <span class="text-gray-500">高学历占比:</span>
                            <span class="font-bold text-green-600">68%</span>
                        </div>
                        <div class="text-sm">
                            <span class="text-gray-500">博士后占比:</span>
                            <span class="font-bold text-yellow-600">12%</span>
                        </div>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="educationChart"></canvas>
                </div>
            </div>

            <!-- 职称结构分析区 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">研发人员职称结构</h2>
                    <div class="text-sm">
                        <span class="text-gray-500">高级职称占比:</span>
                        <span class="font-bold text-red-600">42%</span>
                    </div>
                </div>
                <div class="h-64">
                    <canvas id="titleChart"></canvas>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p>当前职称结构不满足研发梯度要求，建议增加高级职称人员比例。</p>
                </div>
            </div>
        </div>

        <!-- 数据质量与预警设置区 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">数据质量与预警设置</h2>
            <div class="space-y-4">
                <div class="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-yellow-800">5个机构缺失研发人员数据</p>
                        <p class="text-xs text-yellow-700">影响风险评估准确性</p>
                    </div>
                    <button class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-md text-sm hover:bg-yellow-200">去修复</button>
                </div>
                <div class="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-red-800">3个高风险机构待确认</p>
                        <p class="text-xs text-red-700">需人工复核</p>
                    </div>
                    <button class="px-3 py-1 bg-red-100 text-red-800 rounded-md text-sm hover:bg-red-200">处理</button>
                </div>
                <div class="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                    <div>
                        <p class="text-sm font-medium text-blue-800">风险阈值设置</p>
                        <p class="text-xs text-blue-700">当前版本: v2.1 (2024-01-10更新)</p>
                    </div>
                    <button class="px-3 py-1 bg-blue-100 text-blue-800 rounded-md text-sm hover:bg-blue-200">调整阈值</button>
                </div>
            </div>
            <div class="mt-4 text-xs text-gray-500">
                <p>数据更新时间: 2024-01-15 15:30:00 | 数据口径: 宁波市科技局统一标准</p>
            </div>
        </div>
    </div>

    <!-- Chart.js 初始化脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 学历结构图表
            const educationCtx = document.getElementById('educationChart').getContext('2d');
            new Chart(educationCtx, {
                type: 'bar',
                data: {
                    labels: ['企业', '科研机构', '高等院校', '医院'],
                    datasets: [
                        {
                            label: '博士',
                            data: [120, 85, 210, 45],
                            backgroundColor: 'rgba(59, 130, 246, 0.8)'
                        },
                        {
                            label: '硕士',
                            data: [180, 120, 350, 80],
                            backgroundColor: 'rgba(16, 185, 129, 0.8)'
                        },
                        {
                            label: '本科',
                            data: [250, 90, 280, 120],
                            backgroundColor: 'rgba(245, 158, 11, 0.8)'
                        },
                        {
                            label: '其他',
                            data: [50, 25, 60, 30],
                            backgroundColor: 'rgba(107, 114, 128, 0.8)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            stacked: true,
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true
                        }
                    }
                }
            });

            // 职称结构图表
            const titleCtx = document.getElementById('titleChart').getContext('2d');
            new Chart(titleCtx, {
                type: 'doughnut',
                data: {
                    labels: ['研究员', '高级工程师', '工程师', '其他'],
                    datasets: [{
                        data: [85, 120, 180, 150],
                        backgroundColor: [
                            'rgba(59, 130, 246, 0.8)',
                            'rgba(16, 185, 129, 0.8)',
                            'rgba(245, 158, 11, 0.8)',
                            'rgba(107, 114, 128, 0.8)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        });
    </script>
</body>
</html>