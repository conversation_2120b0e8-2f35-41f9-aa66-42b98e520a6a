'use client'

import { useRouter } from 'next/router'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON> } from "lucide-react"

export function Preview() {
  const router = useRouter()

  const handlePreview = () => {
    // 导航到预览页面
    router.push('/preview')
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">预览</h3>
      <Button onClick={handlePreview}>
        <Eye className="h-4 w-4 mr-2" />
        预览
      </Button>
    </div>
  )
} 