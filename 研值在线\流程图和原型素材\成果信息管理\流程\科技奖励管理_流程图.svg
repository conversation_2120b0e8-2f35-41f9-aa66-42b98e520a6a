<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1400 1000" font-family="'Source Han Sans CN', 'Helvetica', 'Arial', sans-serif">
  <!-- 定义 -->
  <defs>
    <!-- 箭头样式 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <!-- 阴影滤镜 -->
    <filter id="soft-shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="3" stdDeviation="3" flood-color="#000000" flood-opacity="0.1" />
    </filter>
  </defs>

  <!-- 背景 -->
  <rect width="100%" height="100%" fill="white" />

  <!-- 主标题 -->
  <text x="700" y="50" font-size="28" text-anchor="middle" font-weight="600" fill="#333">科技奖励管理业务流程</text>

  <!-- 阶段一：系统初始化与权限加载 -->
  <text x="700" y="100" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段一：系统初始化与权限加载</text>
  
  <!-- 节点1: 用户进入模块 -->
  <g transform="translate(300, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户进入模块</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">根据权限加载奖励类别</text>
  </g>

  <!-- 节点2: 默认数据加载 -->
  <g transform="translate(800, 130)" filter="url(#soft-shadow)">
    <rect width="220" height="70" rx="8" ry="8" fill="#E3F2FD" stroke="#90CAF9" stroke-width="1.5" />
    <text x="110" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">默认数据加载</text>
    <text x="110" y="55" text-anchor="middle" font-size="12" fill="#555">拉取最近三年奖励数据</text>
  </g>
  
  <!-- 连接线 1 -> 2 -->
  <path d="M 520 165 Q 600 165, 800 165" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段二：数据查询与检索 -->
  <text x="700" y="280" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段二：数据查询与检索</text>

  <!-- 节点3: 用户提交查询 -->
  <g transform="translate(150, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">用户提交查询</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">记录过滤参数</text>
  </g>

  <!-- 节点4: 奖励检索服务 -->
  <g transform="translate(450, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">奖励检索服务</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">拉取记录与统计指标</text>
  </g>

  <!-- 节点5: 关联信息拉取 -->
  <g transform="translate(750, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">关联信息拉取</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">项目与创新主体信息</text>
  </g>

  <!-- 节点6: 页面刷新 -->
  <g transform="translate(1050, 320)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#E6F4EA" stroke="#A5D6A7" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">页面刷新</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新表格与图表</text>
  </g>

  <!-- 连接线 2 -> 3 -->
  <path d="M 910 200 C 910 250, 250 280, 250 320" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 3 -> 4 -->
  <path d="M 350 355 Q 400 355, 450 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 4 -> 5 -->
  <path d="M 650 355 Q 700 355, 750 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 5 -> 6 -->
  <path d="M 950 355 Q 1000 355, 1050 355" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段三：数据操作与维护 -->
  <text x="700" y="480" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段三：数据操作与维护</text>

  <!-- 节点7: 编辑操作 -->
  <g transform="translate(100, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">编辑操作</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">新增/修改奖励信息</text>
  </g>

  <!-- 节点8: 批量导入 -->
  <g transform="translate(350, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">批量导入</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">文件格式校验与解析</text>
  </g>

  <!-- 节点9: 标签生成 -->
  <g transform="translate(600, 520)" filter="url(#soft-shadow)">
    <rect width="180" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="90" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">标签生成</text>
    <text x="90" y="55" text-anchor="middle" font-size="12" fill="#555">成果标签与映射关系</text>
  </g>

  <!-- 节点10: 数据校验与写入 -->
  <g transform="translate(850, 520)" filter="url(#soft-shadow)">
    <rect width="200" height="70" rx="8" ry="8" fill="#F3E8FD" stroke="#CE93D8" stroke-width="1.5" />
    <text x="100" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据校验与写入</text>
    <text x="100" y="55" text-anchor="middle" font-size="12" fill="#555">更新缓存与统计指标</text>
  </g>

  <!-- 连接线 6 -> 7 -->
  <path d="M 1150 390 C 1150 450, 190 480, 190 520" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 7 -> 10 -->
  <path d="M 280 555 C 500 555, 700 555, 850 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 8 -> 10 -->
  <path d="M 530 555 Q 690 555, 850 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 9 -> 10 -->
  <path d="M 780 555 Q 815 555, 850 555" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 阶段四：数据同步与监控 -->
  <text x="700" y="680" font-size="20" text-anchor="middle" font-weight="500" fill="#555">阶段四：数据同步与监控</text>
  
  <!-- 节点11: 定时同步任务 -->
  <g transform="translate(300, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">定时同步任务</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">对接第三方数据源</text>
    <text x="125" y="75" text-anchor="middle" font-size="12" fill="#555">差异化比对与更新</text>
  </g>

  <!-- 节点12: 数据库维护 -->
  <g transform="translate(650, 720)" filter="url(#soft-shadow)">
    <rect width="250" height="80" rx="8" ry="8" fill="#FFFBEB" stroke="#FFE082" stroke-width="1.5"/>
    <text x="125" y="30" text-anchor="middle" font-size="16" font-weight="600" fill="#333">数据库维护</text>
    <text x="125" y="55" text-anchor="middle" font-size="12" fill="#555">保证数据时效性</text>
    <text x="125" y="75" text-anchor="middle" font-size="12" fill="#555">确保数据准确性</text>
  </g>

  <!-- 连接线 10 -> 11 -->
  <path d="M 950 590 C 950 650, 425 680, 425 720" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />
  <!-- 连接线 11 -> 12 -->
  <path d="M 550 760 Q 600 760, 650 760" stroke="#333" stroke-width="2" fill="none" marker-end="url(#arrowhead)" />

  <!-- 反馈循环：数据同步回到查询阶段 -->
  <path d="M 775 720 C 775 650, 1200 400, 1200 200 C 1200 150, 1000 130, 1020 165" stroke="#666" stroke-width="1.5" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5" />
  <text x="1100" y="400" text-anchor="middle" font-size="11" fill="#666">数据同步反馈</text>

</svg>