<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>区域监测看板 - 数据云窗方案1</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        .glass-card {
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
        }
    </style>
</head>
<body class="min-h-screen p-6">
    <div class="max-w-7xl mx-auto">
        <!-- 顶部控制区 -->
        <div class="glass-card rounded-2xl p-6 mb-6">
            <div class="flex items-center justify-between flex-wrap gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-slate-800 mb-1">区域监测</h1>
                    <p class="text-sm text-slate-600">科技管理决策支持系统</p>
                </div>
                
                <div class="flex items-center gap-4 flex-wrap">
                    <!-- 区域选择 -->
                    <div class="relative">
                        <select class="glass-card rounded-lg px-4 py-2 text-sm border-0 focus:ring-2 focus:ring-blue-500">
                            <option>宁波市</option>
                            <option>全国</option>
                            <option>浙江省</option>
                            <option>甬江科创区</option>
                            <option>区县市</option>
                            <option>高新区</option>
                        </select>
                    </div>
                    
                    <!-- 时间切换 -->
                    <div class="flex bg-slate-100 rounded-lg p-1">
                        <button class="px-3 py-1 text-sm rounded-md bg-white text-blue-600 font-medium">月度</button>
                        <button class="px-3 py-1 text-sm rounded-md text-slate-600">季度</button>
                        <button class="px-3 py-1 text-sm rounded-md text-slate-600">年度</button>
                    </div>
                    
                    <!-- 指标类型 -->
                    <select class="glass-card rounded-lg px-4 py-2 text-sm border-0 focus:ring-2 focus:ring-blue-500">
                        <option>研发投入</option>
                        <option>产业结构</option>
                        <option>人才分布</option>
                        <option>项目进展</option>
                    </select>
                    
                    <span class="text-sm text-slate-500">2025年1-6月数据</span>
                </div>
            </div>
        </div>

        <!-- 四象限布局 -->
        <div class="grid grid-cols-2 gap-6">
            <!-- 左上：重点企业 -->
            <div class="metric-card glass-card rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-slate-800">重点企业</h3>
                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        下钻到企业监测 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span class="text-sm text-slate-700">"510"领域布局情况</span>
                        <span class="text-sm font-medium text-blue-600">查看详情</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-2xl font-bold text-slate-800">500</div>
                            <div class="text-xs text-slate-600">研发费用500强企业</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-2xl font-bold text-slate-800">100</div>
                            <div class="text-xs text-slate-600">"研值"前100企业</div>
                        </div>
                    </div>
                    
                    <div>
                        <select class="w-full glass-card rounded-lg px-3 py-2 text-sm border-0">
                            <option>企业储备库：高企</option>
                            <option>科小</option>
                            <option>瞪羚之星</option>
                            <option>科技领军企业</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                            <span class="text-sm text-slate-700">天使/创投已投企业</span>
                            <span class="text-sm font-medium text-green-600">125家</span>
                        </div>
                        <div class="flex items-center justify-between p-2 bg-red-50 rounded-lg">
                            <span class="text-sm text-slate-700">三色图异常企业</span>
                            <span class="text-sm font-medium text-red-600">8家</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右上：重点平台 -->
            <div class="metric-card glass-card rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-slate-800">重点平台</h3>
                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        下钻到平台监测 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                        <span class="text-sm text-slate-700">"510"领域平台布局</span>
                        <span class="text-sm font-medium text-purple-600">查看详情</span>
                    </div>
                    
                    <div>
                        <select class="w-full glass-card rounded-lg px-3 py-2 text-sm border-0">
                            <option>重点实验室：全国级</option>
                            <option>省级</option>
                            <option>市级</option>
                        </select>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-3">
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">12</div>
                            <div class="text-xs text-slate-600">国家级</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">35</div>
                            <div class="text-xs text-slate-600">省级</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">89</div>
                            <div class="text-xs text-slate-600">市级</div>
                        </div>
                    </div>
                    
                    <div class="p-3 bg-blue-50 rounded-lg">
                        <div class="text-sm text-slate-700 mb-1">有在研项目平台</div>
                        <div class="text-lg font-bold text-blue-600">156个</div>
                    </div>
                </div>
            </div>

            <!-- 左下：重点人才 -->
            <div class="metric-card glass-card rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-slate-800">重点人才</h3>
                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        下钻到人才监测 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-indigo-50 rounded-lg">
                        <span class="text-sm text-slate-700">"510"领域人才分布</span>
                        <span class="text-sm font-medium text-indigo-600">查看详情</span>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-3">
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-2xl font-bold text-slate-800">28</div>
                            <div class="text-xs text-slate-600">顶尖人才</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-2xl font-bold text-slate-800">156</div>
                            <div class="text-xs text-slate-600">领军人才</div>
                        </div>
                    </div>
                    
                    <div>
                        <select class="w-full glass-card rounded-lg px-3 py-2 text-sm border-0">
                            <option>人才层次：顶尖人才</option>
                            <option>特优人才</option>
                            <option>领军人才</option>
                            <option>拔尖人才</option>
                        </select>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                            <span class="text-sm text-slate-700">在研项目人才</span>
                            <span class="text-sm font-medium text-green-600">342人</span>
                        </div>
                        <div class="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                            <span class="text-sm text-slate-700">新增入库人才</span>
                            <span class="text-sm font-medium text-blue-600">+23人</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右下：重大项目 -->
            <div class="metric-card glass-card rounded-2xl p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-slate-800">重大项目</h3>
                    <button class="text-blue-600 hover:text-blue-700 text-sm font-medium">
                        下钻到项目监测 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                        <span class="text-sm text-slate-700">"510"领域项目布局</span>
                        <span class="text-sm font-medium text-orange-600">查看详情</span>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-3">
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">89</div>
                            <div class="text-xs text-slate-600">重大专项</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">234</div>
                            <div class="text-xs text-slate-600">重点研发</div>
                        </div>
                        <div class="text-center p-3 bg-slate-50 rounded-lg">
                            <div class="text-xl font-bold text-slate-800">156</div>
                            <div class="text-xs text-slate-600">技术创新</div>
                        </div>
                    </div>
                    
                    <div>
                        <select class="w-full glass-card rounded-lg px-3 py-2 text-sm border-0">
                            <option>项目状态：全部</option>
                            <option>进行中</option>
                            <option>已完成</option>
                            <option>已延期</option>
                        </select>
                    </div>
                    
                    <div class="p-3 bg-green-50 rounded-lg">
                        <div class="text-sm text-slate-700 mb-1">总投入金额</div>
                        <div class="text-lg font-bold text-green-600">¥ 12.5亿</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>